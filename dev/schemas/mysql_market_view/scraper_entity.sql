CREATE TABLE `scraper_entity` (
  `entity_type` varchar(64) NOT NULL,
  `entity_key` varchar(250) NOT NULL,
  `status` enum('queued','error_http','error_data','error_exception','error_final','processed','ignored') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'queued',
  `status_detail` varchar(4096) NOT NULL DEFAULT '',
  `status_error_count` int NOT NULL DEFAULT '0',
  `priority` int NOT NULL DEFAULT '0',
  `date_created` datetime NOT NULL,
  `date_last` datetime DEFAULT NULL,
  `date_last_success` datetime DEFAULT NULL,
  `extras` varchar(4096) NOT NULL,
  PRIMARY KEY (`entity_type`,`entity_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci