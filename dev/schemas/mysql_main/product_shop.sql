CREATE TABLE `product_shop` (
  `product_id` int unsigned NOT NULL DEFAULT '0',
  `shop_id` tinyint unsigned NOT NULL DEFAULT '0',
  `price_group_id` smallint unsigned NOT NULL DEFAULT '1',
  `vk_brutto` double NOT NULL DEFAULT '0',
  `vk_netto` double NOT NULL,
  `vk_spanne` float NOT NULL,
  `profit_net_amount` double NOT NULL DEFAULT '0',
  `profit_percentage` float NOT NULL DEFAULT '0',
  `profit_classification` enum('unknown','excellent','good','reasonable','bad') NOT NULL DEFAULT 'unknown',
  `vk_formel` varchar(80) NOT NULL DEFAULT '',
  `vk_typ` enum('brutto','netto','spanne','formel') NOT NULL DEFAULT 'brutto',
  `vk_round` enum('x_xx','x_x0','x_99','x_95','x_90','x_00','x5_00','x0_00','x_x9') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'x_xx',
  `mwst_satz` tinyint unsigned NOT NULL,
  `product_vat_rate_id` int NOT NULL DEFAULT '1',
  `campaign_id` varchar(16) NOT NULL DEFAULT '',
  `katwerbung` tinyint unsigned NOT NULL DEFAULT '0',
  `hauptwerbung` tinyint unsigned NOT NULL DEFAULT '0',
  `sonderwerbung` tinyint(1) NOT NULL DEFAULT '0',
  `sonderwerbung_datum` date DEFAULT NULL,
  `lieferbaranzeige` int unsigned NOT NULL DEFAULT '0',
  `lieferbaranzeige_date` datetime NOT NULL,
  `versand_id` int unsigned NOT NULL DEFAULT '0',
  `park` tinyint unsigned NOT NULL DEFAULT '0',
  `park_reason_id` int DEFAULT '0',
  `sperrgut` tinyint unsigned NOT NULL DEFAULT '0',
  `psm_export_disable` tinyint unsigned NOT NULL DEFAULT '0',
  `psm_export_force` tinyint unsigned NOT NULL DEFAULT '0',
  `not_orderable` tinyint unsigned NOT NULL DEFAULT '0',
  `ad_price_strike` tinyint unsigned NOT NULL DEFAULT '0',
  `wochenendpreis_aktiv` tinyint unsigned NOT NULL DEFAULT '0',
  `extra_services_manuell` tinyint unsigned NOT NULL DEFAULT '0',
  `extra_services` varchar(40) NOT NULL DEFAULT '',
  `versandkosten_min` float NOT NULL DEFAULT '0',
  `versandkosten` varchar(1024) NOT NULL DEFAULT '',
  `show_product_media_id` int NOT NULL DEFAULT '0',
  `shop_template` varchar(24) NOT NULL DEFAULT 'standard',
  `expressable` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`product_id`,`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci