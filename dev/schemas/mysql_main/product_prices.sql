CREATE TABLE `product_prices` (
  `product_id` int unsigned NOT NULL,
  `price_group_id` smallint unsigned NOT NULL,
  `price_active` tinyint NOT NULL DEFAULT '0',
  `price_manual` tinyint NOT NULL DEFAULT '1',
  `vk_brutto` double NOT NULL DEFAULT '0',
  `vk_netto` double NOT NULL DEFAULT '0',
  `vk_spanne` float NOT NULL DEFAULT '0',
  `profit_net_amount` double NOT NULL DEFAULT '0',
  `profit_percentage` float NOT NULL DEFAULT '0',
  `profit_classification` enum('unknown','excellent','good','reasonable','bad') NOT NULL DEFAULT 'unknown',
  `vk_formel` varchar(80) NOT NULL DEFAULT '',
  `vk_typ` enum('brutto','netto','spanne','formel') NOT NULL DEFAULT 'brutto',
  `vk_round` enum('x_xx','x_x0','x_99','x_95','x_90','x_00','x5_00','x0_00','x_x9') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'x_xx',
  `mwst_satz` tinyint unsigned NOT NULL,
  `product_vat_rate_id` int NOT NULL DEFAULT '1',
  `extra_charge_net` float NOT NULL DEFAULT '0',
  `price_date` datetime NOT NULL,
  `preisautomatik_aktiv` tinyint(1) NOT NULL DEFAULT '0',
  `preisautomatik_platz` tinyint NOT NULL DEFAULT '0',
  `preisautomatik_platz_behind` tinyint NOT NULL DEFAULT '0',
  `preisautomatik_formel` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `preisautomatik_shipping_cost_handling` enum('auto','versdiff','versdiffu','none') NOT NULL DEFAULT 'auto',
  `preisautomatik_versand` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`product_id`,`price_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci