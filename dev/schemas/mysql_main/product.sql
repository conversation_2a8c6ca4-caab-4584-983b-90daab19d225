CREATE TABLE `product` (
  `product_id` int unsigned NOT NULL AUTO_INCREMENT,
  `product_group` int unsigned NOT NULL DEFAULT '0',
  `product_status` tinyint unsigned NOT NULL DEFAULT '0',
  `product_nr` varchar(12) NOT NULL DEFAULT '',
  `product_warenkorb_typ` varchar(20) NOT NULL DEFAULT 'produkt',
  `product_shop_url` varchar(255) NOT NULL,
  `product_shop_url_manual` tinyint NOT NULL DEFAULT '0',
  `product_tags` varchar(256) NOT NULL DEFAULT '',
  `product_set_id` int unsigned NOT NULL DEFAULT '0',
  `edit_reason` varchar(120) NOT NULL DEFAULT '',
  `vip` tinyint unsigned NOT NULL DEFAULT '0',
  `cat_id` int unsigned NOT NULL DEFAULT '0',
  `cat_id_6` int NOT NULL DEFAULT '0',
  `product_name` varchar(80) NOT NULL DEFAULT '',
  `product_name_envkv_suffix` varchar(50) NOT NULL DEFAULT '',
  `brand_id` int NOT NULL,
  `mpn` varchar(40) DEFAULT '',
  `mpn_normalized` varchar(255) NOT NULL DEFAULT '',
  `model_name` varchar(80) NOT NULL DEFAULT '',
  `hersteller_url` varchar(180) DEFAULT '',
  `ean` char(13) NOT NULL DEFAULT '',
  `ean2` varchar(13) NOT NULL DEFAULT '',
  `asin` varchar(10) NOT NULL DEFAULT '',
  `asin_in_use` varchar(10) NOT NULL DEFAULT '',
  `gewicht` float unsigned NOT NULL DEFAULT '0',
  `size_h` float unsigned NOT NULL DEFAULT '0',
  `size_b` float unsigned NOT NULL DEFAULT '0',
  `size_t` float unsigned NOT NULL DEFAULT '0',
  `spez_gewicht` float unsigned NOT NULL DEFAULT '0',
  `spez_size_h` float unsigned NOT NULL DEFAULT '0',
  `spez_size_b` float unsigned NOT NULL DEFAULT '0',
  `spez_size_t` float unsigned NOT NULL DEFAULT '0',
  `colis` tinyint NOT NULL DEFAULT '1',
  `paketfaehig` tinyint NOT NULL DEFAULT '0',
  `bemerkung_einkauf` longtext NOT NULL,
  `bemerkung_verkauf` longtext NOT NULL,
  `product_type` varchar(10) NOT NULL DEFAULT '',
  `date_create` datetime NOT NULL,
  `date_last` datetime NOT NULL,
  `anzulegen_prioritaet` int unsigned NOT NULL DEFAULT '1',
  `marker` tinyint unsigned NOT NULL DEFAULT '0',
  `product_is_set` tinyint unsigned NOT NULL DEFAULT '0',
  `features_status` enum('pflicht','unvollstaendig','vollstaendig') NOT NULL DEFAULT 'pflicht',
  `product_clip_show` tinyint unsigned NOT NULL DEFAULT '0',
  `product_clip_url` varchar(250) NOT NULL DEFAULT '',
  `product_score` int NOT NULL DEFAULT '0',
  `versand_gruppe` enum('','A','B','C') NOT NULL DEFAULT '',
  `versand_id` tinyint NOT NULL DEFAULT '0',
  `grundpreis_aktiv` tinyint unsigned NOT NULL DEFAULT '0',
  `grundpreis_einheit` varchar(10) NOT NULL DEFAULT '',
  `grundpreis_faktor` float NOT NULL DEFAULT '0',
  `product_without_inventory` tinyint unsigned NOT NULL DEFAULT '0',
  `product_with_serials` tinyint unsigned NOT NULL DEFAULT '1',
  `auto_order_processing` tinyint(1) NOT NULL DEFAULT '0',
  `uvp` float NOT NULL DEFAULT '0',
  `lager_qualifikation` enum('-','A','B','C','D','E','F','X','XXX') NOT NULL DEFAULT '-',
  `product_variations` mediumtext NOT NULL,
  `amazon_fee_id` tinyint NOT NULL DEFAULT '1',
  `amazon_profile` tinyint NOT NULL DEFAULT '1',
  `amazon_max_vk` float DEFAULT NULL,
  `ebay_fee_id` tinyint NOT NULL DEFAULT '99',
  `invisible` tinyint(1) NOT NULL DEFAULT '0',
  `eek` varchar(4) NOT NULL DEFAULT '',
  `eek_scale_id` varchar(10) NOT NULL DEFAULT '',
  `eek_label_product_media_id` int NOT NULL DEFAULT '0',
  `eek_fiche_product_media_id` int NOT NULL DEFAULT '0',
  `deleted_picture_fingerprints` varchar(128) NOT NULL DEFAULT '',
  `ek_limit_min` float DEFAULT '0',
  `condition_id` tinyint NOT NULL DEFAULT '1',
  `eprel_id` int DEFAULT '0',
  `delivery_slip_hint` varchar(100) NOT NULL DEFAULT '',
  `delivery_slip_hint_manual` varchar(80) NOT NULL DEFAULT '',
  `genuine_part` tinyint DEFAULT NULL,
  `check24_fee_id` tinyint NOT NULL DEFAULT '1',
  `kaufland_fee_id` tinyint NOT NULL DEFAULT '1',
  `galaxus_fee_id` tinyint NOT NULL DEFAULT '99',
  `last_device_seo_index` date NOT NULL DEFAULT (curdate()),
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `product_nr_2` (`product_nr`),
  KEY `cat_id` (`cat_id`),
  KEY `product_typ` (`product_type`),
  KEY `ean` (`ean`),
  KEY `product_warenkorb_typ` (`product_warenkorb_typ`),
  KEY `cat_id_5` (`cat_id_6`),
  KEY `hersteller_art_nr` (`mpn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci