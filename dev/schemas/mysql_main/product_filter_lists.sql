CREATE TABLE `product_filter_lists` (
  `filter_list_id` tinyint unsigned NOT NULL AUTO_INCREMENT,
  `base_filter_list_id` tinyint unsigned NOT NULL DEFAULT '0',
  `cat_tree_id` tinyint unsigned NOT NULL DEFAULT '1',
  `type` enum('whitelist','blacklist') NOT NULL DEFAULT 'whitelist',
  `name` varchar(80) NOT NULL,
  `description` mediumtext NOT NULL,
  `price_group_id` smallint unsigned NOT NULL DEFAULT '0',
  `shop_id` int NOT NULL DEFAULT '1',
  `position` int NOT NULL,
  `availability_metrics` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`filter_list_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci