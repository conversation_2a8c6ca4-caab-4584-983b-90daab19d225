CREATE TABLE `product_extra_services` (
  `service_id` int NOT NULL AUTO_INCREMENT,
  `service_active` tinyint NOT NULL DEFAULT '1',
  `service_position` int NOT NULL DEFAULT '9999',
  `service_name_intern` varchar(80) NOT NULL,
  `service_name_shop` varchar(80) NOT NULL,
  `service_name_warenkorb` varchar(200) NOT NULL,
  `service_dependencies` varchar(40) NOT NULL DEFAULT '',
  `service_handler` enum('fix_vk','wertgarantie') NOT NULL,
  `service_handler_extra` varchar(100) NOT NULL,
  `service_ek_brutto` float NOT NULL,
  `service_vk_brutto` float NOT NULL,
  `service_beschreibung_points` mediumtext NOT NULL,
  `service_beschreibung` mediumtext NOT NULL,
  `service_logo` varchar(40) NOT NULL,
  `service_auto_booking` tinyint NOT NULL DEFAULT '0',
  `service_expressable` tinyint NOT NULL DEFAULT '0',
  `product_ids` varchar(120) NOT NULL,
  PRIMARY KEY (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci