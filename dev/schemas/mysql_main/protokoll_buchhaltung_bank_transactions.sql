CREATE TABLE `protokoll_buchhaltung_bank_transactions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` int NOT NULL,
  `date_added` datetime NOT NULL,
  `user_id` mediumint NOT NULL DEFAULT '-1',
  `type` enum('stamm') DEFAULT NULL,
  `type_id` int unsigned NOT NULL DEFAULT '0',
  `field_id` smallint unsigned NOT NULL,
  `new_value` varchar(1024) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci