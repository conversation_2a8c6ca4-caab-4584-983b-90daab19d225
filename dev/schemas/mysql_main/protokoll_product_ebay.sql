CREATE TABLE `protokoll_product_ebay` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `product_id` int unsigned NOT NULL,
  `date_added` datetime NOT NULL,
  `user_id` mediumint NOT NULL DEFAULT '-1',
  `type` enum('','specific') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type_id` varchar(64) NOT NULL DEFAULT '',
  `field_id` smallint unsigned NOT NULL,
  `new_value` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci