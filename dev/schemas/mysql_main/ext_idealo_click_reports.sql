CREATE TABLE `ext_idealo_click_reports` (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_id` varchar(64) NOT NULL,
  `date_begin` datetime NOT NULL,
  `date_end` datetime NOT NULL,
  `date_first` datetime DEFAULT NULL,
  `date_last` datetime DEFAULT NULL,
  `request_date` datetime NOT NULL,
  `status` enum('processing','failed','imported','download_fault','file_fault') NOT NULL DEFAULT 'processing',
  `error_message` varchar(255) DEFAULT '',
  `click_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `report_id` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci