CREATE TABLE `protokoll_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` int unsigned NOT NULL,
  `date_added` datetime NOT NULL,
  `user_id` int NOT NULL,
  `type` enum('order','item','order_addresses') NOT NULL,
  `type_id` int unsigned NOT NULL,
  `field` varchar(32) NOT NULL,
  `field_id` smallint unsigned NOT NULL DEFAULT '0',
  `new_value` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci