CREATE TABLE `product_features` (
  `feature_id` int NOT NULL AUTO_INCREMENT,
  `feature_group_id` mediumint unsigned NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `feature_type` enum('headline','text','text_long','float','int','bit','enum','enum_multi') NOT NULL,
  `feature_search_behavior` enum('and','or') NOT NULL DEFAULT 'and',
  `feature_prefix` varchar(16) NOT NULL,
  `feature_suffix` varchar(16) NOT NULL,
  `feature_pos` tinyint unsigned NOT NULL DEFAULT '0',
  `feature_description` mediumtext NOT NULL,
  `feature_required` tinyint(1) NOT NULL DEFAULT '0',
  `feature_search` tinyint(1) NOT NULL DEFAULT '0',
  `feature_old_envkv` tinyint NOT NULL DEFAULT '0',
  `old_feature_urlify` varchar(255) NOT NULL DEFAULT '' COMMENT 'Mapping Values auf URL',
  `enum_values` mediumtext NOT NULL,
  `match_values` mediumtext NOT NULL,
  PRIMARY KEY (`feature_id`),
  <PERSON>EY `feature_group_id` (`feature_group_id`),
  KEY `feature_old_envkv` (`feature_old_envkv`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci