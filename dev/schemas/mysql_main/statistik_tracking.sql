CREATE TABLE `statistik_tracking` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `affiliate` varchar(30) NOT NULL DEFAULT '',
  `product_id` int NOT NULL DEFAULT '0',
  `shop_id` tinyint unsigned NOT NULL DEFAULT '0',
  `datum` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `bestellt` tinyint NOT NULL DEFAULT '0',
  `order_id` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `product` (`product_id`),
  KEY `order_id` (`order_id`),
  KEY `affiliate` (`affiliate`),
  KEY `datum` (`datum` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci PACK_KEYS=0