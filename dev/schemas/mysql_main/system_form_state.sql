CREATE TABLE `system_form_state` (
  `form_state_id` varchar(30) NOT NULL,
  `user_id` int NOT NULL DEFAULT '0',
  `date_added` datetime NOT NULL,
  `form_key` varchar(128) NOT NULL,
  `form_method` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `form_url` varchar(128) NOT NULL,
  `description_short` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `form_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`form_state_id`),
  KEY `form_key` (`form_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci