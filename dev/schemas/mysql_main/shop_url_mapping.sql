CREATE TABLE `shop_url_mapping` (
  `mapping_id` varchar(255) NOT NULL,
  `mapping_status` enum('valid','invalid') NOT NULL DEFAULT 'valid',
  `datum` datetime NOT NULL,
  `new_mapping` varchar(255) NOT NULL DEFAULT '',
  `mapping_typ` enum('kategorie','product','hersteller','page','cms','controller','404','filter','repairguide_symptom','repairguide_instruction','redirect','repairguide_errorcode','repairguide_errorcode_symptom','device','goodie') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'page',
  `shop_id` int NOT NULL,
  `mapping_value` varchar(50) NOT NULL DEFAULT '',
  `show_in_sitemap` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`mapping_id`,`shop_id`),
  KEY `mapping_typ` (`mapping_typ`,`mapping_value`(8)),
  KEY `new_mapping` (`new_mapping`(40))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci