<?php

use bqp\db\db_generic;
use bqp\db\Migration\DbMigration;
use bqp\db\Migration\DbMigrationResult;
use bqp\db\Migration\DbMigrationTools;

class m20251118112453_protokoll_order_id_migration_step_1 implements DbMigration
{
    public function run(db_generic $db, DbMigrationTools $tools): DbMigrationResult
    {
        //id hinzufügen und unnätiogen index auf order_id, date_added durch order_id ersetzen
        if (!$tools->columnExists('protokoll_order.id') && !$tools->tableExists('protokoll_order_migration')) {
            $db->query("RENAME TABLE protokoll_order TO protokoll_order_migration");
            $db->query("CREATE TABLE protokoll_order LIKE protokoll_order_migration");
            $db->query("
                ALTER TABLE
                    protokoll_order_migration
                ADD `id` BIGINT NOT NULL AUTO_INCREMENT FIRST,
                ADD PRIMARY KEY (`id`),
                DROP INDEX order_id,
                ADD INDEX order_id (order_id) USING BTREE,
                ADD field_id SMALLINT UNSIGNED NOT NULL DEFAULT 0 AFTER field
            ");
        }

        return new DbMigrationResult();
    }
}
