<?php

use bqp\db\db_generic;
use bqp\db\Migration\DbMigration;
use bqp\db\Migration\DbMigrationResult;
use bqp\db\Migration\DbMigrationTools;

class m20251118131004_protokoll_order_id_migration_step_2 implements DbMigration
{
    public function run(db_generic $db, DbMigrationTools $tools): DbMigrationResult
    {
        $result = new DbMigrationResult();

        if (!$tools->tableExists('protokoll_order_migration')) {
            return $result;
        }

        if ($tools->columnExists('protokoll_order.id')) {
            return $result;
        }

        $temp_table_name = 'protokoll_order_tmp_step_2';
        $locked = false;

        try {
            $db->query('LOCK TABLES protokoll_order WRITE, protokoll_order_migration WRITE');
            $locked = true;

            $db->query("
                INSERT INTO
                    protokoll_order_migration (
                        order_id,
                        date_added,
                        user_id,
                        type,
                        type_id,
                        field,
                        new_value
                    )
                SELECT
                    protokoll_order.order_id,
                    protokoll_order.date_added,
                    protokoll_order.user_id,
                    protokoll_order.type,
                    protokoll_order.type_id,
                    protokoll_order.field,
                    protokoll_order.new_value
                FROM
                    protokoll_order
            ");

            $db->query("
                RENAME TABLE
                    protokoll_order TO `{$temp_table_name}`,
                    protokoll_order_migration TO protokoll_order,
                    `{$temp_table_name}` TO protokoll_order_migration
            ");
        } finally {
            if ($locked) {
                $db->query('UNLOCK TABLES');
            }
        }

        return $result;
    }
}
