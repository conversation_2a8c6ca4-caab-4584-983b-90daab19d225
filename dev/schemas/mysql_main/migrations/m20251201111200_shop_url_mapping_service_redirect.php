<?php

use bqp\db\db_generic;
use bqp\db\Migration\DbMigration;
use bqp\db\Migration\DbMigrationResult;
use bqp\db\Migration\DbMigrationTools;

//file-name="m${YEAR}${MONTH}${DAY}${HOUR}${MINUTE}${SECOND}_${NAME}"

class m20251201111200_shop_url_mapping_service_redirect implements DbMigration
{

    public function run(db_generic $db, DbMigrationTools $tools): DbMigrationResult
    {
        if (!env::isEcom()) {
            return new DbMigrationResult();
        }

        $redirects = [
            // alte URL => neue URL
            'service-center/' => 'service/',
            'service-center/2plus3-garantie.html' => 'service/anfrage/?section=garantie-gewaehrleistung',
            'service-center/bestellvorgang.html' => 'service/',
            'service-center/batterie-entsorgung.html' => 'altbatterieentsorgung.html',
            'service-center/datenschutz.html' => 'datenschutz.html',
            'service-center/finanzierung.html' => 'service/anfrage/?section=zahlungsarten&sub=finanzierung',
            'service-center/garantieverlaenergung.html' => 'service/anfrage/?section=garantie-gewaehrleistung',
            'service-center/garantieverlaengerung.html' => 'service/anfrage/?section=garantie-gewaehrleistung',
            'service-center/gewaehrleistung.html' => 'service/anfrage/?section=garantie-gewaehrleistung',
            'service-center/jobs.html' => 'service/',
            'service-center/lieferbaranzeige.html' => 'service/anfrage/?section=lieferzeiten',
            'service-center/lieferung.html' => 'service/anfrage/?section=versandarten-abholung',
            'service-center/preispolitik.html' => 'service/',
            'service-center/reklamation.html' => 'service/',
            'service-center/reklamation/hersteller.html' => 'service/',
            'service-center/serviceleistungen.html' => 'service/anfrage/?section=zusatzdienstleistungen',
            'service-center/test/' => 'service/',
            'service-center/versandkosten.html' => 'service/anfrage/?section=versandkosten',
            'service-center/versandzusatzleistungen.html' => 'service/anfrage/?section=zusatzdienstleistungen',
            'service-center/zahlung_versand.html' => 'service/anfrage/?section=versandkosten',
            'service-center/zahlung.html' => 'service/anfrage/?section=zahlungsarten',
        ];

        foreach ($redirects as $old_url => $new_url) {
            $db->query("
                UPDATE shop_url_mapping
                SET
                    mapping_status = 'invalid',
                    new_mapping = " . $db->quote($new_url) . ",
                    mapping_typ = 'redirect',
                    show_in_sitemap = 0
                WHERE
                    mapping_id = " . $db->quote($old_url) . "
            ");
        }

        return new DbMigrationResult();
    }

}
