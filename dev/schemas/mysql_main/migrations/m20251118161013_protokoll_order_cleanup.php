<?php

use bqp\db\db_generic;
use bqp\db\Migration\DbMigration;
use bqp\db\Migration\DbMigrationResult;
use bqp\db\Migration\DbMigrationTools;

//file-name="m${YEAR}${MONTH}${DAY}${HOUR}${MINUTE}${SECOND}_${NAME}"

class m20251118161013_protokoll_order_cleanup implements DbMigration
{

    public function run(db_generic $db, DbMigrationTools $tools): DbMigrationResult
    {
        $result = new DbMigrationResult();

        if (!$tools->columnExists('protokoll_order.field')) {
            return $result;
        }

        $db->query("
            ALTER TABLE protokoll_order
            DROP COLUMN field
        ");

        return $result;
    }

}
