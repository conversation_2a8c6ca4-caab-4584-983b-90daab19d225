<?php

use bqp\db\db_generic;
use bqp\db\Migration\DbMigration;
use bqp\db\Migration\DbMigrationResult;
use bqp\db\Migration\DbMigrationTools;
use bqp\Exceptions\DevException;

class m20251118140000_protokoll_order_field_id_backfill implements DbMigration
{
    private const BATCH_SIZE = 50000;

    private static array $field_cache;

    public function run(db_generic $db, DbMigrationTools $tools): DbMigrationResult
    {
        $result = new DbMigrationResult();

        $min_id = $db->fieldQuery("SELECT MIN(protokoll_order.id) FROM protokoll_order");
        $max_id = $db->fieldQuery("SELECT MAX(protokoll_order.id) FROM protokoll_order");

        if ($min_id === null || $max_id === null) {
            return $result;
        }

        $min_id = (int)$min_id;
        $max_id = (int)$max_id;

        if ($min_id > $max_id) {
            return $result;
        }

        for ($range_start = $min_id; $range_start <= $max_id; $range_start += self::BATCH_SIZE) {
            $range_end = min($max_id, $range_start + self::BATCH_SIZE - 1);

            $fields = $db->query("
                SELECT
                    DISTINCT protokoll_order.field
                FROM
                    protokoll_order
                WHERE
                    protokoll_order.id BETWEEN {$range_start} AND {$range_end} AND
                    protokoll_order.field_id = 0
            ")->asSingleArray();

            if (!$fields) {
                continue;
            }

            $this->createMissingFields($fields);

            $db->query("
                UPDATE
                    protokoll_order INNER JOIN
                    protokoll_field ON (protokoll_field.field_name = protokoll_order.field)
                SET
                    protokoll_order.field_id = protokoll_field.field_id
                WHERE
                    protokoll_order.id BETWEEN {$range_start} AND {$range_end} AND
                    protokoll_order.field_id = 0
            ");
        }

        return $result;
    }

    private function createMissingFields(array $fields): void
    {
        foreach ($fields as $field) {
            $this->getFieldId($field);
        }
    }

    protected function getFieldId(string $field): int
    {
        if (!isset(self::$field_cache)) {
            $this->loadFieldCache();
        }

        if (!isset(self::$field_cache[$field])) {
            $db = db::getInstance();
            $db->simpleInsert('protokoll_field', ['field_name' => $field]);
            self::$field_cache[$field] = $db->insert_id();
        }

        return self::$field_cache[$field];
    }

    protected function loadFieldCache(): void
    {
        self::$field_cache = db::getInstance()->query("
            SELECT
                protokoll_field.field_name,
                protokoll_field.field_id
            FROM
                protokoll_field
        ")->asSingleArray('field_name');
    }
}
