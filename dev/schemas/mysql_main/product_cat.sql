CREATE TABLE `product_cat` (
  `cat_id` int NOT NULL AUTO_INCREMENT COMMENT 'wenn browse_node -> alternativ browse_node_id',
  `cat_tree_id` tinyint unsigned NOT NULL DEFAULT '1',
  `parent_cat_id` int DEFAULT '0',
  `node_type` enum('category','feature_preset','cms','repairguide','repairguide_instruction','repairguide_errorcode') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'category',
  `node_type_value` varchar(24) NOT NULL DEFAULT '',
  `node_show_in_tree` tinyint NOT NULL DEFAULT '1',
  `cat_name` varchar(70) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `feature_group_id` mediumint unsigned NOT NULL DEFAULT '0',
  `cat_name_webshop` varchar(120) NOT NULL,
  `cat_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `cat_shop_url` varchar(200) NOT NULL,
  `cat_shop_url_manual` tinyint(1) NOT NULL DEFAULT '0',
  `cat_shop_visible_calculated` tinyint NOT NULL DEFAULT '0',
  `cat_template` varchar(100) NOT NULL DEFAULT '',
  `cat_template_auto` tinyint NOT NULL DEFAULT '1',
  `cat_pos` int unsigned NOT NULL DEFAULT '0',
  `cat_headline` varchar(200) NOT NULL DEFAULT '',
  `cat_meta_title` varchar(100) NOT NULL DEFAULT '',
  `cat_meta_description` varchar(255) NOT NULL DEFAULT '',
  `product_type` varchar(10) NOT NULL DEFAULT 'w',
  `visible` tinyint unsigned NOT NULL DEFAULT '0',
  `internal` tinyint(1) NOT NULL DEFAULT '0',
  `noarticles` tinyint unsigned NOT NULL DEFAULT '0',
  `cat_picture` varchar(64) NOT NULL DEFAULT '',
  `cat_id_ebene_1` int unsigned NOT NULL DEFAULT '0',
  `cat_id_ebene_2` int unsigned NOT NULL DEFAULT '0',
  `cat_id_ebene_3` int NOT NULL,
  `cat_id_ebene_4` int NOT NULL,
  `cat_id_path` varchar(100) NOT NULL,
  `cat_path_text` varchar(200) NOT NULL DEFAULT '',
  `warennummer` longtext NOT NULL,
  `buy_prodanet` tinyint unsigned NOT NULL DEFAULT '0',
  `promo_1_picture` varchar(255) NOT NULL,
  `promo_1_typ` enum('url','content_id','product_id','') NOT NULL DEFAULT '',
  `promo_1_value` varchar(255) NOT NULL,
  `versand_gruppe` enum('A','B','C') NOT NULL DEFAULT 'A',
  `std_versand_id` int unsigned NOT NULL DEFAULT '0',
  `avg_weight` float NOT NULL,
  `unsorted` tinyint(1) NOT NULL DEFAULT '0',
  `extra_services` varchar(60) NOT NULL,
  `google_taxonomie` varchar(200) NOT NULL DEFAULT '',
  `amazon_fee_id` tinyint NOT NULL DEFAULT '1',
  `amazon_browse_node` bigint DEFAULT NULL,
  `ebay_cat_id` mediumint NOT NULL DEFAULT '0',
  `ebay_shop_cat_id` varchar(12) DEFAULT NULL,
  `check24_fee_id` tinyint NOT NULL DEFAULT '1',
  `kaufland_fee_id` tinyint NOT NULL DEFAULT '1',
  `misc_keywords` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`cat_id`),
  KEY `parent_cat_id` (`parent_cat_id`),
  KEY `product_typ` (`product_type`),
  KEY `cat_tree_id` (`cat_tree_id`),
  KEY `cat_id_path` (`cat_id_path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci