CREATE TABLE `orders_storno_item` (
  `storno_item_id` int NOT NULL AUTO_INCREMENT,
  `storno_id` int NOT NULL,
  `order_item_id` int NOT NULL,
  `product_id` int NOT NULL,
  `old_status_id` int NOT NULL,
  PRIMARY KEY (`storno_item_id`),
  <PERSON><PERSON>Y `idx_storno_id` (`storno_id`),
  <PERSON>EY `idx_order_item_id` (`order_item_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci