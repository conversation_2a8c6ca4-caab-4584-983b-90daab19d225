# Task Scheduling

Das System verwendet cron um wiederkehrende Tasks und "endlos laufende" Prozesse/Scripte abzubilden.

Es gibt ein crontab file was unter crons/crontab liegt. Beim upload dieser Datei wird dieses crontab automatisch installiert.
Dafür ist es nötig das crons/system/copy_crontab.php auf dem Live System eingerichtet ist.
(Wird ins Home von root kopiert und dieser richtet selbst in seiner crontab "* * * * * php /root/copy_crontab.php" ein)

@todo https://allego.myjetbrains.com/youtrack/issue/WWS-1786

## Concurrency

Es ist drauf zu achten, dass die crons nicht parallel laufen können. Das System bietet `service_loader::runtimeLock()` das
entsprechend Tooling. Z.B. `service_loader::runtimeLock()->tryLock(__FILE__);` rein schauen für weitere Details/Optionen.
Der Lock wird aktuell über Mysql locking system abgebildet. Mehrere Systeme, Berechtigung, Abstürzte, Bereinigungen etc.
alles kein Problem. Es gibt ein bekanntes Problem: wenn der Prozess sich aufhängen sollte, oder extrem lange (24h?) nicht
die Datenbank anspricht, geht der Lock verloren. (nur einmal bei K11 gesehen, die haben bei einem API Call kein Read
Timeout im Client gesetzt gehabt.)

## Fehlerbehandlung/Error Handling bei Crons

Arme-Leute-Variante: es wird in der crontab MAILTO= verwendet. Alle Ausgaben auf STDOUT und STDERR werden an diese
Mail-Adresse gesendet. Ein spezifischer return code ist nicht nötig. Auf Debug Ausgaben sind zu vermeiden.

## Endlos laufende Prozesse

Auch hier wieder die Arme-Leute-Variante: es wird ein cron erstellt, der das Script z.B. alle 5 Minuten startet. Durch das
Concurrency wird sichergestellt, dass der Prozess nur einmal läuft. (oder ggf. nur n mal)
 
GRUND: Warum kein systemd oder ähnliches? Einfachheit. Einrichtung über die crontab. Fehlerbehandlung/Verhalten identisch wie
die Crons. 

## Asynchrone Ausführung 

Das System bietet über den Event Dispatcher an, Events asynchron zu verarbeiten (subscribe). In configs/event_subscriptions.conf.php
werden zu dem entsprechende Events die Event Handler als listener_async registriert. Dieser Event Handler kann manuell ausgeführt
werden, oder von einem vorgefertigten "runner" ausgeführt werden (siehe configs/event_handlers.conf.php).

Wichtig: teilweise nutzt das System intern caches, diese können nicht invalidiert werden. Es kann daher sinnvoll sein,
wenn absehbar ist, dass ein Event Handler in so ein Problem läuft, dass er den Runner mit einer Exception am Ende "abwirkt".
Damit startet der runner neu und die caches sind wieder leer.

Alternativ gibt es den Event "\bqp\Event\Job". Daqs ist ein Event mit eingebeten Event-Handler und fertig konfigurierten Runner.



