# Entwicklungsumgebung

Die Entwicklungsumgebung/Laufzeitumgebung ist mit DDEV realisiert. https://ddev.readthedocs.io/
DDEV ist ein Frontend für Docker mit gepflegten Images und Konfigurationen für PHP, MySQL, etc.

## Plugins PhpStorm

- [DDEV Plugin](https://plugins.jetbrains.com/plugin/18813-ddev-integration) \
  Automatische Konfiguration von PhpStrom anhand von DDEV (Server, PHP Interpreter, DB Connection, etc) und verschiedenes mehr.
- [IDE Remote Controll](https://plugins.jetbrains.com/plugin/19991-ide-remote-control) \
  Erlaubt es Dateinamen von xdebug per Klick in PhpStorm zu öffnen.

## Entwicklungsumgebung DDEV

DDEV wird über das .ddev Verzeichnis im Projekt root konfiguriert. \
Zwischen dem eCom (master) und K11 (k11) Branch kann derzeit nicht mittels git checkout gewechselt werden. Das liegt an
den derzeit unterschiedlichen Konfigurationen für ddev.

Voraussetzungen: bitbucket Account mit hinterlegten public key.
Bitbucket Token / Composer

Setup:
1. [DDEV installieren](https://ddev.readthedocs.io/en/latest/users/install/ddev-installation/)
2. <NAME_EMAIL>:ecomdd/wws.git
3. Im Projekt-Root `ln -s dev/ddev/ecom .ddev` bzw. dev/ddev/k11
4. ```bash
   ddev start
   ```
5. key agent forwarding in die Docker Container (auf Linux/MacOS nicht nötig SSH_AUTH_SOCK wird in den Container durchgereicht)
   ```bash
   ddev auth ssh
   ```   
6. Key und Secret siehe Keeper "Bitbucket Token / Composer" \
   ```bash 
   ddev composer config --global --auth bitbucket-oauth.bitbucket.org [key] [secret]
   ```
7. ```bash
   ddev composer install
   ```
8. aktuellen Snapshot von der DB besorgen und in /.ddev/db_snapshots/ ablegen
9. ```bash
   ddev snapshot restore --latest
   ```
10. im Projekt root eine Datei mit dem Namen TESTMODUS anlegen und "dev" hineinschreiben. \
    Diese Datei steuert, welche System-Konfiguration genutzt wird. \
    Standardmäßig wird /configs/system.conf.php genutzt. Die Datei TESTMODUS mit "dev" lädt
    /configs/system_dev.conf.php
11. Projekt root eine Datei mit dem Namen modus_ecom_k11.php anlegen.
    ```php
    <?php
    return 1;
    ```
    1 für ecom, 2 für k11. Diese Datei steuert das Basisverzeichnis für die Konfiguration.
    configs/ecom und configs/k11 \
12. Fertig

13. ggf. Datenbankmigration
```bash
ddev php dev/bin/migrate_db.php
```
__ACHTUNG__ es hat sich gezeigt, wenn das Schema zu alt ist, dass es Probleme geben kann mit den Migrationen. \
Die Migrationen sind "Setups"/"Updates", die u.U. die Geschäftslogik nutzen. Die Geschäftslogik selbst kann 
zwischenzeitlich geändert wurden sein, sodass diese selbst erst wieder mit einer nachfolgenden Migration lauffähig ist.
Das ist ein Henne-Ei-Problem. Es kann daher nötig sein, ältere Commits auszuchecken, die Migrations auszuführen und sich schrittweise
den Master anzunähern.
Hab die KI zusammenschustern lassen:
> dev/bin/migrate_db_git_history.php
(Master auschecken, Script ausführen, Ausgabe ausführen... überraschen lassen.)
--------

(14. ggf. siehe [Elasticsearch](#Elasticsearch))



http://wws.ecom.ddev.site/ -> zeigt auf /web/wws \
http://dev.ecom.ddev.site/ -> zeigt auf /
http://smartgoods.ecom.ddev.site/ -> zeigt auf /shops/allego2/public/
...

## Konfiguration PHPStorm:
Das ddev Plugin richtet die PHP Interpreter, Datenquellen, Mappings in PHPStorm automatisch ein. \
Für "Open in Browser" muss unter Einstellungen -> Deployment ein neuer Eintrag mit "in place" und den hostnamen "dev.ecom.ddev.site" hinzugefügt werden.


## Besonderheiten Windows
- pagent ins WSL https://github.com/BlackReloaded/wsl2-ssh-pageant
- alles in WSL machen, nur phpstrom in Windows laufen lassen



## Testdatenbank

@todo
siehe dev/utils/test_database/readme.md

## Elasticsearch
Initialisierung und Generierung der Daten für Elasticsearch
1. [dev/schemas/elasticsearch/init.php](../../schemas/elasticsearch/init.php)
2. [utils/elasticsearch/generate_allego_products.php](../../../utils/elasticsearch/generate_allego_products.php)
   Achtung: das Script queued nur noch.
   Es muss crons/ecom/indexer/product_indexer_sg_entity_queue.php danach ausgeführt werden.
   Fortschritt und Fehler können im WWS unter "System" -> "Entity Queue" nachvollzogen werden. 
3. [utils/elasticsearch/generate_allego_devices.php](../../../utils/elasticsearch/generate_allego_devices.php)

## lokalen Code mit Live-Daten 

> [!DANGER] VORSICHTIG / Live-Datenbank und Storage 
> - Fehler können katastrophale Folgen haben
> - Datenschutz
> - Dateisystem (storage) ist nicht überall vollständig abstrahiert!
> - Mails mit dev config gehen nicht an die richtigen Empfänger

gesonderte Config erstellen
die Config kann über ./testmodus je nach Bedarf gewechselt werden

ssh tunel (ddev tunnel)
storage_config => ecom/storage_live
Datenbank Verbindungen müssen neu Konfiguriert werden
etc...

```bash
ddev tunnel
 ```

oder

```bash
ddev tunnel &
 ```

