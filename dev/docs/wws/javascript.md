WIP 

Ich hab im WWS so ein lustiges transparentes nachladen mit Javascript implementiert um Frames zu ersetzen.
Das war ein <PERSON>hler. Das gibt an allen möglichen Stellen diverse Probleme und Workarounds. 

# PHP-Seitig eine Javascript Datei einbinden, so das diese auch mit smartDisplay() nicht mehrfach vom Client geladen wird:
PHP-Seitig kann eine Javascript Datei mittels `$tpl->addJsOnce();` eingebunden werden. mit addJsOnce() wird diese Javascript
nur ausgeführt/geladen, solange dies noch nicht vom Client geladen wurden.
Hier ist auf den State zu achten und die initialsierung.
DOMContentLoaded kann mittels DOMContentLoadedWws abgebildet werden:
```
document.addEventListener("DOMContentLoadedWws", function(event) { ... });
```
Achtung: Hier gabs auch noch irgendwelche lustigen Probleme. <PERSON><PERSON> was da war.
   
   
