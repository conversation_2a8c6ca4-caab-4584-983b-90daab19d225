# Code Style

## PHP

PSR+12 https://www.php-fig.org/psr/psr-12/
Ausnahme Zeilenlänge: Softlimit ist 240.

> [!WARNING] Autoformat von PHPStorm sollte nicht blind verwendet werden! Das zerstört u.U. SQL Queries, oder zumindestens die Formatierung.

Variablen immer __\$snake_case__ nicht __\$pascalCase__.

Benennung von Variablen besser zu lang als zu kurz.
Benennung von Variablen möglichst Konsistent halten und im einklang mit Datenbank. Schlecht \$customer_id, \$customerid, \$c_id, \$cust_id, \$cust_nr -> besser nur \$customer_id.

Codestyle kann mit PHP_CodeSniffer kontrolliert und gefixt werden. PHP_CodeSniffer ist in PHPStorm über die Projekt-Konfiguration fertig eingerichtet.  

Ruleset: dev/bin/phpcs_ruleset.xml.  

Prüfen
```bash
phpcs --standard=dev/bin/phpcs_ruleset.xml .
```

Fixen (Beispiele)
```bash
phpcbf --standard=dev/bin/phpcs_ruleset.xml --sniffs=Generic.Files.LineEndings --extensions=php LIB/core
phpcbf --standard=dev/bin/phpcs_ruleset.xml --sniffs=Generic.Files.LineEndings,PSR2.Classes.ClassDeclaration,Squiz.WhiteSpace.SuperfluousWhitespace,PSR12.Files.FileHeader --extensions=php LIB
phpcbf --standard=dev/bin/phpcs_ruleset.xml --extensions=php web
```


## SQL

- SQL Keywords groß.
- Aliase, insbesondere Tabellen Aliase, nur wenn nötig
- Spalten immer mit Tabelle referenzieren (product.product_id)
- eine Anweisung pro Zeile
- explizite JOINs
- \`Ticks\`, nur wenn nötig

Schlecht:

```SQL
SELECT product_id,pc.`product_type` FROM product p JOIN product_cat pc ON p.product_id = pc.product_id WHERE pc.cat_id = 1
```

Gut:
```SQL
SELECT
    product.product_id,
    product_cat.product_type 
FROM
    product INNER JOIN
    product_cat ON (product.product_id = product_cat.product_id)
WHERE
    product_cat.cat_id = 1
```


## Javascript

...
