# Service Container und Dependency Injection

## PHP-DI
Das System verwendet PHP-DI https://php-di.org/ als Dependency Injection Container.
Eine Dokumentation ist unter https://php-di.org/doc/ verfügbar.

Der Container wird über configs/di/*.php konfiguriert. Bevorzugt setzen wir auf autowiring um die Konfiguration klein zuhalten.

Im System ist der Container über `$container = \service_loader::getContainer();` zugänglich. Der Container bietet als direktes
Interface get(), make() und call() an.

Als Shortcut für `\service_loader::getContainer()->get(FooBar::class);` ist `\service_loader::get(FooBar::class);` möglich.

In \wws\core\wws_controller::class ist der constructor belegt und kann nicht genutzt werden. Für automatische dependency injection
können in den Controllern inject(), inject2() und injectTrait() implementiert werden. Beim initialisieren der Controller werden
diese Methoden falls vorhanden von Dependency Container aufgerufen und die Parameter injiziert.

## Legacy "service_loader"

- \service_loader::class legacy, alter "Container"
- bootstrapping php-di
- global php-di access
- legacy services (noch nicht migriert/bequemes interface)
