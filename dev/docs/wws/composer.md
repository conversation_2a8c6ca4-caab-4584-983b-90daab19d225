# Composer

Dokumentation welche Packages wofür verwendet werden. Leider bietet die composer.json keine Möglichkeit für Kommentare.

> [!TIP] Abgleich mit der composer.json
> 
> `php dev/utils/composer_documentation_check.php`

## DDEV

In DDEV ist Composer integriert, dieser kann über `ddev composer` genutzt werden. Ein lokaler composer kann auch
genutzt werden, benötigt aber unter Umständen `--ignore-platform-reqs`.

## Deployment

Auf dem Live-System ist kein Composer installiert. Es kann mittels `dev/bin/composer_pack.sh` ein Archiv vom
lokalen vendor Ordner erstellt werden und die dev/vendor.tar.gz auf das Liveserver kopiert werden. Zum ausrollen
kann auf dem Live-System `dev/bin/composer_unpack.sh` verwendet werden.

## Composer ecomdd Dependencies

```ddev composer config --global --auth bitbucket-oauth.bitbucket.org SECRET_ID SECRET_KEY```

SECRET_ID SECRET_KEY siehe Keeper
bzw. https://bitbucket.org/ecomdd/workspace/settings/api

Wenn nicht korrekt angegeben, kommt eine Fehlermeldung in Form von: 
> In RemoteFilesystem.php line 477:\
> The "?pagelen=100&fields=values.name%2Cvalues.target.hash%2Cnext&sort=-target.date" file could not be downloaded: Failed to open stream: No such file or directory  

## Composer Dependencies

### WWS Core

__smarty/smarty__: Template Engine vom WWS

__doctrine/orm__: ORM im WWS (tendenziell weg damit, das lässt sich nicht mit SmartData und EntityChanges unter einen Hut bringen)
@deprecated

__tuupola/base62__: "komplexe Daten"/serialized/json Daten per GET übergeben

__league/flysystem__: Dateisystem abstraktion im WWS. Wird auch für externe "Schnittstellen" verwenden. (ftp, stfp, ftps, webdav, etc)
@todo: upgrade? es wird aktuelle v1 verwendet... (upgrade wahrscheinlich aufwending/prüfbarkeit!)

__symfony/mailer__: Mailer für das WWS

__php-di/php-di__: DI Container fürs WWS

__psr/log__: kompatibilität für den logger im WWS mit PSR 3

__laminas/laminas-barcode__: Barcodes für Dokumente im WWS

__phpoffice/phpspreadsheet__: Excel Dateien lesen und erzeugen im WWS

__elasticsearch/elasticsearch__: elasticsearch client für WWS und Shop

__jms/serializer__: Wird für die SupplierOrderConfirmations verwendet um die Probleme mit serialize/unserialize zu umgehen

__horstoeko/zugferd__: ZUGFeRD/XRechnung Support

__jpgraph/jpgraph__: Graphen im WWS

### für externe "Schnittstellen"

__ecomdd/simple-html-dom__: für diverse Scraping Themen

__league/flysystem-sftp__: SFTP Adapter für Flysystem für externe "Schnittstellen" (diverse)

__league/flysystem-webdav__: WebDAV Adapter für Flysystem für externe "Schnittstellen". Dürfte nur Krempl Stammdaten sein?

__nao-pon/flysystem-google-drive__: Google Drive Adapter für Flysystem.
@todo obsolet/dürfte nur damals bei K11 für electrolux verwendet wurden sein

__vladimir-yuldashev/flysystem-curlftp__: ein alternative FTP Adapter für Flysytem. Wird für Check24 gebraucht.
Die verwenden implicit FTPS was PHP nicht kann.

__google/cloud-storage__: Google Bucket Adapter, wird über Flysystem genutzt. Assets und Dokumente des WWS

__google/cloud-vision__: OCR für Produkt-Datenblätter/Energielabels

__google/cloud-logging__: Fetch und Verarbeitung von ASWO-Logs aus GCP für MarketView-Datenimport.

__sabas/edifact__ und __php-edifact/edifact-mapping__: diverse Lieferantenanbindungen

__laminas/laminas-http__: standard HTTP Client für diverse Aufgaben im WWS und diversen Jobs

__jlevers/selling-partner-api__: Amazon Selling Partner API von 3rd Party. Amazon bietet selbst kein PHP SDK mehr an.
Die Lib macht mir aber auch Bauchschmerzen. Die haben in 3, 4 Jahren 7 Major Versionen raus gehauen und dabei ist die zugrunde liegende
Amazon API stable. Da gibts keine breaking changes.
Der Code wird mit einem Code gen erzeugt. Die haben bis 5. openapi-gen verwendet und jetzt Saloon. Der Code von openapi-gen
kommt straight aus der Hölle (das interface ist mit named parameters aber i.O.). Aber es geht schlimmer. Bei Saloon sieht der Code definitiv besser aus, aber es ist alles
ein "Response". Type Hints darf man alle manuell im Code annotieren... klasse.
Und bei der umstellung von openapi-gen auf Saloon haben die auch gesagt "scheiß auf backwards compatibility". Es wäre kein Problem
gewesen, die DTOs kompatibel zu der openapi zu halten.

- __ACHTUNG__ per Hand gepatcht. Aus \SellingPartnerApi\SellingPartnerApi::restrictedAuth() entfernt "\$dataElements = \$this->dataElements;".
  Die Restricted Data Elemente sind auf bestimmte Requests beschränkt. Das wird dadrüber auch schon gefiltert, aber durch die entferne Zeile
  sofort wieder überschreiben.
  (Das ist aber auch richtig bullshit. in der alten Version konnte das auf Requestebene festgelegt werden, ob man Restricted Data Elemente anfordert. Jetzt geht das nur pauschal, alles oder nix.)
- als Alternative gibts es auch direkt von Amazon https://github.com/amzn/selling-partner-api-sdk/blob/main/php/README.md https://packagist.org/packages/amzn-spapi/sdk \
  Die PHP LIB gibt es erst seit 03.2025. Das ist wieder openapi. Die LIB unterstützt __kein RDT__ (Restricted Data Tokens -> also kein PII/Kundendaten). Klasse!

 

__ecomdd/kaufland-api-v2__: client für den Kaufland Marktplatz

__unzerdev/php-sdk__: Client für Unzer Payment

__khanamiryan/qrcode-detector-decoder__: wird zum Auslesen der QR Codes auf den Energielabels verwendet

__league/oauth2-client__: explizit für die Ebay APIs

__cerbero/json-parser__: für die EPREL JSONs (streaming json parser) 

__symfony/http-foundation__: Als Ergänzung für die Controller, für ein sauberes Request/Response Interface. (-> hab aber verpeilt, dass Symfony kein PSR-7 und PSR-15 implementiert.)

## K11 spezifisch
__ecomdd/shopware-api__
__ecomdd/aswo-eed-client__
__ecomdd/taxdoo-api__

__ramsey/uuid__ wurde von K11 mit reingenommen für die AT/OT Preise. Ein AI in Mysql hätte es auch getan.  
@deprecated
@todo tendenziell weg damit

## unklar!
__http-interop/http-factory-guzzle__ in irgendein merge/migrations commit hab ich das mit reingenommen. Es ist gar nicht nachvollziehbar was das ist.

__php-http/curl-client__ / __php-http/message__ irgendeine API hat glaube ich mal HTTP Plug verwendet...

## Dev Dependencies
__phpmd/phpmd__: code quality tool
__squizlabs/php_codesniffer__: code quality tool
__phpstan/phpstan__: code quality tool
__phpunit/phpunit__:
