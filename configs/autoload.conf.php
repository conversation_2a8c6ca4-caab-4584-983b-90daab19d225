<?php

//Die Datei wird nicht vom ConfigManager verwendet, sondern direkt beim initialisieren des autoloaders included

$config = [];

$config['service_loader'] = 'service_loader.php';

$config['config'] = 'core/Config/config.php';

$config['order_repository'] = 'business_logic/Order/order_repository.php';

$config['bestell_vormerkung'] = 'business_logic/bestell_vormerkung.php';
$config['bestell_vormerkung_item'] = 'business_logic/bestell_vormerkung.php';

$config['system_protokoll'] = 'wws/logger/system_protokoll.php';

$config['gutschein'] = 'business_logic/gutscheine/gutschein.php';
$config['gutschein_repository'] = 'business_logic/gutscheine/gutschein_repository.php';

$config['gutschriftsanforderung'] = 'business_logic/gutschriftsanforderung.php';

$config['incoming_invoice'] = 'business_logic/buchhaltung/incoming_invoice.php';
$config['incoming_invoice_image'] = 'business_logic/buchhaltung/incoming_invoice_image.php';
$config['incoming_invoice_repository'] = 'business_logic/buchhaltung/incoming_invoice_repository.php';
$config['incoming_invoice_exception'] = 'business_logic/buchhaltung/incoming_invoice_exception.php';
$config['incoming_invoice_item'] = 'business_logic/buchhaltung/incoming_invoice_item.php';

$config['personenkonto'] = 'business_logic/buchhaltung/personenkonto.php';
$config['personenkonto_repository'] = 'business_logic/buchhaltung/personenkonto_repository.php';
//core
    $config['gz'] = 'core/gz.php';

    $config['b2b_front_controller'] = 'core/b2b_front_controller.php';
    $config['session_handler'] = 'core/session_handler.php';
    $config['persistent_container'] = 'core/persistent_container.php';

    $config['app_environment'] = 'core/app_environment.php';
    $config['env'] = 'core/app_environment.php';

    $config['profiler'] = 'core/debug/profiler.php';
    $config['debug'] = 'core/debug/debug.php';

    $config['url_gateway'] = 'core/url_gateway.php';



    $config['ColorThief'] = '../3thparty/color-thief-php/color-thief-php-1.1.0/lib/ColorThief/';

    //db
    $config['db'] = 'wws/db.php';

    $config['query'] = 'core/db/query.php';
    $config['manipulation_query'] = 'core/db/manipulation_query.php';
    $config['paging_query'] = 'core/db/paging_query.php';
    $config['object_query'] = 'core/db/object_query.php';
    $config['object_query_inferface'] = 'core/db/object_query.php';
    $config['cached_query'] = 'core/db/cached_query.php';
    $config['sql_order'] = 'core/db/sql_order.php';
    $config['cascading_query'] = 'core/db/cascading_query.php';
    $config['cascading_agg_query'] = 'core/db/cascading_agg_query.php';
    $config['olap_query'] = 'core/db/olap_query.php';

    //
    $config['workflow_engine'] = 'core/workflow_engine.php';
    $config['workflow_job'] = 'core/workflow_job.php';
    $config['workflow_job_simple'] = 'core/workflow_job_simple.php';

    //datentypen
    $config['bank_account'] = 'core/datatypes/bank_account.php';

    $config['address'] = 'core/Address/address_legacy.php'; //die klasse wird teilweise noch für konstanten in template gentztz

    $config['zip_writer'] = 'core/files/zip_writer.php';
    $config['dbf_reader'] = 'core/files/dbf_reader.php';

    $config['olap_cube'] = 'core/olap/olap_cube.php';
    $config['olap_cube_diagramm'] = 'core/olap/olap_cube_diagramm.php';
    $config['olap_utils'] = 'core/olap/olap_utils.php';


$config['xzend_http_proxy_rotator'] = 'core/xzend_http_proxy_rotator.php';
$config['xzend_http_google_appengine'] = 'core/xzend_http_google_appengine.php';
$config['xzend_http_proxy_node'] = 'core/xzend_http_proxy_node.php';

//file handling
$config['Mail_mimeDecode'] = 'core/Mail/PearMimeDecoder/mimeDecode.php';

//eingabe
$config['inputFilter'] = 'core/input.php';
$config['input'] = 'core/input.php';

$config['cms'] = 'business_logic/cms/cms.php';

//ausgabe
$config['smarty_b2b'] = 'core/output/smarty_b2b.php';
$config['input_selektor'] = 'core/output/input_selektor.php';
$config['action_selector'] = 'core/output/action_selector.php';
$config['output_component'] = 'core/output/output_component.php';
$config['output'] = 'core/output/output_utilities.php';
$config['mail_editor'] = 'core/output/mail_editor.php';
$config['output_autocomplete'] = 'core/output/output_autocomplete.php';
$config['viewhelper_prototip'] = 'core/output/viewhelper_prototip.php';
$config['pagination'] = 'core/output/pagination.php';


$config['namespaces'] = [
    'bqp\extern' => 'externe_apis/',
    'bqp' => 'core/',
    'wws\frontend' => 'frontend/',
    'wws\core' => 'wws/',
    'wws\WwsController' => '../web/wws/av/',
    'wws' => 'business_logic/',
    'wws\OrderApi' => '../web/wws/api/wws-order-api/1.0.1/lib/',
    'wws\OrderApi\v101' => '../web/wws/api/wws-order-api/1.0.1/lib/',
];

return $config;
