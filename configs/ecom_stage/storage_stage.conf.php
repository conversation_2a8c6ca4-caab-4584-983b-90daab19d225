<?php

use bqp\Flysystem\OverlayAdapter;
use bqp\storage\StorageFactory;

$live_config = include(__DIR__ . '/../ecom/storage_live.conf.php');

$overlays = [
    'wws',
    'mail',
    'media',
    'marketview'
];

$config = [];

foreach ($live_config as $key => $storage_config) {
    if (in_array($key, $overlays)) {
        //Wir packen den ursprünglichen Adapter in den "Overlay Adapter" (geschichtestes Dateisystem, nur die oberste layer ist schreibbar)
        //-> damit können wir die Live-Daten nutzen, aber änderungen erfolgen nur lokal.
        //Die Datenbank von Stage ist eine Kopie von live, somit haben wir ein "relativ konsistenten"
        //Zustand. Kopieren ist aufgrund der größe der Buckets unpraktikabel.
        $existing_adapter_config = $storage_config['adapter_config'];

        $storage_config['adapter_config'] = [
            'adapter' => 'callback',
            'callback' => function () use ($key, $existing_adapter_config) {
                $storage_factory = service_loader::get(StorageFactory::class);

                $base_adapter = $storage_factory->createAdapterByConfig(['adapter_config' => $existing_adapter_config]);
                $layer_adapter = $storage_factory->createAdapterByConfig(['adapter_config' => [
                    'adapter' => 'local',
                    'root' => config::system('root_dir') . '/storage_test/overlay_' . $key . '/',
                ]]);

                $adapter = new OverlayAdapter($base_adapter, $layer_adapter);

                return $adapter;
            }
        ];
    }

    $config[$key] = $storage_config;
}

return $config;
