<?php

use wws\business_structure\Shop;
use wws\Country\CountryConst;
use wws\Shop\Catalog\ShopCatalogElasticsearch;
use wws\Shop\Catalog\ShopCatalogGoodieElasticsearch;

$config = [];
$config['shop_id'] = Shop::ALLEGO;
$config['filter_list_id'] = 1;
$config['cat_tree_id'] = 1;
$config['cat_tree_ids_secondary'] = [];
$config['render_variant'] = 'v2';
$config['price_group_ids'] = [1, 51];
$config['product_detail_lang'] = 'de';

$config['foreign_country_filter_list_id'] = 19;

$config['callback_list_id'] = 1;

//$config['trusted_shop_id'] = config::getLegacy('trusted_shops')->shop_id;
$config['trusted_shop_id'] = null;

//trustedshops testweise für September 2020 deaktivieren
$date = time();
if ($date > 1598911200 && $date < 1601503199) {
    $config['trusted_shop_id'] = null;
}

$config['veinteractive'] = false;

$config['cookie_domain'] = '.smartgoods.de';
$config['main_domain'] = 'smartgoods.de';
$config['http'] = 'https://www.' . $config['main_domain'];
$config['https'] = 'https://www.' . $config['main_domain'];


$config['detault_entries_per_page'] = 16;
$config['entries_per_page'] = [15 => 15, 30 => 30, 60 => 60];


$config['host'] = '/';
$config['res_host'] = '/r/';

//Template System
$config['grafik_url'] = '/assets/';
$config['media_url'] = '/';

//aussehen
$config['shop_id'] = 1;
$config['country_id'] = CountryConst::COUNTRY_ID_DE;

//
$config['product_order'] = [
    1 => [
        'name' => 'Beliebtheit',
        'sql' => 'product.product_score DESC',
        'elastic' => ['product_score' => 'DESC']
    ],
    2 => [
        'name' => 'Preis aufsteigend',
        'sql' => 'product_shop.vk_brutto ASC',
        'elastic' => ['vk_brutto' => 'ASC']
    ],
    3 => [
        'name' => 'Preis absteigend',
        'sql' => 'product_shop.vk_brutto DESC',
        'elastic' => ['vk_brutto' => 'DESC']
    ],
    4 => [
        'name' => 'Name',
        'sql' => 'product.product_name ASC',
        'elastic' => 'product_name.raw'
    ],
    5 => [
        'name' => 'Lieferbarkeit',
        'sql' => 'product_shop.lieferbaranzeige ASC',
        'elastic' => ['lieferbaranzeige' => 'ASC']
    ]
];

$config['shipping_method_pickup_available'] = true;
$config['shipping_method_dhl_packing_station_available'] = true;
$config['shipping_method_dhl_post_office_available'] = true;

$config['billpay'] = true;

$config['default_title'] = 'Smartgoods.de - Ihr Fachhandel für Haushalt, Küche und Garten aus Radebeul.';
$config['default_meta_description'] = 'Smartgoods.de ist Ihr kompetenter Online-Fachhandel aus Radebeul. Erleben Sie Produkte namhafter Hersteller aus den Bereichen Haushalt, Garten und vielen mehr.';
$config['default_meta_keywords'] = ['Dresden', 'Radebeul'];

$config['default_lager_id'] = 1;

//achtung: die elasticsearch configs sind in system_dev.conf.php nochmal komplett überschreiben!
$config['catalog'] = [
    'index' => 'allego_products',
    'indexer_override_index' => 'allego_products_7',
    'class' => ShopCatalogElasticsearch::class,
    'hosts' => ['localhost:9200']
];

$config['elasticsearch_devices'] = [
    'index' => 'allego_devices',
    'indexer_override_index' => 'allego_devices_3',
    'hosts' => ['localhost:9200']
];
$config['product_sparepart_fulltexts_enabled'] = true;

$config['elasticsearch_goodies'] = [
    'index' => 'allego_goodies',
    'product_index' => $config['catalog']['index'],
    'class' => ShopCatalogGoodieElasticsearch::class,
    'hosts' => ['localhost:9200']
];

$config['cms_prefetch'] = ['top_menu_faq_list', 'customer_information', 'product_versand', 'cookie_notice', 'product_finanzierung'];

return $config;
