<?php

use bqp\Model\EntityChanges;
use wws\Payment\PaymentRepository;

$config = [];

$config['protokoll'] = [
    'table' => 'protokoll_order',
    'entity_id_field' => 'order_id',
    'field_modus' => EntityChanges::FIELD_MODUS_ID,
];

$config['orders.versand_memo'] = [
    'label' => 'Bemerkung für Logistik',
    'type' => 'memo'
];

$config['orders.auftnr'] = [
    'label' => 'Auftnr.',
    'type' => 'string',
    'as_table' => [
        'order' => true,
        'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=__value__" onclick="popup_large(event)" class="inline">__value__</a>'
    ]
];

$config['orders.order_origin_id'] = [
    'label' => 'Herkunft',
    'type' => 'enum',
    'enums' => function() {
        return order_repository::getOrderOriginNames();
    },
    'as_filter' => [
        'multiple' => true
    ]
];

$config['orders.zahlungs_id'] = [
    'label' => 'Zahlungsart',
    'required' => true,
    'type' => 'enum',
    'enums' => function() {
        return PaymentRepository::getAllZahlungsarten();
    },
    'as_filter' => [
        'multiple' => true
    ]
];


$config['orders.order_amount_gross'] = [
    'label' => 'Betrag',
    'type' => 'currency'
];


return $config;