<?php

$config = [];

$config['profiles'] = [];

$config['profiles']['csv_archiv'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('archiv_dir') . '/psm_csv/'),
    'file_mask' => '*.gz'
];

$config['profiles']['db_backup'] = [
    'max_age' => '4d',
    'dir' => realpath(config::system('temp_dir') . '/db_backup/'),
    'file_mask' => '*.gz'
];

$config['profiles']['raw_mails'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('temp_dir')),
    'file_mask' => '*.raw'
];

$config['profiles']['gautzsch_pleissa'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/pleissa/LAST/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['gautzsch_muenster'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/muenster/LAST/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['franzkerstin_csv'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/kerstin/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['egh_csv'] = [
    'max_age' => '14d',
    'dir' => realpath(config::system('import_dir') . '/egh/EGH/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['manolya_versandmails'] = [
    'max_age' => '90d',
    'dir' => realpath(config::system('import_dir') . '/manolya/versandmails/LAST/'),
    'file_mask' => '*.mime'
];

$config['profiles']['krempl_status'] = [
    'max_age' => '60d',
    'dir' => realpath(config::system('import_dir') . '/krempl_k11/trackingInfo/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['krempl_prices'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/krempl_k11/offers/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['24h'] = [
    'max_age' => '1d',
    'dir' => realpath(config::system('temp_dir') . '/24h/'),
    'file_mask' => '*.*'
];

$config['profiles']['raw_mails'] = [
    'max_age' => '60d',
    'dir' => realpath(config::system('temp_dir') . '/raw_mails/'),
    'file_mask' => '*.raw'
];


$config['profiles']['idealo_debug'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('temp_dir') . '/idealo/'),
    'file_mask' => '*.html'
];

$config['profiles']['ek_servicegroup'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/ek_servicegroup/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['ets'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/ets/LAST/'),
    'file_mask' => '*.csv'
];

$config['profiles']['ets_new'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/ets/new/LAST/'),
    'file_mask' => ['*.dat', '*.csv', '*.gz']
];


$config['profiles']['deg_garbsen_prices'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/deg/garbsen/LAST/'),
    'file_mask' => ['*.dat', '*.csv', '*.gz']
];

$config['profiles']['deg_hof_prices'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/deg/hof/LAST/'),
    'file_mask' => ['*.dat', '*.csv', '*.gz']
];

$config['profiles']['screxs_availability'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/screxs/in/LAST_AVAILABILITY/'),
    'file_mask' => '*.csv'
];

$config['profiles']['frg'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/frg/'),
    'file_mask' => ['*.xls', '*.eml']
];

$config['profiles']['morele'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/morele/LAST/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['smart_wws'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('root_dir') . '/temp/smarty_compile/'),
    'file_mask' => '*.php'
];

$config['profiles']['uni_sendungslisten'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/uni/sendungslisten/LAST/'),
    'file_mask' => '*.csv'
];

$config['profiles']['computeruniverse'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/computeruniverse/LAST/'),
    'file_mask' => ['*.gz']
];


$config['profiles']['gaefgen'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gaefgen/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['sendungslisten'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/versand/sendungslisten/last/'),
    'file_mask' => ['*.csv', '*.txt']
];

return $config;
