<?php

$config = require(__DIR__ . '/../system_default.conf.php');

$config['default_migration_shop_id'] = 2;

$config['basis_host'] = 'ecom-dresden.de';

//leitet je mandant auf andere domain um
$config['use_subdomain_mapping'] = true;

$config['url'] = 'https://wws2.' . $config['basis_host'] . '/';

$config['https_url'] = 'https://wws2.' . $config['basis_host'] . '/';
$config['https_redirect_on_login'] = true;

$config['temp_dir'] = __DIR__ . '/../../temp';
$config['archiv_dir'] = __DIR__ . '/../../archiv';
$config['import_dir'] = __DIR__ . '/../../import';
$config['root_dir'] = __DIR__ . '/../../';
$config['res_dir'] = __DIR__ . '/../../web/res';

$config['fonts_dir'] = $config['root_dir'] . '/web/wws/res/fonts/';

$config['debug'] = false;

$config['debug_mail_redirecting'] = false; //alle system mails werden an diese addresse geschickt

$config['modul_config'] = [
    'gutscheine' => true,

    'notifications' => true,

    'callback' => true,
    'messager' => true,

    'bugreport' => true,
    'service' => true,
    'wackler_controlling' => true,
    'angebote' => false,
    'kasse' => false,
    'chef' => true,
    'product_vergleich' => true,
    'product_psm_info' => true,
    'product_psm_export' => true,
    'market_view' => true,
    'product_automatik' => true,
    'product_spezial_logistik' => false,
    'product_videos' => true,
    'product_features' => true,
    'product_associations' => true,
    'product_media' => true,
    'product_set' => true,
    'product_statistik' => true,
    'product_protokoll_old' => true,
    'product_grundpreis' => true,
    'product_extra_services' => true,
    'product_filter_lists' => true,
    'product_events' => false,

    'product_goodies' => false,

    'product_preisschild_printer' => true,
    'product_online_marketing' => true,

    'amazon' => true,
    'ebay' => true,

    'customer_beruhigung' => true,
    'customer_frist' => true,
    'customer_mails' => true,

    'b2b' => true,

    'invoice' => true,

    'wws\Shop\ShopNews' => false,

    'controlling' => true
];


$config['default_unit_code'] = 'Stk';
$config['default_lager_id'] = 1;

$config['theme'] = 'skin-green';

$config['themes'] = [
    'default' => 'Standard',
    'skin-green' => 'Grün',
    'skin-green-light' => 'Grün - helles Menü',
    'skin-black' => 'Schwarz/Weiß',
//    'skin-blue' => 'Blau',
//    'skin-blue-light' => 'Blau - helles Menü',
    'skin-yellow' => 'Gelb',
    'skin-yellow-light' => 'Gelb - helles Menü',
//    'skin-red' => 'Rot',
//    'skin-red-light' => 'Rot - helles Menü',
];

$config['gateway_secret'] = 'e§$3wsDF"324'; //wird benutzt um die echtheit von parametern in urls zu garantieren

$config['app_salt'] = '4§$fcw&3#.}fe$32'; //darf mit nicht geändert werden wenn kunden passwörter oder user accounts existieren -> passwörter werden ungültig

//fallback procukt id falls einer bestellung ein unbekanntes produkt hinzugefügt wird (nur ebay u. amazon)
$config['unknown_product_id'] = '143832';

$config['smtp'] = 'smtp://497128ae6c175eb0d58ea3207eca4c0e:<EMAIL>:587';

$config['contact_form_secret'] = 'g4D42-234s3SDShbc';

$config['alert_mailer_default_sender_mail'] = '<EMAIL>';
$config['alert_mailer_default_recipient_mail'] = '<EMAIL>';


$config['storage_config'] = 'k11/storage_live';

$config['di_definitions'] = [
    __DIR__ . '/../di/modules.php',
    __DIR__ . '/../di/modules_k11.php',
    __DIR__ . '/../di/market_view.php',
    __DIR__ . '/../di/shopware.php'
];

$config['cfg_override']['db_default'] = 'k11/db_default';
$config['cfg_override']['db_shop'] = 'k11/db_shop';
$config['cfg_override']['db_stats'] = 'k11/db_stats';
$config['cfg_override']['db_protokoll'] = 'k11/db_protokoll';
$config['cfg_override']['db_market_view'] = 'k11/db_market_view';
$config['cfg_override']['db_instant_ddl_check'] = 'k11/db_instant_ddl_check';

$config['cfg_override']['event_handlers'] = 'k11/event_handlers';
$config['cfg_override']['event_subscriptions'] = 'k11/event_subscriptions';
$config['cfg_override']['file_purger'] = 'k11/file_purger';
$config['cfg_override']['protokoll'] = 'k11/protokoll';
$config['cfg_override']['product_list'] = 'k11/product_list';
$config['cfg_override']['product_defaults'] = 'k11/product_defaults';
$config['cfg_override']['gs_lieferzeiten'] = 'k11/gs_lieferzeiten';

return $config;
