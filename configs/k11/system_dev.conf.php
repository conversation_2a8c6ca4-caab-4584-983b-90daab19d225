<?php

$config = include(__DIR__ . '/system.conf.php');


$config['cfg_override']['db_default'] = 'k11/db_dev';
$config['cfg_override']['db_shop'] = 'k11/db_dev';
$config['cfg_override']['db_protokoll'] = 'k11/db_dev';
$config['cfg_override']['db_market_view'] = 'k11/db_dev';

$config['cfg_override']['k11/shopware_api'] = 'k11/shopware_api_stage';
//$config['cfg_override']['k11/shopware_api'] = 'k11/shopware_api_dev';

$config['https_redirect_on_login'] = false;
$config['debug'] = true;
$config['use_subdomain_mapping'] = false;

$config['extern_product_pictures'] = 'https://wws2.ecom-dresden.de/getimage.php';

$config['theme'] = 'skin-purple';

$config['wkhtmltopdf_bin'] =  'wkhtmltopdf';

$config['basis_host'] = 'local';

$config['url'] = 'http://wws.k11.ddev.site/';

$host = $_SERVER['HTTP_HOST'] ?? '';

$config['override']['shop_1']['cookie_domain'] = str_replace(':8080', '', $host);
$config['override']['shop_1']['main_domain'] = $host;
$config['override']['shop_1']['http'] = 'http://' . $config['override']['shop_1']['main_domain'];
$config['override']['shop_1']['https'] = 'http://' . $config['override']['shop_1']['main_domain'];

$config['override']['shop_2']['cookie_domain'] = $host;
$config['override']['shop_2']['main_domain'] = $host;
$config['override']['shop_2']['http'] = 'http://' . $config['override']['shop_2']['main_domain'];
$config['override']['shop_2']['https'] = 'http://' . $config['override']['shop_2']['main_domain'];

$config['override']['storage']['media'] = [
    'prefix' => 'media',
    'adapter_config' => [
        'adapter' => 'local',
        'root' => $config['res_dir']
    ]
];


$config['override']['shop_2']['grafik_url'] = '/assets/';
$config['override']['shop_2']['media_url'] = '/';

$config['override']['k11/payment/paypal']['API_USERNAME'] = 'sb-jqlpj528292_api1.business.example.com';
$config['override']['k11/payment/paypal']['API_PASSWORD'] = 'M9JNTS9NUYQS2ZU2';
$config['override']['k11/payment/paypal']['API_SIGNATURE'] = 'AV5iMOcuPSFV0POH41qWM7uNuAY9A.e6h0-DeKdpNSns2Xstuw5LiM2Z';
$config['override']['k11/payment/paypal']['API_ENDPOINT'] = 'https://api-3t.sandbox.paypal.com/nvp';
$config['override']['k11/payment/paypal']['PAYPAL_URL_EC'] = 'https://www.sandbox.paypal.com/webscr&cmd=_express-checkout&token=';
$config['override']['k11/payment/paypal']['PAYPAL_URL_ECC'] = 'https://www.sandbox.paypal.com/webscr&cmd=_complete-express-checkout&token=';

$config['override']['k11/payment/paypal']['shop_config'] = [
    'host' => 'http://ersatz.local/',
    'returnurl' => 'http://ersatz.local/kasse/?do=paypal',
    'cancelurl' => 'http://ersatz.local/kasse/step3/',
    'returnurl_express' => 'http://ersatz.local/kasse/?do=paypal_direct',
    'cancelurl_express' => 'http://ersatz.local/warenkorb/',
    'giropaysuccessurl' => 'http://ersatz.local/kasse/success/',
    'giropaycancelurl' => 'http://ersatz.local/kasse/success/',
    'banktxnpendingurl' => 'http://ersatz.local/kasse/success/',
    'notifyurl' => 'http://ersatz.local/api/paypal_ipn.php',
    'hdrimg' => 'https://www.ersatzteilshop.de/assets/logo.png'
];

//paypal sandbox
$config['override']['paypal']['API_USERNAME'] = 'info-facilitator_api1.allego.de';
$config['override']['paypal']['API_PASSWORD'] = 'ZJSGX4QWFDJ3ZAH9';
$config['override']['paypal']['API_SIGNATURE'] = 'AB-LuFP0jr4h9MFa0gf4JkY7K0URAPqrPuc1hD7JJlsfCAF0ouF9B691';
$config['override']['paypal']['API_ENDPOINT'] = 'https://api-3t.sandbox.paypal.com/nvp';
$config['override']['paypal']['PAYPAL_URL_EC'] = 'https://www.sandbox.paypal.com/webscr&cmd=_express-checkout&token=';
$config['override']['paypal']['PAYPAL_URL_ECC'] = 'https://www.sandbox.paypal.com/webscr&cmd=_complete-express-checkout&token=';



$config['override']['paypal']['shop_config'] = [
    'host' => 'http://allego.localhost/',
    'returnurl' => 'http://allego.local/kasse/?do=paypal',
    'cancelurl' => 'http://allego.local/kasse/step3/',
    'returnurl_express' => 'http://allego.local/kasse/?do=paypal_direct',
    'cancelurl_express' => 'http://allego.local/warenkorb/',
    'giropaysuccessurl' => 'http://allego.local/kasse/success/',
    'giropaycancelurl' => 'http://allego.local/kasse/success/',
    'banktxnpendingurl' => 'http://allego.local/kasse/success/',
    'notifyurl' => 'http://allego.local/api/paypal_ipn.php',
    'hdrimg' => 'http://www.allego.de/design/images/logo.png'
];

$config['smtp'] = 'smtp://localhost:1025';

$config['storage_config'] = 'storage_test';

return $config;
