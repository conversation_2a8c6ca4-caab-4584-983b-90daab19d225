<?php

$config = [];

$config['profiles'] = [];

$config['profiles']['csv_archiv'] = [
    'max_age' => '1d',
    'dir' => realpath(config::system('archiv_dir') . '/psm_csv/'),
    'file_mask' => '*.gz'
];

$config['profiles']['raw_mails'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('temp_dir')),
    'file_mask' => '*.raw'
];

$config['profiles']['gautzsch_pleissa'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/pleissa/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['gautzsch_pleissa_dropshipping'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/pleissa_dropshipping/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['gautzsch_pleissa_bau_dropshipping'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/pleissa_bau_dropshipping/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['gautzsch_muenster'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/muenster/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['gautzsch_responses'] = [
    'max_age' => '365d',
    'dir' => realpath(config::system('import_dir') . '/gautzsch/responses/executed/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['krempl_delivery_notes'] = [
    'max_age' => '7d',
    'dir' => realpath(config::get('ecom/supplier/krempl', 'path_orders_out') . '/delivery_notes/'),
    'file_mask' => '*.pdf'
];

$config['profiles']['egh_csv'] = [
    'max_age' => '14d',
    'dir' => realpath(config::system('import_dir') . '/egh/EGH/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['egh_fulfillment_csv'] = [
    'max_age' => '14d',
    'dir' => realpath(config::system('import_dir') . '/egh/fulfillment/offers/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['manolya_versandmails'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/manolya/versandmails/last/'),
    'file_mask' => '*.eml'
];

$config['profiles']['krempl_prices'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/krempl/prices/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['krempl_status'] = [
    'max_age' => '60d',
    'dir' => realpath(config::system('import_dir') . '/krempl/trackingInfo/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['24h'] = [
    'max_age' => '1d',
    'dir' => realpath(config::system('temp_dir') . '/24h/'),
    'file_mask' => '*.*'
];

$config['profiles']['raw_mails'] = [
    'max_age' => '60d',
    'dir' => realpath(config::system('temp_dir') . '/raw_mails/'),
    'file_mask' => '*.raw'
];


$config['profiles']['idealo_debug'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('temp_dir') . '/idealo/'),
    'file_mask' => '*.html'
];

$config['profiles']['ek_servicegroup'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/ek_servicegroup/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['ek_servicegroup_edi'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/ek_servicegroup/edi/last/'),
    'file_mask' => '*.edi'
];

$config['profiles']['screxs_availability'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/screxs/in/LAST_AVAILABILITY/'),
    'file_mask' => '*.csv'
];

$config['profiles']['frg'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/frg/availability/last/'),
    'file_mask' => ['*.xls', '*.eml']
];

$config['profiles']['morele'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/morele/LAST/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['smart_wws'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('root_dir') . '/temp/smarty_compile/'),
    'file_mask' => '*.php'
];

$config['profiles']['uni_sendungslisten'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/uni/sendungslisten/LAST/'),
    'file_mask' => '*.csv'
];

$config['profiles']['uni_fulfillment'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/uni/fulfillment/LAST/'),
    'file_mask' => '*.gz'
];

$config['profiles']['computeruniverse'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/computeruniverse/LAST/'),
    'file_mask' => ['*.gz']
];


$config['profiles']['gaefgen'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/gaefgen/last/'),
    'file_mask' => ['*.csv', '*.gz']
];

$config['profiles']['gaefgen_shipping'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/gaefgen/sendungslisten/last/'),
    'file_mask' => ['*.eml']
];

$config['profiles']['mv_gorenje'] = [
    'max_age' => '120d',
    'dir' => realpath(config::system('import_dir') . '/gorenje/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['mv_maoloya'] = [
    'max_age' => '120d',
    'dir' => realpath(config::system('import_dir') . '/manolya/produkte/last/'),
    'file_mask' => '*.gz'
];


$config['profiles']['sonepar/nord_ost/bestand'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/sonepar/nord_ost/bestand/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['sonepar/sued/bestand'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/sonepar/sued/bestand/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['sonepar/sued_langweid/bestand'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/sonepar/sued_langweid/bestand/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['sonepar/nord_ost/preis'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/sonepar/nord_ost/preis/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['sonepar/sued/preis'] = [
    'max_age' => '7d',
    'dir' => realpath(config::system('import_dir') . '/sonepar/sued/preis/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['easytronics'] = [
    'max_age' => '14d',
    'dir' => realpath(config::system('import_dir') . '/easytronics/products/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['beko'] = [
    'max_age' => '90d',
    'dir' => realpath(config::system('import_dir') . '/beko/availability/last/'),
    'file_mask' => '*.eml'
];

$config['profiles']['wmf_inventory'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/wmf/invetory/last/'),
    'file_mask' => ['*.gz', '*.json']
];

$config['profiles']['check24_prices'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/check24/prices/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['broemmelhaupt'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/broemmelhaupt/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['zajadacz_mails'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/zajadacz/mails/last/'),
    'file_mask' => '*.eml'
];

$config['profiles']['zajadacz_pricelists'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/zajadacz/pricelists/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['sendungslisten'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/versand/sendungslisten/last/'),
    'file_mask' => ['*.csv', '*.txt']
];

$config['profiles']['maxcom_shipping_mails'] = [
    'max_age' => '180d',
    'dir' => realpath(config::system('import_dir') . '/maxcom/versandmails/last/'),
    'file_mask' => '*.eml'
];

$config['profiles']['starmoney_bank_statements'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/konto/last/'),
    'file_mask' => '*.sta'
];

$config['profiles']['eberhard_market_view_imports'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/eberhard/pricelist/last/'),
    'file_mask' => '*.zip'
];

$config['profiles']['rexel_pricelists'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/rexel/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['rexel_ugl_messages'] = [
    'max_age' => '365d',
    'dir' => realpath(config::system('import_dir') . '/rexel/ugl/last/'),
    'file_mask' => '*.ugl'
];

$config['profiles']['dgh_csv'] = [
    'max_age' => '14d',
    'dir' => realpath(config::system('import_dir') . '/dgh/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['hellmann_edi_messaged'] = [
    'max_age' => '365d',
    'dir' => realpath(config::system('import_dir') . '/hellmann/last/'),
    'file_mask' => '*.edi'
];

$config['profiles']['buerkle_product_data'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/buerkle/product_lists/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['buerkle_product_inventory'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/buerkle/inventory_lists/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['dealavo_csv'] = [
    'max_age' => '30d',
    'dir' => realpath(config::system('import_dir') . '/dealavo/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['ek_servicegroup_content'] = [
    'max_age' => '14d',
    'dir' => realpath(config::system('import_dir') . '/ek_servicegroup/content/last/'),
    'file_mask' => '*.gz'
];

$config['profiles']['eprel'] = [
    'max_age' => '90d',
    'dir' => realpath(config::system('import_dir') . '/eprel/last/'),
    'file_mask' => '*.zip'
];

return $config;
