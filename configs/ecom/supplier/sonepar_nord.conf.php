<?php

use wws\Supplier\SuppliersConst;

$config = [];

$config['ftp_host'] = 'ftp.sonepar.de';
$config['ftp_user'] = 'ftp02892612';
$config['ftp_password'] = 'V25tr2A5';

$config['edifact_party'] = 'SONEPAR';
$config['edifact_customer_nr'] = '028926';
$config['edifact_out_path'] = 'in/';

$config['supplier_id'] = 177;

$config['email'] = '<EMAIL>';

$config['easy_mandant'] = '12';
$config['easy_kdnr'] = '649';
$config['easy_passwort'] = 'abgihe';
$config['easy_tracking_url'] = 'https://www.eas-y.de/ECOMDRESDENOHG/temp/PAKETE_12_649_2010a_%s.csv';

//$config['easy_content_url'] = 'https://www.eas-y.de/Download/fileName=public/12_649_XZXF.csv&gesNr=12&kdNr=649&loginName=eCom-Dresden&loginPwd=080713';
//das mit der url läuft nicht mehr sauber... die haben immer wieder probleme mit ihren servern. die stellen uns das jetzt zusätzlich per ftp bereit
$config['easy_content_source'] = 'ftp://' . $config['ftp_user'] . ':' . $config['ftp_password'] . '@' . $config['ftp_host'] . '/out/Produktdaten/';

$config['supplier_ids_status_import'] = [
    SuppliersConst::SUPPLIER_ID_DEG_GARBSEN,
    SuppliersConst::SUPPLIER_ID_SONEPAR_NORD,
    SuppliersConst::SUPPLIER_ID_SONEPAR_SUED,
    SuppliersConst::SUPPLIER_ID_SONEPAR_SUED_LANGWEID
];

$config['webservice_mandant'] = 12;
$config['webservice_kundennr'] = 649;
$config['webservice_passwort'] = 'nymj3x1,5';

$config['opentrans_exchange_source'] = 'ftp://' . $config['ftp_user'] . ':' . $config['ftp_password'] . '@' . $config['ftp_host'] . '/';
$config['opentrans_message_directory'] = config::system('import_dir') . '/sonepar/nord_ost/opentrans/';

return $config;
