<?php

/** @noinspection PhpFullyQualifiedNameUsageInspection */

$events = [];

$events[\wws\Mails\Event\EventMailOrderProcessed::class] = [
    'listener_async' => [
        'OrderTagUnreadMail',
    ]
];

$events[\wws\Mails\Event\EventMailOrderUnprocessed::class] = [
    'listener_async' => [
        'OrderTagUnreadMail'
    ]
];

$events[\wws\Order\Event\EventOrderCreated::class] = [
    'listener' => [
        \wws\core\Notification\EventHandler\NotifyNewOrderCreated::class
    ],
    'listener_async' => [
        \wws\ProductAvailabilityAutomatic\SaleOut\EventHandler\RecalcSaleOutAvailabilityOnNewOrder::class,
        \wws\Order\EventHandler\SendBWareMail::class
    ]
];

$events[\wws\Order\Event\EventOrderNewShop::class] = [
    'listener' => [
    ],
    'listener_async' => [
        ['event_subscriber' => 'OrderAutomaticNewShop'],
        ['event_subscriber' => \bqp\extern\Amazon\EventHandler\AmazonSpeditionMailSendHandler::class, 'filter' => [\bqp\extern\Amazon\EventHandler\AmazonSpeditionMailSendHandler::class, 'isEventReferencingAmazonOrder']],
        ['event_subscriber' => \bqp\extern\Ebay\EventHandler\EventStockCacheUpdate::class, 'filter' => [\bqp\extern\Ebay\EventHandler\EventStockCacheUpdate::class, 'isEventReferencingEbayOrder']],
        ['event_subscriber' => \bqp\extern\Galaxus\EventHandler\GalaxusConfirmationServiceEventHandler::class, 'filter' => [\bqp\extern\Galaxus\EventHandler\GalaxusConfirmationServiceEventHandler::class, 'isEventRelevant']],
    ]
];

$events[\wws\Order\Event\EventOrderChange::class] = [
    'listener' => [
        [\wws\CustomerSearch\EventHandler\UpdateCustomerFulltext::class, 'handleOrderChange'],
        \wws\Order\EventHandler\AllGrosBestaetigtAuftGo::class
    ]
];


$events[\wws\Order\Event\EventOrderBeforeSave::class] = [
    'listener' => [
        \wws\Order\EventHandler\RecalcUstStatus::class,
        \wws\Order\EventHandler\SelfPickupOrderCanceledWarehouseNotification::class
    ]
];

$events[\wws\Order\Event\EventOrderInvoiceCreated::class] = [
    'listener' => [
        \bqp\extern\SpbGarantGarantie\EventHandler\SendCustomerInvoiceToSpbGarant::class,
        \wws\CustomerSearch\EventHandler\UpdateCustomerFulltext::class
    ],
    'listener_async' => [
        ['event_subscriber' => \wws\Order\OrderCompletionService::class],
        ['event_subscriber' => 'KauflandUploadInvoice', 'filter' => [\bqp\extern\Kaufland\KauflandEventHandler::class, 'isEventReferencingKauflandOrder']],
        ['event_subscriber' => 'CommerzFinanzSendInvoice', 'filter' => [\bqp\extern\CommerzFinanz\CommerzFinanzEventHandler::class, 'isEventReferencingCommerzFinanzOrder']],
        ['event_subscriber' => 'AmazonUploadInvoice', 'filter' => [\bqp\extern\Amazon\EventHandler\AmazonInvoiceUploadHandler::class, 'isEventReferencingAmazonOrder']]
    ]
];

$events[\wws\Order\Event\EventOrderGutschriftCreated::class] = [
    'listener' => [
        \wws\Customer\EventHandler\NewGutschriftToCustomerMemo::class,
        \wws\Order\EventHandler\MarkOrderPayedOnGutschriftCreate::class
    ]
];

$events[\wws\OrderRefund\Event\EventOrderRefundExecuted::class] = [
    'listener' => [
        \wws\Customer\EventHandler\OrderRefundsToCustomerMemo::class
    ],
    'listener_async' => [
        'AmazonRefundStockUpdater'
    ]
];

$events[\wws\OrderRefund\Event\EventOrderRefundCanceled::class] = [
    'listener' => [
        \wws\Customer\EventHandler\OrderRefundsToCustomerMemo::class
    ]
];

$events[\wws\OrderRefund\Event\EventOrderRefundCreated::class] = [
    'listener' => [
        \wws\Customer\EventHandler\OrderRefundsToCustomerMemo::class
    ]
];

$events[\wws\Order\Event\EventOrderCompleteForPlatform::class] = [
    'listener_async' => [
        ['event_subscriber' => \bqp\extern\idealo\Direktkauf\WwsIdealoDirektkaufEventHandler::class, 'filter' => [\bqp\extern\idealo\Direktkauf\WwsIdealoDirektkaufEventHandler::class, 'isEventReferencingIdealoOrder']],
        ['event_subscriber' => \bqp\extern\Ebay\EventHandler\EbayEndOrderEventHandler::class, 'filter' => [\bqp\extern\Ebay\EventHandler\EbayEndOrderEventHandler::class, 'isEventRelevant']],
        ['event_subscriber' => \bqp\extern\Amazon\EventHandler\AmazonEndOrderEventHandler::class, 'filter' => [\bqp\extern\Amazon\EventHandler\AmazonEndOrderEventHandler::class, 'isEventRelevant']],
        ['event_subscriber' => \bqp\extern\Kaufland\KauflandEventHandler::class, 'filter' => [\bqp\extern\Kaufland\KauflandEventHandler::class, 'isEventReferencingKauflandOrder']],
        ['event_subscriber' => \bqp\extern\Galaxus\EventHandler\GalaxusEndOrderEventHandler::class, 'filter' => [\bqp\extern\Galaxus\EventHandler\GalaxusEndOrderEventHandler::class, 'isEventRelevant']]
    ]
];


$events[\wws\Product\Event\EventProductStockFreeOut::class] = [
    'listener_async' => [
        'ShopExpressableUpdater'
    ]
];

$events[\wws\Product\Event\EventProductStockFreeIn::class] = [
    'listener_async' => [
        'ShopExpressableUpdater'
    ]
];

$events[\wws\Product\Event\EventProductStockFreeChanged::class] = [
    'listener' => [
        \wws\ProductFilterList\EventHandler\AddProductToFilterlistQueue::class
    ]
];

$events[\wws\Product\Event\EventProductFilterListResultChanged::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class,
        \wws\Product\EventHandler\AmazonFilterlistInvalidateQueueDeletion::class
    ]
];

$events[\wws\ProductDevice\Event\EventProductDeviceDeviceMappingChange::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class
    ]
];

$events[\wws\ProductDevice\Event\EventProductDeviceChange::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class
    ]
];

$events[\wws\ProductDevice\Event\EventProductDeviceMappingChangeBatched::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class
    ]
];

$events[\wws\Product\Event\EventProductCatPriceTemplateChange::class] = [
    'listener_async' => [
        'ProductCatPriceTempleSync'
    ]
];

$events[\wws\Product\Event\EventProductCatChange::class] = [
    'listener' => [
        \wws\Product\Ebay\EventHandler\ProductCatChangeEbayHandler::class,
        [\wws\ProductFilterList\EventHandler\AddProductToFilterlistQueue::class, 'handleCatChange'],
    ]
];

$events[\wws\Product\Event\EventProductChange::class] = [
    'listener' => [
        \wws\Product\EventHandler\RegisterProductShopUrl::class,
        \wws\Product\Ebay\EventHandler\ProductChangeEbayHandler::class,
        \wws\Product\EventHandler\WriteProductEkHistory::class,
        [\wws\ProductFilterList\EventHandler\AddProductToFilterlistQueue::class, 'handleProductChange'],
        \wws\MarketViewWwsConnector\EventHandler\RemoveProductMatchingOnEanChange::class,
        \wws\Supplier\EventHandler\ProductGrosProductIdSync::class,
        \wws\Product\EventHandler\ProductCalcEnvkvMediaIds::class,
        [\wws\Product\EventHandler\ProductSetUpdater::class, 'handleProductChange'],
        \wws\Product\EventHandler\RefreshProductTags::class,
        \wws\Product\EventHandler\ProductUpdateMediaDataSourceUsage::class,
        [\wws\Product\EventHandler\AmazonPriceControlProductEventHandler::class, 'handleChangeEvent'],
        \wws\Product\EventHandler\MarketViewProductLocalCacheEventHandler::class,
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class,
        \wws\MarketViewWwsConnector\EventHandler\SyncMarketViewOffersToProduct::class,
        \wws\Product\ProductSearchQuick\EventHandler\ProductSearchQuickUpdater::class,
        \bqp\extern\Amazon\EventHandler\AmazonQueueOfferSync::class,
    ],
    'listener_async' => [
        //['event_subscriber' => \wws\Product\Envkv\FicheGuaranteeRedaction\EventHandler\EnvkvGuaranteeFicheRedaction::class, 'filter' => [\wws\Product\Envkv\FicheGuaranteeRedaction\EventHandler\EnvkvGuaranteeFicheRedaction::class, 'isEventRelevant']],
        ['event_subscriber' => \wws\Product\Envkv\EnvkvOcr\EventHandler\EnvkvOcrScan::class, 'filter' => [\wws\Product\Envkv\EnvkvOcr\EventHandler\EnvkvOcrScan::class, 'isEventRelevant']],
        ['event_subscriber' => \wws\Product\Envkv\Eprel\EventHandler\EprelIdQrScan::class, 'filter' => [\wws\Product\Envkv\Eprel\EventHandler\EprelIdQrScan::class, 'isEventRelevant']],
    ]
];

$events[\wws\Product\Event\EventProductExtraServiceChange::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class
    ]
];

$events[\wws\Product\Event\EventProductPreSave::class] = [
    'listener' => [
        \wws\Product\VirtMvsrcAvailability\EventHandler\SyncVirtMvsrcAvailability::class,
        \bqp\extern\Unielektro\EventHandler\CalcUnielektroProductEkRelationRule::class,
        \bqp\extern\Unielektro\EventHandler\ProductUniAbakusCondition::class,
        \bqp\extern\Egh\EventHandler\CalcEghProductEkRelationRule::class,
        \bqp\extern\Egh\EghProductEkValidityRule::class,
        \wws\Product\EventHandler\ProductEkAutomatic::class,
        \wws\Product\EventHandler\ProductAvailabilityAutomatic::class,
        [\wws\Product\EventHandler\AmazonPriceControlProductEventHandler::class, 'handlePreSaveEvent'],
        \wws\Product\actions\ProductDeliverySlipHintGenerator::class,
        \wws\Product\EventHandler\ProductEkLimitMinCalculation::class,
        \wws\Product\EventHandler\ProductEkOfferWithPricePerQuantity::class,
        \wws\Product\Ecom\ProductSoneparDropshippingManipulation::class,
        \bqp\extern\Fega\ProductFegaDropshippingManipulation::class,
        \wws\Product\Ecom\ProductLowSpecialPricing::class,
        \bqp\extern\Eberhard\ProductEberhardDropshippingManipulation::class,
        \bqp\extern\Gaefgen\ProductGaefgenDropshippingManipulation::class,
        \wws\Product\ProductDescriptionViolation\ProductDescriptionViolation::class,
        \bqp\extern\Mittelstandskreis\EventHandler\UpdateProductMittelstandskreis::class,
        \wws\Product\EventHandler\ProductFeatureQualityStatusUpdater::class,
        \wws\Product\EventHandler\ProductStatusTags::class,
        \wws\Product\EventHandler\ProductCalcEnvkvState::class,
        \wws\Product\ProductNotAvailable\EventHandler\ClearProductNotAvailableTags::class,
        \wws\Product\EventHandler\RecalcProductNameEnvkvSuffix::class,
        \bqp\extern\Ebay\EventHandler\EbayImageQualificationHandler::class,
        [\wws\Product\EventHandler\ProductSetUpdater::class, 'handleEventPreSave'],
        \bqp\extern\Check24\EventHandler\ProductUpdateCheck24Fees::class,
        \wws\Product\ProductCampaign\EventHandler\ProductCampaignXetAutomatic::class,
        \wws\Product\ProductCampaign\EventHandler\ProductCampaignDstAutomatic::class,
        \wws\Product\EventHandler\ProductPriceAutomaticUpdater::class,
        \wws\Product\EventHandler\ProductInternalRecalcPrices::class, //essentiell funktionalität: Erst nach dem Punkt können die Preise als "valid" angesehen werden (Preise auf Basis von Formeln)
        \bqp\extern\Kaufland\EventHandler\KauflandRecalcProductExtraChargeEventHandler::class,
        \bqp\extern\Check24\EventHandler\ProductUpdateCheck24ActPrice::class,
        \wws\Product\Gpsr\EventHandler\ProductGpsrTagUpdater::class,
        \wws\Product\EventHandler\SetConditionTags::class,
        \wws\Product\EventHandler\NormalizeMpn::class,
    ]
];

$events[\wws\Product\Event\EventProductBrandCreated::class] = [
    'listener' => [
        \wws\Product\EventHandler\ProductDataSourceInitNewHersteller::class
    ]
];

$events[\wws\Product\Event\EventProductBrandChange::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class,
        [\wws\Product\Gpsr\EventHandler\ProductGpsrTagUpdater::class, 'handleProductBrandEvent'],
    ]
];

$events[\wws\Product\ProductFeature\Event\EventProductFeatureGroupChange::class] = [
    'listener' => [
        \wws\Product\ProductFeature\EventHandler\ReformatProductFeatures::class,
        \wws\Product\ProductFeature\EventHandler\RecalcProductFeatureQuality::class,
    ]
];

$events[\wws\ProductRating\Event\EventProductRatingChanged::class] = [
    'listener' => [
        \wws\ProductRating\EventHandler\EventHandlerProductRatingChanged::class,
    ]
];

$events[\wws\Product\Ebay\Event\EventProductEbayCatChange::class] = [
    'listener_async' => [
        \wws\Product\Ebay\EventHandler\ProductEbaySync::class
    ]
];

$events[\bqp\extern\Ebay\Event\EbayCatChangeByEbay::class] = [
    'listener_async' => [
        \bqp\extern\Ebay\EventHandler\EbayCatChangeByEbayUpdateProductEventHandler::class
    ]
];

$events[\wws\Supplier\Event\EventSupplierChange::class] = [
    'listener' => [
        \bqp\extern\Amazon\EventHandler\AmazonInvalidOfferStateCache::class
    ]
];

$events[\wws\Supplier\Event\EventGrossistConditionsChanged::class] = [
    'listener_async' => [
        \wws\Product\EventHandler\UpdateGrossistConditionsInProduct::class
    ]
];

$events[\wws\Supplier\Event\EventSupplierOrderBestellt::class] = [
    'listener' => [
        \wws\Order\EventHandler\SupplierOrderBestelltStatusSync::class,
        \wws\Supplier\EventHandler\SupplierOrderSetAvailabilites::class,
        \wws\Supplier\EventHandler\AddSupplierOrderPendingTag::class
    ]
];

$events[\wws\Supplier\Event\EventSupplierOrderStorniert::class] = [
];

$events[\wws\Supplier\Event\EventSupplierOrderStatusChange::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\SupplierOrderStatusChangeToGrossistenMessage::class
    ]
];

$events[\wws\Callback\Event\EventCallbackCreated::class] = [
    'listener' => [
        \wws\Callback\EventHandler\SendMailOnNewCallback::class
    ]
];

$events[\wws\Customer\Event\EventCustomerChanged::class] = [
    'listener' => [
        \wws\CustomerSearch\EventHandler\UpdateCustomerFulltext::class,
        [\wws\Order\EventHandler\RecalcUstStatus::class, 'handleEventCustomerChanged']
    ]
];

$events[\wws\Customer\Event\EventCustomerCreated::class] = [
    'listener' => [
        \wws\CustomerSearch\EventHandler\UpdateCustomerFulltext::class
    ]
];

$events[\wws\Customer\Event\EventCustomerAddressChanged::class] = [
    'listener' => [
        \wws\Order\EventHandler\SyncCustomerAddressChangesInActiveOrders::class
    ]
];

$events[\wws\BankAccount\Event\EventTransactionBooked::class] = [
    'listener' => [
        \wws\Customer\EventHandler\SyncBankAccountFromTransactions::class
    ]
];

$events[\wws\BankAccount\Event\EventTransactionChanged::class] = [
    'listener' => [
        \wws\BankAccount\EventHandler\RecalcSettlementTransactionStateSummary::class
    ]
];

$events[\wws\preisvergleich\Event\EventPreisvergleichProductResultChanged::class] = [
    'listener' => [
        \wws\Product\EventHandler\RecalcPricesByPreisvergleichResult::class
    ]
];

$events[\wws\ProductRepairRating\Event\EventProductRepairRatingPicturesStored::class] = [
    'listener' => [
        \wws\ProductRepairRating\EventHandler\ResizeNewProductRepairRatingPicturesEventHandler::class,
    ],
];

$events[\wws\ProductUserPicture\Event\EventProductUserPicturesStored::class] = [
    'listener' => [
        \wws\ProductUserPicture\EventHandler\ResizeNewProductUserPicturesEventHandler::class,
    ],
];

$events[\wws\Lager\Event\EventWareneingangLieferscheinBooked::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\WareneingangUpdateSupplierOrder::class
    ],
];

$events[\wws\Lager\Event\EventWareneingangAnnounceCreaded::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\WareneingangAnnounceToGrossistenMessage::class
    ],
];

$events[\wws\Shipment\Event\EventManualShipmentCreated::class] = [
    'listener_async' => [
        \wws\Order\EventHandler\SendShipmentConsignmentMail::class
    ],
];

$events[\wws\Product\ProductDataSourceUsage\Event\EventProductBrandDataSourceUsageChanged::class] = [
    'listener_async' => [
        \wws\Product\ProductDataSourceUsage\EventHandler\RecalcProductPictureDataUsage::class
    ],
];

$events[\wws\Lager\Event\EventWarenausgangLieferscheinCreated::class] = [
    'listener' => [
        \wws\Lager\EventHandler\CreateWarenausgangLieferscheinPdf::class
    ]
];

$events[\wws\Lager\Event\EventWarenausgangLieferscheinErledigt::class] = [
    'listener' => [
        \wws\Order\EventHandler\CreateRechnungAndSendOnDeliveryComplete::class,
        \wws\Lager\EventHandler\SendSpedChecklist::class
    ]
];

$events[\bqp\extern\Ebay\Event\EbayAccountDeletionMessage::class] = [
    'listener_async' => [
        \bqp\extern\Ebay\EventHandler\EbayAccountDeletionEventHandler::class
    ]
];

$events[\wws\Tracking\Event\NewTrackingAdded::class] = [
    'listener' => [
        [\wws\CustomerSearch\EventHandler\UpdateCustomerFulltext::class, 'handleTrackingAdded'],
    ],
    'listener_async' => [
        \wws\Supplier\EventHandler\AddTrackingDateToSupplierOrder::class,
        \bqp\extern\KoesterHapke\EventHandler\ExtendKoesterHapkeTrackingData::class
    ]
];

$events[\wws\Supplier\Event\EventSaveSupplierOrderMessage::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\SendMustReadNotifications::class,
    ]
];

$events[\wws\Supplier\Event\EventSupplierOrderPreSave::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\AddProductTypesToSupplierOrderTags::class,
        \wws\Supplier\EventHandler\SupplierOrderUpdateStockClearance::class,
    ]
];

$events[\wws\Supplier\Event\EventSupplierOrderConfirmationError::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\SendSupplierOrderConfirmationErrorNotifications::class,
    ]
];

$events[\wws\Supplier\Event\EventSupplierOrderChange::class] = [
    'listener' => [
        \wws\Supplier\EventHandler\CalculateSupplierOrderDeliveryTimeStatistic::class,
    ]
];

$events[\wws\Supplier\Event\EventSupplierOrderTransferSuccess::class] = [
    'listener' => [
        \bqp\extern\Aswo\EventHandler\AddAswoOrderNumberToOrderMemo::class,
        \wws\Supplier\EventHandler\RemoveSupplierOrderPendingTag::class
    ],
];

$events[\bqp\extern\Paypal\Event\EventPaypalWebhook::class] = [
    'listener' => [
        \bqp\extern\Paypal\EventHandler\ProcessPaypalEvents::class,
    ]
//    'listener_async' => [
//        ['event_subscriber' => \bqp\extern\Paypal\EventHandler\ProcessPaypalEvents::class, 'filter' => [\bqp\extern\Paypal\EventHandler\ProcessPaypalEvents::class, 'isEventRelevant']],
//    ]
];

$events[\wws\Product\Event\EventProductGoodieChange::class] = [
    'listener' => [
        \wws\ShopBackend\Elasticsearch\EventHandler\QueueElasticsearchIndexer::class
    ]
];

return $events;
