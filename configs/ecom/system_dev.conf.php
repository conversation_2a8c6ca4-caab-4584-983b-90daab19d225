<?php

use wws\Shop\Catalog\ShopCatalogElasticsearch;
use wws\Shop\Catalog\ShopCatalogGoodieElasticsearch;

$config = include(__DIR__ . '/system.conf.php');


$config['cfg_override']['db_default'] = 'ecom/db_dev';
$config['cfg_override']['db_shop'] = 'ecom/db_dev';
$config['cfg_override']['db_protokoll'] = 'ecom/db_dev';
$config['cfg_override']['db_market_view'] = 'ecom/db_dev';

$config['https_redirect_on_login'] = false;
$config['debug'] = true;
$config['use_subdomain_mapping'] = false;

$config['extern_product_pictures'] = 'https://wws.ecom-dresden.de/getimage.php';

$config['theme'] = 'skin-purple';

$config['wkhtmltopdf_bin'] = 'wkhtmltopdf';

$config['basis_host'] = 'local';

$config['url'] = 'http://wws.ecom.ddev.site/';

$host = $_SERVER['HTTP_HOST'] ?? '';

$config['override']['shop_1']['cookie_domain'] = str_replace(':8080', '', $host);
$config['override']['shop_1']['main_domain'] = $host;
$config['override']['shop_1']['http'] = 'http://' . $config['override']['shop_1']['main_domain'];
$config['override']['shop_1']['https'] = 'http://' . $config['override']['shop_1']['main_domain'];

$config['cfg_override']['ecom/payment/paypal'] = 'ecom/payment/paypal_sandbox';

$config['override']['shop_2']['cookie_domain'] = $host;
$config['override']['shop_2']['main_domain'] = $host;
$config['override']['shop_2']['http'] = 'http://' . $config['override']['shop_2']['main_domain'];
$config['override']['shop_2']['https'] = 'http://' . $config['override']['shop_2']['main_domain'];

$config['override']['shop_2']['grafik_url'] = '/assets/';
$config['override']['shop_2']['media_url'] = '/';

$config['override']['paypal_ersatzteilshop']['API_USERNAME'] = 'sb-jqlpj528292_api1.business.example.com';
$config['override']['paypal_ersatzteilshop']['API_PASSWORD'] = 'M9JNTS9NUYQS2ZU2';
$config['override']['paypal_ersatzteilshop']['API_SIGNATURE'] = 'AV5iMOcuPSFV0POH41qWM7uNuAY9A.e6h0-DeKdpNSns2Xstuw5LiM2Z';
$config['override']['paypal_ersatzteilshop']['API_ENDPOINT'] = 'https://api-3t.sandbox.paypal.com/nvp';
$config['override']['paypal_ersatzteilshop']['PAYPAL_URL_EC'] = 'https://www.sandbox.paypal.com/webscr&cmd=_express-checkout&token=';
$config['override']['paypal_ersatzteilshop']['PAYPAL_URL_ECC'] = 'https://www.sandbox.paypal.com/webscr&cmd=_complete-express-checkout&token=';

$config['override']['paypal_ersatzteilshop']['shop_config'] = [
    'host' => 'http://ersatz.local/',
    'returnurl' => 'http://ersatz.local/kasse/?do=paypal',
    'cancelurl' => 'http://ersatz.local/kasse/step3/',
    'returnurl_express' => 'http://ersatz.local/kasse/?do=paypal_direct',
    'cancelurl_express' => 'http://ersatz.local/warenkorb/',
    'giropaysuccessurl' => 'http://ersatz.local/kasse/success/',
    'giropaycancelurl' => 'http://ersatz.local/kasse/success/',
    'banktxnpendingurl' => 'http://ersatz.local/kasse/success/',
    'notifyurl' => 'http://ersatz.local/api/paypal_ipn.php',
    'hdrimg' => 'https://www.ersatzteilshop.de/assets/logo.png'
];

$config['storage_config'] = 'storage_test';

//hosts für elasticsearch anpassen -> es ist leider aktuell nicht möglich einzelnen array keys zu überschreiben
$config['override']['shop_1']['catalog'] = [
    'index' => 'allego_products',
    'indexer_override_index' => 'allego_products_6',
    'class' => ShopCatalogElasticsearch::class,
    'hosts' => ['elasticsearch:9200']
];

$config['override']['shop_1']['elasticsearch_devices'] = [
    'index' => 'allego_devices',
    'indexer_override_index' => 'allego_devices_3',
    'hosts' => ['elasticsearch:9200']
];

$config['override']['shop_1']['elasticsearch_goodies'] = [
    'index' => 'allego_goodies',
    'product_index' => $config['override']['shop_1']['catalog']['index'],
    'class' => ShopCatalogGoodieElasticsearch::class,
    'hosts' => ['elasticsearch:9200']
];

$config['smtp'] = 'smtp://localhost:1025';

return $config;
