<?php

$config = require(__DIR__ . '/../system_default.conf.php');

$config['default_migration_shop_id'] = 1;

$config['basis_host'] = 'ecom-dresden.de';

//leitet je mandant auf andere domain um
$config['use_subdomain_mapping'] = true;

$config['url'] = 'https://wws.' . $config['basis_host'] . '/';

$config['https_url'] = 'https://wws.' . $config['basis_host'] . '/';
$config['https_redirect_on_login'] = true;

$config['temp_dir'] = __DIR__ . '/../../temp';
$config['archiv_dir'] = __DIR__ . '/../../archiv';
$config['import_dir'] = __DIR__ . '/../../import';
$config['root_dir'] = __DIR__ . '/../../';
$config['res_dir'] = __DIR__ . '/../../web/res';

$config['fonts_dir'] = $config['root_dir'] . '/web/wws/res/fonts/';



$config['debug'] = false;

$config['debug_mail_redirecting'] = false; //alle system mails werden an diese addresse geschickt

$config['modul_config'] = [
    'gutscheine' => true,

    'notifications' => true,

    'callback' => true,
    'messager' => true,

    'bugreport' => true,
    'service' => true,
    'wackler_controlling' => true,
    'angebote' => false,
    'kasse' => false,
    'chef' => true,
    'product_vergleich' => true,
    'product_psm_info' => true,
    'product_psm_export' => true,
    'market_view' => true,
    'product_automatik' => true,
    'product_spezial_logistik' => false,
    'product_videos' => true,
    'product_features' => true,
    'product_associations' => true,
    'product_media' => true,
    'product_set' => true,
    'product_statistik' => true,
    'product_protokoll_old' => true,
    'product_grundpreis' => true,
    'product_extra_services' => true,
    'product_filter_lists' => true,
    'product_events' => false,

    'product_goodies' => true,

    'product_preisschild_printer' => true,
    'product_online_marketing' => true,

    'amazon' => true,
    'ebay' => true,

    'customer_beruhigung' => true,
    'customer_frist' => true,
    'customer_mails' => true,

    'b2b' => true,

    'invoice' => true,

    'wws\Shop\ShopNews' => false,

    'controlling' => true
];


$config['default_unit_code'] = 'Stk';
$config['default_lager_id'] = 1;

$config['theme'] = 'skin-blue-light';

$config['themes'] = [
    'default' => 'Standard',
//    'skin-green' => 'Grün',
//    'skin-green-light' => 'Grün - helles Menü',
    'skin-blue' => 'Blau',
    'skin-blue-light' => 'Blau - helles Menü',
    'skin-black' => 'Schwarz/Weiß',
    'skin-yellow' => 'Gelb',
    'skin-yellow-light' => 'Gelb - helles Menü',
//    'skin-red' => 'Rot',
//    'skin-red-light' => 'Rot - helles Menü',
];

$config['gateway_secret'] = 'e§$3wsDF"324'; //wird benutzt um die echtheit von parametern in urls zu garantieren




$config['app_salt'] = '4§$fcw&3#.}fe$32'; //darf mit nicht geändert werden wenn kunden passwörter oder user accounts existieren -> passwörter werden ungültig

//fallback procukt id falls einer bestellung ein unbekanntes produkt hinzugefügt wird (nur ebay u. amazon)
$config['unknown_product_id'] = '143832';

$config['smtp'] = 'smtp://<EMAIL>:<EMAIL>:587';

$config['contact_form_secret'] = 'g4D42-234s3SDShbc';

$config['alert_mailer_default_sender_mail'] = '<EMAIL>';
$config['alert_mailer_default_recipient_mail'] = '<EMAIL>';


$config['storage_config'] = 'ecom/storage_live';

$config['di_definitions'] = [
    __DIR__ . '/../di/modules.php',
    __DIR__ . '/../di/modules_ecom.php',
    __DIR__ . '/../di/market_view.php',
];

$config['cfg_override']['db_default'] = 'ecom/db_default';
$config['cfg_override']['db_shop'] = 'ecom/db_shop';
$config['cfg_override']['db_stats'] = 'ecom/db_stats';
$config['cfg_override']['db_protokoll'] = 'ecom/db_protokoll';
$config['cfg_override']['db_market_view'] = 'ecom/db_market_view';
$config['cfg_override']['db_instant_ddl_check'] = 'ecom/db_instant_ddl_check';

$config['cfg_override']['event_handlers'] = 'ecom/event_handlers';
$config['cfg_override']['event_subscriptions'] = 'ecom/event_subscriptions';
$config['cfg_override']['file_purger'] = 'ecom/file_purger';
$config['cfg_override']['protokoll'] = 'ecom/protokoll';
$config['cfg_override']['product_list'] = 'ecom/product_list';
$config['cfg_override']['product_defaults'] = 'ecom/product_defaults';
$config['cfg_override']['gs_lieferzeiten'] = 'ecom/gs_lieferzeiten';

return $config;
