<?php

use bqp\Currency\CurrencyUtils;
use wws\Lager\InventurRepository;
use wws\Lager\LagerRepository;
use wws\Order\OrderConst;
use wws\Payment\PaymentRepository;
use wws\Product\ProductLagerProtokoll;
use wws\Product\ProductRepositoryLegacy;
use wws\Shipment\ShipmentRepository;

$config = [];

//wiederkehrendes
    $product_id = [
        'name' => 'Produkt ID',
        'order' => true,
        'callback' => [ProductRepositoryLegacy::class, 'tableHelper_product_id']
    ];

    $auftnr = [
        'name' => 'Auftnr.',
        'typ' => 'string',
        'order' => true,
        'template' => '<a href="/ax/customer/orders/?action=search_by_auftnr&auftnr=__value__" onclick="popup_large(event)" class="inline">__value__</a>'
    ];

    $shop_id_template = [
        'name' => 'Shop',
        'order' => true,
        'callback' => function($daten, $key) {
            static $shops = null;
            if($shops === null) {
                $shops = \wws\business_structure\business_structure_factory::getRepository()->getShopNames();
            }
            return $shops[$daten[$key]];
        }
    ];

//config


    $config['protokoll_csv.datum'] = [
        'name' => 'Datum',
        'typ' => 'date',
        'format_output' => 'd.m.Y H:i:s',
        'order' => true
    ];

    $config['protokoll_csv.ip'] = [
        'name' => 'IP',
        'typ' => 'string'
    ];

    $config['protokoll_csv.psm'] = [
        'name' => 'Preissuchmaschine',
        'typ' => 'string'
    ];


    // <editor-fold defaultstate="collapsed" desc="protokoll_product">
        $config['MAKRO.protokoll_product.user'] = [
            'name' => 'User',
            'sql' => 'protokoll_product.user_id',
            'order' => 'protokoll_product.user_id',
            'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
        ];

        $config['MAKRO.protokoll_product.typ'] = [
            'name' => 'Typ',
            'sql' => 'protokoll_product.type, protokoll_product.type_id',
            'order' => 'protokoll_product.type',
            'callback' => [['di' => wws\Product\ModelProtocolTranslatorProduct::class], 'tableHelperTranslateType']
        ];
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="protokoll_customer">
        $config['protokoll_customer.user_id'] = [
            'name' => 'UserId',
            'typ' => 'string'
        ];


        $config['protokoll_customer.date_added'] = [
            'name' => 'Datum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y H:i:s',
            'order' => true
        ];

        $config['protokoll_customer.new_value'] = [
            'name' => 'neuer Wert',
            'typ' => 'string'
        ];

        $config['protokoll_customer.field'] = [
            'name' => 'Feld',
            'typ' => 'string'
        ];

        $config['MAKRO.protokoll_customer.user'] = [
            'name' => 'User',
            'sql' => 'protokoll_customer.user_id',
            'order' => 'protokoll_customer.user_id',
            'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
        ];
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="gutscheine">
        $config['gutscheine.gutschein_code'] = [
            'name' => 'Gutscheincode',
            'typ' => 'string'
        ];

        $config['gutscheine.date_create'] = [
            'name' => 'Datum erstellt',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y H:i:s',
            'order' => true
        ];

        $config['gutscheine.expire'] = [
            'name' => 'abgelaufen',
            'typ' => 'string',
            'order' => true
        ];


        $config['gutscheine.expire_date'] = [
            'name' => 'Verfallsdatum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y',
            'order' => true
        ];

        $config['gutscheine.expire_count'] = [
            'name' => 'maximale Einlösungen',
            'typ' => 'string',
            'order' => true
        ];

        $config['gutscheine.gutscheinwert'] = [
            'name' => 'Gutscheinwert',
            'typ' => 'currency',
            'order' => true
        ];

        $config['MAKRO.gutscheine.gutscheinwert'] = [
            'name' => 'Gutscheinwert',
            'order' => 'gutscheine.gutscheinwert',
            'sql' => 'gutscheine.gutscheinwert, gutscheine.gutscheinwert_art',
            'callback' => function($daten) {
                switch($daten['gutscheinwert_art']) {
                    case 'euro':
                        return CurrencyUtils::format($daten['gutscheinwert']);
                    case 'prozent':
                        return $daten['gutscheinwert'] . ' %';
                }
            }
        ];


        $config['gutscheine.min_warenwert'] = [
            'name' => 'mindest Warenwert',
            'typ' => 'currency',
            'order' => true
        ];

        $config['gutscheine.neukunde'] = [
            'name' => 'nur Nekunden',
            'typ' => 'string'
        ];

        $config['gutscheine.beschreibung'] = [
            'name' => 'Gutscheintext',
            'typ' => 'string'
        ];

        $config['gutscheine.count_eingeloest'] = [
            'name' => 'eingelöst',
            'typ' => 'string'
        ];
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Inventur">

//        $config['inventur_bestand.bestand_ist-inventur_bestand.bestand_soll'] = array(
//            'typ' => 'callback',
//            'name' => 'Bestandabweichung',
//            'order' => true,
//            'callback' => array(InventurRepository::class, 'tableHelper_bestandsabweichung'),
//            'rollup_complete' => 'SUM'
//        );

        $config['inventur_bestand.bestand_ist'] = [
            'name' => 'Bestand ist',
            'order' => true,
            'typ' => 'string'
        ];

        $config['inventur_bestand.bestand_soll'] = [
            'name' => 'Bestand soll',
            'order' => true,
            'typ' => 'string'
        ];

        $config['inventur_bestand.ek_netto'] = [
            'name' => 'einzel EK',
            'order' => true,
            'typ' => 'currency'
        ];


        $config['inventur.inventur_id'] = [
            'name' => 'Inventur ID',
            'typ' => 'string',
            'order' => true
        ];

        $config['inventur.description'] = [
            'name' => 'Beschreibung',
            'typ' => 'string'
        ];

        $config['inventur.inventur_start'] = [
            'name' => 'Datum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y',
            'order' => true
        ];

        $config['inventur.inventur_end'] = [
            'name' => 'Enddatum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y',
            'order' => true
        ];

        $config['inventur.inventur_status'] = [
            'name' => 'Status',
            'typ' => 'string',
            'order' => true
        ];

        $config['MAKRO.inventur.bestand_abweichung'] = [
            'name' => 'Bestandsabweichung',
            'sql' => 'inventur_bestand.bestand_ist-inventur_bestand.bestand_soll AS bestand_abweichung',
            'order' => 'bestand_abweichung',
            'callback' => function($daten) {
                if($daten['bestand_abweichung'] > 0) {
                    return ['style' => 'background-color: #5EFF00;', 'value' => '<b>' . $daten['bestand_abweichung'] . '</b>'];
                } elseif($daten['bestand_abweichung'] < 0) {
                    return ['style' => 'background-color: red;', 'value' => '<b>' . $daten['bestand_abweichung'] . '</b>'];
                }

                return $daten['bestand_abweichung'];
            },
            'rollup' => 'SUM',
            'rollup_complete' => 'SUM'
        ];

        $config['MAKRO.inventur_bestand.bestand_wert_soll'] = [
            'name' => 'Wert soll',
            'sql' => 'inventur_bestand.bestand_soll*inventur_bestand.ek_netto AS bestand_wert_soll',
            'order' => 'bestand_wert_soll',
            'typ' => 'currency',
            'rollup' => 'SUM',
            'rollup_complete' => 'SUM'
        ];

        $config['MAKRO.inventur_bestand.bestand_wert_ist'] = [
            'name' => 'Wert ist',
            'sql' => 'inventur_bestand.bestand_ist*inventur_bestand.ek_netto AS bestand_wert_ist',
            'order' => 'bestand_wert_ist',
            'typ' => 'currency',
            'rollup' => 'SUM',
            'rollup_complete' => 'SUM'
        ];

    // </editor-fold>


    // <editor-fold defaultstate="collapsed" desc="buchhaltung_konto_transaktionen">
        $config['buchhaltung_konto_transaktionen.valuta'] = [
            'name' => 'Valuta',
            'typ' => 'string',
            'order' => true
        ];

        $config['buchhaltung_konto_transaktionen.buchungsdatum'] = [
            'name' => 'Buchungsdatum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y',
            'order' => true
        ];

        $config['buchhaltung_konto_transaktionen.betrag'] = [
            'name' => 'Betrag',
            'typ' => 'currency_high',
            'order' => true
        ];

        $config['buchhaltung_konto_transaktionen.gvc'] = [
            'name' => 'GVC',
            'typ' => 'string'
        ];

        $config['buchhaltung_konto_transaktionen.buchungstext'] = [
            'name' => 'BUCHUNGSTEXT',
            'typ' => 'string'
        ];

        $config['MAKRO.buchhaltung_konto_transaktionen.verwendungszweck'] = [
            'name' => 'Verwendungszweck',
            'sql' => 'buchhaltung_konto_transaktionen.verwendungszweck_1, buchhaltung_konto_transaktionen.verwendungszweck_2',
            'callback' => [wws\buchhaltung\sta_kontoauszuege::class, 'tableHelper_verwendungszweck']
            //'template' => '{{$verwendungszweck_1}}<br />{{$verwendungszweck_2}}'
        ];

        $config['buchhaltung_konto_transaktionen.blz'] = [
            'name' => 'BLZ',
            'typ' => 'string'
        ];

        $config['buchhaltung_konto_transaktionen.ktonr'] = [
            'name' => 'Konto',
            'typ' => 'string'
        ];

        $config['buchhaltung_konto_transaktionen.name'] = [
            'name' => 'Name',
            'typ' => 'string'
        ];

        $config['buchhaltung_konto_transaktionen.vorkassen_import_status'] = [
            'name' => 'Status',
            'typ' => 'string'
        ];


    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Inventur">
        $config['einst_lager.lager_name'] = [
            'name' => 'Lager',
            'order' => true,
            'typ' => 'string'
        ];

        $config['einst_lager.lager_typ'] = [
            'name' => 'Typ',
            'order' => true,
            'callback' => function($daten, $key) {
                return LagerRepository::getLagerTypeNames()[$daten[$key]];
            }
        ];

        $config['einst_lager.wareneingang'] = [
            'name' => 'Wareneingang',
            'callback' => function($daten, $key) {
                return $daten[$key] ? 'ja' : '';
            }
        ];

        $config['einst_lager.warenausgang'] = [
            'name' => 'Warenausgang',
            'callback' => function($daten, $key) {
                return $daten[$key] ? 'ja' : '';
            }
        ];

        $config['einst_lager.versand_handler'] = [
            'name' => 'Versand',
            'typ' => 'string'
        ];


    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Rechnungen">
        $config['buchhaltung_rechnung.rechnungs_nr'] = [
            'name' => 'Belegnr.',
            'typ' => 'string',
            'order' => true,
            'template' => '<a href="javascript:invoiceByBelegId({{$rechnungs_id}})">{{$rechnungs_nr}}</a>'
        ];

        $config['buchhaltung_rechnung.rechnungs_datum'] = [
            'name' => 'Belegdatum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y',
            'order' => true
        ];


        $config['buchhaltung_rechnung.rechnungs_betrag_netto'] = [
            'name' => 'Betrag (Netto)',
            'typ' => 'currency_high',
            'order' => true,
            'rollup_complete' => 'SUM'
        ];

        $config['buchhaltung_rechnung.rechnungs_betrag_brutto'] = [
            'name' => 'Betrag (Brutto)',
            'typ' => 'currency_high',
            'order' => true,
            'rollup_complete' => 'SUM'
        ];

        $config['buchhaltung_rechnung.rechnungs_type'] = [
            'name' => 'Belegart',
            'order' => true,
            'callback' => function($daten, $field) { return $daten[$field] == \wws\buchhaltung\Invoice\InvoiceDocument::INVOICE_TYPE_INVOICE ? 'Rechnung' : 'Gutschrift'; }
        ];

        $config['buchhaltung_rechnung.tax_status'] = [
            'name' => 'Mwst',
            'order' => true,
            'callback' => function($daten, $field) { return $daten[$field] == OrderConst::TAX_STATUS_NORMAL ? 'Ja' : 'Nein'; }
        ];

    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Proxies">
        $config['einst_http_proxies.ip'] = [
            'name' => 'IP',
            'typ' => 'string',
            'order' => true
        ];

        $config['einst_http_proxies.port'] = [
            'name' => 'Port',
            'typ' => 'string'
        ];

        $config['einst_http_proxies.status'] = [
            'name' => 'Status',
            'typ' => 'int',
            'order' => true
        ];
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="warenausgang_lieferschein">
        $config['warenausgang_lieferschein.lieferschein_id'] = [
            'name' => 'Lieferschein-ID',
            'typ' => 'string',
            'order' => true
        ];

        $config['warenausgang_lieferschein.auftnr'] = $auftnr;

        $config['warenausgang_lieferschein.datum'] = [
            'name' => 'Lieferscheindatum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y H:i:s',
            'order' => true
        ];

        $config['warenausgang_lieferschein.lager_id'] = [
            'name' => 'Lager',
            'order' => true,
            'callback' => [\wws\Lager\LagerRepository::class, 'tableHelper_lager']
        ];

        $config['warenausgang_lieferschein.sped_id'] = [
            'name' => 'Sped.',
            'order' => true,
            'callback' => [ShipmentRepository::class, 'tableHelper_sped_logo'],
            'align' => 'center'
        ];

        $config['warenausgang_lieferschein.status'] = [
            'name' => 'Status',
            'order' => true,
            'callback' => [\wws\Lager\WarenausgangLieferscheinRepository::class, 'tableHelper_status']
        ];


        $config['MAKRO.warenausgang_lieferschein.grouped_products'] = [
            'name' => 'Produkte',
            'sql' => "GROUP_CONCAT(CONCAT(order_item.product_id,'||',warenausgang_lieferschein_items.anzahl,'||',order_item.product) SEPARATOR '|||') AS grouped_products",
            'order' => 'order_item.product',

            'callback' => function($daten, $key) {
                $return = '';
                $products = explode('|||', $daten[$key]);

                foreach($products AS $product_temp) {
                    [$product_id, $quantity, $product_name] = explode('||', $product_temp);

                    $return .= $quantity . ' x ' . '<a href="/ax/artikel/product/product/?back=close&product_id=' . $product_id . '" onclick="popup_large(event, {name:\'productwin\'})">' . $product_name . '</a><br>';
                }

                return $return;
            }
        ];

        $config['warenausgang_lieferschein.supplier_order_id'] = [
            'name' => 'Bestellnr.',
            'typ' => 'callback',
            'order' => true,
            'callback' => function (array $row, string $key) {
                $supplier_order_id = $row[$key];
                if ($supplier_order_id) {
                    return '<a href="/ax/gs/bestellung/?supplier_order_id=' . $supplier_order_id . '" target="inline">B' . $supplier_order_id . '</a>';
                }

                return '';
            }
        ];

    // </editor-fold>

    include('tables/orders_warenkorb.php');
    include('tables/customers.php');
    include('tables/supplier_order.php');
    include('tables/supplier.php');
    include('tables/buchhaltung_gutschriften.php');
    include('tables/buchhaltung_export_archive.php');
    include('tables/lager.php');
    include('tables/hersteller_gutschriften.php');
    include('tables/user_accounts.php');
    include('tables/protokoll_system.php');
    include('tables/products.php');
    include('tables/market_view.php');
    include('tables/wareneingang_lieferschein.php');
    include('tables/wackler.php');
    include('tables/controlling_rieck.php');
    include('tables/protokoll_supplier_order.php');
    include('tables/order_addresses.php');

    $config['MAKRO.buchhaltung_rueckzahlung.user'] = [
        'name' => 'User',
        'sql' => 'buchhaltung_rueckzahlung.user_id',
        'order' => 'buchhaltung_rueckzahlung.user_id',
        'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
    ];

    $config['b2b_price_groupes.b2b_price_group_name'] = [
        'name' => 'B2B Preisgruppe',
        'typ' => 'string',
        'order' => true
    ];

    $config['b2b_price_groupes.price_10'] = [
        'name' => 'bis 10 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_20'] = [
        'name' => 'bis 20 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_50'] = [
        'name' => 'bis 50 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];
    $config['b2b_price_groupes.price_75'] = [
        'name' => 'bis 75 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_100'] = [
        'name' => 'bis 100 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_250'] = [
        'name' => 'bis 250 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_500'] = [
        'name' => 'bis 500 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_750'] = [
        'name' => 'bis 750 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_1000'] = [
        'name' => 'bis 1000 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_2000'] = [
        'name' => 'bis 2000 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['b2b_price_groupes.price_gt_2000'] = [
        'name' => 'ab 2000 €',
        'typ' => 'integer',
        'order' => true,
        'template' => '__VALUE__%'
    ];

    $config['shop_news.news_date'] = [
        'name' => 'Datum',
        'typ' => 'string',
        'order' => true
    ];

    $config['shop_news.author'] = [
        'name' => 'Bearbeiter',
        'typ' => 'string',
        'order' => true
    ];

    $config['shop_news.news_title'] = [
        'name' => 'Titel',
        'typ' => 'string',
        'order' => true
    ];



    $config['protokoll_zahlung.bardatum'] = [
            'name' => 'Datum',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y H:i',
            'order' => true
    ];

    $config['protokoll_zahlung.zahlungsart'] = [
        'name' => 'Zahlungsart',
        'order' => true,
        'callback' => [PaymentRepository::class, 'tableHelper_zahlungsart']
    ];

    $config['protokoll_zahlung.barbetrag'] = [
        'name' => 'Betrag',
        'order' => true,
        'typ' => 'currency'
    ];


    $config['buchhaltung_personenkonten.person_konto_nr'] = [
        'name' => 'Kontonummer',
        'typ' => 'string',
        'order' => true
    ];

    $config['buchhaltung_personenkonten.konto_typ'] = [
        'name' => 'Typ',
        'order' => true,
        'callback' => [personenkonto_repository::class, 'tableHelper_konto_type']
    ];

    $config['buchhaltung_personenkonten.konto_name'] = [
        'name' => 'Name',
        'typ' => 'string',
        'order' => true
    ];


    $config['b2b_access_log.ip'] = [
        'name' => 'IP',
        'typ' => 'string',
        'order' => true
    ];

    $config['b2b_access_log.date_login'] = [
            'name' => 'Login',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y H:i',
            'order' => true
    ];

    $config['b2b_access_log.date_last'] = [
            'name' => 'letzter Klick',
            'typ' => 'date',
            'format' => 'date',
            'format_output' => 'd.m.Y H:i'
    ];

    $config['b2b_access_log.clicks'] = [
        'name' => 'Klicks',
        'typ' => 'string'
    ];



    $config['MAKRO.protokoll_order.user'] = [
        'name' => 'User',
        'sql' => 'protokoll_order.user_id',
        'order' => 'protokoll_order.user_id',
        'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
    ];

$config['MAKRO.einst_mail.has_attachments'] = [
    'name' => 'Anlagen',
    'sql' => 'IF(einst_mail.attachments != "", 1, 0) AS has_attachments',
    'order' => 'einst_mail.attachments',
    'callback' => function($daten) {
        return $daten['has_attachments'] ? 'Ja' : '';
    }
];


$config['MAKRO.buchhaltung_bank_transactions.amount'] = [
    'type' => 'currency_high',
    'currency_code' => 'currency_code',
    'name' => 'Betrag',
    'sql' => 'buchhaltung_bank_transactions.amount, buchhaltung_bank_transactions.currency_code',
    'order' => 'buchhaltung_bank_transactions.amount'
];

$config['MAKRO.mailsystem_mails.has_attachments'] = [
    'name' => '',
    'sql' => 'IF(mailsystem_mails.anlagen != "", 1, 0) AS has_attachments',
    'order' => 'mailsystem_mails.anlagen',
    'callback' => function($daten) {
        return $daten['has_attachments'] ? '<img src="/res/images/mailsystem/anlage.gif" alt="Anlage">' : '';
    }
];

$config['protokoll_lager.user_id'] = [
    'name' => 'User',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

$config['MAKRO.protokoll_lager.grund'] = [
    'name' => 'Grund',
    'sql' => 'protokoll_lager.grund',
    'callback' => function($values) { return ProductLagerProtokoll::translateReason($values['grund']); }
];


$config['MAKRO.customer_mail_archive.has_attachments'] = [
    'name' => 'Anlagen',
    'sql' => 'IF(customer_mail_archive.attachments != "", 1, 0) AS has_attachments',
    'order' => 'customer_mail_archive.attachments',
    'callback' => function($daten) {
        return $daten['has_attachments'] ? 'Ja' : '';
    }
];

$config['customer_mail_archive.user_id'] = [
    'name' => 'User',
    'callback' => [wws\Users\UserRepository::class, 'tableHelperUsername']
];

$config['MAKRO.protokoll_filter_lists.user'] = [
    'name' => 'User',
    'sql' => 'protokoll_filter_lists.user_id',
    'order' => 'protokoll_filter_lists.user_id',
    'callback' => [wws\Users\UserRepository::class, 'tableHelper_username']
];

$config['MAKRO.buchhaltung_bank_settlements.quantity_to_process'] = [
    'name' => 'zu buchen',
    'sql' => '(buchhaltung_bank_settlements.quantity - buchhaltung_bank_settlements.quantity_processed) AS quantity_to_process',
    'order' => 'quantity_to_process',
    'callback' => function($row) {
        $value = (int)$row['quantity_to_process'];

        if ($value === 0) {
            return '<span style="color: #ccc">0</span>';
        }

        return '<b style="color: red">' . $value . '</b>';
    }
];

return $config;
