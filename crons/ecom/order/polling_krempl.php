<?php

use bqp\extern\krempl\KremplStatusProcessor;

include('bootstrap.php');
longRun();

service_loader::runtimeLock()->tryLock(__FILE__);

$protocol = system_protokoll::getInstance('krempl_order_status');

$krempl = new KremplStatusProcessor($protocol, service_loader::getConfigRegistry()->get('ecom/supplier/krempl'));

$krempl->processImportDirectory(config::getLegacy('ecom/supplier/krempl', 'tracking_import_dir'));
