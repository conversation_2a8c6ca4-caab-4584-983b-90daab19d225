<?php

use bqp\Date\DateObj;
use bqp\extern\Sonepar\SoneparFulfillOrderApi;

include('bootstrap.php');
longRun();

service_loader::runtimeLock()->tryLock(__FILE__);

$deg = new SoneparFulfillOrderApi(config::getLegacy('k11/supplier/deg'));

$to = new DateObj();
//$to->subSimple('days', 1);
$from = clone $to;
$from->subSimple('days', 8);


foreach ($from->rangeTo($to, 'days', 1) as $date) {
    $deg->updateShipmentForDate($date->format('Ymd'));
}
