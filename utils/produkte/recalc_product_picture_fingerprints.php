<?php

use bqp\GdImage\GdImage;
use bqp\KeyValueStore\KeyValueStoreDb;
use wws\Product\Media\ProductPictureFingerprint;
use wws\Product\ProductMedia;

include('bootstrap.php');

longRun(3600 * 24 * 7, 1024);


$kv_store = service_loader::get(KeyValueStoreDb::class);
$kv_lifetime = 3600 * 24 * 60;
$kv_key = 'fix_last_product_media_id';

$last_product_media_id = $kv_store->get($kv_key, 0);

var_dump("
Product Media Data Refresh Fix
. -> Datensatz ist aktuell
u -> Datensatz wurde aktualisiert

LAST product_media_id: $last_product_media_id

");

$stmt = $db->prepare("
    UPDATE
        product_media
    SET
        product_media.fingerprint = :fingerprint
    WHERE
        product_media.product_media_id = :product_media_id
");

$storage = service_loader::getStorageFactory()->get('media');

while (true) {
    $result = $db->query("
        SELECT
            product_media.product_media_id,
            product_media.filename,
            product_media.fingerprint
        FROM
            product_media
        WHERE
            product_media.media_type = '" . ProductMedia::MEDIA_TYPE_PICTURE . "' AND
            product_media.product_media_id > $last_product_media_id
        ORDER BY
            product_media.product_media_id
        LIMIT
            1000
    ");

    if ($result->count() === 0) {
        echo "Done.\n";
        break;
    }

    $i = 0;

    foreach ($result as $daten) {
        $i++;

        if (($i++ % 25) === 0) {
            $kv_store->put($kv_key, $last_product_media_id, $kv_lifetime);
        }

        $image_raw = $storage->read('media://artimages/' . $daten['filename']);

        $image = new GdImage($image_raw);

        $fingerprint = ProductPictureFingerprint::createFingerprint($image);

        if ($fingerprint !== $daten['fingerprint']) {
            echo 'u';
            $stmt->execute($fingerprint, $daten['product_media_id']);
        } else {
            echo '.';
        }

        $last_product_media_id = $daten['product_media_id'];
    }
}
