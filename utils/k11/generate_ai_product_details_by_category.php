<?php

use bqp\extern\OpenRouter\OpenRouterApi;
use wws\Product\GeneratorAI\ProductDetailsGeneratorAIK11;
use wws\Product\ProductConst;
use wws\Shopware\Product\ProductDataSync;

require_once 'bootstrap.php';

$cat_id = 0;
$limit = 0;
$with_article_name = false;
$regen_description = false;
foreach ($argv as $i => $arg) {
    if ($i === 0) {
        continue;
    }
    if (strpos($arg, '--cat_id=') === 0) {
        $cat_id = (int)substr($arg, 9);
    }
    if (strpos($arg, '--limit=') === 0) {
        $limit = (int)substr($arg, 8);
    }
    if ($arg === '--with_article_name') {
        $with_article_name = true;
    }
    if (strpos($arg, '--with_article_name=') === 0) {
        $with_article_name = ((int)substr($arg, 20)) > 0;
    }
    if ($arg === '--regen-description') {
        $regen_description = true;
    }
}
if ($cat_id <= 0) {
    throw new Exception('Missing or invalid --cat_id');
}

$lockKey = __FILE__ . ':cat=' . (int)$cat_id;
service_loader::runtimeLock()->tryLock($lockKey);

$db = db::getInstance();
$tag = $db->escape(ProductConst::TAG_AI_CONTENT);

if ($regen_description) {
    $tracking_file = __DIR__ . '/regen_description_done_' . (int)$cat_id . '.json';
    $already_done = [];
    if (file_exists($tracking_file)) {
        $already_done = json_decode(file_get_contents($tracking_file), true) ?: [];
    }

    $sql_count = sprintf(
        "SELECT COUNT(*) AS cnt FROM product p WHERE p.cat_id = %d",
        (int)$cat_id
    );
    $total_in_cat = (int)($db->singleQuery($sql_count)['cnt'] ?? 0);
    echo 'Total products in cat_id=' . (int)$cat_id . ': ' . $total_in_cat . ' (regen-description mode)' . PHP_EOL;
    echo 'Already regenerated (tracked): ' . count($already_done) . PHP_EOL;
    echo 'Remaining: ' . ($total_in_cat - count($already_done)) . PHP_EOL;

    $sql = sprintf(
        "SELECT p.product_id FROM product p WHERE p.cat_id = %d ORDER BY p.product_id ASC",
        (int)$cat_id
    );
} else {
    $sql_count = sprintf(
        "SELECT COUNT(*) AS cnt
         FROM product p
         LEFT JOIN product_tags pt
           ON pt.product_id = p.product_id
          AND pt.product_tag = '%s'
         WHERE p.cat_id = %d AND pt.product_id IS NULL",
        $tag,
        (int)$cat_id
    );
    $pending_total = (int)($db->singleQuery($sql_count)['cnt'] ?? 0);
    echo 'Pending without AI tag in cat_id=' . (int)$cat_id . ': ' . $pending_total . PHP_EOL;

    $sql = sprintf(
        "SELECT
            p.product_id
         FROM
            product p
            LEFT JOIN product_tags pt
                   ON pt.product_id = p.product_id
                  AND pt.product_tag = '%s'
         WHERE
            p.cat_id = %d
            AND pt.product_id IS NULL
         ORDER BY
            p.product_id ASC",
        $tag,
        (int)$cat_id
    );
}
$product_ids = $db->query($sql)->asSingleArray();

if ($regen_description && $already_done) {
    $product_ids = array_values(array_diff($product_ids, $already_done));
}

if ($limit > 0) {
    $product_ids = array_slice($product_ids, 0, $limit);
}

if (!$product_ids) {
    throw new Exception('No products found for AI generation.');
}
echo 'Will process up to ' . count($product_ids) . ' products in this run.' . PHP_EOL;

$api = service_loader::get(OpenRouterApi::class);
$generator = new ProductDetailsGeneratorAIK11($api);
if ($with_article_name) {
    $generator->setGenerateStammProductName(true);
}
if ($regen_description) {
    $generator->setOnlyDescription(true);
}
$succeeded = [];
$processed = 0;
$saved = 0;
$skipped = 0;

foreach ($product_ids as $pid) {
    $processed++;
    echo 'Processing product_id=' . (int)$pid . ' (' . $processed . '/' . count($product_ids) . ')' . PHP_EOL;

    $res = $generator->generate((int)$pid);
    $gen = $res['generated_data']['de'] ?? [];
    $errors = $res['errors'] ?? [];

    $article = isset($res['generated_data']['product_name']) ? trim((string)$res['generated_data']['product_name']) : '';
    $title = trim((string)($gen['product_name_localized'] ?? ''));
    $desc = trim((string)($gen['artikelinfo'] ?? ''));

    if ($with_article_name) {
        echo '  Generated Artikelname: ' . ($article !== '' ? $article : '[none]') . PHP_EOL;
    }
    if (!$regen_description) {
        echo '  Generated Shopname: ' . ($title !== '' ? $title : '[none]') . PHP_EOL;
    }
    $desc_preview = $desc !== '' ? mb_substr(strip_tags($desc), 0, 100) : '';
    echo '  Generated Description: ' . ($desc_preview !== '' ? $desc_preview . (mb_strlen(strip_tags($desc)) > 100 ? '…' : '') : '[none]') . PHP_EOL;

    $should_skip = false;
    if (!$regen_description && $title === '') {
        $err = $errors['de']['product_name_localized'] ?? '(empty result)';
        echo '  ERROR: Shopname generation failed: ' . $err . PHP_EOL;
        $should_skip = true;
    }
    if ($desc === '') {
        $err = $errors['de']['artikelinfo'] ?? '(empty result)';
        echo '  ERROR: Description generation failed: ' . $err . PHP_EOL;
        $should_skip = true;
    }
    if ($should_skip) {
        echo '  Skipping save.' . PHP_EOL;
        $skipped++;
        continue;
    }

    $generator->save((int)$pid, $res);
    $succeeded[] = (int)$pid;
    $saved++;
    echo '  Saved.' . PHP_EOL;

    if ($regen_description) {
        $already_done[] = (int)$pid;
        file_put_contents($tracking_file, json_encode($already_done));
    }
}

echo 'Run summary: processed=' . $processed . ', saved=' . $saved . ', skipped=' . $skipped . PHP_EOL;

if ($succeeded) {
    $container = service_loader::getDiContainer();
    if ($container->has(ProductDataSync::class)) {
        try {
            $sync = $container->get(ProductDataSync::class);
            $sync->syncProductIdsToShopware($succeeded);
        } catch (Throwable $e) {
        }
    }
}

