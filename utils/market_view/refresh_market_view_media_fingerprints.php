<?php

use bqp\KeyValueStore\KeyValueStoreDb;
use wws\MarketView\MarketViewMediaRepository;
use wws\MarketView\MarketViewMediaStorage;

include('bootstrap.php');

longRun(3600 * 24 * 7, 1024);


$kv_store = service_loader::get(KeyValueStoreDb::class);
$kv_lifetime = 3600 * 24 * 60;
$kv_key = 'fix_last_mv_media_id';

$db_mv = service_loader::get('db.market_view');
$storage = service_loader::get(MarketViewMediaStorage::class);
$mv_media_repository = service_loader::getDiContainer()->get(MarketViewMediaRepository::class);



$last_mv_media_id = $kv_store->get($kv_key, 0);



var_dump("
Market View Media Meta Data Refresh Fix
. -> Datensatz ist aktuell
u -> Datensatz wurde aktualisiert
F -> url_storage not found
E -> \wws\MarketView\MarketViewMediaUtils::getMetaDataByRawImage failed

LAST mv_media_id: $last_mv_media_id

");

while(true)
{
    $result = $db_mv->query("
        SELECT
            market_view_media.mv_media_id,
            market_view_media.url_storage,
            market_view_media.fingerprint,
            market_view_media.md5
        FROM
            market_view_media
        WHERE
            market_view_media.mv_media_id > $last_mv_media_id AND
            market_view_media.url_storage != ''
        ORDER BY
            market_view_media.mv_media_id
        LIMIT
            100000
    ");

    if ($result->count() === 0) {
        echo "Done.\n";
        break;
    }

    //$result->display();
    $i = 0;

    foreach ($result as $row) {
        if (($i++ % 25) === 0) {
            $last_mv_media_id = $row['mv_media_id'];
            $kv_store->put($kv_key, $last_mv_media_id, $kv_lifetime);
        }

        try {
            $blob = $storage->get($row['url_storage']);
        } catch (League\Flysystem\FileNotFoundException $e) {
            echo 'F';
            continue;
        }

        try {
            $meta = \wws\MarketView\MarketViewMediaUtils::getMetaDataByRawImage($blob);
        } catch (\Throwable) {
            echo 'E';
            continue;
        }

        if ($row['fingerprint'] === $meta['fingerprint'] && $row['md5'] === $meta['md5']) {
            echo '.';
            continue;
        }

        echo 'u';

        $db_mv->query("
            UPDATE
                market_view_media
            SET
                market_view_media.fingerprint = " . $db_mv->quote($meta['fingerprint']) . ",
                market_view_media.md5 = " . $db_mv->quote($meta['md5']) . ",
                market_view_media.width = " . $meta['width'] . ",
                market_view_media.height = " . $meta['height'] . "
            WHERE
                market_view_media.mv_media_id = " . $row['mv_media_id'] . "
        ");
    }
}
