<section>
  <h1 id="setting-default-options">Setting default options</h1>

  <p>
    In some cases, you need to set the default options for all instances of
    Select2 in your web application. This is especially useful when you are
    migrating from past versions of Select2, or you are using non-standard
    options <a href="#amd">like custom AMD builds</a>. Select2 exposes the
    default options through <code>$.fn.select2.defaults</code>, which allows
    you to set them globally.
  </p>

  <p>
    When setting options globally, any past defaults that have been set will
    be overriden. Default options are only used when an option is requested
    that has not been set during initialization.
  </p>

  <p>
    <strong>You can set default options</strong> by calling
    <code>$.fn.select2.defaults.set("key", "value")</code>. The key that is
    set should take the same format as keys set using
    <a href="#data-attributes">HTML <code>data-*</code> attributes</a> which
    means that two dashes (<code>--</code>) will be replaced by a level of
    nesting, and a single dash (<code>-</code>) will convert it to a camelCase
    string.
  </p>

<pre class="prettyprint">
$.fn.select2.defaults.set("theme", "classic");
</pre>

  <p>
    <strong>You can reset the default options</strong> by calling
    <code>$.fn.select2.defaults.reset()</code>.
  </p>
</section>
