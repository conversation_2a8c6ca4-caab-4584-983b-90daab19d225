<section>
  <div id="events" class="page-header">
    <h1>Events</h1>
  </div>

  <p>
    Select2 has an internal event system that is used to notify parts of the
    component that state has changed, as well as an adapter that allows some
    of these events to be relayed to the outside word.
  </p>

  <dl class="dl-horizontal">
    <dt>Adapter</dt>
    <dd>
      <code title="select2/selection">SelectionAdapter</code>
    </dd>

    <dt>Decorator</dt>
    <dd>
      <code title="select2/selection/eventRelay">EventRelay</code>
    </dd>
  </dl>

  <h2 id="events-public">
    Public events
  </h2>

  <p>
    All public events are relayed using the jQuery event system, and they are
    triggered on the <code>&lt;select&gt;</code> element that Select2 is
    attached to. You can attach to them using the
    <a href="https://api.jquery.com/on/"><code>.on</code> method</a> provided
    by jQuery.
  </p>

  <h2 id="events-internal">
    Internal events
  </h2>

  <p>
    Select2 triggers internal events using its own internal event system,
    which allows adapters to communicate with each other. These events are not
    accessible through the jQuery event system.
  </p>

  <p>
    You can find more information on the public events triggered by individual
    adapters in <a href="#adapters">the individual adapter documentation</a>.
  </p>
</section>
