<nav class="s2-docs-sidebar hidden-print hidden-xs hidden-sm">
  <ul class="nav s2-docs-sidenav">
    <li>
      <a href="#core-options">Core options</a>
      <ul class="nav">
        <li><a href="#setting-default-options">Changing default options</a></li>
        <li><a href="#data-attributes">Declaring configuration in the HTML</a></li>
        <li><a href="#amd">AMD compatibility</a></li>
      </ul>
    </li>
    <li>
      <a href="#data-adapters">Data adapters</a>
      <ul class="nav">
        <li>
          <a href="#data-adapters-select-tag">Using a <code>&lt;select&gt;</code></a>
        </li>
        <li>
          <a href="#data">Loading data from an array</a>
        </li>
        <li>
          <a href="#ajax">Connecting to a remote data source</a>
        </li>
      </ul>
    </li>
    <li>
      <a href="#selections">Displaying selections</a>
      <ul class="nav">
        <li>
          <a href="#placeholder">Showing a placeholder</a>
        </li>
        <li>
          <a href="#allowClear">Clearing selections</a>
        </li>
        <li>
          <a href="#templateSelection">Templating</a>
        </li>
      </ul>
    </li>
    <li>
      <a href="#results">Displaying results</a>
      <ul class="nav">
        <li><a href="#can-i-change-when-search-results-are-loaded">Controlling result loading</a></li>
        <li><a href="#can-i-change-how-selecting-results-works">Making selections</a></li>
        <li><a href="#can-i-change-how-the-dropdown-is-placed">Placement</a></li>
      </ul>
    </li>
    <li>
      <a href="#events">Events</a>
      <ul class="nav">
        <li><a href="#events-public">Public jQuery events</a></li>
        <li><a href="#events-internal">Internal events</a></li>
      </ul>
    </li>
    <li>
      <a href="#adapters">The plugin system (adapters)</a>
      <ul class="nav">
        <li><a href="#adapters-all">All adapters</a></li>
        <li><a href="#selectionAdapter">Container (selection)</a></li>
        <li><a href="#dataAdapter">Data set</a></li>
        <li><a href="#dropdownAdapter">Dropdown</a></li>
        <li><a href="#resultsAdapter">Results</a></li>
      </ul>
    </li>
    <li>
      <a href="#backwards-compatibility">Deprecated options</a>
      <ul class="nav">
        <li><a href="#compat-matcher">Simplified function for matching data objects</a></li>
        <li><a href="#initSelection">Old initial selections with <code>initSelection</code></a></li>
        <li><a href="#query">Querying old data with <code>query</code></a></li>
        <li><a href="#input-fallback">Compatibility with <code>&lt;input type="text" /&gt;</code></a></li>
      </ul>
    </li>
  </ul>
  <a class="back-to-top" href="#top">
    Back to top
  </a>
</nav>
