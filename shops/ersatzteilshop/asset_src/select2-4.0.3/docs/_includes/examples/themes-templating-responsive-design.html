<section>

  <h1 id="themes-templating-responsive-design" class="page-header">
    Themes, templating and responsive design
  </h1>

  <h2 id="themes">Theme support</h2>

  <p>
    Select2 supports custom themes using the
    <a href="options.html#theme">theme option</a>
    so you can style Select2 to match the rest of your application.
  </p>

  <p>
    These are using the <code>classic</code> theme, which matches the old
    look of Select2.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-theme-single js-states form-control">
      </select>
    </p>
    <p>
      <select class="js-example-theme-multiple js-states form-control" multiple="multiple"></select>
    </p>
  </div>

{% highlight js linenos %}
$(".js-example-theme-single").select2({
  theme: "classic"
});

$(".js-example-theme-multiple").select2({
  theme: "classic"
});
{% endhighlight %}

<h2 id="templating">Templating</h2>

<p>
  Various display options of the Select2 component can be changed:
  You can access the <code>&lt;option&gt;</code> element
  (or <code>&lt;optgroup&gt;</code>) and any attributes on those elements
  using <code>.element</code>.
</p>

<p>
  Templating is primarily controlled by the
  <a href="options.html#templateResult"><code>templateResult</code></a>
  and <a href="options.html#templateSelection"><code>templateSelection</code></a>
  options.
</p>

<div class="s2-example">
  <p>
    <select class="js-example-templating js-states form-control"></select>
  </p>
</div>

{% highlight js linenos %}
function formatState (state) {
  if (!state.id) { return state.text; }
  var $state = $(
    '<span><img src="vendor/images/flags/' + state.element.value.toLowerCase() + '.png" class="img-flag" /> ' + state.text + '</span>'
  );
  return $state;
};

$(".js-example-templating").select2({
  templateResult: formatState
});
{% endhighlight %}

<h2 id="responsive">Responsive design - Percent width</h2>

<p>
  Select2's width can be set to a percentage of its parent to support
  responsive design. The two Select2 boxes below are styled to 50% and 75%
  width respectively.
</p>

<div class="s2-example">
  <p>
    <select class="js-example-responsive js-states" style="width: 50%"></select>
  </p>
  <p>
    <select class="js-example-responsive js-states" multiple="multiple" style="width: 75%"></select>
  </p>
</div>

{% highlight html linenos %}
<select class="js-example-responsive" style="width: 50%"></select>
<select class="js-example-responsive" multiple="multiple" style="width: 75%"></select>
{% endhighlight %}

<div class="alert alert-warning">
  Select2 will do its best to resolve the percent width specified via a
  css class, but it is not always possible. The best way to ensure that
  Select2 is using a percent based width is to inline the
  <code>style</code> declaration into the tag.
</div>
</section>
