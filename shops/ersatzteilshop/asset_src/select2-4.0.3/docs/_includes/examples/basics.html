<section>

  <h1 id="basics" class="page-header">The basics</h1>

  <h2 id="single">Single select boxes</h2>

  <p>
    Select2 can take a regular select box like this...
  </p>

  <p>
    <select class="js-states form-control"></select>
  </p>

  <p>
    and turn it into this...
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-basic-single js-states form-control"></select>
    </p>
  </div>

{% highlight html linenos %}
<script type="text/javascript">
$(document).ready(function() {
  $(".js-example-basic-single").select2();
});
</script>

<select class="js-example-basic-single">
  <option value="AL">Alabama</option>
    ...
  <option value="WY">Wyoming</option>
</select>
{% endhighlight %}

  <h2 id="multiple">Multiple select boxes</h2>

  <p>
    Select2 also supports multi-value select boxes. The select below is declared with the <code>multiple</code> attribute.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-basic-multiple js-states form-control" multiple="multiple"></select>
    </p>
  </div>

{% highlight html linenos %}
<script type="text/javascript">
$(".js-example-basic-multiple").select2();
</script>

<select class="js-example-basic-multiple" multiple="multiple">
  <option value="AL">Alabama</option>
    ...
  <option value="WY">Wyoming</option>
</select>
{% endhighlight %}

  <h2>Select boxes with labels</h2>

  <p>
    You can, and should, use a <code>&lt;label&gt;</code> with Select2, just like any other <code>&lt;select&gt</code> element.
  </p>

  <div class="s2-example">
    <p>
      <label for="id_label_single">
        Click this to highlight the single select element
        <select class="js-example-basic-single js-states form-control" id="id_label_single"></select>
      </label>
    </p>
    <p>
      <label for="id_label_multiple">
        Click this to highlight the multiple select element
        <select class="js-example-basic-multiple js-states form-control" id="id_label_multiple" multiple="multiple"></select>
      </label>
    </p>
  </div>

{% highlight html linenos %}
<label for="id_label_single">
  Click this to highlight the single select element

  <select class="js-example-basic-single js-states form-control" id="id_label_single"></select>
</label>

<label for="id_label_multiple">
  Click this to highlight the multiple select element

  <select class="js-example-basic-multiple js-states form-control" id="id_label_multiple" multiple="multiple"></select>
</label>
{% endhighlight %}
</section>