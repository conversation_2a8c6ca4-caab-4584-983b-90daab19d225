<section>

  <h1 id="disabled-results">Disabled results</h1>

  <p>
    Select2 will correctly handle disabled results, both with data coming
    from a standard select (when the <code>disabled</code> attribute is set)
    and from remote sources, where the object has
    <code>disabled: true</code> set.
  </p>

  <div class="s2-example">
    <p>
      <select class="js-example-disabled-results form-control">
        <option value="one">First</option>
        <option value="two" disabled="disabled">Second (disabled)</option>
        <option value="three">Third</option>
      </select>
    </p>
  </div>

{% highlight html linenos %}
<select class="js-example-disabled-results">
  <option value="one">First</option>
  <option value="two" disabled="disabled">Second (disabled)</option>
  <option value="three">Third</option>
</select>
{% endhighlight %}
</section>
