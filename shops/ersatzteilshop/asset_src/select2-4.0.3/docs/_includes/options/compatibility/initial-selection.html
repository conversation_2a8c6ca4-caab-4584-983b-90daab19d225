<section>
  <h2 id="initSelection">
    Old initial selections with <code>initSelection</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#removed-initselection" class="alert-link">Deprecated in Select2 4.0.</a>
    This has been replaced by another option and is only available in the
    <a href="index.html#builds-full" class="alert-link">full builds</a> of
    Select2.
  </p>

  <p>
    In the past, Select2 required an option called <code>initSelection</code>
    that was defined whenever a custom data source was being used, allowing
    for the initial selection for the component to be determined. This has
    been replaced by the <code>current</code> method on the
    <a href="#dataAdapter">data adapter</a>.
  </p>

  <div class="row">
    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Key</dt>
        <dd>
          <code>initSelection</code>
        </dd>

        <dt>Value</dt>
        <dd>
          A function taking a <code>callback</code>
        </dd>
      </dl>
    </div>

    <div class="col-sm-6">
      <dl class="dl-horizontal">
        <dt>Adapter</dt>
        <dd>
          <code title="select2/data/base">DataAdapter</code>
        </dd>

        <dt>Decorator</dt>
        <dd>
          <code title="select2/compat/initSelection">InitSelection</code>
        </dd>
      </dl>
    </div>
  </div>
</section>