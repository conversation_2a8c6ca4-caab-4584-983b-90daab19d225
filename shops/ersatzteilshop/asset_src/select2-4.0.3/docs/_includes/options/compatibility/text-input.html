<section>
  <h2 id="input-fallback">
    Compatibility with <code>&lt;input type="text" /&gt;</code>
  </h2>

  <p class="alert alert-warning">
    <a href="announcements-4.0.html#hidden-input" class="alert-link">Deprecated in Select2 4.0.</a>
    It is now encouraged to use the <code>&lt;select&gt;</code> tag instead.
  </p>

  <p>
    In past versions of Select2, a <code>&lt;select&gt;</code> element could
    only be used with a limited subset of options. An
    <code>&lt;input type="hidden" /&gt;</code> was required instead, which did
    not allow for a graceful fallback for users who did not have JavaScript
    enabled. Select2 now supports the <code>&lt;select&gt;</code> element for
    all options, so it is no longer required to use <code>&lt;input /&gt;</code>
    elements with Select2.
  </p>

  <dl class="dl-horizontal">
    <dt>Adapter</dt>
    <dd>
      <code title="select2/data/base">DataAdapter</code>
    </dd>

    <dt>Decorator</dt>
    <dd>
      <code title="select2/compat/inputData">InputData</code>
    </dd>
  </dl>
</section>