<section>
  <p>
    Select2 offers limited backwards compatibility with the previously 3.5.x
    release line, allowing people more efficiently transfer across releases
    and get the latest features. For many of the larger changes, such as the
    change in how custom data adapters work, compatibility modules were
    created that will be used to assist in the upgrade process. It is not
    recommended to rely on these compatibility modules, as they will not
    always exist, but they make upgrading easier for major changes.
  </p>

  <p>
    <strong>The compatibility modules are only included in the
    <a href="index.html#builds-full" class="alert-link">full builds</a> of
    Select2</strong>. These files end in <code>.full.js</code>, and the
    compatibility modules are prefixed with <code>select2/compat</code>.
  </p>
</section>