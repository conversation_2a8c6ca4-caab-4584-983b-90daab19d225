<section>
  <h2 id="data-attributes">
    Can I declare my configuration within the HTML?
  </h2>

  <p>
    It is recommended that you declare your configuration options for Select2
    when initializing Select2. You can also define your configuration options
    by using the HTML5 <code>data-*</code> attributes, which will override
    any options set when initializing Select2 and any defaults.
  </p>

  <h3>
    How should <code>camelCase</code> options be written?
  </h3>

  <p>
    HTML data attributes are case-insensitive, so any options which contain capital letters will be parsed as if they were all lowercase. Because Select2 has many options which are camelCase, where words are separated by uppercase letters, you must write these options out with dashes instead. So an option that would normally be called <code>allowClear</code> should instead be defined as <code>allow-clear</code>.
  </p>

  <p>
    This means that if you declare your <code>&lt;select&gt;</code> tag as...
  </p>

{% highlight html linenos %}
<select data-tags="true" data-placeholder="Select an option" data-allow-clear="true"></select>
{% endhighlight %}

  <p>
    Will be interpreted the same as initializing Select2 as...
  </p>

{% highlight js linenos %}
$("select").select2({
  tags: "true",
  placeholder: "Select an option",
  allowClear: true
});
{% endhighlight %}

  <h3>
    Are options with nested configurations supported?
  </h3>

  <p>
    You can also define nested configurations, which are typically needed for
    options such as AJAX. Each level of nesting should be separated by two
    dashes (<code>--</code>) instead of one. Due to
    <a href="https://github.com/jquery/jquery/issues/2070">a jQuery bug</a>,
    nested options using <code>data-*</code> attributes
    <a href="https://github.com/select2/select2/issues/2969">do not work in jQuery 1.x</a>.
  </p>

{% highlight html linenos %}
<select data-ajax--url="http://example.org/api/test" data-ajax--cache="true"></select>
{% endhighlight %}

  <p>
    Which will be interpreted the same as initializing Select2 with...
  </p>

{% highlight js linenos %}
$("select").select2({
  ajax: {
    url: "http://example.org/api/test",
    cache: true
  }
});
{% endhighlight %}

  <p>
    The value of the option is subject to jQuery's
    <a href="https://api.jquery.com/data/#data-html5">parsing rules</a> for
    HTML5 data attributes.
  </p>
</section>