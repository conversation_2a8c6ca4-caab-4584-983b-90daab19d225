<section>
  <h2 id="data-adapters-select-tag">
    Can Select2 be used with a <code>&lt;select&gt;</code> tag?
  </h2>

  <p>
    Select2 was designed to be a replacement for the standard <code>&lt;select&gt;</code> boxes that are displayed by the browser, so by default it supports all options and operations that are available in a standard select box, but with added flexibility. There is no special configuration required to make Select2 work with a <code>&lt;select&gt;</code> tag.
  </p>

  <h3>
    Does Select2 support nesting options?
  </h3>

  <p>
    A standard <code>&lt;select&gt;</code> box can display nested options by wrapping them with in an <code>&lt;optgroup&gt;</code> tag.
  </p>

{% highlight html linenos %}
<select>
  <optgroup label="Group Name">
    <option>Nested option</option>
  </optgroup>
</select>
{% endhighlight %}

  <h3>
    How many levels of nesting can there be?
  </h3>

  <p>
    Only a single level of nesting is allowed per the HTML specification. If you nest an <code>&lt;optgroup&gt;</code> within another <code>&lt;optgroup&gt;</code>, Select2 will not be able to detect the extra level of nesting and errors may be triggered as a result.
  </p>

  <h3>
    Can <code>&lt;optgroup&gt;</code> tags be made selectable?
  </h3>

  <p>
    No. This is a limitation of the HTML specification and is not a limitation that Select2 can overcome. You can emulate grouping by using an <code>&lt;option&gt;</code> instead of an <code>&lt;optgroup&gt;</code> and <a href="http://stackoverflow.com/q/30820215/359284#30948247">changing the style by using CSS</a>, but this is not recommended as it is not fully accessible.
  </p>

  <h3>
    How are <code>&lt;option&gt;</code> and <code>&lt;optgroup&gt;</code> tags serialized into data objects?
  </h3>

  <p>
    Select2 will convert the <code>&lt;option&gt;</code> tag into a data object based on the following rules.
  </p>

{% highlight js linenos %}
{
  "id": "value attribute" || "option text",
  "text": "label attribute" || "option text",
  "element": HTMLOptionElement
}
{% endhighlight %}

  <p>
    And <code>&lt;optgroup&gt;</code> tags will be converted into data objects using the following rules
  </p>

{% highlight js linenos %}
{
  "text": "label attribute",
  "children": [ option data object, ... ],
  "elment": HTMLOptGroupElement
}
{% endhighlight %}
</section>