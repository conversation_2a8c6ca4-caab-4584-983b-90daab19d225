<section>
  <h2 id="templateSelection">
    How can I customize the way selections are displayed?
  </h2>

  <p>
    When a selection is made by the user Select2 will display the text of the option by default, just like how it is displayed in a standard select box.  You can override the display of the selection by setting the <code>templateSelection</code> option to a JavaScript function.
  </p>

{% highlight js linenos %}
function template(data, container) {
  return data.text;
}

$('select').select2({
  templateSelection: template
});
{% endhighlight %}

  <h3>
    Nothing is being displayed when I select an option
  </h3>

  {% include options/not-written.html %}

  <h3>
    I am using HTML in my selection template but it isn't displaying it
  </h3>

  {% include options/not-written.html %}

  <h3>
    How can I access the container where the selection is displayed?
  </h3>

  {% include options/not-written.html %}
</section>