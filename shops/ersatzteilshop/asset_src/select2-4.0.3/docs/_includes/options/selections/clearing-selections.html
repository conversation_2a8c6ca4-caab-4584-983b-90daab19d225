<section>
  <h2 id="allowClear">
    Can I allow users to clear their selections?
  </h2>

  <p>
    You can allow people to clear their current selections with the <code>allowClear</code> option when initializing Select2. Setting this option to <code>true</code> will enable an "x" icon that will reset the selection to the placeholder.
  </p>

{% highlight js linenos %}
$('select').select2({
  placeholder: 'This is my placeholder',
  allowClear: true
});
{% endhighlight %}

  <h3>
    Why is a placeholder required?
  </h3>

  {% include options/not-written.html %}

  <h3>
    The "x" icon is not clearing the selection
  </h3>

  {% include options/not-written.html %}

  <h3>
    Can users remove all of their selections in a multiple select at once?
  </h3>

  {% include options/not-written.html %}
</section>