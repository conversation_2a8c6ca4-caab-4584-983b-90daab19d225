<?php

use bqp\db\db_generic;
use wws\Shop\ShopUrlRouter;

return function (ShopUrlRouter $router, db_generic $test) {
    $router->registerRoute('~^/product_device_list/~', [
        'action' => 'controller',
        'controller' => 'product',
        'command' => 'device_list'
    ]);
    $router->registerRoute('~^/(?P<code>[a-z0-9]{10})$~i', [
        'action' => 'controller',
        'controller' => 'shortener',
        'command' => 'redirect'
    ]);
    $router->registerController('/suche/', 'search');
    $router->registerController('/ersatzteile/geraete/', 'devices');
    $router->registerController('/kasse', 'order');
    $router->registerController('/meine-daten', 'customer');
    $router->registerController('/warenkorb', 'warenkorb');
    $router->registerRoute('~/rest/menu/~', [
        'action' => 'controller',
        'controller' => 'rest_api',
        'command' => 'menu'
    ]);
    $router->registerController('/rest/', 'rest_api');
    $router->registerController('/api/paypal/', 'paypal');
    $router->registerController('/tracking/', 'tracking');

    $router->registerRoute('~^/goodies/$~', [
        'action' => 'controller',
        'controller' => 'goodies',
        'command' => 'default'
    ]);

    $router->registerController('/service/', 'service');
    $router->registerController('/contact_form_ajax', 'contact_form_ajax');
    $router->registerRoute('~/energielabel/.*\.(?P<product_id>[0-9]{1,6})\.png~', [
        'action' => 'controller',
        'controller' => 'energielabel',
        'command' => 'show'
    ]);
    $router->registerRoute('~/product/shipment/~', [
        'action' => 'controller',
        'controller' => 'product',
        'command' => 'ajax_shipment'
    ]);
    $router->registerRoute('~/envkv/(?<product_url>.*)~', [
        'action' => 'controller',
        'controller' => 'envkv',
        'command' => 'redirect'
    ]);
    $router->registerRoute('~/gpsr/(?<product_url>.*)~', [
        'action' => 'controller',
        'controller' => 'gpsr',
        'command' => 'show'
    ]);
    $router->registerRoute('~/get_further_product_ratings/~', [
        'action' => 'controller',
        'controller' => 'product',
        'command' => 'get_further_product_ratings',
    ]);
};
