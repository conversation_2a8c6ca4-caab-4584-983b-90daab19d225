<?php

use wws\Shop\Basket\Basket;
use wws\Shop\Controller\ShopController;
use wws\Shop\ProductRating\DataProvider\ProductRatingDataProviderDb;
use wws\Shop\ProductRating\ProductRatingReadService;
use wws\Shop\ProductRating\ProductRatingWriteService;
use wws\Shop\Shipping\ShopShippingCostService;
use wws\Shop\ShopCategoryControllerFactory;
use wws\Shop\Basket\BasketRequestHandler;
use wws\Shop\Basket\BasketService;
use wws\Shop\Profile\ShopProfile;
use wws\Shop\Profile\ShopProfileAllego;
use wws\Shop\Tracking\ShopTrackerRequestHandler;

set_exception_handler(function (Throwable $e) {
    /** @noinspection ForgottenDebugOutputInspection */
    error_log('Exception: ' . $_SERVER['REQUEST_URI'] . ' Message: ' . $e->getMessage() . ' ' . debug::getExceptionAsText($e));

    header('HTTP/1.1 500 Internal Server Error');

    sleep(rand(1, 10)); //evtl. timing attacken

    if (!@readfile('../templates/seiten/error_500.html')) {
        echo 'Ein Fehler ist aufgetreten, versuchen Sie es später erneut.';
    }
});

//ob_start();

include(__DIR__.'/../shop.inc.php');

if (config::system('debug')) { //wenn im dev envoirment, exception handler deaktivieren
    restore_exception_handler();
    $db->setQueryLogStatus(true);
}

header('X-Powered-By: wws');
header('Content-Type: text/html; charset=UTF-8'); //das funktioniert nur durch PHP -> manche controller erzeugen auch Bilder mit image/*

if (isset($_SERVER['HTTPS'])) {
    header('Strict-Transport-Security: max-age=86400');
}

if (isset($_GET['reset'])) {
    session_destroy();
}

try {
    session_start();
} catch (Throwable $e) {
    //@todo
    session_reset();
}

if (array_key_exists('env-sgtm', $_GET)) {
    $_SESSION['env-sgtm'] = (bool)$_GET['env-sgtm'] ?: null;
}

$builder = new \DI\ContainerBuilder();

$builder->addDefinitions(__DIR__.'/../config/dependencies.php');
$builder->useAutowiring(true);

$container = $builder->build();
$container->set('shop_id', $_cfg['shop_id']);
$container->set('config', $_cfg);

$funktion = new funktionen($db);

//Mapping
$router = $container->get(\wws\Shop\ShopUrlRouter::class);
$router->setByPhpRequestUri($_SERVER['REQUEST_URI']);

$request = $router->getRequestData();

if (isset($request['redirect_permant'])) {
    $url = $request['redirect_permant'];

    $parameters = $_GET;
    unset($parameters['mapping']);
    unset($parameters['mapping_by_raw_request']);

    if ($parameters) {
        $url .= '?' . http_build_query($parameters);
    }

    header("HTTP/1.1 301 Moved Permanently");
    header("Location: " . $url);
    exit;
}

/* @var ShopProfileAllego $profile */
$profile = $funktion->getShopProfile($request);
$profile->setConfig($_cfg);
$profile->setContainer($container);

$container->set(ShopProfile::class, $profile);

$shop_cookie = $container->get(\wws\Shop\ShopCookie::class);
$shop_cookie->init();
if (isset($_GET['reset'])) {
    $shop_cookie->reset();
}

$tracker = $container->get(\wws\Shop\Tracking\ShopTracker::class);

if (isset($_GET['ga_debug'])) {
    $_SESSION['ga_debug'] = true;
}

$tracker->setDebug(isset($_SESSION['ga_debug']));

$ab_test_manger = $container->get(\wws\Shop\AbTest\AbTestManager::class);
$ab_test_manger->handleRequest();
$tracker->setAbTestManger($ab_test_manger);

$tpl_sys = $tpl = $container->get(\wws\Shop\ShopTemplate::class);

$tpl->assign('tracker', $tracker);

if (isset($request['url'])) {
    $tpl->setCanonicalUrl($request['url']);
}

if (isset($_GET['t'])) {
    $individual_pricing = $container->get(\wws\Shop\ShopIndividualPricing::class);
    $price_group_id = $individual_pricing->getIndividualPriceGroupId($_GET['t']);
    if ($price_group_id) {
        $shop_cookie->set('price_group', $price_group_id, 259200);
    }
}

//
if ($shop_cookie->has('price_group')) {
    $price_group_id = $shop_cookie->get('price_group');

    $profile->setActivePriceGroupId($price_group_id);
    $tracker->setPriceGroup('pg'.$price_group_id);
}

//------------------------------------//
// Warenkrob
//------------------------------------//
if (!isset($_SESSION['warenkorb'])) {
    $_SESSION['warenkorb'] = null;
}

$warenkorb = &$_SESSION['warenkorb'];

if (!($warenkorb instanceof Basket)) {
    $warenkorb = new Basket($_cfg['shop_id']);
    $warenkorb->setDstCountryId($_cfg['country_id']);
}

$warenkorb_service = new BasketService($funktion);
$warenkorb_service->setShopProfile($profile);
$warenkorb_service->setWarenkorb($warenkorb);
$warenkorb_service->setShopTracker($tracker);
$warenkorb_service->setShopCatalogLacy(function () use ($profile) {
    return $profile->getShopCatalog();
});
$warenkorb_service->setBasketCouponService($container->get(\wws\Shop\Basket\BasketCouponService::class));
$warenkorb_service->setShopShippingService($container->get(ShopShippingCostService::class));
$warenkorb_service->setShopPaymentServiceFactory($container->get(\wws\Shop\Payment\ShopPaymentServiceFactory::class));

$warenkorb_handler = new BasketRequestHandler($warenkorb_service, $warenkorb);
$warenkorb_request_handler_result = $warenkorb_handler->execute();

if ($warenkorb_request_handler_result->isServicesMessage()) {
    $tpl->assign('toast_status', 'warning');
    $tpl->assign('toast_message', $warenkorb_request_handler_result->getServicesMessage());
}

$tpl->assign('warenkorb', $warenkorb);
//------------------------------------//

//------------------------------------//
// user einstellungen
//------------------------------------//

$user_settings = new \wws\Shop\ShopUserSessionSettings();

//sortierung
if (empty($_SESSION['product_order'])) {
    $_SESSION['product_order'] = 1;
}
if (!empty($_REQUEST['set_product_order'])) {
    $order = (int)$_REQUEST['set_product_order'];

    if (isset($_cfg['product_order'][$order]['sql'])) {
        $_SESSION['product_order'] = $order;
    }
}

if (!isset($_SESSION['product_order'])) {
    $_SESSION['product_order'] = 1;
}

$tpl->assign('product_order', $_SESSION['product_order']);

//filterung
if (empty($_SESSION['product_filter'])) {
    $_SESSION['product_filter'] = [];
}

if (!empty($_REQUEST['set_product_filter'])) {
    //neuer filter
    foreach ($_REQUEST['set_product_filter'] as $filter_key => $value) {
        switch ($filter_key) {
            case 'max_preis':
                $max_preis = (int)$value;

                if ($max_preis) {
                    $_SESSION['product_filter']['max_preis'] = $max_preis;
                } else {
                    unset($_SESSION['product_filter']['max_preis']);
                }

                break;
            case 'min_preis':
                $min_preis = (int)$value;

                if ($min_preis) {
                    $_SESSION['product_filter']['min_preis'] = $min_preis;
                } else {
                    unset($_SESSION['product_filter']['min_preis']);
                }

                break;
        }
    }
}

if (isset($_REQUEST['set_entries_per_page'])) {
    $temp = (int)$_REQUEST['set_entries_per_page'];
    if (in_array($temp, config::shop('entries_per_page'))) {
        $_SESSION['entries_per_page'] = $temp;
    }
}

$_cfg['entries_per_page'] = isset($_SESSION['entries_per_page']) ? $_SESSION['entries_per_page'] : config::shop('detault_entries_per_page');
$tpl->assign('entries_per_page', $_cfg['entries_per_page']);
$tpl->assign('entries_per_page_select', config::shop('entries_per_page'));

$user_settings->setPriceMax(isset($_SESSION['product_filter']['max_preis']) ? $_SESSION['product_filter']['max_preis'] : null);
$user_settings->setPriceMin(isset($_SESSION['product_filter']['min_preis']) ? $_SESSION['product_filter']['min_preis'] : null);
$user_settings->setEntriesPerPage($_cfg['entries_per_page']);
$user_settings->setEntriesOrder($_SESSION['product_order']);


$tracking_request_handler = new ShopTrackerRequestHandler($tracker, $db);
$tracking_request_handler->handle($request);

$funktion->sideBar(-1);
$profile->getBreadcrumb()->add('Home', url('/'));

$container->get(\wws\Shop\ShopCookie::class)->sendCookie();

switch ($request['page']) {
    case 'controller':
        $data_provider = new \wws\Shop\ProductFeaturePresets\FeaturePresetsDataProviderDb($db);
        if ($profile->isCaching()) {
            $data_provider->setCaching(true);
        }

        $feature_preset_service = new \wws\Shop\ProductFeaturePresets\FeaturePresetService($data_provider);

        if ($request['controller'] === 'category') {
            $factory = new ShopCategoryControllerFactory($profile->getShopDataService(), $feature_preset_service);
            $controller = $factory->createController($request);
        } else {
            $controller = ShopController::executeRequest($request);
        }

        $controller->setDb($db);
        $controller->setWarenkorb($warenkorb);
        $controller->setShopProfile($profile);
        $controller->setTplSys($tpl);
        $controller->setFunktion($funktion);
        $controller->setTracker($tracker);
        $controller->setUserSessionSettings($user_settings);
        $controller->setShopBreadcrumbs($profile->getBreadcrumb());

        if (method_exists($controller, 'setCatalog')) {
            $controller->setCatalog($profile->getShopCatalog());
        }

        if (method_exists($controller, 'setWarenkorbRequestHandlerResult')) {
            $controller->setWarenkorbRequestHandlerResult($warenkorb_request_handler_result);
        }

        if (method_exists($controller, 'setWarenkorbService')) {
            $controller->setWarenkorbService($warenkorb_service);
        }

        if (method_exists($controller, 'setCheckoutValidator')) {
            $checkout_validator = new \wws\Shop\Checkout\CheckoutValidator();
            $controller->setCheckoutValidator($checkout_validator);
        }

        if (method_exists($controller, 'setIsBadBot')) {
            $controller->setIsBadBot($profile->isBadBot());
        }

        if (method_exists($controller, 'setFeaturePresetService')) {
            $controller->setFeaturePresetService($feature_preset_service);
        }

        if (method_exists($controller, 'inject')) {
            $container->call([$controller, 'inject']);
        }

        //teilweise wurden traits verwendet um funktionalität zentrall zu haben... "notlösung" um nicht inject auszuhebeln
        if (method_exists($controller, 'traitInject')) {
            $container->call([$controller, 'traitInject']);
        }

        if (method_exists($controller, 'setProductRatingService')) {
            $product_rating_data_provider = new ProductRatingDataProviderDb($db, $profile->getShopId());
            $product_rating_service = new ProductRatingReadService($product_rating_data_provider);
            $controller->setProductRatingService($product_rating_service);
        }

        if (method_exists($controller, 'setProductRatingWriteService')) {
            $product_rating_write_service = new ProductRatingWriteService();
            $controller->setProductRatingWriteService($product_rating_write_service);
        }

        $controller->run();
        break;
    case '404':
    default:
        error_log('Content does not exist: ' . $_SERVER['REQUEST_URI']);
        header("HTTP/1.1 404 Not Found");
        $tpl->display('pages/landing/404.tpl');
        break;
}

ob_end_flush();
flush();

$tracker->fireEvent('end');

session_write_close();

if (config::system('debug')) {
    //$db->displayQueryLog();
}
