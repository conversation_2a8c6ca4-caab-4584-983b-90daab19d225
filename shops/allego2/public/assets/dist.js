/*! For license information please see dist.js.LICENSE */
!function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(e,t,n){var i;!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,(function(n,o){"use strict";var r=[],a=Object.getPrototypeOf,s=r.slice,l=r.flat?function(e){return r.flat.call(e)}:function(e){return r.concat.apply([],e)},c=r.push,u=r.indexOf,d={},f=d.toString,h=d.hasOwnProperty,p=h.toString,g=p.call(Object),m={},v=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},y=function(e){return null!=e&&e===e.window},b=n.document,w={type:!0,src:!0,nonce:!0,noModule:!0};function x(e,t,n){var i,o,r=(n=n||b).createElement("script");if(r.text=e,t)for(i in w)(o=t[i]||t.getAttribute&&t.getAttribute(i))&&r.setAttribute(i,o);n.head.appendChild(r).parentNode.removeChild(r)}function E(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?d[f.call(e)]||"object":typeof e}var S=function(e,t){return new S.fn.init(e,t)};function L(e){var t=!!e&&"length"in e&&e.length,n=E(e);return!v(e)&&!y(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}S.fn=S.prototype={jquery:"3.6.0",constructor:S,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(e){return this.pushStack(S.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,(function(e,t){return(t+1)%2})))},odd:function(){return this.pushStack(S.grep(this,(function(e,t){return t%2})))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:r.sort,splice:r.splice},S.extend=S.fn.extend=function(){var e,t,n,i,o,r,a=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||v(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(e=arguments[s]))for(t in e)i=e[t],"__proto__"!==t&&a!==i&&(c&&i&&(S.isPlainObject(i)||(o=Array.isArray(i)))?(n=a[t],r=o&&!Array.isArray(n)?[]:o||S.isPlainObject(n)?n:{},o=!1,a[t]=S.extend(c,r,i)):void 0!==i&&(a[t]=i));return a},S.extend({expando:"jQuery"+("3.6.0"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==f.call(e))&&(!(t=a(e))||"function"==typeof(n=h.call(t,"constructor")&&t.constructor)&&p.call(n)===g)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){x(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(L(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},makeArray:function(e,t){var n=t||[];return null!=e&&(L(Object(e))?S.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,o=e.length;i<n;i++)e[o++]=t[i];return e.length=o,e},grep:function(e,t,n){for(var i=[],o=0,r=e.length,a=!n;o<r;o++)!t(e[o],o)!==a&&i.push(e[o]);return i},map:function(e,t,n){var i,o,r=0,a=[];if(L(e))for(i=e.length;r<i;r++)null!=(o=t(e[r],r,n))&&a.push(o);else for(r in e)null!=(o=t(e[r],r,n))&&a.push(o);return l(a)},guid:1,support:m}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=r[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){d["[object "+t+"]"]=t.toLowerCase()}));var T=function(e){var t,n,i,o,r,a,s,l,c,u,d,f,h,p,g,m,v,y,b,w="sizzle"+1*new Date,x=e.document,E=0,S=0,L=le(),T=le(),C=le(),A=le(),M=function(e,t){return e===t&&(d=!0),0},k={}.hasOwnProperty,_=[],P=_.pop,O=_.push,j=_.push,q=_.slice,R=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},I="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",D="[\\x20\\t\\r\\n\\f]",F="(?:\\\\[\\da-fA-F]{1,6}"+D+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",N="\\["+D+"*("+F+")(?:"+D+"*([*^$|!~]?=)"+D+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+F+"))|)"+D+"*\\]",H=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+N+")*)|.*)\\)|)",B=new RegExp(D+"+","g"),z=new RegExp("^"+D+"+|((?:^|[^\\\\])(?:\\\\.)*)"+D+"+$","g"),$=new RegExp("^"+D+"*,"+D+"*"),W=new RegExp("^"+D+"*([>+~]|"+D+")"+D+"*"),V=new RegExp(D+"|>"),U=new RegExp(H),X=new RegExp("^"+F+"$"),Y={ID:new RegExp("^#("+F+")"),CLASS:new RegExp("^\\.("+F+")"),TAG:new RegExp("^("+F+"|[*])"),ATTR:new RegExp("^"+N),PSEUDO:new RegExp("^"+H),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+D+"*(even|odd|(([+-]|)(\\d*)n|)"+D+"*(?:([+-]|)"+D+"*(\\d+)|))"+D+"*\\)|)","i"),bool:new RegExp("^(?:"+I+")$","i"),needsContext:new RegExp("^"+D+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+D+"*((?:-\\d)?\\d*)"+D+"*\\)|)(?=[^-]|$)","i")},G=/HTML$/i,Z=/^(?:input|select|textarea|button)$/i,Q=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\[\\da-fA-F]{1,6}"+D+"?|\\\\([^\\r\\n\\f])","g"),ne=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},ie=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},re=function(){f()},ae=we((function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()}),{dir:"parentNode",next:"legend"});try{j.apply(_=q.call(x.childNodes),x.childNodes),_[x.childNodes.length].nodeType}catch(e){j={apply:_.length?function(e,t){O.apply(e,q.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function se(e,t,i,o){var r,s,c,u,d,p,v,y=t&&t.ownerDocument,x=t?t.nodeType:9;if(i=i||[],"string"!=typeof e||!e||1!==x&&9!==x&&11!==x)return i;if(!o&&(f(t),t=t||h,g)){if(11!==x&&(d=J.exec(e)))if(r=d[1]){if(9===x){if(!(c=t.getElementById(r)))return i;if(c.id===r)return i.push(c),i}else if(y&&(c=y.getElementById(r))&&b(t,c)&&c.id===r)return i.push(c),i}else{if(d[2])return j.apply(i,t.getElementsByTagName(e)),i;if((r=d[3])&&n.getElementsByClassName&&t.getElementsByClassName)return j.apply(i,t.getElementsByClassName(r)),i}if(n.qsa&&!A[e+" "]&&(!m||!m.test(e))&&(1!==x||"object"!==t.nodeName.toLowerCase())){if(v=e,y=t,1===x&&(V.test(e)||W.test(e))){for((y=ee.test(e)&&ve(t.parentNode)||t)===t&&n.scope||((u=t.getAttribute("id"))?u=u.replace(ie,oe):t.setAttribute("id",u=w)),s=(p=a(e)).length;s--;)p[s]=(u?"#"+u:":scope")+" "+be(p[s]);v=p.join(",")}try{return j.apply(i,y.querySelectorAll(v)),i}catch(t){A(e,!0)}finally{u===w&&t.removeAttribute("id")}}}return l(e.replace(z,"$1"),t,i,o)}function le(){var e=[];return function t(n,o){return e.push(n+" ")>i.cacheLength&&delete t[e.shift()],t[n+" "]=o}}function ce(e){return e[w]=!0,e}function ue(e){var t=h.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function de(e,t){for(var n=e.split("|"),o=n.length;o--;)i.attrHandle[n[o]]=t}function fe(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function he(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function pe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function ge(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&ae(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function me(e){return ce((function(t){return t=+t,ce((function(n,i){for(var o,r=e([],n.length,t),a=r.length;a--;)n[o=r[a]]&&(n[o]=!(i[o]=n[o]))}))}))}function ve(e){return e&&void 0!==e.getElementsByTagName&&e}for(t in n=se.support={},r=se.isXML=function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!G.test(t||n&&n.nodeName||"HTML")},f=se.setDocument=function(e){var t,o,a=e?e.ownerDocument||e:x;return a!=h&&9===a.nodeType&&a.documentElement?(p=(h=a).documentElement,g=!r(h),x!=h&&(o=h.defaultView)&&o.top!==o&&(o.addEventListener?o.addEventListener("unload",re,!1):o.attachEvent&&o.attachEvent("onunload",re)),n.scope=ue((function(e){return p.appendChild(e).appendChild(h.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length})),n.attributes=ue((function(e){return e.className="i",!e.getAttribute("className")})),n.getElementsByTagName=ue((function(e){return e.appendChild(h.createComment("")),!e.getElementsByTagName("*").length})),n.getElementsByClassName=K.test(h.getElementsByClassName),n.getById=ue((function(e){return p.appendChild(e).id=w,!h.getElementsByName||!h.getElementsByName(w).length})),n.getById?(i.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}},i.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var n=t.getElementById(e);return n?[n]:[]}}):(i.filter.ID=function(e){var t=e.replace(te,ne);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},i.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var n,i,o,r=t.getElementById(e);if(r){if((n=r.getAttributeNode("id"))&&n.value===e)return[r];for(o=t.getElementsByName(e),i=0;r=o[i++];)if((n=r.getAttributeNode("id"))&&n.value===e)return[r]}return[]}}),i.find.TAG=n.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],o=0,r=t.getElementsByTagName(e);if("*"===e){for(;n=r[o++];)1===n.nodeType&&i.push(n);return i}return r},i.find.CLASS=n.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&g)return t.getElementsByClassName(e)},v=[],m=[],(n.qsa=K.test(h.querySelectorAll))&&(ue((function(e){var t;p.appendChild(e).innerHTML="<a id='"+w+"'></a><select id='"+w+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+D+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+D+"*(?:value|"+I+")"),e.querySelectorAll("[id~="+w+"-]").length||m.push("~="),(t=h.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+D+"*name"+D+"*="+D+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+w+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")})),ue((function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=h.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+D+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),p.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")}))),(n.matchesSelector=K.test(y=p.matches||p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ue((function(e){n.disconnectedMatch=y.call(e,"*"),y.call(e,"[s!='']:x"),v.push("!=",H)})),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),t=K.test(p.compareDocumentPosition),b=t||K.test(p.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},M=t?function(e,t){if(e===t)return d=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i||(1&(i=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!n.sortDetached&&t.compareDocumentPosition(e)===i?e==h||e.ownerDocument==x&&b(x,e)?-1:t==h||t.ownerDocument==x&&b(x,t)?1:u?R(u,e)-R(u,t):0:4&i?-1:1)}:function(e,t){if(e===t)return d=!0,0;var n,i=0,o=e.parentNode,r=t.parentNode,a=[e],s=[t];if(!o||!r)return e==h?-1:t==h?1:o?-1:r?1:u?R(u,e)-R(u,t):0;if(o===r)return fe(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[i]===s[i];)i++;return i?fe(a[i],s[i]):a[i]==x?-1:s[i]==x?1:0},h):h},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(f(e),n.matchesSelector&&g&&!A[t+" "]&&(!v||!v.test(t))&&(!m||!m.test(t)))try{var i=y.call(e,t);if(i||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(e){A(t,!0)}return se(t,h,null,[e]).length>0},se.contains=function(e,t){return(e.ownerDocument||e)!=h&&f(e),b(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=h&&f(e);var o=i.attrHandle[t.toLowerCase()],r=o&&k.call(i.attrHandle,t.toLowerCase())?o(e,t,!g):void 0;return void 0!==r?r:n.attributes||!g?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},se.escape=function(e){return(e+"").replace(ie,oe)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,i=[],o=0,r=0;if(d=!n.detectDuplicates,u=!n.sortStable&&e.slice(0),e.sort(M),d){for(;t=e[r++];)t===e[r]&&(o=i.push(r));for(;o--;)e.splice(i[o],1)}return u=null,e},o=se.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=o(t);return n},(i=se.selectors={cacheLength:50,createPseudo:ce,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Y.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&U.test(n)&&(t=a(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=L[e+" "];return t||(t=new RegExp("(^|"+D+")"+e+"("+D+"|$)"))&&L(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(i){var o=se.attr(i,e);return null==o?"!="===t:!t||(o+="","="===t?o===n:"!="===t?o!==n:"^="===t?n&&0===o.indexOf(n):"*="===t?n&&o.indexOf(n)>-1:"$="===t?n&&o.slice(-n.length)===n:"~="===t?(" "+o.replace(B," ")+" ").indexOf(n)>-1:"|="===t&&(o===n||o.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,i,o){var r="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===o?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,f,h,p,g=r!==a?"nextSibling":"previousSibling",m=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!l&&!s,b=!1;if(m){if(r){for(;g;){for(f=t;f=f[g];)if(s?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;p=g="only"===e&&!p&&"nextSibling"}return!0}if(p=[a?m.firstChild:m.lastChild],a&&y){for(b=(h=(c=(u=(d=(f=m)[w]||(f[w]={}))[f.uniqueID]||(d[f.uniqueID]={}))[e]||[])[0]===E&&c[1])&&c[2],f=h&&m.childNodes[h];f=++h&&f&&f[g]||(b=h=0)||p.pop();)if(1===f.nodeType&&++b&&f===t){u[e]=[E,h,b];break}}else if(y&&(b=h=(c=(u=(d=(f=t)[w]||(f[w]={}))[f.uniqueID]||(d[f.uniqueID]={}))[e]||[])[0]===E&&c[1]),!1===b)for(;(f=++h&&f&&f[g]||(b=h=0)||p.pop())&&((s?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++b||(y&&((u=(d=f[w]||(f[w]={}))[f.uniqueID]||(d[f.uniqueID]={}))[e]=[E,b]),f!==t)););return(b-=o)===i||b%i==0&&b/i>=0}}},PSEUDO:function(e,t){var n,o=i.pseudos[e]||i.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return o[w]?o(t):o.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?ce((function(e,n){for(var i,r=o(e,t),a=r.length;a--;)e[i=R(e,r[a])]=!(n[i]=r[a])})):function(e){return o(e,0,n)}):o}},pseudos:{not:ce((function(e){var t=[],n=[],i=s(e.replace(z,"$1"));return i[w]?ce((function(e,t,n,o){for(var r,a=i(e,null,o,[]),s=e.length;s--;)(r=a[s])&&(e[s]=!(t[s]=r))})):function(e,o,r){return t[0]=e,i(t,null,r,n),t[0]=null,!n.pop()}})),has:ce((function(e){return function(t){return se(e,t).length>0}})),contains:ce((function(e){return e=e.replace(te,ne),function(t){return(t.textContent||o(t)).indexOf(e)>-1}})),lang:ce((function(e){return X.test(e||"")||se.error("unsupported lang: "+e),e=e.replace(te,ne).toLowerCase(),function(t){var n;do{if(n=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!i.pseudos.empty(e)},header:function(e){return Q.test(e.nodeName)},input:function(e){return Z.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:me((function(){return[0]})),last:me((function(e,t){return[t-1]})),eq:me((function(e,t,n){return[n<0?n+t:n]})),even:me((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:me((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:me((function(e,t,n){for(var i=n<0?n+t:n>t?t:n;--i>=0;)e.push(i);return e})),gt:me((function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e}))}}).pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[t]=he(t);for(t in{submit:!0,reset:!0})i.pseudos[t]=pe(t);function ye(){}function be(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function we(e,t,n){var i=t.dir,o=t.next,r=o||i,a=n&&"parentNode"===r,s=S++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||a)return e(t,n,o);return!1}:function(t,n,l){var c,u,d,f=[E,s];if(l){for(;t=t[i];)if((1===t.nodeType||a)&&e(t,n,l))return!0}else for(;t=t[i];)if(1===t.nodeType||a)if(u=(d=t[w]||(t[w]={}))[t.uniqueID]||(d[t.uniqueID]={}),o&&o===t.nodeName.toLowerCase())t=t[i]||t;else{if((c=u[r])&&c[0]===E&&c[1]===s)return f[2]=c[2];if(u[r]=f,f[2]=e(t,n,l))return!0}return!1}}function xe(e){return e.length>1?function(t,n,i){for(var o=e.length;o--;)if(!e[o](t,n,i))return!1;return!0}:e[0]}function Ee(e,t,n,i,o){for(var r,a=[],s=0,l=e.length,c=null!=t;s<l;s++)(r=e[s])&&(n&&!n(r,i,o)||(a.push(r),c&&t.push(s)));return a}function Se(e,t,n,i,o,r){return i&&!i[w]&&(i=Se(i)),o&&!o[w]&&(o=Se(o,r)),ce((function(r,a,s,l){var c,u,d,f=[],h=[],p=a.length,g=r||function(e,t,n){for(var i=0,o=t.length;i<o;i++)se(e,t[i],n);return n}(t||"*",s.nodeType?[s]:s,[]),m=!e||!r&&t?g:Ee(g,f,e,s,l),v=n?o||(r?e:p||i)?[]:a:m;if(n&&n(m,v,s,l),i)for(c=Ee(v,h),i(c,[],s,l),u=c.length;u--;)(d=c[u])&&(v[h[u]]=!(m[h[u]]=d));if(r){if(o||e){if(o){for(c=[],u=v.length;u--;)(d=v[u])&&c.push(m[u]=d);o(null,v=[],c,l)}for(u=v.length;u--;)(d=v[u])&&(c=o?R(r,d):f[u])>-1&&(r[c]=!(a[c]=d))}}else v=Ee(v===a?v.splice(p,v.length):v),o?o(null,a,v,l):j.apply(a,v)}))}function Le(e){for(var t,n,o,r=e.length,a=i.relative[e[0].type],s=a||i.relative[" "],l=a?1:0,u=we((function(e){return e===t}),s,!0),d=we((function(e){return R(t,e)>-1}),s,!0),f=[function(e,n,i){var o=!a&&(i||n!==c)||((t=n).nodeType?u(e,n,i):d(e,n,i));return t=null,o}];l<r;l++)if(n=i.relative[e[l].type])f=[we(xe(f),n)];else{if((n=i.filter[e[l].type].apply(null,e[l].matches))[w]){for(o=++l;o<r&&!i.relative[e[o].type];o++);return Se(l>1&&xe(f),l>1&&be(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(z,"$1"),n,l<o&&Le(e.slice(l,o)),o<r&&Le(e=e.slice(o)),o<r&&be(e))}f.push(n)}return xe(f)}return ye.prototype=i.filters=i.pseudos,i.setFilters=new ye,a=se.tokenize=function(e,t){var n,o,r,a,s,l,c,u=T[e+" "];if(u)return t?0:u.slice(0);for(s=e,l=[],c=i.preFilter;s;){for(a in n&&!(o=$.exec(s))||(o&&(s=s.slice(o[0].length)||s),l.push(r=[])),n=!1,(o=W.exec(s))&&(n=o.shift(),r.push({value:n,type:o[0].replace(z," ")}),s=s.slice(n.length)),i.filter)!(o=Y[a].exec(s))||c[a]&&!(o=c[a](o))||(n=o.shift(),r.push({value:n,type:a,matches:o}),s=s.slice(n.length));if(!n)break}return t?s.length:s?se.error(e):T(e,l).slice(0)},s=se.compile=function(e,t){var n,o=[],r=[],s=C[e+" "];if(!s){for(t||(t=a(e)),n=t.length;n--;)(s=Le(t[n]))[w]?o.push(s):r.push(s);(s=C(e,function(e,t){var n=t.length>0,o=e.length>0,r=function(r,a,s,l,u){var d,p,m,v=0,y="0",b=r&&[],w=[],x=c,S=r||o&&i.find.TAG("*",u),L=E+=null==x?1:Math.random()||.1,T=S.length;for(u&&(c=a==h||a||u);y!==T&&null!=(d=S[y]);y++){if(o&&d){for(p=0,a||d.ownerDocument==h||(f(d),s=!g);m=e[p++];)if(m(d,a||h,s)){l.push(d);break}u&&(E=L)}n&&((d=!m&&d)&&v--,r&&b.push(d))}if(v+=y,n&&y!==v){for(p=0;m=t[p++];)m(b,w,a,s);if(r){if(v>0)for(;y--;)b[y]||w[y]||(w[y]=P.call(l));w=Ee(w)}j.apply(l,w),u&&!r&&w.length>0&&v+t.length>1&&se.uniqueSort(l)}return u&&(E=L,c=x),b};return n?ce(r):r}(r,o))).selector=e}return s},l=se.select=function(e,t,n,o){var r,l,c,u,d,f="function"==typeof e&&e,h=!o&&a(e=f.selector||e);if(n=n||[],1===h.length){if((l=h[0]=h[0].slice(0)).length>2&&"ID"===(c=l[0]).type&&9===t.nodeType&&g&&i.relative[l[1].type]){if(!(t=(i.find.ID(c.matches[0].replace(te,ne),t)||[])[0]))return n;f&&(t=t.parentNode),e=e.slice(l.shift().value.length)}for(r=Y.needsContext.test(e)?0:l.length;r--&&(c=l[r],!i.relative[u=c.type]);)if((d=i.find[u])&&(o=d(c.matches[0].replace(te,ne),ee.test(l[0].type)&&ve(t.parentNode)||t))){if(l.splice(r,1),!(e=o.length&&be(l)))return j.apply(n,o),n;break}}return(f||s(e,h))(o,t,!g,n,!t||ee.test(e)&&ve(t.parentNode)||t),n},n.sortStable=w.split("").sort(M).join("")===w,n.detectDuplicates=!!d,f(),n.sortDetached=ue((function(e){return 1&e.compareDocumentPosition(h.createElement("fieldset"))})),ue((function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")}))||de("type|href|height|width",(function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)})),n.attributes&&ue((function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")}))||de("value",(function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue})),ue((function(e){return null==e.getAttribute("disabled")}))||de(I,(function(e,t,n){var i;if(!n)return!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null})),se}(n);S.find=T,S.expr=T.selectors,S.expr[":"]=S.expr.pseudos,S.uniqueSort=S.unique=T.uniqueSort,S.text=T.getText,S.isXMLDoc=T.isXML,S.contains=T.contains,S.escapeSelector=T.escape;var C=function(e,t,n){for(var i=[],o=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&S(e).is(n))break;i.push(e)}return i},A=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},M=S.expr.match.needsContext;function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var _=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function P(e,t,n){return v(t)?S.grep(e,(function(e,i){return!!t.call(e,i,e)!==n})):t.nodeType?S.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?S.grep(e,(function(e){return u.call(t,e)>-1!==n})):S.filter(t,e,n)}S.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?S.find.matchesSelector(i,e)?[i]:[]:S.find.matches(e,S.grep(t,(function(e){return 1===e.nodeType})))},S.fn.extend({find:function(e){var t,n,i=this.length,o=this;if("string"!=typeof e)return this.pushStack(S(e).filter((function(){for(t=0;t<i;t++)if(S.contains(o[t],this))return!0})));for(n=this.pushStack([]),t=0;t<i;t++)S.find(e,o[t],n);return i>1?S.uniqueSort(n):n},filter:function(e){return this.pushStack(P(this,e||[],!1))},not:function(e){return this.pushStack(P(this,e||[],!0))},is:function(e){return!!P(this,"string"==typeof e&&M.test(e)?S(e):e||[],!1).length}});var O,j=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var i,o;if(!e)return this;if(n=n||O,"string"==typeof e){if(!(i="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:j.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),_.test(i[1])&&S.isPlainObject(t))for(i in t)v(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(o=b.getElementById(i[2]))&&(this[0]=o,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this)}).prototype=S.fn,O=S(b);var q=/^(?:parents|prev(?:Until|All))/,R={children:!0,contents:!0,next:!0,prev:!0};function I(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0}))},closest:function(e,t){var n,i=0,o=this.length,r=[],a="string"!=typeof e&&S(e);if(!M.test(e))for(;i<o;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&S.find.matchesSelector(n,e))){r.push(n);break}return this.pushStack(r.length>1?S.uniqueSort(r):r)},index:function(e){return e?"string"==typeof e?u.call(S(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return C(e,"parentNode")},parentsUntil:function(e,t,n){return C(e,"parentNode",n)},next:function(e){return I(e,"nextSibling")},prev:function(e){return I(e,"previousSibling")},nextAll:function(e){return C(e,"nextSibling")},prevAll:function(e){return C(e,"previousSibling")},nextUntil:function(e,t,n){return C(e,"nextSibling",n)},prevUntil:function(e,t,n){return C(e,"previousSibling",n)},siblings:function(e){return A((e.parentNode||{}).firstChild,e)},children:function(e){return A(e.firstChild)},contents:function(e){return null!=e.contentDocument&&a(e.contentDocument)?e.contentDocument:(k(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},(function(e,t){S.fn[e]=function(n,i){var o=S.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(o=S.filter(i,o)),this.length>1&&(R[e]||S.uniqueSort(o),q.test(e)&&o.reverse()),this.pushStack(o)}}));var D=/[^\x20\t\r\n\f]+/g;function F(e){return e}function N(e){throw e}function H(e,t,n,i){var o;try{e&&v(o=e.promise)?o.call(e).done(t).fail(n):e&&v(o=e.then)?o.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return S.each(e.match(D)||[],(function(e,n){t[n]=!0})),t}(e):S.extend({},e);var t,n,i,o,r=[],a=[],s=-1,l=function(){for(o=o||e.once,i=t=!0;a.length;s=-1)for(n=a.shift();++s<r.length;)!1===r[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=r.length,n=!1);e.memory||(n=!1),t=!1,o&&(r=n?[]:"")},c={add:function(){return r&&(n&&!t&&(s=r.length-1,a.push(n)),function t(n){S.each(n,(function(n,i){v(i)?e.unique&&c.has(i)||r.push(i):i&&i.length&&"string"!==E(i)&&t(i)}))}(arguments),n&&!t&&l()),this},remove:function(){return S.each(arguments,(function(e,t){for(var n;(n=S.inArray(t,r,n))>-1;)r.splice(n,1),n<=s&&s--})),this},has:function(e){return e?S.inArray(e,r)>-1:r.length>0},empty:function(){return r&&(r=[]),this},disable:function(){return o=a=[],r=n="",this},disabled:function(){return!r},lock:function(){return o=a=[],n||t||(r=n=""),this},locked:function(){return!!o},fireWith:function(e,n){return o||(n=[e,(n=n||[]).slice?n.slice():n],a.push(n),t||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!i}};return c},S.extend({Deferred:function(e){var t=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],i="pending",o={state:function(){return i},always:function(){return r.done(arguments).fail(arguments),this},catch:function(e){return o.then(null,e)},pipe:function(){var e=arguments;return S.Deferred((function(n){S.each(t,(function(t,i){var o=v(e[i[4]])&&e[i[4]];r[i[1]]((function(){var e=o&&o.apply(this,arguments);e&&v(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[i[0]+"With"](this,o?[e]:arguments)}))})),e=null})).promise()},then:function(e,i,o){var r=0;function a(e,t,i,o){return function(){var s=this,l=arguments,c=function(){var n,c;if(!(e<r)){if((n=i.apply(s,l))===t.promise())throw new TypeError("Thenable self-resolution");c=n&&("object"==typeof n||"function"==typeof n)&&n.then,v(c)?o?c.call(n,a(r,t,F,o),a(r,t,N,o)):(r++,c.call(n,a(r,t,F,o),a(r,t,N,o),a(r,t,F,t.notifyWith))):(i!==F&&(s=void 0,l=[n]),(o||t.resolveWith)(s,l))}},u=o?c:function(){try{c()}catch(n){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(n,u.stackTrace),e+1>=r&&(i!==N&&(s=void 0,l=[n]),t.rejectWith(s,l))}};e?u():(S.Deferred.getStackHook&&(u.stackTrace=S.Deferred.getStackHook()),n.setTimeout(u))}}return S.Deferred((function(n){t[0][3].add(a(0,n,v(o)?o:F,n.notifyWith)),t[1][3].add(a(0,n,v(e)?e:F)),t[2][3].add(a(0,n,v(i)?i:N))})).promise()},promise:function(e){return null!=e?S.extend(e,o):o}},r={};return S.each(t,(function(e,n){var a=n[2],s=n[5];o[n[1]]=a.add,s&&a.add((function(){i=s}),t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),a.add(n[3].fire),r[n[0]]=function(){return r[n[0]+"With"](this===r?void 0:this,arguments),this},r[n[0]+"With"]=a.fireWith})),o.promise(r),e&&e.call(r,r),r},when:function(e){var t=arguments.length,n=t,i=Array(n),o=s.call(arguments),r=S.Deferred(),a=function(e){return function(n){i[e]=this,o[e]=arguments.length>1?s.call(arguments):n,--t||r.resolveWith(i,o)}};if(t<=1&&(H(e,r.done(a(n)).resolve,r.reject,!t),"pending"===r.state()||v(o[n]&&o[n].then)))return r.then();for(;n--;)H(o[n],a(n),r.reject);return r.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){n.console&&n.console.warn&&e&&B.test(e.name)&&n.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){n.setTimeout((function(){throw e}))};var z=S.Deferred();function $(){b.removeEventListener("DOMContentLoaded",$),n.removeEventListener("load",$),S.ready()}S.fn.ready=function(e){return z.then(e).catch((function(e){S.readyException(e)})),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0,!0!==e&&--S.readyWait>0||z.resolveWith(b,[S]))}}),S.ready.then=z.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?n.setTimeout(S.ready):(b.addEventListener("DOMContentLoaded",$),n.addEventListener("load",$));var W=function(e,t,n,i,o,r,a){var s=0,l=e.length,c=null==n;if("object"===E(n))for(s in o=!0,n)W(e,t,s,n[s],!0,r,a);else if(void 0!==i&&(o=!0,v(i)||(a=!0),c&&(a?(t.call(e,i),t=null):(c=t,t=function(e,t,n){return c.call(S(e),n)})),t))for(;s<l;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return o?e:c?t.call(e):l?t(e[0],n):r},V=/^-ms-/,U=/-([a-z])/g;function X(e,t){return t.toUpperCase()}function Y(e){return e.replace(V,"ms-").replace(U,X)}var G=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function Z(){this.expando=S.expando+Z.uid++}Z.uid=1,Z.prototype={cache:function(e){var t=e[this.expando];return t||(t={},G(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,o=this.cache(e);if("string"==typeof t)o[Y(t)]=n;else for(i in t)o[Y(i)]=t[i];return o},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][Y(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(Y):(t=Y(t))in i?[t]:t.match(D)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||S.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var Q=new Z,K=new Z,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ee=/[A-Z]/g;function te(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(ee,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:J.test(e)?JSON.parse(e):e)}(n)}catch(e){}K.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return K.hasData(e)||Q.hasData(e)},data:function(e,t,n){return K.access(e,t,n)},removeData:function(e,t){K.remove(e,t)},_data:function(e,t,n){return Q.access(e,t,n)},_removeData:function(e,t){Q.remove(e,t)}}),S.fn.extend({data:function(e,t){var n,i,o,r=this[0],a=r&&r.attributes;if(void 0===e){if(this.length&&(o=K.get(r),1===r.nodeType&&!Q.get(r,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(i=a[n].name).indexOf("data-")&&(i=Y(i.slice(5)),te(r,i,o[i]));Q.set(r,"hasDataAttrs",!0)}return o}return"object"==typeof e?this.each((function(){K.set(this,e)})):W(this,(function(t){var n;if(r&&void 0===t)return void 0!==(n=K.get(r,e))||void 0!==(n=te(r,e))?n:void 0;this.each((function(){K.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){K.remove(this,e)}))}}),S.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=Q.get(e,t),n&&(!i||Array.isArray(n)?i=Q.access(e,t,S.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),i=n.length,o=n.shift(),r=S._queueHooks(e,t);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===t&&n.unshift("inprogress"),delete r.stop,o.call(e,(function(){S.dequeue(e,t)}),r)),!i&&r&&r.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Q.get(e,n)||Q.access(e,n,{empty:S.Callbacks("once memory").add((function(){Q.remove(e,[t+"queue",n])}))})}}),S.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?S.queue(this[0],e):void 0===t?this:this.each((function(){var n=S.queue(this,e,t);S._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&S.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){S.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,o=S.Deferred(),r=this,a=this.length,s=function(){--i||o.resolveWith(r,[r])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=Q.get(r[a],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),o.promise(t)}});var ne=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ie=new RegExp("^(?:([+-])=|)("+ne+")([a-z%]*)$","i"),oe=["Top","Right","Bottom","Left"],re=b.documentElement,ae=function(e){return S.contains(e.ownerDocument,e)},se={composed:!0};re.getRootNode&&(ae=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(se)===e.ownerDocument});var le=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ae(e)&&"none"===S.css(e,"display")};function ce(e,t,n,i){var o,r,a=20,s=i?function(){return i.cur()}:function(){return S.css(e,t,"")},l=s(),c=n&&n[3]||(S.cssNumber[t]?"":"px"),u=e.nodeType&&(S.cssNumber[t]||"px"!==c&&+l)&&ie.exec(S.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;a--;)S.style(e,t,u+c),(1-r)*(1-(r=s()/l||.5))<=0&&(a=0),u/=r;u*=2,S.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,o=n[1]?u+(n[1]+1)*n[2]:+n[2],i&&(i.unit=c,i.start=u,i.end=o)),o}var ue={};function de(e){var t,n=e.ownerDocument,i=e.nodeName,o=ue[i];return o||(t=n.body.appendChild(n.createElement(i)),o=S.css(t,"display"),t.parentNode.removeChild(t),"none"===o&&(o="block"),ue[i]=o,o)}function fe(e,t){for(var n,i,o=[],r=0,a=e.length;r<a;r++)(i=e[r]).style&&(n=i.style.display,t?("none"===n&&(o[r]=Q.get(i,"display")||null,o[r]||(i.style.display="")),""===i.style.display&&le(i)&&(o[r]=de(i))):"none"!==n&&(o[r]="none",Q.set(i,"display",n)));for(r=0;r<a;r++)null!=o[r]&&(e[r].style.display=o[r]);return e}S.fn.extend({show:function(){return fe(this,!0)},hide:function(){return fe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){le(this)?S(this).show():S(this).hide()}))}});var he,pe,ge=/^(?:checkbox|radio)$/i,me=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ve=/^$|^module$|\/(?:java|ecma)script/i;he=b.createDocumentFragment().appendChild(b.createElement("div")),(pe=b.createElement("input")).setAttribute("type","radio"),pe.setAttribute("checked","checked"),pe.setAttribute("name","t"),he.appendChild(pe),m.checkClone=he.cloneNode(!0).cloneNode(!0).lastChild.checked,he.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!he.cloneNode(!0).lastChild.defaultValue,he.innerHTML="<option></option>",m.option=!!he.lastChild;var ye={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function be(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&k(e,t)?S.merge([e],n):n}function we(e,t){for(var n=0,i=e.length;n<i;n++)Q.set(e[n],"globalEval",!t||Q.get(t[n],"globalEval"))}ye.tbody=ye.tfoot=ye.colgroup=ye.caption=ye.thead,ye.th=ye.td,m.option||(ye.optgroup=ye.option=[1,"<select multiple='multiple'>","</select>"]);var xe=/<|&#?\w+;/;function Ee(e,t,n,i,o){for(var r,a,s,l,c,u,d=t.createDocumentFragment(),f=[],h=0,p=e.length;h<p;h++)if((r=e[h])||0===r)if("object"===E(r))S.merge(f,r.nodeType?[r]:r);else if(xe.test(r)){for(a=a||d.appendChild(t.createElement("div")),s=(me.exec(r)||["",""])[1].toLowerCase(),l=ye[s]||ye._default,a.innerHTML=l[1]+S.htmlPrefilter(r)+l[2],u=l[0];u--;)a=a.lastChild;S.merge(f,a.childNodes),(a=d.firstChild).textContent=""}else f.push(t.createTextNode(r));for(d.textContent="",h=0;r=f[h++];)if(i&&S.inArray(r,i)>-1)o&&o.push(r);else if(c=ae(r),a=be(d.appendChild(r),"script"),c&&we(a),n)for(u=0;r=a[u++];)ve.test(r.type||"")&&n.push(r);return d}var Se=/^([^.]*)(?:\.(.+)|)/;function Le(){return!0}function Te(){return!1}function Ce(e,t){return e===function(){try{return b.activeElement}catch(e){}}()==("focus"===t)}function Ae(e,t,n,i,o,r){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(i=i||n,n=void 0),t)Ae(e,s,n,i,t[s],r);return e}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=Te;else if(!o)return e;return 1===r&&(a=o,(o=function(e){return S().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=S.guid++)),e.each((function(){S.event.add(this,t,o,i,n)}))}function Me(e,t,n){n?(Q.set(e,t,!1),S.event.add(e,t,{namespace:!1,handler:function(e){var i,o,r=Q.get(this,t);if(1&e.isTrigger&&this[t]){if(r.length)(S.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),Q.set(this,t,r),i=n(this,t),this[t](),r!==(o=Q.get(this,t))||i?Q.set(this,t,!1):o={},r!==o)return e.stopImmediatePropagation(),e.preventDefault(),o&&o.value}else r.length&&(Q.set(this,t,{value:S.event.trigger(S.extend(r[0],S.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Q.get(e,t)&&S.event.add(e,t,Le)}S.event={global:{},add:function(e,t,n,i,o){var r,a,s,l,c,u,d,f,h,p,g,m=Q.get(e);if(G(e))for(n.handler&&(n=(r=n).handler,o=r.selector),o&&S.find.matchesSelector(re,o),n.guid||(n.guid=S.guid++),(l=m.events)||(l=m.events=Object.create(null)),(a=m.handle)||(a=m.handle=function(t){return void 0!==S&&S.event.triggered!==t.type?S.event.dispatch.apply(e,arguments):void 0}),c=(t=(t||"").match(D)||[""]).length;c--;)h=g=(s=Se.exec(t[c])||[])[1],p=(s[2]||"").split(".").sort(),h&&(d=S.event.special[h]||{},h=(o?d.delegateType:d.bindType)||h,d=S.event.special[h]||{},u=S.extend({type:h,origType:g,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&S.expr.match.needsContext.test(o),namespace:p.join(".")},r),(f=l[h])||((f=l[h]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(e,i,p,a)||e.addEventListener&&e.addEventListener(h,a)),d.add&&(d.add.call(e,u),u.handler.guid||(u.handler.guid=n.guid)),o?f.splice(f.delegateCount++,0,u):f.push(u),S.event.global[h]=!0)},remove:function(e,t,n,i,o){var r,a,s,l,c,u,d,f,h,p,g,m=Q.hasData(e)&&Q.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(D)||[""]).length;c--;)if(h=g=(s=Se.exec(t[c])||[])[1],p=(s[2]||"").split(".").sort(),h){for(d=S.event.special[h]||{},f=l[h=(i?d.delegateType:d.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=r=f.length;r--;)u=f[r],!o&&g!==u.origType||n&&n.guid!==u.guid||s&&!s.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(f.splice(r,1),u.selector&&f.delegateCount--,d.remove&&d.remove.call(e,u));a&&!f.length&&(d.teardown&&!1!==d.teardown.call(e,p,m.handle)||S.removeEvent(e,h,m.handle),delete l[h])}else for(h in l)S.event.remove(e,h+t[c],n,i,!0);S.isEmptyObject(l)&&Q.remove(e,"handle events")}},dispatch:function(e){var t,n,i,o,r,a,s=new Array(arguments.length),l=S.event.fix(e),c=(Q.get(this,"events")||Object.create(null))[l.type]||[],u=S.event.special[l.type]||{};for(s[0]=l,t=1;t<arguments.length;t++)s[t]=arguments[t];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(a=S.event.handlers.call(this,l,c),t=0;(o=a[t++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,n=0;(r=o.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==r.namespace&&!l.rnamespace.test(r.namespace)||(l.handleObj=r,l.data=r.data,void 0!==(i=((S.event.special[r.origType]||{}).handle||r.handler).apply(o.elem,s))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,i,o,r,a,s=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&e.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(r=[],a={},n=0;n<l;n++)void 0===a[o=(i=t[n]).selector+" "]&&(a[o]=i.needsContext?S(o,this).index(c)>-1:S.find(o,this,null,[c]).length),a[o]&&r.push(i);r.length&&s.push({elem:c,handlers:r})}return c=this,l<t.length&&s.push({elem:c,handlers:t.slice(l)}),s},addProp:function(e,t){Object.defineProperty(S.Event.prototype,e,{enumerable:!0,configurable:!0,get:v(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return ge.test(t.type)&&t.click&&k(t,"input")&&Me(t,"click",Le),!1},trigger:function(e){var t=this||e;return ge.test(t.type)&&t.click&&k(t,"input")&&Me(t,"click"),!0},_default:function(e){var t=e.target;return ge.test(t.type)&&t.click&&k(t,"input")&&Q.get(t,"click")||k(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Le:Te,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:Te,isPropagationStopped:Te,isImmediatePropagationStopped:Te,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Le,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Le,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Le,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},(function(e,t){S.event.special[e]={setup:function(){return Me(this,e,Ce),!1},trigger:function(){return Me(this,e),!0},_default:function(){return!0},delegateType:t}})),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){S.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,o=e.relatedTarget,r=e.handleObj;return o&&(o===i||S.contains(i,o))||(e.type=r.origType,n=r.handler.apply(this,arguments),e.type=t),n}}})),S.fn.extend({on:function(e,t,n,i){return Ae(this,e,t,n,i)},one:function(e,t,n,i){return Ae(this,e,t,n,i,1)},off:function(e,t,n){var i,o;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,S(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Te),this.each((function(){S.event.remove(this,e,n,t)}))}});var ke=/<script|<style|<link/i,_e=/checked\s*(?:[^=]|=\s*.checked.)/i,Pe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Oe(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function je(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function qe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Re(e,t){var n,i,o,r,a,s;if(1===t.nodeType){if(Q.hasData(e)&&(s=Q.get(e).events))for(o in Q.remove(t,"handle events"),s)for(n=0,i=s[o].length;n<i;n++)S.event.add(t,o,s[o][n]);K.hasData(e)&&(r=K.access(e),a=S.extend({},r),K.set(t,a))}}function Ie(e,t){var n=t.nodeName.toLowerCase();"input"===n&&ge.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function De(e,t,n,i){t=l(t);var o,r,a,s,c,u,d=0,f=e.length,h=f-1,p=t[0],g=v(p);if(g||f>1&&"string"==typeof p&&!m.checkClone&&_e.test(p))return e.each((function(o){var r=e.eq(o);g&&(t[0]=p.call(this,o,r.html())),De(r,t,n,i)}));if(f&&(r=(o=Ee(t,e[0].ownerDocument,!1,e,i)).firstChild,1===o.childNodes.length&&(o=r),r||i)){for(s=(a=S.map(be(o,"script"),je)).length;d<f;d++)c=o,d!==h&&(c=S.clone(c,!0,!0),s&&S.merge(a,be(c,"script"))),n.call(e[d],c,d);if(s)for(u=a[a.length-1].ownerDocument,S.map(a,qe),d=0;d<s;d++)c=a[d],ve.test(c.type||"")&&!Q.access(c,"globalEval")&&S.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?S._evalUrl&&!c.noModule&&S._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):x(c.textContent.replace(Pe,""),c,u))}return e}function Fe(e,t,n){for(var i,o=t?S.filter(t,e):e,r=0;null!=(i=o[r]);r++)n||1!==i.nodeType||S.cleanData(be(i)),i.parentNode&&(n&&ae(i)&&we(be(i,"script")),i.parentNode.removeChild(i));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,o,r,a,s=e.cloneNode(!0),l=ae(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(a=be(s),i=0,o=(r=be(e)).length;i<o;i++)Ie(r[i],a[i]);if(t)if(n)for(r=r||be(e),a=a||be(s),i=0,o=r.length;i<o;i++)Re(r[i],a[i]);else Re(e,s);return(a=be(s,"script")).length>0&&we(a,!l&&be(e,"script")),s},cleanData:function(e){for(var t,n,i,o=S.event.special,r=0;void 0!==(n=e[r]);r++)if(G(n)){if(t=n[Q.expando]){if(t.events)for(i in t.events)o[i]?S.event.remove(n,i):S.removeEvent(n,i,t.handle);n[Q.expando]=void 0}n[K.expando]&&(n[K.expando]=void 0)}}}),S.fn.extend({detach:function(e){return Fe(this,e,!0)},remove:function(e){return Fe(this,e)},text:function(e){return W(this,(function(e){return void 0===e?S.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return De(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Oe(this,e).appendChild(e)}))},prepend:function(){return De(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Oe(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return De(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return De(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(be(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return S.clone(this,e,t)}))},html:function(e){return W(this,(function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ke.test(e)&&!ye[(me.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(be(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return De(this,arguments,(function(t){var n=this.parentNode;S.inArray(this,e)<0&&(S.cleanData(be(this)),n&&n.replaceChild(t,this))}),e)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){S.fn[e]=function(e){for(var n,i=[],o=S(e),r=o.length-1,a=0;a<=r;a++)n=a===r?this:this.clone(!0),S(o[a])[t](n),c.apply(i,n.get());return this.pushStack(i)}}));var Ne=new RegExp("^("+ne+")(?!px)[a-z%]+$","i"),He=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=n),t.getComputedStyle(e)},Be=function(e,t,n){var i,o,r={};for(o in t)r[o]=e.style[o],e.style[o]=t[o];for(o in i=n.call(e),t)e.style[o]=r[o];return i},ze=new RegExp(oe.join("|"),"i");function $e(e,t,n){var i,o,r,a,s=e.style;return(n=n||He(e))&&(""!==(a=n.getPropertyValue(t)||n[t])||ae(e)||(a=S.style(e,t)),!m.pixelBoxStyles()&&Ne.test(a)&&ze.test(t)&&(i=s.width,o=s.minWidth,r=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=i,s.minWidth=o,s.maxWidth=r)),void 0!==a?a+"":a}function We(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",re.appendChild(c).appendChild(u);var e=n.getComputedStyle(u);i="1%"!==e.top,l=12===t(e.marginLeft),u.style.right="60%",a=36===t(e.right),o=36===t(e.width),u.style.position="absolute",r=12===t(u.offsetWidth/3),re.removeChild(c),u=null}}function t(e){return Math.round(parseFloat(e))}var i,o,r,a,s,l,c=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===u.style.backgroundClip,S.extend(m,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),i},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),r},reliableTrDimensions:function(){var e,t,i,o;return null==s&&(e=b.createElement("table"),t=b.createElement("tr"),i=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",t.style.height="1px",i.style.height="9px",i.style.display="block",re.appendChild(e).appendChild(t).appendChild(i),o=n.getComputedStyle(t),s=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===t.offsetHeight,re.removeChild(e)),s}}))}();var Ve=["Webkit","Moz","ms"],Ue=b.createElement("div").style,Xe={};function Ye(e){var t=S.cssProps[e]||Xe[e];return t||(e in Ue?e:Xe[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ve.length;n--;)if((e=Ve[n]+t)in Ue)return e}(e)||e)}var Ge=/^(none|table(?!-c[ea]).+)/,Ze=/^--/,Qe={position:"absolute",visibility:"hidden",display:"block"},Ke={letterSpacing:"0",fontWeight:"400"};function Je(e,t,n){var i=ie.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function et(e,t,n,i,o,r){var a="width"===t?1:0,s=0,l=0;if(n===(i?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=S.css(e,n+oe[a],!0,o)),i?("content"===n&&(l-=S.css(e,"padding"+oe[a],!0,o)),"margin"!==n&&(l-=S.css(e,"border"+oe[a]+"Width",!0,o))):(l+=S.css(e,"padding"+oe[a],!0,o),"padding"!==n?l+=S.css(e,"border"+oe[a]+"Width",!0,o):s+=S.css(e,"border"+oe[a]+"Width",!0,o));return!i&&r>=0&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-r-l-s-.5))||0),l}function tt(e,t,n){var i=He(e),o=(!m.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,i),r=o,a=$e(e,t,i),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ne.test(a)){if(!n)return a;a="auto"}return(!m.boxSizingReliable()&&o||!m.reliableTrDimensions()&&k(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===S.css(e,"display",!1,i))&&e.getClientRects().length&&(o="border-box"===S.css(e,"boxSizing",!1,i),(r=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+et(e,t,n||(o?"border":"content"),r,i,a)+"px"}function nt(e,t,n,i,o){return new nt.prototype.init(e,t,n,i,o)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=$e(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,r,a,s=Y(t),l=Ze.test(t),c=e.style;if(l||(t=Ye(s)),a=S.cssHooks[t]||S.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(o=a.get(e,!1,i))?o:c[t];"string"===(r=typeof n)&&(o=ie.exec(n))&&o[1]&&(n=ce(e,t,o),r="number"),null!=n&&n==n&&("number"!==r||l||(n+=o&&o[3]||(S.cssNumber[s]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,i))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,i){var o,r,a,s=Y(t);return Ze.test(t)||(t=Ye(s)),(a=S.cssHooks[t]||S.cssHooks[s])&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=$e(e,t,i)),"normal"===o&&t in Ke&&(o=Ke[t]),""===n||n?(r=parseFloat(o),!0===n||isFinite(r)?r||0:o):o}}),S.each(["height","width"],(function(e,t){S.cssHooks[t]={get:function(e,n,i){if(n)return!Ge.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?tt(e,t,i):Be(e,Qe,(function(){return tt(e,t,i)}))},set:function(e,n,i){var o,r=He(e),a=!m.scrollboxSize()&&"absolute"===r.position,s=(a||i)&&"border-box"===S.css(e,"boxSizing",!1,r),l=i?et(e,t,i,s,r):0;return s&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(r[t])-et(e,t,"border",!1,r)-.5)),l&&(o=ie.exec(n))&&"px"!==(o[3]||"px")&&(e.style[t]=n,n=S.css(e,t)),Je(0,n,l)}}})),S.cssHooks.marginLeft=We(m.reliableMarginLeft,(function(e,t){if(t)return(parseFloat($e(e,"marginLeft"))||e.getBoundingClientRect().left-Be(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),S.each({margin:"",padding:"",border:"Width"},(function(e,t){S.cssHooks[e+t]={expand:function(n){for(var i=0,o={},r="string"==typeof n?n.split(" "):[n];i<4;i++)o[e+oe[i]+t]=r[i]||r[i-2]||r[0];return o}},"margin"!==e&&(S.cssHooks[e+t].set=Je)})),S.fn.extend({css:function(e,t){return W(this,(function(e,t,n){var i,o,r={},a=0;if(Array.isArray(t)){for(i=He(e),o=t.length;a<o;a++)r[t[a]]=S.css(e,t[a],!1,i);return r}return void 0!==n?S.style(e,t,n):S.css(e,t)}),e,t,arguments.length>1)}}),S.Tween=nt,nt.prototype={constructor:nt,init:function(e,t,n,i,o,r){this.elem=e,this.prop=n,this.easing=o||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=r||(S.cssNumber[n]?"":"px")},cur:function(){var e=nt.propHooks[this.prop];return e&&e.get?e.get(this):nt.propHooks._default.get(this)},run:function(e){var t,n=nt.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):nt.propHooks._default.set(this),this}},nt.prototype.init.prototype=nt.prototype,nt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[Ye(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}},nt.propHooks.scrollTop=nt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=nt.prototype.init,S.fx.step={};var it,ot,rt=/^(?:toggle|show|hide)$/,at=/queueHooks$/;function st(){ot&&(!1===b.hidden&&n.requestAnimationFrame?n.requestAnimationFrame(st):n.setTimeout(st,S.fx.interval),S.fx.tick())}function lt(){return n.setTimeout((function(){it=void 0})),it=Date.now()}function ct(e,t){var n,i=0,o={height:e};for(t=t?1:0;i<4;i+=2-t)o["margin"+(n=oe[i])]=o["padding"+n]=e;return t&&(o.opacity=o.width=e),o}function ut(e,t,n){for(var i,o=(dt.tweeners[t]||[]).concat(dt.tweeners["*"]),r=0,a=o.length;r<a;r++)if(i=o[r].call(n,t,e))return i}function dt(e,t,n){var i,o,r=0,a=dt.prefilters.length,s=S.Deferred().always((function(){delete l.elem})),l=function(){if(o)return!1;for(var t=it||lt(),n=Math.max(0,c.startTime+c.duration-t),i=1-(n/c.duration||0),r=0,a=c.tweens.length;r<a;r++)c.tweens[r].run(i);return s.notifyWith(e,[c,i,n]),i<1&&a?n:(a||s.notifyWith(e,[c,1,0]),s.resolveWith(e,[c]),!1)},c=s.promise({elem:e,props:S.extend({},t),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},n),originalProperties:t,originalOptions:n,startTime:it||lt(),duration:n.duration,tweens:[],createTween:function(t,n){var i=S.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(i),i},stop:function(t){var n=0,i=t?c.tweens.length:0;if(o)return this;for(o=!0;n<i;n++)c.tweens[n].run(1);return t?(s.notifyWith(e,[c,1,0]),s.resolveWith(e,[c,t])):s.rejectWith(e,[c,t]),this}}),u=c.props;for(!function(e,t){var n,i,o,r,a;for(n in e)if(o=t[i=Y(n)],r=e[n],Array.isArray(r)&&(o=r[1],r=e[n]=r[0]),n!==i&&(e[i]=r,delete e[n]),(a=S.cssHooks[i])&&"expand"in a)for(n in r=a.expand(r),delete e[i],r)n in e||(e[n]=r[n],t[n]=o);else t[i]=o}(u,c.opts.specialEasing);r<a;r++)if(i=dt.prefilters[r].call(c,e,u,c.opts))return v(i.stop)&&(S._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return S.map(u,ut,c),v(c.opts.start)&&c.opts.start.call(e,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),S.fx.timer(S.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c}S.Animation=S.extend(dt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ce(n.elem,e,ie.exec(t),n),n}]},tweener:function(e,t){v(e)?(t=e,e=["*"]):e=e.match(D);for(var n,i=0,o=e.length;i<o;i++)n=e[i],dt.tweeners[n]=dt.tweeners[n]||[],dt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,o,r,a,s,l,c,u,d="width"in t||"height"in t,f=this,h={},p=e.style,g=e.nodeType&&le(e),m=Q.get(e,"fxshow");for(i in n.queue||(null==(a=S._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,f.always((function(){f.always((function(){a.unqueued--,S.queue(e,"fx").length||a.empty.fire()}))}))),t)if(o=t[i],rt.test(o)){if(delete t[i],r=r||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[i])continue;g=!0}h[i]=m&&m[i]||S.style(e,i)}if((l=!S.isEmptyObject(t))||!S.isEmptyObject(h))for(i in d&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(c=m&&m.display)&&(c=Q.get(e,"display")),"none"===(u=S.css(e,"display"))&&(c?u=c:(fe([e],!0),c=e.style.display||c,u=S.css(e,"display"),fe([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===S.css(e,"float")&&(l||(f.done((function(){p.display=c})),null==c&&(u=p.display,c="none"===u?"":u)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",f.always((function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}))),l=!1,h)l||(m?"hidden"in m&&(g=m.hidden):m=Q.access(e,"fxshow",{display:c}),r&&(m.hidden=!g),g&&fe([e],!0),f.done((function(){for(i in g||fe([e]),Q.remove(e,"fxshow"),h)S.style(e,i,h[i])}))),l=ut(g?m[i]:0,i,f),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?dt.prefilters.unshift(e):dt.prefilters.push(e)}}),S.speed=function(e,t,n){var i=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||v(e)&&e,duration:e,easing:n&&t||t&&!v(t)&&t};return S.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in S.fx.speeds?i.duration=S.fx.speeds[i.duration]:i.duration=S.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){v(i.old)&&i.old.call(this),i.queue&&S.dequeue(this,i.queue)},i},S.fn.extend({fadeTo:function(e,t,n,i){return this.filter(le).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var o=S.isEmptyObject(e),r=S.speed(t,n,i),a=function(){var t=dt(this,S.extend({},e),r);(o||Q.get(this,"finish"))&&t.stop(!0)};return a.finish=a,o||!1===r.queue?this.each(a):this.queue(r.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each((function(){var t=!0,o=null!=e&&e+"queueHooks",r=S.timers,a=Q.get(this);if(o)a[o]&&a[o].stop&&i(a[o]);else for(o in a)a[o]&&a[o].stop&&at.test(o)&&i(a[o]);for(o=r.length;o--;)r[o].elem!==this||null!=e&&r[o].queue!==e||(r[o].anim.stop(n),t=!1,r.splice(o,1));!t&&n||S.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=Q.get(this),i=n[e+"queue"],o=n[e+"queueHooks"],r=S.timers,a=i?i.length:0;for(n.finish=!0,S.queue(this,e,[]),o&&o.stop&&o.stop.call(this,!0),t=r.length;t--;)r[t].elem===this&&r[t].queue===e&&(r[t].anim.stop(!0),r.splice(t,1));for(t=0;t<a;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish}))}}),S.each(["toggle","show","hide"],(function(e,t){var n=S.fn[t];S.fn[t]=function(e,i,o){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(ct(t,!0),e,i,o)}})),S.each({slideDown:ct("show"),slideUp:ct("hide"),slideToggle:ct("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){S.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}})),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(it=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),it=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){ot||(ot=!0,st())},S.fx.stop=function(){ot=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(e,t){return e=S.fx&&S.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,i){var o=n.setTimeout(t,e);i.stop=function(){n.clearTimeout(o)}}))},function(){var e=b.createElement("input"),t=b.createElement("select").appendChild(b.createElement("option"));e.type="checkbox",m.checkOn=""!==e.value,m.optSelected=t.selected,(e=b.createElement("input")).value="t",e.type="radio",m.radioValue="t"===e.value}();var ft,ht=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return W(this,S.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){S.removeAttr(this,e)}))}}),S.extend({attr:function(e,t,n){var i,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===e.getAttribute?S.prop(e,t,n):(1===r&&S.isXMLDoc(e)||(o=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?ft:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:(e.setAttribute(t,n+""),n):o&&"get"in o&&null!==(i=o.get(e,t))?i:null==(i=S.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&"radio"===t&&k(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,o=t&&t.match(D);if(o&&1===e.nodeType)for(;n=o[i++];)e.removeAttribute(n)}}),ft={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=ht[t]||S.find.attr;ht[t]=function(e,t,i){var o,r,a=t.toLowerCase();return i||(r=ht[a],ht[a]=o,o=null!=n(e,t,i)?a:null,ht[a]=r),o}}));var pt=/^(?:input|select|textarea|button)$/i,gt=/^(?:a|area)$/i;function mt(e){return(e.match(D)||[]).join(" ")}function vt(e){return e.getAttribute&&e.getAttribute("class")||""}function yt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(D)||[]}S.fn.extend({prop:function(e,t){return W(this,S.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[S.propFix[e]||e]}))}}),S.extend({prop:function(e,t,n){var i,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&S.isXMLDoc(e)||(t=S.propFix[t]||t,o=S.propHooks[t]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:e[t]=n:o&&"get"in o&&null!==(i=o.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):pt.test(e.nodeName)||gt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){S.propFix[this.toLowerCase()]=this})),S.fn.extend({addClass:function(e){var t,n,i,o,r,a,s,l=0;if(v(e))return this.each((function(t){S(this).addClass(e.call(this,t,vt(this)))}));if((t=yt(e)).length)for(;n=this[l++];)if(o=vt(n),i=1===n.nodeType&&" "+mt(o)+" "){for(a=0;r=t[a++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");o!==(s=mt(i))&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,i,o,r,a,s,l=0;if(v(e))return this.each((function(t){S(this).removeClass(e.call(this,t,vt(this)))}));if(!arguments.length)return this.attr("class","");if((t=yt(e)).length)for(;n=this[l++];)if(o=vt(n),i=1===n.nodeType&&" "+mt(o)+" "){for(a=0;r=t[a++];)for(;i.indexOf(" "+r+" ")>-1;)i=i.replace(" "+r+" "," ");o!==(s=mt(i))&&n.setAttribute("class",s)}return this},toggleClass:function(e,t){var n=typeof e,i="string"===n||Array.isArray(e);return"boolean"==typeof t&&i?t?this.addClass(e):this.removeClass(e):v(e)?this.each((function(n){S(this).toggleClass(e.call(this,n,vt(this),t),t)})):this.each((function(){var t,o,r,a;if(i)for(o=0,r=S(this),a=yt(e);t=a[o++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else void 0!==e&&"boolean"!==n||((t=vt(this))&&Q.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":Q.get(this,"__className__")||""))}))},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+mt(vt(n))+" ").indexOf(t)>-1)return!0;return!1}});var bt=/\r/g;S.fn.extend({val:function(e){var t,n,i,o=this[0];return arguments.length?(i=v(e),this.each((function(n){var o;1===this.nodeType&&(null==(o=i?e.call(this,n,S(this).val()):e)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=S.map(o,(function(e){return null==e?"":e+""}))),(t=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,o,"value")||(this.value=o))}))):o?(t=S.valHooks[o.type]||S.valHooks[o.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(o,"value"))?n:"string"==typeof(n=o.value)?n.replace(bt,""):null==n?"":n:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:mt(S.text(e))}},select:{get:function(e){var t,n,i,o=e.options,r=e.selectedIndex,a="select-one"===e.type,s=a?null:[],l=a?r+1:o.length;for(i=r<0?l:a?r:0;i<l;i++)if(((n=o[i]).selected||i===r)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(t=S(n).val(),a)return t;s.push(t)}return s},set:function(e,t){for(var n,i,o=e.options,r=S.makeArray(t),a=o.length;a--;)((i=o[a]).selected=S.inArray(S.valHooks.option.get(i),r)>-1)&&(n=!0);return n||(e.selectedIndex=-1),r}}}}),S.each(["radio","checkbox"],(function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=S.inArray(S(e).val(),t)>-1}},m.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})})),m.focusin="onfocusin"in n;var wt=/^(?:focusinfocus|focusoutblur)$/,xt=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,i,o){var r,a,s,l,c,u,d,f,p=[i||b],g=h.call(e,"type")?e.type:e,m=h.call(e,"namespace")?e.namespace.split("."):[];if(a=f=s=i=i||b,3!==i.nodeType&&8!==i.nodeType&&!wt.test(g+S.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(e=e[S.expando]?e:new S.Event(g,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),t=null==t?[e]:S.makeArray(t,[e]),d=S.event.special[g]||{},o||!d.trigger||!1!==d.trigger.apply(i,t))){if(!o&&!d.noBubble&&!y(i)){for(l=d.delegateType||g,wt.test(l+g)||(a=a.parentNode);a;a=a.parentNode)p.push(a),s=a;s===(i.ownerDocument||b)&&p.push(s.defaultView||s.parentWindow||n)}for(r=0;(a=p[r++])&&!e.isPropagationStopped();)f=a,e.type=r>1?l:d.bindType||g,(u=(Q.get(a,"events")||Object.create(null))[e.type]&&Q.get(a,"handle"))&&u.apply(a,t),(u=c&&a[c])&&u.apply&&G(a)&&(e.result=u.apply(a,t),!1===e.result&&e.preventDefault());return e.type=g,o||e.isDefaultPrevented()||d._default&&!1!==d._default.apply(p.pop(),t)||!G(i)||c&&v(i[g])&&!y(i)&&((s=i[c])&&(i[c]=null),S.event.triggered=g,e.isPropagationStopped()&&f.addEventListener(g,xt),i[g](),e.isPropagationStopped()&&f.removeEventListener(g,xt),S.event.triggered=void 0,s&&(i[c]=s)),e.result}},simulate:function(e,t,n){var i=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(i,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each((function(){S.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}}),m.focusin||S.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){S.event.simulate(t,e.target,S.event.fix(e))};S.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,o=Q.access(i,t);o||i.addEventListener(e,n,!0),Q.access(i,t,(o||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,o=Q.access(i,t)-1;o?Q.access(i,t,o):(i.removeEventListener(e,n,!0),Q.remove(i,t))}}}));var Et=n.location,St={guid:Date.now()},Lt=/\?/;S.parseXML=function(e){var t,i;if(!e||"string"!=typeof e)return null;try{t=(new n.DOMParser).parseFromString(e,"text/xml")}catch(e){}return i=t&&t.getElementsByTagName("parsererror")[0],t&&!i||S.error("Invalid XML: "+(i?S.map(i.childNodes,(function(e){return e.textContent})).join("\n"):e)),t};var Tt=/\[\]$/,Ct=/\r?\n/g,At=/^(?:submit|button|image|reset|file)$/i,Mt=/^(?:input|select|textarea|keygen)/i;function kt(e,t,n,i){var o;if(Array.isArray(t))S.each(t,(function(t,o){n||Tt.test(e)?i(e,o):kt(e+"["+("object"==typeof o&&null!=o?t:"")+"]",o,n,i)}));else if(n||"object"!==E(t))i(e,t);else for(o in t)kt(e+"["+o+"]",t[o],n,i)}S.param=function(e,t){var n,i=[],o=function(e,t){var n=v(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,(function(){o(this.name,this.value)}));else for(n in e)kt(n,e[n],t,o);return i.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&Mt.test(this.nodeName)&&!At.test(e)&&(this.checked||!ge.test(e))})).map((function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,(function(e){return{name:t.name,value:e.replace(Ct,"\r\n")}})):{name:t.name,value:n.replace(Ct,"\r\n")}})).get()}});var _t=/%20/g,Pt=/#.*$/,Ot=/([?&])_=[^&]*/,jt=/^(.*?):[ \t]*([^\r\n]*)$/gm,qt=/^(?:GET|HEAD)$/,Rt=/^\/\//,It={},Dt={},Ft="*/".concat("*"),Nt=b.createElement("a");function Ht(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,o=0,r=t.toLowerCase().match(D)||[];if(v(n))for(;i=r[o++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function Bt(e,t,n,i){var o={},r=e===Dt;function a(s){var l;return o[s]=!0,S.each(e[s]||[],(function(e,s){var c=s(t,n,i);return"string"!=typeof c||r||o[c]?r?!(l=c):void 0:(t.dataTypes.unshift(c),a(c),!1)})),l}return a(t.dataTypes[0])||!o["*"]&&a("*")}function zt(e,t){var n,i,o=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((o[n]?e:i||(i={}))[n]=t[n]);return i&&S.extend(!0,e,i),e}Nt.href=Et.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Et.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Et.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ft,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?zt(zt(e,S.ajaxSettings),t):zt(S.ajaxSettings,e)},ajaxPrefilter:Ht(It),ajaxTransport:Ht(Dt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var i,o,r,a,s,l,c,u,d,f,h=S.ajaxSetup({},t),p=h.context||h,g=h.context&&(p.nodeType||p.jquery)?S(p):S.event,m=S.Deferred(),v=S.Callbacks("once memory"),y=h.statusCode||{},w={},x={},E="canceled",L={readyState:0,getResponseHeader:function(e){var t;if(c){if(!a)for(a={};t=jt.exec(r);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?r:null},setRequestHeader:function(e,t){return null==c&&(e=x[e.toLowerCase()]=x[e.toLowerCase()]||e,w[e]=t),this},overrideMimeType:function(e){return null==c&&(h.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)L.always(e[L.status]);else for(t in e)y[t]=[y[t],e[t]];return this},abort:function(e){var t=e||E;return i&&i.abort(t),T(0,t),this}};if(m.promise(L),h.url=((e||h.url||Et.href)+"").replace(Rt,Et.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(D)||[""],null==h.crossDomain){l=b.createElement("a");try{l.href=h.url,l.href=l.href,h.crossDomain=Nt.protocol+"//"+Nt.host!=l.protocol+"//"+l.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=S.param(h.data,h.traditional)),Bt(It,h,t,L),c)return L;for(d in(u=S.event&&h.global)&&0==S.active++&&S.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!qt.test(h.type),o=h.url.replace(Pt,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(_t,"+")):(f=h.url.slice(o.length),h.data&&(h.processData||"string"==typeof h.data)&&(o+=(Lt.test(o)?"&":"?")+h.data,delete h.data),!1===h.cache&&(o=o.replace(Ot,"$1"),f=(Lt.test(o)?"&":"?")+"_="+St.guid+++f),h.url=o+f),h.ifModified&&(S.lastModified[o]&&L.setRequestHeader("If-Modified-Since",S.lastModified[o]),S.etag[o]&&L.setRequestHeader("If-None-Match",S.etag[o])),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&L.setRequestHeader("Content-Type",h.contentType),L.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Ft+"; q=0.01":""):h.accepts["*"]),h.headers)L.setRequestHeader(d,h.headers[d]);if(h.beforeSend&&(!1===h.beforeSend.call(p,L,h)||c))return L.abort();if(E="abort",v.add(h.complete),L.done(h.success),L.fail(h.error),i=Bt(Dt,h,t,L)){if(L.readyState=1,u&&g.trigger("ajaxSend",[L,h]),c)return L;h.async&&h.timeout>0&&(s=n.setTimeout((function(){L.abort("timeout")}),h.timeout));try{c=!1,i.send(w,T)}catch(e){if(c)throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,t,a,l){var d,f,b,w,x,E=t;c||(c=!0,s&&n.clearTimeout(s),i=void 0,r=l||"",L.readyState=e>0?4:0,d=e>=200&&e<300||304===e,a&&(w=function(e,t,n){for(var i,o,r,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(o in s)if(s[o]&&s[o].test(i)){l.unshift(o);break}if(l[0]in n)r=l[0];else{for(o in n){if(!l[0]||e.converters[o+" "+l[0]]){r=o;break}a||(a=o)}r=r||a}if(r)return r!==l[0]&&l.unshift(r),n[r]}(h,L,a)),!d&&S.inArray("script",h.dataTypes)>-1&&S.inArray("json",h.dataTypes)<0&&(h.converters["text script"]=function(){}),w=function(e,t,n,i){var o,r,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(r=u.shift();r;)if(e.responseFields[r]&&(n[e.responseFields[r]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=r,r=u.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(a=c[l+" "+r]||c["* "+r]))for(o in c)if((s=o.split(" "))[1]===r&&(a=c[l+" "+s[0]]||c["* "+s[0]])){!0===a?a=c[o]:!0!==c[o]&&(r=s[0],u.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+r}}}return{state:"success",data:t}}(h,w,L,d),d?(h.ifModified&&((x=L.getResponseHeader("Last-Modified"))&&(S.lastModified[o]=x),(x=L.getResponseHeader("etag"))&&(S.etag[o]=x)),204===e||"HEAD"===h.type?E="nocontent":304===e?E="notmodified":(E=w.state,f=w.data,d=!(b=w.error))):(b=E,!e&&E||(E="error",e<0&&(e=0))),L.status=e,L.statusText=(t||E)+"",d?m.resolveWith(p,[f,E,L]):m.rejectWith(p,[L,E,b]),L.statusCode(y),y=void 0,u&&g.trigger(d?"ajaxSuccess":"ajaxError",[L,h,d?f:b]),v.fireWith(p,[L,E]),u&&(g.trigger("ajaxComplete",[L,h]),--S.active||S.event.trigger("ajaxStop")))}return L},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],(function(e,t){S[t]=function(e,n,i,o){return v(n)&&(o=o||i,i=n,n=void 0),S.ajax(S.extend({url:e,type:t,dataType:o,data:n,success:i},S.isPlainObject(e)&&e))}})),S.ajaxPrefilter((function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")})),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(v(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return v(e)?this.each((function(t){S(this).wrapInner(e.call(this,t))})):this.each((function(){var t=S(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=v(e);return this.each((function(n){S(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){S(this).replaceWith(this.childNodes)})),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(e){}};var $t={0:200,1223:204},Wt=S.ajaxSettings.xhr();m.cors=!!Wt&&"withCredentials"in Wt,m.ajax=Wt=!!Wt,S.ajaxTransport((function(e){var t,i;if(m.cors||Wt&&!e.crossDomain)return{send:function(o,r){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];for(a in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)s.setRequestHeader(a,o[a]);t=function(e){return function(){t&&(t=i=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?r(0,"error"):r(s.status,s.statusText):r($t[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=t(),i=s.onerror=s.ontimeout=t("error"),void 0!==s.onabort?s.onabort=i:s.onreadystatechange=function(){4===s.readyState&&n.setTimeout((function(){t&&i()}))},t=t("abort");try{s.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}})),S.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),S.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(i,o){t=S("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&o("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}}));var Vt,Ut=[],Xt=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Ut.pop()||S.expando+"_"+St.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",(function(e,t,i){var o,r,a,s=!1!==e.jsonp&&(Xt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Xt.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=v(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Xt,"$1"+o):!1!==e.jsonp&&(e.url+=(Lt.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return a||S.error(o+" was not called"),a[0]},e.dataTypes[0]="json",r=n[o],n[o]=function(){a=arguments},i.always((function(){void 0===r?S(n).removeProp(o):n[o]=r,e[o]&&(e.jsonpCallback=t.jsonpCallback,Ut.push(o)),a&&v(r)&&r(a[0]),a=r=void 0})),"script"})),m.createHTMLDocument=((Vt=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Vt.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((i=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(i)):t=b),r=!n&&[],(o=_.exec(e))?[t.createElement(o[1])]:(o=Ee([e],t,r),r&&r.length&&S(r).remove(),S.merge([],o.childNodes)));var i,o,r},S.fn.load=function(e,t,n){var i,o,r,a=this,s=e.indexOf(" ");return s>-1&&(i=mt(e.slice(s)),e=e.slice(0,s)),v(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&S.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done((function(e){r=arguments,a.html(i?S("<div>").append(S.parseHTML(e)).find(i):e)})).always(n&&function(e,t){a.each((function(){n.apply(this,r||[e.responseText,t,e])}))}),this},S.expr.pseudos.animated=function(e){return S.grep(S.timers,(function(t){return e===t.elem})).length},S.offset={setOffset:function(e,t,n){var i,o,r,a,s,l,c=S.css(e,"position"),u=S(e),d={};"static"===c&&(e.style.position="relative"),s=u.offset(),r=S.css(e,"top"),l=S.css(e,"left"),("absolute"===c||"fixed"===c)&&(r+l).indexOf("auto")>-1?(a=(i=u.position()).top,o=i.left):(a=parseFloat(r)||0,o=parseFloat(l)||0),v(t)&&(t=t.call(e,n,S.extend({},s))),null!=t.top&&(d.top=t.top-s.top+a),null!=t.left&&(d.left=t.left-s.left+o),"using"in t?t.using.call(e,d):u.css(d)}},S.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){S.offset.setOffset(this,e,t)}));var t,n,i=this[0];return i?i.getClientRects().length?(t=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],o={top:0,left:0};if("fixed"===S.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((o=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),o.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-o.top-S.css(i,"marginTop",!0),left:t.left-o.left-S.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===S.css(e,"position");)e=e.offsetParent;return e||re}))}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;S.fn[e]=function(i){return W(this,(function(e,i,o){var r;if(y(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===o)return r?r[t]:e[i];r?r.scrollTo(n?r.pageXOffset:o,n?o:r.pageYOffset):e[i]=o}),e,i,arguments.length)}})),S.each(["top","left"],(function(e,t){S.cssHooks[t]=We(m.pixelPosition,(function(e,n){if(n)return n=$e(e,t),Ne.test(n)?S(e).position()[t]+"px":n}))})),S.each({Height:"height",Width:"width"},(function(e,t){S.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,i){S.fn[i]=function(o,r){var a=arguments.length&&(n||"boolean"!=typeof o),s=n||(!0===o||!0===r?"margin":"border");return W(this,(function(t,n,o){var r;return y(t)?0===i.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===o?S.css(t,n,s):S.style(t,n,o,s)}),t,a?o:void 0,a)}}))})),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){S.fn[t]=function(e){return this.on(t,e)}})),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){S.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));var Yt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,i,o;if("string"==typeof t&&(n=e[t],t=e,e=n),v(e))return i=s.call(arguments,2),(o=function(){return e.apply(t||this,i.concat(s.call(arguments)))}).guid=e.guid=e.guid||S.guid++,o},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=k,S.isFunction=v,S.isWindow=y,S.camelCase=Y,S.type=E,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(Yt,"")},void 0===(i=function(){return S}.apply(t,[]))||(e.exports=i);var Gt=n.jQuery,Zt=n.$;return S.noConflict=function(e){return n.$===S&&(n.$=Zt),e&&n.jQuery===S&&(n.jQuery=Gt),S},void 0===o&&(n.jQuery=n.$=S),S}))},function(e,t,n){n(8),e.exports=n(7)},function(e,t){!function(){function e(e){var t=e.getBoundingClientRect().top+window.pageYOffset-((parseInt((getComputedStyle(document.documentElement).getPropertyValue("--header-height")||"72px").replace("px",""),10)||72)+8+(innerWidth<1024?-72:10));window.scrollTo({top:t<0?0:t,behavior:"smooth"})}document.addEventListener("click",(function(t){var n=t.target&&t.target.closest?t.target.closest("a.note-jump, a.finanzierung-link, a.more-goodies"):null;if(n){var i=n.getAttribute("data-jump");i&&(t.preventDefault(),function(t){var n=document.querySelector('.product-details-nav .detail-nav-item[data-target="'+t+'"]'),i=document.getElementById("section-"+t);if(n&&i)return"true"!==n.getAttribute("aria-expanded")&&(n.setAttribute("aria-expanded","true"),i.classList.add("-active")),void e(n);var o=document.getElementById(t);o&&e(o)}(i))}}),{passive:!1})}()},function(e,t){!function(){"use strict";const e={activeFilters:new Set,selectedRating:5,init(){this.bindShowMoreButton(),this.bindRatingFilters(),this.bindReviewFormTrigger(),this.bindStarRatingInput(),this.bindFormSubmit()},bindShowMoreButton(){const e=document.getElementById("product_rating_show_more_button");e&&e.addEventListener("click",()=>{this.showMoreReviews(4),this.updateShowMoreButton(e)})},showMoreReviews(e){const t=document.querySelectorAll(".review-card.-hidden:not(.-filtered-out)");let n=0;t.forEach(t=>{n<e&&(t.classList.remove("-hidden"),n++)})},updateShowMoreButton(e){const t=document.querySelectorAll(".review-card.-hidden:not(.-filtered-out)").length>0;e.style.display=t?"":"none"},bindRatingFilters(){document.querySelectorAll(".rating-bar-item[data-rating-filter]").forEach(e=>{e.addEventListener("click",()=>{const t=e.dataset.ratingFilter;this.toggleFilter(t,e)})})},toggleFilter(e,t){this.activeFilters.has(e)?(this.activeFilters.delete(e),t.classList.remove("-active")):(this.activeFilters.add(e),t.classList.add("-active")),this.applyFilters()},applyFilters(){const e=document.querySelectorAll(".review-card"),t=document.getElementById("product_rating_show_more_button");if(0===this.activeFilters.size)return e.forEach((e,t)=>{e.classList.remove("-filtered-out"),t>=4?e.classList.add("-hidden"):e.classList.remove("-hidden")}),void(t&&this.updateShowMoreButton(t));e.forEach(e=>{const t=e.dataset.rating;this.activeFilters.has(t)?(e.classList.remove("-filtered-out"),e.classList.remove("-hidden")):(e.classList.add("-filtered-out"),e.classList.add("-hidden"))}),t&&(t.style.display="none")},bindReviewFormTrigger(){document.querySelectorAll(".js-review-form-trigger").forEach(e=>{e.addEventListener("click",()=>{this.toggleReviewForm()})})},bindFormSubmit(){document.querySelectorAll(".review-form").forEach(e=>{e.addEventListener("submit",t=>{t.preventDefault(),this.submitReview(e)})})},submitReview(e){const t=new FormData(e),n=e.querySelector('button[type="submit"]'),i=e.querySelector(".form-error"),o=e.querySelector(".form-success");i.style.display="none",o.style.display="none",n.disabled=!0,n.textContent="Wird gesendet...";const r=new URLSearchParams;r.append("type","user_rating_1"),r.append("rating[product_id]",t.get("product_id")),r.append("rating[name]",t.get("name")),r.append("rating[mail]",t.get("mail")),r.append("rating[rating]",t.get("rating")),r.append("rating[headline]",t.get("title")),r.append("rating[comment]",t.get("comment")),fetch("/rest/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:r}).then(e=>e.json()).then(t=>{t.error?(i.textContent=t.error,i.style.display="block",n.disabled=!1,n.textContent="Bewerten"):(o.style.display="block",e.reset(),this.selectedRating=5,setTimeout(()=>{location.reload()},2e3))}).catch(e=>{i.textContent="Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.",i.style.display="block",n.disabled=!1,n.textContent="Bewerten"})},toggleReviewForm(){const e=document.querySelector(".review-form"),t=document.querySelector(".image-banner.-review");if(!e)return;"none"===e.style.display?(e.style.display="flex",t&&(t.style.display="none")):(e.style.display="none",t&&(t.style.display=""))},bindStarRatingInput(){const e=document.querySelectorAll(".star-rating-input .star-btn"),t=document.getElementById("rating-value"),n=window.matchMedia("(hover: hover) and (pointer: fine)").matches;e.forEach((i,o)=>{i.addEventListener("click",()=>{const n=parseInt(i.dataset.rating);this.setStarRating(n,e,t)}),n&&i.addEventListener("mouseenter",()=>{const t=parseInt(i.dataset.rating);this.updateStarDisplay(t,e)})});const i=document.querySelector(".star-rating-input");i&&n&&i.addEventListener("mouseleave",()=>{this.updateStarDisplay(this.selectedRating,e)})},setStarRating(e,t,n){this.selectedRating=e,n&&(n.value=e),this.updateStarDisplay(e,t)},updateStarDisplay(e,t){t.forEach((t,n)=>{const i=t.querySelector(".star-icon"),o=n<e,r=i.src.includes("star-full.svg");o&&!r?i.src=i.src.replace("star-empty.svg","star-full.svg"):!o&&r&&(i.src=i.src.replace("star-full.svg","star-empty.svg"))})}};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>e.init()):e.init()}()},function(e,t,n){"use strict";window.loadbeeCallback=function(e){if(200!==e.code)return!1;let t=document.getElementById("product_description");document.getElementById("product_description_loadbee_content").append(t);var n=document.getElementById("product_description_loadbee");n.style.display="block";var i=document.getElementById("product_description_button")||document.querySelector(".loadbee-toggle");i&&(i.onclick=function(){document.getElementById("product_description_loadbee_content").classList.remove("product_description_loadbee_content_hidden"),i.style.display="none",n.classList.add("is-expanded")});let o=new FormData;return o.append("type","loadbee"),o.append("url",e.url),fetch("/rest/",{method:"POST",body:o}),!0}},function(e,t,n){},function(e,t){function n(e,t){e&&t.length&&!t.data("is-loaded")&&(t.data("is-loaded",!0),jQuery.get("/product/shipment/",{product_id:e},(function(e){t.html(e)})))}var i=$(".page-product").data("product-id"),o=$("#shipment_extended");$(document).on("click",'.product-details-nav .detail-nav-item[data-target="shipping"]',(function(){setTimeout((function(){($(document).find("#section-shipping").is(":visible")||"true"===$('.detail-nav-item[data-target="shipping"]').attr("aria-expanded"))&&n(i,o)}),0)})),"#versandkosten"!==window.location.hash&&"#section-shipping"!==window.location.hash||n(i,o),"true"===$('.detail-nav-item[data-target="shipping"]').attr("aria-expanded")&&n(i,o)},function(e,t,n){},function(e,t,n){"use strict";n.r(t);var i=n(0),o=n.n(i);window.$=window.jQuery=o.a;window.jQuery;var r=o.a;const a=o.a,s=function(e){e||(e={}),e.hasOwnProperty("close_button")||(e.close_button=!0),e.hasOwnProperty("margin_scroller")||(e.margin_scroller=20),e.hasOwnProperty("height")||(e.height="fixed"),e.hasOwnProperty("classname")||(e.classname=""),this.config=e,this.id=e.id,this.inner=null,this.outer=null,this.content=a("<div/>",{class:"window_content"}),this.content.on("click",".window_close",o.a.proxy(this.close,this)),a(window).resize(o.a.proxy(this.resize,this))};s.prototype.show=function(){if(this.outer=a("<div/>",{class:"modalWindow",id:this.id}),this.inner=a("<div/>",{class:"modalWindowInner "+this.config.classname}),this.outer.on("click",o.a.proxy(this.close,this)),this.scroller=a("<div/>",{class:"modalWindowInner_scroller"}),this.inner.append(this.scroller),this.scroller.append(this.content),a("body").append(this.outer),a("body").append(this.inner),this.doPosition(),this.config.close_button){var e=a("<a/>",{class:"window_close",href:"#"}),t=a("<img/>",{src:"/assets/close2.png"});e.append(t),e.click(o.a.proxy(this.close,this)),this.inner.append(e)}},s.prototype.doPosition=function(){var e=this.inner.height(),t=this.inner.width();if(window.innerWidth<800)this.inner.css("left",0),this.inner.css("top",0),this.inner.css("width","100%"),this.inner.css("height","100%"),this.inner.css("overflow","auto");else{if("fixed"==this.config.height){var n=window.innerHeight-e>>1;n<0&&(this.inner.css("height",window.innerHeight-10+"px"),n=5),this.scroller.css("height",this.inner.height()-2*this.config.margin_scroller+"px"),this.inner.css("top",n+"px")}else this.inner.css("top","50px");var i=window.innerWidth-t>>1;i<0&&(i=0,this.inner.css("width","100%")),this.inner.css("left",i+"px")}},s.prototype.resize=function(){this.doPosition()},s.prototype.close=function(e){e.preventDefault(),e.stopPropagation(),this.outer.remove(),this.inner.remove()},s.prototype.html=function(e){this.content.html(e)},s.prototype.ajaxShow=function(e){this.show(),this.content.load(e)},s.prototype.setClass=function(e){this.config.classname=e},document.addEventListener("keydown",(function(e){if("Tab"===e.key){const e=a(".modalWindowInner").first();e&&setTimeout((function(){const t=a(":focus").first();console.log(t),e.has(t).length||e.children().find(":focusable").first().focus()}),100)}}));var l=s;let c=0;const u=27,d=9,f=13,h=38,p=40;function g(e){return e?e instanceof Element?e:e.jquery?e.get(0)||null:document.querySelector(e):null}function m(e){return null==e?"":"string"==typeof e?e:"string"==typeof e.label?e.label:String(e)}function v(e){return null==e?"":"string"==typeof e?e:"string"==typeof e.value?e.value:"string"==typeof e.label?e.label:""}function y(e){return"string"!=typeof e?[]:e.split(/\s+/).filter(Boolean)}function b(e,t={}){const n=g(e);if(!n)throw new Error("createAutocomplete requires a valid input element.");n.setAttribute("autocomplete","off"),n.hasAttribute("autocorrect")||n.setAttribute("autocorrect","off"),n.hasAttribute("autocapitalize")||n.setAttribute("autocapitalize","off"),n.hasAttribute("spellcheck")||n.setAttribute("spellcheck","false");const i={minLength:2,appendTo:null,dropdownClass:"",itemClass:"",activeItemClass:"",getItemValue:v,renderItem:m,fetchSuggestions:null,onSelect:null,onFocus:null,selectFirstOnEnter:!1,...t},o="function"==typeof i.fetchSuggestions?i.fetchSuggestions:null;if(!o)throw new Error("createAutocomplete requires a fetchSuggestions(term) function.");const r=g(i.appendTo)||n.parentElement;if(!r)throw new Error("createAutocomplete could not determine where to append the dropdown.");"static"===window.getComputedStyle(r).position&&(r.classList.add("autocomplete-parent"),r.style.position="relative");const a="autocomplete-list-"+ ++c,s=document.createElement("ul"),l=["autocomplete-list"];i.dropdownClass&&l.push(i.dropdownClass),s.className=l.join(" "),s.id=a,s.setAttribute("role","listbox"),s.style.display="none",r.appendChild(s),n.setAttribute("role","combobox"),n.setAttribute("aria-autocomplete","list"),n.setAttribute("aria-expanded","false"),n.setAttribute("aria-controls",a);const b=y(i.itemClass),w=y(i.activeItemClass);let x=[],E=-1,S=!1,L=0,T="";function C(e){const t=i.getItemValue(e);"string"==typeof t&&(n.value=t)}function A(e,t){"function"==typeof e&&e(t,{input:n,dropdown:s})}function M(){const e=s.querySelector(".is-active");e&&(e.classList.remove("is-active"),w.forEach(t=>e.classList.remove(t)),e.setAttribute("aria-selected","false"))}function k(e,{fromKeyboard:t=!1}={}){if(e<0||e>=x.length)return E=-1,void M();E=e,M();const n=s.querySelector(`[data-autocomplete-index="${e}"]`);if(n){const e=["autocomplete-item",...b];n.className=e.join(" "),w.forEach(e=>n.classList.add(e)),n.classList.add("is-active"),n.setAttribute("aria-selected","true")}const o=x[e];t&&C(o),A(i.onFocus,o)}function _(){const e=r.getBoundingClientRect(),t=n.getBoundingClientRect(),i=r.scrollTop||0,o=r.scrollLeft||0,a=t.bottom-e.top+i,l=t.left-e.left+o;s.style.top=a+"px",s.style.left=l+"px",s.style.minWidth=t.width+"px",s.style.width=t.width+"px"}function P(){S||0===x.length||(_(),s.style.display="block",n.setAttribute("aria-expanded","true"),S=!0)}function O({restoreTypedValue:e=!1}={}){S&&(s.style.display="none",n.setAttribute("aria-expanded","false"),S=!1,E=-1,M(),e&&(n.value=T))}function j(e){if(e<0||e>=x.length)return;const t=x[e];C(t),A(i.onSelect,t),O()}function q(){const e=n.value;if(T=e,!e||e.length<i.minLength)return O(),x=[],void(s.innerHTML="");const t=++L;Promise.resolve(o(e)).then(e=>{if(t===L){if(x=Array.isArray(e)?e:[],0===x.length)return O({restoreTypedValue:!0}),void(s.innerHTML="");s.innerHTML="",x.forEach((e,t)=>{const n=document.createElement("li"),o=["autocomplete-item",...b];n.className=o.join(" "),n.setAttribute("role","option"),n.setAttribute("aria-selected","false"),n.setAttribute("data-autocomplete-index",String(t)),n.innerHTML=i.renderItem(e,t,x),s.appendChild(n)}),P()}}).catch(()=>{t===L&&(x=[],s.innerHTML="",O({restoreTypedValue:!0}))})}function R(e){if(S||e.keyCode!==p&&e.keyCode!==h||x.length>0&&P(),S)switch(e.keyCode){case p:e.preventDefault(),E<x.length-1?k(E+1,{fromKeyboard:!0}):x.length>0&&k(0,{fromKeyboard:!0});break;case h:e.preventDefault(),E>0?k(E-1,{fromKeyboard:!0}):x.length>0&&k(x.length-1,{fromKeyboard:!0});break;case f:E>=0?(e.preventDefault(),j(E)):i.selectFirstOnEnter&&x.length>0&&(e.preventDefault(),j(0));break;case u:e.preventDefault(),O({restoreTypedValue:!0});break;case d:E>=0?j(E):O()}}function I(){window.setTimeout(()=>{O()},150)}function D(e){e.preventDefault();const t=e.target.closest("[data-autocomplete-index]");if(!t)return;const n=parseInt(t.getAttribute("data-autocomplete-index"),10);Number.isNaN(n)||j(n)}function F(e){const t=e.target.closest("[data-autocomplete-index]");if(!t)return;const n=parseInt(t.getAttribute("data-autocomplete-index"),10);Number.isNaN(n)||n===E||k(n)}return n.addEventListener("input",q),n.addEventListener("keydown",R),n.addEventListener("blur",I),s.addEventListener("mousedown",D),s.addEventListener("mousemove",F),window.addEventListener("resize",_),{close:O,open:P,destroy:function(){n.removeEventListener("input",q),n.removeEventListener("keydown",R),n.removeEventListener("blur",I),s.removeEventListener("mousedown",D),s.removeEventListener("mousemove",F),window.removeEventListener("resize",_),s.parentElement&&s.parentElement.removeChild(s),n.removeAttribute("aria-controls"),n.removeAttribute("aria-expanded"),n.removeAttribute("aria-autocomplete"),n.removeAttribute("role")}}}const w={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#039;"};function x(e){return null==e?"":String(e).replace(/[&<>"']/g,(function(e){return w[e]||e}))}function E(e){return x(e)}const S={check_housenumber:function(e){var t=$(this).closest(".form-field, .form_row"),n=t.find(".error");0===n.length&&(n=$('<div class="error"></div>').appendTo(t)),null===this.value.substr(-5).match(/[0-9]/)?(t.addClass("warn"),n.html("Haben Sie vielleicht die Hausnummer vergessen?")):(t.removeClass("warn"),n.html(""))},notice_delivery_countries:function(){if(window.eu_block_country_ids){var e=jQuery('select[name="country_id"]'),t=e.closest(".form-field, .form_row"),n=t.find(".error");0===n.length&&(n=$('<div class="error"></div>').appendTo(t));var i=parseInt(e.val());window.eu_block_country_ids.indexOf(i)>-1?(t.addClass("warn"),n.html("Wir bieten derzeit keine Lieferung in dieses Land an. Sie können die Ware jedoch bei uns abholen oder eine abweichende Lieferadresse auswählen.")):(t.removeClass("warn"),n.html(""))}},login:function(e){e&&e.stopImmediatePropagation();var t=new l({id:"order_login_window",classname:"order_login_window",height:"auto"});return t.show(),$(t.content).html($("#myModal").html()),!1},step1:{init:function(){jQuery('input[name="art"]').on("change",S.step1.changeArt),jQuery('input[name="given-name"]').on("change",S.step1.determineGender),S.step1.changeArt(),jQuery('input[name="address1"]').on("change",S.check_housenumber),jQuery('select[name="country_id"]').on("change",S.notice_delivery_countries),S.notice_delivery_countries(),b(jQuery('input[name="postal-code"]').get(0),{minLength:3,selectFirstOnEnter:!0,getItemValue:function(e){return e&&void 0!==e.plz?String(e.plz):""},renderItem:function(e){return"<b>"+x(e&&e.plz?String(e.plz):"")+"</b> "+x(e&&e.ort?String(e.ort):"")},fetchSuggestions:function(e){return new Promise((function(t){jQuery.ajax({type:"POST",url:"/rest/",data:{type:"city",plz:e},success:function(e){t(Array.isArray(e)?e:[])},error:function(){t([])}})}))},onSelect:function(e){e&&(jQuery('input[name="ort"]').val(e.ort),jQuery('input[name="address1"]').focus())}})},changeArt:function(){var e=jQuery('input[name="art"]:checked').val(),t=jQuery(".commercial"),n=jQuery("#firma");switch(e){case"commercial":t.show(),n.length&&n.prop("required",!0).attr("aria-required","true");break;case"private":t.hide(),n.length&&n.prop("required",!1).removeAttr("aria-required")}},determineGender:function(){var e=jQuery('select[name="anrede"]');if(0!==e.length){var t=e.val();if(!t||!String(t).trim()){var n=jQuery('input[name="given-name"]').val();jQuery.post("/rest/",{type:"gender",firstname:n}).done((function(t){var n=e.val();n&&""!==String(n).trim()||"male"==t&&e.val("Frau")}))}}}},step_liefer:{init:function(){jQuery('input[name="address1"]').on("change",S.check_housenumber),jQuery('input[name="given-name"]').on("change",S.step1.determineGender),b(jQuery('input[name="postal-code"]').get(0),{minLength:3,selectFirstOnEnter:!0,getItemValue:function(e){return e&&void 0!==e.plz?String(e.plz):""},renderItem:function(e){return"<b>"+x(e&&e.plz?String(e.plz):"")+"</b> "+x(e&&e.ort?String(e.ort):"")},fetchSuggestions:function(e){return new Promise((function(t){jQuery.ajax({type:"POST",url:"/rest/",data:{type:"city",plz:e},success:function(e){t(Array.isArray(e)?e:[])},error:function(){t([])}})}))},onSelect:function(e){e&&(jQuery('input[name="ort"]').val(e.ort),jQuery('input[name="address1"]').focus())}})}},step_dhlpackstation:{init:function(){jQuery('input[name="given-name"]').on("change",S.step1.determineGender)}},step_dhlpostfiliale:{init:function(){jQuery('input[name="given-name"]').on("change",S.step1.determineGender)}},step3:{init:function(){jQuery(".zahlungsart_changer").css("cursor","pointer"),jQuery(".zahlungsart_changer").bind("click",(function(e){var t=e.target.tagName;"INPUT"!=t&&"A"!=t&&(e.stopPropagation(),jQuery(e.currentTarget).find("input").attr("checked","checked"),jQuery(e.currentTarget).parents("form")[0].submit())}))}},step_success:{register:function(){var e=null,t=!1,n=jQuery("#register_form .error-message");return n.html(""),jQuery("#register_form input[type=password]").each((function(n,i){null==e?e=i.value:e==i.value&&(t=!0)})),t?e.length<5?(n.html("Ihr Passwort muss mindestens 5 Zeichen lang sein."),!1):void jQuery.post("/rest/",{type:"order_create_account",password:e}).done((function(e){0==e.error?jQuery("#register_form").html('<p class="success-message"><strong>Ihr Konto wurde erfolgreich erstellt.</strong></p>'):n.html("Es ist ein Fehler aufgetreten.")})):(n.html("Die eingegebenen Passwörter sind unterschiedlich."),!1)}}},L={login:function(){S.login()},step1:function(){S.step1.init()},step3:function(){S.step3.init()},step_liefer:function(){S.step_liefer.init()},step_dhlpackstation:function(){S.step_dhlpackstation.init()},step_dhlpostfiliale:function(){S.step_dhlpostfiliale.init()}};S.init=function(e){e&&Array.isArray(e)&&e.forEach((function(e){var t=L[e];t&&t()}))},window.checkout=S;var T=S;const C={currentToast:null,duration:3e3,initialized:!1,init:function(){if(!this.initialized){this.initialized=!0;var e=document.getElementById("toast-container");if(e){var t=e.querySelector(".toast-notification");t&&(this.currentToast=t,setTimeout((function(){C.remove(t)}),this.duration))}}},show:function(e,t){var n=document.getElementById("toast-container");if(n){this.currentToast&&this.remove(this.currentToast);var i=document.createElement("div");i.className="toast-notification -"+e,i.setAttribute("role","status"),i.setAttribute("aria-live","polite");var o=this.getIconSvg(e);i.innerHTML=o+'<span class="message">'+t+"</span>",n.appendChild(i),this.currentToast=i,setTimeout((function(){C.remove(i)}),this.duration)}else console.error("Toast container not found in header")},remove:function(e){e&&e.parentNode&&(e.parentNode.removeChild(e),this.currentToast===e&&(this.currentToast=null))},getIconSvg:function(e){var t={success:'<svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true"><path d="M10.6 16.6L17.65 9.55L16.25 8.15L10.6 13.8L7.75 10.95L6.35 12.35L10.6 16.6ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z" fill="#349357"/></svg>',warning:'<svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true"><path d="M12 17C12.2833 17 12.5208 16.9042 12.7125 16.7125C12.9042 16.5208 13 16.2833 13 16C13 15.7167 12.9042 15.4792 12.7125 15.2875C12.5208 15.0958 12.2833 15 12 15C11.7167 15 11.4792 15.0958 11.2875 15.2875C11.0958 15.4792 11 15.7167 11 16C11 16.2833 11.0958 16.5208 11.2875 16.7125C11.4792 16.9042 11.7167 17 12 17ZM11 13H13V7H11V13ZM8.25 21L3 15.75V8.25L8.25 3H15.75L21 8.25V15.75L15.75 21H8.25ZM9.1 19H14.9L19 14.9V9.1L14.9 5H9.1L5 9.1V14.9L9.1 19Z" fill="#EE3616"/></svg>',error:'<svg class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true"><path d="M12 17C12.2833 17 12.5208 16.9042 12.7125 16.7125C12.9042 16.5208 13 16.2833 13 16C13 15.7167 12.9042 15.4792 12.7125 15.2875C12.5208 15.0958 12.2833 15 12 15C11.7167 15 11.4792 15.0958 11.2875 15.2875C11.0958 15.4792 11 15.7167 11 16C11 16.2833 11.0958 16.5208 11.2875 16.7125C11.4792 16.9042 11.7167 17 12 17ZM11 13H13V7H11V13ZM8.25 21L3 15.75V8.25L8.25 3H15.75L21 8.25V15.75L15.75 21H8.25ZM9.1 19H14.9L19 14.9V9.1L14.9 5H9.1L5 9.1V14.9L9.1 19Z" fill="#EE3616"/></svg>'};return t[e]||t.success}};var A=C;function M(e,t,n){n=n||{};var i=encodeURIComponent(e)+"="+t;if(void 0!==n.expires&&null!==n.expires){var o=function(e){if("number"==typeof e){var t=new Date;return t.setDate(t.getDate()+e),t}return e instanceof Date?e:null}(n.expires);i+="; expires="+(o?o.toUTCString():n.expires)}return n.path&&(i+="; path="+n.path),n.domain&&(i+="; domain="+n.domain),n.secure&&(i+="; secure"),n.sameSite&&(i+="; samesite="+n.sameSite),i}function k(e,t,n){var i=n?Object.assign({},n):{};null==t&&(i.expires=-1);var o=String(t),r=M(e,i.raw?o:encodeURIComponent(o),i);return document.cookie=r,r}function _(e,t){var n=(t||{}).raw?function(e){return e}:decodeURIComponent,i=new RegExp("(?:^|; )"+encodeURIComponent(e)+"=([^;]*)").exec(document.cookie);return i?n(i[1]):null}var P={track(e){const t=new Date;t.setFullYear(t.getFullYear()+1);const n=_("recently_viewed")||"",i=n?n.split(",").filter(t=>t!==e):[];i.unshift(e),k("recently_viewed",i.slice(0,4).join(","),{expires:365,path:"/"})}};const O=1024;function j(){return window.innerWidth<O}const q={output:{formatPrice:function(e){return q.numberFormat(e,2,",",".")+" €"}},parseFloat:function(e){return"number"==typeof e?e:(e=e.indexOf(",")>=0?(e=e.replace(/\./,"")).replace(/,/,"."):e.replace(/,/,""))?parseFloat(e):0},numberFormat:function(e,t,n,i){var o="",r=e.toString(),a=r.indexOf("e");if(a>-1&&(o=r.substring(a),e=parseFloat(r.substring(0,a))),null!=t){var s=Math.pow(10,t);e=Math.round(e*s)/s}var l=e<0?"-":"",c=(e>0?Math.floor(e):Math.abs(Math.ceil(e))).toString(),u=e.toString().substring(c.length+l.length);if(n=null!=n?n:".",u=null!=t&&t>0||u.length>1?n+u.substring(1):"",null!=t&&t>0)for(var d=u.length-1,f=t;d<f;++d)u+="0";if(null!=(i=i!=n||0==u.length?i:null)&&""!=i)for(d=c.length-3;d>0;d-=3)c=c.substring(0,d)+i+c.substring(d);return l+c+u+o},floor:function(e,t){return t||(t=0),e*=Math.pow(10,t),e=Math.floor(e),e/=Math.pow(10,t)}};var R=q;var I=new class{init(){this.initializeTables(),this.bindCalculateTriggers()}bindCalculateTriggers(){document.querySelectorAll("[data-financing-trigger]").forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const n=e.getAttribute("data-target");if(!n)return;const i=document.querySelector(n);if(!i)return;const o=e.getAttribute("data-amount-input");let r=null;if(o){const e=document.querySelector(o);e&&(r=e.value)}null!=r&&""!==r||(r=i.getAttribute("data-amount")||e.getAttribute("data-amount"));const a=i.getAttribute("data-min-rate"),s=i.getAttribute("data-min-amount");this.calcTable(i,r,a,s)})})}initializeTables(){document.querySelectorAll("[data-financing-table]").forEach(e=>{const t=e.getAttribute("data-amount");if(!t)return;const n=e.getAttribute("data-min-rate"),i=e.getAttribute("data-min-amount");this.calcTable(e,t,n,i)})}calcTable(e,t,n,i){let o;if(o="string"==typeof e?document.getElementById(e):e,!o)return;if(null==t||""===t)return;const r=R.parseFloat(t);if(!Number.isFinite(r))return;const a=null==n||""===n?null:parseFloat(n),s=null==i||""===i?null:parseFloat(i),l=o.getElementsByTagName("tr");for(let e=1;e<l.length;e++){const t=l[e],n=t.getElementsByTagName("td");if(n.length<5)continue;const i=parseInt(n[0].innerHTML,10);if(!Number.isFinite(i)||i<=0)continue;const o=R.parseFloat(n[3].innerHTML);if(!Number.isFinite(o))continue;let c,u,d;if(o>0){c=R.floor(12*(Math.pow(1+o/100,1/12)-1)*100,2);const e=Math.pow(o/100+1,1/12);u=R.floor(r*Math.pow(e,i)/(Math.pow(e,i)-1)*(e-1),2),d=u*i}else c=0,u=r/i,d=r;const f=null!==s&&r<s;null!==a&&parseFloat(u)<a||f?t.style.display="none":(t.style.display="table-row",n[1].innerHTML=R.output.formatPrice(u)),n[2].innerHTML=R.numberFormat(c,2,",","")+" %",n[4].innerHTML=R.output.formatPrice(d)}}};const D={text:"",backgroundColor:"rgba(128, 128, 128, 0.3)",color:"#fff",fontSize:"1.5rem",zIndex:"999",className:"ui-loading-overlay"};function F(e){return Array.from(e.querySelectorAll("."+D.className+', [data-loading-overlay="1"]'))}var N={show:function(e,t){if(!e||"function"!=typeof e.appendChild)throw new Error("Invalid target element for loading overlay");const n=F(e);if(n.length>0)return n[0];!function(e){"static"===window.getComputedStyle(e).position&&(e.dataset.loadingPrevPosition=e.style.position||"",e.style.position="relative",e.dataset.loadingPositionAdjusted="1")}(e);const i=function(e={}){const t={...D,...e},n=document.createElement("div");n.className=t.className,n.setAttribute("data-loading-overlay","1"),n.style.position="absolute",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.height="100%",n.style.backgroundColor=t.backgroundColor,n.style.display="flex",n.style.justifyContent="center",n.style.alignItems="center",n.style.zIndex=t.zIndex;const i=document.createElement("div");return i.textContent="string"==typeof t.text?t.text:D.text,i.style.color=t.color,i.style.fontSize=t.fontSize,n.appendChild(i),n}(t);return e.appendChild(i),i},hide:function(e){if(!e)return;if(F(e).forEach(e=>e.remove()),e.dataset&&"1"===e.dataset.loadingPositionAdjusted){const t=e.dataset.loadingPrevPosition||"";e.style.position=t,delete e.dataset.loadingPrevPosition,delete e.dataset.loadingPositionAdjusted}}};function H(e){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e,{once:!0}):e()}var B=0;function z(e){e?(B=window.pageYOffset||document.documentElement.scrollTop,document.body.style.position="fixed",document.body.style.top="-"+B+"px",document.body.style.width="100%",document.body.style.overflowY="scroll"):(document.body.style.position="",document.body.style.top="",document.body.style.width="",document.body.style.overflowY="",window.scrollTo(0,B))}const W=function(){this.button=null,this.container=null,this.is_show=!1,this.iconPath=null,this.activeCatId=void 0};W.prototype.setButton=function(e){this.button=e},W.prototype.setContainer=function(e){this.container=e},W.prototype.init=function(){if(!this.button||!this.container)return;this.button.addEventListener("click",this.buttonClickHandler.bind(this)),window.document.addEventListener("click",this.windowClickHandler.bind(this));const e=this.container.querySelector(".backdrop");e&&e.addEventListener("click",this.hide.bind(this))},W.prototype.buttonClickHandler=function(e){e.stopPropagation(),e.preventDefault(),this.show()},W.prototype.windowClickHandler=function(e){if(!this.is_show)return;if(!this.container.contains(e.target))return void this.hide();const t=e.target.closest("a,button");!t||this.linkClickHandler(e,t)},W.prototype.linkClickHandler=function(e,t){const n=t.dataset.category;if(!n)return!1;const i=e=>{const t=this.container.querySelector(".menu_category.-active");t&&t.classList.remove("-active");const n=document.getElementById("menu_category_"+e);n&&n.classList.add("-active")};return document.getElementById("menu_category_"+n)?i(n):$.getJSON("/rest/menu/?cat_id="+n,e=>{$("#mobile_menu_container_main_menu .header-menu-nav").append(this.renderCategory(e)),i(n)}),e.stopPropagation(),e.preventDefault(),!0},W.prototype.getIconPath=function(){if(this.iconPath)return this.iconPath;const e=window._cfg&&window._cfg.grafik_url?window._cfg.grafik_url:"/assets/";return this.iconPath=e,this.iconPath},W.prototype.getActiveCategoryId=function(){if(void 0!==this.activeCatId)return this.activeCatId;const e=this.container.getAttribute("data-active-cat-id");if(e)return this.activeCatId=String(e),this.activeCatId;const t=this.container.querySelector(".menu_category.-active .item.active");if(t){const e=t.querySelector(".link[data-category]");if(e&&e.dataset.category)return this.activeCatId=String(e.dataset.category),this.activeCatId;const n=t.querySelector('.cat_thumb[class*="cat_thumb_"]');if(n){const e=Array.prototype.slice.call(n.classList);let t=null;for(let n=0;n<e.length;n+=1)if(0===e[n].indexOf("cat_thumb_")){t=e[n];break}if(t)return this.activeCatId=t.replace("cat_thumb_",""),this.activeCatId}}return this.activeCatId=null,this.activeCatId},W.prototype.renderCategory=function(e){const t=void 0!==e.parent_cat_id&&null!==e.parent_cat_id,n=t?String(e.parent_cat_id):"0",i=Array.isArray(e.children)?e.children:[],o=this.getIconPath(),r=o+"icons/chevron-left.svg",a=o+"icons/chevron-right.svg",s=this.getActiveCategoryId(),l=String(e.cat_id),c=e.cat_shop_url?e.cat_shop_url:"#",u=i.map(e=>{const t=Array.isArray(e.children)&&e.children.length>0,n=e.cat_shop_url,i=x(e.cat_name),o=String(e.cat_id);return`\n            <li class="item${s===o?" active":""}">\n                <a class="link hover-underline" href="/${E(n)}" ${t?`data-category="${E(o)}"`:""}>\n                    <span class="cat_thumb_out">\n                        <span class="cat_thumb cat_thumb_${E(o)}"></span>\n                    </span>\n                    <span>${i}</span>\n                    ${t?`<img class="chevron" src="${E(a)}" alt="" width="24" height="24" />`:""}\n                </a>\n            </li>\n        `}).join("");return`\n        <div id="menu_category_${E(l)}" class="menu_category">\n            <div class="sticky-header">\n                <div class="section"${t?"":" hidden"}>\n                    <button class="back hover-underline" type="button" data-category="${E(n)}">\n                        <img class="chevron-left" src="${E(r)}" alt="" width="24" height="24">\n                        <span>Zurück</span>\n                    </button>\n                </div>\n                <div class="section-heading"${t?"":" hidden"}>\n                    <a class="heading" href="/${E(c)}">${x(e.cat_name)}</a>\n                </div>\n            </div>\n            <ul class="list">\n                ${u}\n            </ul>\n        </div>\n    `},W.prototype.show=function(){this.container.hidden=!1,this.container.classList.add("-active"),this.is_show=!0,z(!0)},W.prototype.hide=function(){this.container.classList.remove("-active"),this.container.hidden=!0,this.is_show=!1,z(!1)},H(()=>{const e=document.querySelector(".header-nav .icon-btn.menu"),t=document.getElementById("mobile_menu_container_main_menu");if(e&&t){const n=new W;n.setButton(e),n.setContainer(t),n.init()}}),H((function(){var e=document.querySelector('header[role="banner"]');if(!e)return;var t=e.querySelector(".header-nav > .wrapper > .main > .search-bar");if(!t)return;var n=e.querySelector(".header-nav > .wrapper > .main > .right .toggle");if(!n)return;const i=(o=O,window.matchMedia("(max-width: "+(o-1)+"px)"));var o;let r=0,a=0,s=!1,l=!1,c=window.scrollY||0;const u=document.getElementById("header-menu"),d=t.querySelector("input#exp"),f=t.querySelector(".clear");function h(){const e=!(!d||!d.value.trim().length);t.classList.toggle("-filled",e),f&&(f.hidden=!e)}function p(e){e?(t.classList.remove("-collapsed"),t.setAttribute("aria-hidden","false")):(t.classList.add("-collapsed"),t.setAttribute("aria-hidden","true")),function(){const e=!t.classList.contains("-collapsed");n.setAttribute("aria-expanded",String(e)),n.setAttribute("aria-pressed",String(s&&e))}()}function g(e){e?n.classList.add("-visible"):n.classList.remove("-visible")}function m(){r=Math.round(.3*window.innerHeight),a=Math.round(.1*window.innerHeight),v(!0)}function v(e){if(!i.matches)return p(!0),g(!1),l=!1,void(c=window.scrollY||0);if(u&&u.classList.contains("-active"))return p(!1),l=!1,void(c=window.scrollY||0);if(s)return p(!0),g(!1),l=!1,void(c=window.scrollY||0);var n=window.scrollY||0;!e&&Math.abs(n-c)<5||(n>=r?p(!1):n<=a&&p(!0),g(t.classList.contains("-collapsed")),c=n),l=!1}d&&(d.addEventListener("input",h),d.addEventListener("change",h)),f&&d&&f.addEventListener("click",(function(){d.value="",d.focus(),h()})),window.addEventListener("scroll",(function(){l||(l=!0,requestAnimationFrame((function(){v(!1)})))}),{passive:!0}),window.addEventListener("resize",m,{passive:!0}),i.addEventListener?i.addEventListener("change",(function(){v(!0)})):i.addListener&&i.addListener((function(){v(!0)})),n.addEventListener("click",(function(e){if(window.matchMedia("(hover: hover) and (pointer: fine)").matches&&e.preventDefault(),i.matches&&(s=!0,p(!0),g(!1),d&&window.matchMedia("(hover: hover) and (pointer: fine)").matches))try{d.focus({preventScroll:!0})}catch(e){d.focus()}})),m(),h()})),function(e){function t(t){return e.expr.filters.visible(t)&&!e(t).parents().addBack().filter((function(){return"hidden"===e.css(this,"visibility")})).length}e.extend(e.expr[":"],{focusable:function(n){return function(n,i){var o,r,a,s=n.nodeName.toLowerCase();return"area"===s?(r=(o=n.parentNode).name,!(!n.href||!r||"map"!==o.nodeName.toLowerCase())&&(!!(a=e("img[usemap=#"+r+"]")[0])&&t(a))):(/input|select|textarea|button|object/.test(s)?!n.disabled:"a"===s&&n.href||i)&&t(n)}(n,!isNaN(e.attr(n,"tabindex")))}})}(jQuery),r("a, button").each(e=>{const t=r(void 0);if(t.is(":hidden")||t.prop("aria-hidden"))return;const n=t.text().trim();t.attr("aria-label")||n&&t.attr("aria-label",n)}),function(){class e{constructor(e){this.root=e,this.dropdown=e.querySelector("[data-sort-dropdown]"),this.toggleBtn=e.querySelector("[data-sort-toggle]"),this.optionsBox=e.querySelector("[data-sort-options]")}init(){if(this.dropdown&&this.toggleBtn&&(this.toggleBtn.addEventListener("click",e=>{e.stopPropagation();const t=this.dropdown.classList.toggle("-open");this.toggleBtn.setAttribute("aria-expanded",String(t))}),document.addEventListener("click",e=>{this.dropdown.contains(e.target)||(this.dropdown.classList.remove("-open"),this.toggleBtn.setAttribute("aria-expanded","false"))}),this.optionsBox)){const e=this.optionsBox.querySelectorAll(".sort-option");e.forEach(t=>{t.addEventListener("click",()=>{const n=t.getAttribute("data-value");e.forEach(e=>{e.classList.remove("-selected"),e.setAttribute("aria-selected","false")}),t.classList.add("-selected"),t.setAttribute("aria-selected","true"),this.dropdown.classList.remove("-open"),this.toggleBtn.setAttribute("aria-expanded","false"),window.location.href=function(e,t){const n=new URL(window.location.href);return null==t?n.searchParams.delete(e):n.searchParams.set(e,t),n.toString()}("set_product_order",n)})})}}}class t{constructor(e){this.root=e,this.form=null,this.minInput=null,this.maxInput=null,this.leftHandle=null,this.rightHandle=null,this.rangeHighlight=null,this.summary=null,this.rangeMin=Number(e.getAttribute("data-min"))||0,this.rangeMax=Number(e.getAttribute("data-max"))||0,this.valueRange=this.rangeMax-this.rangeMin,(!isFinite(this.valueRange)||this.valueRange<=0)&&(this.valueRange=1,this.rangeMax=this.rangeMin+1),this.minDistanceBase=10,this.minDistancePx=this.minDistanceBase,this.maxPosition=0,this.handleWidth=0,this.actMin=0,this.actMax=0,this.activeHandle=null,this.activeHandleElement=null,this.startPointerX=0,this.startMin=0,this.startMax=0,this.activePointerId=null,this.isUpdatingInputs=!1,this.resizeObserver=null,this.prefix="",this.suffix="",this.decimals=null,this.rangeBehavior="",this.rangeBehaviorThreshold=null,this.rangeBehaviorFactor=2,this.rangeBehaviorActive=!1,this.boundPointerMove=this.onPointerMove.bind(this),this.boundPointerUp=this.onPointerUp.bind(this),this.boundResize=this.refreshLayout.bind(this),this.onInputChange=this.handleInputChange.bind(this),this.onMinPointerDown=e=>this.onPointerDown(e,"min"),this.onMaxPointerDown=e=>this.onPointerDown(e,"max"),this.handleListeners=[]}static findForm(e){if(!e)return null;var t=e.nextElementSibling;if(t&&t.matches(".filter_range_form"))return t;var n=e.parentElement;if(n){var i=n.querySelector(".filter_range_form");if(i)return i}return null}init(){if(this.root&&(this.form=t.findForm(this.root),this.leftHandle=this.root.querySelector(".slider_handle.left"),this.rightHandle=this.root.querySelector(".slider_handle.right"),this.rangeHighlight=this.root.querySelector(".slider-range"),this.summary=this.root.nextElementSibling,this.summary&&this.summary.hasAttribute("data-range-summary")||(this.summary=null),this.leftHandle&&this.rightHandle)){this.form&&(this.minInput=this.form.querySelector('input[name$="[value_min]"]'),this.maxInput=this.form.querySelector('input[name$="[value_max]"]')),this.refreshLayout(),this.attachHandleListeners(),this.attachInputListeners(),this.observeResize(),this.prefix=this.root.getAttribute("data-prefix")||"",this.suffix=this.root.getAttribute("data-suffix")||"";var e=this.root.getAttribute("data-decimals");if(null!=e&&""!==e){var n=Number(e);isFinite(n)&&(this.decimals=n)}this.updateSummary()}}destroy(){this.detachHandleListeners(),this.detachInputListeners(),this.disconnectResizeObserver(),this.releaseActivePointer()}attachHandleListeners(){if(this.leftHandle&&this.rightHandle)if(window.PointerEvent)this.leftHandle.addEventListener("pointerdown",this.onMinPointerDown),this.rightHandle.addEventListener("pointerdown",this.onMaxPointerDown),this.handleListeners.push({element:this.leftHandle,type:"pointerdown",listener:this.onMinPointerDown}),this.handleListeners.push({element:this.rightHandle,type:"pointerdown",listener:this.onMaxPointerDown});else{var e=e=>this.onPointerDown(e,"min"),t=e=>this.onPointerDown(e,"max"),n=e=>this.onPointerDown(e,"min"),i=e=>this.onPointerDown(e,"max");this.leftHandle.addEventListener("mousedown",e),this.leftHandle.addEventListener("touchstart",n,{passive:!1}),this.rightHandle.addEventListener("mousedown",t),this.rightHandle.addEventListener("touchstart",i,{passive:!1}),this.handleListeners.push({element:this.leftHandle,type:"mousedown",listener:e}),this.handleListeners.push({element:this.leftHandle,type:"touchstart",listener:n}),this.handleListeners.push({element:this.rightHandle,type:"mousedown",listener:t}),this.handleListeners.push({element:this.rightHandle,type:"touchstart",listener:i})}}detachHandleListeners(){this.handleListeners.forEach(e=>{e.element&&e.element.removeEventListener&&e.element.removeEventListener(e.type,e.listener)}),this.handleListeners=[]}attachInputListeners(){this.minInput&&(this.minInput.addEventListener("change",this.onInputChange),this.minInput.addEventListener("input",this.onInputChange)),this.maxInput&&(this.maxInput.addEventListener("change",this.onInputChange),this.maxInput.addEventListener("input",this.onInputChange))}detachInputListeners(){this.minInput&&(this.minInput.removeEventListener("change",this.onInputChange),this.minInput.removeEventListener("input",this.onInputChange)),this.maxInput&&(this.maxInput.removeEventListener("change",this.onInputChange),this.maxInput.removeEventListener("input",this.onInputChange))}observeResize(){window.ResizeObserver?(this.resizeObserver=new ResizeObserver(this.boundResize),this.resizeObserver.observe(this.root)):window.addEventListener("resize",this.boundResize)}disconnectResizeObserver(){this.resizeObserver?(this.resizeObserver.disconnect(),this.resizeObserver=null):window.removeEventListener("resize",this.boundResize)}refreshLayout(){this.readRangeBehaviorConfig(),this.measure(),this.syncHandlesFromInputs(),this.updateRangeHighlight()}measure(){this.root&&(this.handleWidth=this.leftHandle?this.leftHandle.offsetWidth:0,this.maxPosition=Math.max((this.root.clientWidth||0)-this.handleWidth,0),this.minDistancePx=Math.min(this.minDistanceBase,this.maxPosition),this.minDistancePx<0&&(this.minDistancePx=0))}formatNumber(e){if(!isFinite(e))return"";var t=Number(e);if(null!==this.decimals&&isFinite(this.decimals)){var n=Math.max(0,this.decimals);return t.toLocaleString(void 0,{minimumFractionDigits:n,maximumFractionDigits:n})}return Math.round(t).toLocaleString(void 0,{maximumFractionDigits:0})}formatValue(e){var t=this.formatNumber(e);return t?(this.prefix||"")+t+(this.suffix||""):""}parseValue(e){if(null==e)return null;var t=String(e).trim();if(!t)return null;var n=Number(t.replace(",","."));return isFinite(n)?n:null}valueToPosition(e){if(!isFinite(e))return 0;var t=(Math.min(Math.max(e,this.rangeMin),this.rangeMax)-this.rangeMin)/this.valueRange;return isFinite(t)||(t=0),this.mapValueRatioToSliderRatio(t)*this.maxPosition}positionToValue(e){if(0===this.maxPosition)return this.rangeMin;var t=e/this.maxPosition,n=this.mapSliderRatioToValueRatio(t),i=this.rangeMin+n*this.valueRange;return null!==this.decimals&&isFinite(this.decimals)?Number(i.toFixed(Math.max(0,this.decimals))):Math.round(i)}readRangeBehaviorConfig(){if(!this.root)return this.rangeBehavior="",this.rangeBehaviorThreshold=null,this.rangeBehaviorFactor=2,void(this.rangeBehaviorActive=!1);var e=this.root.getAttribute("data-range-behavior")||"";if(this.rangeBehavior=e?e.trim():"",this.rangeBehavior&&"non-linear-low"!==this.rangeBehavior&&"non-linear-high"!==this.rangeBehavior)throw new Error("Unexpected range behavior value: "+this.rangeBehavior);var t=this.root.getAttribute("data-range-behavior-threshold"),n=Number(t);(!isFinite(n)||n<=0)&&(n=null),this.rangeBehaviorThreshold=n;var i=this.root.getAttribute("data-range-behavior-factor"),o=Number(i);(!isFinite(o)||o<=0)&&(o=2),this.rangeBehaviorFactor=o,this.rangeBehaviorActive=this.shouldUseNonLinearBehavior()}shouldUseNonLinearBehavior(){return!!this.rangeBehavior&&(("non-linear-low"===this.rangeBehavior||"non-linear-high"===this.rangeBehavior)&&(!(!isFinite(this.rangeBehaviorThreshold)||this.rangeBehaviorThreshold<=0)&&this.valueRange>=this.rangeBehaviorThreshold))}clampRatio(e){return isFinite(e)?e<0?0:e>1?1:e:0}mapSliderRatioToValueRatio(e){var t=this.clampRatio(e);if(!this.rangeBehaviorActive)return t;var n=this.rangeBehaviorFactor;return"non-linear-low"===this.rangeBehavior?Math.pow(t,n):"non-linear-high"===this.rangeBehavior?1-Math.pow(1-t,n):t}mapValueRatioToSliderRatio(e){var t=this.clampRatio(e);if(!this.rangeBehaviorActive)return t;var n=this.rangeBehaviorFactor;return"non-linear-low"===this.rangeBehavior?Math.pow(t,1/n):"non-linear-high"===this.rangeBehavior?1-Math.pow(1-t,1/n):t}syncHandlesFromInputs(){var e=this.minInput?this.minInput.value:"",t=this.maxInput?this.maxInput.value:"",n=!(!e||""===String(e).trim()),i=!(!t||""===String(t).trim()),o=n?this.parseValue(e):null,r=i?this.parseValue(t):null;if(null===o&&(o=this.rangeMin),null===r&&(r=this.rangeMax),r<o){var a=o;o=r,r=a}this.actMin=this.valueToPosition(o),this.actMax=this.valueToPosition(r),this.actMin=Math.max(0,Math.min(this.actMin,this.actMax-this.minDistancePx)),this.actMax=Math.min(this.maxPosition,Math.max(this.actMax,this.actMin+this.minDistancePx)),!n&&this.actMin<=this.minDistancePx&&(this.actMin=0),!i&&this.actMax>=this.maxPosition-this.minDistancePx&&(this.actMax=this.maxPosition),this.positionHandles(),this.updateRangeHighlight(),this.updateSummary()}positionHandles(){this.leftHandle&&(this.leftHandle.style.left=Math.round(this.actMin)+"px"),this.rightHandle&&(this.rightHandle.style.left=Math.round(this.actMax)+"px")}getClientX(e){return"undefined"!=typeof TouchEvent&&e instanceof TouchEvent?e.touches&&e.touches.length?e.touches[0].clientX:null:void 0!==e.clientX?e.clientX:null}onPointerDown(e,t){if("mousedown"!==e.type||!window.PointerEvent){var n=this.getClientX(e);null!==n&&(e.preventDefault(),this.activeHandle=t,this.activeHandleElement=e.currentTarget||e.target,this.startPointerX=n,this.startMin=this.actMin,this.startMax=this.actMax,void 0!==e.pointerId&&this.activeHandleElement&&this.activeHandleElement.setPointerCapture?(this.activePointerId=e.pointerId,this.activeHandleElement.setPointerCapture(this.activePointerId),document.addEventListener("pointermove",this.boundPointerMove),document.addEventListener("pointerup",this.boundPointerUp),document.addEventListener("pointercancel",this.boundPointerUp)):"touchstart"===e.type?(document.addEventListener("touchmove",this.boundPointerMove,{passive:!1}),document.addEventListener("touchend",this.boundPointerUp),document.addEventListener("touchcancel",this.boundPointerUp)):(document.addEventListener("mousemove",this.boundPointerMove),document.addEventListener("mouseup",this.boundPointerUp)),this.activeHandleElement&&this.activeHandleElement.classList.add("-dragging"))}}onPointerMove(e){if(this.activeHandle&&("pointermove"!==e.type||null===this.activePointerId||e.pointerId===this.activePointerId)){var t=this.getClientX(e);if(null!==t){e.preventDefault&&e.preventDefault();var n=t-this.startPointerX;if("min"===this.activeHandle){var i=this.startMin+n,o=this.startMax-this.minDistancePx;o<0&&(o=0),this.actMin=Math.max(0,Math.min(i,o)),this.actMin>this.actMax-this.minDistancePx&&(this.actMin=this.actMax-this.minDistancePx)}else{var r=this.startMax+n,a=this.startMin+this.minDistancePx;this.actMax=Math.min(this.maxPosition,Math.max(r,a)),this.actMax<this.actMin+this.minDistancePx&&(this.actMax=this.actMin+this.minDistancePx)}this.positionHandles(),this.updateInputs(),this.updateRangeHighlight()}}}onPointerUp(e){e&&"pointerup"===e.type&&null!==this.activePointerId&&e.pointerId!==this.activePointerId||this.releaseActivePointer()}releaseActivePointer(){if(this.activeHandleElement&&null!==this.activePointerId&&this.activeHandleElement.releasePointerCapture)try{this.activeHandleElement.releasePointerCapture(this.activePointerId)}catch(e){}document.removeEventListener("pointermove",this.boundPointerMove),document.removeEventListener("pointerup",this.boundPointerUp),document.removeEventListener("pointercancel",this.boundPointerUp),document.removeEventListener("mousemove",this.boundPointerMove),document.removeEventListener("mouseup",this.boundPointerUp),document.removeEventListener("touchmove",this.boundPointerMove),document.removeEventListener("touchmove",this.boundPointerMove,{passive:!1}),document.removeEventListener("touchend",this.boundPointerUp),document.removeEventListener("touchcancel",this.boundPointerUp),this.activeHandleElement&&this.activeHandleElement.classList.remove("-dragging"),this.activeHandle=null,this.activeHandleElement=null,this.activePointerId=null}updateInputs(){if(!this.isUpdatingInputs){this.isUpdatingInputs=!0;var e=this.positionToValue(this.actMin),t=this.positionToValue(this.actMax);this.minInput&&(this.actMin<=0?this.minInput.value="":this.minInput.value=String(e)),this.maxInput&&(this.actMax>=this.maxPosition?this.maxInput.value="":this.maxInput.value=String(t)),this.updateSummary(),this.isUpdatingInputs=!1}}updateRangeHighlight(){if(this.rangeHighlight){var e=this.actMin+this.handleWidth/2,t=this.actMax+this.handleWidth/2,n=Math.max(t-e,0),i=this.root?this.root.clientWidth:0;if(i>0){var o=Math.max(i-e,0);n=Math.min(n,o)}this.rangeHighlight.style.left=Math.round(e)+"px",this.rangeHighlight.style.width=Math.max(Math.round(n),0)+"px"}}updateSummary(){if(this.summary){var e=this.actMin<=0?this.rangeMin:this.positionToValue(this.actMin),t=this.actMax>=this.maxPosition?this.rangeMax:this.positionToValue(this.actMax),n=this.formatValue(e),i=this.formatValue(t);this.summary.textContent=n||i?n?i?n+" - "+i:n:i:""}}handleInputChange(){this.isUpdatingInputs||(this.syncHandlesFromInputs(),this.updateRangeHighlight(),this.updateSummary())}}class n{constructor(e){this.root=e,this.dynamicMount=e.querySelector("#filters-container-new"),this.dataScript=document.querySelector("#filters-data"),this.goodieFilterCheckboxes=document.querySelectorAll(".goodie-filter-checkbox"),this.iconFilter=e.getAttribute("data-icon-filter")||"",this.iconChevron=e.getAttribute("data-icon-chevron")||"",this.renderMode=e.getAttribute("data-render-mode")||"default",this.resetUrl=this.dynamicMount&&this.dynamicMount.getAttribute("data-reset-url")||"",this.loaderTargetId=this.dynamicMount&&this.dynamicMount.getAttribute("data-loader-target")||"",this.mainToggle=null,this.container=null,this.sectionToggles=[],this.resetBtn=null,this.lastFiltersSignature=null,this.refreshScheduled=!1,this.pendingForceRefresh=!1,this.sectionStates={},this.containerVisible=null,this.rangeSliders=[],this.mutationObserver=null}updateDomRefs(){this.dynamicMount=this.root.querySelector("#filters-container-new"),this.dataScript=document.querySelector("#filters-data"),this.resetUrl=this.dynamicMount&&this.dynamicMount.getAttribute("data-reset-url")||"",this.loaderTargetId=this.dynamicMount&&this.dynamicMount.getAttribute("data-loader-target")||"",this.dynamicMount||(this.containerVisible=!1)}captureUiState(){if(this.container){this.containerVisible=this.container.classList.contains("-visible");var e=this.container.querySelectorAll("[data-filter-section]");Array.prototype.forEach.call(e,e=>{var t=e.getAttribute("data-feature-key");t&&(this.sectionStates[t]=!e.classList.contains("-collapsed"))})}}destroyRangeSliders(){this.rangeSliders&&this.rangeSliders.length?(this.rangeSliders.forEach(e=>{e&&"function"==typeof e.destroy&&e.destroy()}),this.rangeSliders=[]):this.rangeSliders=[]}initializeRangeSliders(){if(this.destroyRangeSliders(),this.container){var e=this.container.querySelectorAll("[data-range-slider]");this.rangeSliders=[],Array.prototype.forEach.call(e,e=>{var n=new t(e);n.init(),n.leftHandle&&n.rightHandle&&this.rangeSliders.push(n)})}}resolveFeatureKey(e,t){for(var n=[e&&e.feature_id,e&&e.feature_key,e&&e.key,e&&e.id],i=0;i<n.length;i++){var o=n[i];if(null!=o){var r=String(o);if(r)return r}}if(e&&e.feature_name){var a=String(e.feature_name);if(a)return a+"-"+t}return"feature-"+t}readFiltersPayload(){if(!this.dataScript)return null;var e=this.dataScript.textContent||this.dataScript.innerText||"";if(!e||!e.trim())return null;var t=e.trim();try{var n,i=JSON.parse(e);return n=Array.isArray(i)?i:i&&"object"==typeof i?Object.keys(i).map((function(e){var t=i[e];if(!t||"object"!=typeof t)return null;var n=Object.assign({},t);return!n.feature_id&&e&&(n.feature_id=e),n.feature_key=e,n})).filter(Boolean):[],{filters:Array.isArray(n)?n:[],signature:t}}catch(e){return null}}refreshFilters(e){this.updateDomRefs();var t=this.readFiltersPayload();if(t){var n=Array.isArray(t.filters)?t.filters:[],i=t.signature;if(!n.length)return this.dynamicMount&&(this.destroyRangeSliders(),this.dynamicMount.innerHTML=""),this.lastFiltersSignature=i,this.mainToggle=null,this.container=null,this.sectionToggles=[],void(this.resetBtn=null);!e&&this.lastFiltersSignature===i&&this.container||(this.lastFiltersSignature=i,this.renderFilters(n),this.bindFilterEvents())}}renderFilters(e){if(this.dynamicMount&&Array.isArray(e)){if(this.captureUiState(),this.destroyRangeSliders(),this.dynamicMount.innerHTML="",this.mainToggle=null,this.container=null,this.sectionToggles=[],this.resetBtn=null,!e.length)return this.containerVisible=!1,void(this.sectionStates={});var t,n=e.reduce((function(e,t){return e+(Number(t.selected_count)||0)}),0);t="boolean"==typeof this.containerVisible?this.containerVisible:n>0||!j();var i=document.createDocumentFragment(),o=null;"compact"!==this.renderMode&&(o=this.createMainToggle(n,t))&&i.appendChild(o);var r=this.createFiltersContainer("compact"===this.renderMode||t),a=[];if(e.forEach((e,t)=>{if(!e.hidden)if("compact"===this.renderMode){this.createCompactFilterItems(e,t).forEach(e=>{e&&r.appendChild(e)})}else{var i=this.createFilterSection(e,n,t);if(i){r.appendChild(i);var o=i.getAttribute("data-feature-key");o&&a.push(o)}}}),n>0&&"compact"!==this.renderMode){var s=this.createResetButton(n);s&&r.appendChild(s)}if(i.appendChild(r),this.dynamicMount.appendChild(i),this.containerVisible=t,a.length){var l={};a.forEach(e=>{l[e]=!0}),Object.keys(this.sectionStates).forEach(e=>{l[e]||delete this.sectionStates[e]})}window.ajaxLoader&&"function"==typeof window.ajaxLoader.bindLinks&&window.ajaxLoader.bindLinks(this.dynamicMount)}}bindFilterEvents(){if(this.mainToggle=this.root.querySelector("[data-filter-main-toggle]"),this.container=this.root.querySelector("[data-filters-container]"),this.sectionToggles=this.root.querySelectorAll("[data-filter-toggle]"),this.resetBtn=this.root.querySelector("[data-filter-reset]"),this.mainToggle&&this.container&&this.mainToggle.addEventListener("click",()=>{var e=function(e,t,n){var i=n||"aria-expanded",o=e.classList.toggle(t);return e.setAttribute(i,String(o)),o}(this.mainToggle,"-active");this.container.classList.toggle("-visible",e),this.containerVisible=e,e&&this.rangeSliders&&this.rangeSliders.length&&this.rangeSliders.forEach(e=>{e&&"function"==typeof e.refreshLayout&&e.refreshLayout()})}),this.sectionToggles.forEach(e=>{e.addEventListener("click",()=>{var t=e.closest("[data-filter-section]");if(t){t.classList.toggle("-collapsed");var n=!t.classList.contains("-collapsed");e.setAttribute("aria-expanded",String(n));var i=t.getAttribute("data-feature-key");i&&(this.sectionStates[i]=n),n&&this.rangeSliders&&this.rangeSliders.length&&this.rangeSliders.forEach(e=>{e&&e.root&&t.contains(e.root)&&"function"==typeof e.refreshLayout&&e.refreshLayout()})}}),e.addEventListener("keydown",t=>{"Enter"!==t.key&&" "!==t.key||(t.preventDefault(),e.click())})}),this.resetBtn&&this.resetBtn.addEventListener("click",()=>{for(var e of this.goodieFilterCheckboxes)e.checked=!1;var t=this.resetBtn.getAttribute("data-url-reset");this.loadUrl(t)}),this.container){this.container.addEventListener("change",e=>{var t=e.target;if(t instanceof HTMLInputElement&&("checkbox"===t.type||t.classList.contains("checkbox")||t.classList.contains("checkbox-input"))){var n=t.getAttribute("data-url-add"),i=t.getAttribute("data-url-remove"),o=t.getAttribute("data-filter-key");if(o){var r=t.getAttribute("data-filter-value");n||(n=this.buildFilterUrl(o,r,!0)),i||(i=this.buildFilterUrl(o,r,!1))}var a=t.checked?n:i;a&&this.loadUrl(a)}});var e=this.container.querySelectorAll(".filter_range_form[data-dynamic-range-form]");Array.prototype.forEach.call(e,e=>{e.addEventListener("submit",t=>{t.preventDefault();var n=e.getAttribute("data-filter-id"),i=e.querySelector('input[name$="[value_min]"]'),o=e.querySelector('input[name$="[value_max]"]');if(!n&&i){var r=i.name.match(/^feature\[(.+?)\]/);r&&(n=r[1])}if(n){var a=i&&i.value.trim()||"",s=o&&o.value.trim()||"",l=new URL(window.location.href),c="feature["+n+"][value_min]",u="feature["+n+"][value_max]";a?l.searchParams.set(c,a):l.searchParams.delete(c),s?l.searchParams.set(u,s):l.searchParams.delete(u),l.searchParams.delete("feature["+n+"]"),this.loadUrl(l.toString())}})}),this.container&&(this.containerVisible=this.container.classList.contains("-visible")),this.initializeRangeSliders()}}scheduleRefresh(e){if(e&&(this.pendingForceRefresh=!0),!this.refreshScheduled){this.refreshScheduled=!0;var t=()=>{this.refreshScheduled=!1;var e=this.pendingForceRefresh;this.pendingForceRefresh=!1,this.refreshFilters(e)};"function"==typeof window.requestAnimationFrame?window.requestAnimationFrame(t):setTimeout(t,0)}}startFiltersObserver(){if(!this.mutationObserver&&window.MutationObserver){this.mutationObserver=new MutationObserver(e=>{var t=!1,n=!1;e.forEach(e=>{if("childList"===e.type)e.addedNodes.forEach(e=>{e.nodeType===Node.ELEMENT_NODE&&("filters-data"===e.id||"function"==typeof e.querySelector&&e.querySelector("#filters-data"))&&(t=!0,n=!0)}),e.removedNodes.forEach(e=>{e.nodeType===Node.ELEMENT_NODE&&("filters-data"===e.id||"function"==typeof e.querySelector&&e.querySelector("#filters-data"))&&(t=!0,n=!0)});else if("characterData"===e.type){var i=e.target&&e.target.parentElement;i&&"filters-data"===i.id&&(t=!0)}}),t&&this.scheduleRefresh(n)});try{this.mutationObserver.observe(document.body,{childList:!0,characterData:!0,subtree:!0})}catch(e){this.refreshFilters(!0)}}}createMainToggle(e,t){var n=document.createElement("button");n.type="button",n.className="filter-toggle";var i="boolean"==typeof t?t:e>0;i&&n.classList.add("-active"),n.setAttribute("data-filter-main-toggle",""),n.setAttribute("aria-label","Filter"),n.setAttribute("aria-expanded",i?"true":"false");var o=document.createElement("div");if(o.className="button-content",this.iconFilter){var r=document.createElement("img");r.className="icon",r.src=this.iconFilter,r.alt="",r.width=24,r.height=24,o.appendChild(r)}var a=document.createElement("span");if(a.className="text",a.textContent="Filter",o.appendChild(a),n.appendChild(o),e>0){var s=document.createElement("span");s.className="badge",s.setAttribute("data-filter-count",""),s.textContent=String(e),n.appendChild(s)}return n}createFiltersContainer(e){var t=document.createElement("div");return t.className="filters-container",e&&t.classList.add("-visible"),t.setAttribute("data-filters-container",""),t}buildFilterUrl(e,t,n){const i=new URL(window.location.href),o=`feature[${e}]`;return n?i.searchParams.set(o,t):i.searchParams.delete(o),i.toString()}buildEnumOptions(e){const t=[];return(Array.isArray(e.values)?e.values:[]).forEach(n=>{if(!n||!n.feature_value_format)return;const i=document.createElement("label");i.className="filter-item checkbox-option";const o=document.createElement("input");o.type="checkbox",o.className="checkbox-input",n.selected&&(o.checked=!0),n.url_add&&o.setAttribute("data-url-add",n.url_add),n.url_remove&&o.setAttribute("data-url-remove",n.url_remove),e.feature_id&&o.setAttribute("data-filter-key",e.feature_id);const r=void 0!==n.feature_value&&null!==n.feature_value?n.feature_value:n.feature_value_format;o.setAttribute("data-filter-value",r);const a=document.createElement("span");a.className="checkmark";const s=document.createElement("span");s.className="label";let l=n.feature_value_format;"compact"===this.renderMode&&(l=l.replace(/\s*\(\d+\)\s*$/,"")),s.textContent=l;const c=document.createElement("span");c.className="quantity",c.textContent=n.anzahl,s.appendChild(c),i.appendChild(o),i.appendChild(a),i.appendChild(s),n.anzahl&&i.appendChild(c),t.push(i)}),t}buildRangeFilter(e){const t=document.createElement("div");t.className="filter-item range-filter";const n=document.createElement("div");n.className="slider",n.setAttribute("data-range-slider",""),void 0!==e.range_min&&n.setAttribute("data-min",e.range_min),void 0!==e.range_max&&n.setAttribute("data-max",e.range_max),e.range_behavior&&n.setAttribute("data-range-behavior",e.range_behavior),void 0!==e.range_behavior_threshold&&null!==e.range_behavior_threshold&&n.setAttribute("data-range-behavior-threshold",e.range_behavior_threshold),void 0!==e.range_behavior_factor&&null!==e.range_behavior_factor&&n.setAttribute("data-range-behavior-factor",e.range_behavior_factor);const i=e.range_prefix||e.value_prefix||"",o=e.range_suffix||e.value_suffix||"",r=void 0!==e.range_decimals&&null!==e.range_decimals?e.range_decimals:e.value_decimals;i&&n.setAttribute("data-prefix",i),o&&n.setAttribute("data-suffix",o),null!=r&&""!==r&&n.setAttribute("data-decimals",r);const a=document.createElement("div");a.className="slider-range",n.appendChild(a);const s=document.createElement("div");s.className="slider_handle left";const l=document.createElement("div");l.className="slider_handle right",n.appendChild(s),n.appendChild(l);const c=document.createElement("div");c.className="filter-range-summary",c.setAttribute("data-range-summary",""),c.textContent="";const u=document.createElement("form");u.method="get",u.className="filter_range_form",e.feature_id&&u.setAttribute("data-filter-id",e.feature_id),u.setAttribute("data-dynamic-range-form","");const d=e.selected_values||{},f=document.createElement("input");f.type="hidden",f.className="filter_range",e.feature_id&&(f.name=`feature[${e.feature_id}][value_min]`),void 0!==d.value_min&&null!==d.value_min&&(f.value=d.value_min);const h=document.createElement("input");h.type="hidden",h.className="filter_range",e.feature_id&&(h.name=`feature[${e.feature_id}][value_max]`),void 0!==d.value_max&&null!==d.value_max&&(h.value=d.value_max);const p=document.createElement("button");p.type="submit",p.className="filter_range_button";const g=e.range_apply_label||e.apply_label||"Auswahl anwenden";return p.textContent=g,u.appendChild(f),u.appendChild(h),u.appendChild(p),t.appendChild(n),t.appendChild(c),t.appendChild(u),t}createFilterSection(e,t,n){const i=document.createElement("div");i.className="filter-section _card",i.setAttribute("data-filter-section","");const o=this.resolveFeatureKey(e,n);i.setAttribute("data-feature-key",o);const r=Number(e.selected_count)||0,a=t>0&&r>0,s=Object.prototype.hasOwnProperty.call(this.sectionStates,o)?Boolean(this.sectionStates[o]):a;this.sectionStates[o]=s,s||i.classList.add("-collapsed");const l=document.createElement("div");l.className="filter-header",l.setAttribute("data-filter-toggle",""),l.setAttribute("role","button"),l.setAttribute("tabindex","0"),l.setAttribute("aria-expanded",s?"true":"false");const c=document.createElement("span");c.className="title",c.textContent=e.feature_name||"";const u=document.createElement("div");if(u.className="header-actions",r>0){const e=document.createElement("span");e.className="badge",e.textContent=String(r),u.appendChild(e)}if(this.iconChevron){const e=document.createElement("img");e.className="chevron",e.src=this.iconChevron,e.alt="",e.width=24,e.height=24,u.appendChild(e)}l.appendChild(c),l.appendChild(u);const d=document.createElement("div");d.className="filter-items";let f=!0;if("range"===e.type_frontend){const t=this.buildRangeFilter(e);t&&d.appendChild(t)}else{const t=this.buildEnumOptions(e);f=t.length>0,t.forEach(e=>{d.appendChild(e)})}return f||i.classList.add("-empty"),i.appendChild(l),i.appendChild(d),i}createCompactFilterItems(e,t){const n=[];if("range"===e.type_frontend){const t=this.buildRangeFilter(e);t&&n.push(t)}else{this.buildEnumOptions(e).forEach(e=>{n.push(e)})}return n}createResetButton(e){if(!this.resetUrl)return null;const t=document.createElement("button");t.type="button",t.className="filter-reset",t.setAttribute("data-filter-reset",""),t.setAttribute("data-url-reset",this.resetUrl);const n=document.createElement("span");n.className="text",n.textContent="Alle Filter zurücksetzen";const i=document.createElement("span");return i.className="badge",i.textContent=String(e),t.appendChild(n),t.appendChild(i),t}loadUrl(e){if(e){var t=window.ajaxLoader,n=this.loaderTargetId||this.dynamicMount&&this.dynamicMount.getAttribute("data-loader-target")||"",i=n?document.getElementById(n):null;if(t&&i){var o=e instanceof URL?e:null;if(!o)try{o=new URL(e,window.location.href)}catch(e){o=null}if(o)return void t.loadContent(o,i)}window.location.href=e}}init(){this.refreshFilters(!0),this.startFiltersObserver(),this.initGoodies(),this.initGoodieCatFilter()}initGoodies(){var e="feature[goodie_id][]";for(var t of(this.goodieFilterCheckboxes=document.querySelectorAll(".goodie-filter-checkbox"),this.goodieFilterCheckboxes)){var n=(t.getAttribute("data-goodie-id")||"").replace("goodie-","");n&&(t.checked=new URL(window.location.href).searchParams.has(e,n),t.addEventListener("change",()=>{var t=new URL(window.location.href);for(var n of(t.searchParams.delete(e),t.searchParams.delete("offset"),this.goodieFilterCheckboxes)){var i=(n.getAttribute("data-goodie-id")||"").replace("goodie-","");n.checked&&i&&t.searchParams.append(e,i)}this.loadUrl(t.toString())}))}}initGoodieCatFilter(){document.addEventListener("AjaxLoader:loaded",()=>{this.initGoodies()});var e=document.querySelectorAll(".cat-filter-checkbox");for(var t of e){var n=(t.getAttribute("data-cat-id")||"").replace("cat-","");if(n){var i=new URL(window.location.href).searchParams;t.checked=i.has("cat_ids[]",n),t.addEventListener("change",t=>{var n=window.location.href.indexOf("?");-1===n&&(n=window.location.href.length);var i=new URL(window.location.href.substring(0,n));for(var o of e)if(o==t.currentTarget){var r=(o.getAttribute("data-cat-id")||"").replace("cat-","");o.checked&&r&&i.searchParams.append("cat_ids[]",r)}else o.checked=!1;var a=document.querySelector("#filters-container-new"),s=a?a.getAttribute("data-loader-target"):"";this.loadUrl(i.toString(),s)})}}}}H((function(){document.querySelectorAll(".listing-filter, .category-filter").forEach(t=>{new e(t).init();new n(t).init()})}))}();var V=!1;r(".product_set_preview").each((function(e,t){V=!0;var n=r(t),i=n.find(".product_set_preview_inner"),o=null,a=20;i.width()>240&&(n.addClass("moreitems"),n.find(".prev_button, .next_button").hover((function(e){var t=n.width()-i.width()-20,s=-2;r(e.target).hasClass("prev_button")&&(s*=-1),o=window.setInterval((function(){(s>0&&a<=20||s<0&&a>=t)&&(a+=s),i.css("left",a+"px")}),10)}),(function(){o&&window.clearInterval(o)})));var s=n.closest(".product_box_2").find(".product_image img").get(0);n.find("img").bind("mouseover",(function(e){s.src=e.target.src.replace(/40x40/,"250x250")}))})),V&&r(".product_set_preview").each((function(e,t){var n=r(t);n.find(".product_set_preview_inner").width()>240?n.addClass("moreitems"):n.removeClass("moreitems")}));var U=class{constructor(){this.inited=new WeakSet}init(){var e=this;document.querySelectorAll(".category-cards-grid").forEach((function(t){e.enhance(t)}))}enhance(e){if(!this.inited.has(e)){this.inited.add(e);var t=e.querySelector(".action.show-all-categories");t&&(t.setAttribute("data-expand-container",""),t.setAttribute("data-remove-self",""))}}};function X(e,t){var n=document.querySelector(e);n&&n.querySelectorAll(".accordion-trigger").forEach((function(e){e.addEventListener("click",(function(n){n.preventDefault();var i=e.getAttribute("data-target");if(i){var o=document.getElementById(i);if(o){var r="true"===e.getAttribute("aria-expanded"),a=document.querySelectorAll(t+" .accordion-trigger"),s=document.querySelectorAll(t+" .accordion-content");r?(o.classList.remove("-active"),e.setAttribute("aria-expanded","false")):(a.forEach((function(t){t!==e&&t.setAttribute("aria-expanded","false")})),s.forEach((function(e){e.classList.remove("-active")})),o.classList.add("-active"),e.setAttribute("aria-expanded","true"));var l=new CustomEvent("accordion:toggle",{detail:{id:i,expanded:!r},bubbles:!0});e.dispatchEvent(l)}}}))}))}H((function(){var e;(function(){var e=document.querySelectorAll(".goodie-card");if(e.length){var t=0;Array.prototype.forEach.call(e,(function(e,n){var i=e.querySelector("input.exp"),o=e.querySelector(".bar > .nav"),r=e.querySelector(".bar"),a=e.querySelector(".bar .brand-link"),s=e.querySelector(".bar input.goodie-filter-checkbox");if(i&&o){var l=i.id?i.id.replace(/-exp$/,""):"goodie-card-"+ ++t,c=Array.prototype.filter.call(e.querySelectorAll(".collapsible"),(function(t){return t.parentNode===e}));if(c.length){var u=[];c.forEach((function(e,n){if(!e.id){for(var i=l+"-panel-"+(n+1);document.getElementById(i);)i=l+"-panel-"+(n+1)+"-"+ ++t;e.id=i}u.push(e.id)})),o.setAttribute("role","button"),o.hasAttribute("tabindex")||o.setAttribute("tabindex","0"),o.setAttribute("aria-controls",u.join(" "));var d=function(){var e=!!i.checked;o.setAttribute("aria-expanded",String(e)),c.forEach((function(t){t.setAttribute("aria-hidden",String(!e))}))},f=function(e){if("function"==typeof Event)e.dispatchEvent(new Event("change",{bubbles:!0}));else{var t=document.createEvent("HTMLEvents");t.initEvent("change",!0,!1),e.dispatchEvent(t)}},h=[];document.querySelectorAll(".page-home .goodie-card").length>0?h=j()?[2]:[0,1,2,3]:document.querySelectorAll(".page-goodies .goodie-card").length>0&&(h=j()?[0,1,2]:[0,1,2,3,4,5,6,7]),-1!==h.indexOf(n)&&(i.checked=!0),d(),i.addEventListener("change",d),o.addEventListener("keydown",(function(e){" "!==e.key&&"Spacebar"!==e.key&&"Enter"!==e.key||(e.preventDefault(),i.checked=!i.checked,f(i))})),r&&r.addEventListener("click",(function(e){a&&(e.target===a||a.contains(e.target))||s&&(e.target===s||s.contains(e.target))||o&&(e.target===o||o.contains(e.target))||(i.checked=!i.checked,f(i))}))}}}))}})(),function(){var e=document.querySelector(".page-goodies .search-input"),t=document.querySelector(".page-goodies .goodies-grid");if(e&&t){var n,i=t.querySelectorAll(".goodie-card");i.length&&(e.addEventListener("input",(function(){clearTimeout(n),n=setTimeout(o,200)})),e.addEventListener("search",o))}function o(){var t=e.value.trim().toLowerCase();Array.prototype.forEach.call(i,(function(e){if(t){var n=e.querySelector(".bar .brand .title"),i=e.querySelector(".desc .text"),o=n?n.textContent.toLowerCase():"",r=i?i.textContent.toLowerCase():"";e.style.display=-1!==o.indexOf(t)||-1!==r.indexOf(t)?"":"none"}else e.style.display=""}))}}(),(e=document.querySelector(".page-goodies .more-goodies-btn"))&&e.addEventListener("click",(function(){var t=document.querySelector(".page-goodies .goodies-grid");t&&(t.classList.add("is-expanded"),t.querySelectorAll("[data-hidden-card].d-none").forEach((function(e){e.classList.remove("d-none")})),e.parentNode&&e.parentNode.removeChild(e))}),{once:!0}),function(){var e=document.querySelector("#goodie-description-toggle"),t=document.querySelector("#goodie-description"),n=document.querySelector(".page-goodie-detail .toggle-container");if(e&&t)t.scrollHeight<=250?(t.classList.remove("-faded"),n&&(n.style.display="none")):e.addEventListener("click",(function(){t.classList.remove("-faded"),t.classList.add("-expanded"),e.setAttribute("aria-expanded","true"),n?n.style.display="none":e.style.display="none"}),{once:!0})}()}));var Y={init(e){e.querySelectorAll("form[data-ajax-form]").forEach(e=>this.setupForm(e))},setupForm(e){e.addEventListener("submit",t=>{t.preventDefault();const n=t.submitter||null;n&&n.hasAttribute("data-ajax-confirm")&&!confirm(n.getAttribute("data-ajax-confirm"))||this.handleSubmit(e,n)})},async handleSubmit(e,t){if(e.__submitting)return;if("function"==typeof e.checkValidity&&!e.checkValidity())return void("function"==typeof e.reportValidity&&e.reportValidity());e.__submitting=!0;const n=t||e.querySelector('button[type="submit"]'),i=e.dataset.ajaxEndpoint||"/contact_form_ajax/?action=send",o=e.dataset.ajaxSuccessMessage||"Vielen Dank! Ihre Anfrage ist bei uns eingegangen.";e.querySelectorAll(".field-error").forEach(e=>e.textContent=""),n&&(n.disabled=!0,n.classList.add("-loading"));const r=this.buildFormData(e,t);try{const t=await fetch(i,{method:"POST",headers:{Accept:"application/json"},body:r}),a=await t.json();"ok"===a.status?(e.reset(),A.show("success",a.message||o)):(a.errors&&this.showFieldErrors(e,a.errors),a.message&&A.show("error",a.message))}catch(e){A.show("error","Es gab ein Problem beim Senden. Bitte später erneut versuchen.")}finally{e.__submitting=!1,n&&(n.disabled=!1,n.classList.remove("-loading"))}},buildFormData(e,t){const n=new FormData(e),i=e.getAttribute("data-form-type");return i&&!n.has("form_type")&&n.append("form_type",i),t&&t.name&&n.append(t.name,t.value||"1"),n},showFieldErrors(e,t){Object.keys(t).forEach(n=>{const i=e.querySelector('[data-error-for="'+n+'"]');i&&(i.textContent=t[n])})}};H(()=>{!function(){const e=document.querySelector(".service-anfrage-page");if(!e)return;const t=e.querySelector(".anfrage-sidebar"),n=e.querySelector(".anfrage-content"),i=e.querySelector("h1");if(!t||!n||!i)return;const o=t.querySelectorAll(".nav-item"),r=n.querySelector(".welcome"),a=n.querySelectorAll(".section-content"),s=e.querySelector(".back-button"),l=i.textContent,c=new Set,u=e=>{if(!e)return;const t=e.getBoundingClientRect().top+window.pageYOffset-((parseInt((getComputedStyle(document.documentElement).getPropertyValue("--header-height")||"72px").replace("px",""),10)||72)+(innerWidth<1024?16:24));window.scrollTo({top:t<0?0:t,behavior:"auto"})},d=(e,t,n)=>{const i=new URL(window.location.href);e?i.searchParams.set("section",e):i.searchParams.delete("section"),t?i.searchParams.set("sub",t):i.searchParams.delete("sub");const o=i.pathname+(i.search?i.search:"");(n?history.replaceState:history.pushState).call(history,null,"",o)},f=e=>o.forEach(t=>t.setAttribute("aria-current",t===e?"page":"false")),h=e=>{j()&&(t.classList.toggle("-hidden",e),n.classList.toggle("-visible",e),s&&s.classList.toggle("-visible",e))},p=(e,o,s)=>{void 0===s&&(s=!0),r&&r.classList.remove("-active"),a.forEach(t=>t.classList.toggle("-active",t.dataset.section===e));const l=n.querySelector(".section-content.-active");if(l){const t=l.getAttribute("data-title");t&&(i.textContent=t),c.has(e)||(l.querySelector(".sub-accordions")&&X("#"+e+" .sub-accordions","#"+e),c.add(e))}f(o||t.querySelector('.nav-item[data-section="'+e+'"]')),s&&d(e,null),h(!0)},g=e=>{void 0===e&&(e=!0),a.forEach(e=>e.classList.remove("-active")),r&&r.classList.add("-active"),i.textContent=l,f(null),e&&d(null,null),h(!1)},m=n.querySelector(".section-content.-active");if(m){const e=m.getAttribute("id"),o=m.getAttribute("data-title");o&&(i.textContent=o),m.querySelector(".sub-accordions")&&(X("#"+e+" .sub-accordions","#"+e),c.add(e)),j()&&(t.classList.add("-hidden"),n.classList.add("-visible"),s&&s.classList.add("-visible"));const r=m.querySelector('.accordion-trigger[aria-expanded="true"]');r&&u(r)}n.addEventListener("accordion:toggle",e=>{const t=n.querySelector(".section-content.-active");if(!t)return;const i=t.getAttribute("id"),o=e.detail&&e.detail.id?e.detail.id:null;e.detail&&e.detail.expanded?d(i,o):d(i,null)}),window.addEventListener("popstate",()=>{const e=new URLSearchParams(location.search),t=e.get("section")||"",i=e.get("sub")||"";if(!t)return void g(!1);p(t,null,!1);const o=n.querySelector(".section-content.-active");if(o)if(i){const e=o.querySelector('.accordion-trigger[data-target="'+i+'"]');e&&"true"!==e.getAttribute("aria-expanded")&&e.click()}else{const e=o.querySelector('.accordion-trigger[aria-expanded="true"]');e&&e.click()}}),o.forEach(e=>e.addEventListener("click",()=>p(e.dataset.section,e))),s&&s.addEventListener("click",g),Y.init(e)}(),function(){const e=document.querySelector(".service-center-page");e&&(X(".service-accordions",".service-center-page"),Y.init(e),document.querySelectorAll('input[type="file"]').forEach(e=>{e.addEventListener("change",e=>{const t=document.querySelector('label[for="'+e.target.id+'"]');if(t&&e.target.files&&e.target.files.length){const n=t.textContent.replace(/\s*\(.*\)/,"").trim();t.textContent=n+" ("+e.target.files[0].name+")"}})}))}()}),function(){var e=!1;function t(e){var t=e.target&&e.target.closest("a.gpsr[data-gpsr]");if(t){e.preventDefault();var n,i,o=t.getAttribute("data-gpsr");o&&(n=o,(i=new l).setClass("modalWindowSmall modalWindowInner"),i.ajaxShow(n))}}H((function(){(function(e,t){var n=document.querySelector(e);if(n){var i=t||".accordion-trigger";n.addEventListener("click",(function(e){var t=e.target&&e.target.closest?e.target.closest(i):null;if(t&&n.contains(t)){var o=t.getAttribute("data-target");if(o){var r=o.startsWith("section-")?o:"section-"+o,a=document.getElementById(r);if(a||(a=document.getElementById(o)))"true"===t.getAttribute("aria-expanded")?(a.classList.remove("-active"),t.setAttribute("aria-expanded","false")):(a.classList.add("-active"),t.setAttribute("aria-expanded","true"))}}}))}})(".product-details-nav",".detail-nav-item"),function(){var e=document.getElementById("section-related");if(!e)return;var t=e.querySelector("#related-show-all");if(!t)return;t.addEventListener("click",(function(){e.classList.add("is-expanded"),t.setAttribute("aria-expanded","true"),t.parentNode&&t.parentNode.removeChild(t)}))}(),function(){const e=document.querySelector(".page-product");if(!e)return;const t=e.querySelector("#service_price_service"),n=e.querySelector("#service_price_product"),i=e.querySelector("#service_price_total"),o=e.querySelectorAll(".extra-service-checkbox"),r=e.querySelector(".product-sticky-cta");o.forEach(e=>{e.addEventListener("change",a=>{!function e(t){t=parseInt(t),o.forEach(n=>{if(-1!==JSON.parse(n.getAttribute("data-dependencies")).indexOf(t)){const t=n.getAttribute("data-extra-service-id");a.target.checked?n.parentElement.parentElement.classList.remove("-blocked"):(n.checked=!1,e(t),n.parentElement.parentElement.classList.add("-blocked")),n.disabled=!a.target.checked}})}(e.getAttribute("data-extra-service-id"));let s=0;o.forEach(e=>{if(e.checked&&(s+=parseFloat(e.getAttribute("data-price"))||0),r){const t=e.getAttribute("data-extra-service-id");r.querySelector('input[name="service['+t+']"]').value=e.checked?"1":"0"}}),t.innerText=R.output.formatPrice(s)+"*",i.innerText=R.output.formatPrice(parseFloat(n.getAttribute("data-price"))+s)+"*",r&&(r.querySelector(".price .amount").innerText=i.innerText)})})}(),function(){var e=document.querySelector(".product-box > .media");if(!e)return;var t=e.querySelector(".hero"),n=e.querySelector(".photo"),i=Array.prototype.slice.call(e.querySelectorAll(".thumbs .thumb")),o=e.querySelectorAll(".dots .dot");if(!t||!n||!i.length)return;var r=i.map((function(e){var t,n=e.getAttribute("href")||e.href||"";return{high:n,prev:/_highres\./i.test(t=n)?t.replace(/_highres\./i,"_nor."):/_50x50\./i.test(t)?t.replace(/_50x50\./i,"_nor."):t,el:e}}));function a(e){if(r.length){var a=(e+r.length)%r.length,s=r[a];s.prev&&n.setAttribute("src",s.prev),s.high&&t.setAttribute("href",s.high),function(e){i.forEach((function(t,n){t.setAttribute("aria-current",n===e?"true":"false")})),o.forEach((function(t,n){t.setAttribute("aria-current",n===e?"true":"false")}))}(a)}}i.forEach((function(e,t){e.addEventListener("mouseenter",(function(){a(t)}),{passive:!0}),e.addEventListener("mouseover",(function(){a(t)}),{passive:!0})})),o.forEach((function(e,t){e.addEventListener("click",(function(){a(t)}))}));var s=0,l=0,c=!1,u=0;var d=t;d.addEventListener("touchstart",(function(e){j()&&(c=!0,l=0,s=e.clientX||(e.touches&&e.touches[0]?e.touches[0].clientX:0),u=Date.now())}),{passive:!0}),d.addEventListener("touchmove",(function(e){if(c){var t=e.clientX||(e.touches&&e.touches[0]?e.touches[0].clientX:0);l=t-s}}),{passive:!0}),d.addEventListener("touchend",(function(e){if(c){c=!1;var t,i,o=Date.now()-u,s=(t=(n.currentSrc||n.src||"").split("/").pop(),(i=r.findIndex((function(e){return e.prev.split("/").pop()===t})))>=0?i:0);Math.abs(l)>=30&&o<300&&(e.preventDefault(),a(l<0?s+1:s-1))}}),{passive:!1}),d.addEventListener("touchcancel",(function(){c=!1}),{passive:!0})}(),function(){const e=document.querySelector(".page-product");if(!e)return;const t=e.getAttribute("data-product-id");if(!t)return;!function e(){window.RecentlyViewed?window.RecentlyViewed.track(t):setTimeout(e,100)}()}(),function(){var e=document.querySelector(".product-sticky-cta");if(!e)return;var t=document.querySelector(".product-box > .actions, .product-box > .details > .actions");t||(t=document.querySelector(".page-product .std_form.put_in_basket"));if(!t)return;function n(){window.pageYOffset>1080?e.classList.add("show"):e.classList.remove("show")}window.addEventListener("scroll",(function(){!1||(requestAnimationFrame(n),!0)}),{passive:!0});var i=t.querySelector('input[name="menge"]'),o=e.querySelector('input[name="menge"]');if(!i||!o)return;var r=t.querySelector(".qty .button:first-child"),a=t.querySelector(".qty .button:last-child"),s=e.querySelector(".qty .button:first-child"),l=e.querySelector(".qty .button:last-child");function c(e,t){if(e&&t){var n=parseInt(e.value,10);!isNaN(n)&&n>=1&&(t.value=n)}}i&&o&&(i.addEventListener("input",(function(){c(i,o)})),i.addEventListener("change",(function(){c(i,o)})),o.addEventListener("input",(function(){c(o,i)})),o.addEventListener("change",(function(){c(o,i)})));r&&o&&r.addEventListener("click",(function(){setTimeout((function(){c(i,o)}),10)}));a&&o&&a.addEventListener("click",(function(){setTimeout((function(){c(i,o)}),10)}));s&&i&&s.addEventListener("click",(function(){setTimeout((function(){c(o,i)}),10)}));l&&i&&l.addEventListener("click",(function(){setTimeout((function(){c(o,i)}),10)}))}(),function(){var e=document.getElementById("service_quantity");if(!e)return;if((parseInt(e.getAttribute("data-cart-qty"))||0)>0)return;var t=document.querySelector(".product-box .std_form.put_in_basket");if(!t)return;var n=t.querySelector('input[name="menge"]');if(!n)return;var i=t.querySelector(".qty .button:first-child"),o=t.querySelector(".qty .button:last-child");function r(){var t=parseInt(n.value);t&&t>=1&&(e.value=t)}n.addEventListener("input",r),n.addEventListener("change",r),i&&i.addEventListener("click",(function(){setTimeout(r,10)}));o&&o.addEventListener("click",(function(){setTimeout(r,10)}))}(),function(){if(e)return;if(!document.querySelector(".page-product"))return;e=!0,document.addEventListener("click",t,!0)}()}))}();n(2),n(3),n(4);const G=e=>"object"==typeof e&&null!==e&&e.constructor===Object&&"[object Object]"===Object.prototype.toString.call(e),Z=e=>"string"==typeof e,Q=e=>e&&null!==e&&e instanceof Element&&"nodeType"in e,K=function(e){var t=(new DOMParser).parseFromString(e,"text/html").body;if(t.childElementCount>1){for(var n=document.createElement("div");t.firstChild;)n.appendChild(t.firstChild);return n}let i=t.firstChild;return!i||i instanceof HTMLElement?i:((n=document.createElement("div")).appendChild(i),n)},J=function(e,t){return!e||e===document.body||t&&e===t?null:function(e){if(!(e&&e instanceof Element&&e.offsetParent))return!1;const t=e.scrollHeight>e.clientHeight,n=window.getComputedStyle(e).overflowY,i=-1!==n.indexOf("hidden"),o=-1!==n.indexOf("visible");return t&&!i&&!o}(e)?e:J(e.parentElement,t)},ee=(e=!0,t="--f-scrollbar-compensate",n="--f-body-margin",i="hide-scrollbar")=>{const o=document,r=o.body,a=o.documentElement;if(e){if(r.classList.contains(i))return;let e=window.innerWidth-a.getBoundingClientRect().width;e<0&&(e=0),a.style.setProperty(t,e+"px");const o=parseFloat(window.getComputedStyle(r).marginRight);o&&r.style.setProperty(n,o+"px"),r.classList.add(i)}else r.classList.remove(i),r.style.setProperty(n,""),o.documentElement.style.setProperty(t,"")},te=(e,...t)=>{const n=t.length;for(let i=0;i<n;i++){const n=t[i]||{};Object.entries(n).forEach(([t,n])=>{const i=Array.isArray(n)?[]:{};e[t]||Object.assign(e,{[t]:i}),G(n)?Object.assign(e[t],te(e[t],n)):Array.isArray(n)?Object.assign(e,{[t]:[...n]}):Object.assign(e,{[t]:n})})}return e};function ne(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}const ie=function(e=0,t=0,n=0){return Math.max(Math.min(t,n),e)},oe=function(e=0,t=0,n=0,i=0,o=0,r=!1){const a=(e-t)/(n-t)*(o-i)+i;return r?i<o?ie(i,a,o):ie(o,a,i):a},re=(e,t="")=>{e&&e.classList&&t.split(" ").forEach(t=>{t&&e.classList.add(t)})},ae=(e,t="")=>{e&&e.classList&&t.split(" ").forEach(t=>{t&&e.classList.remove(t)})},se=(e,t="",n)=>{e&&e.classList&&t.split(" ").forEach(t=>{t&&e.classList.toggle(t,n||!1)})};function le(e){return G(e)||Array.isArray(e)}function ce(e,t){const n=Object.keys(e),i=Object.keys(t);return n.length===i.length&&n.every(n=>{const i=e[n],o=t[n];return"function"==typeof i?""+i==""+o:le(i)&&le(o)?ce(i,o):i===o})}const ue=function(e){for(const t of ge)t.getState()===de.Running&&t.tick(ve?e-ve:0);ve=e,me=window.requestAnimationFrame(ue)};var de,fe,he,pe;(pe=de||(de={}))[pe.Initializing=0]="Initializing",pe[pe.Running=1]="Running",pe[pe.Paused=2]="Paused",pe[pe.Completed=3]="Completed",pe[pe.Destroyed=4]="Destroyed",function(e){e[e.Spring=0]="Spring",e[e.Ease=1]="Ease"}(fe||(fe={})),function(e){e[e.Loop=0]="Loop",e[e.Reverse=1]="Reverse"}(he||(he={}));const ge=new Set;let me=null,ve=0;function ye(){let e=de.Initializing,t=fe.Ease,n=0,i=0,o=ye.Easings.Linear,r=500,a=0,s=0,l=0,c=0,u=1/0,d=.01,f=.01,h=!1,p={},g=null,m={},v={},y={},b=0,w=0,x=he.Loop,E=ye.Easings.Linear;const S=new Map;function L(e,...t){for(const n of S.get(e)||[])n(...t)}function T(e){return i=0,e?g=setTimeout(()=>{C()},e):C(),M}function C(){e=de.Running,L("start",m,v)}function A(){if(e=de.Completed,y={},L("end",m),e===de.Completed)if(n<b){if(n++,x===he.Reverse){const e=Object.assign({},p);p=Object.assign({},v),v=e}T(w)}else n=0;return M}const M={getState:function(){return e},easing:function(e){return o=e,t=fe.Ease,y={},M},duration:function(e){return r=e,M},spring:function(e={}){t=fe.Spring;const{velocity:n,mass:i,tension:o,friction:r,restDelta:p,restSpeed:g,maxSpeed:m,clamp:v}=Object.assign(Object.assign({},{velocity:0,mass:1,tension:170,friction:26,restDelta:.1,restSpeed:.1,maxSpeed:1/0,clamp:!0}),e);return a=n,s=i,l=o,c=r,f=p,d=g,u=m,h=v,y={},M},isRunning:function(){return e===de.Running},isSpring:function(){return t===fe.Spring},from:function(e){return m=Object.assign({},e),M},to:function(e){return v=e,M},repeat:function(e,t=0,n=he.Loop,i){return b=e,w=t,x=n,E=i||o,M},on:function(e,t){var n,i;return n=e,i=t,S.set(n,[...S.get(n)||[],i]),M},off:function(e,t){var n,i;return n=e,i=t,S.has(n)&&S.set(n,S.get(n).filter(e=>e!==i)),M},start:function(t){return ce(m,v)||(e=de.Initializing,p=Object.assign({},m),ge.add(this),me||(me=window.requestAnimationFrame(ue)),T(t)),M},pause:function(){return g&&(clearTimeout(g),g=null),e===de.Running&&(e=de.Paused,L("pause",m)),M},end:A,tick:function(n){n>50&&(n=50),i+=n;let g=0,b=!1;if(e!==de.Running)return M;if(t===fe.Ease){g=ie(0,i/r,1),b=1===g;const e=x===he.Reverse?E:o;for(const t in m)m[t]=p[t]+(v[t]-p[t])*e(g)}if(t===fe.Spring){const e=.001*n;let t=0;for(const n in m){const i=v[n];let o=m[n];if("number"!=typeof i||isNaN(i)||"number"!=typeof o||isNaN(o))continue;if(Math.abs(i-o)<=f){m[n]=i,y[n]=0;continue}y[n]||("object"==typeof a&&"number"==typeof a[n]?y[n]=a[n]:y[n]="number"==typeof a?a:0);let r=y[n];r=ie(-1*Math.abs(u),r,Math.abs(u));const p=r*s*c;r+=((o>i?-1:1)*(Math.abs(i-o)*l)-p)/s*e,o+=r*e;const g=m[n]>i?o<i:o>i;let b=Math.abs(r)<d&&Math.abs(i-o)<=f;h&&g&&(b=!0),b?(o=i,r=0):t++,m[n]=o,y[n]=r}b=!t}const w=Object.assign({},v);return L("step",m,p,v,g),b&&e===de.Running&&ce(v,w)&&(e=de.Completed,A()),M},getStartValues:function(){return p},getCurrentValues:function(){return m},getCurrentVelocities:function(){return y},getEndValues:function(){return v},destroy:function(){e=de.Destroyed,g&&(clearTimeout(g),g=null),p=m=v={},ge.delete(this)}};return M}function be(e){return"undefined"!=typeof TouchEvent&&e instanceof TouchEvent}function we(e,t){const n=[],i=be(e)?e[t]:e instanceof MouseEvent&&("changedTouches"===t||"mouseup"!==e.type)?[e]:[];for(const e of i)n.push({x:e.clientX,y:e.clientY,ts:Date.now()});return n}function xe(e){return we(e,"touches")}function Ee(e){return we(e,"targetTouches")}function Se(e){return we(e,"changedTouches")}function Le(e){const t=e[0],n=e[1]||t;return{x:(t.x+n.x)/2,y:(t.y+n.y)/2,ts:n.ts}}function Te(e){const t=e[0],n=e[1]||e[0];return t&&n?-1*Math.sqrt((n.x-t.x)*(n.x-t.x)+(n.y-t.y)*(n.y-t.y)):0}ye.destroy=()=>{for(const e of ge)e.destroy();me&&(cancelAnimationFrame(me),me=null)},ye.Easings={Linear:function(e){return e},EaseIn:function(e){return 0===e?0:Math.pow(2,10*e-10)},EaseOut:function(e){return 1===e?1:1-Math.pow(2,-10*e)},EaseInOut:function(e){return 0===e?0:1===e?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2}};const Ce=e=>{e.cancelable&&e.preventDefault()},Ae={passive:!1},Me={panThreshold:5,swipeThreshold:3,ignore:["textarea","input","select","[contenteditable]","[data-selectable]","[data-draggable]"]};let ke=!1,_e=!0;const Pe=(e,t)=>{let n,i,o,r=Object.assign(Object.assign({},Me),t),a=[],s=[],l=[],c=!1,u=!1,d=!1,f=!1,h=0,p=0,g=0,m=0,v=0,y=0,b=0,w=0,x=0,E=[],S=0,L=0;const T=new Map;function C(e){const t=Te(s),r=Te(l),d=t&&r?t/r:0,f=Math.abs(b)>Math.abs(w)?b:w,h={srcEvent:n,isPanRecognized:c,isSwipeRecognized:u,firstTouch:a,previousTouch:l,currentTouch:s,deltaX:g,deltaY:m,offsetX:v,offsetY:y,velocityX:b,velocityY:w,velocity:f,angle:x,axis:o,scale:d,center:i};for(const t of T.get(e)||[])t(h)}function A(e){const t=Date.now();if(E=E.filter(e=>!e.ts||e.ts>t-100),e&&E.push(e),b=0,w=0,E.length>3){const e=E[0],t=E[E.length-1];if(e&&t){const n=t.x-e.x,i=t.y-e.y,o=e.ts&&t.ts?t.ts-e.ts:0;o>0&&(b=Math.abs(n)>3?n/(o/30):0,w=Math.abs(i)>3?i/(o/30):0)}}}function M(e){if("undefined"!=typeof MouseEvent&&e instanceof MouseEvent){if(ke)return}else ke=!0;const t=e.composedPath()[0],o=r.ignore.join(",");if(t.matches(o)||t.closest(o))return;if("undefined"!=typeof MouseEvent&&e instanceof MouseEvent){if(!e.buttons||0!==e.button)return;Ce(e)}e instanceof MouseEvent&&(window.addEventListener("mousemove",k),window.addEventListener("mouseup",_)),window.addEventListener("blur",P),n=e,s=Ee(e),a=[...s],l=[],p=s.length,i=Le(s),1===p&&(c=!1,u=!1,d=!1),p&&A(Le(s));const g=Date.now(),m=g-(h||g);f=m>0&&m<=250&&1===p,h=g,clearTimeout(S),C("start")}function k(e){var t;if(!a.length)return;if(e.defaultPrevented)return;n=e,l=[...s],s=xe(e);const u=Le(l),f=Le(xe(e));if(A(f),p=s.length,i=f,l.length===s.length?(g=f.x-u.x,m=f.y-u.y):(g=0,m=0),a.length){const e=Le(a);v=f.x-e.x,y=f.y-e.y}if(s.length>1){const e=Te(s),t=Te(l);Math.abs(e-t)>=.1&&(d=!0,C("pinch"))}c||(c=Math.abs(v)>r.panThreshold||Math.abs(y)>r.panThreshold,c&&(_e=!1,clearTimeout(L),L=0,x=Math.abs(180*Math.atan2(y,v)/Math.PI),o=x>45&&x<135?"y":"x",a=[...s],l=[...s],v=0,y=0,g=0,m=0,null===(t=window.getSelection())||void 0===t||t.removeAllRanges(),C("panstart"))),c&&(g||m)&&C("pan"),C("move")}function _(e){if(n=e,!a.length)return;const t=Ee(e),o=Se(e);if(p=t.length,i=Le(o),o.length&&A(Le(o)),l=[...s],s=[...t],a=[...t],p>0)C("end"),c=!1,u=!1,E=[];else{const e=r.swipeThreshold;(Math.abs(b)>e||Math.abs(w)>e)&&(u=!0),c&&C("panend"),u&&C("swipe"),c||u||d||(C("tap"),f?C("doubleTap"):S=setTimeout((function(){C("singleTap")}),250)),C("end"),O()}}function P(){clearTimeout(S),O(),c&&C("panend"),C("end")}function O(){ke=!1,c=!1,u=!1,f=!1,p=0,E=[],s=[],l=[],a=[],g=0,m=0,v=0,y=0,b=0,w=0,x=0,o=void 0,window.removeEventListener("mousemove",k),window.removeEventListener("mouseup",_),window.removeEventListener("blur",P),_e||L||(L=setTimeout(()=>{_e=!0,L=0},100))}function j(e){const t=e.target;ke=!1,t&&!e.defaultPrevented&&(_e||(Ce(e),e.stopPropagation()))}const q={init:function(){if(e)return e.addEventListener("click",j,Ae),e.addEventListener("mousedown",M,Ae),e.addEventListener("touchstart",M,Ae),e.addEventListener("touchmove",k,Ae),e.addEventListener("touchend",_),e.addEventListener("touchcancel",_),q},on:function(e,t){return function(e,t){T.set(e,[...T.get(e)||[],t])}(e,t),q},off:function(e,t){return T.has(e)&&T.set(e,T.get(e).filter(e=>e!==t)),q},isPointerDown:()=>p>0,destroy:function(){clearTimeout(S),clearTimeout(L),L=0,e&&(e.removeEventListener("click",j,Ae),e.removeEventListener("mousedown",M,Ae),e.removeEventListener("touchstart",M,Ae),e.removeEventListener("touchmove",k,Ae),e.removeEventListener("touchend",_),e.removeEventListener("touchcancel",_)),e=null,O()}};return q};Pe.isClickAllowed=()=>_e;const Oe={IMAGE_ERROR:"This image couldn't be loaded. <br /> Please try again later.",MOVE_UP:"Move up",MOVE_DOWN:"Move down",MOVE_LEFT:"Move left",MOVE_RIGHT:"Move right",ZOOM_IN:"Zoom in",ZOOM_OUT:"Zoom out",TOGGLE_FULL:"Toggle zoom level",TOGGLE_1TO1:"Toggle zoom level",ITERATE_ZOOM:"Toggle zoom level",ROTATE_CCW:"Rotate counterclockwise",ROTATE_CW:"Rotate clockwise",FLIP_X:"Flip horizontally",FLIP_Y:"Flip vertically",RESET:"Reset",TOGGLE_FS:"Toggle fullscreen"},je=e=>{e.cancelable&&e.preventDefault()},qe=(e,t=1e4)=>(e=parseFloat(e+"")||0,Math.round((e+Number.EPSILON)*t)/t),Re=e=>e instanceof HTMLImageElement;var Ie,De,Fe;(Fe=Ie||(Ie={})).Reset="reset",Fe.Zoom="zoom",Fe.ZoomIn="zoomIn",Fe.ZoomOut="zoomOut",Fe.ZoomTo="zoomTo",Fe.ToggleCover="toggleCover",Fe.ToggleFull="toggleFull",Fe.ToggleMax="toggleMax",Fe.IterateZoom="iterateZoom",Fe.Pan="pan",Fe.Swipe="swipe",Fe.Move="move",Fe.MoveLeft="moveLeft",Fe.MoveRight="moveRight",Fe.MoveUp="moveUp",Fe.MoveDown="moveDown",Fe.RotateCCW="rotateCCW",Fe.RotateCW="rotateCW",Fe.FlipX="flipX",Fe.FlipY="flipY",Fe.ToggleFS="toggleFS",function(e){e.Cover="cover",e.Full="full",e.Max="max"}(De||(De={}));const Ne={x:0,y:0,scale:1,angle:0,flipX:1,flipY:1},He={bounds:!0,classes:{container:"f-panzoom",wrapper:"f-panzoom__wrapper",content:"f-panzoom__content",viewport:"f-panzoom__viewport"},clickAction:Ie.ToggleFull,dblClickAction:!1,gestures:{},height:"auto",l10n:Oe,maxScale:4,minScale:1,mouseMoveFactor:1,panMode:"drag",protected:!1,singleClickAction:!1,spinnerTpl:'<div class="f-spinner"></div>',wheelAction:Ie.Zoom,width:"auto"};let Be,ze=0,$e=0,We=0;const Ve=(e,t={},n={})=>{let i,o,r,a,s,l,c,u,d=0,f=Object.assign(Object.assign({},He),t),h={},p=Object.assign({},Ne),g=Object.assign({},Ne);const m=[];function v(e){let t=f[e];return t&&"function"==typeof t?t(ue):t}function y(){return e&&e.parentElement&&i&&3===d}const b=new Map;function w(e,...t){const n=[...b.get(e)||[]];f.on&&n.push(f.on[e]);for(const e of n)e&&e instanceof Function&&e(ue,...t);"*"!==e&&w("*",e,...t)}function x(e){if(!y())return;const t=e.target;if(J(t))return;const n=Date.now(),i=[-e.deltaX||0,-e.deltaY||0,-e.detail||0].reduce((function(e,t){return Math.abs(t)>Math.abs(e)?t:e})),o=ie(-1,i,1);w("wheel",e,o);const r=v("wheelAction");if(!r)return;if(e.defaultPrevented)return;const a=g.scale;let s=a*(o>0?1.5:.5);if(r===Ie.Zoom){const t=Math.abs(e.deltaY)<100&&Math.abs(e.deltaX)<100;if(n-$e<(t?200:45))return void je(e);$e=n;const i=j(),r=D();if(qe(s)<qe(i)&&qe(a)<=qe(i)?(We+=Math.abs(o),s=i):qe(s)>qe(r)&&qe(a)>=qe(r)?(We+=Math.abs(o),s=r):(We=0,s=ie(i,s,r)),We>7)return}switch(je(e),r){case Ie.Pan:B(r,{srcEvent:e,deltaX:2*-e.deltaX,deltaY:2*-e.deltaY});break;case Ie.Zoom:B(Ie.ZoomTo,{srcEvent:e,scale:s,center:{x:e.clientX,y:e.clientY}});break;default:B(r,{srcEvent:e})}}function E(t){var n,o;const r=t.composedPath()[0];if(!Pe.isClickAllowed())return;if(!Q(r)||t.defaultPrevented)return;if(!(null==e?void 0:e.contains(r)))return;if(r.hasAttribute("disabled")||r.hasAttribute("aria-disabled"))return;const a=r.closest("[data-panzoom-action]"),s=null===(n=null==a?void 0:a.dataset)||void 0===n?void 0:n.panzoomAction,l=(null===(o=null==a?void 0:a.dataset)||void 0===o?void 0:o.panzoomValue)||"";if(s){switch(je(t),s){case Ie.ZoomTo:case Ie.ZoomIn:case Ie.ZoomOut:B(s,{scale:parseFloat(l||"")||void 0});break;case Ie.MoveLeft:case Ie.MoveRight:B(s,{deltaX:parseFloat(l||"")||void 0});break;case Ie.MoveUp:case Ie.MoveDown:B(s,{deltaY:parseFloat(l||"")||void 0});break;case Ie.ToggleFS:oe();break;default:B(s)}return}if(!(null==i?void 0:i.contains(r)))return;const c={srcEvent:t};if(B(v("clickAction"),c),v("dblClickAction")){const e=Date.now(),t=e-(ze||e);ze=e,t>0&&t<=250?(Be&&(clearTimeout(Be),Be=void 0),B(v("dblClickAction"),c)):Be=setTimeout(()=>{B(v("singleClickAction"),c)},250)}}function S(e){if(u=e,!y()||!_())return;if(p.scale<=1||g.scale<=1)return;if(((null==i?void 0:i.dataset.animationName)||"").indexOf("zoom")>-1)return;const t=P(g.scale);if(!t)return;const{x:n,y:o}=t;B(Ie.Pan,{deltaX:n-g.x,deltaY:o-g.y})}function L(){var t;e&&(ae(e,"is-loading"),null===(t=e.querySelector(".f-spinner"))||void 0===t||t.remove())}function T(){if(!e||!o)return;if(L(),Re(o)&&(!o.complete||!o.naturalWidth))return d=2,null==i||i.classList.add("has-error"),void w("error");w("loaded");const{width:t,height:n}=M();Re(o)&&(o.setAttribute("width",t+""),o.setAttribute("height",n+"")),i&&(ae(i,"has-error"),Re(o)&&(i.setAttribute("width",t+""),i.setAttribute("height",n+""),i.style.aspectRatio=""+(t/n||""))),l=ye().on("start",(e,t)=>{void 0!==t.angle&&(t.angle=90*Math.round(t.angle/90)),void 0!==t.flipX&&(t.flipX=t.flipX>0?1:-1),void 0!==t.flipY&&(t.flipY=t.flipY>0?1:-1),g=Object.assign(Object.assign({},Ne),t),H(),w("animationStart")}).on("pause",e=>{g=Object.assign(Object.assign({},Ne),e)}).on("step",e=>{if(!y())return void(null==l||l.end());if(p=Object.assign(Object.assign({},Ne),e),_()||!v("bounds")||ee()||g.scale>p.scale||g.scale<q())return void z();const t=F(g.scale);let n=!1,i=!1,o=!1,r=!1;p.x<t.x[0]&&(n=!0),p.x>t.x[1]&&(i=!0),p.y<t.y[0]&&(r=!0),p.y>t.y[1]&&(o=!0);let a=!1,s=!1,c=!1,u=!1;g.x<t.x[0]&&(a=!0),g.x>t.x[1]&&(s=!0),g.y<t.y[0]&&(u=!0),g.y>t.y[1]&&(c=!0);let d=!1;(i&&s||n&&a)&&(g.x=ie(t.x[0],g.x,t.x[1]),d=!0),(o&&c||r&&u)&&(g.y=ie(t.y[0],g.y,t.y[1]),d=!0),d&&l&&l.spring({tension:94,friction:17,maxSpeed:555*g.scale,restDelta:.1,restSpeed:.1,velocity:l.getCurrentVelocities()}).from(p).to(g).start(),z()}).on("end",()=>{(null==s?void 0:s.isPointerDown())||N(),(null==l?void 0:l.isRunning())||(H(),w("animationEnd"))}),function(){const e=v("gestures");if(!e)return;if(!a||!o)return;let t=!1;s=Pe(a,e).on("start",e=>{if(!v("gestures"))return;if(!l)return;if(!y()||_())return;const n=e.srcEvent;(p.scale>1||e.currentTouch.length>1)&&(null==n||n.stopPropagation(),l.pause(),t=!0),1===e.currentTouch.length&&w("touchStart")}).on("move",e=>{var n;t&&(1!==g.scale||e.currentTouch.length>1)&&(je(e.srcEvent),null===(n=e.srcEvent)||void 0===n||n.stopPropagation())}).on("pan",e=>{if(!t)return;const n=e.srcEvent;(1!==g.scale||e.currentTouch.length>1)&&(je(n),B(Ie.Pan,e))}).on("swipe",e=>{t&&g.scale>1&&B(Ie.Swipe,e)}).on("tap",e=>{w("click",e)}).on("singleTap",e=>{w("singleClick",e)}).on("doubleTap",e=>{w("dblClick",e)}).on("pinch",e=>{t&&(e.scale>q()?B(Ie.ZoomIn,e):e.scale<q()?B(Ie.ZoomOut,e):B(Ie.Pan,e))}).on("end",e=>{t&&(e.currentTouch.length?(e.srcEvent.stopPropagation(),je(e.srcEvent),null==l||l.end()):(t=!1,H(),N(),w("touchEnd")))}).init()}(),a&&(a.addEventListener("wheel",x,{passive:!1}),m.push(()=>{null==a||a.removeEventListener("wheel",x,{passive:!1})})),null==e||e.addEventListener("click",E),null===document||void 0===document||document.addEventListener("mousemove",S),m.push(()=>{null==e||e.removeEventListener("click",E),null===document||void 0===document||document.removeEventListener("mousemove",S)});const r=C();p=Object.assign({},r),g=Object.assign({},r),d=3,z(),H(),w("ready"),requestAnimationFrame(()=>{L(),a&&(a.style.visibility="")})}function C(){const e=Object.assign({},v("startPos")||{});let t=e.scale,n=1;n="string"==typeof t?O(t):"number"==typeof t?t:q();const i=Object.assign(Object.assign(Object.assign({},Ne),e),{scale:n}),o=_()?P(n):void 0;if(o){const{x:e,y:t}=o;i.x=e,i.y=t}return i}function A(){const e={top:0,left:0,width:0,height:0};if(i){const t=i.getBoundingClientRect();g.angle%180==90?(e.top=t.top+.5*t.height-.5*t.width,e.left=t.left+.5*t.width-.5*t.height,e.width=t.height,e.height=t.width):(e.top=t.top,e.left=t.left,e.width=t.width,e.height=t.height)}return e}function M(){let e=v("width"),t=v("height");if(o&&"auto"===e){const t=o.getAttribute("width");e=t?parseFloat(t+""):void 0!==o.dataset.width?parseFloat(o.dataset.width+""):Re(a)?a.naturalWidth:Re(o)?o.naturalWidth:o.getBoundingClientRect().width}else e=Z(e)?parseFloat(e):e;if(o&&"auto"===t){const e=o.getAttribute("height");t=e?parseFloat(e+""):void 0!==o.dataset.height?parseFloat(o.dataset.height+""):Re(a)?a.naturalHeight:Re(o)?o.naturalHeight:o.getBoundingClientRect().height}else t=Z(t)?parseFloat(t):t;return{width:e,height:t}}function k(){const e=A();return{width:e.width,height:e.height}}function _(){return"mousemove"===v("panMode")&&matchMedia("(hover: hover)").matches}function P(e){const t=u||v("event"),n=null==i?void 0:i.getBoundingClientRect();if(!t||!n||e<=1)return{x:0,y:0};const o=(t.clientX||0)-n.left,r=(t.clientY||0)-n.top,{width:a,height:s}=k(),l=F(e);if(e>1){const t=v("mouseMoveFactor");t>1&&(e*=t)}let c=a*e,d=s*e,f=.5*(c-a)-o/a*100/100*(c-a),h=.5*(d-s)-r/s*100/100*(d-s);return f=ie(l.x[0],f,l.x[1]),h=ie(l.y[0],h,l.y[1]),{x:f,y:h}}function O(t="base"){if(!e)return 1;const n=e.getBoundingClientRect(),i=A(),{width:o,height:r}=M(),a=e=>{if("number"==typeof e)return e;switch(e){case"min":case"base":return 1;case"cover":return Math.max(n.height/i.height,n.width/i.width)||1;case"full":case"max":{const e=g.angle%180==90?r:o;return e&&i.width?e/i.width:1}}},s=v("minScale"),l=v("maxScale"),c=Math.min(a("full"),a(s)),u="number"==typeof l?a("full")*l:Math.min(a("full"),a(l));switch(t){case"min":return c;case"base":return ie(c,1,u);case"cover":return a("cover");case"full":return Math.min(u,a("full"));case"max":return u}}function j(){return O("min")}function q(){return O("base")}function R(){return O("cover")}function I(){return O("full")}function D(){return O("max")}function F(t){const n={x:[0,0],y:[0,0]},i=null==e?void 0:e.getBoundingClientRect();if(!i)return n;const o=A(),r=i.width,a=i.height;let s=o.width,l=o.height,c=t=void 0===t?g.scale:t,u=t;if(_()&&t>1){const e=v("mouseMoveFactor");e>1&&(s*t>r+.01&&(c*=e),l*t>a+.01&&(u*=e))}return s*=c,l*=u,t>1&&(s>r&&(n.x[0]=.5*(r-s),n.x[1]=.5*(s-r)),n.x[0]-=.5*(o.left-i.left),n.x[1]-=.5*(o.left-i.left),n.x[0]-=.5*(o.left+o.width-i.right),n.x[1]-=.5*(o.left+o.width-i.right),l>a&&(n.y[0]=.5*(a-l),n.y[1]=.5*(l-a)),n.y[0]-=.5*(o.top-i.top),n.y[1]-=.5*(o.top-i.top),n.y[0]-=.5*(o.top+o.height-i.bottom),n.y[1]-=.5*(o.top+o.height-i.bottom)),n}function N(){if(!y())return;if(!v("bounds"))return;if(!l)return;const e=j(),t=D(),n=ie(e,g.scale,t);if(g.scale<e-.01||g.scale>t+.01)return void B(Ie.ZoomTo,{scale:n});if(l.isRunning())return;if(ee())return;const i=F(n);g.x<i.x[0]||g.x>i.x[1]||g.y<i.y[0]||g.y>i.y[1]?(g.x=ie(i.x[0],g.x,i.x[1]),g.y=ie(i.y[0],g.y,i.y[1]),l.spring({tension:170,friction:17,restDelta:.001,restSpeed:.001,maxSpeed:1/0,velocity:l.getCurrentVelocities()}),l.from(p).to(g).start()):z()}function H(t){var n;if(!y())return;const o=G(),r=ee(),a=te(),s=ne(),l=W(),c=V();se(i,"is-fullsize",s),se(i,"is-expanded",a),se(i,"is-dragging",r),se(i,"can-drag",o),se(i,"will-zoom-in",l),se(i,"will-zoom-out",c);const u=X(),d=Y(),f=U(),h=!y();for(const i of(null===(n=t||e)||void 0===n?void 0:n.querySelectorAll("[data-panzoom-action]"))||[]){const e=i.dataset.panzoomAction;let t=!1;if(h)t=!0;else switch(e){case Ie.ZoomIn:u||(t=!0);break;case Ie.ZoomOut:f||(t=!0);break;case Ie.ToggleFull:{d||f||(t=!0);const e=i.querySelector("g");e&&(e.style.display=s&&!t?"none":"");break}case Ie.IterateZoom:{u||f||(t=!0);const e=i.querySelector("g");e&&(e.style.display=u||t?"":"none");break}case Ie.ToggleCover:case Ie.ToggleMax:u||f||(t=!0)}t?(i.setAttribute("aria-disabled",""),i.setAttribute("tabindex","-1")):(i.removeAttribute("aria-disabled"),i.removeAttribute("tabindex"))}}function B(t,n){var i;if(!(t&&e&&o&&l&&y()))return;if(t===Ie.Swipe&&Math.abs(l.getCurrentVelocities().scale)>.01)return;const r=Object.assign({},g);let a=Object.assign({},g),s=F(_()?r.scale:p.scale);const c=l.getCurrentVelocities(),u=A(),d=((null===(i=(n=n||{}).currentTouch)||void 0===i?void 0:i.length)||0)>1,f=n.velocityX||0,h=n.velocityY||0;let m=n.center;n.srcEvent&&(m=Le(Se(n.srcEvent)));let b=n.deltaX||0,x=n.deltaY||0;switch(t){case Ie.MoveRight:b=n.deltaX||100;break;case Ie.MoveLeft:b=n.deltaX||-100;break;case Ie.MoveUp:x=n.deltaY||-100;break;case Ie.MoveDown:x=n.deltaY||100}let E=[];switch(t){case Ie.Reset:a=Object.assign({},Ne),a.scale=q();break;case Ie.Pan:case Ie.Move:case Ie.MoveLeft:case Ie.MoveRight:case Ie.MoveUp:case Ie.MoveDown:if(ee()){let e=1,t=1;a.x<=s.x[0]&&f<=0&&(e=Math.max(.01,1-Math.abs(1/u.width*Math.abs(a.x-s.x[0]))),e*=.2),a.x>=s.x[1]&&f>=0&&(e=Math.max(.01,1-Math.abs(1/u.width*Math.abs(a.x-s.x[1]))),e*=.2),a.y<=s.y[0]&&h<=0&&(t=Math.max(.01,1-Math.abs(1/u.height*Math.abs(a.y-s.y[0]))),t*=.2),a.y>=s.y[1]&&h>=0&&(t=Math.max(.01,1-Math.abs(1/u.height*Math.abs(a.y-s.y[1]))),t*=.2),a.x+=b*e,a.y+=x*t}else a.x=ie(s.x[0],a.x+b,s.x[1]),a.y=ie(s.y[0],a.y+x,s.y[1]);break;case Ie.Swipe:const e=(e=0)=>Math.sign(e)*Math.pow(Math.abs(e),1.5);a.x+=ie(-1e3,e(f),1e3),a.y+=ie(-1e3,e(h),1e3),h&&!f&&(a.x=ie(s.x[0],a.x,s.x[1])),!h&&f&&(a.y=ie(s.y[0],a.y,s.y[1])),c.x=f,c.y=h;break;case Ie.ZoomTo:a.scale=n.scale||1;break;case Ie.ZoomIn:a.scale=a.scale*(n.scale||2),d||(a.scale=Math.min(a.scale,D()));break;case Ie.ZoomOut:a.scale=a.scale*(n.scale||.5),d||(a.scale=Math.max(a.scale,j()));break;case Ie.ToggleCover:E=[q(),R()];break;case Ie.ToggleFull:E=[q(),I()];break;case Ie.ToggleMax:E=[q(),D()];break;case Ie.IterateZoom:E=[q(),I(),D()];break;case Ie.Zoom:const t=I();a.scale>=t-.05?a.scale=q():a.scale=Math.min(t,a.scale*(n.scale||2));break;case Ie.RotateCW:a.angle+=90;break;case Ie.RotateCCW:a.angle-=90;break;case Ie.FlipX:a.flipX*=-1;break;case Ie.FlipY:a.flipY*=-1}if(void 0!==p.angle&&Math.abs(p.angle)>=360&&(a.angle-=360*Math.floor(p.angle/360),p.angle-=360*Math.floor(p.angle/360)),E.length){const e=E.findIndex(e=>e>a.scale+1e-4);a.scale=E[e]||E[0]}if(d&&(a.scale=ie(j()*(d?.8:1),a.scale,D()*(d?1.6:1))),_()){const e=P(a.scale);if(e){const{x:t,y:n}=e;a.x=t,a.y=n}}else if(Math.abs(a.scale-r.scale)>1e-4){let t=0,n=0;if(m)t=m.x,n=m.y;else{const i=e.getBoundingClientRect();t=i.x+.5*i.width,n=i.y+.5*i.height}let i=t-u.left,o=n-u.top;i-=.5*u.width,o-=.5*u.height;const l=(i-r.x)/r.scale,c=(o-r.y)/r.scale;a.x=i-l*a.scale,a.y=o-c*a.scale,!d&&v("bounds")&&(s=F(a.scale),a.x=ie(s.x[0],a.x,s.x[1]),a.y=ie(s.y[0],a.y,s.y[1]))}if(t===Ie.Swipe){let e=94,t=17,n=500*a.scale,i=c;l.spring({tension:e,friction:t,maxSpeed:n,restDelta:.1,restSpeed:.1,velocity:i})}else t===Ie.Pan||d?l.spring({tension:900,friction:17,restDelta:.01,restSpeed:.01,maxSpeed:1}):l.spring({tension:170,friction:17,restDelta:.001,restSpeed:.001,maxSpeed:1/0,velocity:c});if(0===n.velocity||ce(p,a))p=Object.assign({},a),g=Object.assign({},a),l.end(),z(),H();else{if(ce(g,a))return;l.from(p).to(a).start()}w("action",t)}function z(){if(!o||!i||!a)return;const{width:t,height:n}=M();Object.assign(i.style,{maxWidth:`min(${t}px, 100%)`,maxHeight:`min(${n}px, 100%)`});const r=function(){const{width:t,height:n}=M(),{width:i,height:o}=k();if(!e)return{x:0,y:0,width:0,height:0,scale:0,flipX:0,flipY:0,angle:0,fitWidth:i,fitHeight:o,fullWidth:t,fullHeight:n};let{x:r,y:a,scale:s,angle:l,flipX:c,flipY:u}=p,d=1/I(),f=t,h=n,m=p.scale*d,v=g.scale*d;const y=Math.max(i,o),b=Math.min(i,o);t>n?(f=y,h=b):(f=b,h=y),m=t>n?y*s/t||1:y*s/n||1;let w=f?t*v:0,x=h?n*v:0;return r=r+.5*f-.5*w,a=a+.5*h-.5*x,{x:r,y:a,width:w,height:x,scale:f&&h?t*m/w:0,flipX:c,flipY:u,angle:l,fitWidth:i,fitHeight:o,fullWidth:t,fullHeight:n}}(),{x:s,y:l,width:c,height:u,scale:d,angle:f,flipX:h,flipY:m}=r;let v=`translate(${qe(s)}px, ${qe(l)}px)`;v+=1!==h||1!==m?` scaleX(${qe(d*h)}) scaleY(${qe(d*m)})`:` scale(${qe(d)})`,0!==f&&(v+=` rotate(${f}deg)`),a.style.width=qe(c)+"px",a.style.height=qe(u)+"px",a.style.transform=""+v,w("render")}function $(){let e=g.scale;const t=v("clickAction");let n=q();if(t){let i=[];switch(t){case Ie.ZoomIn:n=2*e;break;case Ie.ZoomOut:n=.5*e;break;case Ie.ToggleCover:i=[q(),R()];break;case Ie.ToggleFull:i=[q(),I()];break;case Ie.ToggleMax:i=[q(),D()];break;case Ie.IterateZoom:i=[q(),I(),D()];break;case Ie.Zoom:const t=I();n=e>=t-.05?q():Math.min(t,2*e)}if(i.length){const t=i.findIndex(t=>t>e+1e-4);n=i[t]||q()}}return n=ie(j(),n,D()),n}function W(){return!!(y()&&$()>g.scale)}function V(){return!!(y()&&$()<g.scale)}function U(){return!!(y()&&g.scale>j())}function X(){return!!(y()&&g.scale<D())}function Y(){return!!(y()&&g.scale<I())}function G(){return!(!(y()&&te()&&s)||_())}function ee(){return!(!y()||!(null==s?void 0:s.isPointerDown())||_())}function te(){return!!(y()&&g.scale>q())}function ne(){return!!(y()&&g.scale>=I())}function oe(){const t="in-fullscreen",n="with-panzoom-in-fullscreen";null==e||e.classList.toggle(t);const i=null==e?void 0:e.classList.contains(t);i?(document.documentElement.classList.add(n),document.addEventListener("keydown",le,!0)):(document.documentElement.classList.remove(n),document.removeEventListener("keydown",le,!0)),z(),w(i?"enterFS":"exitFS")}function le(e){"Escape"!==e.key||e.defaultPrevented||oe()}const ue={canDrag:G,canZoomIn:X,canZoomOut:U,canZoomToFull:Y,destroy:function(){w("destroy");for(const e of Object.values(h))null==e||e.destroy(ue);for(const e of m)e();return i&&(i.style.aspectRatio="",i.style.maxWidth="",i.style.maxHeight=""),a&&(a.style.width="",a.style.height="",a.style.transform=""),i=void 0,o=void 0,a=void 0,p=Object.assign({},Ne),g=Object.assign({},Ne),null==l||l.destroy(),l=void 0,null==s||s.destroy(),s=void 0,d=4,ue},emit:w,execute:B,getBoundaries:F,getContainer:function(){return e},getContent:function(){return o},getFullDim:M,getGestures:function(){return s},getMousemovePos:P,getOptions:function(){return f},getPlugins:function(){return h},getScale:O,getStartPosition:C,getState:function(){return d},getTransform:function(e){return!0===e?g:p},getTween:function(){return l},getViewport:function(){return a},getWrapper:function(){return i},init:function(){return d=0,w("init"),function(){for(const[e,t]of Object.entries(Object.assign(Object.assign({},n),f.plugins||{})))if(e&&!h[e]&&t instanceof Function){const n=t();n.init(ue),h[e]=n}w("initPlugins")}(),function(){const t=Object.assign(Object.assign({},He.classes),v("classes"));if(e&&(re(e,t.container),o=e.querySelector("."+t.content),o)){if(o.setAttribute("draggable","false"),i=e.querySelector("."+t.wrapper),i||(i=document.createElement("div"),re(i,t.wrapper),o.insertAdjacentElement("beforebegin",i),i.insertAdjacentElement("afterbegin",o)),a=e.querySelector("."+t.viewport),a||(a=document.createElement("div"),re(a,t.viewport),a.insertAdjacentElement("afterbegin",o),i.insertAdjacentElement("beforeend",a)),r=o.cloneNode(!0),r.removeAttribute("id"),i.insertAdjacentElement("afterbegin",r),o instanceof HTMLPictureElement&&(o=o.querySelector("img")),r instanceof HTMLPictureElement&&(r=r.querySelector("img")),a instanceof HTMLPictureElement&&(a=a.querySelector("img")),a&&(a.style.visibility="hidden",v("protected"))){a.addEventListener("contextmenu",e=>{je(e)});const e=document.createElement("div");re(e,"f-panzoom__protected"),a.appendChild(e)}w("initLayout")}}(),function(){if(e&&i&&!c){let e=null;c=new ResizeObserver(()=>{y()&&(e=e||requestAnimationFrame(()=>{y()&&(H(),N(),w("refresh")),e=null}))}),c.observe(i),m.push(()=>{null==c||c.disconnect(),c=void 0,e&&(cancelAnimationFrame(e),e=null)})}}(),function(){if(!e||!o)return;if(!Re(o)||!Re(r))return void T();const t=()=>{o&&Re(o)&&o.decode().then(()=>{T()}).catch(()=>{T()})};d=1,e.classList.add("is-loading"),w("loading"),r.src&&r.complete?t():(function(){if(!e)return;if(null==e?void 0:e.querySelector(".f-spinner"))return;const t=v("spinnerTpl"),n=K(t);n&&(n.classList.add("f-spinner"),e.classList.add("is-loading"),null==i||i.insertAdjacentElement("afterbegin",n))}(),r.addEventListener("load",t,!1),r.addEventListener("error",t,!1),m.push(()=>{null==r||r.removeEventListener("load",t,!1),null==r||r.removeEventListener("error",t,!1)}))}(),ue},isDragging:ee,isExpanded:te,isFullsize:ne,isMousemoveMode:_,localize:function(e,t=[]){const n=v("l10n")||{};e=String(e).replace(/\{\{(\w+)\}\}/g,(e,t)=>n[t]||e);for(let n=0;n<t.length;n++)e=e.split(t[n][0]).join(t[n][1]);return e.replace(/\{\{(.*?)\}\}/g,(e,t)=>t)},off:function(e,t){for(const n of e instanceof Array?e:[e])b.has(n)&&b.set(n,b.get(n).filter(e=>e!==t));return ue},on:function(e,t){for(const n of e instanceof Array?e:[e])b.set(n,[...b.get(n)||[],t]);return ue},toggleFS:oe,updateControls:H,version:"6.0.34",willZoomIn:W,willZoomOut:V};return ue};Ve.l10n={en_EN:Oe},Ve.getDefaults=()=>He;const Ue=(e,t)=>{let n=[];return e.childNodes.forEach(e=>{e.nodeType!==Node.ELEMENT_NODE||t&&!e.matches(t)||n.push(e)}),n},Xe=Object.assign(Object.assign({},Oe),{ERROR:"Something went wrong. <br /> Please try again later.",NEXT:"Next page",PREV:"Previous page",GOTO:"Go to page #%d",DOWNLOAD:"Download",TOGGLE_FULLSCREEN:"Toggle full-screen mode",TOGGLE_EXPAND:"Toggle full-size mode",TOGGLE_THUMBS:"Toggle thumbnails",TOGGLE_AUTOPLAY:"Toggle slideshow"}),Ye=e=>{e.cancelable&&e.preventDefault()},Ge={adaptiveHeight:!1,center:!0,classes:{container:"f-carousel",isEnabled:"is-enabled",isLTR:"is-ltr",isRTL:"is-rtl",isHorizontal:"is-horizontal",isVertical:"is-vertical",hasAdaptiveHeight:"has-adaptive-height",viewport:"f-carousel__viewport",slide:"f-carousel__slide",isSelected:"is-selected"},dragFree:!1,enabled:!0,errorTpl:'<div class="f-html">{{ERROR}}</div>',fill:!1,infinite:!0,initialPage:0,l10n:Xe,rtl:!1,slides:[],slidesPerPage:"auto",spinnerTpl:'<div class="f-spinner"></div>',transition:"fade",tween:{clamp:!0,mass:1,tension:160,friction:25,restDelta:1,restSpeed:1,velocity:0},vertical:!1};let Ze,Qe=0;const Ke=(e,t={},n={})=>{Qe++;let i,o,r,a,s,l=0,c=Object.assign({},Ge),u=Object.assign({},Ge),d={},f=null,h=null,p=!1,g=!1,m=!1,v=!1,y="height",b=0,w=!0,x=0,E=0,S=0,L=0,T="*",C=[],A=[];const M=new Set;let k=[],_=[],P=0,O=0,j=0;function q(e,...t){let n=u[e];return n&&n instanceof Function?n(Me,...t):n}function R(e,t=[]){const n=q("l10n")||{};e=String(e).replace(/\{\{(\w+)\}\}/g,(e,t)=>n[t]||e);for(let n=0;n<t.length;n++)e=e.split(t[n][0]).join(t[n][1]);return e.replace(/\{\{(.*?)\}\}/g,(e,t)=>t)}const I=new Map;function D(e,...t){const n=[...I.get(e)||[]];u.on&&n.push(u.on[e]);for(const e of n)e&&e instanceof Function&&e(Me,...t);"*"!==e&&D("*",e,...t)}function F(){var e,t;const a=te({},Ge,c);te(a,Ge,c);let p="";const m=c.breakpoints||{};if(m)for(const[e,t]of Object.entries(m))window.matchMedia(e).matches&&(p+=e,te(a,t));if(void 0===s||p!==s){if(s=p,0!==l){let n=null===(t=null===(e=_[x])||void 0===e?void 0:e.slides[0])||void 0===t?void 0:t.index;void 0===n&&(n=u.initialSlide),a.initialSlide=n,a.slides=[];for(const e of C)e.isVirtual&&a.slides.push(e)}Te(),u=a,!1!==u.enabled&&(l=0,D("init"),function(){for(const[e,t]of Object.entries(Object.assign(Object.assign({},n),u.plugins||{})))if(e&&!d[e]&&t instanceof Function){const n=t();n.init(Me,Ke),d[e]=n}D("initPlugins")}(),function(){if(!f)return;const e=q("classes")||{};re(f,e.container);const t=q("style");if(t&&G(t))for(const[e,n]of Object.entries(t))f.style.setProperty(e,n);h=f.querySelector("."+e.viewport),h||(h=document.createElement("div"),re(h,e.viewport),h.append(...Ue(f,"."+e.slide)),f.insertAdjacentElement("afterbegin",h)),f.carousel=Me,D("initLayout")}(),function(){if(!h)return;const e=q("classes")||{};C=[],[...Ue(h,"."+e.slide)].forEach(e=>{if(e.parentElement){const t=le(Object.assign({el:e,isVirtual:!1},e.dataset||{}));D("createSlide",t),C.push(t)}}),fe();for(const e of C)D("addSlide",e);ne(q("slides"));for(const e of C){const t=e.el;(null==t?void 0:t.parentElement)===h&&(re(t,u.classes.slide),re(t,e.class),Ee(e),D("attachSlideEl",e))}D("initSlides")}(),he(),function(){if(f&&h&&(f.addEventListener("click",ve,{passive:!1}),document.addEventListener("mousemove",N),!r)){let e=null;r=new ResizeObserver(t=>{e||(e=requestAnimationFrame(()=>{!function(e){var t;if(!h)return;if(0===l){const n=null===(t=e[0])||void 0===t?void 0:t.contentBoxSize[0],r=(null==n?void 0:n.blockSize)||0,a=(null==n?void 0:n.inlineSize)||0;return P=r,O=a,l=1,re(f,(q("classes")||{}).isEnabled),Le(),o=ye().on("start",()=>{i&&i.isPointerDown()||(z(),Le())}).on("step",e=>{const t=b;b=e.pos,b!==t&&(w=!1,Le())}).on("end",e=>{(null==i?void 0:i.isPointerDown())||(b=e.pos,o&&!g&&(b<S||b>L)?o.spring({clamp:!0,mass:1,tension:200,friction:25,velocity:0,restDelta:1,restSpeed:1}).from({pos:b}).to({pos:ie(S,b,L)}).start():w||(w=!0,D("settle")))}),H(),$(),void D("ready")}if(1!==l)return;const n=_.length;he(),H();const r=h.getBoundingClientRect(),a=r.height,s=r.width;n>1&&(v&&Math.abs(a-P)<.5||!v&&Math.abs(s-O)<.5)||(P=a,O=s,v&&!a||!v&&!s||f&&h&&(n===_.length&&(null==i?void 0:i.isPointerDown())||(q("dragFree")&&(g||b>S&&b<L)?(z(),Le()):Se(x,{transition:!1}))))}(t),e=null}))}),r.observe(h)}}())}}function N(e){Ze=e}function H(){if(!h)return;const e=q("gestures");!1!==e?i||(i=Pe(h,e).on("start",e=>{var t,n;if(!o)return;const{srcEvent:i}=e;v&&be(i)&&!J(i.target)&&Ye(i),o.pause(),o.getCurrentVelocities().pos=0;const r=null===(t=_[x])||void 0===t?void 0:t.slides[0];r&&M.has(r.index)&&r.el&&(b=r.offset||0,b+=(function(e){const t=window.getComputedStyle(e),n=new DOMMatrixReadOnly(t.transform);return{width:n.m41||0,height:n.m42||0}}(r.el)[y]||0)*(m&&!v?1:-1)),ge(),g||(b<S||b>L)&&o.spring({clamp:!0,mass:1,tension:500,friction:25,velocity:(null===(n=o.getCurrentVelocities())||void 0===n?void 0:n.pos)||0,restDelta:1,restSpeed:1}).from({pos:b}).to({pos:ie(S,b,L)}).start()}).on("move",e=>{var t,n;const{srcEvent:i,axis:r}=e,a=i.target;if(r||(Ye(i),i.stopPropagation(),i.stopImmediatePropagation()),("y"===r&&v||"x"===r&&!v)&&(Ye(i),i.stopPropagation()),!r)return;const{deltaX:s,deltaY:l}=e;if(!o)return;if(be(i)&&(null===(t=i.touches)||void 0===t?void 0:t.length)>1)return;if("y"===r&&!v||"x"===r&&v)return;if(a&&J(a)&&("x"!==r||v))return;const c=m&&!v?1:-1;let u=v?l:s,d=(null==o?void 0:o.isRunning())?o.getEndValues().pos:b,f=1;g||(d<=S&&u*c<0?(f=Math.max(.01,1-Math.abs(1/X()*Math.abs(d-S))),f*=.2):d>=L&&u*c>0&&(f=Math.max(.01,1-Math.abs(1/X()*Math.abs(d-L))),f*=.2)),d+=u*f*c,o.spring({clamp:!0,mass:1,tension:700,friction:25,velocity:(null===(n=o.getCurrentVelocities())||void 0===n?void 0:n.pos)||0,restDelta:1,restSpeed:1}).from({pos:b}).to({pos:d}).start()}).on("panstart",e=>{(null==e?void 0:e.axis)===(v?"y":"x")&&re(h,"is-dragging")}).on("panend",()=>{ae(h,"is-dragging")}).on("end",e=>{var t,n;const{srcEvent:i,axis:r,velocityX:a,velocityY:s,currentTouch:l}=e;if(l.length>0||!o)return;const c=i.target,u=c&&J(c)&&!("x"===r&&!v);v&&be(i)&&!e.axis&&ve(i);const f=_.length,h=q("dragFree");if(!f)return;const p=q("vertical")?s:a;let y=(null==o?void 0:o.isRunning())?o.getEndValues().pos:b;const E=m&&!v?1:-1;if(u||(y+=p*(h?5:1)*E),!g&&(p*E<=0&&y<S||p*E>=0&&y>L)){let e=0;return Math.abs(p)>0&&(e=2*Math.abs(p),e=Math.min(.3*X(),e)),y=ie(S+-1*e,y,L+e),void o.spring({clamp:!0,mass:1,tension:380,friction:25,velocity:-1*p,restDelta:1,restSpeed:1}).from({pos:b}).to({pos:y}).start()}if(h||(null===(t=d.Autoscroll)||void 0===t?void 0:t.isEnabled()))return void(Math.abs(p)>10?o.spring({clamp:!0,mass:1,tension:150,friction:25,velocity:-1*p,restDelta:1,restSpeed:1}).from({pos:b}).to({pos:y}).start():o.isRunning()||w||(w=!0,D("settle")));if(!h&&!(null===(n=d.Autoscroll)||void 0===n?void 0:n.isEnabled())&&(!e.offsetX&&!e.offsetY||"y"===r&&!v||"x"===r&&v))return void Se(x,{transition:"tween"});let T=V(y);Math.abs(p)>10&&T===x&&(T+=p>0?m&&!v?1:-1:m&&!v?-1:1),Se(T,{transition:"tween",tween:{velocity:-1*p}})}).init()):i&&(i.destroy(),i=void 0)}function B(e="*"){var t;const n=[];for(const i of C)("*"===e||i.class&&i.class.includes(e)||i.el&&(null===(t=i.el)||void 0===t?void 0:t.classList.contains(e)))&&n.push(i);a=void 0,T=e,A=[...n]}function z(){if(!o)return;const e=V((null==o?void 0:o.isRunning())?o.getEndValues().pos:b);e!==x&&(a=x,x=e,Ee(),$(),W(),D("change",x,a))}function $(){var e;if(!f)return;se(h,"is-draggable",!!i&&_.length>0);for(const e of f.querySelectorAll("[data-carousel-index]"))e.innerHTML=x+"";for(const e of f.querySelectorAll("[data-carousel-page]"))e.innerHTML=x+1+"";for(const e of f.querySelectorAll("[data-carousel-pages]"))e.innerHTML=_.length+"";for(const e of f.querySelectorAll("[data-carousel-go-prev]"))e.toggleAttribute("aria-disabled",!Ce()),Ce()?e.removeAttribute("tabindex"):e.setAttribute("tabindex","-1");for(const e of f.querySelectorAll("[data-carousel-go-next]"))e.toggleAttribute("aria-disabled",!Ae()),Ae()?e.removeAttribute("tabindex"):e.setAttribute("tabindex","-1");let t=!1;const n=null===(e=_[x])||void 0===e?void 0:e.slides[0];n&&(n.downloadSrc||"image"===n.type&&n.src)&&(t=!0);for(const e of f.querySelectorAll("[data-carousel-download]"))e.toggleAttribute("aria-disabled",!t)}function W(e){var t;e||(e=null===(t=_[x])||void 0===t?void 0:t.slides[0]);const n=e.el;if(n)for(const t of n.querySelectorAll("[data-slide-index]"))t.innerHTML=e.index+1+""}function V(e){var t,n,i;if(!_.length||!o)return 0;const r=Y();let a=e;g?a-=Math.floor((e-(null===(t=_[0])||void 0===t?void 0:t.pos))/r)*r:a=ie(null===(n=_[0])||void 0===n?void 0:n.pos,e,null===(i=_[_.length-1])||void 0===i?void 0:i.pos);const s=new Map;let l=0;for(const e of _){const t=Math.abs(e.pos-a),n=Math.abs(e.pos-a-r),i=Math.abs(e.pos-a+r),o=Math.min(t,n,i);s.set(l,o),l++}const c=s.size>0?[...s.entries()].reduce((e,t)=>t[1]<e[1]?t:e):[x,0];return parseInt(c[0])}function U(){return j}function X(){let e=0;return h&&(h.childElementCount||(h.style.display="block"),e=h.getBoundingClientRect()[y]||0,h.style.display=""),e}function Y(e=!0){return A.reduce((e,t)=>e+t.dim,0)+(A.length-(g&&e?0:1))*j}function ee(e){const t=Y();let n=X();if(!h)return[];const i=[];if(!t||!n)return[];e=void 0===e?b:e,g&&(e-=Math.floor(e/t)*t);let o=0,r=0;if(p){const e=h.getBoundingClientRect();o=Math.abs(e.left),r=Math.abs(window.innerWidth-e.right)}let a=0;for(let s of A){const l=(t=0)=>{i.indexOf(s)>-1||(s.pos=a-e+t||0,s.offset+t>e-s.dim-o+.51&&s.offset+t<e+n+r-.51&&i.push(s))};s.offset=a,g&&(l(t),l(-1*t)),l(),a+=s.dim+j}return i}function ne(e,t){const n=[];for(const t of Array.isArray(e)?e:[e]){const e=le(Object.assign(Object.assign({},t),{isVirtual:!0}));e.el||(e.el=document.createElement("div")),D("createSlide",e),n.push(e)}C.splice(void 0===t?C.length:t,0,...n),fe();for(const e of n)D("addSlide",e),ce(e);return B(T),n}function le(e){return(Z(e)||e instanceof HTMLElement)&&(e={html:e}),Object.assign({index:-1,el:void 0,class:"",isVirtual:!0,dim:0,pos:0,offset:0,html:"",src:""},e)}function ce(e){let t=e.el;if(!e||!t)return;const n=e.html?e.html instanceof HTMLElement?e.html:K(e.html):void 0;n&&(re(n,"f-html"),e.htmlEl=n,re(t,"has-html"),t.append(n),D("contentReady",e))}function ue(e){if(!h||!e)return;let t=e.el;if(t){if(t.setAttribute("index",e.index+""),t.parentElement!==h){let n;re(t,u.classes.slide),re(t,e.class),Ee(e);for(const t of C)if(t.index>e.index){n=t.el;break}h.insertBefore(t,n&&h.contains(n)?n:null),D("attachSlideEl",e)}return W(e),t}}function de(e){const t=null==e?void 0:e.el;t&&(t.remove(),pe(t),D("detachSlideEl",e))}function fe(){for(let e=0;e<C.length;e++){const t=C[e],n=t.el;n&&(t.index!==e&&pe(n),n.setAttribute("index",""+e)),t.index=e}}function he(){var e,t,n,i,r;if(!f||!h)return;m=q("rtl"),v=q("vertical"),y=v?"height":"width";const s=q("classes");if(se(f,s.isLTR,!m),se(f,s.isRTL,m),se(f,s.isHorizontal,!v),se(f,s.isVertical,v),se(f,s.hasAdaptiveHeight,q("adaptiveHeight")),!X())return;const c=window.getComputedStyle(h);p="visible"===c.getPropertyValue("overflow-"+(v?"y":"x")),j=h&&parseFloat(c.getPropertyValue("--f-carousel-gap"))||0;const d=function(){let e=0;if(h){let t=document.createElement("div");t.style.display="block",re(t,u.classes.slide),h.appendChild(t),e=t.getBoundingClientRect()[y],t.remove(),t=void 0}return e}();for(const t of A){const n=t.el;let i=0;if(!t.isVirtual&&n&&Q(n)){let t=!1;n.parentElement&&n.parentElement===h||(h.appendChild(n),t=!0),i=n.getBoundingClientRect()[y],t&&(null===(e=n.parentElement)||void 0===e||e.removeChild(n))}else i=d;t.dim=i}if(g=!1,q("infinite")){g=!0;const e=Y();let n=X();if(p){const e=h.getBoundingClientRect();n+=e.left,n+=e.right-e.width}for(let i=0;i<A.length;i++){const o=(null===(t=A[i])||void 0===t?void 0:t.dim)+j;if(e-o<n&&e-o-n<o){g=!1;break}}}if(function(){var e;if(!f)return;const t=X(),n=Y(!1);let i=q("slidesPerPage");i="auto"===i?1/0:parseFloat(i+""),_=[];let o=0,r=0;for(const n of A)(!_.length||o+n.dim-t>.05||r>=i)&&(_.push({index:_.length,slides:[],dim:0,offset:0,pos:0}),o=0,r=0),null===(e=_[_.length-1])||void 0===e||e.slides.push(n),o+=n.dim+j,r++;const a=q("center"),s=q("fill");let l=0;for(const e of _){e.dim=(e.slides.length-1)*j;for(const t of e.slides)e.dim+=t.dim;e.offset=l,e.pos=l,!1!==a&&(e.pos-=.5*(t-e.dim)),s&&!g&&n>t&&(e.pos=ie(0,e.pos,n-t)),l+=e.dim+j}const c=[];let u;for(const e of _){const t=Object.assign({},e);u&&Math.abs(t.pos-u.pos)<.1?(u.dim+=t.dim,u.slides=[...u.slides,...t.slides]):(u=t,t.index=c.length,c.push(t))}_=c,$()}(),S=(null===(n=_[0])||void 0===n?void 0:n.pos)||0,L=(null===(i=_[_.length-1])||void 0===i?void 0:i.pos)||0,0===l)!function(){var e;a=void 0,x=q("initialPage");const t=q("initialSlide")||void 0;void 0!==t&&(x=Me.getPageIndex(t)||0),x=ie(0,x,_.length-1),b=(null===(e=_[x])||void 0===e?void 0:e.pos)||0,E=b}();else{const e=(null==o?void 0:o.isRunning())?o.getEndValues().pos:b;(e<S||e>L)&&(x=ie(0,x,_.length-1),E=(null===(r=_[x||0])||void 0===r?void 0:r.pos)||0)}D("refresh")}function pe(e){if(!e||!Q(e))return;const t=parseInt(e.getAttribute("index")||"-1");let n="";for(const t of Array.from(e.classList)){const e=t.match(/^f-(\w+)(Out|In)$/);e&&e[1]&&(n=e[1]+"")}if(!e||!n)return;const i=[`f-${n}Out`,`f-${n}In`,"to-prev","to-next","from-prev","from-next"];e.removeEventListener("animationend",me),ae(e,i.join(" ")),M.delete(t)}function ge(){if(!h)return;const e=M.size>0;for(const e of A)pe(e.el);M.clear(),e&&Le()}function me(e){"f-"===e.animationName.substring(0,2)&&(pe(e.target),M.size||(ae(f,"in-transition"),!w&&Math.abs(Me.getPosition(!0)-E)<.5&&(w=!0,D("settle"))),Le())}function ve(e){var t;if(e.defaultPrevented)return;const n=e.composedPath()[0];if(n.closest("[data-carousel-go-prev]"))return Ye(e),void Me.prev();if(n.closest("[data-carousel-go-next]"))return Ye(e),void Me.next();const i=n.closest("[data-carousel-go-to]");if(i)return Ye(e),void Me.goTo(parseFloat(i.dataset.carouselGoTo||"")||0);if(n.closest("[data-carousel-download]")){Ye(e);const n=null===(t=_[x])||void 0===t?void 0:t.slides[0];if(n&&(n.downloadSrc||"image"===n.type&&n.src)){const e=n.downloadFilename,t=document.createElement("a"),i=n.downloadSrc||n.src||"";t.href=i,t.target="_blank",t.download=e||i,t.click()}}else D("click",e)}function we(e){var t;const n=e.el;n&&(null===(t=n.querySelector(".f-spinner"))||void 0===t||t.remove())}function xe(e){var t;const n=e.el;n&&(null===(t=n.querySelector(".f-html.is-error"))||void 0===t||t.remove(),ae(n,"has-error"))}function Ee(e){var t;e||(e=null===(t=_[x])||void 0===t?void 0:t.slides[0]);const n=null==e?void 0:e.el;if(!n)return;let i=q("formatCaption",e);void 0===i&&(i=e.caption),i=i||"";const o=q("captionEl");if(o&&o instanceof HTMLElement){if(e.index!==x)return;if(Z(i)&&(o.innerHTML=R(i+"")),i instanceof HTMLElement){if(i.parentElement===o)return;o.innerHTML="",i.parentElement&&(i=i.cloneNode(!0)),o.append(i)}return}if(!i)return;let r=e.captionEl||n.querySelector(".f-caption");!r&&i instanceof HTMLElement&&i.classList.contains("f-caption")&&(r=i),r||(r=document.createElement("div"),re(r,"f-caption"),Z(i)?r.innerHTML=R(i+""):i instanceof HTMLElement&&(i.parentElement&&(i=i.cloneNode(!0)),r.append(i)));const a=`f-caption-${Qe}_${e.index}`;r.setAttribute("id",a),r.dataset.selectable="true",re(n,"has-caption"),n.setAttribute("aria-labelledby",a),e.captionEl=r,n.insertAdjacentElement("beforeend",r)}function Se(e,t={}){var n,i;let{transition:r,tween:s}=Object.assign({transition:u.transition,tween:u.tween},t||{});if(!f||!o)return;const l=_.length;if(!l)return;if(function(e,t){var n,i,r,a;if(!(f&&o&&t&&Z(t)&&"tween"!==t))return!1;if((null===(n=_[x])||void 0===n?void 0:n.slides.length)>1)return!1;const s=_.length;let l=e>x?1:-1;e=g?(e%s+s)%s:ie(0,e,s-1),m&&(l*=-1);const c=null===(i=_[x])||void 0===i?void 0:i.slides[0],d=null==c?void 0:c.index,h=null===(r=_[e])||void 0===r?void 0:r.slides[0],p=null==h?void 0:h.index,v=null===(a=_[e])||void 0===a?void 0:a.pos;if(void 0===p||void 0===d||d===p||b===v||Math.abs(X()-((null==h?void 0:h.dim)||0))>1)return!1;w=!1,o.pause(),ge(),re(f,"in-transition"),b=E=v;const y=ue(c),S=ue(h);return z(),y&&(M.add(d),y.style.transform="",y.addEventListener("animationend",me),ae(y,u.classes.isSelected),y.inert=!1,re(y,`f-${t}Out to-${l>0?"next":"prev"}`)),S&&(M.add(p),S.style.transform="",S.addEventListener("animationend",me),re(S,u.classes.isSelected),S.inert=!1,re(S,`f-${t}In from-${l>0?"prev":"next"}`)),Le(),!0}(e,r))return;e=g?(e%l+l)%l:ie(0,e,l-1),E=(null===(n=_[e||0])||void 0===n?void 0:n.pos)||0;const c=o.isRunning()?o.getEndValues().pos:b;if(Math.abs(E-c)<1)return b=E,x!==e&&(Ee(),$(),W(),a=x,x=e,D("change",x,a)),Le(),void(w||(w=!0,D("settle")));if(o.pause(),ge(),g){const e=Y(),t=Math.floor((c-(null===(i=_[0])||void 0===i?void 0:i.pos))/e),n=E+t*e;E=[n+e,n,n-e].reduce((function(e,t){return Math.abs(t-c)<Math.abs(e-c)?t:e}))}!1!==r&&G(s)?o.spring(te({},u.tween,s)).from({pos:b}).to({pos:E}).start():(b=E,z(),Le(),w||(w=!0,D("settle")))}function Le(){var e;if(!f||!h)return;k=ee();const t=new Set,n=[],i=_[x],o=u.setTransform||void 0;let r;for(const o of A){const a=M.has(o.index),s=k.indexOf(o)>-1,l=(null===(e=null==i?void 0:i.slides)||void 0===e?void 0:e.indexOf(o))>-1;if(o.isVirtual&&!a&&!s)continue;let c=ue(o);if(c&&(n.push(o),l&&t.add(c),q("adaptiveHeight")&&l)){const e=(c.firstElementChild||c).getBoundingClientRect().height;r=null==r?e:Math.max(r,e)}}h&&r&&(h.style.height=r+"px"),[...Ue(h,"."+u.classes.slide)].forEach(e=>{se(e,u.classes.isSelected,t.has(e));const n=C[parseInt(e.getAttribute("index")||"-1")];if(!n)return e.remove(),void pe(e);const i=M.has(n.index),r=k.indexOf(n)>-1;if(n.isVirtual&&!i&&!r)return void de(n);e.inert=!r;let a=n.pos?Math.round(1e4*n.pos)/1e4:0,s=0,l=0,c=0,d=0;i||(s=v?0:m?-1*a:a,l=v?a:0,c=oe(s,0,n.dim,0,100),d=oe(l,0,n.dim,0,100)),o instanceof Function&&!i?o(Me,n,{x:s,y:l,xPercent:c,yPercent:d}):e.style.transform=s||l?`translate3d(${c}%, ${d}%,0)`:""}),D("render",n)}function Te(){var e;null==f||f.removeEventListener("click",ve),ge(),document.removeEventListener("mousemove",N),null==r||r.disconnect(),r=void 0;for(const t of C)t.el&&Q(t.el)&&(t.state=void 0,we(t),xe(t),de(t),t.isVirtual?(null===(e=t.el)||void 0===e||e.remove(),t.el=void 0):(t.el.style.transform="",null==h||h.appendChild(t.el)));for(const e of Object.values(d))null==e||e.destroy();d={},null==i||i.destroy(),i=void 0,null==o||o.destroy(),o=void 0;for(const[e,t]of Object.entries(u.classes||{}))"container"!==e&&ae(f,t);ae(h,"is-draggable")}function Ce(){return g||x>0}function Ae(){return g||x<_.length-1}const Me={add:function(e,t){var n,i;let r=b;const a=x,s=Y(),l=(null==o?void 0:o.isRunning())?o.getEndValues().pos:b,c=Math.floor((l-(null===(n=_[0])||void 0===n?void 0:n.pos))/s);return ne(e,t),B(T),he(),o&&(a===x&&(r-=c*s),E=(null===(i=_[x||0])||void 0===i?void 0:i.pos)||0,r===E?b=E:o.spring({clamp:!0,mass:1,tension:300,friction:25,restDelta:1,restSpeed:1}).from({pos:r}).to({pos:E}).start()),Le(),Me},canGoPrev:Ce,canGoNext:Ae,destroy:function(){return D("destroy"),window.removeEventListener("resize",F),Te(),I.clear(),f=null,_=[],C=[],u=Object.assign({},Ge),d={},A=[],s="",T="*",l=2,Me},emit:D,filter:function(e="*"){return B(e),he(),Le(),D("filter",e),Me},getContainer:function(){return f},getGapDim:U,getGestures:function(){return i},getLastMouseMove:function(){return Ze},getOption:function(e){return q(e)},getOptions:function(){return u},getPage:function(){return _[x]},getPageIndex:function(e){if(void 0!==e){for(const t of _||[])for(const n of t.slides)if(n.index===e)return t.index;return-1}return x},getPageProgress:function(e,t){var n;void 0===e&&(e=x);const i=_[e];if(!i)return e>x?-1:1;const o=Y(),r=U();let a=i.pos,s=Me.getPosition();if(g&&!0!==t){const e=Math.floor((s-(null===(n=_[0])||void 0===n?void 0:n.pos))/o);s-=e*o,a=[a+o,a,a-o].reduce((function(e,t){return Math.abs(t-s)<Math.abs(e-s)?t:e}))}return(s-a)/(i.dim+r)},getPageVisibility:function(e){var t;void 0===e&&(e=x);const n=_[e];if(!n)return e>x?-1:1;const i=Me.getPosition(),o=X();let r=n.pos;if(g){const e=Me.getPosition(),n=Y(),i=r+Math.floor((e-(null===(t=_[0])||void 0===t?void 0:t.pos))/n)*n;r=[i+n,i,i-n].reduce((function(t,n){return Math.abs(n-e)<Math.abs(t-e)?n:t}))}return r>i&&r+n.dim<i+o?1:r<i?(r+n.dim-i)/n.dim:r+n.dim>i+o?(i+o-r)/n.dim:0},getPages:function(){return _},getPlugins:function(){return d},getPosition:function(e){var t;let n=b;if(g&&!0!==e){const e=Y();n-=Math.floor((b-(null===(t=_[0])||void 0===t?void 0:t.pos)||0)/e)*e}return n},getSlides:function(){return C},getState:function(){return l},getTotalSlideDim:Y,getTween:function(){return o},getViewport:function(){return h},getViewportDim:X,getVisibleSlides:function(e){return void 0===e?k:ee(e)},goTo:Se,hasNavigated:function(){return void 0!==a},hideError:xe,hideLoading:we,init:function(){if(!e||!Q(e))throw new Error("No Element found");return 0!==l&&(Te(),l=0),f=e,c=t,window.removeEventListener("resize",F),c.breakpoints&&window.addEventListener("resize",F),F(),Me},isInfinite:function(){return g},isInTransition:function(){return M.size>0},isRTL:function(){return m},isSettled:function(){return w},isVertical:function(){return v},localize:function(e,t=[]){return R(e,t)},next:function(e={}){return Se(x+1,e),Me},off:function(e,t){for(const n of e instanceof Array?e:[e])I.has(n)&&I.set(n,I.get(n).filter(e=>e!==t));return Me},on:function(e,t){for(const n of e instanceof Array?e:[e])I.set(n,[...I.get(n)||[],t]);return Me},prev:function(e={}){return Se(x-1,e),Me},remove:function(e){void 0===e&&(e=C.length-1);const t=C[e];return t&&(D("removeSlide",t),t.el&&(pe(t.el),t.el.remove(),t.el=void 0),C.splice(e,1),B(T),he(),Le()),Me},setPosition:function(e){b=e,z(),Le()},showError:function(e,t){we(e),xe(e);const n=e.el;if(n){const i=document.createElement("div");re(i,"f-html"),re(i,"is-error"),i.innerHTML=R(t||"<p>{{ERROR}}</p>"),e.htmlEl=i,re(n,"has-html"),re(n,"has-error"),n.insertAdjacentElement("afterbegin",i),D("contentReady",e)}return Me},showLoading:function(e){const t=e.el,n=null==t?void 0:t.querySelector(".f-spinner");if(!t||n)return Me;const i=q("spinnerTpl"),o=K(i);return o&&(re(o,"f-spinner"),t.insertAdjacentElement("beforeend",o)),Me},version:"6.0.34"};return Me};Ke.l10n={en_EN:Xe},Ke.getDefaults=()=>Ge;const Je=function(e="",t="",n=""){return e.split(t).join(n)},et={tpl:e=>`<img class="f-panzoom__content" \n    ${e.srcset?'data-lazy-srcset="{{srcset}}"':""} \n    ${e.sizes?'data-lazy-sizes="{{sizes}}"':""} \n    data-lazy-src="{{src}}" alt="{{alt}}" />`},tt=()=>{let e;function t(t,n){const i=null==e?void 0:e.getOptions().Zoomable;let o=(G(i)?Object.assign(Object.assign({},et),i):et)[t];return o&&"function"==typeof o&&n?o(n):o}function n(){e&&!1!==e.getOptions().Zoomable&&(e.on("addSlide",r),e.on("removeSlide",a),e.on("attachSlideEl",s),e.on("click",o),e.on("change",i),e.on("ready",i))}function i(){l();const t=(null==e?void 0:e.getVisibleSlides())||[];if(t.length>1||"slide"===(null==e?void 0:e.getOption("transition")))for(const n of t){const t=n.panzoomRef;t&&((null==e?void 0:e.getPage().slides)||[]).indexOf(n)<0&&t.execute(Ie.ZoomTo,Object.assign({},t.getStartPosition()))}}function o(e,t){const n=t.target;n&&!t.defaultPrevented&&n.dataset.panzoomAction&&c(n.dataset.panzoomAction)}function r(n,i){const o=i.el;if(!e||!o||i.panzoomRef)return;const r=i.src||i.lazySrc||"",a=i.alt||i.caption||"Image #"+i.index,s=i.srcset||i.lazySrcset||"",c=i.sizes||i.lazySizes||"";if(r&&Z(r)&&!i.html&&(!i.type||"image"===i.type)){i.type="image",i.thumbSrc=i.thumbSrc||r;let e=t("tpl",i);e=Je(e,"{{src}}",r+""),e=Je(e,"{{srcset}}",s+""),e=Je(e,"{{sizes}}",c+""),o.insertAdjacentHTML("afterbegin",e)}const u=o.querySelector(".f-panzoom__content");if(!u)return;u.setAttribute("alt",a+"");const d=i.width&&"auto"!==i.width?parseFloat(i.width+""):"auto",f=i.height&&"auto"!==i.height?parseFloat(i.height+""):"auto",h=Ve(o,Object.assign({width:d,height:f,classes:{container:"f-zoomable"},event:()=>null==e?void 0:e.getLastMouseMove(),spinnerTpl:()=>(null==e?void 0:e.getOption("spinnerTpl"))||""},t("Panzoom")));h.on("*",(t,n,...o)=>{e&&("loading"===n&&(i.state=0),"loaded"===n&&(i.state=1),"error"===n&&(i.state=2,null==e||e.showError(i,"{{IMAGE_ERROR}}")),e.emit("panzoom:"+n,i,...o),"ready"===n&&e.emit("contentReady",i),i.index===(null==e?void 0:e.getPageIndex())&&l())}),i.panzoomRef=h}function a(e,t){t.panzoomRef&&(t.panzoomRef.destroy(),t.panzoomRef=void 0)}function s(e,t){const n=t.panzoomRef;if(n)switch(n.getState()){case 0:n.init();break;case 3:n.execute(Ie.ZoomTo,Object.assign(Object.assign({},n.getStartPosition()),{velocity:0}))}}function l(){var t,n;const i=(null==e?void 0:e.getContainer())||void 0,o=null===(n=null===(t=null==e?void 0:e.getPage())||void 0===t?void 0:t.slides[0])||void 0===n?void 0:n.panzoomRef;if(i)if(o)o.updateControls(i);else for(const e of i.querySelectorAll("[data-panzoom-action]")||[])e.setAttribute("aria-disabled",""),e.setAttribute("tabindex","-1")}function c(t,...n){var i;null===(i=null==e?void 0:e.getPage().slides[0].panzoomRef)||void 0===i||i.execute(t,...n)}return{init:function(t){e=t,e.on("initPlugins",n)},destroy:function(){if(e){e.off("initPlugins",n),e.off("addSlide",r),e.off("removeSlide",a),e.off("attachSlideEl",s),e.off("click",o),e.off("change",i),e.off("ready",i);for(const t of e.getSlides())a(0,t)}e=void 0},execute:c}},nt={syncOnChange:!1,syncOnClick:!0,syncOnHover:!1},it=()=>{let e,t;function n(){const t=null==e?void 0:e.getOptions().Sync;return G(t)?Object.assign(Object.assign({},nt),t):nt}function i(){const i=n().target;e&&i&&function(i){var d,f,h;e&&i&&(t=i,e.getOptions().classes=Object.assign(Object.assign({},e.getOptions().classes),{isSelected:""}),e.getOptions().initialSlide=(null===(f=null===(d=t.getPage())||void 0===d?void 0:d.slides[0])||void 0===f?void 0:f.index)||0,n().syncOnChange&&e.on("change",r),n().syncOnClick&&e.on("click",s),n().syncOnHover&&(null===(h=e.getViewport())||void 0===h||h.addEventListener("mouseover",l)),e&&t&&(e.on("ready",o),e.on("refresh",c),t.on("change",a),t.on("filter",u)))}(i)}function o(){d()}function r(){var n;if(e&&t){const i=(null===(n=e.getPage())||void 0===n?void 0:n.slides)||[],o=t.getPageIndex(i[0].index||0);o>-1&&t.goTo(o,e.hasNavigated()?void 0:{tween:!1,transition:!1}),d()}}function a(){var n;if(e&&t){const i=e.getPageIndex((null===(n=t.getPage())||void 0===n?void 0:n.slides[0].index)||0);i>-1&&e.goTo(i,t.hasNavigated()?void 0:{tween:!1,transition:!1}),d()}}function s(n,i){var o;if(!e||!t)return;if(null===(o=e.getTween())||void 0===o?void 0:o.isRunning())return;const r=null==e?void 0:e.getOptions().classes.slide;if(!r)return;const a=r?i.target.closest("."+r):null;if(a){const e=parseInt(a.getAttribute("index")||"")||0,n=t.getPageIndex(e);t.goTo(n)}}function l(t){e&&s(0,t)}function c(){var n;if(e&&t){const i=e.getPageIndex((null===(n=t.getPage())||void 0===n?void 0:n.slides[0].index)||0);i>-1&&e.goTo(i,{tween:!1,transition:!1}),d()}}function u(n,i){e&&t&&(e.filter(i),a())}function d(){var n,i,o;if(!t)return;const r=(null===(i=null===(n=t.getPage())||void 0===n?void 0:n.slides[0])||void 0===i?void 0:i.index)||0;for(const t of(null==e?void 0:e.getSlides())||[])null===(o=t.el)||void 0===o||o.classList.toggle("is-selected",t.index===r)}return{init:function(t){e=t,e.on("initSlides",i)},destroy:function(){var n;null==e||e.off("ready",o),null==e||e.off("refresh",c),null==e||e.off("change",r),null==e||e.off("click",s),null===(n=null==e?void 0:e.getViewport())||void 0===n||n.removeEventListener("mouseover",l),null==t||t.off("change",a),null==t||t.off("filter",u),t=void 0,null==e||e.off("initSlides",i),e=void 0},getTarget:function(){return t}}},ot={showLoading:!0,preload:1},rt=()=>{let e;function t(){const t=null==e?void 0:e.getOptions().Lazyload;return G(t)?Object.assign(Object.assign({},ot),t):ot}function n(n){var i;const o=n.el;if(!o)return;const r="[data-lazy-src],[data-lazy-srcset],[data-lazy-bg]",a=Array.from(o.querySelectorAll(r));o.matches(r)&&a.push(o);for(const o of a){const r=o.dataset.lazySrc,a=o.dataset.lazySrcset,s=o.dataset.lazySizes,l=o.dataset.lazyBg,c=(o instanceof HTMLImageElement||o instanceof HTMLSourceElement)&&(r||a),u=o instanceof HTMLElement&&l;if(!c&&!u)continue;const d=r||a||l;if(d){if(c&&d){const l=null===(i=o.parentElement)||void 0===i?void 0:i.classList.contains("f-panzoom__wrapper");t().showLoading&&(null==e||e.showLoading(n)),o.addEventListener("load",()=>{null==e||e.hideLoading(n),o instanceof HTMLImageElement?o.decode().then(()=>{o.classList.remove("is-lazyloading"),o.classList.add("is-lazyloaded")}):(o.classList.remove("is-lazyloading"),o.classList.add("is-lazyloaded")),l||null==e||e.emit("lazyLoad:loaded",n,o,d)}),o.addEventListener("error",()=>{null==e||e.hideLoading(n),o.classList.remove("is-lazyloading"),o.classList.add("has-lazyerror"),l||null==e||e.emit("lazyLoad:error",n,o,d)}),o.classList.add("f-lazyload"),o.classList.add("is-lazyloading"),l||null==e||e.emit("lazyLoad:load",n,o,d),r&&(o.src=r),a&&(o.srcset=a),s&&(o.sizes=s)}else u&&(document.body.contains(o)||(document.createElement("img").src=l),o.style.backgroundImage=`url('${l}')`);delete o.dataset.lazySrc,delete o.dataset.lazySrcset,delete o.dataset.lazySizes,delete o.dataset.lazyBg}}}function i(){if(!e)return;const i=[...e.getVisibleSlides()],o=t().preload;if(o>0){const t=e.getPosition(),n=e.getViewportDim();i.push(...e.getVisibleSlides(t+n*o),...e.getVisibleSlides(t-n*o))}for(const e of i)n(e)}return{init:function(t){e=t,e.on("render",i)},destroy:function(){null==e||e.off("render",i),e=void 0}}},at={prevTpl:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" tabindex="-1"><path d="M15 3l-9 9 9 9"></path></svg>',nextTpl:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" tabindex="-1"><path d="M9 3l9 9-9 9"></path></svg>'},st=()=>{let e,t,n;function i(){const t=null==e?void 0:e.getOptions().Arrows;return G(t)?Object.assign(Object.assign({},at),t):at}function o(t){if(!e)return;const n=`<button data-carousel-go-${t} tabindex="0" class="f-button is-arrow is-${t}" title="{{${t.toUpperCase()}}}">`+i()[t+"Tpl"]+"</button",o=K(e.localize(n))||void 0;return o&&re(o,i()[t+"Class"]),o}function r(){var i;null==t||t.remove(),t=void 0,null==n||n.remove(),n=void 0,null===(i=null==e?void 0:e.getContainer())||void 0===i||i.classList.remove("has-arrows")}function a(){e&&!1!==e.getOptions().Arrows&&e.getPages().length>1?(function(){if(!e)return;const i=e.getViewport();i&&(t||(t=o("prev"),t&&i.insertAdjacentElement("beforebegin",t)),n||(n=o("next"),n&&i.insertAdjacentElement("afterend",n)),se(e.getContainer(),"has-arrows",!(!t&&!n)))}(),e&&(null==t||t.toggleAttribute("aria-disabled",!e.canGoPrev()),null==n||n.toggleAttribute("aria-disabled",!e.canGoNext()))):r()}return{init:function(t){e=t.on(["change","refresh"],a)},destroy:function(){r(),null==e||e.off(["change","refresh"],a),e=void 0}}},lt='<circle cx="11" cy="11" r="7.5"/><path d="m21 21-4.35-4.35M8 11h6"/>',ct='<g><line x1="11" y1="8" x2="11" y2="14"></line></g>'+lt,ut={moveLeft:["moveLeft","MOVE_LEFT",'<path d="M5 12h14M5 12l6 6M5 12l6-6"/>'],moveRight:["moveRight","MOVE_RIGHT",'<path d="M5 12h14M13 18l6-6M13 6l6 6"/>'],moveUp:["moveUp","MOVE_UP",'<path d="M12 5v14M18 11l-6-6M6 11l6-6"/>'],moveDown:["moveDown","MOVE_DOWN",'<path d="M12 5v14M18 13l-6 6M6 13l6 6"/>'],zoomOut:["zoomOut","ZOOM_OUT",lt],zoomIn:["zoomIn","ZOOM_IN",ct],toggleFull:["toggleFull","TOGGLE_FULL",ct],iterateZoom:["iterateZoom","ITERATE_ZOOM",ct],toggle1to1:["toggleFull","TOGGLE_FULL",'<path d="M3.51 3.07c5.74.02 11.48-.02 17.22.02 1.37.1 2.34 1.64 2.18 3.13 0 4.08.02 8.16 0 12.23-.1 1.54-1.47 2.64-2.79 2.46-5.61-.01-11.24.02-16.86-.01-1.36-.12-2.33-1.65-2.17-3.14 0-4.07-.02-8.16 0-12.23.1-1.36 1.22-2.48 2.42-2.46Z"/><path d="M5.65 8.54h1.49v6.92m8.94-6.92h1.49v6.92M11.5 9.4v.02m0 5.18v0"/>'],rotateCCW:["rotateCCW","ROTATE_CCW",'<path d="M15 4.55a8 8 0 0 0-6 14.9M9 15v5H4M18.37 7.16v.01M13 19.94v.01M16.84 18.37v.01M19.37 15.1v.01M19.94 11v.01"/>'],rotateCW:["rotateCW","ROTATE_CW",'<path d="M9 4.55a8 8 0 0 1 6 14.9M15 15v5h5M5.63 7.16v.01M4.06 11v.01M4.63 15.1v.01M7.16 18.37v.01M11 19.94v.01"/>'],flipX:["flipX","FLIP_X",'<path d="M12 3v18M16 7v10h5L16 7M8 7v10H3L8 7"/>'],flipY:["flipY","FLIP_Y",'<path d="M3 12h18M7 16h10L7 21v-5M7 8h10L7 3v5"/>'],reset:["reset","RESET",'<path d="M20 11A8.1 8.1 0 0 0 4.5 9M4 5v4h4M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>'],toggleFS:["toggleFS","TOGGLE_FS",'<g><path d="M14.5 9.5 21 3m0 0h-6m6 0v6M3 21l6.5-6.5M3 21v-6m0 6h6"/></g><g><path d="m14 10 7-7m-7 7h6m-6 0V4M3 21l7-7m0 0v6m0-6H4"/></g>']},dt={};for(const[e,t]of Object.entries(ut))dt[e]={tpl:`<button data-panzoom-action="${t[0]}" class="f-button" title="{{${t[1]}}}"><svg>${t[2]}</svg></button>`};var ft,ht;(ht=ft||(ft={})).Left="left",ht.middle="middle",ht.right="right";const pt=Object.assign({counter:{tpl:'<div class="f-counter"><span data-carousel-page></span>/<span data-carousel-pages></span></div>'},download:{tpl:'<button data-carousel-download class="f-button" title="{{DOWNLOAD}}"><svg><path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2M7 11l5 5 5-5M12 4v12"/></svg></button>'},autoplay:{tpl:'<button data-autoplay-action="toggle" class="f-button" title="{{TOGGLE_AUTOPLAY}}"><svg><g><path d="M5 3.5 19 12 5 20.5Z"/></g><g><path d="M8 4v15M17 4v15"/></g></svg></button>'},thumbs:{tpl:'<button data-thumbs-action="toggle" class="f-button" title="{{TOGGLE_THUMBS}}"><svg><rect width="18" height="14" x="3" y="3" rx="2"/><path d="M4 21h1M9 21h1M14 21h1M19 21h1"/></svg></button>'}},dt),gt={absolute:!1,display:{left:[],middle:["zoomIn","zoomOut","toggle1to1","rotateCCW","rotateCW","flipX","flipY","reset"],right:[]},enabled:"auto",items:{}},mt=()=>{let e,t;function n(t){const n=null==e?void 0:e.getOptions().Toolbar;let i=(G(n)?Object.assign(Object.assign({},gt),n):gt)[t];return i&&"function"==typeof i&&e?i(e):i}function i(){var i,o;if(!(null==e?void 0:e.getOptions().Toolbar))return;if(!e||t)return;const r=e.getContainer();if(!r)return;let a=n("enabled");if(!a)return;const s=n("absolute"),l=e.getSlides().length>1;let c=!1,u=!1;for(const t of e.getSlides())t.panzoomRef&&(c=!0),(t.downloadSrc||"image"===t.type&&t.src)&&(u=!0);const d=(null===(i=e.getPlugins().Thumbs)||void 0===i?void 0:i.isEnabled())||!1,f=l&&e.getPlugins().Autoplay||!1,h=e.getPlugins().Fullscreen&&(document.fullscreenEnabled||document.webkitFullscreenEnabled);if("auto"===a&&(a=c),!a)return;t=r.querySelector(".f-carousel__toolbar")||void 0,t||(t=document.createElement("div"),t.classList.add("f-carousel__toolbar"));const p=n("display"),g=te({},pt,n("items"));for(const n of["left","middle","right"]){const i=p[n]||[],o=document.createElement("div");o.classList.add("f-carousel__toolbar__column"),o.classList.add("is-"+n);for(const t of i){let n;if(Z(t)){if("counter"===t&&!l)continue;if("autoplay"===t&&!f)continue;if(dt[t]&&!c)continue;if("fullscreen"===t&&!h)continue;if("thumbs"===t&&!d)continue;if("download"===t&&!u)continue;n=g[t]}if(G(t)&&(n=t),n&&n.tpl){let t=e.localize(n.tpl);t=t.split("<svg>").join('<svg tabindex="-1" width="24" height="24" viewBox="0 0 24 24">');const i=K(t);i&&("function"==typeof n.click&&e&&i.addEventListener("click",t=>{t.preventDefault(),t.stopPropagation(),"function"==typeof n.click&&e&&n.click(e,t)}),o.append(i))}}t.append(o)}if(t.childElementCount){if(s&&t.classList.add("is-absolute"),!t.parentElement){const i=n("parentEl");i?i.insertAdjacentElement("afterbegin",t):null===(o=e.getViewport())||void 0===o||o.insertAdjacentElement("beforebegin",t)}r.contains(t)&&r.classList.add("has-toolbar")}}return{init:function(t){e=t,null==e||e.on("initSlides",i)},destroy:function(){var n;null==e||e.off("initSlides",i),null===(n=null==e?void 0:e.getContainer())||void 0===n||n.classList.remove("has-toolbar"),null==t||t.remove(),t=void 0},add:function(e,t){pt[e]=t},isEnabled:function(){return!!t}}},vt={autoStart:!0,pauseOnHover:!0,showProgressbar:!0,timeout:2e3},yt=()=>{let e,t,n=!1,i=!1,o=!1,r=null;function a(t){const n=null==e?void 0:e.getOptions().Autoplay;let i=(G(n)?Object.assign(Object.assign({},vt),n):vt)[t];return i&&"function"==typeof i&&e?i(e):i}function s(){clearTimeout(t),t=void 0}function l(){if(!n)return;if(o)return;if(i)return;if(t)return;if(!(null==e?void 0:e.isSettled()))return;if(function(){var t;const n=(null===(t=null==e?void 0:e.getPage())||void 0===t?void 0:t.slides)||[];for(const e of n)if(0===e.state)return!0;return!1}())return;!function(){var t,n,i,o;if(!e)return;if(d(),!a("showProgressbar"))return;let s=a("progressbarParentEl");if(!s&&(null===(t=e.getPlugins().Toolbar)||void 0===t?void 0:t.isEnabled())&&(s=e.getContainer()),!s&&!0!==(null===(n=e.getPlugins().Toolbar)||void 0===n?void 0:n.isEnabled())){const t=(null===(i=e.getPages()[0])||void 0===i?void 0:i.slides)||[],n=(null===(o=e.getPage())||void 0===o?void 0:o.slides)||[];1===t.length&&1===n.length&&(s=n[0].el)}if(s||(s=e.getViewport()),!s)return;r=document.createElement("div"),r.classList.add("f-progressbar"),s.prepend(r);const l=a("timeout")||1e3;r.style.animationDuration=l+"ms"}();const s=a("timeout");t=setTimeout(()=>{e&&n&&!i&&(e.isInfinite()||e.getPageIndex()!==e.getPages().length-1?e.next():e.goTo(0))},s)}function c(){var t,i;if(!e||e.getPages().length<2||!1===e.getOptions().Autoplay)return;if(n)return;n=!0,e.emit("autoplay:start",a("timeout")),null===(t=e.getContainer())||void 0===t||t.classList.add("has-autoplay"),null===(i=e.getTween())||void 0===i||i.on("start",v);const o=null==e?void 0:e.getContainer();o&&a("pauseOnHover")&&matchMedia("(hover: hover)").matches&&(o.addEventListener("mouseenter",y,!1),o.addEventListener("mouseleave",b,!1)),e.on("change",g),e.on("settle",m),e.on("contentReady",h),e.on("panzoom:touchStart",u),e.on("panzoom:wheel",u),e.isSettled()&&l()}function u(){var t,o;if(s(),d(),n&&e){e.emit("autoplay:end"),null===(t=e.getContainer())||void 0===t||t.classList.remove("has-autoplay"),null===(o=e.getTween())||void 0===o||o.off("start",v);const n=null==e?void 0:e.getContainer();n&&(n.removeEventListener("mouseenter",y,!1),n.removeEventListener("mouseleave",b,!1))}e&&(e.off("change",g),e.off("settle",m),e.off("contentReady",h),e.off("panzoom:touchStart",u),e.off("panzoom:wheel",u)),n=!1,i=!1}function d(){r&&(r.remove(),r=null)}function f(){e&&e.getPages().length>1&&a("autoStart")&&c()}function h(){e&&l()}function p(e,t){const n=t.target;n&&!t.defaultPrevented&&"toggle"===n.dataset.autoplayAction&&w.toggle()}function g(){!e||!(null==e?void 0:e.isInfinite())&&e.getPageIndex()===e.getPages().length-1?u():(d(),s())}function m(){l()}function v(){s(),d()}function y(){o=!0,n&&(d(),s())}function b(){o=!1,n&&!i&&(null==e?void 0:e.isSettled())&&l()}const w={init:function(t){e=t,e.on("ready",f),e.on("click",p)},destroy:function(){u(),null==e||e.off("ready",f),null==e||e.off("click",p),e=void 0},isEnabled:()=>n,pause:function(){i=!0,s()},resume:function(){i=!1,n&&!o&&l()},start(){c()},stop(){u()},toggle(){n?u():c()}};return w},bt={Carousel:{Lazyload:{showLoading:!1}},minCount:2,showOnStart:!0,thumbTpl:'<button aria-label="Slide to #{{page}}"><img draggable="false" alt="{{alt}}" data-lazy-src="{{src}}" /></button>',type:"modern"};let wt;const xt=()=>{let e,t,n,i=0,o=0,r=!0;function a(t){const n=null==e?void 0:e.getOptions().Thumbs;let i=(G(n)?Object.assign(Object.assign({},bt),n):bt)[t];return i&&"function"==typeof i&&e?i(e):i}function s(){if(!e)return!1;if(!1===(null==e?void 0:e.getOptions().Thumbs))return!1;let t=0;for(const n of e.getSlides())n.thumbSrc&&t++;return t>=a("minCount")}function l(){return"modern"===a("type")}function c(t=!1){var i;const o=null==e?void 0:e.getContainer();if(!e||!o||n)return;if(!s())return;const r=(null===(i=a("Carousel"))||void 0===i?void 0:i.classes)||{};if(r.container=r.container||"f-thumbs",!n){const e=o.nextElementSibling;(null==e?void 0:e.classList.contains(r.container))&&(n=e)}if(!n){n=document.createElement("div");const e=a("parentEl");e?e.insertAdjacentElement("beforeend",n):o.insertAdjacentElement("afterend",n)}re(n,r.container),re(n,"f-thumbs"),re(n,"is-"+a("type")),re(n,"is-syncing"),t&&(n.style.maxHeight="0px")}function u(e){const t=e.thumb?e.thumb instanceof HTMLImageElement?e.thumb.src:e.thumb:e.thumbSrc||void 0,n=void 0===e.thumbAlt?"Thumbnail #"+e.index:e.thumbAlt+"";let i=a("thumbTpl");return i=Je(i,"{{alt}}",n),i=Je(i,"{{src}}",t+""),i=Je(i,"{{index}}",""+e.index),i=Je(i,"{{page}}",""+(e.index||1)),{html:i,class:e.thumbClass}}function d(){var s;if(!wt)return;if(!e||!n||t)return;const c=[];for(const t of e.getSlides())c.push(u(t));c.length&&(t=wt(n,te({},{Sync:{target:e},Lazyload:{preload:1},slides:c,classes:{container:"f-thumbs",viewport:"f-thumbs__viewport",slide:"f-thumbs__slide"},center:!0,fill:!l(),infinite:!1,dragFree:!0,rtl:e.getOptions().rtl||!1,slidesPerPage:e=>{let t=0;return l()&&(function(){if(!l())return;if(!n)return;const e=e=>n&&parseFloat(getComputedStyle(n).getPropertyValue("--f-thumb-"+e))||0;i=e("width"),o=e("clip-width")}(),t=4*(i-o)),e&&e.getTotalSlideDim()<=e.getViewportDim()-t?1/0:1}},bt.Carousel||{},a("Carousel")||{}),{Sync:it,Lazyload:rt}),t.on("ready",()=>{null==e||e.emit("thumbs:ready"),l()&&(null==e||e.on("change",m),null==e||e.on("render",g))}),t.on("destroy",()=>{null==e||e.emit("thumbs:destroy")}),t.init(),null===(s=t.getGestures())||void 0===s||s.on("start",()=>{r=!1}),t.on("click",(e,t)=>{const n=t.target;if(n){const e=n.matches("button")?n:n.firstElementChild;e&&e.matches("button")&&(t.preventDefault(),e.focus({preventScroll:!0}))}}),re(e.getContainer(),"has-thumbs"),b())}function f(){s()&&a("showOnStart")&&(c(),d())}function h(){var t;s()&&(null==e||e.on("addSlide",v),null==e||e.on("click",y),null===(t=null==e?void 0:e.getGestures())||void 0===t||t.on("start",p))}function p(){var e,t;r=!0,(null===(e=document.activeElement)||void 0===e?void 0:e.closest(".f-thumbs"))&&(null===(t=document.activeElement)||void 0===t||t.blur())}function g(){var a,s;null==n||n.classList.toggle("is-syncing",!1===(null==e?void 0:e.hasNavigated())||(null===(a=null==e?void 0:e.getTween())||void 0===a?void 0:a.isRunning())),b(),(null===(s=null==e?void 0:e.getGestures())||void 0===s?void 0:s.isPointerDown())&&function(){if(!l())return;if(!e||!t)return;if(!r)return;const n=t.getTween(),a=t.getPages(),s=e.getPageIndex()||0,c=e.getPageProgress()||0;if(!(e&&a&&a[s]&&n))return;const u=n.isRunning()?n.getCurrentValues().pos:t.getPosition();if(void 0===u)return;let d=a[s].pos+c*(i-o);d=ie(a[0].pos,d,a[a.length-1].pos),n.from({pos:u}).to({pos:d}).start()}()}function m(){r=!0}function v(e,n){null==t||t.add(u(n),n.index)}function y(e,i){const o=i.target;o&&!i.defaultPrevented&&"toggle"===o.dataset.thumbsAction&&function(){if(n||(c(!0),d()),!n)return;const e=(null==t?void 0:t.isVertical())?"maxWidth":"maxHeight",i=n.style[e];n.style[e]=i?"":"0px"}()}function b(){if(!l())return;if(!e||!t)return;const n=(null==t?void 0:t.getSlides())||[];let o=-.5*i;for(const t of n){const n=t.el;if(!n)continue;let r=e.getPageProgress(t.index)||0;r=Math.max(-1,Math.min(1,r)),r>-1&&r<1&&(o+=.5*i*(1-Math.abs(r))),r=Math.round(1e4*r)/1e4,o=Math.round(1e4*o)/1e4,n.style.setProperty("--progress",""+Math.abs(r)),n.style.setProperty("--shift",((null==e?void 0:e.isRTL())?-1*o:o)+"px"),r>-1&&r<1&&(o+=.5*i*(1-Math.abs(r)))}}return{init:function(t,n){wt=n,e=t,e.on("ready",h),e.on("initSlides",f)},destroy:function(){var i,o;null==e||e.off("ready",h),null==e||e.off("initSlides",f),null==e||e.off("change",m),null==e||e.off("render",g),null==e||e.off("addSlide",v),null==e||e.off("click",y),null===(i=null==e?void 0:e.getGestures())||void 0===i||i.off("start",p),null===(o=null==e?void 0:e.getContainer())||void 0===o||o.classList.remove("has-thumbs"),e=void 0,null==t||t.destroy(),t=void 0,null==n||n.remove(),n=void 0},getCarousel:function(){return t},getContainer:function(){return n},isEnabled:function(){return s()}}},Et={iframeAttr:{allow:"autoplay; fullscreen",scrolling:"auto"}},St=()=>{let e;function t(e,t){let n=t.src;if(!Z(n))return;let i=t.type;if(!i){if(i||("#"===n.charAt(0)?i="inline":n.match(/(^data:image\/[a-z0-9+\/=]*,)|(\.((a)?png|avif|gif|jp(g|eg)|pjp(eg)?|jfif|svg|webp|bmp|ico|tif(f)?)((\?|#).*)?$)/i)?i="image":n.match(/\.(pdf)((\?|#).*)?$/i)?i="pdf":n.match(/\.(html|php)((\?|#).*)?$/i)&&(i="iframe")),!i){const e=n.match(/(?:maps\.)?google\.([a-z]{2,3}(?:\.[a-z]{2})?)\/(?:(?:(?:maps\/(?:place\/(?:.*)\/)?\@(.*),(\d+.?\d+?)z))|(?:\?ll=))(.*)?/i);e&&(n=`https://maps.google.${e[1]}/?ll=${(e[2]?e[2]+"&z="+Math.floor(parseFloat(e[3]))+(e[4]?e[4].replace(/^\//,"&"):""):e[4]+"").replace(/\?/,"&")}&output=${e[4]&&e[4].indexOf("layer=c")>0?"svembed":"embed"}`,i="gmap")}if(!i){const e=n.match(/(?:maps\.)?google\.([a-z]{2,3}(?:\.[a-z]{2})?)\/(?:maps\/search\/)(.*)/i);e&&(n=`https://maps.google.${e[1]}/maps?q=${e[2].replace("query=","q=").replace("api=1","")}&output=embed`,i="gmap")}t.src=n,t.type=i}}function n(t,n){"iframe"!==n.type&&"pdf"!==n.type&&"gmap"!==n.type||function(t){if(!e||!t.el||!t.src)return;const n=document.createElement("iframe");n.classList.add("f-iframe");for(const[t,i]of Object.entries(function(){const t=null==e?void 0:e.getOptions().Html;return G(t)?Object.assign(Object.assign({},Et),t):Et}().iframeAttr||{}))n.setAttribute(t,i);n.onerror=()=>{e&&1===e.getState()&&e.showError(t,"{{IFRAME_ERROR}}")},n.src=t.src;const i=document.createElement("div");if(i.classList.add("f-html"),i.append(n),t.width){let e=""+t.width;e.match(/^\d+$/)&&(e+="px"),i.style.maxWidth=""+e}if(t.height){let e=""+t.height;e.match(/^\d+$/)&&(e+="px"),i.style.maxHeight=""+e}if(t.aspectRatio){const e=t.el.getBoundingClientRect();i.style.aspectRatio=""+t.aspectRatio,i.style[e.width>e.height?"width":"height"]="auto",i.style[e.width>e.height?"maxWidth":"maxHeight"]="none"}t.contentEl=n,t.htmlEl=i,t.el.classList.add("has-html"),t.el.classList.add("has-iframe"),t.el.classList.add("has-"+t.type),t.el.prepend(i),e.emit("contentReady",t)}(n)}function i(t,n){var i,o;"iframe"!==n.type&&"pdf"!==n.type&&"gmap"!==n.type||(null==e||e.hideError(n),null===(i=n.contentEl)||void 0===i||i.remove(),n.contentEl=void 0,null===(o=n.htmlEl)||void 0===o||o.remove(),n.htmlEl=void 0)}return{init:function(o){e=o,e.on("addSlide",t),e.on("attachSlideEl",n),e.on("detachSlideEl",i)},destroy:function(){null==e||e.off("addSlide",t),null==e||e.off("attachSlideEl",n),null==e||e.off("detachSlideEl",i),e=void 0}}},Lt=(e,t={})=>{const n=new URL(e),i=new URLSearchParams(n.search),o=new URLSearchParams;for(const[e,n]of[...i,...Object.entries(t)]){let t=n+"";if("t"===e){let e=t.match(/((\d*)m)?(\d*)s?/);e&&o.set("start",60*parseInt(e[2]||"0")+parseInt(e[3]||"0")+"")}else o.set(e,t)}let r=o+"",a=e.match(/#t=((.*)?\d+s)/);return a&&(r+="#t="+a[1]),r},Tt={autoplay:!1,html5videoTpl:'<video class="f-html5video" playsinline controls controlsList="nodownload" poster="{{poster}}">\n    <source src="{{src}}" type="{{format}}" />Sorry, your browser doesn\'t support embedded videos.</video>',iframeAttr:{allow:"autoplay; fullscreen",scrolling:"auto",credentialless:""},vimeo:{byline:1,color:"00adef",controls:1,dnt:1,muted:0},youtube:{controls:1,enablejsapi:1,nocookie:1,rel:0,fs:1}},Ct=()=>{let e,t=!1;function n(){const t=null==e?void 0:e.getOptions().Video;return G(t)?Object.assign(Object.assign({},Tt),t):Tt}function i(){var t;return null===(t=null==e?void 0:e.getPage())||void 0===t?void 0:t.slides[0]}const o=t=>{var n;try{let i=JSON.parse(t.data);if("https://player.vimeo.com"===t.origin){if("ready"===i.event)for(let i of Array.from((null===(n=null==e?void 0:e.getContainer())||void 0===n?void 0:n.getElementsByClassName("f-iframe"))||[]))i instanceof HTMLIFrameElement&&i.contentWindow===t.source&&(i.dataset.ready="true")}else if(t.origin.match(/^https:\/\/(www.)?youtube(-nocookie)?.com$/)&&"onReady"===i.event){const e=document.getElementById(i.id);e&&(e.dataset.ready="true")}}catch(t){}};function r(e,t){const i=t.src;if(!Z(i))return;let o=t.type;if(!o||"html5video"===o){const e=i.match(/\.(mp4|mov|ogv|webm)((\?|#).*)?$/i);e&&(o="html5video",t.html5videoFormat=t.html5videoFormat||"video/"+("ogv"===e[1]?"ogg":e[1]))}if(!o||"youtube"===o){const e=i.match(/(youtube\.com|youtu\.be|youtube\-nocookie\.com)\/(?:watch\?(?:.*&)?v=|v\/|u\/|shorts\/|embed\/?)?(videoseries\?list=(?:.*)|[\w-]{11}|\?listType=(?:.*)&list=(?:.*))(?:.*)/i);if(e){const r=Object.assign(Object.assign({},n().youtube),t.youtube||{}),a=`www.youtube${r.nocookie?"-nocookie":""}.com`,s=Lt(i,r),l=encodeURIComponent(e[2]);t.videoId=l,t.src=`https://${a}/embed/${l}?${s}`,t.thumb=t.thumb||`https://i.ytimg.com/vi/${l}/mqdefault.jpg`,o="youtube"}}if(!o||"vimeo"===o){const e=i.match(/^.+vimeo.com\/(?:\/)?(video\/)?([\d]+)((\/|\?h=)([a-z0-9]+))?(.*)?/);if(e){const r=Object.assign(Object.assign({},n().vimeo),t.vimeo||{}),a=Lt(i,r),s=encodeURIComponent(e[2]),l=e[5]||"";t.videoId=s,t.src=`https://player.vimeo.com/video/${s}?${l?`h=${l}${a?"&":""}`:""}${a}`,o="vimeo"}}t.type=o}function a(t,i){"html5video"===i.type&&function(t){if(!e||!t.el||!t.src)return;const{el:i,src:o}=t;if(!i||!o)return;const r=t.html5videoTpl||n().html5videoTpl,a=t.html5videoFormat||n().html5videoFormat;if(!r)return;const s=t.poster||(t.thumb&&Z(t.thumb)?t.thumb:""),l=K(r.replace(/\{\{src\}\}/gi,o+"").replace(/\{\{format\}\}/gi,a||"").replace(/\{\{poster\}\}/gi,s+""));if(!l)return;const c=document.createElement("div");c.classList.add("f-html"),c.append(l),t.contentEl=l,t.htmlEl=c,i.classList.add("has-"+t.type),i.prepend(c),u(t),e.emit("contentReady",t)}(i),"youtube"!==i.type&&"vimeo"!==i.type||function(t){if(!e||!t.el||!t.src)return;const i=document.createElement("iframe");i.classList.add("f-iframe"),i.setAttribute("id","f-iframe_"+t.videoId);for(const[e,t]of Object.entries(n().iframeAttr||{}))i.setAttribute(e,t);i.onload=()=>{var n;e&&1===e.getState()&&"youtube"===t.type&&(null===(n=i.contentWindow)||void 0===n||n.postMessage(JSON.stringify({event:"listening",id:i.getAttribute("id")}),"*"))},i.onerror=()=>{e&&1===e.getState()&&(null==e||e.showError(t,"{{IFRAME_ERROR}}"))},i.src=t.src;const o=document.createElement("div");o.classList.add("f-html"),o.append(i),t.contentEl=i,t.htmlEl=o,t.el.classList.add("has-html"),t.el.classList.add("has-iframe"),t.el.classList.add("has-"+t.type),t.el.prepend(o),u(t),e.emit("contentReady",t)}(i)}function s(e,t){var n,i;"html5video"!==t.type&&"youtube"!==t.type&&"vimeo"!==t.type||(null===(n=t.contentEl)||void 0===n||n.remove(),t.contentEl=void 0,null===(i=t.htmlEl)||void 0===i||i.remove(),t.htmlEl=void 0),t.poller&&clearTimeout(t.poller)}function l(){t=!1}function c(){if(t)return;t=!0;const e=i();(e&&void 0!==e.autoplay?e.autoplay:n().autoplay)&&(function(){var e;const t=i(),n=null==t?void 0:t.el;if(n&&"html5video"===(null==t?void 0:t.type))try{const e=n.querySelector("video");if(e){const t=e.play();void 0!==t&&t.then(()=>{}).catch(t=>{e.muted=!0,e.play()})}}catch(e){}const o=null==t?void 0:t.htmlEl;o instanceof HTMLIFrameElement&&(null===(e=o.contentWindow)||void 0===e||e.postMessage('{"event":"command","func":"stopVideo","args":""}',"*"))}(),function(){const e=i(),t=null==e?void 0:e.type;if(!(null==e?void 0:e.el)||"youtube"!==t&&"vimeo"!==t)return;const n=()=>{if(e.contentEl&&e.contentEl instanceof HTMLIFrameElement&&e.contentEl.contentWindow){let t;if("true"===e.contentEl.dataset.ready)return t="youtube"===e.type?{event:"command",func:"playVideo"}:{method:"play",value:"true"},t&&e.contentEl.contentWindow.postMessage(JSON.stringify(t),"*"),void(e.poller=void 0);"youtube"===e.type&&(t={event:"listening",id:e.contentEl.getAttribute("id")},e.contentEl.contentWindow.postMessage(JSON.stringify(t),"*"))}e.poller=setTimeout(n,250)};n()}())}function u(e){const t=null==e?void 0:e.htmlEl;if(e&&t&&("html5video"===e.type||"youtube"===e.type||"vimeo"===e.type)){if(t.style.aspectRatio="",t.style.width="",t.style.height="",t.style.maxWidth="",t.style.maxHeight="",e.width){let n=""+e.width;n.match(/^\d+$/)&&(n+="px"),t.style.maxWidth=""+n}if(e.height){let n=""+e.height;n.match(/^\d+$/)&&(n+="px"),t.style.maxHeight=""+n}if(e.aspectRatio){const n=e.aspectRatio.split("/"),i=parseFloat(n[0].trim()),o=n[1]?parseFloat(n[1].trim()):0,r=i&&o?i/o:i;t.offsetHeight;const a=t.getBoundingClientRect(),s=r<(a.width||1)/(a.height||1);t.style.aspectRatio=""+e.aspectRatio,t.style.width=s?"auto":"",t.style.height=s?"":"auto"}}}function d(){u(i())}return{init:function(t){e=t,e.on("addSlide",r),e.on("attachSlideEl",a),e.on("detachSlideEl",s),e.on("ready",c),e.on("change",l),e.on("settle",c),e.on("refresh",d),window.addEventListener("message",o)},destroy:function(){null==e||e.off("addSlide",r),null==e||e.off("attachSlideEl",a),null==e||e.off("detachSlideEl",s),null==e||e.off("ready",c),null==e||e.off("change",l),null==e||e.off("settle",c),null==e||e.off("refresh",d),window.removeEventListener("message",o),e=void 0}}},At={autoStart:!1,btnTpl:'<button data-fullscreen-action="toggle" class="f-button" title="{{TOGGLE_FULLSCREEN}}"><svg><g><path d="M8 3H5a2 2 0 0 0-2 2v3M21 8V5a2 2 0 0 0-2-2h-3M3 16v3a2 2 0 0 0 2 2h3M16 21h3a2 2 0 0 0 2-2v-3"/></g><g><path d="M15 19v-2a2 2 0 0 1 2-2h2M15 5v2a2 2 0 0 0 2 2h2M5 15h2a2 2 0 0 1 2 2v2M5 9h2a2 2 0 0 0 2-2V5"/></g></svg></button>'},Mt=()=>{let e;function t(t){const n=null==e?void 0:e.getOptions().Fullscreen;let i=(G(n)?Object.assign(Object.assign({},At),n):At)[t];return i&&"function"==typeof i&&e?i(e):i}function n(){var n;null===(n=null==e?void 0:e.getPlugins().Toolbar)||void 0===n||n.add("fullscreen",{tpl:t("btnTpl")})}function i(){if(t("autoStart")){const e=r();e&&s(e)}}function o(e,t){const n=t.target;n&&!t.defaultPrevented&&"toggle"===n.dataset.fullscreenAction&&c()}function r(){return t("el")||(null==e?void 0:e.getContainer())||void 0}function a(){const e=document;return e.fullscreenEnabled?!!e.fullscreenElement:!!e.webkitFullscreenEnabled&&!!e.webkitFullscreenElement}function s(e){const t=document;let n;return e||(e=t.documentElement),t.fullscreenEnabled?n=e.requestFullscreen():t.webkitFullscreenEnabled&&(n=e.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)),n&&n.then(()=>{e.classList.add("in-fullscreen-mode")}),n}function l(){const e=document;let t;return e.fullscreenEnabled?t=e.fullscreenElement&&e.exitFullscreen():e.webkitFullscreenEnabled&&(t=e.webkitFullscreenElement&&e.webkitExitFullscreen()),t&&t.then(()=>{var e;null===(e=r())||void 0===e||e.classList.remove("in-fullscreen-mode")}),t}function c(){const e=r();e&&(a()?l():s(e))}return{init:function(t){e=t,e.on("initPlugins",n),e.on("ready",i),e.on("click",o)},destroy:function(){null==e||e.off("initPlugins",n),null==e||e.off("ready",i),null==e||e.off("click",o)},exit:l,inFullscreen:a,request:s,toggle:c}};let kt,_t=!1,Pt=!1,Ot=!1,jt=!1;const qt=()=>{const e=new URL(document.URL).hash,t=e.slice(1).split("-"),n=t[t.length-1],i=n&&/^\+?\d+$/.test(n)&&parseInt(t.pop()||"1",10)||1;return{hash:e,slug:t.join("-"),index:i}},Rt=()=>{if(!kt||kt.getInstance())return;const{slug:e,index:t}=qt();if(!e)return;if(!kt||kt.getInstance())return;const n=document.querySelectorAll(`[data-fancybox="${e}"]`);if(!n.length)return;const i=n[t-1];i&&i.dispatchEvent(new CustomEvent("click",{bubbles:!0,cancelable:!0}))},It=()=>{var e,t;if(!kt)return;if(Ot)return;const n=null==kt?void 0:kt.getInstance();if(!1===(null==n?void 0:n.getOptions().Hash))return;const{slug:i,index:o}=qt(),r=(null===(e=null==n?void 0:n.getSlide())||void 0===e?void 0:e.fancybox)||void 0;n&&r&&(i===r?null===(t=n.getCarousel())||void 0===t||t.goTo(o-1):(jt=!0,n.close(),jt=!1)),Rt()},Dt=()=>{kt&&setTimeout(()=>{_t=!0,Rt(),_t=!1,window.addEventListener("hashchange",It,!1)},300)},Ft=()=>{let e,t="auto",n="";function i(){var i,r;if(!e||!e.isTopMost())return;if(!1===e.getOptions().Hash)return;const a=e.getCarousel();if(!a)return;const{hash:s,slug:l}=qt(),c=e.getSlide();if(!c)return;let u=c.fancybox||"",d=parseInt(c.index+"",10)+1;if(!u)return;let f=`#${u}-${d}`;if(((null===(r=null===(i=e.getCarousel())||void 0===i?void 0:i.getPages())||void 0===r?void 0:r.length)||0)<2&&(f="#"+u),s!==f&&(n=s),history.scrollRestoration&&(t=history.scrollRestoration,history.scrollRestoration="manual"),a.on("change",o),!_t)if(u===l)try{window.history.replaceState({},document.title,window.location.pathname+window.location.search+f)}catch(e){}else try{window.history.pushState({},document.title,window.location.pathname+window.location.search+f),Pt=!0}catch(e){}}function o(){if(!e||!e.isTopMost())return;if(!1===e.getOptions().Hash)return;const{slug:t}=qt(),n=e.getSlide();if(!n)return;let i=n.fancybox||"",o=`#${i}-${n.index+1}`;if(i===t){Ot=!0;try{window.history.replaceState({},document.title,window.location.pathname+window.location.search+o)}catch(e){}Ot=!1}}function r(){if(jt)return;if(!e||!e.isTopMost())return;if(!1===e.getOptions().Hash)return;const t=e.getSlide();if(t&&t.fancybox){Ot=!0;try{!Pt||_t||function(){if(window.parent===window)return!1;try{var e=window.frameElement}catch(t){e=null}return null===e?"data:"===location.protocol:e.hasAttribute("sandbox")}()?window.history.replaceState({},document.title,window.location.pathname+window.location.search+n):window.history.back()}catch(e){}Ot=!1}}return{init:function(t){e=t,e.on("ready",i),e.on("close",r)},destroy:function(){null==e||e.off("ready",i),null==e||e.off("close",r);const n=null==e?void 0:e.getCarousel();n&&n.off("change",o),e=void 0,history.scrollRestoration&&t&&(history.scrollRestoration=t)}}};Ft.startFromUrl=Rt,Ft.setup=function(e){kt||(kt=e,ne()&&(/complete|interactive|loaded/.test(document.readyState)?Dt():document.addEventListener("DOMContentLoaded",Dt)))};const Nt=Object.assign(Object.assign({},Xe),{CLOSE:"Close",NEXT:"Next",PREV:"Previous",MODAL:"You can close this modal content with the ESC key",ELEMENT_NOT_FOUND:"HTML Element Not Found",IFRAME_ERROR:"Error Loading Page"}),Ht='<button class="f-button" title="{{CLOSE}}" data-fancybox-close><svg><path d="M19.286 4.714 4.714 19.286M4.714 4.714l14.572 14.572" /></svg></button>';mt().add("close",{tpl:Ht});const Bt=e=>{e.cancelable&&e.preventDefault()},zt=e=>{e&&e.dispatchEvent(new CustomEvent("animationend",{bubbles:!1,cancelable:!0,currentTarget:e}))},$t=(e=null,t="",n)=>{if(!e||!e.parentElement||!t)return void(n&&n());zt(e);const i=o=>{o.target===e&&e.dataset.animationName&&(e.removeEventListener("animationend",i),delete e.dataset.animationName,n&&n(),e.classList.remove(t))};e.dataset.animationName=t,e.addEventListener("animationend",i),re(e,t)};var Wt;!function(e){e[e.Init=0]="Init",e[e.Ready=1]="Ready",e[e.Closing=2]="Closing",e[e.Destroyed=3]="Destroyed"}(Wt||(Wt={}));const Vt={ajax:null,backdropClick:"close",Carousel:{},closeButton:"auto",closeExisting:!1,delegateEl:void 0,dragToClose:!0,fadeEffect:!0,groupAll:!1,groupAttr:"data-fancybox",hideClass:"f-fadeOut",hideScrollbar:!0,id:void 0,idle:!1,keyboard:{Escape:"close",Delete:"close",Backspace:"close",PageUp:"next",PageDown:"prev",ArrowUp:"prev",ArrowDown:"next",ArrowRight:"next",ArrowLeft:"prev"},l10n:Nt,mainClass:"",mainStyle:{},mainTpl:'<dialog class="fancybox__dialog">\n    <div class="fancybox__container" tabindex="0" aria-label="{{MODAL}}">\n      <div class="fancybox__backdrop"></div>\n      <div class="fancybox__carousel"></div>\n    </div>\n  </dialog>',on:{},parentEl:void 0,placeFocusBack:!0,showClass:"f-zoomInUp",startIndex:0,sync:void 0,theme:"dark",triggerEl:void 0,triggerEvent:void 0,zoomEffect:!0},Ut=new Map;let Xt=0;const Yt=()=>{let e,t,n,i,o,r,a,s=Wt.Init,l=Object.assign({},Vt),c=-1,u={},d=[],f=!1,h=!0,p=0;function g(e,...t){let n=l[e];return n&&"function"==typeof n?n(Y,...t):n}function m(e,t=[]){const n=g("l10n")||{};e=String(e).replace(/\{\{(\w+)\}\}/g,(e,t)=>n[t]||e);for(let n=0;n<t.length;n++)e=e.split(t[n][0]).join(t[n][1]);return e.replace(/\{\{(.*?)\}\}/g,(e,t)=>t)}const v=new Map;function y(e,...t){const n=[...v.get(e)||[]];for(const[t,i]of Object.entries(l.on||{}))(t===e||t.split(" ").indexOf(e)>-1)&&n.push(i);for(const e of n)e&&"function"==typeof e&&e(Y,...t);"*"!==e&&y("*",e,...t)}function b(){ae(t,"is-revealing");try{document.activeElement===e&&((null==t?void 0:t.querySelector("[autofocus]"))||t).focus()}catch(e){}}function w(e,t){var n;j(t),M(),null===(n=t.el)||void 0===n||n.addEventListener("click",E),"inline"!==t.type&&"clone"!==t.type||function(e){if(!i||!e||!e.el)return;let t=null;if(Z(e.src)){const n=e.src.split("#",2).pop();t=n?document.getElementById(n):null}if(t){if(re(t,"f-html"),"clone"===e.type||t.closest(".fancybox__carousel")){t=t.cloneNode(!0);const n=t.dataset.animationName;n&&(t.classList.remove(n),delete t.dataset.animationName);let i=t.getAttribute("id");i=i?i+"--clone":`clone-${c}-${e.index}`,t.setAttribute("id",i)}else if(t.parentNode){const n=document.createElement("div");n.inert=!0,t.parentNode.insertBefore(n,t),e.placeholderEl=n}e.htmlEl=t,re(e.el,"has-html"),e.el.prepend(t),t.classList.remove("hidden"),"none"===t.style.display&&(t.style.display=""),"none"===getComputedStyle(t).getPropertyValue("display")&&(t.style.display=t.dataset.display||"flex"),null==i||i.emit("contentReady",e)}else null==i||i.showError(e,"{{ELEMENT_NOT_FOUND}}")}(t),"ajax"===t.type&&function(e){const t=e.el;if(!t)return;if(e.htmlEl||e.xhr)return;null==i||i.showLoading(e),e.state=0;const n=new XMLHttpRequest;n.onreadystatechange=function(){if(n.readyState===XMLHttpRequest.DONE&&s===Wt.Ready)if(null==i||i.hideLoading(e),e.state=1,200===n.status){let o=n.responseText+"",r=null,a=null;if(e.filter){const t=document.createElement("div");t.innerHTML=o,a=t.querySelector(e.filter+"")}a&&a instanceof HTMLElement?r=a:(r=document.createElement("div"),r.innerHTML=o),r.classList.add("f-html"),e.htmlEl=r,t.classList.add("has-html"),t.classList.add("has-ajax"),t.prepend(r),null==i||i.emit("contentReady",e)}else null==i||i.showError(e)};const o=g("ajax")||null;n.open(o?"POST":"GET",e.src+""),n.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.setRequestHeader("X-Requested-With","XMLHttpRequest"),n.send(o),e.xhr=n}(t)}function x(e,t){var n;!function(e){e.closeButtonEl&&(e.closeButtonEl.remove(),e.closeButtonEl=void 0)}(t),null===(n=t.el)||void 0===n||n.removeEventListener("click",E),"inline"!==t.type&&"clone"!==t.type||function(e){const t=e.htmlEl,n=e.placeholderEl;t&&("none"!==getComputedStyle(t).getPropertyValue("display")&&(t.style.display="none"),t.offsetHeight),n&&(t&&n.parentNode&&n.parentNode.insertBefore(t,n),n.remove()),e.htmlEl=void 0,e.placeholderEl=void 0}(t),t.xhr&&(t.xhr.abort(),t.xhr=void 0)}function E(e){if(!R())return;if(s!==Wt.Ready)return Bt(e),void e.stopPropagation();if(e.defaultPrevented)return;if(!Pe.isClickAllowed())return;const t=e.composedPath()[0];t.closest(".fancybox__carousel")&&t.classList.contains("fancybox__slide")&&k(e)}function S(){h=!1,t&&i&&t.classList.remove("is-revealing"),M();const e=g("sync");if(i&&e){const t=e.getPageIndex(i.getPageIndex())||0;e.goTo(t,{transition:!1})}}function L(){var e;if(s!==Wt.Ready)return;q(I()),function(){if(!g("dragToClose"))return;if(!i)return;const e=i.getViewport();if(!e)return;if(o=Pe(e).init(),!o)return;let n=!1,a=0,l=0,c={},u=1;function d(){var e,t;null==r||r.spring({clamp:!0,mass:1,tension:0===l?140:800,friction:17,restDelta:.1,restSpeed:.1,maxSpeed:1/0}).from({y:a}).to({y:l}).start();const n=(null===(e=null==i?void 0:i.getViewport())||void 0===e?void 0:e.getBoundingClientRect().height)||0,o=null===(t=I())||void 0===t?void 0:t.panzoomRef;if(n&&o)if(0===l)o.execute(Ie.Reset);else{const e=oe(Math.abs(a),0,.33*n,u,.75*u,!1);o.execute(Ie.ZoomTo,{scale:e})}}const f=e=>{var t;const n=e.srcEvent,o=n.target;return i&&!(be(n)&&(null===(t=n.touches)||void 0===t?void 0:t.length)>1)&&o&&!J(o)};r=ye().on("step",n=>{if(t&&e&&s===Wt.Ready){const i=e.getBoundingClientRect().height;a=Math.min(i,Math.max(-1*i,n.y));const o=oe(Math.abs(a),0,.5*i,1,0,!0);t.style.setProperty("--f-drag-opacity",o+""),t.style.setProperty("--f-drag-offset",a+"px")}}),o.on("start",(function(){n||(null==r||r.pause(),l=a)})).on("panstart",e=>{var t,o;if(!n&&f(e)&&"y"===e.axis){Bt(e.srcEvent),n=!0,W(),null===(t=null==i?void 0:i.getViewport())||void 0===t||t.classList.add("is-dragging");const r=null===(o=I())||void 0===o?void 0:o.panzoomRef;if(r){u=r.getTransform().scale||1;const e=r.getOptions();c=Object.assign({},e),e.bounds=!1,e.gestures=!1}}else n=!1}).on("pan",(function(e){n&&f(e)&&(Bt(e.srcEvent),e.srcEvent.stopPropagation(),"y"===e.axis&&(l+=e.deltaY,d()))})).on("end",e=>{var t,o,r;if(null===(t=null==i?void 0:i.getViewport())||void 0===t||t.classList.remove("is-dragging"),n){const t=null===(o=I())||void 0===o?void 0:o.panzoomRef;if(t){null===(r=t.getTween())||void 0===r||r.end();const e=t.getOptions();e.bounds=c.bounds||!1,e.gestures=c.gestures||!1}f(e)&&"y"===e.axis&&(Math.abs(e.velocityY)>5||Math.abs(a)>50)&&V(e.srcEvent,"f-throwOut"+(e.velocityY>0?"Down":"Up"))}n=!1,s===Wt.Ready&&0!==a&&(l=0,d())})}(),document.body.addEventListener("click",O),document.body.addEventListener("keydown",P,{passive:!1,capture:!0}),M(),H();const n=g("sync");n&&(null===(e=n.getTween())||void 0===e||e.start())}function T(){(null==i?void 0:i.canGoNext())?H():$()}function C(e,t){j(t),q(t)}function A(){var e;const n=null==i?void 0:i.getPlugins().Thumbs;se(t,"has-thumbs",(null==n?void 0:n.isEnabled())||!1),se(t,"has-vertical-thumbs",(null===(e=null==n?void 0:n.getCarousel())||void 0===e?void 0:e.isVertical())||!1)}function M(){if(t){const e=(null==i?void 0:i.getPages())||[],n=(null==i?void 0:i.getPageIndex())||0;for(const e of t.querySelectorAll("[data-fancybox-index]"))e.innerHTML=n+"";for(const e of t.querySelectorAll("[data-fancybox-page]"))e.innerHTML=n+1+"";for(const n of t.querySelectorAll("[data-fancybox-pages]"))n.innerHTML=e.length+""}}function k(e){e.composedPath()[0].closest("[data-fancybox-close]")?V(e):(y("backdropClick",e),e.defaultPrevented||g("backdropClick")&&V(e))}function _(){z()}function P(e){if(!R())return;if(s!==Wt.Ready)return;const t=e.key,n=g("keyboard");if(!n)return;if(e.ctrlKey||e.altKey||e.shiftKey)return;const o=e.composedPath()[0];if(!Q(o))return;if("Escape"!==t&&(e=>{const t=["input","textarea","select","option","video","iframe","[contenteditable]","[data-selectable]","[data-draggable]"].join(",");return e.matches(t)||e.closest(t)})(o))return;if(y("keydown",e),e.defaultPrevented)return;const r=n[t];if(r)switch(r){case"close":V(e);break;case"next":Bt(e),null==i||i.next();break;case"prev":Bt(e),null==i||i.prev()}}function O(e){if(!R())return;if(s!==Wt.Ready)return;if(z(),e.defaultPrevented)return;const t=e.composedPath()[0],n=!!t.closest("[data-fancybox-close]"),i=t.classList.contains("fancybox__backdrop");(n||i)&&k(e)}function j(e){var t;const{el:n,htmlEl:o,closeButtonEl:r}=e;if(!n||!o||r)return;let a=g("closeButton");if("auto"===a&&(a=!0!==(null===(t=null==i?void 0:i.getPlugins().Toolbar)||void 0===t?void 0:t.isEnabled())),a){const t=K(m(Ht));t&&(re(t,"is-close-button"),e.closeButtonEl=o.insertAdjacentElement("afterbegin",t),n.classList.add("has-close-btn"))}}function q(e){if(!(h&&i&&1===i.getState()&&e&&e.index===i.getOptions().initialPage&&e.el&&e.el.parentElement))return;if(void 0!==e.state&&1!==e.state)return;h=!1;const t=e.panzoomRef,n=null==t?void 0:t.getTween(),o=g("zoomEffect")&&n?F(e):void 0;if(t&&n&&o){const{x:e,y:i,scale:r}=t.getStartPosition();return void n.spring({tension:215,friction:25,restDelta:.001,restSpeed:.001,maxSpeed:1/0}).from(o).to({x:e,y:i,scale:r}).start()}const r=(null==t?void 0:t.getContent())||e.htmlEl;r&&$t(r,g("showClass",e))}function R(){var e;return(null===(e=Gt.getInstance())||void 0===e?void 0:e.getId())===c}function I(){var e;return null===(e=null==i?void 0:i.getPage())||void 0===e?void 0:e.slides[0]}function D(){const e=I();return e?e.triggerEl||g("triggerEl"):void 0}function F(e){var t,n;const i=e.thumbEl;if(!i||!(e=>{const t=e.getBoundingClientRect(),n=e.closest("[style]"),i=null==n?void 0:n.parentElement;if(n&&n.style.transform&&i){const e=i.getBoundingClientRect();if(t.left<e.left||t.left>e.left+e.width-t.width)return!1;if(t.top<e.top||t.top>e.top+e.height-t.height)return!1}const o=Math.max(document.documentElement.clientHeight,window.innerHeight),r=Math.max(document.documentElement.clientWidth,window.innerWidth);return!(t.bottom<0||t.top-o>=0||t.right<0||t.left-r>=0)})(i))return;const o=null===(n=null===(t=e.panzoomRef)||void 0===t?void 0:t.getWrapper())||void 0===n?void 0:n.getBoundingClientRect(),r=null==o?void 0:o.width,a=null==o?void 0:o.height;if(!r||!a)return;const s=i.getBoundingClientRect();let l=s.width,c=s.height,u=s.left,d=s.top;if(s&&l&&c){if(i instanceof HTMLImageElement){const e=window.getComputedStyle(i).getPropertyValue("object-fit");if("contain"===e||"scale-down"===e){const{width:t,height:n}=((e,t,n,i,o="contain")=>{if("contain"===o||e>n||t>i){const o=n/e,r=i/t,a=Math.min(o,r);e*=a,t*=a}return{width:e,height:t}})(i.naturalWidth,i.naturalHeight,l,c,e);u+=.5*(l-t),d+=.5*(c-n),l=t,c=n}}if(!(Math.abs(r/a-l/c)>.1))return{x:u+.5*l-(o.left+.5*r),y:d+.5*c-(o.top+.5*a),scale:l/r}}}function N(){a&&clearTimeout(a),a=void 0,document.removeEventListener("mousemove",_)}function H(){if(f)return;if(a)return;const e=g("idle");e&&(a=setTimeout(B,e))}function B(){t&&(N(),re(t,"is-idle"),document.addEventListener("mousemove",_),f=!0)}function z(){f&&($(),H())}function $(){N(),null==t||t.classList.remove("is-idle"),f=!1}function W(){if(g("placeFocusBack")){const t=D();!t||(e=t.getBoundingClientRect()).bottom>0&&e.right>0&&e.left<(window.innerWidth||document.documentElement.clientWidth)&&e.top<(window.innerHeight||document.documentElement.clientHeight)||t.scrollIntoView({behavior:"instant",block:"center",inline:"center"})}var e}function V(e,t){var n,a,l,c,u;if(s===Wt.Closing||s===Wt.Destroyed)return;const d=new Event("shouldClose",{bubbles:!0,cancelable:!0});if(y("shouldClose",d,e),d.defaultPrevented)return;if(N(),e){if(e.defaultPrevented)return;Bt(e),e.stopPropagation(),e.stopImmediatePropagation()}if(s=Wt.Closing,null==r||r.pause(),null==o||o.destroy(),i){null===(n=i.getGestures())||void 0===n||n.destroy(),null===(a=i.getTween())||void 0===a||a.pause();for(const e of i.getSlides()){const t=e.panzoomRef;t&&(te(t.getOptions(),{clickAction:!1,dblClickAction:!1,wheelAction:!1,bounds:!1,minScale:0,maxScale:1/0}),null===(l=t.getGestures())||void 0===l||l.destroy(),null===(c=t.getTween())||void 0===c||c.pause())}}const f=null==i?void 0:i.getPlugins();null===(u=null==f?void 0:f.Autoplay)||void 0===u||u.stop();const h=null==f?void 0:f.Fullscreen;h&&h.inFullscreen()?Promise.resolve(h.exit()).then(()=>{setTimeout(()=>{U(e,t)},150)}):U(e,t)}function U(e,n){var o,r,a,l;if(s!==Wt.Closing)return;y("close",e),document.body.removeEventListener("click",O),document.body.removeEventListener("keydown",P,{passive:!1,capture:!0}),W(),g("fadeEffect")&&(null==t||t.classList.remove("is-ready"),null==t||t.classList.add("is-hiding")),null==t||t.classList.add("is-closing");const c=I(),u=null==c?void 0:c.panzoomRef,d=null===(o=null==c?void 0:c.panzoomRef)||void 0===o?void 0:o.getTween(),f=n||g("hideClass");let h=!1,p=!1;if(i&&c&&u&&d){let e;g("zoomEffect")&&((null===(a=null===(r=i.getTween())||void 0===r?void 0:r.getCurrentVelocities())||void 0===a?void 0:a.pos)||0)<700&&1===c.state&&(e=F(c)),e&&(h=!0,i.on("refresh",()=>{const e=F(c);e&&d.to(Object.assign(Object.assign({},Ne),e))}),d.easing(ye.Easings.EaseOut).duration(350).from(Object.assign({},u.getTransform())).to(Object.assign(Object.assign({},Ne),e)).start())}const m=(null==c?void 0:c.htmlEl)||(null===(l=null==c?void 0:c.panzoomRef)||void 0===l?void 0:l.getWrapper());m&&zt(m),!h&&f&&m&&(p=!0,$t(m,f,()=>{X()})),h||p?setTimeout(()=>{X()},350):X()}function X(){var n,o,r,a,l;if(s===Wt.Destroyed)return;s=Wt.Destroyed;const d=D();y("destroy"),null===(o=null===(n=g("sync"))||void 0===n?void 0:n.getPlugins().Autoplay)||void 0===o||o.resume(),null===(a=null===(r=g("sync"))||void 0===r?void 0:r.getPlugins().Autoscroll)||void 0===a||a.resume(),e instanceof HTMLDialogElement&&e.close(),null===(l=null==i?void 0:i.getContainer())||void 0===l||l.classList.remove("is-idle"),null==i||i.destroy();for(const e of Object.values(u))null==e||e.destroy();if(u={},null==e||e.remove(),e=void 0,t=void 0,i=void 0,Ut.delete(c),!Ut.size&&(ee(!1),document.documentElement.classList.remove("with-fancybox"),g("placeFocusBack")))try{null==d||d.focus({preventScroll:!0})}catch(n){}}const Y={close:V,destroy:X,getCarousel:function(){return i},getContainer:function(){return t},getId:function(){return c},getOptions:function(){return l},getPlugins:function(){return u},getSlide:function(){return I()},getState:function(){return s},init:function(o=[],r={}){return function(o=[],r={}){s!==Wt.Init&&(Y.destroy(),s=Wt.Init),l=te({},Vt,r),c=g("id")||"fancybox-"+ ++Xt;const a=Ut.get(c);if(a&&a.destroy(),Ut.set(c,Y),y("init"),function(){for(const[e,t]of Object.entries(Object.assign(Object.assign({},Gt.Plugins),l.plugins||{})))if(e&&!u[e]&&t instanceof Function){const n=t();n.init(Y),u[e]=n}y("initPlugins")}(),function(e=[]){y("initSlides",e),d=[...e]}(o),function(){const n=g("parentEl")||document.body;if(!(n&&n instanceof HTMLElement))return;const o=m(g("mainTpl")||"");if(e=K(o)||void 0,!e)return;if(t=e.querySelector(".fancybox__container"),!(t&&t instanceof HTMLElement))return;const r=g("mainClass");r&&re(t,r);const a=g("mainStyle");if(a&&G(a))for(const[e,n]of Object.entries(a))t.style.setProperty(e,n);const s=g("theme"),l="auto"===s?window.matchMedia("(prefers-color-scheme:light)").matches:"light"===s;t.setAttribute("theme",l?"light":"dark"),e.setAttribute("id",""+c),e.addEventListener("keydown",e=>{"Escape"===e.key&&Bt(e)}),e.addEventListener("wheel",e=>{const t=e.target;let n=g("wheel",e);t.closest(".f-thumbs")&&(n="slide");const o="slide"===n,r=[-e.deltaX||0,-e.deltaY||0,-e.detail||0].reduce((function(e,t){return Math.abs(t)>Math.abs(e)?t:e})),a=Math.max(-1,Math.min(1,r)),s=Date.now();p&&s-p<300?o&&Bt(e):(p=s,y("wheel",e,a),e.defaultPrevented||("close"===n?V(e):"slide"===n&&i&&!J(t)&&(Bt(e),i[a>0?"prev":"next"]())))},{capture:!0,passive:!1}),e.addEventListener("cancel",e=>{V(e)}),n.append(e),1===Ut.size&&(g("hideScrollbar")&&ee(!0),document.documentElement.classList.add("with-fancybox")),e instanceof HTMLDialogElement&&e.showModal(),y("initLayout")}(),function(){if(n=(null==e?void 0:e.querySelector(".fancybox__carousel"))||void 0,!n)return;n.fancybox=Y;const o=te({},{Autoplay:{autoStart:!1,pauseOnHover:!1,progressbarParentEl:e=>{const t=e.getContainer();return(null==t?void 0:t.querySelector(".f-carousel__toolbar [data-autoplay-action]"))||t}},Fullscreen:{el:t},Toolbar:{absolute:!0,items:{counter:{tpl:'<div class="f-counter"><span data-fancybox-page></span>/<span data-fancybox-pages></span></div>'}},display:{left:["counter"],right:["toggleFull","autoplay","fullscreen","thumbs","close"]}},Video:{autoplay:!0},Thumbs:{minCount:2,Carousel:{classes:{container:"fancybox__thumbs"}}},classes:{container:"fancybox__carousel",viewport:"fancybox__viewport",slide:"fancybox__slide"},spinnerTpl:'<div class="f-spinner" data-fancybox-close></div>',dragFree:!1,slidesPerPage:1,plugins:{Sync:it,Arrows:st,Lazyload:rt,Zoomable:tt,Html:St,Video:Ct,Autoplay:yt,Fullscreen:Mt,Thumbs:xt,Toolbar:mt}},g("Carousel")||{},{slides:d,enabled:!0,initialPage:g("startIndex")||0,l10n:g("l10n")});i=Ke(n,o),y("initCarousel",i),i.on("*",(e,t,...n)=>{y("Carousel."+t,e,...n)}),i.on("attachSlideEl",w),i.on("detachSlideEl",x),i.on("contentReady",C),i.on("ready",L),i.on("change",S),i.on("settle",T),i.on("thumbs:ready",A),i.on("thumbs:destroy",A),i.init()}(),e&&t){if(g("closeExisting"))for(const[e,t]of Ut.entries())e!==c&&t.close();g("fadeEffect")?(setTimeout(()=>{b()},500),re(t,"is-revealing")):b(),t.classList.add("is-ready"),s=Wt.Ready,y("ready")}}(o,r),Y},isCurrentSlide:function(e){const t=I();return!(!e||!t)&&t.index===e.index},isTopMost:function(){return R()},off:function(e,t){return v.has(e)&&v.set(e,v.get(e).filter(e=>e!==t)),Y},on:function(e,t){return v.set(e,[...v.get(e)||[],t]),Y},toggleIdle(e){(f||!0===e)&&B(),f&&!1!==e||$()}};return Y},Gt={Plugins:{Hash:Ft},version:"6.0.34",openers:new Map,bind:function(e,t,n){if(!ne())return;let i=document.body,o="[data-fancybox]",r={};if(Z(e)?(i=document.body,o=e,"object"==typeof t&&(r=t||{})):e instanceof Element&&(i=e,Z(t)&&(o=t),"object"==typeof n&&(r=n||{})),!(i&&i instanceof Element&&o))return!1;const a=Gt.openers.get(i)||new Map;if(a.set(o,r),Gt.openers.set(i,a),1===a.size){i.addEventListener("click",Gt.fromEvent);for(const e of Object.values(Gt.Plugins)){const t=e.setup;"function"==typeof t&&t(Gt)}}return!0},close:function(e=!0,...t){if(e)for(const e of Ut.values())e.close(...t);else{const e=Gt.getInstance();e&&e.close(...t)}},destroy:function(){let e;for(;e=Gt.getInstance();)e.destroy();for(const e of Gt.openers.keys())e.removeEventListener("click",Gt.fromEvent);Gt.openers.clear()},fromEvent:function(e){var t,n,i;if(e.defaultPrevented)return;if(e.button&&0!==e.button)return;if(e.ctrlKey||e.metaKey||e.shiftKey)return;let o=e.composedPath()[0];if(o.closest(".fancybox__container.is-hiding"))return Bt(e),void e.stopPropagation();const r=o.closest("[data-fancybox-delegate]")||void 0;if(r){const e=r.dataset.fancyboxDelegate||"",t=document.querySelectorAll(`[data-fancybox="${e}"]`);o=t[parseInt(r.dataset.fancyboxIndex||"",10)||0]||t[0]}if(!(o&&o instanceof Element))return;let a,s,l={};for(const[t,n]of Gt.openers)if(n&&t.contains(o))for(const[i,r]of n){let n=null;try{n=o.closest(i)}catch(e){}n&&(o=n,a=t,s=i,te(l,r||{}))}if(!a||!s)return;Bt(e);const c=te({},Vt,{triggerEvent:e,triggerEl:o,delegateEl:r},l),u=c.groupAll,d=c.groupAttr,f=d&&o?o.getAttribute(""+d):"";let h=[];const p=null===(t=o.closest(".f-carousel"))||void 0===t?void 0:t.carousel;if(p){const e=[];for(const t of null==p?void 0:p.getSlides()){const n=t.el;n&&(n.matches(s)?e.push(n):e.push(...[].slice.call(n.querySelectorAll(s))))}e.length&&(h=[...e],null===(n=p.getPlugins().Autoplay)||void 0===n||n.pause(),null===(i=p.getPlugins().Autoscroll)||void 0===i||i.pause(),c.sync=p)}else(!o||f||u)&&(h=[].slice.call(a.querySelectorAll(s)));if(o&&!u&&(h=d&&f?h.filter(e=>e.getAttribute(""+d)===f):[o]),!h.length)return;const g=Gt.getInstance();if(g){const e=g.getOptions().triggerEl;if(e&&h.indexOf(e)>-1)return}return Object.assign({},c.Carousel||{}).rtl&&(h=h.reverse()),o&&(c.startIndex=h.indexOf(o)),Gt.fromNodes(h,c)},fromNodes:function(e,t){t=te({},Vt,t||{});const n=[],i=e=>e instanceof HTMLImageElement?e:e instanceof HTMLElement?e.querySelector("img:not([aria-hidden])"):void 0;for(const o of e){const r=o.dataset||{},a=t.delegateEl&&e.indexOf(o)===t.startIndex?t.delegateEl:void 0,s=i(a)||i(o)||void 0,l=r.src||o.getAttribute("href")||o.getAttribute("currentSrc")||o.getAttribute("src")||void 0,c=r.thumb||r.thumbSrc||(null==s?void 0:s.getAttribute("currentSrc"))||(null==s?void 0:s.getAttribute("src"))||(null==s?void 0:s.dataset.lazySrc)||void 0,u={src:l,alt:r.alt||(null==s?void 0:s.getAttribute("alt"))||void 0,thumbSrc:c,thumbEl:s,triggerEl:o,delegateEl:a};for(const e in r){let t=r[e]+"";t="false"!==t&&("true"===t||t),u[e]=t}n.push(u)}return Gt.show(n,t)},fromSelector:function(e,t,n){let i=document.body,o="",r={};if(Z(e)?o=e:e instanceof Element&&(i=e,Z(t)&&(o=t),"object"==typeof n&&(r=n||{})),!(i&&i instanceof Element&&o))return;const a=Gt.openers.get(i);return a?(r=te({},a.get(o)||{},r),r?Gt.fromNodes(Array.from(i.querySelectorAll(o)),r):void 0):void 0},getCarousel:function(){var e;return(null===(e=Gt.getInstance())||void 0===e?void 0:e.getCarousel())||void 0},getDefaults:function(){return Vt},getInstance:function(e){if(e){const t=Ut.get(e);return t&&t.getState()!==Wt.Destroyed?t:void 0}return Array.from(Ut.values()).reverse().find(e=>{if(e.getState()!==Wt.Destroyed)return e})||void 0},getSlide:function(){var e;return(null===(e=Gt.getInstance())||void 0===e?void 0:e.getSlide())||void 0},show:function(e=[],t={}){return Yt().init(e,t)},unbind:function(e,t){let n=document.body,i="";if(Z(e)?i=e:e instanceof Element&&(n=e,Z(t)&&(i=t)),n){const e=Gt.openers.get(n);e&&i&&e.delete(i),(null==e?void 0:e.size)&&i||Gt.openers.delete(n),n.removeEventListener("click",Gt.fromEvent)}}};n(5);document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("produkt_bild_link");if(!e)return;const t={Carousel:{Thumbs:{type:"modern",showOnStart:!0},Toolbar:{display:{left:["infobar"],middle:[],right:["thumbs","close"]}}}},n=document.querySelectorAll(".product-box > .media .thumbs .thumb, .picture_box_carusel a"),i=[...n].map(e=>({src:e.href,thumbSrc:(e.querySelector("img")||{}).src})),o=(e,n)=>{t.startIndex=n,Gt.show(e,t)};n.forEach((e,t)=>{e.addEventListener("click",e=>{e.preventDefault(),o(i,t)})}),e.addEventListener("click",t=>{t.preventDefault();const n={src:e.href};if(0===i.length){const e=[n],t=document.getElementById("eek_label");t&&e.push({src:t.href}),o(e,0)}else{const t=e.href;let n=i.findIndex(e=>e.src===t);-1===n&&(n=0),o(i,n)}}),e.querySelector("img").addEventListener("dragstart",(function(){event.preventDefault(),event.stopPropagation()}));const r=document.getElementById("eek_label");r&&r.addEventListener("click",e=>{e.preventDefault(),Gt.show([{src:r.href}],t)});const a=document.querySelector(".eek_arrow");a&&(a.addEventListener("click",e=>{e.preventDefault(),Gt.show([{src:a.href}],t)}),"#eek_arrow"===window.location.hash&&(a.click(),history.replaceState(null,null," "))),r&&i.length>0&&i.push({src:r.href})});n(6);r('#device_list_container input[type="text"]').on("keyup",(function(e){if(""!=this.value&&this.value.length<3)return;let t=r(this).data("url")+"&phrase="+encodeURIComponent(this.value);window.device_list_container_request&&window.clearTimeout(window.device_list_container_request),window.device_list_container_request=window.setTimeout(()=>r("#device_list").load(t),100)}));var Zt=class{constructor(){this.inited=new WeakSet}init(){var e=this;document.querySelectorAll(".product-tabs").forEach((function(t){e.enhance(t)}))}enhance(e){if(!this.inited.has(e)){this.inited.add(e);var t=e.querySelector(".nav");if(t){var n=Array.prototype.slice.call(t.querySelectorAll(".tab")),i=Array.prototype.slice.call(e.querySelectorAll(".content > .panel"));if(n.length&&i.length){t.setAttribute("role",t.getAttribute("role")||"tablist"),n.forEach((function(e){e.setAttribute("role",e.getAttribute("role")||"tab")})),i.forEach((function(e){e.setAttribute("role",e.getAttribute("role")||"tabpanel")}));var o=function(e){e&&(n.forEach((function(t){var n=t.getAttribute("data-tab")===e;t.classList.toggle("-active",n),t.setAttribute("aria-selected",n?"true":"false"),t.setAttribute("tabindex",n?"0":"-1")})),i.forEach((function(t){var n=t.getAttribute("data-panel")===e;t.classList.toggle("-active",n),n?t.removeAttribute("hidden"):t.setAttribute("hidden","")})))},r=function(e){if(!(e<0||e>=n.length)){var t=n[e];t&&t.focus()}},a=function(e,t){var i=(e+t+n.length)%n.length;return r(i),i};n.forEach((function(t,i){t.addEventListener("click",(function(){var n=t.getAttribute("data-tab");n&&(t.classList.contains("-active")||(o(n),window.innerWidth<1024&&setTimeout((function(){var t=e.querySelector(".content");if(t){var n=t.getBoundingClientRect(),i=window.pageYOffset||document.documentElement.scrollTop,o=n.top+i-100;window.scrollTo({top:Math.max(0,o),behavior:"smooth"})}}),100)))})),t.addEventListener("keydown",(function(e){switch(e.key){case"ArrowRight":case"ArrowDown":e.preventDefault();var s=a(i,1);n[s]&&o(n[s].getAttribute("data-tab"));break;case"ArrowLeft":case"ArrowUp":e.preventDefault();var l=a(i,-1);n[l]&&o(n[l].getAttribute("data-tab"));break;case"Home":e.preventDefault(),r(0),n[0]&&o(n[0].getAttribute("data-tab"));break;case"End":e.preventDefault();var c=n.length-1;r(c),n[c]&&o(n[c].getAttribute("data-tab"));break;case" ":case"Space":case"Spacebar":case"Enter":e.preventDefault(),o(t.getAttribute("data-tab"))}}))}));for(var s=null,l=0;l<n.length;l+=1)if(n[l].classList.contains("-active")){s=n[l];break}s||(s=n[0]),s&&o(s.getAttribute("data-tab"))}}}}};var Qt=class{constructor(){this.tracked=new WeakSet,this.initialized=!1,this.refresh=this.refresh.bind(this)}init(){this.initialized||(this.initialized=!0,H(this.refresh))}refresh(){var e=this;document.querySelectorAll("[data-expand-container], [data-expand-target]").forEach((function(t){e.tracked.has(t)||(e.tracked.add(t),t.addEventListener("click",(function(){e.handleExpandTrigger(t)}),{once:!0}))}))}handleExpandTrigger(e){var t=this.findExpandContainer(e);t&&(this.expandContainer(t),this.revealHiddenItems(t,e),this.removeTriggerButton(e))}findExpandContainer(e){var t=e.getAttribute("data-expand-target");return t?document.querySelector(t):e.hasAttribute("data-expand-container")&&e.closest(".category-cards-grid, .top-products")||e.parentElement}expandContainer(e){e.classList.add("is-expanded")}revealHiddenItems(e,t){var n=t.getAttribute("data-reveal-class");n&&e.querySelectorAll("."+n).forEach((function(e){e.classList.remove(n),"-hidden"===n&&e.style.removeProperty("display")}))}removeTriggerButton(e){e.hasAttribute("data-remove-self")&&e.parentNode&&e.parentNode.removeChild(e)}};function Kt(e,t){const n=function(){try{return window.matchMedia&&window.matchMedia("(prefers-reduced-motion: reduce)").matches}catch(e){return!1}}();if(n&&!t.forceSmooth)return void window.scrollTo(0,e);if("scrollBehavior"in document.documentElement.style&&!n)return void window.scrollTo({top:e,behavior:"smooth"});const i=window.pageYOffset||document.documentElement.scrollTop||0,o=Math.max(0,e)-i,r=Math.max(0,0|(t&&t.duration))||300;if(0===r||0===o)return void window.scrollTo(0,Math.max(0,e));const a=performance.now();requestAnimationFrame((function e(t){const n=Math.min(1,(t-a)/r),s=function(e){return e<.5?2*e*e:(4-2*e)*e-1}(n);window.scrollTo(0,Math.round(i+o*s)),n<1&&requestAnimationFrame(e)}))}function Jt(e,t){const n=e;t=Object.assign({duration:300,forceSmooth:!1},t||{});let i="hide";const o=()=>{n.style.transition||(n.style.transition="opacity 200ms ease")},r=()=>{n.style.top=window.innerHeight-100+"px",n.style.opacity="0",n.style.display="none",i="hide"};r(),window.addEventListener("resize",()=>{r()}),window.addEventListener("scroll",()=>{window.scrollY>100?"hide"!==i||window.hide_top_button||"show"!==i&&(o(),n.style.display="",n.offsetHeight,n.style.opacity="1",i="show"):"show"===i&&(()=>{if("hide"===i)return;o(),n.style.opacity="0";const e=()=>{n.style.display="none",n.removeEventListener("transitionend",e)};n.addEventListener("transitionend",e),i="hide"})()}),n.addEventListener("click",e=>{e&&"function"==typeof e.stopImmediatePropagation?e.stopImmediatePropagation():e&&"function"==typeof e.stopPropagation&&e.stopPropagation(),e&&"function"==typeof e.preventDefault&&e.preventDefault();Kt(0,t)})}r(".expandlink").css("cursor","pointer").bind("click",(function(e){if(!r(e.target).closest("a").length){var t=r(this);(t.find("a.expandlink_pri").first().length?t.find("a.expandlink_pri").first():t.find("a").first()).get(0).click()}}));var en=class{constructor(){}init(){document.querySelectorAll("[data-modal-id]").forEach(e=>{const t=e.getAttribute("data-modal-id"),n=document.getElementById(t);e.addEventListener("click",e=>{const t=new l({id:"extra_service_window",classname:"extra_service_window",close_button:!0});t.show(),t.html(n.innerHTML)})})}};if(window.onerror=function(e,t,n){const i=String(e||"");if(!i.includes("_AutofillCallbackHandler"))try{const e=new URLSearchParams({message:i,url:String(t||""),line:String(n||"")}).toString();fetch("/api/js.php",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"},body:e})}catch(e){}},window.location&&"#terror"===window.location.hash)throw new Error('URL anchor "#error" detected');window.RecentlyViewed=P,("ontouchstart"in window||navigator.maxTouchPoints>0)&&document.addEventListener("touchstart",(function(){}),{passive:!0});const tn=new URL(window.location.href);tn.searchParams.has("t")&&(tn.searchParams.delete("t"),window.history.replaceState({},"",tn.toString()));const nn=new class{constructor(){this._processed=new WeakSet,this._buttonTimeouts=new WeakMap,this.rootClass="basket-modal-window",this.modal=new l({id:"basket-modal-window",classname:this.rootClass,height:"auto"})}init(){this._enhance("#put_in_basket, .put_in_basket, .put_in_basket_service, .std_form.put_in_basket",e=>{this._setupFormQuantityControls(e),this._setupFormSubmission(e)}),this._enhance("#put_in_basket .expres_checkout, .put_in_basket .expres_checkout, .put_in_basket_service .expres_checkout",e=>this._setupExpressCheckout(e)),this._enhance('.qty[role="group"]',e=>this._setupQtyGroup(e))}_enhance(e,t){document.querySelectorAll(e).forEach(e=>{if(!this._processed.has(e)){this._processed.add(e);try{t(e)}catch(e){}}})}_setupFormQuantityControls(e){const t=e.querySelector('input.input[type="number"][name="menge"]');if(!t)return;const n=e.querySelector(".qty .button:first-child");n&&n.addEventListener("click",e=>{e.preventDefault();const n=parseInt(t.value,10)||1;t.value=Math.max(1,n-1),t.dispatchEvent(new CustomEvent("change"))});const i=e.querySelector(".qty .button:last-child");i&&i.addEventListener("click",e=>{e.preventDefault();const n=parseInt(t.value,10)||1;t.value=n+1,t.dispatchEvent(new CustomEvent("change"))})}_setupFormSubmission(e){e instanceof HTMLFormElement&&(e.addEventListener("submit",t=>this._handleFormSubmit(t,e)),e.querySelectorAll("[data-click-submit]").forEach(t=>{t.addEventListener("click",t=>{t.stopPropagation(),this._handleFormSubmit(t,e)})}))}_setupExpressCheckout(e){const t=e instanceof HTMLElement?e:null;t&&t.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation();const n=t.closest("form");n&&this._submitPaypal(n)})}_handleFormSubmit(e,t){if(e.preventDefault(),e.stopPropagation(),!(t instanceof HTMLFormElement))return;const n=t.querySelector('button.action[type="submit"]');n&&this._prepareSubmitButton(n),this._addItem(t,n)}async _addItem(e,t){const n=this._serializeForm(e);n.set("modus","ajax");try{const e=await fetch("/warenkorb/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8","X-Requested-With":"XMLHttpRequest"},body:n.toString()});if(!e.ok)throw new Error("Request failed with status "+e.status);const i=await this._parseResponsePayload(e);this._updateCartWidget(i),this._showSuccess(t,i.html),i&&"object"==typeof i&&i.tracker&&this._executeTracker(i.tracker);const o=new CustomEvent("putInBasket:success");document.dispatchEvent(o)}catch(e){console.error(e),this._handleSubmitError(t),A.show("error","Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.")}}async _parseResponsePayload(e){const t=await e.text();try{return JSON.parse(t)}catch(e){return t}}_updateCartWidget(e){if(!e||"object"!=typeof e||!e.html||void 0===e.count)return;const t=document.querySelector(".cart-widget .count");t&&(t.textContent=e.count);const n=document.querySelector(".cart-widget");if(!n)return;n.classList.toggle("-has-items",e.count>0);const i=n.querySelector(".icon-btn img");if(i){const t=e.count>0?n.dataset.filledIcon:n.dataset.emptyIcon;t&&i.setAttribute("src",t)}}_executeTracker(e){if(!e||"string"!=typeof e)return;const t=document.createElement("div");t.innerHTML=e;t.querySelectorAll("script").forEach(e=>{const t=document.createElement("script");for(let n=0;n<e.attributes.length;n++){const i=e.attributes[n];t.setAttribute(i.name,i.value)}t.text=e.textContent||e.innerText||"",document.head.appendChild(t),document.head.removeChild(t)})}_showSuccess(e,t){this.modal.outer&&this.modal.close(new CustomEvent("null")),this.modal.show(),$(this.modal.content).html(t);const n=document.querySelector(`.${this.rootClass} input.input[type="number"][name="menge"]`),i=document.querySelector(".amount form.put_in_basket");n.addEventListener("change",e=>{this._handleFormSubmit(e,i)}),e&&this._scheduleButtonReset(e,1e3)}_handleSubmitError(e){e&&(this._clearButtonTimeout(e),this._resetButton(e))}_prepareSubmitButton(e){this._clearButtonTimeout(e),e.classList.add("is-adding"),e.disabled=!0,e.setAttribute("aria-busy","true")}_scheduleButtonReset(e,t){this._clearButtonTimeout(e);const n=window.setTimeout(()=>{this._resetButton(e),this._buttonTimeouts.delete(e)},t);this._buttonTimeouts.set(e,n)}_clearButtonTimeout(e){const t=this._buttonTimeouts.get(e);t&&(window.clearTimeout(t),this._buttonTimeouts.delete(e))}_resetButton(e){e.classList.remove("is-adding"),e.disabled=!1,e.setAttribute("aria-busy","false")}_submitPaypal(e){const t=this._serializeForm(e);t.set("modus","paypal"),window.location.href="/warenkorb/?"+t.toString()}_serializeForm(e){const t=new FormData(e);return new URLSearchParams(t)}_setupQtyGroup(e){const t=e.querySelector("button[aria-label], .button:first-child"),n=e.querySelector("button:last-child");if(t&&n&&(e.style.cursor||(e.style.cursor="pointer"),e.addEventListener("click",i=>{if(i.target.closest("button")||i.target.closest("input"))return;const o=e.getBoundingClientRect(),r=i.clientX-o.left,a=e.querySelector('input[type="number"]'),s=a?parseInt(a.value,10)||1:null;r<o.width/2?(t.click(),a&&(parseInt(a.value,10)||1)===s&&(a.value=Math.max(1,s-1))):(n.click(),a&&(parseInt(a.value,10)||1)===s&&(a.value=s+1)),a.dispatchEvent(new CustomEvent("change"))}),!e.closest(".product-sticky-cta"))){const t=t=>{if(t&&t.target&&t.target.closest("input"))return e.classList.remove("-hover-left"),void e.classList.remove("-hover-right");const n=e.getBoundingClientRect();(t&&"number"==typeof t.clientX?t.clientX-n.left:n.width/2)<n.width/2?(e.classList.add("-hover-left"),e.classList.remove("-hover-right")):(e.classList.add("-hover-right"),e.classList.remove("-hover-left"))};e.addEventListener("mousemove",t),e.addEventListener("mouseenter",t),e.addEventListener("mouseleave",()=>{e.classList.remove("-hover-left"),e.classList.remove("-hover-right")})}}},on=new class{constructor(){this._processed=new WeakSet,this._gridElementsPerRow=j()?1:4,this._displayRows=2,this._rootSelector=".page-cart",this._cardSelector=".cart-product-card",this._moreSelector=".show-all-products",this._hasMore=!1,this._ajaxSelector="#ajax-cart",document.addEventListener("putInBasket:success",()=>{if(!document.querySelector(this._rootSelector))return;const e=document.querySelector(this._ajaxSelector);window.ajaxLoader&&e?window.ajaxLoader.loadContent("/warenkorb/",e):window.location.href="/warenkorb/"})}init(){this._enhance(this._moreSelector,e=>{e.addEventListener("click",()=>{this._displayRows=999,e.classList.toggle("-hidden"),this._render()})}),this._enhance(".extra-service-checkbox",e=>{e.addEventListener("change",t=>{const n=`/warenkorb/?service_set=${e.getAttribute("data-extra-service-id")}&item_id=${e.getAttribute("data-item-id")}&value=${e.checked?1:0}`;window.ajaxLoader?(t.preventDefault(),window.ajaxLoader.loadContent(n,document.querySelector(this._ajaxSelector))):document.location.href=n})}),this._render()}_render(){document.querySelectorAll(this._cardSelector).forEach((e,t)=>{t>=this._gridElementsPerRow*this._displayRows?(e.classList.add("-hidden"),this._hasMore=!0):e.classList.remove("-hidden")}),this._hasMore||document.querySelectorAll(this._moreSelector).forEach(e=>{e.classList.add("-hidden")})}_enhance(e,t){const n=document.querySelector(this._rootSelector);n&&n.querySelectorAll(e).forEach(e=>{if(!this._processed.has(e)){this._processed.add(e);try{t(e)}catch(e){}}})}},rn=new class{constructor(){}init(){let e=document.getElementById("paypal-buttons-product");e&&this.loadPayPalButtonForProduct(e),e=document.getElementById("paypal-buttons-basket"),e&&this.loadPayPalButtonForBasket(e),e=document.getElementById("step3_paypal"),e&&this.loadPayPalButtonForCheckout(e)}async loadPayPalButtonForProduct(e){const t=e.getAttribute("data-client-id"),n=await this.loadPaypalSdk(t),i=document.getElementById("put_in_basket");if(!i)return;const o=i.querySelector('input[name="add_item"]').value,r=i.querySelector('input[name="menge"]');n.Buttons({style:{layout:j()?"vertical":"horizontal",color:"gold",label:"paypal",borderRadius:16,disableMaxWidth:!0,height:55,tagline:!1},async createOrder(){const e=await fetch("/api/paypal/product/",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:`product_id=${encodeURIComponent(o)}&quantity=${encodeURIComponent(r.value)}`});return(await e.json()).id},async onApprove(e){window.location.href="/kasse/?do=paypal_direct&token="+e.orderID}}).render(e)}async loadPayPalButtonForBasket(e){const t=e.getAttribute("data-client-id");(await this.loadPaypalSdk(t)).Buttons({style:{layout:j()?"vertical":"horizontal",color:"gold",label:"paypal",borderRadius:16,disableMaxWidth:!0,height:55,tagline:!1},async createOrder(){const e=await fetch("/api/paypal/basket/",{method:"POST"});return(await e.json()).id},async onApprove(e){window.location.href="/kasse/?do=paypal_direct&token="+e.orderID}}).render(e)}async loadPayPalButtonForCheckout(e){const t=e.getAttribute("data-client-id"),n=await this.loadPaypalSdk(t);try{e.style.width="100%",e.style.maxWidth="100%",e.style.minWidth="0",e.style.display="block"}catch(e){}n.Buttons({style:{layout:"vertical",color:"gold",label:"paypal",borderRadius:16,disableMaxWidth:!0,height:55},async createOrder(){const e=await fetch("/api/paypal/checkout/",{method:"POST"});return(await e.json()).id},async onApprove(e){window.location.href="/kasse/?do=paypal&token="+e.orderID}}).render(e);document.querySelectorAll('input[name="zahlungsart"]').forEach(e=>{e.addEventListener("change",()=>{this.switchButtons()})}),this.switchButtons()}switchButtons(){const e=document.querySelector('input[name="zahlungsart"]:checked'),t=15==(e?e.value:null);document.getElementById("step3_paypal").style.display=t?"block":"none",document.getElementById("step3_normal").style.display=t?"none":"block"}loadPaypalSdk(e){return new Promise((t,n)=>{const i=document.getElementById("paypal-sdk");if(i)return window.paypal?t(window.paypal):(i.addEventListener("load",()=>t(window.paypal),{once:!0}),void i.addEventListener("error",n,{once:!0}));const o=document.createElement("script");o.id="paypal-sdk",o.src=`https://www.paypal.com/sdk/js?client-id=${e}&currency=EUR&components=buttons,messages&enable-funding=paylater&commit=false&disable-funding=venmo,card,sepa`,o.onload=()=>t(window.paypal),o.onerror=n,document.head.appendChild(o)})}},an=new en;document.addEventListener("putInBasket:success",()=>{nn.init()}),document.addEventListener("AjaxLoader:loaded",()=>{rn.init(),nn.init(),on.init(),an.init()}),r(document).ready((function(){rn.init(),nn.init(),on.init(),A.init(),I.init();(new U).init();const e=new Qt;e.init(),e.refresh();(new Zt).init(),an.init(),Jt(document.getElementById("toTopButton"),{forceSmooth:!0})})),T.init(window.checkout_init);const sn=new class{constructor(){this.boundLinks=new WeakSet,this.handleClick=this.handleClick.bind(this),this.onPopState=this.onPopState.bind(this),this.onDomContentLoaded=this.onDomContentLoaded.bind(this)}init(){window.addEventListener("popstate",this.onPopState),document.addEventListener("DOMContentLoaded",this.onDomContentLoaded,{once:!0})}onDomContentLoaded(){this.bindLinks(document)}bindLinks(e){if(!e||"function"!=typeof e.querySelectorAll)return;const t=[];e.nodeType===Node.ELEMENT_NODE&&e.hasAttribute("data-loader-target")&&t.push(e),e.querySelectorAll("[data-loader-target]").forEach(e=>{t.includes(e)||t.push(e)}),t.forEach(e=>this.processLoaderElement(e))}processLoaderElement(e){const t=e.dataset?e.dataset.loaderTarget:null;t&&(e.tagName&&"a"===e.tagName.toLowerCase()?this.bindLink(e,t):e.querySelectorAll("a[href]").forEach(e=>{const n=e.dataset.loaderTarget||t;this.bindLink(e,n)}))}bindLink(e,t){t&&!this.boundLinks.has(e)&&(e.dataset.loaderTarget||(e.dataset.loaderTarget=t),e.addEventListener("click",this.handleClick),this.boundLinks.add(e))}handleClick(e){const t=e.currentTarget,n=t.getAttribute("href");if(!n)return;const i=n.toLowerCase();if(i.startsWith("#")||i.startsWith("javascript:"))return;const o=t.dataset.loaderTarget;if(!o)return;const r=document.getElementById(o);if(!r)return;let a=null;try{a=new URL(n,window.location.href)}catch(e){return}e.preventDefault(),this.loadContent(a,r),this.scrollToTarget(r)}loadContent(e,t){N.show(t);const n=e.toString();return window.history.pushState({ajaxLoader:!0,target:t.id,url:n},"",n),new Promise((e,i)=>{const o=new XMLHttpRequest;o.open("GET",n,!0),o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.onload=()=>{t.innerHTML=o.responseText,this.bindLinks(t);const i=t.querySelector('script[type="application/json"]#toast-message');if(i){const e=JSON.parse(i.innerText);A.show(e.status,e.message)}document.dispatchEvent(new CustomEvent("AjaxLoader:loaded",{detail:{target:t,url:n},bubbles:!0})),e()},o.onerror=()=>{t.textContent="Error loading content",this.scrollToTarget(t),i()},o.send()})}onPopState(){window.location.reload()}scrollToTarget(e){if(e&&"function"==typeof e.scrollIntoView)try{e.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}catch(t){e.scrollIntoView(!0)}}};sn.init(),window.ajaxLoader=sn}]);