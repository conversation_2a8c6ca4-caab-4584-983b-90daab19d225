<?php

use bqp\Exceptions\DevException;
use bqp\output\pagination;
use wws\Shop\Catalog\ShopCatalogAggChildCatsConf;
use wws\Shop\Catalog\ShopCatalogAggregation;
use wws\Shop\Catalog\ShopCatalogDeviceContext;
use wws\Shop\Catalog\ShopCatalogErsatzteilDevices;
use wws\Shop\Catalog\ShopCatalogErsatzteile;
use wws\Shop\Catalog\ShopCatalogResult;
use wws\Shop\Controller\ShopController;
use wws\Shop\Model\ShopDevice;
use wws\Shop\PaginationHelper;
use wws\Shop\Tracking\Entities\TrackerSearch;
use wws\Shop\Url\ShopUrl;


class controller_device extends ShopController
{
    private int $offset = 0;

    private ShopDevice $device;
    private ShopUrl $shop_url;
    private ShopCatalogErsatzteile $catalog;
    private ShopCatalogErsatzteilDevices $device_catalog;

    public function inject(ShopCatalogErsatzteile $catalog, ShopCatalogErsatzteilDevices $device_catalog): void
    {
        $this->catalog = $catalog;
        $this->device_catalog = $device_catalog;
    }

    public function init(): void
    {
        $this->device = $this->device_catalog->getDevice($this->shop_request['device_id']);

        $this->shop_url = new ShopUrl();
        $this->shop_url->setBaseUrl(url($this->device->getShopUrl()));

        if (isset($_REQUEST['offset'])) {
            $this->offset = (int)$_REQUEST['offset'];
        }
    }

    public function view_device(): void
    {
        $this->tracker->setPageType('device');

        $_SESSION['order_device'] = array_unique(array_merge($_SESSION['order_device'] ?? [], [$this->device->getDeviceId()]));

        $show_filter = false;

        if ($this->device['model_name']) {
            $title = $this->device['model_name'];
            if ($this->device['model_code']) {
                $title .= ' (' . $this->device['model_code'] . ')';
            }
        } else {
            $title = $this->device['model_code'];
        }

        $title = $this->device['brand'] . ' ' . $title;

        //$this->breadcrumbs->add('Ersatzteile', '/ersatzteile/geraete/');
        $this->breadcrumbs->add('Ersatzteile', '/ersatzteile/');
        $this->breadcrumbs->add('Ersatzteile für ' . $title, '#');

        $this->tpl->setBotControll($this->device['seo_index'], true);

        $this->tpl->assign('headline', 'Ersatzteile für ' . $title);
        $this->tpl->setTitle('Ersatzteile für ' . $title);
        $this->tpl->assign('device', $this->device);

        $meta_description = $this->device->getMetaDescription();
        if ($meta_description) {
            $this->tpl->setMetaDescription($meta_description);
        }

        $term = $_REQUEST['term'] ?? '';

        $this->tpl->assign('selected_cat_ids_raw', $this->request->cat_ids ?? '');

        $cat_ids = [];
        if ($this->request->cat_ids) {
            $cat_ids = explode(',', $this->request->cat_ids);

            foreach ($cat_ids as $key => $cat_id) {
                if (!is_numeric($cat_id)) {
                    unset($cat_ids[$key]);
                    continue;
                }
                $cat_ids[$key] = (int)$cat_ids[$key];
            }
        }
        $products = $this->getProducts($term, $cat_ids);

        if ($term !== '' || $cat_ids) {
            $show_filter = true;
        }

        if ($products->getTotal() > 5) {
            $show_filter = true;
        }

        $pagination = new pagination();
        $pagination->setEntries($products->getTotal());
        $pagination->setOffset($this->catalog->getOffset());
        $pagination->setOffsetModus(pagination::OFFSET_MODUS_ENTRIES);
        $pagination->setEntriesPerPage($this->catalog->getLimit());
        $pagination->setMaxPages(5);
        $pagination->setShowTotalEntries(true);
        $pagination->setClassName('pagination');
        $pagination->setActiveAsSpan(true);
        $pagination->setShowEndButtons(false);
        $pagination->setAttribute('data-loader-target', 'product-list');
        $pagination->setRendererUrl(function ($offset) {
            return $this->shop_url->build(['offset' => $offset]);
        });

        $this->tpl->setPagination($pagination);

        $this->tpl->assign('search_notice', false);
        if ($show_filter && $products->getTotal() === 0) {
            $this->tpl->assign('search_notice', true);
        }

        if ($this->catalog->getOffset() > 0) {
            $this->tpl->setBotControll(false, true);
        }

        $this->tpl->setCanonicalUrl($this->shop_url);

        $childCatsAgg = $products->getAggregation('child_cats_unique');
        $childCats = $childCatsAgg ? iterator_to_array($childCatsAgg) : [];

        $canonical_url = $this->shop_url->build(['cat_ids' => null, 'term' => $term ?: null, 'offset' => null]);
        $featureCategory = $this->buildCategoryFiltersPayload($childCats, $cat_ids, $term, $canonical_url);

        $this->tpl->assign('filters_json', json_encode([$featureCategory], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
        $this->tpl->assign('canonical_url', $canonical_url);

        $this->tpl->assign('url', $this->shop_url);
        $this->tpl->assign('offset', $this->offset + $this->catalog->getLimit());
        $this->tpl->assign('show_filter', $show_filter);
        $this->tpl->assign('products', $products);

        if ($this->request->isAjax()) {
            $this->tpl->display('components/listing/listing-product-list-inner.tpl');
            return;
        }

        $this->tpl->display('pages/device/device.tpl');
    }


    private function getProducts(string $term = '', array $cat_ids = []): ShopCatalogResult
    {
        $this->catalog->setOrder(1);

        $device_context = new ShopCatalogDeviceContext($this->device);
        $this->catalog->setDeviceContext($device_context, true);

        //kategorie aggreagation
        $cat_agg_config = new ShopCatalogAggChildCatsConf();
        $cat_agg_config->setLevel(4); //bei allego sind die teil derzeit auf ebene 4
        $cat_agg_config->setLimit(100);

        $this->catalog->setAggChildCats($cat_agg_config);

        $child_cats_unique = null;

        //wenn ein filter gesetzt ist, muss die kategorie aggregation seperat abgefragt werden
        if ($term || $cat_ids) { //getrennt
            $this->catalog->setLimit(0);
            $result = $this->catalog->execute();

            $child_cats_unique = $this->aggregateChildCats($result);
        }

        if ($cat_ids) {
            $this->catalog->setCatIds($cat_ids);
        }

        $limit = PaginationHelper::calculateLimit($this->user_session_settings->getEntriesPerPage());
        $this->catalog->setLimit($limit);

        $this->catalog->setOffset($this->offset);

        if ($term) {
            //$this->catalog->setProductNameSearchPhrase($term);
            $this->catalog->setGeneralSearchPhrase($term);
            $this->tpl->assign('term', $term);
            $this->shop_url->addParameter('term', $term);

            $search = new TrackerSearch();
            $search->setSearchTerm($term);
            $search->setSearchType('device');
            $search->setSearchContext($this->device->getDeviceId());

            $this->tracker->addSearch($search);
        }

        $products = $this->catalog->execute();

        if (!$child_cats_unique) {
            $child_cats_unique = $this->aggregateChildCats($products);
        }
        $products->addAggregation($child_cats_unique);

        return $products;
    }


    /**
     * Die Ersatzteile haben mehrfach zurodnungen, teilweise in Kategorien mit den gleichen Namen. das wird hier entsprechend gemergt.
     *
     * @param ShopCatalogResult $products
     */
    private function aggregateChildCats(ShopCatalogResult $products): ShopCatalogAggregation
    {
        if (!$products->isAggregation('child_cats')) {
            throw new DevException('child_cats agg is missing');
        }

        $cat_agg = $products->getAggregation('child_cats');

        $cats_new = [];
        $cats_new_counts = [];
        foreach ($cat_agg as $cat) {
            if (!isset($cats_new[$cat['cat_name_webshop']])) {
                $cats_new[$cat['cat_name_webshop']] = [];
                $cats_new_counts[$cat['cat_name_webshop']] = 0;
            }

            $cats_new[$cat['cat_name_webshop']][] = $cat['cat_id'];
            $cats_new_counts[$cat['cat_name_webshop']] += $cat['count'];
        }

        //sortieren nach name...
        ksort($cats_new);

        $agg = new ShopCatalogAggregation('child_cats_unique');
        $agg->add(null, 'alle Ersatzteile', $products->getTotal());
        foreach ($cats_new as $cat_name => $cat_ids) {
            $agg->add(implode(',', $cat_ids), $cat_name, $cats_new_counts[$cat_name]);
        }

        return $agg;
    }

    /**
     * Build category filter payload for ListingFilter JSON schema
     */
    private function buildCategoryFiltersPayload(array $childCats, array $selectedCatIds, string $term = '', string $resetUrl = ''): array
    {
        $values = [];

        // Add "Show all parts" as first option for compact mode
        $values[] = [
            'feature_value' => '',
            'feature_value_format' => 'Alle Ersatzteile anzeigen',
            'selected' => empty($selectedCatIds),
            'url_add' => $resetUrl,
            'url_remove' => $resetUrl
        ];

        foreach ($childCats as $cat) {
            if (empty($cat['id'])) {
                continue;
            }

            $catIdsArray = array_map('intval', explode(',', $cat['id']));
            $isSelected = !empty(array_intersect($catIdsArray, $selectedCatIds));

            $addSet = array_unique(array_merge($selectedCatIds, $catIdsArray));
            sort($addSet);

            $values[] = [
                'feature_value' => $cat['id'],
                'feature_value_format' => ($cat['name'] ?? 'Kategorie') . ' (' . (int)($cat['count'] ?? 0) . ')',
                'selected' => $isSelected,
                'url_add' => $this->shop_url->build([
                    'cat_ids' => implode(',', $addSet),
                    'offset' => null,
                    'term' => $term ?: null
                ]),
                'url_remove' => $this->shop_url->build([
                    'cat_ids' => implode(',', array_diff($selectedCatIds, $catIdsArray)) ?: null,
                    'offset' => null,
                    'term' => $term ?: null
                ])
            ];
        }

        return [
            'feature_id' => 'cat_ids',
            'feature_name' => 'Kategorien',
            'type_frontend' => 'enum',
            'selected_count' => count($selectedCatIds),
            'values' => $values
        ];
    }
}
