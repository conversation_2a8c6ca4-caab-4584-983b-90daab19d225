<?php

use bqp\Date\DateObj;
use bqp\db\ExtendedInsertQuery;
use bqp\Exceptions\ConnectionException;
use bqp\Exceptions\FatalException;
use bqp\extern\Paypal\PaypalExceptionFraud;
use bqp\extern\Paypal\PaypalExceptionRedirectCustomer;
use bqp\extern\trustedshops\trusted_shops;
use League\Flysystem\FileNotFoundException;
use wws\Customer\Customer;
use wws\Mails\Mail;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Shop\Basket\BasketConverter;
use wws\Shop\Basket\BasketCouponException;
use wws\Shop\Basket\BasketService;
use wws\Shop\Checkout\CheckoutValidator;
use wws\Shop\Controller\ShopController;
use wws\Shop\Newsletter;
use wws\Shop\Payment\ShopPaymentServiceFactory;

include(__DIR__ . '/controller_ext_order_commerzfinanz.php');
include(__DIR__ . '/controller_ext_order_billpay.php');
include(__DIR__ . '/controller_ext_order_paypal.php');

class controller_order extends ShopController
{
    use controller_ext_order_commerzfinanz;
    use controller_ext_order_billpay;
    use controller_ext_order_paypal;

    protected $request_id;

    /** @var BasketService */
    protected $warenkorb_service;

    /** @var array */
    protected $error = [];

    /** @var CheckoutValidator */
    private $checkout_validator;

    /**
     * @var ShopPaymentServiceFactory
     */
    protected $shop_payment_service_factory;

    public function initPreExecution(): bool
    {
        $this->tpl->assignByRef('error', $this->error);
        $this->tpl->assign('show_psm_ads', false);

        $this->tpl->assign('info_message', '');
        $this->tpl->assign('zahlungsextra', '');
        $this->tpl->assign('trusted_shops', '');

        //legacy zeug
        $this->tpl->assign('readonly', false);
        $this->tpl->assign('email_block', false);
        $this->tpl->assign('billpay_secure', '');
        $this->tpl->assign('button_aktiv', '');
        //


        $this->request_id = rand(1000, 9999);

        $this->logDebug();

        if ($this->warenkorb->isEmpty() && $this->warenkorb->getLastOrderId() && !in_array(
                $this->view_to_call,
                ['commerz_finanz', 'commerz_finanz_frame', 'commerz_cancel']
            )) {
            $this->logDebug('leerer Warenkorb, aber bereits bestellt', 'History Navigation?');
            $this->view_success();

            return false;
        }

        if (strpos(
                $_SERVER['HTTP_USER_AGENT'],
                'AdsBot-Google'
            ) !== false) { //der google adsbot sprint paypal kunden hinterher... weg damit
            return false;
        }

        $this->tracker->setPageType('checkout');

        return true;
    }

    public function inject(ShopPaymentServiceFactory $shop_payment_service_factory): void
    {
        $this->shop_payment_service_factory = $shop_payment_service_factory;
    }

    public function init()
    {
        if (!$this->warenkorb->isVersandValid()) {
            //Nur möglich wenn der Bestellprozess nicht sauber durchgegangen wird (manipulation, mehrere Fenster, abgelaufene session)
            //Die Fehlermeldung ist nicht wirklich akkurat.
            $this->error['message'] = 'Wir bieten derzeit keinen Versand in dieses Land an. Bitte wählen Sie eine abweichende Lieferadresse aus.';
            $this->logErrors();
            $this->setViewToCall('step1');
        }
    }

    public function logDebug($message = null, $pre_info = '')
    {
        $protokoll = system_protokoll::getInstance('shop_checkout');

        $status = system_protokoll::DEBUG;

        $ip = $_SERVER["REMOTE_ADDR"];

        $info = 'IP: ' . $ip . "\n";
        $info .= 'Request Id: ' . $this->request_id . "\n";
        $info .= 'View: ' . $this->view_to_call . "\n";
        $info .= 'Action: ' . $this->action_to_call . "\n";
        $info .= 'Session ID: ' . session_id() . "\n";
        $info .= 'Debug ID: ' . $this->warenkorb->getDebugId() . "\n";

        $details = '';
        $details .= 'User-Agent: ' . $_SERVER['HTTP_USER_AGENT'] . "\n";
        $details .= 'Referer: ' . ($_SERVER['HTTP_REFERER'] ?? '') . "\n";

        if ($message === null) {
            $pre_info = 'Request';

            //$this->warenkorb->get
            $details .= "Session\n";
            $details .= debug::getAsText($_SESSION);
            $details .= "\n\n";
            $details .= "GET\n";
            $details .= debug::getAsText($_GET);
            $details .= "\n\n";
            $details .= "POST\n";
            $details .= debug::getAsText($_POST);
            $details .= "\n\n";
            $details .= "Shop Request\n";
            $details .= debug::getAsText($this->shop_request);
        } elseif ($message === 'errors') {
            $pre_info = 'Errors';

            $details .= debug::getAsText($this->error);

            $status = system_protokoll::WARNING;
        } elseif ($message instanceof Exception) {
            if (!$pre_info) {
                $pre_info = 'Exception';
            }

            $details .= $message->getMessage() . "\n";
            $details .= debug::getExceptionAsText($message);

            $status = system_protokoll::ERROR;
        } else {
            $details .= $message;
        }

        if ($pre_info) {
            $pre_info .= "\n";
        }

        $protokoll->wlog($status, $pre_info . $info, $details);
    }

    public function setWarenkorbService(BasketService $warenkorb_service)
    {
        $this->warenkorb_service = $warenkorb_service;
    }

    public function setCheckoutValidator(CheckoutValidator $checkout_validator): void
    {
        $this->checkout_validator = $checkout_validator;
    }

    public function view_success()
    {
        $this->tracker->setPageType('checkout_success');

        $this->tpl->assign('toast_status', 'success');
        $this->tpl->assign('toast_message', 'Die Bestellung war erfolgreich!');
        $this->tpl->display('pages/checkout/order_step_5.tpl');
    }

    public function view_default()
    {
        if ($this->warenkorb->isEmpty()) {
            return $this->view_warenkorb_empty();
        }

        if (!$this->warenkorb->getCustomerId()) {
            return $this->callView('step0');
        }

        $this->callView('step1');
    }

    public function view_step0()
    {
        $this->tracker->setPageType('checkout_step0');

        if ($this->warenkorb->isEmpty()) {
            return $this->view_warenkorb_empty();
        }

        $this->tpl->displayOrder('pages/checkout/order_step_0.tpl');
    }

    public function view_step1()
    {
        $this->tracker->setPageType('checkout_step1');

        if ($this->warenkorb->isEmpty()) {
            return $this->view_warenkorb_empty();
        }

        if ($this->profile->isModule('billpay')) {
            $billpay = $this->shop_payment_service_factory->getBillpay();
            $html = $billpay->getSecurityHtmlCode();

            $this->tpl->assign('billpay_secure', $html);
        }

        $contains_extra_services = $this->warenkorb_service->containsExtraServices();
        $this->tpl->assign('shipping_method_pickup_available', $this->profile->isShippingMethodPickupAvailable() && !$contains_extra_services);

        $this->tpl->assign('shipping_method_dhl_packing_station_available', $this->profile->isShippingMethodDhlPackingStationAvailable());
        $this->tpl->assign('shipping_method_dhl_post_office_available', $this->profile->isShippingMethodDhlPostOfficeAvailable());

        $this->tpl->assign('countries', $this->profile->getCountryNamesForInvoice());

        $eu_block_country_ids = array_keys($this->profile->getCountryNamesEuBlock());
        $this->tpl->assign('eu_block_country_ids', $eu_block_country_ids);
        $this->tpl->assign('eu_block_country_ids_json', json_encode($eu_block_country_ids));

        $this->tpl->assign('button_aktiv', 'kundendaten');
        $this->tpl->displayOrder('pages/checkout/order_step_1.tpl');
    }

    public function view_warenkorb_empty()
    {
        $this->tracker->setPageType('checkout_empty');

        $this->error['message'] = 'Ihr Warenkorb ist leer.';

        $this->logErrors();

        $this->tpl->displayOrder('pages/checkout/order_empty.tpl');
    }

    public function logErrors()
    {
        if ($this->error) {
            $this->logDebug('errors', 'Errors');
        }
    }

    private function clearDeliveryAddress(): void
    {
        $this->warenkorb->setLaAnrede('');
        $this->warenkorb->setLaFirma('');
        $this->warenkorb->setLaVorname('');
        $this->warenkorb->setLaName('');
        $this->warenkorb->setLaAdresse1('');
        $this->warenkorb->setLaPlz('');
        $this->warenkorb->setLaOrt('');
        $this->warenkorb->setLaTelefon('');
        $this->warenkorb->setLaCountryId('');
    }

    public function action_login()
    {
        if (!$this->warenkorb->customerLogin($_POST['login_email'], $_POST['login_password'])) {
            $this->error['login'] = 'Die E-Mail Adresse oder das Passwort sind falsch. Bitte überprüfen Sie Ihre Eingabe und versuchen Sie es erneut.';
        }

        $this->tpl->assign('login_email', $_POST['login_email']);

        $this->logErrors();

        $origin = $_POST['origin'] ?? '';
        if (!empty($this->error['login']) && $origin === 'step0') {
            return $this->callView('step0');
        }

        $this->callView('step1');
    }

    public function action_step1()
    {
        if (!$this->warenkorb->getShopReferenz()) {
            $this->warenkorb->createShopReferenz();
        }

        $this->warenkorb->setArt($_POST['art'] ?? 'private');
        //$this->warenkorb->setAnrede($_POST['anrede'] ?? '');
        $this->warenkorb->setFirma($_POST['firma'] ?? '');

        if ($this->warenkorb->getArt() === 'private') {
            $this->warenkorb->setFirma('');
        }

        $this->warenkorb->setName($_POST['family-name']);
        $this->warenkorb->setVorname($_POST['given-name']);
        $this->warenkorb->setAdresse1($_POST['address1']);
        $this->warenkorb->setPlz($_POST['postal-code']);
        $this->warenkorb->setOrt($_POST['ort']);
        $this->warenkorb->setCountryId($_POST['country_id']);

        $this->warenkorb->setEmail($_POST['email']);
        $this->warenkorb->setTelefon($_POST['tel']);
        $this->warenkorb->setFax($_POST['fax'] ?? '');

        $this->warenkorb->setNewsletter(isset($_POST['newsletter']));

        $new_lieferoption = $_POST['extralieferadresse'] ?? 'no';
        $old_lieferoption = $this->warenkorb->getLieferoption();

        if ($old_lieferoption !== $new_lieferoption) {
            $this->clearDeliveryAddress();
        }

        $this->warenkorb->setLieferoption($new_lieferoption);

        $this->checkout_validator->validateCustomerContact($this->warenkorb);
        $this->checkout_validator->validateValueGuarantee($this->warenkorb);
        $this->error = $this->checkout_validator->getViolationsAndFlush();

        if (!$this->error) {
            $this->checkout_validator->validateServicesForShippingAddress($this->warenkorb);
            $impossible_services = $this->checkout_validator->getViolationsAndFlush();
            if ($impossible_services) {
                $this->error['message'] = $this->getImpossibleServicesErrorMessage($impossible_services);
            }
        }

        if (!$this->error) {
            $this->warenkorb_service->eventWarenkorbModified();

            if (!$this->warenkorb->isVersandValid()) {
                $this->error['message'] = 'Wir bieten derzeit keinen Versand in dieses Land an. Bitte wählen Sie eine abweichende Lieferadresse aus.';
                return $this->callView('step2');
            }

            if (in_array($this->warenkorb->getLieferoption(), ['no', 'abholen'])) {
                return $this->callView('step3');
            }

            if ($this->warenkorb->getLieferoption() === 'dhlpackstation') {
                return $this->callView('dhlpackstation');
            }

            if ($this->warenkorb->getLieferoption() === 'dhlpostfiliale') {
                return $this->callView('dhlpostfiliale');
            }

            return $this->callView('step2');
        }

        $this->logErrors();

        $this->callView('step1');
    }

    public function action_remove_guarantee(): void
    {
        $this->warenkorb->removeWertgarantie();
        $this->callView();
    }

    public function view_dhlpackstation()
    {
        $this->tracker->setPageType('checkout_step2_dhl');

        $this->tpl->assign('countries', $this->profile->getCountryNamesForDelivery());

        $this->tpl->assign('button_aktiv', 'kundendaten');

        $this->tpl->displayOrder('pages/checkout/order_step_dhlpackstation.tpl');
    }

    public function action_dhlpackstation(): void
    {
        $this->warenkorb->setLaAnrede($_POST['anrede'] ?? '');
        $this->warenkorb->setLaFirma($_POST['address2'] ?? '');
        $this->warenkorb->setLaName($_POST['family-name'] ?? '');
        $this->warenkorb->setLaVorname($_POST['given-name'] ?? '');

        $address1 = $_POST['address1'] ?? '';
        if (!empty($address1) && stripos($address1, 'Packstation') === false) {
            $address1 = 'Packstation ' . $address1;
        }

        $this->warenkorb->setLaAdresse1($address1);
        $this->warenkorb->setLaPlz($_POST['postal-code'] ?? '');
        $this->warenkorb->setLaOrt($_POST['ort'] ?? '');
        $this->warenkorb->setLaTelefon($_POST['tel'] ?? '');
        $this->warenkorb->setLaCountryId($_POST['country_id'] ?? '');

        $this->checkout_validator->validateShippingContact($this->warenkorb);

        if (!empty($address1)) {
            $this->checkout_validator->validateDhlPackingStationAddress($this->warenkorb);
        }

        $this->error = $this->checkout_validator->getViolationsAndFlush();

        if (isset($this->error['str'])) {
            $this->error['str'] = 'Bitte geben Sie die Packstationsnummer an.';
        }
        if (empty($this->warenkorb->getLaFirma())) {
            $this->error['firma'] = 'Bitte geben Sie Ihre Postnummer an.';
        }

        if (!$this->error) {
            $this->checkout_validator->validateServicesForShippingAddress($this->warenkorb);
            $impossible_services = $this->checkout_validator->getViolationsAndFlush();
            if ($impossible_services) {
                $this->error['message'] = $this->getImpossibleServicesErrorMessage($impossible_services);
            }
        }

        if ($this->error) {
            $this->logErrors();
            $this->callView('dhlpackstation');
            return;
        }

        $this->warenkorb_service->eventWarenkorbModified();
        $this->callView('step3');
    }

    public function view_dhlpostfiliale()
    {
        $this->tracker->setPageType('checkout_step2_dhl');

        $this->tpl->assign('countries', $this->profile->getCountryNamesForDelivery());

        $this->tpl->assign('button_aktiv', 'kundendaten');

        $this->tpl->displayOrder('pages/checkout/order_step_dhlpostfiliale.tpl');
    }

    public function action_dhlpostfiliale(): void
    {
        $this->warenkorb->setLaAnrede($_POST['anrede'] ?? '');
        $this->warenkorb->setLaFirma($_POST['address2'] ?? '');
        $this->warenkorb->setLaName($_POST['family-name'] ?? '');
        $this->warenkorb->setLaVorname($_POST['given-name'] ?? '');

        $address1 = $_POST['address1'] ?? '';
        if (!empty($address1) && stripos($address1, 'Postfiliale') === false) {
            $address1 = 'Postfiliale ' . $address1;
        }

        $this->warenkorb->setLaAdresse1($address1);
        $this->warenkorb->setLaPlz($_POST['postal-code'] ?? '');
        $this->warenkorb->setLaOrt($_POST['ort'] ?? '');
        $this->warenkorb->setLaTelefon($_POST['tel'] ?? '');
        $this->warenkorb->setLaCountryId($_POST['country_id'] ?? '');

        $this->checkout_validator->validateShippingContact($this->warenkorb);

        if (!empty($address1)) {
            $this->checkout_validator->validateDhlPackingStationAddress($this->warenkorb);
        }

        $this->error = $this->checkout_validator->getViolationsAndFlush();

        if (isset($this->error['str'])) {
            $this->error['str'] = 'Bitte geben Sie die Filialnummer an.';
        }
        if (empty($this->warenkorb->getLaFirma())) {
            $this->error['firma'] = 'Bitte geben Sie Ihre Postnummer an.';
        }

        if (!$this->error) {
            $this->checkout_validator->validateServicesForShippingAddress($this->warenkorb);
            $impossible_services = $this->checkout_validator->getViolationsAndFlush();
            if ($impossible_services) {
                $this->error['message'] = $this->getImpossibleServicesErrorMessage($impossible_services);
            }
        }

        if ($this->error) {
            $this->logErrors();
            $this->callView('dhlpostfiliale');
            return;
        }

        $this->warenkorb_service->eventWarenkorbModified();
        $this->callView('step3');
    }

    public function view_step2(): void
    {
        $this->tracker->setPageType('checkout_step2');

        $this->tpl->assign('countries', $this->profile->getCountryNamesForDelivery());

        $this->tpl->assign('button_aktiv', 'kundendaten');

        $this->tpl->displayOrder('pages/checkout/order_step_liefer.tpl');
    }

    public function action_step2()
    {
        $this->warenkorb->setLaAnrede($_POST['anrede'] ?? '');
        $this->warenkorb->setLaFirma($_POST['firma'] ?? '');
        $this->warenkorb->setLaVorname($_POST['given-name'] ?? '');
        $this->warenkorb->setLaName($_POST['family-name'] ?? '');
        $this->warenkorb->setLaAdresse1($_POST['address1'] ?? '');
        $this->warenkorb->setLaPlz($_POST['postal-code'] ?? '');
        $this->warenkorb->setLaOrt($_POST['ort'] ?? '');
        $this->warenkorb->setLaTelefon($_POST['tel'] ?? '');
        $this->warenkorb->setLaCountryId($_POST['country_id'] ?? '');
        $this->warenkorb->setLieferoption('yes');

        $this->checkout_validator->validateShippingContact($this->warenkorb);
        $this->error = $this->checkout_validator->getViolationsAndFlush();

        if (!$this->error) {
            $this->checkout_validator->validateServicesForShippingAddress($this->warenkorb);
            $impossible_services = $this->checkout_validator->getViolationsAndFlush();
            if ($impossible_services) {
                $this->error['message'] = $this->getImpossibleServicesErrorMessage($impossible_services);
            }
        }

        if ($this->error) {
            $this->logErrors();
            $this->callView('step2');
            return;
        }

        $this->warenkorb_service->eventWarenkorbModified();
        $this->callView('step3');
    }

    public function view_step3()
    {
        $this->tracker->setPageType('checkout_step3');

        if ($this->profile->isModule('billpay')) {
            if ($this->warenkorb->isFirma()) {
                if (!$this->warenkorb->getBillpayFirma()) {
                    $this->warenkorb->setBillpayFirma($this->warenkorb->getFirma());
                }
            }

            $billpay = $this->shop_payment_service_factory->getBillpay();
            $this->tpl->assign('legal_forms', $billpay->getLegalForms($this->warenkorb->getCountryId()));
        }

        $this->tpl->assign('versandart', 'nor');
        $this->tpl->assign('button_aktiv', 'zahlart');

        $expressable = $this->warenkorb_service->isWarenkorbExpressable();

        $this->tpl->assign('express', $expressable);

        $shipping = $this->warenkorb_service->getShippingForCheckout();

        $shipment_group_prices = $shipping->getShipmentGroupPrices();

        if (!$expressable) {
            unset($shipment_group_prices['exp']);
        }

        $contains_extra_services = $this->warenkorb_service->containsExtraServices();
        if ($contains_extra_services) {
            unset($shipment_group_prices['abh']);
        }

        //wenn die aktuell gewählte versandgruppe nicht möglich ist, die erst mögliche nehmen
        if (!isset($shipment_group_prices[$this->warenkorb->getVersandKundeOpt()])) {
            $shipment_group_id = key($shipment_group_prices);
            $this->warenkorb->setVersandKundeOpt($shipment_group_id);
        }

        $this->tpl->assign('shipment_group_prices', $shipment_group_prices);

        $this->tpl->assign('zahlungsarten_preis', $shipping->getPaymentPrices());

        $this->tpl->assign('versandart', $this->warenkorb->getVersandart());
        $this->tpl->assign('selected_zahlungsart', $this->warenkorb->getZahlungsart());

        $zahlungsarten = $this->warenkorb_service->getZahlungsarten();

        $this->tpl->assign('zahlungsarten', $zahlungsarten);

        $this->tpl->displayOrder('pages/checkout/order_step_3.tpl');
    }

    public function action_set3()
    {
        if (isset($_POST['zahlungsart'])) {
            $this->warenkorb_service->setZahlungsart((int)$_POST['zahlungsart']);
        }

        if (isset($_POST['versandart'])) {
            $this->warenkorb_service->setVersandKundeOpt($_POST['versandart']);
        }

        $this->logErrors();

        $this->callView('step3');
    }

    public function action_step3()
    {
        if (!$_POST['zahlungsart']) {
            $this->error['message'] = 'Bitte wählen Sie eine Zahlungsart aus.';
        }

        if (!$this->warenkorb_service->isShipmentGroupIdValid($this->warenkorb->getVersandKundeOpt())) {
            $this->error['message'] = 'Bitte wählen Sie eine Versandart aus.';
        }

        if (
            $this->warenkorb->getZahlungsart() === OrderConst::PAYMENT_BILLPAY_RECHUNG ||
            $this->warenkorb->getZahlungsart() === OrderConst::PAYMENT_BILLPAY_LASTSCHRIFT
        ) {
            $this->step3_billpay_check();
        }

        if (count($this->error) == 0) {
            $this->warenkorb_service->setZahlungsart($_POST['zahlungsart']);

            switch ($this->warenkorb->getZahlungsart()) {
                case OrderConst::PAYMENT_PAYPAL:
                    $this->warenkorb->createShopReferenz();

                    try {
                        $paypal = $this->shop_payment_service_factory->getPaypal();

                        $result = $paypal->startCheckout($this->warenkorb);

                        $redirect_url = $result->getRedirectUrl();

                        $this->warenkorb->backup('PAYPAL_' . $_SERVER['REMOTE_ADDR'] . '_' . $paypal->getLastToken());

                        $this->redirect($redirect_url);
                    } catch (ConnectionException $e) {
                        $this->error['message'] = 'Leider ist eine Zahlung per Paypal zurzeit nicht möglich. Bitte versuchen Sie es später noch einmal oder wählen Sie eine andere Zahlungsart.';
                    } catch (Exception $e) {
                        $this->error['message'] = 'Leider ist eine Zahlung per Paypal zurzeit nicht möglich. Bitte versuchen Sie es später noch einmal oder wählen Sie eine andere Zahlungsart.';
                    }

                    break;
                case OrderConst::PAYMENT_BILLPAY_RECHUNG:
                case OrderConst::PAYMENT_BILLPAY_LASTSCHRIFT:
                    $billpay = $this->shop_payment_service_factory->getBillpay();

                    $request = $billpay->getPreauthorizeRequestPerWarenkorb($this->warenkorb);
                    $request->setIp($_SERVER['REMOTE_ADDR']);

                    $response = $billpay->sendPreauthorizeRequest($request);

                    if ($response->isError()) {
                        $this->error['message'] = $response->getCustomerMessage();

                        $this->logDebug($response->getMerchantMessage(), 'Billpay');
                    } else {
                        if ($response->isApproved()) {
                            $this->warenkorb->setPaymentReferenz($response->getBptid());
                        } else {
                            $this->error['message'] = $response->getCustomerMessage() . '&nbsp;';
                            $this->warenkorb->setZahlungsart(OrderConst::PAYMENT_VORKASSE);
                            //$warenkorb->disableZahlungsart(OrderConst::PAYMENT_BILLPAY_RECHUNG);
                        }
                    }
                    break;
            }
        }

        $this->logErrors();

        if ($this->error) {
            $this->tpl->assign('user_daten', input::filter($_POST, 'html'));
            return $this->callView('step3');
        }

        $this->callView('step4');
    }

    public function redirect($url)
    {
        header('Location: ' . $url);
        exit;
    }

    public function action_gutschein()
    {
        $this->warenkorb_service->setZahlungsart($_POST['zahlungsart']);

        try {
            $this->warenkorb_service->addCoupon($_POST['gutschein_code']);
        } catch (BasketCouponException $e) {
            $this->error['gutschein_code'] = true;
            $this->error['gutschein_code_msg'] = $e->getMessage();

            $this->logErrors();
            $this->logDebug($e, 'Gutschein');
        }

        $this->callView();
    }

    public function action_paypal()
    {
        try {
            $paypal = $this->shop_payment_service_factory->getPaypal();
            $result = $paypal->getCheckoutDetails($_GET['token']);

            $is_restore = false;

            if ($this->warenkorb->isEmpty()) {
                if ($this->warenkorb->restore('PAYPAL_' . $_SERVER['REMOTE_ADDR'] . '_' . $_GET['token'])) {
                    $this->logDebug('warenkorb restore');
                } else {
                    $this->logDebug('warenkorb restore fail');
                }
                $this->warenkorb = &$_SESSION['warenkorb'];

                $is_restore = true;
            }

            $_SESSION['paypal_token'] = $result->getId();
            $_SESSION['paypal_payer_id'] = $result->getPayerId();
            $_SESSION['paypal_payerstatus'] = $result->getPayerStatus();

            if ($is_restore) {
                header('Location: /kasse/step4/');
                exit;
            } else {
                $this->callView('step4');
            }
        } catch (Exception $e) {
            $this->logDebug($e, 'Paypal Error');
            $this->callView('step3');
        }
    }

    public function view_step4()
    {
        $this->tracker->setPageType('checkout_step4');

        $this->tpl->assign('button_aktiv', 'abschluss');

        $this->tpl->displayOrder('pages/checkout/order_step_4.tpl');
    }

    public function action_step4()
    {
        if (!$this->error) {
            $this->checkout_validator->validateServicesForShippingAddress($this->warenkorb);
            $impossible_services = $this->checkout_validator->getViolationsAndFlush();
            if ($impossible_services) {
                $this->error['message'] = $this->getImpossibleServicesErrorMessage($impossible_services);
            }
        }

        $contains_extra_services = $this->warenkorb_service->containsExtraServices();
        if ($contains_extra_services && $this->warenkorb->getVersandart() == 'abh') {
            $this->error['message'] = 'Die von Ihnen gewählten Zusatzleistungen sind mit der Abholung nicht kombinierbar. Bitte ändern Sie Ihre Auswahl.';
        }

        //if($_POST['agbs'] != 'yes') $error['agb'] = true;

        $this->warenkorb->setKundenBemerkung($_POST['anmerkungen'] ?? '');


        if (!$this->warenkorb->getWarenkorbItems()) {
            throw new FatalException('warenkorb is empty in step4 and no last order_id');
        }

        if (!$this->warenkorb_service->checkCoupons()) {
            $this->error['coupon'] = 'Ein Gutschein ist nicht gültig und wurde aus Ihrem Warenkorb entfernt.';
            $this->warenkorb_service->removeCoupons();
        }

        $this->logErrors();

        if (count($this->error) > 0) {
            return $this->callView('step4');
        }

        if ($this->warenkorb->getZahlungsart() == OrderConst::PAYMENT_PAYPAL) {
            try {
                $this->paypalFraudCheck();

                $paypal = $this->shop_payment_service_factory->getPaypal();
                $paypal_checkout_result = $paypal->preDoCheckout(
                    $this->warenkorb,
                    $_SESSION['paypal_token'],
                    $_SESSION['paypal_payer_id']
                );
            } catch (PaypalExceptionRedirectCustomer $e) {
                $url = $e->getRedirectUrl();
                $this->redirect($url);
            } catch (PaypalExceptionFraud $e) {
                $this->logDebug($e, 'Paypal Fraud');
                $this->error['message'] = 'Ihre Zahlung wurde von Paypal abgelehnt. Bitte versuchen Sie es später noch einmal oder wählen Sie eine andere Zahlungsart. ';
                return $this->callView('step3');
            } catch (Exception $e) {
                $this->logDebug($e, 'Paypal: doExpressCheckout fehlgeschlagen');
            }
        }

        $this->logDebug('Create - Start', 'Create');
        $customer_id = $this->warenkorb->getCustomerId();

        //neu kunde
        if (!$customer_id) {
            $customer = new Customer();

            $customer->setShopId($this->warenkorb->getShopId());

            $customer->setAnrede($this->warenkorb->getAnrede());
            $customer->setFirma($this->warenkorb->getFirma());
            $customer->setName($this->warenkorb->getName());
            $customer->setVorname($this->warenkorb->getVorname());
            $customer->setAdresse1($this->warenkorb->getAdresse1());
            $customer->setPlz($this->warenkorb->getPlz());
            $customer->setOrt($this->warenkorb->getOrt());
            $customer->setCountryId($this->warenkorb->getCountryId());
            $customer->setTelefon($this->warenkorb->getTelefon() ?: '-');

            $customer->setMobil($this->warenkorb->getTelefon2());
            $customer->setFax($this->warenkorb->getFax());
            $customer->setEmail($this->warenkorb->getEmail());

            $customer->setStdZahlungsart($this->warenkorb->getZahlungsart());
            $customer_id = $customer->save();

            $this->warenkorb->setCustomerId($customer->getCustomerId());
            $this->warenkorb->setCustomerNr($customer->getCustomerNr());
        } else {
            $customer = new Customer($customer_id);

            $customer->setAnrede($this->warenkorb->getAnrede());
            $customer->setFirma($this->warenkorb->getFirma());
            $customer->setName($this->warenkorb->getName());
            $customer->setVorname($this->warenkorb->getVorname());
            $customer->setAdresse1($this->warenkorb->getAdresse1());
            $customer->setPlz($this->warenkorb->getPlz());
            $customer->setOrt($this->warenkorb->getOrt());
            $customer->setCountryId($this->warenkorb->getCountryId());
            $customer->setTelefon($this->warenkorb->getTelefon() ?: '-');
            $customer->setEmail($this->warenkorb->getEmail());

            $customer->save();
        }

        $this->db->query(
            "
			UPDATE
		 		customers
		 	SET
		 		customers.anrede2 = '" . $this->db->escape($this->warenkorb->getLaAnrede()) . "',
		 		customers.name2 = '" . $this->db->escape($this->warenkorb->getLaName()) . "',
		 		customers.vorname2 = '" . $this->db->escape($this->warenkorb->getLaVorname()) . "',
		 		customers.strasse2 = '" . $this->db->escape($this->warenkorb->getLaAdresse1()) . "',
		 		customers.plz2 = '" . $this->db->escape($this->warenkorb->getLaPlz()) . "',
		 		customers.ort2 = '" . $this->db->escape($this->warenkorb->getLaOrt()) . "',
		 		customers.land2 = '" . $this->db->escape($this->warenkorb->getLaCountryId()) . "',
		 		customers.firma2 = '" . $this->db->escape($this->warenkorb->getLaFirma()) . "',
		 		customers.telefon2 = '" . $this->db->escape($this->warenkorb->getLaTelefon()) . "'
		 	WHERE
		 		customers.customer_id = '" . (int)$customer_id . "'
		"
        );

        $converter = new BasketConverter();
        $order = $converter->convertToOrder($this->warenkorb);

        $order->setCustomerId($customer_id);
        $order->setZahlungsart($this->warenkorb->getZahlungsart());
        $order->setKundenBemerkung($this->warenkorb->getKundenBemerkung());

        $versand_kunde_opt = $this->warenkorb->getVersandKundeOpt();
        if ($versand_kunde_opt === 'eu_block') {
            $versand_kunde_opt = 'abh';
        }
        $order->setVersandKundeOpt($versand_kunde_opt);
        $order->setPaymentReferenz($this->warenkorb->getPaymentReferenz());

        if ($this->warenkorb->isAbweichendeLieferadresse()) {
            $address = $this->warenkorb->getAbweichendeLieferAddress();

            $order->setAddress($address, Order::ADDRESS_TYPE_LIEFER);
        }

        $order->autoDetermineSped(); //spedition bestimmen
        $order->recalcVersand();

        $order_id = $order->save();

        $this->warenkorb->setOrderId($order_id);

        $this->warenkorb_service->redeemCoupons();

        $this->logDebug('Create - Created', 'Create');

        $auftrags_summe = $order->getRechnungsBetrag();

        $this->tpl->assign('button_aktiv', '');

        $zahlungsextra = '';
        $payment_url = '';
        $email_zahlungsart_hinweis = '';
        $redirect_url = '';

        switch ($this->warenkorb->getZahlungsart()) {
            case OrderConst::PAYMENT_KREDITKARTE:
                $concardis = $this->shop_payment_service_factory->getConcardis();
                $request = $concardis->createNewRequest();
                $request->loadByOrder($order);
                $request->addAmount($auftrags_summe);

                $payment_url = $request->createLink();

                $zahlungsextra .= '<script type="text/javascript">window.open(\'' . $payment_url . '\',\'payment\',\'width=600,height=600\')</script>';

                $shortener = service_loader::urlShortener($order->getShopId());
                $short_url = $shortener->short($payment_url, 4);

                $zahlungsextra .= 'Sie haben sich für die Zahlung per Kreditkarte entschieden. Es sollte sich jetzt ein Fenster geöffnet haben, in dem Sie ihre Kreditkartendaten eingeben können. Falls nicht klicken Sie bitte <a style="border-bottom: 1px solid black;" href="' . $payment_url . '" target="zpop" onclick="openpop(600,600,\'yes\')">HIER</a>.<br /><br />';

                $order->setPaymentUrl($payment_url);
                $order->save();

                $email_zahlungsart_hinweis = 'Sie haben sich für die Zahlung per Kreditkarte entschieden. Wurde der Bezahlvorgang abgebrochen oder konnten Sie Ihre Kreditkartendaten nicht vollständig angeben so können Sie dies unter folgendem Link nachholen: ' . "\n\n" . $short_url . "\n\n";

                break;
            case OrderConst::PAYMENT_ECOM_UNZER:
                $unzer = $this->shop_payment_service_factory->getUnzer();
                $unzer->setReturnUrl($this->createViewUrl('success'));
                $payment_url = $unzer->createPaymentLinkByOrder($order);

                //$zahlungsextra .= '<script type="text/javascript">window.open(\'' . $payment_url . '\',\'payment\',\'width=600,height=600\')</script>';

                $shortener = service_loader::urlShortener($order->getShopId());
                $short_url = $shortener->short($payment_url, 1);

                $zahlungsextra .= 'Sie haben sich für die Zahlung per Kreditkarte entschieden. Es sollte sich jetzt ein Fenster geöffnet haben, in dem Sie ihre Kreditkartendaten eingeben können. Falls nicht klicken Sie bitte <a style="border-bottom: 1px solid black;" href="' . $payment_url . '" target="zpop" onclick="openpop(600,600,\'yes\')">HIER</a>.<br /><br />';

                $order->setPaymentUrl($payment_url);
                $order->save();

                $email_zahlungsart_hinweis = 'Sie haben sich für die Zahlung per Kreditkarte entschieden. Wurde der Bezahlvorgang abgebrochen oder konnten Sie Ihre Kreditkartendaten nicht vollständig angeben so können Sie dies unter folgendem Link nachholen: ' . "\n\n" . $short_url . "\n\n";

                $redirect_url = $payment_url;

                break;
            case OrderConst::PAYMENT_NACHNAHME:
                $email_zahlungsart_hinweis = 'Sie zahlen in Bar direkt an den Zusteller bzw. Spediteur. Beachten Sie bitte das die Deutsche Post/DHL bei Paketsendungen ein zusätzliches Nachnahmeentgelt in Höhe von 2,00 Euro erhebt.';
                break;
            case OrderConst::PAYMENT_VORKASSE:
                if ($order->isOrderVersandPrio()) { //wenn ware verfügbar, vorkassenfrist setzen
                    $order->refreshVorkassenFrist();
                    $order->setBestaedigungsMailDate(new DateObj());
                    $order->save();

                    $email_zahlungsart_hinweis = '<!--include_mail:block_vorkasse/-->';
                } else {
                    $email_zahlungsart_hinweis = 'Die Bankverbindung für Ihre Überweisung erhalten Sie nach der Bearbeitung Ihres Auftrages durch einen unserer Mitarbeiter zusammen mit Ihrer Auftragsbestätigung als separate Email.';
                }

                break;
            case OrderConst::PAYMENT_PAYPAL:
                try {
                    if ($paypal_checkout_result) {
                        $paypal->postDoCheckout($paypal_checkout_result, $this->warenkorb);
                    }
                } catch (Exception $e) {
                    $this->logDebug('paypal postDoCheckout Exception: ' . $e->getMessage());
                }
                break;
            case OrderConst::PAYMENT_SOFORTUEBERWEISUNG:
                $sofort = $this->shop_payment_service_factory->getSofortueberweisung();
                $request = $sofort->createRequest();
                $request->loadByOrder($order);

                $payment_url = $request->createLink();
                //$zahlungsextra .= 'Sie werden weitergeleitet... <a href="'.$payment_url.'">weiter</a>';
                //$zahlungsextra .= '<script type="text/javascript">document.location.href = \''.$payment_url.'\';</script>';

                $redirect_url = $payment_url;
                break;
            case OrderConst::PAYMENT_FINANZIERUNG_COMMERZ:
                /*$finanzierung = $this->shop_payment_service_factory->getFinanzierungCommerz();

                $request = $finanzierung->createRequest();
                $request->loadByOrder($order);

                $redirect_url = $request->createLink();*/
                $redirect_url = $this->createViewUrl('commerz_finanz');

                break;
            case OrderConst::PAYMENT_BILLPAY_RECHUNG:
                $billpay = $this->shop_payment_service_factory->getBillpay();

                $response = $billpay->capture($order);

                if (!$response->isError()) {
                    $email_zahlungsart_hinweis = "Bitte verwenden Sie für Ihre Zahlung ausschließlich nachfolgende Kontodaten:\n";

                    $email_zahlungsart_hinweis .= "Verwendungszweck: " . $response->getInvoiceReference() . "\n";
                    $email_zahlungsart_hinweis .= "Inhaber: " . $response->getAccountHolder() . "\n";
                    $email_zahlungsart_hinweis .= "IBAN: " . $response->getAccountNumber() . "\n";
                    $email_zahlungsart_hinweis .= "BIC: " . $response->getBankCode() . "\n";
                    $email_zahlungsart_hinweis .= "Bank: " . $response->getBankName() . "\n\n";

                    $email_zahlungsart_hinweis .= "Diese Daten finden Sie später auch auf Ihrer Rechnung, welche Sie zum Versandzeitpunkt per Email erhalten.";
                }

                break;
            case OrderConst::PAYMENT_BILLPAY_LASTSCHRIFT:
                $billpay = $this->shop_payment_service_factory->getBillpay();

                $billpay->capture($order);

                break;
        }

        $this->logDebug('Create - Payment', 'Create');

        $this->tpl->assign('zahlungsextra', $zahlungsextra);

        //tracking
        $this->tracker->addOrder($order);

        if ($_SESSION['order_device'] ?? []) {
            $query = new ExtendedInsertQuery($this->db, 'order_device', ['order_id', 'device_id']);
            foreach ($_SESSION['order_device'] as $device_id) {
                $query->insert(['order_id' => $order_id, 'device_id' => $device_id]);
            }
            $query->execute();
            $_SESSION['order_device'] = [];
        }

        //daten an trusted shops übermitteln falls nötig
        if ($this->warenkorb->hasTrustedshops()) {
            trusted_shops::addToExport($order_id);
        }

        $this->logDebug('Create - Tracking', 'Create');

        /**
         * E-Mail mit den AGBs müssen an den Kunden gesendet werden
         */
        $mail = new Mail();
        $mail->setOrder($order);
        try {
            $mail->loadVorlage('shop_zugang');
            $mail->assign('zahlungsart_hinweis', $email_zahlungsart_hinweis);
            $mail->trySend();
        } catch (FileNotFoundException $file_not_found_exception) {
            // @todo log error
        } catch (Exception $exception) {
            // @todo log error
        }

        $_SESSION['finanzierungs_antrag'] = '';

        $this->logDebug('Create - Mail', 'Create');

        if ($this->warenkorb->getNewsletter()) {
            Newsletter::subscribe(
                $this->warenkorb->getAnrede(),
                $this->warenkorb->getVorname(),
                $this->warenkorb->getName(),
                $this->warenkorb->getEmail(),
                true,
                $this->warenkorb->getShopId()
            );
        }

        $this->warenkorb_service->clear();

        $this->warenkorb->setLastOrderId($order_id);

        $this->tpl->assign('button_aktiv', '');


        if ($redirect_url) {
            return $this->redirect($redirect_url);
        }

        $this->tpl->assign('toast_status', 'success');
        $this->tpl->assign('toast_message', 'Die Bestellung war erfolgreich!');
        $this->tpl->display('pages/checkout/order_step_5.tpl');
    }

    public function action_remove_all_services()
    {
        $this->warenkorb_service->removeAllServices();

        $this->callView();
    }

    /**
     * @param array $impossible_services
     * @return string
     */
    private function getImpossibleServicesErrorMessage(array $impossible_services): string
    {
        if (empty($impossible_services)) {
            return '';
        }

        $error_message = sprintf(
            'Leider sind einige Zusatzleistungen an der von Ihnen gewählten Lieferadresse nicht verfügbar:
                        <div class="text_content"><ul><li>%s</li></ul></div>',
            implode('</li><li>', $impossible_services)
        );

        $error_message .= 'Um mit der Bestellung fortzufahren entfernen Sie bitte die aufgeführten Zusatzleistungen aus Ihrem Warenkorb.<br>';
        $error_message .= '<a href="' . $this->createViewUrl() . '&do=remove_all_services" class="high">Zusatzleistungen jetzt aus Warenkorb entfernen.</a>';

        return $error_message;
    }

    /**
     * Irgendwer platziert bei uns Bestellungen mit geklauten Daten.
     * Warum, wieso, weshalb ist komplett unklar.
     *
     * Auffällig bei den aktuellen Bestellungen: bei der Telefonnummer fehlt immer die führende 0 / Paypal Gastbestellung / Warenkorb um die 50€ / weiß klein, haushalt / express checkout
     * (vorhergehende welle bis um die 140€, normaler checkout)
     *
     * Sollte er das mit der Telefonnummer durchschauen, dann kann hier ggf. noch über die IP gegangen werden. Das erwischt dann nicht alle, aber reduziert das stark.
     * (eventuell limitieren wir auch die 'unverified'... das sind i.d.R. nur 2, 3 pro Tag)
     *
     * @return void
     * @throws PaypalExceptionFraud
     */
    private function paypalFraudCheck(): void
    {
        if ($_SESSION['paypal_payerstatus'] !== 'unverified') {
            return;
        }

        //prüfen ob die Telefonnummer mit 1-9 beginnt -> das ist sehr eindeutig und dürfte in kombination wahrscheinlich 100% ergeben.
        if (!preg_match('~^[1-9]~', $this->warenkorb->getTelefon())) {
            return;
        }

        //warenorb unter 150
        if ($this->warenkorb->getGesamtPreis() > 150) {
            return;
        }

        throw new PaypalExceptionFraud();
    }
}
