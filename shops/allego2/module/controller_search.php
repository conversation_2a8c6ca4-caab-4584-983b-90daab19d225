<?php

use bqp\output\pagination;
use wws\Shop\Catalog\ShopCatalog;
use wws\Shop\Catalog\ShopCatalogAggChildCatsConf;
use wws\Shop\Controller\ShopController;
use wws\Shop\PaginationHelper;
use wws\Shop\Tracking\Entities\TrackerSearch;

class controller_search extends ShopController
{
    protected ShopCatalog $catalog;

    public function setCatalog(ShopCatalog $catalog)
    {
        $this->catalog = $catalog;
    }

    public function getCatalog(): ShopCatalog
    {
        return $this->catalog;
    }

    public function view_default()
    {
        $this->breadcrumbs->add('Suche', 'suche/');

        $this->tpl->setBotControll(false, false);

        $this->tpl->assign('exp', $_REQUEST['exp']);
        $this->tpl->assign('exp_capitalized', ucfirst(mb_strtolower($_REQUEST['exp'])));
        $this->tpl->assign('exp_uppercase', mb_strtoupper($_REQUEST['exp']));

        $this->tpl->assign('url_base', '/suche/');

        $this->tpl->assign('min_preis', '');
        $this->tpl->assign('max_preis', '');
        $this->tpl->assign('error_search', '');

        if ($_REQUEST['exp']) {
            $searchexp = trim($_REQUEST['exp']);
            $cat_id = isset($_REQUEST['cat_id']) ? (int)$_REQUEST['cat_id'] : 0;

            //such protokoll
            $search = new TrackerSearch();
            $search->setSearchTerm($_REQUEST['exp']);

            $this->tracker->addSearch($search);

            $superkeywords = [
                'agb' => '/agb.html',
                'batterieentsorgung' => '/umweltschutz.html',
                'batterie' => '/umweltschutz.html',
                'entsorgung' => '/umweltschutz.html',

                'bestellung' => '/service-center/bestellvorgang.html',
                'bestellvorgang' => '/service-center/bestellvorgang.html',

                'verfügbarkeiten' => '/service-center/lieferbaranzeige.html',
                'lieferbarkeit' => '/service-center/lieferbaranzeige.html',
                'lieferbarkeitsanzeige' => '/service-center/lieferbaranzeige.html',

                'zahlung' => '/service-center/zahlung_versand.html',
                'kreditkarte' => '/service-center/zahlung_versand.html#kreditkarte',
                'nachnahme' => '/service-center/zahlung_versand.html#nachnahme',
                'vorkasse' => '/service-center/zahlung_versand.html#vorkasse',
                'rechnung' => '/service-center/zahlung_versand.html#rechnung',
                'finanzierung' => '/service-center/zahlung_versand.html#finanzierung',
                'versand' => '/service-center/zahlung_versand.html',
                'versandkosten' => '/service-center/zahlung_versand.html',
                'versandarten' => '/service-center/zahlung_versand.html',
                'versandzusatzleistungen' => '/service-center/zahlung_versand.html',

                'garantieverlängerung' => '/service-center/garantieverlaengerung.html',
                'garantiebedingungen' => '/service-center/garantieverlaengerung.html',

                'widerruf' => '/agb.html#widerruf',
                'rücksendung' => '/agb.html#widerruf',
                'reklamationsformular' => '/reklamation.html',
                'reklamation' => '/reklamation.html',
                'kontakt' => '/kontakt.html',
                'sicherheit' => '/datenschutz.html',
                'datenschutz' => '/datenschutz.html',
                'impressum' => '/impressum.html',
                'newsletter' => '/newsletter.html'
            ];

            if (isset($superkeywords[mb_strtolower($searchexp)])) {
                header("Location: " . $superkeywords[mb_strtolower($searchexp)]);
                exit();
            }

            $catalog = $this->getCatalog();
            $catalog->setFeatureGroupId(0);
            $catalog->setProductNameSearchPhrase($_REQUEST['exp']);
            $catalog->setShowInvisible(true);

            $catalog->setOrder($this->user_session_settings->getEntriesOrder());

            $catalog->setOffset($this->getOffset());

            $limit = PaginationHelper::calculateLimit($this->user_session_settings->getEntriesPerPage());
            $catalog->setLimit($limit);

            if ($cat_id) {
                $catalog->setCatId($cat_id);
            }

            $config = new ShopCatalogAggChildCatsConf();
            $catalog->setAggChildCats($config);

            if ($this->user_session_settings->isPriceMin()) {
                $catalog->setPriceMin($this->user_session_settings->getPriceMin());
                $this->tpl->assign('min_preis', (float)$this->user_session_settings->getPriceMin());
            }
            if ($this->user_session_settings->isPriceMax()) {
                $catalog->setPriceMax($this->user_session_settings->getPriceMax());
                $this->tpl->assign('max_preis', (float)$this->user_session_settings->getPriceMax());
            }

            $catalog_result = $catalog->execute();

            $cats = $catalog_result->getAggregation('child_cats');

            $url = '/suche/?exp=' . urlencode($_REQUEST['exp']);

            $this->tpl->assign('url_base', $url);

            $this->groupCatsAndAssign($cats, $cat_id, $url);

            if ($cat_id) {
                $url .= '&cat_id=' . $cat_id;
            }

            $this->tpl->assign('anzahl_produkte', $catalog_result->getTotal());
            $this->tpl->assign('cat_id', $cat_id);

            $this->tpl->assign('url', $url);

            $i = 0;
            foreach ($catalog_result as $daten) {
                $this->tracker->addProductImpression($daten, $catalog_result->getOffset() + $i++, 'search');
            }

            $pagination = new pagination();
            $pagination->setEntries($catalog_result->getTotal());
            $pagination->setOffset($catalog_result->getOffset());
            $pagination->setOffsetModus(pagination::OFFSET_MODUS_ENTRIES);
            $pagination->setEntriesPerPage($catalog_result->getLimit());
            $pagination->setMaxPages(5);
            $pagination->setShowTotalEntries(true);
            $pagination->setClassName('pagination');
            $pagination->setActiveAsSpan(true);
            $pagination->setShowEndButtons(false);

            $pagination->setRendererUrl(function ($offset) use ($url) {
                if (!$offset) {
                    return $url;
                }
                return $url . '&offset=' . $offset;
            });

            $this->tpl->setPagination($pagination);


            $this->tpl->assign('products', $catalog_result);
            $this->tpl->assign('result', $catalog_result);
        }
        $this->tpl->display('pages/search/search.tpl');
    }

    protected function getOffset()
    {
        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
        if ($offset < 0) {
            $offset = 0;
        }

        return $offset;
    }

    protected function groupCatsAndAssign($cats, $act_cat_id = -1, $url_base = '')
    {
        $menu_tree = $this->profile->getMenu();

        $group_count = [];
        foreach ($cats as $cat) {
            for ($i = 1; $i <= 4; $i++) {
                $id = (int)($cat['cat_id_ebene_' . $i] ?? 0);
                if (!$id) {
                    continue;
                }
                if (!isset($group_count[$id])) {
                    $group_count[$id] = 0;
                }

                $group_count[$id] += (int)$cat['count'];
            }
        }

        $path = [];
        if ($act_cat_id > 0) {
            $path = $menu_tree->getCatPathCatArray($act_cat_id);
        }

        $back_url = '';
        if (!empty($path)) {
            if (count($path) > 1) {
                $parent_id = $path[count($path) - 2]['cat_id'];
                $back_url = $parent_id > 0 ? $url_base . '&cat_id=' . $parent_id : $url_base;
            } else {
                $back_url = $url_base;
            }
        }

        $children = [];
        $all_cats = $menu_tree->getAllChildCats($act_cat_id);

        $direct_children = [];
        foreach ($all_cats as $cat) {
            $parent_cat_id = isset($cat['parent_cat_id']) ? $cat['parent_cat_id'] : 0;
            if ($parent_cat_id == $act_cat_id && isset($group_count[$cat['cat_id']]) && $group_count[$cat['cat_id']] > 0) {
                $direct_children[] = [
                    'cat_id' => $cat['cat_id'],
                    'cat_name' => $cat['cat_name'] ?? '',
                    'cat_name_webshop' => $cat['cat_name_webshop'] ?? '',
                    'anzahl' => $group_count[$cat['cat_id']],
                    'is_current' => false
                ];
            }
        }

        if (empty($direct_children) && $act_cat_id > 0) {
            if (!empty($path)) {
                $current = end($path);
                $children[] = [
                    'cat_id' => $act_cat_id,
                    'cat_name' => $current['cat_name'] ?? '',
                    'cat_name_webshop' => $current['cat_name_webshop'] ?? '',
                    'anzahl' => $group_count[$act_cat_id] ?? 0,
                    'is_current' => true
                ];
            }
        } else {
            $children = $direct_children;
        }

        $this->tpl->assign('search_nav', [
            'back_url' => $back_url,
            'children' => $children
        ]);

        $this->tpl->assign('cat_result', []);
        foreach ($children as $c) {
            $this->tpl->append('cat_result', [
                'cat_id' => $c['cat_id'],
                'cat_name' => $c['cat_name_webshop'] ?: $c['cat_name'],
                'anzahl' => $c['anzahl'],
                'is_current' => !empty($c['is_current'])
            ]);
        }
    }
}
