<?php

use bqp\output\pagination;
use wws\Shop\PaginationHelper;
use wws\Shop\ProductFeaturePresets\FeaturePreset;
use wws\Shop\ProductFeaturePresets\FeaturePresetService;
use wws\Shop\Url\ShopCatalogUrlAllego;

include_once(__DIR__ . '/controller_category_base.php');

class controller_category_standard extends controller_category_base
{

    /**
     * @var FeaturePreset
     */
    protected $feature_preset = null;

    /**
     * @var FeaturePresetService
     */
    protected $feature_preset_service;

    public function setFeaturePreset(FeaturePreset $feature_preset): void
    {
        $this->feature_preset = $feature_preset;
    }

    public function setFeaturePresetService(FeaturePresetService $feature_presets): void
    {
        $this->feature_preset_service = $feature_presets;
    }

    public function view_feature_preset()
    {
        $this->category->overloadByFilterPreset($this->feature_preset);

        $catalog = $this->getCatalog();

        //aktuelles filter preset setzen
        foreach ($this->feature_preset->getPresetValues() as $feature_id => $value) {
            switch ($feature_id) {
                case 'brand_id': //kommt immer mit brand_name
                    break;
                case 'device_brand_id':

                    //$brand_service = $this->profile->getShopDataService()->getDataProviderBrands();
                    //if($feature_id === 'brand_id') {
                    //$this->filter['brand'] = $data_provider_sparespart_brands->loadBrand($value);
                    //}
                    $catalog->addFilter($feature_id, $value);
                    break;
                default:
                    $catalog->addFilter($feature_id, $value);
            }
        }

        $this->tpl->setCanonicalUrl($this->feature_preset->getPresetUrl());
        //$this->url->setByFilter($this->getFilter());
        $this->view_default();
    }

    public function view_default()
    {
        $this->initTplDefaults();
        if ($this->feature_preset) {
            $this->breadcrumbs->addBrotkrummen_feature_preset($this->feature_preset);
        }

        //kategorie standards
        $promo = $this->funktion->getPromo($this->category->getCatId());
        $this->tpl->assign('promo', $promo);

        //
        $view_config = $this->category->getViewConfig();

        $url = new ShopCatalogUrlAllego();
        $url->setBaseUrl(url('/' . $this->category->getCatShopUrlOriginal()));

        $catalog = $this->getCatalog();

//        $conf = new \wws\Shop\Catalog\ShopCatalogAggChildCatsConf();
//        $conf->setCatId($this->category->getCatId());
//        $conf->setOnlyDirectChilds(true);
//        $catalog->setAggChildCats($conf);


        if (isset($_REQUEST['action']) && $_REQUEST['action'] == 'setFilter' && !empty($_GET['value'])) { // legacy URLs
            $catalog->addFilter($_GET['feature_id'], $_GET['value']);
        }

        if (isset($_REQUEST['feature']) && is_array($_REQUEST['feature'])) {
            foreach ($_REQUEST['feature'] as $feature_id => $value) {
                $catalog->addFilter($feature_id, $value);
            }
        }

        if (isset($view_config['data_show_invisible']) && $view_config['data_show_invisible']) {
            $catalog->setShowInvisible(true);
        }


        if ($view_config['use_goodies']) {
            $catalog->setAggGoodieIds(true);
        }

        $catalog->setOrder($this->user_session_settings->getEntriesOrder());

        $catalog->setOffset($this->getOffset());


        $columns = match ($this->category->getCatTemplate()) {
            'knot', 'standard_knot' => 4,
            default => 3
        };
        $limit = PaginationHelper::calculateLimit($this->user_session_settings->getEntriesPerPage(), $columns);
        $catalog->setLimit($limit);

        if (isset($view_config['data_load_set_previews']) && $view_config['data_load_set_previews']) {
            $catalog->setLoadSetPreview(true);
        }

        //wenn im kontext eines feature_presets muss die feature_preset_id mit in die produktdaten
        if ($this->feature_preset) {
            $catalog->setRawDataCallback(function ($daten) {
                if ($daten) {
                    $daten['feature_preset_id'] = $this->feature_preset->getFilterPresetId();
                }

                return $daten;
            });
        }

        $result = $catalog->execute();

        if ($view_config['use_goodies']) {
            // goodies: get full result set
            $goodies = $this->catalog_goodie->getTopGoodiesForCat($this->category->getCatId());

            //@todo... doppelt gemoppelt -> spezielle aggregation welche die goodies gleich selbst laden kann
            $aggregation = $result->getAggregation('goodie_ids');

            $available_goodies = [];

            foreach ($aggregation as $agg) {
                $available_goodies[$agg['id']] = true;
            }

            $available_goodies = array_keys($available_goodies);

            foreach ($goodies as $i => $goodie) {
                if (!in_array($goodie['goodie_id'], $available_goodies)) {
                    $goodies[$i]['unavailable'] = true;
                    unset($goodies[$i]); // remove to display them anyway and gray them out
                }
            }

            $this->tpl->assign('goodies', $goodies);
        } else {
            $this->tpl->assign('goodies', []);
        }


        //urls erzeugen
        $url_map = [];

        $feature_presets = $this->feature_preset_service->getFeaturePresetsAsArray($this->category->getCatId());
        foreach ($feature_presets as $feature_preset) {
            $url_map[] = ['url' => '/' . $feature_preset['feature_preset_url'], 'filters' => $feature_preset['preset_values']];
        }

        $result->buildUrl($url, $url_map); //hm... setzt die url und erweirtert/manipuliert diese für die richtigen parameter


        $filters = $result->getFilterForForm();

        $this->tpl->assign('filters_json', json_encode(array_values($filters))); //array_values() -> ansonsten wird das mit der sortierung in js schwierig

        $this->tpl->assign('products_found', $result->getTotal());

        $this->tpl->assign('result', $result);
        $this->tpl->assign('products', $result); //legacy

        if ($result->getOffset() > 0) {
            $this->tpl->setBotControll(false, true);
        }

        $pagination = new pagination();
        $pagination->setEntries($result->getTotal());
        $pagination->setOffset($result->getOffset());
        $pagination->setOffsetModus(pagination::OFFSET_MODUS_ENTRIES);
        $pagination->setEntriesPerPage($result->getLimit());
        $pagination->setMaxPages(5);
        $pagination->setShowTotalEntries(true);
        $pagination->setClassName('pagination');
        $pagination->setActiveAsSpan(true);
        $pagination->setShowEndButtons(false);

        $pagination->setAttribute('data-loader-target', 'product-list');

        $this->tpl->assign('url', $url);

        $pagination->setRendererUrl(function ($offset) use ($url) {
            return $url->build(['offset' => $offset]);
        });

        $this->tpl->setPagination($pagination);

        if ($result->isAggregation('child_cats')) {
            $this->tpl->assign('sub_cats', $result->getAggregation('child_cats'));
        }

        $position = 0;
        foreach ($result as $product) {
            $position++;
            $this->tracker->addProductImpression($product, $result->getOffset() + $position, 'category');
        }

        if ($this->request->isAjax()) {
            $this->tpl->display($view_config['template_file_product_list']);
        } else {
            $this->tpl->display($view_config['template_file']);
        }
    }

    protected function getOffset(): int
    {
        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
        if ($offset < 0) {
            $offset = 0;
        }

        return $offset;
    }
}
