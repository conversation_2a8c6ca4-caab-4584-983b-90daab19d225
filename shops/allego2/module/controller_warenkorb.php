<?php

use bqp\extern\SpbGarantGarantie\SpbGarantGarantie;
use wws\Product\ProductConst;
use wws\Product\ProductExtraServices;
use wws\Shop\Basket\BasketItem;
use wws\Shop\Basket\BasketRequestHandlerResult;
use wws\Shop\Catalog\ShopCatalog;
use wws\Shop\Controller\ShopController;
use wws\Shop\ShopProductCrosssell;
use wws\Shop\Tracking\ShopTracker;

class controller_warenkorb extends ShopController
{
    /**
     * @var BasketRequestHandlerResult
     */
    private $warenkorb_request_handler_result;

    /**
     * @var ShopCatalog
     */
    private $catalog;

    public function init()
    {
        $this->tpl->assign('button_aktiv', 'warenkorb');
        $this->breadcrumbs->add('Warenkorb', '/warenkorb/');
    }

    public function setCatalog(ShopCatalog $catalog): void
    {
        $this->catalog = $catalog;
    }

    public function setWarenkorbRequestHandlerResult(BasketRequestHandlerResult $warenkorb_request_handler_result): void
    {
        $this->warenkorb_request_handler_result = $warenkorb_request_handler_result;
    }

    public function view_default()
    {
        if (isset($_POST['modus']) && $_POST['modus'] === 'ajax') {
            $this->view_add_ajax();
        } elseif (isset($_GET['modus']) && $_GET['modus'] === 'paypal') {
            $this->paypalExpress();
        } elseif ($this->warenkorb_request_handler_result->isAdd() && !$this->warenkorb_request_handler_result->isService()) {
            $this->view_add();
        } else {
            $this->view_overview();
        }
    }

    public function view_overview()
    {
        $this->tracker->setPageType('basket');

        $this->tpl->assign('warenkorb_nav', 'Warenkorb <small>&nbsp; &nbsp; Bitte prüfen Sie den Inhalt Ihres Warenkorbs.</small>');

        $product_ids = array_map(fn(BasketItem $item) => $item->getProductId(), $this->warenkorb->getWarenkorbItemsWithoutServices());

        $loader = $this->catalog->getProductLoader();
        $products = array_map(fn(int $product_id) => $loader->getProduct($product_id), $product_ids);
        $this->tpl->assign('products', $products);

        $crosssell_utils = service_loader::get(ShopProductCrosssell::class);
        $crosssell_products = $crosssell_utils->fetchForMultipleProducts($loader, $products);
        $this->tpl->assign('crosssell_products', $crosssell_products);

        $product_extra_service_data = [];

        $extra_services = ProductExtraServices::getExtraServicesExtended($this->warenkorb->getPossibleExtraServiceIds(), true);
        foreach ($extra_services as $extra_service_id => $extra_service) {
            if (trim($extra_service['service_dependencies'])) {
                $extra_service['service_dependencies'] = explode(',', $extra_service['service_dependencies']);

                foreach ($extra_service['service_dependencies'] as $i => $dependency_id) {
                    if (!isset($extra_services[$dependency_id])) {
                        unset($extra_service['service_dependencies'][$i]);
                    }
                }
            } else {
                $extra_service['service_dependencies'] = [];
            }

            switch ($extra_service['service_handler']) {
                case 'wertgarantie':
                    foreach ($products as $product) {
                        $garantie = new SpbGarantGarantie(config::getLegacy('garantie_spb_garant'));
                        $prices = $garantie->calcPricesAll($extra_service['service_handler_extra'], $product->getVkBrutto());

                        $product_extra_service_data[$product->getProductId()][$extra_service_id]['service_vk_brutto_multi'] = $prices;
                    }
                    break;
            }

            $extra_service['service_beschreibung_points'] = implode('<br>', $extra_service['service_beschreibung_points']);

            $extra_services[$extra_service_id] = $extra_service;
        }

        $this->tpl->assign('extra_services', $extra_services);
        $this->tpl->assign('product_extra_service_data', $product_extra_service_data);

        // edge case for display of Trageservice apart from its dependencies
        $this->tpl->assign('notice_service_ids', [14, 40, 41, 55]);

        if ($this->request->isAjax()) {
            $this->tpl->display('pages/cart/cart-ajax.tpl');
        } else {
            $this->tpl->display('pages/cart/cart.tpl');
        }
    }

    public function view_add()
    {
        $warenkorb_item = $this->warenkorb_request_handler_result->getWarenkorbItem();

        $product_id = $warenkorb_item->getProductId();

        $loader = $this->catalog->getProductLoader();
        $product = $loader->getProduct($product_id);

        $product->setProductName($warenkorb_item->getProductName());

        $show_extra_services = false;

        if ($show_extra_services) {
            $extra_services = $this->funktion->loadExtraServices($product->getExtraServices(), $product->getVkBrutto());
            $this->tpl->assign('extra_services', $extra_services);
        }

        $this->tpl->assign('show_extra_services', $show_extra_services);

        $this->tpl->assign('product', $product);

        $crosssel_products = $loader->getAssociations($product, [ProductConst::ASSOCIATION_TYPE_ID_ZUBEHOER]);
        $this->tpl->assign('zubehoer', $crosssel_products);

        $this->tpl->display('pages/cart/cart_add.tpl');
    }

    public function view_add_ajax()
    {
        $warenkorb_item = $this->warenkorb_request_handler_result->getWarenkorbItem();

        if (!$warenkorb_item) {
            $this->tpl->display('pages/cart/cart_add_ajax_error.tpl');
            return;
        }

        $product_id = $warenkorb_item->getProductId();
        $loader = $this->catalog->getProductLoader();
        $product = $loader->getProduct($product_id);

        $this->tpl->assign('product', $product);
        $this->tpl->assign('warenkorb_item', $warenkorb_item);

        $html = $this->tpl->fetch('pages/cart/cart_add_ajax.tpl');

        $cart_count = 0;
        foreach ($this->warenkorb->getWarenkorbItemsWithoutServices() as $item) {
            $cart_count += $item->getAnzahl();
        }

        header('Content-Type: application/json');
        echo json_encode([
            'html' => $html,
            'count' => $cart_count,
            'tracker' => $this->tracker->render(ShopTracker::EVENT_BASKET_AJAX),
        ]);
    }

    private function paypalExpress(): void
    {
        header('Location: /kasse/?do=paypal_direct_start');
    }
}
