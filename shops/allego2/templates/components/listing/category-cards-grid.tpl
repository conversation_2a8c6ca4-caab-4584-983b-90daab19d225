{{assign var='has_sub_cats' value=false}}
{{foreach from=$kategorien item='sub_cat'}}
  {{if $sub_cat.parent_cat_id == $cat_id}}
    {{assign var='has_sub_cats' value=true}}
    {{break}}
  {{/if}}
{{/foreach}}

{{if $has_sub_cats}}
  <section class="category-cards-grid" id="category-cards-grid" aria-label="Unterkategorien">
    <div class="grid" role="list">
      {{foreach from=$kategorien item='sub_cat'}}
        {{if $sub_cat.parent_cat_id == $cat_id}}
          <a class="card" role="listitem" href="{{url url=$sub_cat.cat_shop_url}}">
            {{if $sub_cat.cat_picture}}
              <img class="image" src="{{$_cfg.res_host}}cat_logos/{{$sub_cat.cat_picture}}" alt="{{$sub_cat.cat_name_webshop}}" width="50" height="50" loading="lazy" decoding="async" />
            {{else}}
              <img class="image" src="{{$_cfg.res_host}}cat_logos/nopic.jpg" alt="{{$sub_cat.cat_name_webshop}}" width="50" height="50" loading="lazy" decoding="async" />
            {{/if}}
            <span class="name">{{$sub_cat.cat_name_webshop|default:$sub_cat.cat_name}}</span>
          </a>
        {{/if}}
      {{/foreach}}
    </div>
  </section>
{{/if}}