{{block name='category_goodies'}}
  {{if $goodies|default:[]|@count}}
    {{include file='components/goodie/goodie-list.tpl' filter=true layout='list' show_more_button=false css_class='-no-mb' goodies=$goodies}}
  {{/if}}
{{/block}}

<section class="top-products -expandable {{if isset($type)}}-{{$type}}{{/if}}" id="top-products" aria-label="Produkte aus dieser Kategorie">
    {{if isset($filters_json)}}<script id="filters-data" type="application/json">{{$filters_json nofilter}}</script>{{/if}}

    {{foreach from=$products item='product' name='productLoop'}}
      <div class="product-wrapper{{*if $smarty.foreach.productLoop.iteration > 20}} -hidden{{/if*}}">
        {{include file='components/product/product-card.tpl' product=$product}}
      </div>
    {{/foreach}}
    {{*              {{if isset($products_count) && $products_count > 20}}*}}
    {{*                <button class="action show-all-products" type="button" aria-controls="top-products" aria-expanded="false">*}}
    {{*                  <span class="text">Alle Produkte anzeigen</span>*}}
    {{*                </button>*}}
    {{*              {{/if}}*}}
</section>
{{$navigation nofilter}}
