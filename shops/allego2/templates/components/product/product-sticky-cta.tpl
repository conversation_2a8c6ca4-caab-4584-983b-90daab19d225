{{if !$product->getNotOrderable()}}
<aside class="product-sticky-cta" role="region" aria-label="Schnellzugriff Warenkorb">
  <div class="price">
    <span class="amount">{{if $product.vk_brutto_is_ab}}<span>ab</span>{{/if}}{{make_price price=$product.vk_brutto}}</span>
  </div>
  
  <form method="post" action="/warenkorb/" class="actions std_form put_in_basket">
    <input type="hidden" name="add_item_extended" value="1">
    <input type="hidden" name="product_id" value="{{$product.product_id}}">
    <input type="hidden" name="menge" value="{{if $current_cart_quantity > 0}}{{$current_cart_quantity}}{{else}}1{{/if}}" id="service_quantity" data-cart-qty="{{$current_cart_quantity}}">

    {{foreach from=$extra_services item=$extra_service}}
        <input type="hidden" class="sticky-service" name="service[{{$extra_service.service_id}}]" value="0">
    {{/foreach}}

    <div class="qty" role="group" aria-label="Menge auswählen">
      <button class="button" type="button" aria-label="Menge verringern">-</button>
      <input class="input" type="number" name="menge" value="1" min="1" aria-label="Anzahl" />
      <button class="button" type="button" aria-label="Menge erhöhen">+</button>
    </div>
    
    <button class="action hover-underline" type="submit">
      <img class="logo" src="{{$_cfg.grafik_url}}icons/cart-white.svg" alt="" aria-hidden="true" decoding="async" width="24" height="24" />
    </button>
  </form>
</aside>
{{/if}}
