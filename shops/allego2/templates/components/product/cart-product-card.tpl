<article class="cart-product-card">
    <figure class="media">
        <a class="hero" href="{{url url=$product.product_shop_url}}">
            <img
                class="photo"
                src="{{picture_url product_media_id=$product.product_media_id size='nor' product_shop_url=$product.product_shop_url}}"
                alt="{{$product->getProductName()}}"
                width="300"
                height="300"
                loading="lazy"
                decoding="async" />
        </a>
    </figure>

    <div class="meta" data-loader-target="ajax-cart">
        <form class="actions std_form put_in_basket" action="{{url url='/warenkorb/'}}" method="GET">
            <input type="hidden" name="add_item" value="{{$product.product_id}}" />

            <button class="action hover-underline" type="submit">
                <img class="logo" src="{{$_cfg.grafik_url}}icons/cart-white.svg" alt="" aria-hidden="true" decoding="async" width="24" height="24" />
            </button>
        </form>

        <div class="delivery-info">
            {{lieferbaranzeige_compact id=$product.lieferbaranzeige express=$product.express}}
        </div>

        <div class="price">
            <span class="amount">{{make_price_star price=$product.vk_brutto}}</span>
        </div>
    </div>

    <div class="title">
        <a class="title-link" href="{{url url=$product.product_shop_url}}">{{$product.product_name}}</a>
    </div>

    <div class="details">
        <section class="info" aria-label="Produktinformationen">
            {{if $product.mpn || $product.ean}}
            <div class="codes">
                {{if $product.mpn}}Art.-Nr.: {{$product.mpn}}<br />{{/if}}
                {{if $product.ean}}EAN: {{$product.ean}}{{/if}}
            </div>
            {{/if}}
        </section>
    </div>

    <div class="view">
        <a href="{{url url=$product.product_shop_url}}" class="button-view">*Produkt ansehen</a>
    </div>
</article>