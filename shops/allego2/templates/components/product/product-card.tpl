<article class="product-card">
  <figure class="media">
    <a class="hero" href="{{url url=$product.product_shop_url}}">
      <img
        class="photo"
        src="{{picture_url product_media_id=$product.product_media_id size='nor' product_shop_url=$product.product_shop_url}}"
        alt="{{$product->getProductName()}}"
        loading="lazy"
        decoding="async"
      />
    </a>
  </figure>

  <div class="meta">
    <div class="brand-slot">
      {{if $product.brand_logo}}
        <img class="brand"
             src="{{$_cfg.res_host}}hersteller_logos/{{$product.brand_logo}}"
             alt="{{$product.brand_name}}"
             height="24"
             loading="lazy"
             decoding="async" />
      {{/if}}
    </div>

    {{if $product.average_product_rating > 0}}
      <div class="stars" role="img" aria-label="{{$product.average_product_rating}} von 5 Sternen Bewertung">
          {{section name=s loop=5}}
          {{if $product.average_product_rating >= $smarty.section.s.iteration}}
          <img class="star -filled" src="{{$_cfg.grafik_url}}icons/star-full.svg" alt="" aria-hidden="true" decoding="async" width="16" height="16" />
          {{elseif $product.average_product_rating >= ($smarty.section.s.iteration - 0.5)}}
          <img class="star -half" src="{{$_cfg.grafik_url}}icons/star-half.svg" alt="" aria-hidden="true" decoding="async" width="16" height="16" />
          {{else}}
          <img class="star -empty" src="{{$_cfg.grafik_url}}icons/star-empty.svg" alt="" aria-hidden="true" decoding="async" width="16" height="16" />
          {{/if}}
          {{/section}}
      </div>
    {{/if}}

      <div class="delivery-info">
          {{lieferbaranzeige_compact id=$product.lieferbaranzeige express=$product.express}}
      </div>

      {{if $product.extra_services|@count}}
          <div class="extra-services">
            <img src="{{$_cfg.grafik_url}}hand_package.png">
              <span class="text">optionale Serviceleistungen buchbar</span>
          </div>
      {{/if}}
  </div>

  {{if isset($product.eek_label_url) && $product.eek_label_url || isset($product.eek_fiche_url) && $product.eek_fiche_url || isset($product.eek_label_url) && $product.eek_label_url}}
      <div class="pdb">
        {{* Separate links: label icon opens label.webp, text opens fiche (fallback to existing behavior) *}}
        {{if isset($product.eek_label_url) && $product.eek_label_url}}
          <a class="label" href="{{$product.eek_label_url}}" target="_blank" rel="noopener" aria-label="Energielabel {{$product.eek}}">
            {{include file="components/energy-label-icon.tpl" product=$product}}
          </a>
        {{/if}}

        {{if isset($product.eek_fiche_url) && $product.eek_fiche_url}}
          <a class="doc" href="{{$product.eek_fiche_url}}" target="_blank" rel="noopener">
            <span class="text">Produktdatenblatt</span>
          </a>
        {{elseif isset($product.eek_label_url) && $product.eek_label_url}}
          <a class="doc" href="{{$product.eek_label_url}}" target="_blank" rel="noopener">
            <span class="text">Produktdatenblatt</span>
          </a>
        {{/if}}
      </div>
    {{/if}}

    {{if isset($product.service_highlights) && $product.service_highlights|@count || $product.express}}
        <div class="badges">
            {{foreach from=$product.service_highlights item='highlight'}}
                <div class="badge">{{$highlight}}</div>
            {{/foreach}}
            {{if $product.express}}
                <div class="badge">Expressversand möglich</div>
            {{/if}}
        </div>
    {{/if}}

  <div class="title">
    <a class="title-link" href="{{url url=$product.product_shop_url}}">{{$product.product_name}}</a>
  </div>

  <div class="details">
    <div class="codes">
        {{if $product.product_nr}}Art.-Nr.: {{$product.product_nr}}<br />{{/if}}
        {{if $product.ean}}EAN: {{$product.ean}}{{/if}}
    </div>

    <div class="price">
      <span class="amount">{{make_price price=$product.vk_brutto}}</span>
      <p class="note">
          inkl. MwSt.
          {{if isset($product.min_versand)}}
          <span>+ Versand ab {{make_price price=$product.min_versand}}</span>
          {{/if}}
      </p>
    </div>
  </div>

    {{if !$product->getNotOrderable()}}
      <form class="actions std_form put_in_basket" action="{{url url='/warenkorb/'}}" method="GET">
        <input type="hidden" name="add_item" value="{{$product.product_id}}" />

        <div class="qty" role="group" aria-label="Menge auswählen">
          <button class="button" type="button" aria-label="Menge verringern">-</button>
          <input class="input" type="number" name="menge" value="1" min="1" aria-label="Anzahl" />
          <button class="button" type="button" aria-label="Menge erhöhen">+</button>
        </div>

        <button class="action hover-underline" type="submit">
          <img class="logo" src="{{$_cfg.grafik_url}}icons/cart-white.svg" alt="" aria-hidden="true" decoding="async" width="24" height="24" />
        </button>
      </form>
    {{else}}
    <div class="actions std_form put_in_basket"></div>
    {{/if}}
</article>
