{{if isset($product)}}
<article class="product-box">
  <figure class="media{{if count($product_pictures) > 1}} has-thumbs{{/if}}">
    <a class="hero" id="produkt_bild_link"
       href="{{picture_url product_media_id=$product.product_media_id size='highres' product_shop_url=$product.product_shop_url}}">
      <img
              class="photo" id="produkt_bild"
              src="{{picture_url product_media_id=$product.product_media_id size='nor' product_shop_url=$product.product_shop_url}}"
              alt="{{$product.product_name}}"
              width="300" height="300"
              loading="lazy" decoding="async" />
    </a>

    {{if count($product_pictures) > 1}}
      <nav class="thumbs" aria-label="Weitere Bilder">
        {{foreach item=bild name=pb from=$product_pictures}}
          {{if $bild.media_type === 'video'}}
            <a class="thumb {{if $smarty.foreach.pb.iteration > 5}}thumb-hidden{{/if}}" href="{{$bild.media_data.url}}"
               title="{{picture_alt product_name=$product.product_name product_media_id=$bild.product_media_id}}"
               aria-current="{{if $smarty.foreach.pb.iteration == 1}}true{{else}}false{{/if}}">
          {{else}}
            <a class="thumb {{if $smarty.foreach.pb.iteration > 5}}thumb-hidden{{/if}}"
               href="{{picture_url product_media_id=$bild.product_media_id size='highres' product_shop_url=$product.product_shop_url}}"
               title="{{picture_alt product_name=$product.product_name product_media_id=$bild.product_media_id}}"
               aria-current="{{if $smarty.foreach.pb.iteration == 1}}true{{else}}false{{/if}}">
          {{/if}}
              <img
                  src="{{picture_url product_media_id=$bild.product_media_id size='50x50' product_shop_url=$product.product_shop_url}}"
                  alt="{{picture_alt product_name=$product.product_name product_media_id=$bild.product_media_id}}"
                  width="50" height="50" loading="lazy" decoding="async" />
          </a>
        {{/foreach}}
      </nav>
    {{/if}}

    {{if count($product_pictures) > 1}}
      <ol class="dots" aria-label="Bilder">
        {{section name=i loop=$product_pictures|@count}}
          <li class="dot" aria-current="{{if $smarty.section.i.index == 0}}true{{else}}false{{/if}}"></li>
        {{/section}}
      </ol>
    {{/if}}
  </figure>

  <div class="details">
    <header class="head">
      <div class="bar">
        <div class="badges">
          {{if isset($product.service_highlights) && $product.service_highlights|@count}}
            {{foreach from=$product.service_highlights item='highlight'}}
              {{if $highlight}}
                <div class="badge"><span class="text-bold">{{$highlight}}</span></div>
              {{/if}}
            {{/foreach}}
          {{/if}}
          {{if $product.express}}
            <div class="badge"><span class="text">Expressversand möglich</span></div>
          {{/if}}
        </div>

        {{* Energy label icon (preserve old id for legacy JS) *}}
        {{if $product.eek_label_url}}
          <a class="label" id="eek_label"
             href="{{$product.eek_label_url}}" target="_blank" rel="noopener"
             aria-label="Energielabel {{$product.eek}}">
            {{include file="components/energy-label-icon.tpl" product=$product}}
          </a>
        {{/if}}

        {{* Text links on the right: GPSR + Produktdatenblatt *}}
        <div class="links">
          <a class="gpsr" href="#"
             data-gpsr="/gpsr/{{$product.product_shop_url}}" role="button" onclick="event.preventDefault();">
            <span class="text">GPSR</span>
          </a>

          {{if $product.eek_fiche_url}}
            <a class="doc" href="{{$product.eek_fiche_url}}" target="_blank" rel="noopener">
              <span class="text">Produktdatenblatt</span>
            </a>
          {{elseif $product.eek_label_url}}
            <a class="doc" href="{{$product.eek_label_url}}" target="_blank" rel="noopener">
              <span class="text">Produktdatenblatt</span>
            </a>
          {{/if}}
        </div>
      </div>

      <p class="title heading-2">
        <span class="title-link">{{$product.product_name}}</span>
      </p>
    </header>

    <section class="info" aria-label="Produktinformationen">
      <div class="brand-slot">
        {{if $product.brand_logo}}
          <img class="brand"
               src="{{$_cfg.res_host}}hersteller_logos/{{$product.brand_logo}}"
               alt="{{$product.brand_name}}"
               height="24"
               loading="lazy"
               decoding="async" />
        {{/if}}
      </div>

      <div class="meta">
        {{if $product.average_product_rating > 0}}
          <div class="stars" role="img" aria-label="{{$product.average_product_rating}} von 5 Sternen Bewertung">
            {{section name=s loop=5}}
              {{if $product.average_product_rating >= $smarty.section.s.iteration}}
                <img class="star -filled" src="{{$_cfg.grafik_url}}icons/star-full.svg" alt="" aria-hidden="true" decoding="async" width="16" height="16" />
              {{elseif $product.average_product_rating >= ($smarty.section.s.iteration - 0.5)}}
                <img class="star -half" src="{{$_cfg.grafik_url}}icons/star-half.svg" alt="" aria-hidden="true" decoding="async" width="16" height="16" />
              {{else}}
                <img class="star -empty" src="{{$_cfg.grafik_url}}icons/star-empty.svg" alt="" aria-hidden="true" decoding="async" width="16" height="16" />
              {{/if}}
            {{/section}}
          </div>
        {{/if}}

        {{if $product.product_nr || $product.ean}}
          <div class="codes">
            {{if $product.product_nr}}Art.-Nr.: {{$product.product_nr}}<br />{{/if}}
            {{if $product.ean}}EAN: {{$product.ean}}{{/if}}
          </div>
        {{/if}}
      </div>

      <div class="price">
        {{if $product.uvp}}
          <div class="uvp">statt <s>{{make_price price=$product.uvp}}</s> jetzt nur</div>
        {{/if}}
        <span class="amount">{{if $product.vk_brutto_is_ab}}<span>ab</span>{{/if}}{{make_price price=$product.vk_brutto}}</span>
        {{if $product.grundpreis}}
          <div class="grundpreis">{{$product.grundpreis}}</div>
        {{/if}}
        <p class="note">
          inkl. MwSt.
          {{if isset($product.min_versand)}}
            <a href="#versandkosten" class="finanzierung-link note-jump" data-jump="shipping">+ Versand ab {{make_price price=$product.min_versand}}</a>
          {{/if}}
        </p>
        {{if $product.finanzierung}}
          <div class="finanzierung">
            <a href="#section-financing" title="mehr Informationen" class="finanzierung-link note-jump" data-jump="financing">
              <span>0% Finanzierung möglich</span>
            </a>
          </div>
        {{/if}}
        <div class="avail">{{lieferbaranzeige_compact id=$product.lieferbaranzeige express=$product.express}}</div>
        {{if $product.paketfaehig == 1}}
          <div class="note">paketversandfähiger Artikel</div>
        {{/if}}
        {{if $product.paketfaehig == -1}}
          <div class="note">nicht paketversandfähiger Artikel</div>
        {{/if}}

  </div>
</section>

    {{if !$product->getNotOrderable()}}
      <form class="actions std_form put_in_basket" action="{{url url='/warenkorb/'}}" method="GET" id="put_in_basket">
        <input type="hidden" name="add_item" value="{{$product.product_id}}" />

        <div class="qty" role="group" aria-label="Menge auswählen">
          <button class="button" type="button" aria-label="Menge verringern">-</button>
          <input class="input" type="number" name="menge" value="1" min="1" aria-label="Anzahl" />
          <button class="button" type="button" aria-label="Menge erhöhen">+</button>
        </div>

        <button class="action hover-underline" type="submit">
        <img class="logo" src="{{$_cfg.grafik_url}}icons/cart-white.svg" alt="" aria-hidden="true" decoding="async" width="24" height="24" />
          <span class="text">in den Warenkorb</span>
        </button>

          <div class="payment-methods">
              {{if $profile->isPaymentMethodByKey('PAYPAL')}}<img src="/assets/icons/payment-paypal.svg" alt="PayPal" width="76" height="32" loading="lazy" decoding="async">{{/if}}
              {{if $profile->isPaymentMethodByKey('KREDITKARTE')}}<img src="/assets/icons/payment-visa.svg" alt="Visa" width="76" height="32" loading="lazy" decoding="async">{{/if}}
              {{if $profile->isPaymentMethodByKey('KREDITKARTE')}}<img src="/assets/icons/payment-mastercard.svg" alt="Mastercard" width="76" height="32" loading="lazy" decoding="async">{{/if}}
              {{if $profile->isPaymentMethodByKey('KREDITKARTE')}}<img class="americanexpress" src="/assets/icons/payment-americanexpress.svg" alt="American Express" width="76" height="32" loading="lazy" decoding="async">{{/if}}
              {{if $profile->isPaymentMethodByKey('VORKASSE')}}<img src="/assets/icons/payment-vorkasse.svg" alt="Vorkasse" width="76" height="32" loading="lazy" decoding="async">{{/if}}
              {{if $profile->isPaymentMethodByKey('FINANZIERUNG_COMMERZ')}}<img src="/assets/icons/payment-consors.svg" alt="Finanzierung - BNP Paribas S.A." width="76" height="32" loading="lazy" decoding="async">{{/if}}
          </div>

        {{if isset($paypal)}}
          <div class="paypal-row">
            <div class="direct">
              <div id="paypal-buttons-product"  data-client-id="{{$paypal->getClientId()}}"></div>
            </div>
              <div class="later">
               <div data-pp-message data-pp-amount="{{$product.vk_brutto}}" data-pp-style-layout="text" data-pp-style-logo-type="inline" data-pp-placement="product"></div>
            </div>
          </div>
        {{/if}}
      </form>
    {{/if}}
  </div>

</article>
{{/if}}

