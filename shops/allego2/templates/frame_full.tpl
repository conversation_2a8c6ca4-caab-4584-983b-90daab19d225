{{include file='frame_head.tpl'}}

<div class="layout-content">
  {{block name='layout_header'}}
    {{include file="components/header/header.tpl"}}
  {{/block}}

  <div class="header-spacer"></div>

  {{if template_content_exists('customer_information')}}
    <section class="customer-information" role="status">
      {{*<svg class="customer-information__icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_customer_info_order" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
          <rect width="24" height="24" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_customer_info_order)">
          <path d="M12 17C12.2833 17 12.5208 16.9042 12.7125 16.7125C12.9042 16.5208 13 16.2833 13 16C13 15.7167 12.9042 15.4792 12.7125 15.2875C12.5208 15.0958 12.2833 15 12 15C11.7167 15 11.4792 15.0958 11.2875 15.2875C11.0958 15.4792 11 15.7167 11 16C11 16.2833 11.0958 16.5208 11.2875 16.7125C11.4792 16.9042 11.7167 17 12 17ZM11 13H13V7H11V13ZM8.25 21L3 15.75V8.25L8.25 3H15.75L21 8.25V15.75L15.75 21H8.25ZM9.1 19H14.9L19 14.9V9.1L14.9 5H9.1L5 9.1V14.9L9.1 19Z" fill="#DE5A13"/>
        </g>
      </svg>*}}
      <span class="customer-information__text">{{content id='customer_information'}}</span>
    </section>
  {{/if}}


  <main id="content_section" itemscope itemtype="https://schema.org/WebPage">
    {{block name='breadcrumbs'}}
      {{include file="components/layout/breadcrumbs.tpl"}}
    {{/block}}
    {{block name='page_content'}}{{/block}}

    {{* Optional cross-sell *}}
    {{* {{if $ad_products}}
      <section id="recommended_products">
        <ul>
          {{foreach item="list_product" from=$ad_products}}
            {{include file="cat/part_raster_product.tpl"}}
          {{/foreach}}
        </ul>
      </section>
    {{/if}}
    *}}
  </main>

  <div class="_space-6 _space-desktop-8"></div>

  {{block name='layout_footer'}}
    {{include file='components/layout/footer.tpl'}}
  {{/block}}
</div>

{{include file='frame_foot.tpl'}}
