{{extends file='frame_full.tpl'}}

{{block name='page_content'}}
  <div class="page-device">
    <section class="sidebar-layout">
      {{block name='device_site_navigation'}}
        <aside class="sidebar device-sidebar">
          <div class="device-info _surface">
            <h1 class="title text-1-bold">{{$headline}}</h1>
            <div class="device-facts">
              <div class="fact-item">
                <div class="fact-label">Marke:</div>
                <div class="fact-value text-1-bold">{{$device.brand}}</div>
              </div>
              {{if $device.model_name}}
                <div class="fact-item">
                  <div class="fact-label">Modell:</div>
                  <div class="fact-value text-1-bold">{{$device.model_name}}</div>
                </div>
              {{/if}}
              {{if $device.model_code}}
                <div class="fact-item">
                  <div class="fact-label">Modellnummer:</div>
                  <div class="fact-value text-1-bold">{{$device.model_code}}</div>
                </div>
              {{/if}}
              {{if $device.product_periode}}
                <div class="fact-item">
                  <div class="fact-label">Produktionszeitraum:</div>
                  <div class="fact-value text-1-bold">{{$device.product_periode}}</div>
                </div>
              {{/if}}
              {{if $device.device_detail_cat_name}}
                <div class="fact-item">
                  <div class="fact-label">Kategorie:</div>
                  <div class="fact-value text-1-bold">{{$device.device_detail_cat_name}}</div>
                </div>
              {{/if}}
            </div>
          </div>

          <div class="category-filter _surface" data-render-mode="compact" data-icon-filter="/assets/icons/filter.svg" data-icon-chevron="/assets/icons/down-chevron.svg">
            <h3 class="heading-3">Passende Kategorie wählen:</h3>
            <div id="filters-container-new" data-loader-target="top-products" {{if isset($canonical_url)}}data-reset-url="{{$canonical_url}}"{{/if}}>
            </div>
          </div>
        </aside>
      {{/block}}

      <div class="primary">
        <div class="device-products-header">{{$headline}}</div>

        {{block name='device_products'}}
          {{if $products->getTotal()}}
            {{include file='components/listing/listing-product-list-inner.tpl'}}
          {{/if}}
        {{/block}}
      </div>
    </section>
  </div>
{{/block}}
