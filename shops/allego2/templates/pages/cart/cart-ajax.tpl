<div class="page-cart">
    <div class="container">
        <div class="container_product">
            <div class="product_main">
                <h1 class="section-title">Warenkorb</h1>

                {{if $warenkorb->isEmpty()}}
                <section class="basket-empty" aria-labelledby="basket-empty-heading">
                    <div class="image-banner -empty" aria-hidden="true">
                        <div class="viewport">
                            <div class="content">
                                <h2 id="basket-empty-heading" class="heading-2" aria-live="polite">Ihr Warenkorb ist leer</h2>
                            </div>
                        </div>
                    </div>

                    <div class="_surface">
                        <p class="text-bold">Du hast noch nichts in den Warenkorb gelegt.</p>
                        <p class="text-1">
                            Entdecke unsere große Auswahl an Produkten rund um Smart Home, Garten, Haushalt und Installation, die dein Zuhause effizienter und komfortabler machen.
                            Stöbere außerdem durch die neuesten GOODIES mit innovativen Produktfeatures, die deinen Alltag nicht nur erleichtern, sondern auch spannender gestalten.
                        </p>

                        <div class="actions" style="--gap: var(--space-4);">
                            <a href="{{url url='/'}}" class="action back hover-underline" aria-label="Zurück">
                                <img class="icon" src="{{$_cfg.grafik_url}}/icons/arrow-left.svg" alt="" width="24" height="24"/>
                                <span class="text">Zurück</span>
                            </a>
                            <a href="{{url url='/'}}" class="action primary hover-underline" aria-label="Produkte entdecken">
                                <span class="text">Produkte entdecken</span>
                            </a>
                            <a href="{{url url='/#goodies'}}" class="action goodies hover-underline" aria-label="GOODIES entdecken">
                                <img class="icon" src="{{$_cfg.grafik_url}}/icons/goodie-smiley-orange.svg" alt="" width="24" height="24"/>
                                <span class="text -gradient">GOODIES entdecken</span>
                            </a>
                        </div>
                    </div>
                </section>
                {{else}}
                <form action="{{url url="/warenkorb/"}}" method="post" class="cart-form">
                    <div class="cart-list">
                        {{foreach from=$warenkorb->getWarenkorbItemsWithoutServices() item=warenkorb_item}}
                        {{assign var="product_id" value=$warenkorb_item->getProductId()}}
                        {{assign var="item_id" value=$warenkorb_item->getItemId()}}

                        <div class="cart-item-card {{if $warenkorb_item->getExtraServices()}}-has-services{{/if}}">
                            <div class="media">
                                <img src="{{picture_url product_name=$warenkorb_item->getProductName() product_media_id=$warenkorb_item->getExtra('product_media_id') size='nor' product_shop_url=$warenkorb_item->getProductShopUrl()}}"
                                     alt="{{$warenkorb_item->getProductName()}}"
                                     class="image" width="300" height="300">
                            </div>

                            <div class="details">
                                <div class="head">
                                    <h2 class="title">
                                        <a href="/{{$warenkorb_item->getProductShopUrl()}}" class="titlelink">
                                            {{$warenkorb_item->getProductName()}}
                                        </a>
                                    </h2>
                                </div>

                                <div class="meta">
                                    <div class="codes">
                                        Art.-Nr.: {{$warenkorb_item->getProductNr()}}<br/>
                                        EAN: {{$warenkorb_item->getEan()}}
                                    </div>
                                    <div class="avail">{{lieferbaranzeige_compact id=$warenkorb_item->getLieferbaranzeige() express=$warenkorb_item->getExpressable()}}</div>
                                    {{if $warenkorb_item->getWarenkorbProductTyp() === 'aswo'}}
                                    <div class="notice">
                                        Ein Versand dieses Artikels nach Österreich ist derzeit logistisch nicht möglich.
                                    </div>
                                    {{/if}}
                                </div>

                                <div class="pricing">
                                    <div class="quantity">
                                        <button type="button" class="btn" data-action="decrease" onclick="var i=this.parentNode.querySelector('.input'); i.value=Math.max(0,(+i.value||1)-1); this.form.submit();">−</button>
                                        <input type="number"
                                               name="quantity[{{$warenkorb_item->getItemId()}}]"
                                               value="{{$warenkorb_item->getAnzahl()}}"
                                               class="input"
                                               min="0"
                                               onchange="this.form.submit()"/>
                                        <button type="button" class="btn" data-action="increase" onclick="var i=this.parentNode.querySelector('.input'); i.value=(+i.value||1)+1; this.form.submit();">+</button>
                                    </div>

                                    <div class="prices">
                                        <div class="single">
                                            <span class="label">Einzelpreis:</span>
                                            <span class="amount">{{make_price price=$warenkorb_item->getVkBrutto()}}</span>
                                        </div>
                                        <div class="total">
                                            <span class="label -bold">Gesamt:</span>
                                            <span class="amount -bold">{{make_price price=$warenkorb_item->getVkBruttoTotal()}}</span>
                                        </div>
                                        <div class="note">
                                            *inkl. MwSt.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{if $warenkorb_item->getExtraServices()}}
                            <section class="services-box" role="group" aria-labelledby="srv-title-{{$item_id}}">
                                <header class="head">
                                    <div class="title-row">
                                        <h3 id="srv-title-{{$item_id}}" class="title">Optionale Serviceleistungen</h3>
                                        <button type="button" class="info" aria-label="Informationen anzeigen" data-modal-id="service_informations_{{$warenkorb_item->getProductId()}}">
                                            <img class="icon" src="{{$_cfg.grafik_url}}icons/information.svg" alt="" width="24" height="24"/>
                                        </button>
                                    </div>
                                    <p class="subtitle">verlängert die Lieferzeit um bis zu 10 Werktage</p>
                                </header>

                                <div class="services-list">
                                    {{assign var='notice_shown' value=0}}
                                    {{foreach from=$warenkorb_item->getExtraServices() item=extra_service_id}}
                                    {{assign var="extra_service" value=$extra_services[$extra_service_id]|default:null}}
                                    {{if $extra_service}}
                                    {{assign var="service_group_active" value=$warenkorb_item->isServiceGroupActice($extra_service_id)}}
                                    {{assign var='blocked' value=$extra_service.service_dependencies|@count > 0}}
                                    {{assign var='bottom_notice' value=''}}

                                    {{if $extra_service.service_dependencies|@count}}
                                    {{foreach from=$extra_service.service_dependencies item=$dependency_id}}
                                    {{if $warenkorb_item->isServiceActice($dependency_id)}}
                                    {{assign var='blocked' value=false}}
                                    {{/if}}
                                    {{/foreach}}

                                    {{if !($notice_service_id|default:null)}}
                                    {{foreach from=$notice_service_ids|default:[] item=$possible_service_id}}
                                        {{if in_array($possible_service_id, $warenkorb_item->getExtraServices())}}
                                            {{assign var='notice_service_id' value=$possible_service_id}}
                                        {{/if}}
                                    {{/foreach}}
                                    {{/if}}

                                    {{if in_array($notice_service_id, $extra_service.service_dependencies) || $notice_shown}}
                                    {{if $notice_shown == 0}}
                                    {{assign var='notice_shown' value=1}}
                                    <hr>
                                    <div class="notice-text">
                                        Folgende Serviceleistungen sind nur in Kombination mit "{{$extra_services[$notice_service_id].service_name_shop}}" buchbar.
                                    </div>
                                    {{/if}}

                                    {{foreach from=$extra_service.service_dependencies item=dependency_id}}
                                    {{if $dependency_id != $notice_service_id && isset($extra_services[$dependency_id])}}
                                    {{if $bottom_notice}}
                                    {{assign var='bottom_notice' value="`$bottom_notice` ODER"}}
                                    {{/if}}
                                    {{assign var='bottom_notice' value="\"`$extra_services[$dependency_id].service_name_shop`\""}}
                                    {{/if}}
                                    {{/foreach}}
                                    {{/if}}
                                    {{/if}}

                                    {{assign var='service_vk_brutto_multi' value=$product_extra_service_data[$warenkorb_item->getProductId()][$extra_service_id].service_vk_brutto_multi|default:null}}
                                    {{if $service_vk_brutto_multi}}
                                    {{foreach from=$service_vk_brutto_multi item=vk_daten}}
                                    {{assign var="sub_service_id" value=$vk_daten.id}}
                                    {{assign var="raw_service_id" value=$extra_service_id|cat:'|'|cat:$sub_service_id}}
                                    {{assign var="service_active" value=$warenkorb_item->isServiceActice($raw_service_id)}}

                                    <div class="service-row {{if $service_active}}-active{{else}}-inactive{{/if}} {{if $blocked}}-blocked{{/if}}">
                                        <label class="checkbox-option">
                                            <input type="checkbox"
                                                   class="checkbox-input extra-service-checkbox"
                                                   data-extra-service-id="{{$raw_service_id}}"
                                                   data-item-id="{{$item_id}}"
                                                   {{if $service_active}}checked="checked"{{/if}}
                                                   {{if $blocked}}disabled="disabled" aria-disabled="true"{{/if}}
                                                   aria-label="{{$extra_service.service_name_shop}} - {{$vk_daten.text}} auswählen"/>
                                            <span class="checkmark" aria-hidden="true"></span>
                                            <span class="name">{{$extra_service.service_name_shop}} <span class="variant">{{$vk_daten.text}}</span></span>
                                        </label>
                                        <span class="price">{{make_price price=$vk_daten.vk_brutto}}*</span>
                                    </div>
                                    {{/foreach}}
                                    {{else}}
                                    <div class="service-row {{if $service_group_active}}-active{{else}}-inactive{{/if}} {{if $blocked}}-blocked{{/if}}">
                                        <label class="checkbox-option">
                                            <input type="checkbox"
                                                   class="checkbox-input extra-service-checkbox"
                                                   data-extra-service-id="{{$extra_service_id}}"
                                                   data-item-id="{{$item_id}}"
                                                   {{if $service_group_active}}checked="checked"{{/if}}
                                                   {{if $blocked}}disabled="disabled" aria-disabled="true"{{/if}} aria-label="{{$extra_service.service_name_shop}} auswählen"/>
                                            <span class="checkmark" aria-hidden="true"></span>
                                            <span class="name">{{$extra_service.service_name_shop}}</span>
                                            {{if $bottom_notice}}
                                            <span class="sublabel">
                                            nur in Verbindung mit "{{$extra_services[$notice_service_id].service_name_shop}}" UND
                                            {{$bottom_notice}}
                                        </span>
                                            {{/if}}
                                        </label>
                                        <span class="price">{{make_price price=$extra_service.service_vk_brutto}}*</span>
                                    </div>
                                    {{/if}}
                                    {{/if}}
                                    {{/foreach}}

                                    <div id="service_informations_{{$warenkorb_item->getProductId()}}" style="display: none;">
                                        <div class="extra_services_description">
                                            {{foreach from=$warenkorb_item->getExtraServices() item=extra_service_id}}
                                            {{assign var="extra_service" value=$extra_services[$extra_service_id]|default:null}}
                                            {{if $extra_service}}
                                            <div class="extra_service_description">
                                                <div class="title">{{$extra_service.service_name_shop}}</div>
                                                <div class="description">{{$extra_service.service_beschreibung nofilter}}</div>
                                            </div>
                                            {{/if}}
                                            {{/foreach}}
                                        </div>
                                    </div>
                                </div>

                                <footer class="foot">
                                    <div class="sum"><strong>Gesamt Serviceleistungen: {{make_price price=$warenkorb_item->getVkBruttoServices()}}</strong></div>
                                    <small class="vat-note">*inkl. MwSt.</small>
                                </footer>
                            </section>
                            {{/if}}

                            <div class="actions">
                                <a href="{{url url="/warenkorb/?del_item=$item_id"}}" class="delete hover-underline">
                                    <img class="icon" src="{{$_cfg.grafik_url}}/icons/trashcan.svg" alt="" width="24" height="24"/>
                                    <span class="text">Produkt löschen</span>
                                </a>
                            </div>
                        </div>
                        {{/foreach}}
                    </div>
                </form>
                {{/if}}
            </div>
        </div>
    </div>

    {{if !$warenkorb->isEmpty()}}
        {{assign var="checkout_taxes" value=$warenkorb->getTaxes()}}

        <section class="cart-summary" aria-labelledby="cart-summary-heading" role="region">
            <div class="container">
                <article class="_card" role="group" aria-label="Kostenübersicht">
                    <div class="breakdown">
                        <dl class="line-items">
                            <div class="item">
                                <dt class="label">Summe Artikel (brutto)</dt>
                                <dd class="value">{{make_price price=$warenkorb->getArtikelSummeBrutto()}}</dd>
                            </div>

                            <div class="item -with-info">
                                <dt class="label">
                                    <span class="text">Versandkosten ab</span>
                                    <button
                                            class="info-button"
                                            type="button"
                                            aria-label="Versandkosten Details anzeigen"
                                            aria-controls="shipping-info"
                                            aria-expanded="false"
                                            data-modal-id="shipping-info"
                                    >
                                        <img class="icon" src="{{$_cfg.grafik_url}}/icons/information.svg" alt="" width="24" height="24"/>
                                    </button>
                                </dt>
                                <dd class="value">{{make_price price=$warenkorb->getMinShippingPrice()}}</dd>
                            </div>

                            <div id="shipping-info" hidden>
                                Versandkosten bei Standardlieferung, Inselzuschläge oder Expresszuschläge können die angegebenen Versandkosten erhöhen. Selbstabholung: 0,00 EUR
                            </div>

                            <div class="item">
                                <dt class="label">Serviceleistungen</dt>
                                <dd class="value">{{make_price price=$warenkorb->getServicesSummeBrutto()}}</dd>
                            </div>

                            <div class="item -total">
                                <dt class="label">Gesamtbetrag</dt>
                                <dd class="value">{{make_price price=$warenkorb->getGesamtPreis()}}</dd>
                            </div>

                            {{foreach from=$checkout_taxes item=tax_amount key=tax_rate}}
                            {{if $tax_amount > 0}}
                            <div class="item -secondary">
                                <dt class="label">inkl. {{$tax_rate}}% MwSt.</dt>
                                <dd class="value">{{make_price price=$tax_amount}}</dd>
                            </div>
                            {{/if}}
                            {{/foreach}}
                        </dl>

                        <div class="divider" role="separator" aria-hidden="true"></div>
                    </div>

                    <footer class="actions">
                        <a href="{{url url="/kasse/"}}" aria-label="Jetzt bestellen" class="pay-now">
                            <div class="button -primary hover-underline">
                                <span class="text">Jetzt bestellen</span>
                                <img class="icon" src="{{$_cfg.grafik_url}}/icons/arrow-right-white.svg" alt="" width="24" height="24"/>
                            </div>

                            <div class="payment-methods">
                                {{if $profile->isPaymentMethodByKey('PAYPAL')}}<img src="/assets/icons/payment-paypal.svg" alt="PayPal" width="76" height="32" loading="lazy" decoding="async">{{/if}}
                                {{if $profile->isPaymentMethodByKey('SOFORTUEBERWEISUNG')}}<img src="/assets/icons/payment-sofort.svg" alt="Sofort" width="76" height="32" loading="lazy" decoding="async">{{/if}}
                                {{if $profile->isPaymentMethodByKey('KREDITKARTE')}}<img src="/assets/icons/payment-visa.svg" alt="Visa" width="76" height="32" loading="lazy" decoding="async">{{/if}}
                                {{if $profile->isPaymentMethodByKey('KREDITKARTE')}}<img src="/assets/icons/payment-mastercard.svg" alt="Mastercard" width="76" height="32" loading="lazy" decoding="async">{{/if}}
                                {{if $profile->isPaymentMethodByKey('KREDITKARTE')}}<img class="americanexpress" src="/assets/icons/payment-americanexpress.svg" alt="American Express" width="76" height="32" loading="lazy" decoding="async">{{/if}}
                                {{if $profile->isPaymentMethodByKey('VORKASSE')}}<img src="/assets/icons/payment-vorkasse.svg" alt="Vorkasse" width="76" height="32" loading="lazy" decoding="async">{{/if}}
                                {{if $profile->isPaymentMethodByKey('FINANZIERUNG_COMMERZ')}}<img src="/assets/icons/payment-consors.svg" alt="Finanzierung - BNP Paribas S.A." width="76" height="32" loading="lazy" decoding="async">{{/if}}
                            </div>
                        </a>

                        {{if isset($paypal)}}
                        <div class="paypal-row">
                            <div class="direct">
                                <div id="paypal-buttons-basket" data-client-id="{{$paypal->getClientId()}}"></div>
                            </div>
                            <div class="later">
                                <div data-pp-message data-pp-amount="{{$warenkorb->getWarenwert() + $warenkorb->getMinShippingPrice()}}" data-pp-style-layout="text" data-pp-style-logo-type="inline" data-pp-placement="cart"></div>
                            </div>
                        </div>
                        {{/if}}
                    </footer>
                </article>
            </div>
        </section>
    {{/if}}

    {{if !$warenkorb->isEmpty()}}
        <div class="_space-6 _space-desktop-6"></div>

        {{if isset($crosssell_products) && $crosssell_products|@count}}
            <section class="crossselling">
                <div class="container">
                    <h2 class="section-title">Wird oft zusammen gekauft</h2>

                    <div class="product-grid">
                        {{foreach from=$crosssell_products item=$crosssell_product}}
                        {{include file='components/product/cart-product-card.tpl' product=$crosssell_product }}
                        {{/foreach}}
                    </div>

                    <button type="button" class="show-all-products">Alle anzeigen</button>
                </div>
            </section>
        {{/if}}
    {{/if}}

    <div class="_space-6 _space-desktop-6"></div>

    {{include 'components/benefits.tpl'}}
</div>

{{if isset($toast_message)}}
<script id="toast-message" type="application/json">
  {
    "status": "error",
    "message": "{{$toast_message}}"
  }
</script>
{{/if}}