<div class="banner success">Der Artikel wurde erfolgreich in den Warenkorb gelegt</div>
<div class="products">
	<div class="product">
		<img alt="{{$warenkorb_item->getProductName()}}"
			 class="image"
			 src="{{if $warenkorb_item->getExtra('aswo_picture_url')}}
					{{$warenkorb_item->getExtra('aswo_picture_url')}}
				{{else}}
					{{picture_url product_name=$warenkorb_item->getProductName()
						product_media_id=$warenkorb_item->getExtra('product_media_id')
						product_shop_url=$warenkorb_item->getProductShopUrl()
						size='250x250'
					}}
				{{/if}}">
		<div class="title">{{$warenkorb_item->getProductName()}}</div>
		<div class="details">
			{{if $warenkorb_item->getProductNr()}}
				Art.-Nr.: {{$warenkorb_item->getProductNr()}}<br>
			{{/if}}
			{{if $warenkorb_item->getEan()}}
				EAN: {{$warenkorb_item->getEan()}}
			{{/if}}
		</div>
		<div class="lieferzeit">
			{{lieferbaranzeige_compact id=$warenkorb_item->getLieferbaranzeige() express=$warenkorb_item->getExpressable()}}
		</div>
		<div class="amount">
			<form class="actions std_form put_in_basket" action="http://smartgoods.ecom.ddev.site/warenkorb/" method="GET">
				<input type="hidden" name="add_item" value="{{$warenkorb_item->getProductId()}}">

				<div class="qty" role="group" aria-label="Menge auswählen" style="cursor: pointer;">
					<button class="button" type="button" aria-label="Menge verringern">-</button>
					<input class="input" type="number" name="menge" value="{{$warenkorb_item->getAnzahl()}}" min="1" aria-label="Anzahl">
					<button class="button" type="button" aria-label="Menge erhöhen">+</button>
				</div>
			</form>

		</div>
		<div class="price">
			Einzelpreis: {{make_price price=$warenkorb_item->getVkBruttoSum()}}
			<b>Gesamt: {{make_price price=$warenkorb_item->getVkBruttoSum() * $warenkorb_item->getAnzahl()}}</b>
			<small>inkl. MwSt. + Versand ab {{make_price price=$product.min_versand}}</small>
		</div>
	</div>
</div>
<div class="foot">
	<a href="#" class="button" onclick="event.preventDefault(); try{ jQuery('.modalWindow').trigger('click'); }catch(e){} return false;">
		Weiter einkaufen
	</a>
	<a href="{{url url="/warenkorb/"}}" class="button proceed">
		Zum Warenkorb
	</a>
</div>