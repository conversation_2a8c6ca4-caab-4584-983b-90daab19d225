{{extends file='frame_full.tpl'}}

{{block name='page_content'}}
  <div class="page-category">
    <section class="sidebar-layout">
      {{block name='category_site_navigation'}}
        <aside class="sidebar">
            {{include file='components/layout/category-sidebar.tpl'}}
        </aside>
      {{/block}}
      <div class="primary">
        {{block name='category_short_description'}}
          <div class="_surface category-short-description">
            <h2 class="title heading-2">{{$category->getCatName()}}</h2>
            <div class="text text-1">{{$category->getCatDescriptionTop()|default:''|cms_parse nofilter}}</div>
          </div>
        {{/block}}

        {{block name='category_cards_grid'}}
          <section class="category-cards-grid" id="category-cards-grid" aria-label="Unterkategorien">
            <div class="grid" role="list">
              {{foreach from=$kategorien item='sub_cat'}}
                {{if $sub_cat.parent_cat_id == $cat_id}}
                  <a class="card" role="listitem" href="{{url url=$sub_cat.cat_shop_url}}">
                    {{if $sub_cat.cat_picture}}
                      <img class="image" src="{{$_cfg.res_host}}cat_logos/{{$sub_cat.cat_picture}}" alt="{{$sub_cat.cat_name_webshop}}" width="50" height="50" loading="lazy" decoding="async" />
                    {{else}}
                      <img class="image" src="{{$_cfg.res_host}}cat_logos/nopic.jpg" alt="{{$sub_cat.cat_name_webshop}}" width="50" height="50" loading="lazy" decoding="async" />
                    {{/if}}
                    <span class="name">{{$sub_cat.cat_name_webshop|default:$sub_cat.cat_name}}</span>
                  </a>
                {{/if}}
              {{/foreach}}
            </div>
          </section>
        {{/block}}
      </div>
    </section>

    {{* Non-leaf categories: render goodies and products outside sidebar-layout (full width) *}}
    {{block name='category_goodies'}}
      {{if $goodies|@count}}
        <h2 class="section-title">Top GOODIES für {{$category->getCatName()}}</h2>
        {{include file='components/goodie/goodie-list.tpl' layout='list' show_more_button=true goodies=$goodies}}
      {{/if}}
    {{/block}}

    {{block name='category_top_products'}}
      <h2 class="section-title">Top Produkte für {{$category->getCatName()}}</h2>
      <section class="top-products -full-row" id="top-products" aria-label="Produkte aus dieser Kategorie">
        {{if isset($products) && $products|@count}}
          {{if isset($is_leaf_category) && $is_leaf_category}}
            {{foreach from=$products item='p' name='productLoop'}}
              <div class="product-wrapper{{if $smarty.foreach.productLoop.iteration > 20}} -hidden{{/if}}">
                {{include file='components/product/product-card.tpl' product=$p}}
              </div>
            {{/foreach}}
          {{else}}
            {{foreach from=$products item='p'}}
              {{include file='components/product/product-card.tpl' product=$p}}
            {{/foreach}}
          {{/if}}
{{*          {{if isset($is_leaf_category) && $is_leaf_category && $products_count > 20}}*}}
{{*            <button class="action show-all-products" type="button" aria-controls="top-products" aria-expanded="false">*}}
{{*              <span class="text">Alle Produkte anzeigen</span>*}}
{{*            </button>*}}
{{*          {{/if}}*}}
        {{/if}}
      </section>
      {{$navigation nofilter}}
    {{/block}}

    {{block name='category_long_seo'}}
      <section class="category-long-description" aria-label="Kategorie Beschreibung">
        <div class="text text-1">{{$category->getCatDescriptionBottom()|default:''|cms_parse nofilter}}</div>
      </section>
    {{/block}}
  </div>
{{/block}}
