<button class="detail-nav-item" type="button" data-target="services" aria-expanded="false" aria-controls="section-services">
    <div class="item-content">
        <div class="item-left">
            <img class="icon" src="{{$_cfg.grafik_url}}icons/pdp-details-services.svg" alt="" role="presentation" width="24" height="24" />
            <span class="label">Zusatzleistungen</span>
        </div>
        <img class="arrow" src="{{$_cfg.grafik_url}}icons/arrow-right.svg" alt="" role="presentation" width="24" height="24" />
    </div>
</button>
<div class="detail-section" id="section-services">
    <div class="section-content">
        <h2 class="heading-2">Optionale Zusatzleistungen</h2>
        <form method="post" action="/warenkorb/" id="put_in_basket_service" class="put_in_basket_service">
            <input type="hidden" name="add_item_extended" value="1">
            <input type="hidden" name="product_id" value="{{$product.product_id}}">
            <input type="hidden" name="menge" value="{{if $current_cart_quantity > 0}}{{$current_cart_quantity}}{{else}}1{{/if}}" id="service_quantity" data-cart-qty="{{$current_cart_quantity}}">

            <section class="services-box" role="group" aria-labelledby="srv-title-{{$product.product_id}}">
                <div class="services-list">
                    {{if $extra_services|@count}}
                        <section class="services-box" role="group" aria-labelledby="srv-title">
                            <header class="head">
                                <div class="title-row">
                                    <h3 id="srv-title" class="title">Optionale Serviceleistungen</h3>
                                    <button type="button" class="info" aria-label="Informationen anzeigen" data-modal-id="service_informations">
                                        <img class="icon" src="{{$_cfg.grafik_url}}icons/information.svg" alt="" width="24" height="24"/>
                                    </button>
                                </div>
                                <p class="subtitle">verlängert die Lieferzeit um bis zu 10 Werktage</p>
                            </header>

                            <div class="services-list">
                                {{assign var='notice_shown' value=0}}
                                {{foreach from=$extra_services item=extra_service}}
                                    {{assign var='blocked' value=$extra_service.service_dependencies|@count > 0}}
                                    {{assign var='bottom_notice' value=''}}

                                    {{if $extra_service.service_dependencies|@count}}
                                        {{if !($notice_service_id|default:null)}}
                                            {{foreach from=$notice_service_ids|default:[] item=$possible_service_id}}
                                                {{if in_array($possible_service_id, $product.extra_services)}}
                                                    {{assign var='notice_service_id' value=$possible_service_id}}
                                                {{/if}}
                                            {{/foreach}}
                                        {{/if}}

                                        {{if in_array($notice_service_id, $extra_service.service_dependencies) || $notice_shown}}
                                            {{if $notice_shown == 0}}
                                                {{assign var='notice_shown' value=1}}
                                                <hr>
                                                <div class="notice-text">
                                                    Folgende Serviceleistungen sind nur in Kombination mit "{{$extra_services[$notice_service_id].service_name_shop}}" buchbar.
                                                </div>
                                            {{/if}}

                                            {{foreach from=$extra_service.service_dependencies item=dependency_id}}
                                                {{if $dependency_id != $notice_service_id && isset($extra_services[$dependency_id])}}
                                                    {{if $bottom_notice}}
                                                        {{assign var='bottom_notice' value="`$bottom_notice` ODER"}}
                                                    {{/if}}
                                                    {{assign var='bottom_notice' value="\"`$extra_services[$dependency_id].service_name_shop`\""}}
                                                {{/if}}
                                            {{/foreach}}
                                        {{/if}}
                                    {{/if}}

                                    {{assign var='service_vk_brutto_multi' value=$product_extra_service_data[$extra_service.service_id].service_vk_brutto_multi|default:null}}
                                    {{if $service_vk_brutto_multi}}
                                        {{foreach from=$service_vk_brutto_multi item=vk_daten}}
                                            {{assign var="sub_service_id" value=$vk_daten.id}}
                                            {{assign var="raw_service_id" value=$extra_service.service_id|cat:'|'|cat:$sub_service_id}}

                                            <div class="service-row {{if $blocked}}-blocked{{/if}}">
                                                <label class="checkbox-option">
                                                    <input type="checkbox"
                                                           class="checkbox-input extra-service-checkbox"
                                                           name="service[{{$raw_service_id}}]"
                                                           value="1"
                                                           {{if $extra_service.service_auto_booking}}checked{{/if}}
                                                           data-price="{{$extra_service.service_vk_brutto}}"
                                                           data-extra-service-id="{{$raw_service_id}}"
                                                           data-dependencies="[{{join(',', $extra_service.service_dependencies)}}]"
                                                           {{if $blocked}}disabled="disabled" aria-disabled="true"{{/if}}
                                                           aria-label="{{$extra_service.service_name_shop}} - {{$vk_daten.text}} auswählen"/>
                                                    <span class="checkmark" aria-hidden="true"></span>
                                                    <span class="name">{{$extra_service.service_name_shop}} <span class="variant">{{$vk_daten.text}}</span></span>
                                                </label>
                                                <span class="price">{{make_price price=$vk_daten.vk_brutto}}*</span>
                                            </div>
                                        {{/foreach}}
                                    {{else}}
                                        <div class="service-row {{if $blocked}}-blocked{{/if}}">
                                            <label class="checkbox-option">
                                                <input type="checkbox"
                                                       class="checkbox-input extra-service-checkbox"
                                                       {{if $extra_service.service_auto_booking}}checked{{/if}}
                                                       data-price="{{$extra_service.service_vk_brutto}}"
                                                       name="service[{{$extra_service.service_id}}]"
                                                       value="1"
                                                       data-extra-service-id="{{$extra_service.service_id}}"
                                                       data-dependencies="[{{join(',', $extra_service.service_dependencies)}}]"
                                                       {{if $blocked}}disabled="disabled" aria-disabled="true"{{/if}} aria-label="{{$extra_service.service_name_shop}} auswählen"/>
                                                <span class="checkmark" aria-hidden="true"></span>
                                                <span class="name">{{$extra_service.service_name_shop}}</span>
                                                {{if $bottom_notice}}
                                                    <span class="sublabel">
                                                        nur in Verbindung mit "{{$extra_services[$notice_service_id].service_name_shop}}" UND
                                                        {{$bottom_notice}}
                                                    </span>
                                                {{/if}}
                                            </label>
                                            <span class="price">{{make_price price=$extra_service.service_vk_brutto}}*</span>
                                        </div>
                                    {{/if}}
                                {{/foreach}}

                                <div id="service_informations" style="display: none;">
                                    <div class="extra_services_description">
                                        {{foreach from=$extra_services item=extra_service}}
                                            <div class="extra_service_description">
                                                <div class="title">{{$extra_service.service_name_shop}}</div>
                                                <div class="description">{{$extra_service.service_beschreibung nofilter}}</div>
                                            </div>
                                        {{/foreach}}
                                    </div>
                                </div>
                            </div>
                        </section>
                    {{/if}}
                </div>

                <footer class="foot" data-click-submit>
                    <div class="pricing-info">
                        <div class="sum">
                            Summe Service:
                        </div>
                        <div class="sum">
                            <span id="service_price_service">{{make_price_star price=0}}</span>
                        </div>
                        <div class="sum">
                            Gerät:
                        </div>
                        <div>
                            <span id="service_price_product" data-price="{{$product.vk_brutto}}">{{make_price_star price=$product.vk_brutto}}</span>
                        </div>
                        <div class="sum">
                            <strong>Gesamt:</strong>
                        </div>
                        <div class="sum">
                            <strong>
                                <span id="service_price_total">{{make_price_star price=$product.vk_brutto}}</span>
                            </strong>
                        </div>
                        <small class="vat-note">*inkl. MwSt.</small>
                    </div>
                    <div class="cart-action">
                        <button type="submit" class="btn -primary">
                            <img class="logo" src="/assets/icons/cart-white.svg" alt="" aria-hidden="true" decoding="async" width="24" height="24">
                            <span>in den Warenkorb</span>
                        </button>
                    </div>
                </footer>
            </section>
        </form>
    </div>
</div>