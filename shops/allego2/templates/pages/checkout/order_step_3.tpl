<section class="checkout-address" aria-labelledby="checkout-payment-title">
  <div class="page-header">
    <h1 id="checkout-payment-title" class="title">Zahlung &amp; Versand</h1>
  </div>

  <form method="post" action="{{$_controller->createViewUrl()}}" class="std_form address-form" novalidate>
    <input type="hidden" name="dol_set3" value="1">

    <input type="submit" name="do_3" value="Weiter" style="display: none;" />

    <div class="column">
      <fieldset class="_card">
        <div class="head">
          <h2 class="title">Versandart</h2>
        </div>
        <div class="inner">
          <fieldset class="radio-group">
            <legend class="legend">Versandart</legend>
            {{foreach from=$shipment_group_prices item="shipment_group_price" key="shipment_group_id"}}
              <label class="option">
                <input type="radio" name="versandart" value="{{$shipment_group_id}}" class="input" onchange="submit()" {{if $versandart == $shipment_group_id}}checked{{/if}} aria-checked="{{if $versandart == $shipment_group_id}}true{{else}}false{{/if}}">
                <span class="indicator" aria-hidden="true"></span>
                <span class="label">{{$shipment_group_price->getName()}}</span>
                <span class="meta price">{{make_price price=$shipment_group_price->getVkBrutto()}}</span>
              </label>
            {{/foreach}}
          </fieldset>
          {{* Shipping info blocks for selected option (retain legacy content) *}}
          {{if $versandart == 'exp'}}
            <div class="shipping-info text_content">{{content id='warenkorb_versand_express'}}</div>
          {{/if}}
          {{if $versandart == 'abh'}}
            <div class="shipping-info text_content">{{content id='warenkorb_versand_abholung'}}</div>
          {{/if}}
          {{if $versandart == 'eu_block'}}
            <div class="shipping-info text_content">{{content id='warenkorb_versand_abholung'}}</div>
          {{/if}}
          {{if isset($show_aswo_at_notice) && $show_aswo_at_notice}}
            <p class="hint" style="margin: 0;">Hinweis: Bitte beachten Sie dass die Lieferung Ihrer Bestellung nach Österreich derzeit 4 bis 8 Werktage dauern kann.</p>
          {{/if}}
        </div>
      </fieldset>
    </div>

    <div class="column">
      <fieldset class="_card">
        <div class="head">
          <h2 class="title">Zahlungsarten</h2>
        </div>
        <div class="inner">
          <fieldset class="radio-group -payments">
            <legend class="legend">Zahlungsarten</legend>
            {{foreach from=$zahlungsarten item="zahlungsart" key="zahlungs_id"}}
              {{if isset($zahlungsarten_preis[$zahlungs_id].vk_brutto) && $zahlungsarten_preis[$zahlungs_id].vk_brutto !== ''}}
                <label class="option{{if $zahlungsart.disabled}} -disabled{{/if}}">
                  {{if !$zahlungsart.disabled}}
                    <input aria-label="{{$zahlungsart.name}}" type="radio" name="zahlungsart" value="{{$zahlungs_id}}" class="input" onclick="submit()" {{if $selected_zahlungsart == $zahlungs_id}}checked{{/if}} aria-checked="{{if $selected_zahlungsart == $zahlungs_id}}true{{else}}false{{/if}}">
                    <span class="indicator" aria-hidden="true"></span>
                  {{else}}
                    <span class="indicator" aria-hidden="true"></span>
                  {{/if}}
  <img class="logo{{if $zahlungsart.disabled}} grayscale{{/if}}" src="{{$_cfg.grafik_url}}payment/{{$zahlungsart.logo}}" alt="" height="24">
                  <span class="label"><span class="name">{{$zahlungsart.name}}</span></span>
                  {{if $selected_zahlungsart == $zahlungs_id}}
                    {{if !empty($zahlungsarten_preis[$zahlungs_id].text)}}
                      <div class="payment-description">{{$zahlungsarten_preis[$zahlungs_id].text}}</div>
                    {{else}}
                      {{* Fallback to legacy content snippets for selected payment (text-only) *}}
                      {{if $zahlungs_id == 2}}
                        <div class="payment-description">{{content id=warenkorb_payment_nachnahme}}</div>
                      {{elseif $zahlungs_id == 1}}
                        <div class="payment-description">{{content id=warenkorb_payment_kreditkarte}}</div>
                      {{elseif $zahlungs_id == 39}}
                        <div class="payment-description">{{content id=warenkorb_payment_unzer}}</div>
                      {{elseif $zahlungs_id == 3}}
                        <div class="payment-description">{{content id=warenkorb_payment_vorkasse}}</div>
                      {{elseif $zahlungs_id == 15}}
                        <div class="payment-description">{{content id=warenkorb_payment_paypal}}</div>
                      {{elseif $zahlungs_id == 9}}
                        <div class="payment-description">{{content id=warenkorb_payment_bar}}</div>
                      {{elseif $zahlungs_id == 23}}
                        <div class="payment-description">{{content id=warenkorb_payment_sofortueberweisung}}</div>
                      {{elseif $zahlungs_id == 26}}
                        <div class="payment-description">{{content id=warenkorb_payment_barzahlen}}</div>
                      {{elseif $zahlungs_id == 24}}
                        <div class="payment-description">
                          {{content id=warenkorb_payment_finanzierung}}
                          {{**if $finanzierung_interest_rates}}
                            <br>
                            <strong>M&ouml;gliche Raten f&uuml;r Ihren Auftrag</strong>
                            <table id="finanz_table" class="finanz_table">
                                <tr>
                                  <th>Anzahl Raten</th>
                                  <th>Monatliche Rate</th>
                                  <th>Sollzins<br><small>(jährl., gebunden)</small></th>
                                  <th>Effektiver<br>Jahreszins</th>
                                  <th>Gesamtbetrag</th>
                                </tr>
                                <tbody>
                                  {{foreach $finanzierung_interest_rates AS $runtime => $rate}}
                                    <tr><td>{{$runtime}}</td><td></td><td></td><td>{{if $rate <= 0.01}}0{{else}}{{$rate|number_format:1:",":""}}{{/if}} %</td><td></td></tr>
                                  {{/foreach}}
                                </tbody>
                            </table>
                          {{/if**}}
                        </div>
                      {{/if}}
                    {{/if}}
                  {{/if}}
                  {{*
                      Wird das noch gebraucht??
                      {{if isset($zahlungsarten_preis[$zahlungs_id].vk_brutto)}}<span class="meta price">{{if $zahlungsarten_preis[$zahlungs_id].vk_brutto>0}}+{{/if}}{{make_price price=$zahlungsarten_preis[$zahlungs_id].vk_brutto}}</span>{{/if}}
                   *}}
                </label>
              {{/if}}
            {{/foreach}}
          </fieldset>
        </div>
      </fieldset>

      <nav class="form-actions">
        <div id="step3_normal" style="display: {{if $selected_zahlungsart == 15}}none{{else}}block{{/if}};">
          <button type="submit" name="do_step3" class="primary-button hover-underline">
            <span class="label">Weiter zur Übersicht</span>
            <img class="icon" src="https://c.animaapp.com/9HkCBdpl/img/navigation-5.svg" alt="" aria-hidden="true" width="24" height="24">
          </button>
        </div>
        
        {{if isset($paypal)}}
          <div id="step3_paypal" class="u-h-56" data-client-id="{{$paypal->getClientId()}}" style="display: {{if $selected_zahlungsart == 15}}block{{else}}none{{/if}};"></div>
        {{/if}}
        
        <a class="back-link hover-underline" href="{{$_controller->createViewUrl('step1')}}" aria-label="Zurück zur Adresse">
          <img class="icon" src="https://c.animaapp.com/Kl7AY8kJ/img/navigation-4.svg" alt="" width="24" height="24" />
          <span class="label">Zurück zur Adresse</span>
        </a>
      </nav>
    </div>
  </form>

  <script type="text/javascript">
    checkout_init.push('step3');
    _gaq.push(['_trackPageview', '/virt_trichter1/schritt3.html']);
  </script>
</section>
