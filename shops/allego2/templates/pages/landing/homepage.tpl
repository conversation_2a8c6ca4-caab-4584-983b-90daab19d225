{{extends file='frame_full.tpl'}}

{{block name='breadcrumbs'}}
{{/block}}

{{block name='page_content'}}
  <div class="page-home">
    {{block name='home_header'}}
      <section class="sidebar-layout">
        {{block name='home_sales_categories'}}
          <aside class="sidebar">
            <nav class="home-categories" aria-label="Kategorien Navigation">
              {{foreach from=$start_categories item='cat'}}
                <a class="item" href="{{url url=$cat.cat_shop_url}}">
                  <img class="icon" src="{{$_cfg.grafik_url}}cat-icons/{{$cat.cat_id}}.svg" alt="" width="24" height="24" decoding="async" />
                  <span class="name">{{$cat.cat_name_webshop|default:$cat.cat_name}}</span>
                  <img class="chevron" src="{{$_cfg.grafik_url}}icons/chevron-right.svg" alt="" width="24" height="24" decoding="async" fetchpriority="low" />
                </a>
              {{/foreach}}
            </nav>
          </aside>
        {{/block}}

        {{block name='home_hero'}}
          <div class="primary">
            <div class="image-banner">
              <a href="/elektroinstallation/" class="banner banner1">
                <div class="viewport">
                  <div class="content">
                    <h1 class="title">smartgoods</h1>
                    <p class="subtitle">Deine Extras. Deine Wahl.</p>
                  </div>
                  <img class="image" src="{{$_cfg.grafik_url}}images/banner-home-elektroinstallation.png" alt="Elektroinstallation" loading="lazy" decoding="async" />
                </div>
              </a>
              <a href="/haushaltsgeraete/" class="banner banner2">
                <div class="viewport">
                  <div class="content">
                    <h1 class="title">smartgoods</h1>
                    <p class="subtitle">Deine Extras. Deine Wahl.</p>
                  </div>
                  <img class="image" src="{{$_cfg.grafik_url}}images/banner-home-haushaltsgeraete.png" alt="Haushaltsgeräte" loading="lazy" decoding="async" />
                </div>
              </a>
            </div>
          </div>
        {{/block}}
      </section>
      <div class="_space-6 _space-desktop-8"></div>
    {{/block}}

    {{block name='home_brand_logos'}}
      <section class="logo-bar" role="region" aria-label="Partner brand logos">
      <div class="scroller">
        <img class="logo" src="{{$_cfg.grafik_url}}images/gira-brand.png" alt="Gira logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/abb-brand.png" alt="ABB logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/bosch-brand.png" alt="Bosch logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/siemens-brand.png" alt="Siemens logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/hager-brand.png" alt="Hager logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/aeg-brand.png" alt="AEG logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/siedle-brand.png" alt="Siedle logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
        <img class="logo" src="{{$_cfg.grafik_url}}images/eltako-brand.png" alt="Eltako logo" height="40" width="150" loading="lazy" decoding="async" fetchpriority="low" />
      </div>
    </section>
      <div class="_space-6 _space-desktop-8"></div>
    {{/block}}

    {{block name='home_goodies'}}
      {{if $goodies|@count}}
        <h2 class="section-title">Unsere Top GOODIES</h2>
        <div class="_space-4 _space-desktop-5"></div>
        {{include file='components/goodie/goodie-list.tpl' goodies=$goodies show_more_button=true}}
        <div class="_space-6 _space-desktop-8"></div>
      {{/if}}
    {{/block}}

    {{block name='home_top_categories'}}
      <h2 class="section-title">Unsere Top Kategorien</h2>
      <div class="_space-4 _space-desktop-5"></div>
      <section class="top-categories" aria-label="Top Kategorien">
        <!-- Banner 1: Zählerschränke (Full width) -->
        <div class="category-banner -full-width -zaehlerschraenke expandlink">
          <div class="image-banner">
            <div class="viewport">
              <div class="content">
                <h3 class="title">Zählerschränke</h3>
                <a class="action" href="/elektroinstallation/zaehlerschraenke/">smart wählen</a>
              </div>
              <img class="image -zaehlerschrank1" src="{{$_cfg.grafik_url}}images/categories/zaehlerschrank1.png" alt="Zählerschränke" loading="lazy" decoding="async" />
              <img class="image -zaehlerschrank2" src="{{$_cfg.grafik_url}}images/categories/zaehlerschrank2.png" alt="Zählerschränke" loading="lazy" decoding="async" />
            </div>
          </div>
        </div>

        <!-- Banner 2 & 3: Steckdosen und Smarthome (Side by side) -->
        <div class="category-row">
          <div class="category-banner -half-width -steckdosen expandlink">
            <div class="image-banner">
              <div class="viewport">
                <div class="content">

                  <h3 class="title">Steckdosen</h3>
                  <a class="action" href="/elektroinstallation/steckdosen-schalter/">smart wählen</a>
                </div>
                <img class="image" src="{{$_cfg.grafik_url}}images/categories/steckdosen.png" alt="Steckdosen" loading="lazy" decoding="async" />
              </div>
            </div>
          </div>
          <div class="category-banner -half-width -smarthome expandlink">
            <div class="image-banner">
              <div class="viewport">
                <div class="content">
                  <h3 class="title">Smarthome</h3>
                  <a class="action" href="/smarthome/">smart wählen</a>
                </div>
                <img class="image" src="{{$_cfg.grafik_url}}images/categories/smarthome.png" alt="Smarthome" loading="lazy" decoding="async" />
              </div>
            </div>
          </div>
        </div>

        <!-- Banner 4: Waschmaschine (Full width) -->
        <div class="category-banner -full-width -waschmaschine expandlink">
          <div class="image-banner">
            <div class="viewport">
              <div class="content">
                <h3 class="title">Waschmaschine</h3>
                <a class="action" href="/waschmaschinen/">smart wählen</a>
              </div>
              <img class="image" src="{{$_cfg.grafik_url}}images/categories/waschmaschine.png" alt="Waschmaschine" loading="lazy" decoding="async" />
            </div>
          </div>
        </div>

        <!-- Banner 5 & 6: Kaffeegenuss und Kühlen & Gefrieren (Side by side) -->
        <div class="category-row">
          <div class="category-banner -half-width -kaffeegenuss expandlink">
            <div class="image-banner">
              <div class="viewport">
                <div class="content">
                  <h3 class="title">Kaffeegenuss</h3>
                  <a class="action" href="/kaffeegenuss/">smart wählen</a>
                </div>
                <img class="image" src="{{$_cfg.grafik_url}}images/categories/kaffeegenuss.png" alt="Kaffeegenuss" loading="lazy" decoding="async" />
              </div>
            </div>
          </div>
          <div class="category-banner -half-width -kuehlen-gefrieren expandlink">
            <div class="image-banner">
              <div class="viewport">
                <div class="content">
                  <h3 class="title">Kühlen & Gefrieren</h3>
                  <a class="action" href="/kuehlen-gefrieren/">smart wählen</a>
                </div>
                <img class="image" src="{{$_cfg.grafik_url}}images/categories/kuehlen-gefrieren.png" alt="Kühlen & Gefrieren" loading="lazy" decoding="async" />
              </div>
            </div>
          </div>
        </div>

        <!-- Banner 7: Haus, Garten & Camping (Full width) -->
        <div class="category-banner -full-width -haus-garten-camping expandlink">
          <div class="image-banner">
            <div class="viewport">
              <div class="content">
                <h3 class="title">Haus, Garten <br>& Camping</h3>
                <a class="action" href="/garten/">smart wählen</a>
              </div>
              <img class="image" src="{{$_cfg.grafik_url}}images/categories/haus-garten-camping.png" alt="Haus, Garten & Camping" loading="lazy" decoding="async" />
            </div>
          </div>
        </div>

        <!-- Banner 8 & 9: Bräter, Töpfe & Pfannen und Grillen (Side by side) -->
        <div class="category-row">
          <div class="category-banner -half-width -braeter-toepfe-pfannen expandlink">
            <div class="image-banner">
              <div class="viewport">
                <div class="content">
                  <h3 class="title">Bräter, Töpfe <br>& Pfannen</h3>
                  <a class="action" href="/toepfe-pfannen/">smart wählen</a>
                </div>
                <img class="image" src="{{$_cfg.grafik_url}}images/categories/braeter-toepfe-pfannen.png" alt="Bräter, Töpfe & Pfannen" loading="lazy" decoding="async" />
              </div>
            </div>
          </div>
          <div class="category-banner -half-width -grillen expandlink">
            <div class="image-banner">
              <div class="viewport">
                <div class="content">
                  <h3 class="title">Grillen</h3>
                  <a class="action" href="/grillen/">smart wählen</a>
                </div>
                <img class="image" src="{{$_cfg.grafik_url}}images/categories/grillen.png" alt="Grillen" loading="lazy" decoding="async" />
              </div>
            </div>
          </div>
        </div>
      </section>
      <div class="_space-6 _space-desktop-8"></div>
    {{/block}}

    {{block name='home_benefits'}}
       {{include 'components/benefits.tpl'}}

      <div class="_space-6 _space-desktop-8"></div>
    {{/block}}

    {{block name='home_reviews'}}
      <h2 class="section-title">Das sagen unsere Kunden</h2>
      <div class="_space-4 _space-desktop-5"></div>
      <section class="reviews" role="region" aria-label="Kundenbewertungen">
        <div class="panel">
          <a class="_surface review-box trustpilot" href="https://www.trustpilot.com/review/www.allego.de" target="_blank" rel="noopener">
            <img class="logo" src="{{$_cfg.grafik_url}}images/trustpilot-logo.png" alt="Trustpilot Logo" height="40" loading="lazy" decoding="async" fetchpriority="low" />
          </a>
          <a class="_surface review-box google" href="https://www.idealo.de/preisvergleich/Shop/273179.html" target="_blank" rel="noopener">
            <img class="logo" src="{{$_cfg.grafik_url}}images/idealo-logo.png" alt="Idealo Logo" height="40" loading="lazy" decoding="async" fetchpriority="low" />
          </a>
          <div class="reviews-slider">
            <div class="banner reviews-banner slide1">
              <picture>
                <source media="(min-width: 768px)" srcset="{{$_cfg.grafik_url}}images/review-banner-1.png">
                <img class="reviews-image" src="{{$_cfg.grafik_url}}images/review-banner-1-mobile.png" alt="Customer Reviews" decoding="async" />
              </picture>
            </div>
            <div class="banner reviews-banner slide2">
              <picture>
                <source media="(min-width: 768px)" srcset="{{$_cfg.grafik_url}}images/review-banner-2.png">
                <img class="reviews-image" src="{{$_cfg.grafik_url}}images/review-banner-2-mobile.png" alt="Customer Reviews" decoding="async"/>
              </picture>
            </div>
            <div class="banner reviews-banner slide3">
              <picture>
                <source media="(min-width: 768px)" srcset="{{$_cfg.grafik_url}}images/review-banner-3.png">
                <img class="reviews-image" src="{{$_cfg.grafik_url}}images/review-banner-3-mobile.png" alt="Customer Reviews" decoding="async"/>
              </picture>
            </div>
          </div>
        </div>
      </section>
      <div class="_space-6 _space-desktop-8"></div>
    {{/block}}

    {{block name='home_recently_viewed'}}
      {{if isset($recently_viewed_products) && $recently_viewed_products|@count > 0}}
        <h2 class="section-title">Kürzlich angesehen</h2>
        <div class="_space-4 _space-desktop-5"></div>
        <section class="top-products -full-row" role="region" aria-label="Kürzlich angesehene Produkte">
          {{foreach from=$recently_viewed_products item='p'}}
            {{include file='components/product/product-card.tpl' product=$p}}
          {{/foreach}}
        </section>
        <div class="_space-6 _space-desktop-8"></div>
      {{/if}}
    {{/block}}

    {{block name='home_long_seo'}}
      <section class="info-section" aria-label="Smartgoods Onlineshop Textblock">
        <h2><strong>Willkommen bei smartgoods – dein Onlineshop aus Radebeul</strong></h2>
        <p class="text-1">
          Wir sind <strong>smartgoods</strong>, dein Shop aus <strong>Radebeul</strong> für
          <strong>Installationsmaterial</strong>, <strong>Smart-Home-Lösungen</strong> inkl.
          <strong>KNX</strong> sowie <strong>Haushaltsgeräte</strong> wie
          <strong>Waschmaschinen</strong>, <strong>Backöfen</strong> und <strong>Kühlschränke</strong>.
          Unser Unterschied: Wir erklären in eigenen <strong>Videos</strong> die
          <strong>Goodies</strong> jedes Produkts – also genau die Eigenschaften, die es
          <strong>einzigartig</strong> machen. Diese <strong>Goodies sind filter- und suchbar</strong>,
          sodass du <strong>schnell das Gerät</strong> oder <strong>Bauteil</strong> findest, das
          <strong>wirklich</strong> zu deinen Anforderungen passt.
        </p>

        <h2><strong>Elektro-Installationsprodukte – professionell auswählen, passgenau bestellen</strong></h2>

        <h3><strong>Schalter &amp; Steckdosen</strong></h3>
        <p class="text-1">
          Moderne <strong>Schalterprogramme</strong> und <strong>Steckdosen</strong>
          (z. B. mit <strong>USB-A/USB-C</strong>, <strong>Kinderschutz</strong>,
          <strong>Feuchtraum</strong>, <strong>Rahmen-Designs</strong>) für Renovierung und Neubau.
          Mit dem <strong>Goodies-Filter</strong> findest du in Sekunden die passende Funktion, Farbe
          und Schutzart.
        </p>

        <h3><strong>Sicherungskästen &amp; Leitungsschutzschalter (LS-Schalter)</strong></h3>
        <p class="text-1">
          Vom <strong>Sicherungskasten</strong> über <strong>Verteiler</strong> bis zu
          <strong>Leitungsschutzschaltern</strong> und <strong>FI/RCD</strong> – exakt nach
          <strong>Polzahl</strong>, <strong>Bemessungsstrom</strong>, <strong>Charakteristik</strong>
          und <strong>IP-Schutzart</strong> filtern. Unsere <strong>Goodies-Videos</strong> erklären
          Einsatz, Sicherheit und typische Fehlerbilder – praxisnah und verständlich.
        </p>

        <h3><strong>Kabel &amp; Leitungen</strong></h3>
        <p class="text-1">
          Ob <strong>NYM-J</strong>, <strong>H07RN-F</strong>, Steuer- und Datenleitungen –
          filtere nach <strong>Querschnitt</strong>, <strong>Aderzahl</strong>,
          <strong>Mantelmaterial</strong>, <strong>Meterware</strong> oder
          <strong>Trommel</strong>. So planst du Installation und Materialbedarf effizient.
        </p>

        <h2><strong>Smart Home &amp; KNX – zuverlässig skalieren, komfortabel steuern</strong></h2>

        <h3><strong>Smart-Home-Komponenten</strong></h3>
        <p class="text-1">
          Von <strong>Sensoren</strong> und <strong>Aktoren</strong> über
          <strong>Zentralen</strong> bis zu <strong>Heizungs-, Licht- und Rollladensteuerung</strong>.
          Dank Goodies-Filter findest du kompatible Geräte nach <strong>Funkstandard</strong>,
          <strong>Energie-Management</strong>, <strong>Sprachsteuerung</strong> und
          <strong>Szenen</strong>.
        </p>

        <h3><strong>KNX-Systeme</strong></h3>
        <p class="text-1">
          <strong>KNX-Aktoren</strong>, <strong>Taster</strong>, <strong>Gateways</strong> und
          <strong>Netzteile</strong> für anspruchsvolle Gebäudeautomation. Filtere u. a. nach
          <strong>Kanälen</strong>, <strong>Last</strong>, <strong>Topologie</strong> oder
          <strong>Reglerfunktionen</strong> – und sieh dir in den Videos an, welche Goodies dein
          Projekt wirklich nach vorne bringen (z. B. <strong>Logikfunktionen</strong>,
          <strong>Zeitprofile</strong>, <strong>Energie-Monitoring</strong>).
        </p>

        <h2><strong>Haushaltsgeräte – die richtige Wahl auf den ersten Blick</strong></h2>

        <h3><strong>Waschmaschinen</strong></h3>
        <p class="text-1">
          Finde das Modell mit der <strong>richtigen Kapazität</strong>,
          <strong>Energieeffizienz (EU-Label A–G)</strong>, <strong>Programmen</strong> und
          <strong>Lautstärke</strong>. Goodies wie <strong>Dampfprogramme</strong>,
          <strong>Schnellwäsche</strong>, <strong>Fleckenautomatik</strong> oder
          <strong>App-Steuerung</strong> sind direkt filterbar.
        </p>

        <h3><strong>Backöfen</strong></h3>
        <p class="text-1">
          Wähle nach <strong>Beheizungsarten</strong> (Heißluft, Dampf, Grill),
          <strong>Pyrolyse</strong>, <strong>Volumen</strong> und
          <strong>Sicherheitsfunktionen</strong>. Mit Goodies wie
          <strong>Kerntemperaturfühler</strong>, <strong>Teleskopauszüge</strong> oder
          <strong>Automatikprogramme</strong> triffst du die bessere Entscheidung.
        </p>

        <h3><strong>Kühlschränke</strong></h3>
        <p class="text-1">
          Vergleiche <strong>Nutzinhalt</strong>, <strong>No-Frost</strong>,
          <strong>Frischezonen</strong>, <strong>Türanschlag</strong>,
          <strong>Einbau/Freistehend</strong> und <strong>Lautstärke</strong>. Goodies wie
          <strong>Holiday-Modus</strong>, <strong>Door-Alarm</strong> oder
          <strong>Innenkamera</strong> helfen, das passende Gerät zu finden.
        </p>

        <h2><strong>Der Goodie-Finder – die Abkürzung zu deinem perfekten Produkt</strong></h2>
        <ul>
          <li class="text-1">
            <strong>Goodies = einzigartige Produkteigenschaften</strong> – bei smartgoods
            <strong>sichtbar, erklärbar, filterbar</strong>.
          </li>
          <li class="text-1">
            <strong>Video-Erklärungen</strong> zeigen dir <strong>Nutzen &amp; Einsatz</strong> in der Praxis.
          </li>
          <li class="text-1">
            <strong>Multi-Filter</strong> nach Goodies + Basisdaten (z. B. Maße, Leistung, Schutzart)
            sorgt dafür, dass dein Ergebnis <strong>wirklich passt</strong>.
          </li>
        </ul>

        <h2><strong>Beratung &amp; Service aus Radebeul – online stark, persönlich erreichbar</strong></h2>
        <p class="text-1">
          Ob <strong>Elektroinstallation</strong>, <strong>Sanierung</strong> oder
          <strong>Smart-Home-Projekt</strong>: Unser Team unterstützt dich bei Produktauswahl und Planung –
          <strong>verständlich</strong>, <strong>lösungsorientiert</strong> und <strong>neutral</strong>.
        </p>

        <h2><strong>Häufige Fragen (FAQ)</strong></h2>

        <p class="text-1">
          <strong>Wie hilft mir der Goodies-Filter?</strong><br />
          Du siehst <strong>relevante Produkteigenschaften</strong> zuerst – und filterst sie so, dass am Ende
          <strong>genau dein Wunschprodukt</strong> übrig bleibt.
        </p>

        <p class="text-1">
          <strong>Unterscheiden sich Smart-Home-Funk und KNX-Komponenten?</strong><br />
          Ja. <strong>KNX</strong> ist <strong>verdrahtet, herstellerübergreifend</strong> und besonders
          <strong>skalierbar</strong> – ideal für Neubau/Profiprojekte.
          <strong>Smart-Home-Funk</strong> punktet bei <strong>Nachrüstungen</strong>.
          Unser Filter berücksichtigt beide Welten.
        </p>

        <p class="text-1">
          <strong>Kann ich mehrere Goodies kombinieren?</strong><br />
          Ja. Kombiniere z. B. <strong>No-Frost + leise + A-Label</strong> (Kühlschrank) oder
          <strong>Charakteristik B + 16 A + 1-polig</strong> (LS-Schalter) – der Shop zeigt dir
          <strong>präzise Treffer</strong>.
        </p>

        <p class="text-1">
          <strong>Bietet ihr Video-Erklärungen zu allen Kategorien?</strong><br />
          Unser Ziel ist eine wachsende Bibliothek. Wir beginnen mit <strong>Kernkategorien</strong> und
          erweitern laufend – so bleibt dein Einkauf <strong>kompetent &amp; transparent</strong>.
        </p>
      </section>
    {{/block}}

  </div>
{{/block}}
