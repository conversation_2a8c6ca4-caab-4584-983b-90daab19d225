<!DOCTYPE html>
<html lang="de-DE">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>{{$page_title|default:'smartgoods'}}</title>
  <meta name="description" content="{{$meta_description}}">
  {{if isset($canonical_url)}}<link rel="canonical" href="{{$canonical_url}}">{{/if}}
  {{if !empty($pagination_seo.prev)}}<link rel="prev" href="{{$pagination_seo.prev}}">{{/if}}
  {{if !empty($pagination_seo.next)}}<link rel="next" href="{{$pagination_seo.next}}">{{/if}}
  {{if isset($bot_controll)}}<meta name="robots" content="{{$bot_controll}}">{{/if}}

  <link rel="preload" href="{{$_cfg.grafik_url}}dist.js?rev={{$rev}}" as="script">

  <link rel="icon" href="/favicon.ico" sizes="any">
  <!-- optional -->
  <!-- <link rel="icon" type="image/svg+xml" href="/favicon.svg"> -->
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" sizes="180x180">
  <meta name="msapplication-config" content="none"> <!-- optional -->

  {{include file="components/tracking/head.tpl"}}

  <link rel="stylesheet" href="{{$_cfg.grafik_url}}m.css?rev={{$rev}}">
  <link rel="preload" href="/assets/images/goodie-smiley-white.svg" as="image" type="image/svg+xml">

  {{if $profile->getAdditionalCss()}}
    <style>{{$profile->getAdditionalCss() nofilter}}</style>
  {{/if}}
  {{$additional_headers nofilter}}

  <script>var checkout_init = [];</script>

  {{if !empty($meta_title)}}<meta property="og:title" content="{{$meta_title}}">{{/if}}
  <meta property="og:description" content="{{$meta_description}}">
  {{if isset($canonical_url)}}<meta property="og:url" content="{{$canonical_url}}">{{/if}}
  {{if isset($og_image)}}<meta property="og:image" content="{{$og_image}}">{{/if}}
  <meta name="twitter:card" content="summary_large_image">

  {{* Speculation Rules: prefetch broadly, prerender high-intent pages *}}
  {{* unklar... gezielt wäre vielleicht besser. z.B. produkte etc. teilweise haben wir links auch überladen.. *}}
  {{* wir verwenden nicht clean über all ddie richtigen HTTP verben... im Warenkorb gibt es ein löschen was über ein <a href=""> läuft... geblacklisted über "item=" und "do=" *}}
  <script type="speculationrules">
  {
    "prefetch": [
      {
        "eagerness": "moderate",
        "where": {
          "and": [
            { "selector_matches": "a[href^='/'], a[href^='https://www.smartgoods.de/'" },
            { "not": { "selector_matches": "a[download], a[target='_blank'], a[href$='.pdf'], a[href^='/media/'], a[href*='item='], a[href*='do=']" } }
          ]
        }
      }
    ],
    "prerender": [
      {
        "eagerness": "conservative",
        "where": { "href_matches": [
          "/checkout/*",
          "/warenkorb/*"
        ] }
      }
    ]
  }
  </script>
 </head>
 <body>
 <a id="top_bar" class="visually-hidden" tabindex="-1" aria-hidden="true"></a>
 {{include file="components/tracking/body-top.tpl"}}
