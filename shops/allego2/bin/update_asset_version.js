#!/usr/bin/env node

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

const assetVersionFile = path.resolve(__dirname, '..', 'config', 'asset_version.php');
const assetVersion = crypto.randomBytes(3).toString('hex');
const fileContents = `<?php\nreturn '${assetVersion}';\n`;

fs.writeFileSync(assetVersionFile, fileContents, 'utf8');
console.log(`Updated asset version: ${assetVersion}`);
