{"name": "allego.de", "version": "1.0.0", "private": true, "main": "webpack.config.js", "scripts": {"start": "webpack --config webpack.dev.js", "build": "webpack -p --config webpack.prod.js && node ./bin/update_asset_version.js", "watch": "webpack --config webpack.dev.js --watch"}, "devDependencies": {"acorn": "^6.4.0", "autoprefixer": "^9.7.4", "css-loader": "^2.1.1", "file-loader": "^3.0.1", "mini-css-extract-plugin": "^0.5.0", "node-sass": "^4.13.1", "postcss-loader": "^3.0.0", "sass": "^1.93.2", "sass-loader": "^7.3.1", "script-loader": "^0.7.0", "static-eval": "^2.0.3", "style-loader": "^0.23.1", "terser-webpack-plugin": "^1.4.3", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-merge": "^4.2.2", "webpack-spritesmith": "^1.1.0"}, "dependencies": {"@fancyapps/ui": "^6.0.34", "jquery": "^3.6.0"}}