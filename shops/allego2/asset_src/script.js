/* jshint esversion: 8 */

import $ from "./scripts/jquery-global.js";
import checkout from "./scripts/features/checkout.js";
import PutInBasket from "./scripts/features/put-in-basket.js";
import ToastNotification from "./scripts/ui/toast-notification.js";
import RecentlyViewed from "./scripts/features/recently-viewed.js";
import Paypal from "./scripts/features/paypal.js";
import financing from "./scripts/features/financing.js";
import AjaxLoader from "./scripts/ui/ajax-loader.js";
import "./scripts/features/header/header-menu.js";
import "./scripts/features/header/header-search.js";
import "./scripts/features/category/listing-filter.js";
import "./scripts/features/category/product-set-preview.js";
import CategoryGrid from "./scripts/features/category/category-grid.js";
import "./scripts/features/goodies.js";
import "./scripts/features/service.js";

import "./scripts/features/product/product-detail.js";
import "./scripts/features/product/product-jump.js";
import "./scripts/features/product/product-ratings.js";
import "./scripts/features/product/product-loadbee.js";
import "./scripts/features/product/picture-viewer.js";
import "./scripts/features/product/product-extended-shipment.js";
import "./scripts/features/product/device-list.js";
import ProductTabs from "./scripts/features/product/product-tabs.js";
import ExpandTriggers from "./scripts/ui/expand-triggers.js";
//import ProductVariations from "./scripts/features/product/product-variations.js";
import "./scripts/ui/expandlink.js";

import "./scripts/ui/accessibility.js";
import initToTopButton from "./scripts/ui/to-top-button.js";
import Cart from "./scripts/features/cart-crosssell.js";
import Modal from "./scripts/features/modal";

// js error logging
window.onerror = function (message_raw, url, line) {
    const message = String(message_raw || '');

    // spezifische Fehler ignorieren

    // Safari/iCloud autofill
    if (message.includes('_AutofillCallbackHandler')) {
        return;
    }

    try {
        const params = new URLSearchParams({
            message: message,
            url: String(url || ''),
            line: String(line || '')
        }).toString();

        fetch('/api/js.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
            body: params
        });
    } catch (e) {
    }
};

//@todo kann wieder raus!
if (window.location && window.location.hash === '#terror') {
    throw new Error('URL anchor "#error" detected');
}
//

window.RecentlyViewed = RecentlyViewed;

//@todo unklar
if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
    document.addEventListener('touchstart', function() {}, { passive: true });
}

// remove tracking parameter from URL
const url = new URL(window.location.href);
if (url.searchParams.has("t")) {
    url.searchParams.delete("t");
    window.history.replaceState({}, "", url.toString());
}

//init
const putInBasket = new PutInBasket();
const cart = new Cart();
const paypalWidget = new Paypal();
const modal = new Modal();

document.addEventListener("putInBasket:success", () => {
   putInBasket.init();
});

document.addEventListener('AjaxLoader:loaded', () => {
    paypalWidget.init();
    putInBasket.init();
    cart.init();
    modal.init();
});

$(document).ready(function() {
    paypalWidget.init();
    putInBasket.init();
    cart.init();
    ToastNotification.init();
    financing.init();
    const categoryGrid = new CategoryGrid();
    categoryGrid.init();
    const expandTriggers = new ExpandTriggers();
    expandTriggers.init();
    expandTriggers.refresh();
    const productTabs = new ProductTabs();
    productTabs.init();
    //ProductVariations.initFromGlobal();
    modal.init();

    initToTopButton(document.getElementById('toTopButton'), {forceSmooth: true});
});

checkout.init(window.checkout_init);

const ajaxLoader = new AjaxLoader();
ajaxLoader.init();
window.ajaxLoader = ajaxLoader;
