/* jshint esversion: 8 */
import LoadingAnimation from './loading-animation.js';
import ToastNotification from "./toast-notification";

export default class AjaxLoader
{
    constructor()
    {
        this.boundLinks = new WeakSet();

        this.handleClick = this.handleClick.bind(this);
        this.onPopState = this.onPopState.bind(this);
        this.onDomContentLoaded = this.onDomContentLoaded.bind(this);
    }

    init()
    {
        window.addEventListener('popstate', this.onPopState);
        document.addEventListener('DOMContentLoaded', this.onDomContentLoaded, { once: true });
    }

    onDomContentLoaded()
    {
        this.bindLinks(document);
    }

    bindLinks(root)
    {
        if (!root || typeof root.querySelectorAll !== 'function') {
            return;
        }

        const loaderElements = [];

        if (root.nodeType === Node.ELEMENT_NODE && root.hasAttribute('data-loader-target')) {
            loaderElements.push(root);
        }

        root.querySelectorAll('[data-loader-target]').forEach((element) => {
            if (!loaderElements.includes(element)) {
                loaderElements.push(element);
            }
        });

        loaderElements.forEach((element) => this.processLoaderElement(element));
    }

    processLoaderElement(element)
    {
        const targetId = element.dataset ? element.dataset.loaderTarget : null;

        if (!targetId) {
            return;
        }

        if (element.tagName && element.tagName.toLowerCase() === 'a') {
            this.bindLink(element, targetId);

            return;
        }

        element.querySelectorAll('a[href]').forEach((anchor) => {
            const anchorTarget = anchor.dataset.loaderTarget || targetId;
            this.bindLink(anchor, anchorTarget);
        });
    }

    bindLink(anchor, targetId)
    {
        if (!targetId || this.boundLinks.has(anchor)) {
            return;
        }

        if (!anchor.dataset.loaderTarget) {
            anchor.dataset.loaderTarget = targetId;
        }

        anchor.addEventListener('click', this.handleClick);
        this.boundLinks.add(anchor);
    }

    handleClick(event)
    {
        const anchor = event.currentTarget;
        const rawUrl = anchor.getAttribute('href');

        if (!rawUrl) {
            return;
        }

        const loweredUrl = rawUrl.toLowerCase();

        if (loweredUrl.startsWith('#') || loweredUrl.startsWith('javascript:')) {
            return;
        }

        const targetId = anchor.dataset.loaderTarget;

        if (!targetId) {
            return;
        }

        const targetElement = document.getElementById(targetId);

        if (!targetElement) {
            return;
        }

        let resolvedUrl = null;

        try {
            resolvedUrl = new URL(rawUrl, window.location.href);
        } catch (error) {
            return;
        }

        event.preventDefault();

        this.loadContent(resolvedUrl, targetElement);
        this.scrollToTarget(targetElement);
    }

    loadContent(urlObject, targetElement)
    {
        LoadingAnimation.show(targetElement);

        const url = urlObject.toString();

        window.history.pushState(
            {
                ajaxLoader: true,
                target: targetElement.id,
                url: url
            },
            '',
            url
        );

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

            xhr.onload = () => {
                targetElement.innerHTML = xhr.responseText;
                this.bindLinks(targetElement);

                const element = targetElement.querySelector('script[type="application/json"]#toast-message');
                if(element) {
                    const data = JSON.parse(element.innerText);
                    ToastNotification.show(data.status, data.message);
                }

                document.dispatchEvent(new CustomEvent('AjaxLoader:loaded', {
                    detail: {
                        target: targetElement,
                        url: url
                    },
                    bubbles: true
                }));
                resolve();
            };

            xhr.onerror = () => {
                //@todo
                //window.location.reload(); //ist das schlau?
                targetElement.textContent = 'Error loading content';
                this.scrollToTarget(targetElement);
                reject();
            };

            xhr.send();
        });
    }

    onPopState()
    {
        // wenn der user "zurück" im browser nutzt, dann haben wir in window.location die vorherige url -> mit reload() machen wir ein normalen request draus
        window.location.reload();
    }

    scrollToTarget(targetElement)
    {
        if (!targetElement || typeof targetElement.scrollIntoView !== 'function') {
            return;
        }

        try {
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
        } catch (error) {
            targetElement.scrollIntoView(true);
        }
    }
}
