import jQuery from "jquery";

const $ = jQuery;

const modalWindow = function(config) {
	var self = this;

	if(!config) {
		config = {};
	}

	if(!config.hasOwnProperty('close_button')) {
		config.close_button = true;
	}

	if(!config.hasOwnProperty('margin_scroller')) {
		config.margin_scroller = 20;
	}

	if(!config.hasOwnProperty('height')) {
		config.height = 'fixed';
	}
	
	if(!config.hasOwnProperty('classname')) {
		config.classname = '';
	}

	this.config = config;
	this.id = config.id;
	this.inner = null;
	this.outer = null;
	this.content = $('<div/>', {
		'class': 'window_content'
	});

	this.content.on('click', '.window_close', jQuery.proxy(this.close, this));
	$( window ).resize(jQuery.proxy(this.resize, this));
}

modalWindow.prototype.show = function() {
	this.outer = $('<div/>', {
		'class': 'modalWindow',
		'id': this.id
	});

	this.inner = $('<div/>', {
		'class': 'modalWindowInner '+this.config.classname
	});

	//this.inner.on('dblclick', jQuery.proxy(this.close, this));
	this.outer.on('click', jQuery.proxy(this.close, this));

	this.scroller = $('<div/>', {
		'class': 'modalWindowInner_scroller'
	});

	this.inner.append(this.scroller);

	this.scroller.append(this.content);

	$('body').append(this.outer);
	$('body').append(this.inner);

	this.doPosition();

	if(this.config.close_button) {
		var link = $('<a/>', {
			class: 'window_close',
			href: '#',
		});

		var close_button = $('<img/>', {
			src: '/assets/close2.png'
		});

		link.append(close_button);

		link.click(jQuery.proxy(this.close, this));

		this.inner.append(link);
	}

	/*
	if(this.config.close_button) {
		var close_button = $('<i class="fa fa-times window_close"></i>');

		close_button.click(jQuery.proxy(this.close, this));

		this.inner.append(close_button);
	}*/
}

modalWindow.prototype.doPosition = function() {
	var size = {
		height: this.inner.height(),
		width: this.inner.width()
	};

	var is_mobile = window.innerWidth < 800;

	if(is_mobile) {
		this.inner.css('left', 0);
		this.inner.css('top', 0);
		this.inner.css('width', '100%');
		this.inner.css('height', '100%');
		this.inner.css('overflow', 'auto');
	} else {
		if(this.config.height == 'fixed') {
			var top = ((window.innerHeight - size.height) >> 1);

			if(top < 0) {
				this.inner.css('height', (window.innerHeight - 10) + 'px');
				top = 5;
			}

			this.scroller.css('height', (this.inner.height()-(this.config.margin_scroller*2)) + 'px');
			this.inner.css('top', top + 'px');
		} else {
			this.inner.css('top', '50px');
		}

		var left = (window.innerWidth - size.width) >> 1;
		if(left < 0) {
			left = 0;
			this.inner.css('width', '100%');
		}

		this.inner.css('left', left + 'px');
	}
}


modalWindow.prototype.resize = function() {
	this.doPosition();
}

modalWindow.prototype.close = function(Event) {
	Event.preventDefault();
	Event.stopPropagation();

	this.outer.remove();
	this.inner.remove();
}

modalWindow.prototype.html = function(html) {
	this.content.html(html);
}


modalWindow.prototype.ajaxShow = function(url) {
	this.show();
	this.content.load(url);
}

modalWindow.prototype.setClass = function(classname) {
	this.config.classname = classname;
}

document.addEventListener('keydown', function(event) {
	if (event.key === 'Tab') {
		const modalWindow = $(".modalWindowInner").first();
		if(modalWindow) {
			setTimeout(function() {
				const focusedElement = $(":focus").first();
				console.log(focusedElement)
				if (!modalWindow.has(focusedElement).length) {
					modalWindow.children().find(":focusable").first().focus();
				}
			}, 100);
		}
	}
});


export default modalWindow;
