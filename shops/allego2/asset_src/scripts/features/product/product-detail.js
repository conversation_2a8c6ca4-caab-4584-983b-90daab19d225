import {initAccordion} from "../../ui/accordion.js";
import ModalWindow from "../../ui/modal-window.js";
import {onReady} from "../../utils/ready.js";
import {isMobile as isMobileView} from "../../utils/responsive.js";
import ejs from "../../utils/ejs.js";

(function() {
  var gpsrHandlerBound = false;
  onReady(init);

  function init() {
    initAccordion('.product-details-nav', '.detail-nav-item');
    initRelatedShowAll();
    initExtraService();
    initGallerySimple();
    trackRecentlyViewed();
    initStickyCTA();
    initServiceFormQuantitySync();
    initGpsrHandler();
  }

  function initExtraService() {
    const root = document.querySelector('.page-product');
    if(!root) {
      return;
    }

    const service_price_service = root.querySelector("#service_price_service");
    const service_price_product = root.querySelector("#service_price_product");
    const service_price_total = root.querySelector("#service_price_total");
    const service_boxes = root.querySelectorAll('.extra-service-checkbox');

    const sticky_cta = root.querySelector(".product-sticky-cta");

    service_boxes.forEach(element => {
      element.addEventListener("change", event => {
        const extra_service_id = element.getAttribute('data-extra-service-id');

        function checkRecursive(extra_service_id) {
          extra_service_id = parseInt(extra_service_id);
          service_boxes.forEach(subelement => {
            const dependencies = JSON.parse(subelement.getAttribute('data-dependencies'));
            if (dependencies.indexOf(extra_service_id) !== -1) {
              const subelement_extra_service_id = subelement.getAttribute('data-extra-service-id');

              if (!event.target.checked) {
                subelement.checked = false;
                checkRecursive(subelement_extra_service_id);
                subelement.parentElement.parentElement.classList.add('-blocked');
              } else {
                subelement.parentElement.parentElement.classList.remove('-blocked');
              }

              subelement.disabled = !event.target.checked;
            }
          });
        }

        checkRecursive(extra_service_id);

        let total = 0;
        service_boxes.forEach(element => {
          if (element.checked) {
            total += parseFloat(element.getAttribute('data-price')) || 0;
          }

          // update sticky
          if(sticky_cta) {
            const extra_service_id = element.getAttribute("data-extra-service-id");
            const sticky_service = sticky_cta.querySelector('input[name="service[' + extra_service_id + ']"]');
            sticky_service.value = element.checked ? "1" : "0";
          }
        });

        service_price_service.innerText = ejs.output.formatPrice(total) + "*";
        service_price_total.innerText = ejs.output.formatPrice(parseFloat(service_price_product.getAttribute('data-price')) + total)  + "*";

        // update sticky
        if(sticky_cta) {
          sticky_cta.querySelector(".price .amount").innerText = service_price_total.innerText;
        }
      });
    });
  }

  function trackRecentlyViewed() {
    const productPage = document.querySelector('.page-product');
    if (!productPage) return;

    const productId = productPage.getAttribute('data-product-id');
    if (!productId) return;

    function doTrack() {
      if (window.RecentlyViewed) {
        window.RecentlyViewed.track(productId);
      } else {
        setTimeout(doTrack, 100);
      }
    }
    doTrack();
  }

  function initGpsrHandler() {
    if (gpsrHandlerBound) return;
    if (!document.querySelector('.page-product')) return;

    gpsrHandlerBound = true;
    document.addEventListener('click', onGpsrClick, true);
  }

  function onGpsrClick(event) {
    var link = event.target && event.target.closest('a.gpsr[data-gpsr]');
    if (!link) return;

    event.preventDefault();
    var href = link.getAttribute('data-gpsr');
    if (href) {
      openGpsrModal(href);
    }
  }

  function openGpsrModal(url) {
    var gpsrModal = new ModalWindow();
    gpsrModal.setClass('modalWindowSmall modalWindowInner');
    gpsrModal.ajaxShow(url);
  }

  function initGallerySimple() {
    var media = document.querySelector('.product-box > .media');
    if (!media) return;
    var hero = media.querySelector('.hero');
    var photo = media.querySelector('.photo');
    var thumbs = Array.prototype.slice.call(media.querySelectorAll('.thumbs .thumb'));
    var dots = media.querySelectorAll('.dots .dot');
    if (!hero || !photo || !thumbs.length) return;

    function toPreview(url) {
      if (/_highres\./i.test(url))   return url.replace(/_highres\./i,   '_nor.');
      if (/_50x50\./i.test(url))   return url.replace(/_50x50\./i,   '_nor.');
      return url;
    }

    var slides = thumbs.map(function(a){
      var high = a.getAttribute('href') || a.href || '';
      var prev = toPreview(high);
      return { high: high, prev: prev, el: a };
    });

    function setActive(i){
      thumbs.forEach(function(t, idx){ t.setAttribute('aria-current', idx === i ? 'true' : 'false'); });
      dots.forEach(function(t, idx){ t.setAttribute('aria-current', idx === i ? 'true' : 'false'); });
    }

    function show(i) {
      if (!slides.length) return;
      var ni = (i + slides.length) % slides.length;
      var s = slides[ni];
      if (s.prev) photo.setAttribute('src', s.prev);
      if (s.high) hero.setAttribute('href', s.high);
      setActive(ni);
    }

    thumbs.forEach(function(a, i){
      a.addEventListener('mouseenter', function(){ show(i); }, { passive: true });
      a.addEventListener('mouseover', function(){ show(i); }, { passive: true });
    });

    dots.forEach(function(t, idx) {
      t.addEventListener("click", function() { show(idx); });
    });

      // Swipe functionality - only use pointer events (modern, works on all devices)
    // Removed duplicate touch handlers that were causing double-tap issues on iOS Chrome
    var startX = 0, dx = 0, active = false, startTime = 0;
    // viewport helper
    function currentIndex(){
      var src = photo.currentSrc || photo.src || '';
      var name = src.split('/').pop();
      var idx = slides.findIndex(function(s){ return s.prev.split('/').pop() === name; });
      return idx >= 0 ? idx : 0;
    }
    
    function onDown(e) {
      if (!isMobileView()) return;
      active = true;
      dx = 0;
      startX = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : 0);
      startTime = Date.now();
    }
    
    function onMove(e) {
      if (!active) return;
      var x = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : 0);
      dx = x - startX;
    }
    
    function onUp(e) {
      if (!active) return;
      active = false;
      var elapsed = Date.now() - startTime;
      var i = currentIndex();
      // Only count as swipe if moved > 30px and was a quick gesture (< 300ms)
      // This prevents accidental swipes when trying to tap the link
      if (Math.abs(dx) >= 30 && elapsed < 300) {
        e.preventDefault(); // Only prevent default when actually swiping
        show(dx < 0 ? i + 1 : i - 1);
      }
    }

    // Use only pointer events to avoid duplicate event firing on iOS
    // Pointer events work on all modern browsers and handle touch automatically
    var tgt = hero;
    if (false && window.PointerEvent) {
      // PointerEvent API fires pointercancel when the browser thinks that there are no more pointer events
      // https://stackoverflow.com/questions/66430313/stop-pointercancel-event-from-firing-without-disabling-touch-scrolling-on-chrome
      tgt.addEventListener('pointerdown', onDown, { passive: true });
      tgt.addEventListener('pointermove', onMove, { passive: true });
      tgt.addEventListener('pointerup', onUp, { passive: false }); // Non-passive so we can preventDefault on swipe
      tgt.addEventListener('pointercancel', function() { active = false; }, { passive: true });
    } else {
      // Fallback for older browsers (unlikely on iOS)
      tgt.addEventListener('touchstart', onDown, { passive: true });
      tgt.addEventListener('touchmove', onMove, { passive: true });
      tgt.addEventListener('touchend', onUp, { passive: false });
      tgt.addEventListener('touchcancel', function() { active = false; }, { passive: true });
    }
  }

  function initStickyCTA() {
    var stickyCTA = document.querySelector('.product-sticky-cta');
    if (!stickyCTA) return;

    var mainActions = document.querySelector('.product-box > .actions, .product-box > .details > .actions');
    if (!mainActions) {
      mainActions = document.querySelector('.page-product .std_form.put_in_basket');
    }
    if (!mainActions) return;

    var scrollThreshold = 1080;

    function handleScroll() {
      if (window.pageYOffset > scrollThreshold) {
        stickyCTA.classList.add('show');
      } else {
        stickyCTA.classList.remove('show');
      }
    }

    var ticking = false;
    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(handleScroll);
        ticking = true;
      }
    }

    function onScroll() {
      ticking = false;
      requestTick();
    }

    window.addEventListener('scroll', onScroll, { passive: true });

    var mainQtyInput = mainActions.querySelector('input[name="menge"]');
    var stickyQtyInput = stickyCTA.querySelector('input[name="menge"]');
    if (!mainQtyInput || !stickyQtyInput) return;

    var mainDecBtn = mainActions.querySelector('.qty .button:first-child');
    var mainIncBtn = mainActions.querySelector('.qty .button:last-child');
    var stickyDecBtn = stickyCTA.querySelector('.qty .button:first-child');
    var stickyIncBtn = stickyCTA.querySelector('.qty .button:last-child');

    function syncQty(source, target) {
      if (!source || !target) return;
      var val = parseInt(source.value, 10);
      if (!isNaN(val) && val >= 1) {
        target.value = val;
      }
    }

    if (mainQtyInput && stickyQtyInput) {
      mainQtyInput.addEventListener('input', function() {
        syncQty(mainQtyInput, stickyQtyInput);
      });
      mainQtyInput.addEventListener('change', function() {
        syncQty(mainQtyInput, stickyQtyInput);
      });

      stickyQtyInput.addEventListener('input', function() {
        syncQty(stickyQtyInput, mainQtyInput);
      });
      stickyQtyInput.addEventListener('change', function() {
        syncQty(stickyQtyInput, mainQtyInput);
      });
    }

    if (mainDecBtn && stickyQtyInput) {
      mainDecBtn.addEventListener('click', function() {
        setTimeout(function() { syncQty(mainQtyInput, stickyQtyInput); }, 10);
      });
    }
    if (mainIncBtn && stickyQtyInput) {
      mainIncBtn.addEventListener('click', function() {
        setTimeout(function() { syncQty(mainQtyInput, stickyQtyInput); }, 10);
      });
    }
    if (stickyDecBtn && mainQtyInput) {
      stickyDecBtn.addEventListener('click', function() {
        setTimeout(function() { syncQty(stickyQtyInput, mainQtyInput); }, 10);
      });
    }
    if (stickyIncBtn && mainQtyInput) {
      stickyIncBtn.addEventListener('click', function() {
        setTimeout(function() { syncQty(stickyQtyInput, mainQtyInput); }, 10);
      });
    }
  }


  function initServiceFormQuantitySync() {
    var serviceInput = document.getElementById('service_quantity');
    if (!serviceInput) return;

    var cartQty = parseInt(serviceInput.getAttribute('data-cart-qty')) || 0;
    if (cartQty > 0) return;

    var mainForm = document.querySelector('.product-box .std_form.put_in_basket');
    if (!mainForm) return;

    var mainInput = mainForm.querySelector('input[name="menge"]');
    if (!mainInput) return;

    var decreaseBtn = mainForm.querySelector('.qty .button:first-child');
    var increaseBtn = mainForm.querySelector('.qty .button:last-child');

    function sync() {
      var qty = parseInt(mainInput.value);
      if (qty && qty >= 1) {
        serviceInput.value = qty;
      }
    }

    mainInput.addEventListener('input', sync);
    mainInput.addEventListener('change', sync);
    
    if (decreaseBtn) {
      decreaseBtn.addEventListener('click', function() {
        setTimeout(sync, 10);
      });
    }
    
    if (increaseBtn) {
      increaseBtn.addEventListener('click', function() {
        setTimeout(sync, 10);
      });
    }
  }

  function initRelatedShowAll() {
    var section = document.getElementById('section-related');
    if (!section) return;

    var btn = section.querySelector('#related-show-all');
    if (!btn) return;

    btn.addEventListener('click', function () {
      section.classList.add('is-expanded');
      btn.setAttribute('aria-expanded', 'true');
      if (btn.parentNode) {
        btn.parentNode.removeChild(btn);
      }
    });
  }
})();
