class ProductTabs {
  constructor() {
    this.inited = new WeakSet();
  }

  init() {
    var self = this;
    document.querySelectorAll('.product-tabs').forEach(function (root) {
      self.enhance(root);
    });
  }

  enhance(root) {
    if (this.inited.has(root)) return;
    this.inited.add(root);

    var nav = root.querySelector('.nav');
    if (!nav) return;

    var tabs = Array.prototype.slice.call(nav.querySelectorAll('.tab'));
    var panels = Array.prototype.slice.call(root.querySelectorAll('.content > .panel'));
    if (!tabs.length || !panels.length) return;

    nav.setAttribute('role', nav.getAttribute('role') || 'tablist');

    tabs.forEach(function (tab) {
      tab.setAttribute('role', tab.getAttribute('role') || 'tab');
    });

    panels.forEach(function (panel) {
      panel.setAttribute('role', panel.getAttribute('role') || 'tabpanel');
    });

    var setActive = function (name) {
      if (!name) return;

      tabs.forEach(function (tab) {
        var isActive = tab.getAttribute('data-tab') === name;
        tab.classList.toggle('-active', isActive);
        tab.setAttribute('aria-selected', isActive ? 'true' : 'false');
        tab.setAttribute('tabindex', isActive ? '0' : '-1');
      });

      panels.forEach(function (panel) {
        var isActive = panel.getAttribute('data-panel') === name;
        panel.classList.toggle('-active', isActive);
        if (isActive) {
          panel.removeAttribute('hidden');
        } else {
          panel.setAttribute('hidden', '');
        }
      });
    };

    var focusTabAt = function (index) {
      if (index < 0 || index >= tabs.length) return;
      var target = tabs[index];
      if (target) {
        target.focus();
      }
    };

    var focusTabByOffset = function (currentIndex, offset) {
      var targetIndex = (currentIndex + offset + tabs.length) % tabs.length;
      focusTabAt(targetIndex);
      return targetIndex;
    };

    tabs.forEach(function (tab, index) {
      tab.addEventListener('click', function () {
        var name = tab.getAttribute('data-tab');
        if (!name) return;
        if (tab.classList.contains('-active')) return;
        setActive(name);

        // Mobile: Scroll to content after tab activation
        if (window.innerWidth < 1024) {
          setTimeout(function() {
            var contentContainer = root.querySelector('.content');
            if (contentContainer) {
              var rect = contentContainer.getBoundingClientRect();
              var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
              var targetPosition = rect.top + scrollTop - 100;
              window.scrollTo({
                top: Math.max(0, targetPosition),
                behavior: 'smooth'
              });
            }
          }, 100);
        }
      });

      tab.addEventListener('keydown', function (event) {
        switch (event.key) {
          case 'ArrowRight':
          case 'ArrowDown':
            event.preventDefault();
            var nextIndex = focusTabByOffset(index, 1);
            if (tabs[nextIndex]) {
              setActive(tabs[nextIndex].getAttribute('data-tab'));
            }
            break;
          case 'ArrowLeft':
          case 'ArrowUp':
            event.preventDefault();
            var prevIndex = focusTabByOffset(index, -1);
            if (tabs[prevIndex]) {
              setActive(tabs[prevIndex].getAttribute('data-tab'));
            }
            break;
          case 'Home':
            event.preventDefault();
            focusTabAt(0);
            if (tabs[0]) {
              setActive(tabs[0].getAttribute('data-tab'));
            }
            break;
          case 'End':
            event.preventDefault();
            var lastIndex = tabs.length - 1;
            focusTabAt(lastIndex);
            if (tabs[lastIndex]) {
              setActive(tabs[lastIndex].getAttribute('data-tab'));
            }
            break;
          case ' ':
          case 'Space':
          case 'Spacebar':
          case 'Enter':
            event.preventDefault();
            setActive(tab.getAttribute('data-tab'));
            break;
        }
      });
    });

    var initial = null;
    for (var i = 0; i < tabs.length; i += 1) {
      if (tabs[i].classList.contains('-active')) {
        initial = tabs[i];
        break;
      }
    }

    if (!initial) {
      initial = tabs[0];
    }

    if (initial) {
      setActive(initial.getAttribute('data-tab'));
    }
  }
}

export default ProductTabs;
