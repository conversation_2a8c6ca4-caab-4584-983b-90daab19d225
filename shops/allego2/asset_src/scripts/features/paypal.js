/* jshint esversion: 8 */
import {isMobile} from '../utils/responsive.js';

export default class Paypal
{
    constructor()
    {
    }

    init()
    {
        let element = document.getElementById("paypal-buttons-product");

        if (element) {
            this.loadPayPalButtonForProduct(element);
        }

        element = document.getElementById("paypal-buttons-basket");
        if (element) {
            this.loadPayPalButtonForBasket(element);
        }

        element = document.getElementById("step3_paypal");
        if (element) {
            this.loadPayPalButtonForCheckout(element);
        }
    }

    async loadPayPalButtonForProduct(container)
    {
        const clientId = container.getAttribute("data-client-id");
        const paypal = await this.loadPaypalSdk(clientId);

        const basketElement = document.getElementById('put_in_basket');
        if (!basketElement) {
            return;
        }

        const productId = basketElement.querySelector('input[name="add_item"]').value;
        const quantityElement = basketElement.querySelector('input[name="menge"]');

        const paypalButtons = paypal.Buttons({
            style: {
                layout: isMobile() ? 'vertical' : 'horizontal',
                //layout: "horizontal",
                //layout: "vertical",
                color: "gold",
                label: "paypal",
                borderRadius: 16,
                disableMaxWidth: true,
                height: 55,
                tagline: false,
            },
            async createOrder()
            {
                //@todo wie mit fehler umgehen?
                const response = await fetch("/api/paypal/product/", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                    body: `product_id=${encodeURIComponent(productId)}&quantity=${encodeURIComponent(quantityElement.value)}`,
                });

                const orderData = await response.json();

                return orderData.id;
            },
            async onApprove(data)
            {
                window.location.href = `/kasse/?do=paypal_direct&token=${data.orderID}`;
            }
        });

        paypalButtons.render(container);
    }


    async loadPayPalButtonForBasket(container)
    {
        const clientId = container.getAttribute("data-client-id");
        const paypal = await this.loadPaypalSdk(clientId);

        const paypalButtons = paypal.Buttons({
            style: {
                layout: isMobile() ? 'vertical' : 'horizontal',
                color: "gold",
                label: "paypal",
                borderRadius: 16,
                disableMaxWidth: true,
                height: 55,
                tagline: false,
            },
            async createOrder()
            {
                //@todo wie mit fehler umgehen?
                const response = await fetch("/api/paypal/basket/", {
                    method: "POST",
                });

                const orderData = await response.json();

                return orderData.id;
            },
            async onApprove(data)
            {
                //@ŧodo hier heißt das ding orderID!=
                window.location.href = `/kasse/?do=paypal_direct&token=${data.orderID}`;
            }
        });

        paypalButtons.render(container);
    }


    async loadPayPalButtonForCheckout(container)
    {
        const clientId = container.getAttribute("data-client-id");

        const paypal = await this.loadPaypalSdk(clientId);

        try {
            container.style.width = '100%';
            container.style.maxWidth = '100%';
            container.style.minWidth = '0';
            container.style.display = 'block';
        } catch (_) {}

        const paypalButtons = paypal.Buttons({
            style: {
                layout: "vertical",
                color: "gold",
                label: "paypal",
                borderRadius: 16,
                disableMaxWidth: true,
                height: 55,
            },
            async createOrder()
            {
                //@todo wie mit fehler umgehen?
                const response = await fetch("/api/paypal/checkout/", {
                    method: "POST",
                });

                const orderData = await response.json();

                return orderData.id;
            },
            async onApprove(data)
            {
                window.location.href = `/kasse/?do=paypal&token=${data.orderID}`;
            }
        });

        paypalButtons.render(container);

        const radios = document.querySelectorAll('input[name="zahlungsart"]');
        radios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.switchButtons();
            });
        });
        this.switchButtons();
    }

    switchButtons()
    {
        const selected = document.querySelector('input[name="zahlungsart"]:checked');
        const value = selected ? selected.value : null;

        const isPaypal = value == 15;

        document.getElementById("step3_paypal").style.display = isPaypal ? 'block' : 'none';
        document.getElementById("step3_normal").style.display = !isPaypal ? 'block' : 'none';
    }

    loadPaypalSdk(clientId)
    {
        return new Promise((resolve, reject) => {
            const existing = document.getElementById("paypal-sdk");
            if (existing) {
                if (window.paypal) {
                    return resolve(window.paypal);
                }
                existing.addEventListener('load', () => resolve(window.paypal), { once: true });
                existing.addEventListener('error', reject, { once: true });
                return;
            }

            const script = document.createElement("script");
            script.id = "paypal-sdk";
            //commit=false ist wichtig -> die buttons ignorieren natürlich experience_context.user_action! nerv
            script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=EUR&components=buttons,messages&enable-funding=paylater&commit=false&disable-funding=venmo,card,sepa`;
            script.onload = () => resolve(window.paypal);
            script.onerror = reject;

            document.head.appendChild(script);
        });
    }
}
