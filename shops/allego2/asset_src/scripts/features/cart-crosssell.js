import * as utilities from "../utils/responsive";

export default class CartCrosssell
{
    constructor()
    {
        this._processed = new WeakSet();
        this._gridElementsPerRow = utilities.isMobile() ? 1 : 4;
        this._displayRows = 2;
        this._rootSelector = '.page-cart';
        this._cardSelector = '.cart-product-card';
        this._moreSelector = '.show-all-products';
        this._hasMore = false;
        this._ajaxSelector = '#ajax-cart';

        document.addEventListener('putInBasket:success', () => {
            const root = document.querySelector(this._rootSelector);
            if(!root)
                return;

            const ajaxElement = document.querySelector(this._ajaxSelector);
            if(!window.ajaxLoader || !ajaxElement) {
                window.location.href = "/warenkorb/";
            } else {
                window.ajaxLoader.loadContent('/warenkorb/', ajaxElement);
            }
        });
    }

    init() {
        this._enhance(this._moreSelector, element => {
            element.addEventListener("click", () => {
                this._displayRows = 999;
                element.classList.toggle("-hidden");
                this._render();
            });
        });

        this._enhance('.extra-service-checkbox', element => {
            element.addEventListener("change", event => {
                const extra_service_id = element.getAttribute("data-extra-service-id");
                const item_id = element.getAttribute("data-item-id");

                const url = `/warenkorb/?service_set=${extra_service_id}&item_id=${item_id}&value=${element.checked ? 1 : 0}`;

                if(!window.ajaxLoader) {
                    document.location.href = url;
                } else {
                    event.preventDefault();
                    window.ajaxLoader.loadContent(url, document.querySelector(this._ajaxSelector));
                }
            });
        });

        this._render();
    }

    _render() {
        document.querySelectorAll(this._cardSelector).forEach((element, i) => {
            if(i >= this._gridElementsPerRow * this._displayRows) {
                element.classList.add('-hidden');
                this._hasMore = true;
            } else {
                element.classList.remove('-hidden');
            }
        });

        if(!this._hasMore) {
            document.querySelectorAll(this._moreSelector).forEach(element => {
                element.classList.add('-hidden');
            });
        }
    }

    _enhance(selector, initializer) {
        const root = document.querySelector(this._rootSelector);
        if(!root) {
            return;
        }

        root.querySelectorAll(selector).forEach((element) => {
            if (this._processed.has(element)) {
                return;
            }

            this._processed.add(element);
            try {
                initializer(element);
            } catch (e) {
                // noop
            }
        });
    }
}