import ModalWindow from "../ui/modal-window.js";

class Modal {
    constructor() {

    }

    init() {
        const elements = document.querySelectorAll('[data-modal-id]');
        elements.forEach(element => {
           const destId = element.getAttribute('data-modal-id');
           const destElement = document.getElementById(destId);

           element.addEventListener('click', event => {
               const modalWindow = new ModalWindow({
                   id: "extra_service_window",
                   classname: "extra_service_window",
                   close_button: true
               });

               modalWindow.show();
               modalWindow.html(destElement.innerHTML);
           });
        });
    }
}

export {Modal};
export default Modal;