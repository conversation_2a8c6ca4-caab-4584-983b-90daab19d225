import {onReady} from "../../utils/ready.js";
import {updateUrlParam} from "../../utils/url.js";
import {isMobile} from "../../utils/responsive.js";
import {syncClassWithAriaState} from "../../ui/accessibility.js";

(function() {
  'use strict';

  class ListingSort {
    constructor(root) {
      this.root = root;
      this.dropdown = root.querySelector('[data-sort-dropdown]');
      this.toggleBtn = root.querySelector('[data-sort-toggle]');
      this.optionsBox = root.querySelector('[data-sort-options]');
    }

    init() {
      if (!this.dropdown || !this.toggleBtn) return;

      this.toggleBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        const open = this.dropdown.classList.toggle('-open');
        this.toggleBtn.setAttribute('aria-expanded', String(open));
      });

      document.addEventListener('click', (e) => {
        if (!this.dropdown.contains(e.target)) {
          this.dropdown.classList.remove('-open');
          this.toggleBtn.setAttribute('aria-expanded', 'false');
        }
      });

      if (this.optionsBox) {
        const options = this.optionsBox.querySelectorAll('.sort-option');
        options.forEach((opt) => {
          opt.addEventListener('click', () => {
            const value = opt.getAttribute('data-value');

            options.forEach((o) => {
              o.classList.remove('-selected');
              o.setAttribute('aria-selected', 'false');
            });
            opt.classList.add('-selected');
            opt.setAttribute('aria-selected', 'true');

            this.dropdown.classList.remove('-open');
            this.toggleBtn.setAttribute('aria-expanded', 'false');

            window.location.href = updateUrlParam('set_product_order', value);
          });
        });
      }
    }
  }

  class RangeSlider {
    constructor(root) {
      this.root = root;
      this.form = null;
      this.minInput = null;
      this.maxInput = null;
      this.leftHandle = null;
      this.rightHandle = null;
      this.rangeHighlight = null;
      this.summary = null;
      this.rangeMin = Number(root.getAttribute('data-min')) || 0;
      this.rangeMax = Number(root.getAttribute('data-max')) || 0;
      this.valueRange = this.rangeMax - this.rangeMin;
      if (!isFinite(this.valueRange) || this.valueRange <= 0) {
        this.valueRange = 1;
        this.rangeMax = this.rangeMin + 1;
      }
      this.minDistanceBase = 10;
      this.minDistancePx = this.minDistanceBase;
      this.maxPosition = 0;
      this.handleWidth = 0;
      this.actMin = 0;
      this.actMax = 0;
      this.activeHandle = null;
      this.activeHandleElement = null;
      this.startPointerX = 0;
      this.startMin = 0;
      this.startMax = 0;
      this.activePointerId = null;
      this.isUpdatingInputs = false;
      this.resizeObserver = null;
      this.prefix = '';
      this.suffix = '';
      this.decimals = null;
      this.rangeBehavior = '';
      this.rangeBehaviorThreshold = null;
      this.rangeBehaviorFactor = 2;
      this.rangeBehaviorActive = false;
      this.boundPointerMove = this.onPointerMove.bind(this);
      this.boundPointerUp = this.onPointerUp.bind(this);
      this.boundResize = this.refreshLayout.bind(this);
      this.onInputChange = this.handleInputChange.bind(this);
      this.onMinPointerDown = (event) => this.onPointerDown(event, 'min');
      this.onMaxPointerDown = (event) => this.onPointerDown(event, 'max');
      this.handleListeners = [];
    }

    static findForm(root) {
      if (!root) return null;
      var sibling = root.nextElementSibling;
      if (sibling && sibling.matches('.filter_range_form')) {
        return sibling;
      }

      var parent = root.parentElement;
      if (parent) {
        var form = parent.querySelector('.filter_range_form');
        if (form) {
          return form;
        }
      }

      return null;
    }

    init() {
      if (!this.root) {
        return;
      }

      this.form = RangeSlider.findForm(this.root);
      this.leftHandle = this.root.querySelector('.slider_handle.left');
      this.rightHandle = this.root.querySelector('.slider_handle.right');
      this.rangeHighlight = this.root.querySelector('.slider-range');
      this.summary = this.root.nextElementSibling;
      if (!this.summary || !this.summary.hasAttribute('data-range-summary')) {
        this.summary = null;
      }

      if (!this.leftHandle || !this.rightHandle) {
        return;
      }

      if (this.form) {
        this.minInput = this.form.querySelector('input[name$="[value_min]"]');
        this.maxInput = this.form.querySelector('input[name$="[value_max]"]');
      }

      this.refreshLayout();
      this.attachHandleListeners();
      this.attachInputListeners();
      this.observeResize();

      this.prefix = this.root.getAttribute('data-prefix') || '';
      this.suffix = this.root.getAttribute('data-suffix') || '';
      var decimalsAttr = this.root.getAttribute('data-decimals');
      if (decimalsAttr !== null && decimalsAttr !== undefined && decimalsAttr !== '') {
        var parsedDecimals = Number(decimalsAttr);
        if (isFinite(parsedDecimals)) {
          this.decimals = parsedDecimals;
        }
      }

      this.updateSummary();
    }

    destroy() {
      this.detachHandleListeners();
      this.detachInputListeners();
      this.disconnectResizeObserver();
      this.releaseActivePointer();
    }

    attachHandleListeners() {
      if (!this.leftHandle || !this.rightHandle) return;

      if (window.PointerEvent) {
        this.leftHandle.addEventListener('pointerdown', this.onMinPointerDown);
        this.rightHandle.addEventListener('pointerdown', this.onMaxPointerDown);
        this.handleListeners.push({ element: this.leftHandle, type: 'pointerdown', listener: this.onMinPointerDown });
        this.handleListeners.push({ element: this.rightHandle, type: 'pointerdown', listener: this.onMaxPointerDown });
      } else {
        var minMouse = (event) => this.onPointerDown(event, 'min');
        var maxMouse = (event) => this.onPointerDown(event, 'max');
        var minTouch = (event) => this.onPointerDown(event, 'min');
        var maxTouch = (event) => this.onPointerDown(event, 'max');

        this.leftHandle.addEventListener('mousedown', minMouse);
        this.leftHandle.addEventListener('touchstart', minTouch, { passive: false });
        this.rightHandle.addEventListener('mousedown', maxMouse);
        this.rightHandle.addEventListener('touchstart', maxTouch, { passive: false });
        this.handleListeners.push({ element: this.leftHandle, type: 'mousedown', listener: minMouse });
        this.handleListeners.push({ element: this.leftHandle, type: 'touchstart', listener: minTouch });
        this.handleListeners.push({ element: this.rightHandle, type: 'mousedown', listener: maxMouse });
        this.handleListeners.push({ element: this.rightHandle, type: 'touchstart', listener: maxTouch });
      }
    }

    detachHandleListeners() {
      this.handleListeners.forEach((entry) => {
        if (entry.element && entry.element.removeEventListener) {
          entry.element.removeEventListener(entry.type, entry.listener);
        }
      });
      this.handleListeners = [];
    }

    attachInputListeners() {
      if (this.minInput) {
        this.minInput.addEventListener('change', this.onInputChange);
        this.minInput.addEventListener('input', this.onInputChange);
      }

      if (this.maxInput) {
        this.maxInput.addEventListener('change', this.onInputChange);
        this.maxInput.addEventListener('input', this.onInputChange);
      }
    }

    detachInputListeners() {
      if (this.minInput) {
        this.minInput.removeEventListener('change', this.onInputChange);
        this.minInput.removeEventListener('input', this.onInputChange);
      }

      if (this.maxInput) {
        this.maxInput.removeEventListener('change', this.onInputChange);
        this.maxInput.removeEventListener('input', this.onInputChange);
      }
    }

    observeResize() {
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(this.boundResize);
        this.resizeObserver.observe(this.root);
      } else {
        window.addEventListener('resize', this.boundResize);
      }
    }

    disconnectResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      } else {
        window.removeEventListener('resize', this.boundResize);
      }
    }

    refreshLayout() {
      this.readRangeBehaviorConfig();
      this.measure();
      this.syncHandlesFromInputs();
      this.updateRangeHighlight();
    }

    measure() {
      if (!this.root) return;

      this.handleWidth = this.leftHandle ? this.leftHandle.offsetWidth : 0;
      this.maxPosition = Math.max((this.root.clientWidth || 0) - this.handleWidth, 0);
      this.minDistancePx = Math.min(this.minDistanceBase, this.maxPosition);
      if (this.minDistancePx < 0) {
        this.minDistancePx = 0;
      }
    }

    formatNumber(value) {
      if (!isFinite(value)) {
        return '';
      }

      var numericValue = Number(value);

      if (this.decimals !== null && isFinite(this.decimals)) {
        var decimals = Math.max(0, this.decimals);
        return numericValue.toLocaleString(undefined, {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals
        });
      }

      return Math.round(numericValue).toLocaleString(undefined, {
        maximumFractionDigits: 0
      });
    }

    formatValue(value) {
      var numeric = this.formatNumber(value);
      if (!numeric) {
        return '';
      }

      var prefix = this.prefix || '';
      var suffix = this.suffix || '';
      return prefix + numeric + suffix;
    }

    parseValue(raw) {
      if (raw === null || raw === undefined) {
        return null;
      }
      var trimmed = String(raw).trim();
      if (!trimmed) {
        return null;
      }
      var value = Number(trimmed.replace(',', '.'));
      if (!isFinite(value)) {
        return null;
      }
      return value;
    }

    valueToPosition(value) {
      if (!isFinite(value)) {
        return 0;
      }

      var clamped = Math.min(Math.max(value, this.rangeMin), this.rangeMax);
      var ratio = (clamped - this.rangeMin) / this.valueRange;
      if (!isFinite(ratio)) {
        ratio = 0;
      }
      var sliderRatio = this.mapValueRatioToSliderRatio(ratio);
      return sliderRatio * this.maxPosition;
    }

    positionToValue(position) {
      if (this.maxPosition === 0) {
        return this.rangeMin;
      }

      var sliderRatio = position / this.maxPosition;
      var valueRatio = this.mapSliderRatioToValueRatio(sliderRatio);
      var value = this.rangeMin + (valueRatio * this.valueRange);
      if (this.decimals !== null && isFinite(this.decimals)) {
        return Number(value.toFixed(Math.max(0, this.decimals)));
      }

      return Math.round(value);
    }

    readRangeBehaviorConfig() {
      if (!this.root) {
        this.rangeBehavior = '';
        this.rangeBehaviorThreshold = null;
        this.rangeBehaviorFactor = 2;
        this.rangeBehaviorActive = false;
        return;
      }

      var behavior = this.root.getAttribute('data-range-behavior') || '';
      this.rangeBehavior = behavior ? behavior.trim() : '';
      if (this.rangeBehavior && this.rangeBehavior !== 'non-linear-low' && this.rangeBehavior !== 'non-linear-high') {
        throw new Error('Unexpected range behavior value: ' + this.rangeBehavior);
      }

      var thresholdAttr = this.root.getAttribute('data-range-behavior-threshold');
      var parsedThreshold = Number(thresholdAttr);
      if (!isFinite(parsedThreshold) || parsedThreshold <= 0) {
        parsedThreshold = null;
      }
      this.rangeBehaviorThreshold = parsedThreshold;

      var factorAttr = this.root.getAttribute('data-range-behavior-factor');
      var parsedFactor = Number(factorAttr);
      if (!isFinite(parsedFactor) || parsedFactor <= 0) {
        parsedFactor = 2;
      }
      this.rangeBehaviorFactor = parsedFactor;

      this.rangeBehaviorActive = this.shouldUseNonLinearBehavior();
    }

    shouldUseNonLinearBehavior() {
      if (!this.rangeBehavior) {
        return false;
      }

      if (this.rangeBehavior !== 'non-linear-low' && this.rangeBehavior !== 'non-linear-high') {
        return false;
      }

      if (!isFinite(this.rangeBehaviorThreshold) || this.rangeBehaviorThreshold <= 0) {
        return false;
      }

      return this.valueRange >= this.rangeBehaviorThreshold;
    }

    clampRatio(ratio) {
      if (!isFinite(ratio)) {
        return 0;
      }
      if (ratio < 0) {
        return 0;
      }
      if (ratio > 1) {
        return 1;
      }
      return ratio;
    }

    mapSliderRatioToValueRatio(sliderRatio) {
      var clampedSlider = this.clampRatio(sliderRatio);
      if (!this.rangeBehaviorActive) {
        return clampedSlider;
      }

      var factor = this.rangeBehaviorFactor;
      if (this.rangeBehavior === 'non-linear-low') {
        return Math.pow(clampedSlider, factor);
      }

      if (this.rangeBehavior === 'non-linear-high') {
        return 1 - Math.pow(1 - clampedSlider, factor);
      }

      return clampedSlider;
    }

    mapValueRatioToSliderRatio(valueRatio) {
      var clampedValue = this.clampRatio(valueRatio);
      if (!this.rangeBehaviorActive) {
        return clampedValue;
      }

      var factor = this.rangeBehaviorFactor;
      if (this.rangeBehavior === 'non-linear-low') {
        return Math.pow(clampedValue, 1 / factor);
      }

      if (this.rangeBehavior === 'non-linear-high') {
        return 1 - Math.pow(1 - clampedValue, 1 / factor);
      }

      return clampedValue;
    }

    syncHandlesFromInputs() {
      var minRaw = this.minInput ? this.minInput.value : '';
      var maxRaw = this.maxInput ? this.maxInput.value : '';
      var hasMin = !!(minRaw && String(minRaw).trim() !== '');
      var hasMax = !!(maxRaw && String(maxRaw).trim() !== '');

      var minValue = hasMin ? this.parseValue(minRaw) : null;
      var maxValue = hasMax ? this.parseValue(maxRaw) : null;

      if (minValue === null) {
        minValue = this.rangeMin;
      }

      if (maxValue === null) {
        maxValue = this.rangeMax;
      }

      if (maxValue < minValue) {
        var temp = minValue;
        minValue = maxValue;
        maxValue = temp;
      }

      this.actMin = this.valueToPosition(minValue);
      this.actMax = this.valueToPosition(maxValue);

      this.actMin = Math.max(0, Math.min(this.actMin, this.actMax - this.minDistancePx));
      this.actMax = Math.min(this.maxPosition, Math.max(this.actMax, this.actMin + this.minDistancePx));

      if (!hasMin && this.actMin <= this.minDistancePx) {
        this.actMin = 0;
      }

      if (!hasMax && this.actMax >= this.maxPosition - this.minDistancePx) {
        this.actMax = this.maxPosition;
      }

      this.positionHandles();
      this.updateRangeHighlight();
      this.updateSummary();
    }

    positionHandles() {
      if (this.leftHandle) {
        this.leftHandle.style.left = Math.round(this.actMin) + 'px';
      }
      if (this.rightHandle) {
        this.rightHandle.style.left = Math.round(this.actMax) + 'px';
      }
    }

    getClientX(event) {
      if (typeof TouchEvent !== 'undefined' && event instanceof TouchEvent) {
        if (!event.touches || !event.touches.length) {
          return null;
        }
        return event.touches[0].clientX;
      }

      if (event.clientX !== undefined) {
        return event.clientX;
      }

      return null;
    }

    onPointerDown(event, handleType) {
      if (event.type === 'mousedown' && window.PointerEvent) {
        return;
      }

      var clientX = this.getClientX(event);
      if (clientX === null) {
        return;
      }

      event.preventDefault();
      this.activeHandle = handleType;
      this.activeHandleElement = event.currentTarget || event.target;
      this.startPointerX = clientX;
      this.startMin = this.actMin;
      this.startMax = this.actMax;

      if (event.pointerId !== undefined && this.activeHandleElement && this.activeHandleElement.setPointerCapture) {
        this.activePointerId = event.pointerId;
        this.activeHandleElement.setPointerCapture(this.activePointerId);
        document.addEventListener('pointermove', this.boundPointerMove);
        document.addEventListener('pointerup', this.boundPointerUp);
        document.addEventListener('pointercancel', this.boundPointerUp);
      } else if (event.type === 'touchstart') {
        document.addEventListener('touchmove', this.boundPointerMove, { passive: false });
        document.addEventListener('touchend', this.boundPointerUp);
        document.addEventListener('touchcancel', this.boundPointerUp);
      } else {
        document.addEventListener('mousemove', this.boundPointerMove);
        document.addEventListener('mouseup', this.boundPointerUp);
      }

      if (this.activeHandleElement) {
        this.activeHandleElement.classList.add('-dragging');
      }
    }

    onPointerMove(event) {
      if (!this.activeHandle) {
        return;
      }
      if (event.type === 'pointermove' && this.activePointerId !== null && event.pointerId !== this.activePointerId) {
        return;
      }

      var clientX = this.getClientX(event);
      if (clientX === null) {
        return;
      }

      if (event.preventDefault) {
        event.preventDefault();
      }

      var delta = clientX - this.startPointerX;

      if (this.activeHandle === 'min') {
        var newMin = this.startMin + delta;
        var maxForMin = this.startMax - this.minDistancePx;
        if (maxForMin < 0) {
          maxForMin = 0;
        }
        this.actMin = Math.max(0, Math.min(newMin, maxForMin));
        if (this.actMin > this.actMax - this.minDistancePx) {
          this.actMin = this.actMax - this.minDistancePx;
        }
      } else {
        var newMax = this.startMax + delta;
        var minForMax = this.startMin + this.minDistancePx;
        this.actMax = Math.min(this.maxPosition, Math.max(newMax, minForMax));
        if (this.actMax < this.actMin + this.minDistancePx) {
          this.actMax = this.actMin + this.minDistancePx;
        }
      }

      this.positionHandles();
      this.updateInputs();
      this.updateRangeHighlight();
    }

    onPointerUp(event) {
      if (event && event.type === 'pointerup' && this.activePointerId !== null && event.pointerId !== this.activePointerId) {
        return;
      }
      this.releaseActivePointer();
    }

    releaseActivePointer() {
      if (this.activeHandleElement && this.activePointerId !== null && this.activeHandleElement.releasePointerCapture) {
        try {
          this.activeHandleElement.releasePointerCapture(this.activePointerId);
        } catch (error) {
          // ignore
        }
      }

      document.removeEventListener('pointermove', this.boundPointerMove);
      document.removeEventListener('pointerup', this.boundPointerUp);
      document.removeEventListener('pointercancel', this.boundPointerUp);
      document.removeEventListener('mousemove', this.boundPointerMove);
      document.removeEventListener('mouseup', this.boundPointerUp);
      document.removeEventListener('touchmove', this.boundPointerMove);
      document.removeEventListener('touchmove', this.boundPointerMove, { passive: false });
      document.removeEventListener('touchend', this.boundPointerUp);
      document.removeEventListener('touchcancel', this.boundPointerUp);

      if (this.activeHandleElement) {
        this.activeHandleElement.classList.remove('-dragging');
      }

      this.activeHandle = null;
      this.activeHandleElement = null;
      this.activePointerId = null;
    }

    updateInputs() {
      if (this.isUpdatingInputs) {
        return;
      }

      this.isUpdatingInputs = true;

      var minValue = this.positionToValue(this.actMin);
      var maxValue = this.positionToValue(this.actMax);

      if (this.minInput) {
        if (this.actMin <= 0) {
          this.minInput.value = '';
        } else {
          this.minInput.value = String(minValue);
        }
      }

      if (this.maxInput) {
        if (this.actMax >= this.maxPosition) {
          this.maxInput.value = '';
        } else {
          this.maxInput.value = String(maxValue);
        }
      }

      this.updateSummary();

      this.isUpdatingInputs = false;
    }

    updateRangeHighlight() {
      if (!this.rangeHighlight) {
        return;
      }

      var centerStart = this.actMin + (this.handleWidth / 2);
      var centerEnd = this.actMax + (this.handleWidth / 2);
      var width = Math.max(centerEnd - centerStart, 0);
      var trackWidth = this.root ? this.root.clientWidth : 0;

      if (trackWidth > 0) {
        var maxAllowedWidth = Math.max(trackWidth - centerStart, 0);
        width = Math.min(width, maxAllowedWidth);
      }

      this.rangeHighlight.style.left = Math.round(centerStart) + 'px';
      this.rangeHighlight.style.width = Math.max(Math.round(width), 0) + 'px';
    }

    updateSummary() {
      if (!this.summary) {
        return;
      }

      var minValue = (this.actMin <= 0) ? this.rangeMin : this.positionToValue(this.actMin);
      var maxValue = (this.actMax >= this.maxPosition) ? this.rangeMax : this.positionToValue(this.actMax);
      var minText = this.formatValue(minValue);
      var maxText = this.formatValue(maxValue);

      if (!minText && !maxText) {
        this.summary.textContent = '';
        return;
      }

      if (!minText) {
        this.summary.textContent = maxText;
        return;
      }

      if (!maxText) {
        this.summary.textContent = minText;
        return;
      }

      this.summary.textContent = minText + ' - ' + maxText;
    }

    handleInputChange() {
      if (this.isUpdatingInputs) {
        return;
      }

      this.syncHandlesFromInputs();
      this.updateRangeHighlight();
      this.updateSummary();
    }
  }

  class ListingFilter {
    constructor(root) {
      this.root = root;
      this.dynamicMount = root.querySelector('#filters-container-new');
      this.dataScript = document.querySelector('#filters-data');
      this.goodieFilterCheckboxes = document.querySelectorAll('.goodie-filter-checkbox');
      this.iconFilter = root.getAttribute('data-icon-filter') || '';
      this.iconChevron = root.getAttribute('data-icon-chevron') || '';
      this.renderMode = root.getAttribute('data-render-mode') || 'default';
      this.resetUrl = this.dynamicMount ? this.dynamicMount.getAttribute('data-reset-url') || '' : '';
      this.loaderTargetId = this.dynamicMount ? this.dynamicMount.getAttribute('data-loader-target') || '' : '';
      this.mainToggle = null;
      this.container = null;
      this.sectionToggles = [];
      this.resetBtn = null;
      this.lastFiltersSignature = null;
      this.refreshScheduled = false;
      this.pendingForceRefresh = false;
      this.sectionStates = {};
      this.containerVisible = null;
      this.rangeSliders = [];
      this.mutationObserver = null;
    }

    updateDomRefs() {
      this.dynamicMount = this.root.querySelector('#filters-container-new');
      this.dataScript = document.querySelector('#filters-data');
      this.resetUrl = this.dynamicMount ? this.dynamicMount.getAttribute('data-reset-url') || '' : '';
      this.loaderTargetId = this.dynamicMount ? this.dynamicMount.getAttribute('data-loader-target') || '' : '';
      if (!this.dynamicMount) {
        this.containerVisible = false;
      }
    }

    captureUiState() {
      if (!this.container) {
        return;
      }

      this.containerVisible = this.container.classList.contains('-visible');

      var sections = this.container.querySelectorAll('[data-filter-section]');
      Array.prototype.forEach.call(sections, (section) => {
        var key = section.getAttribute('data-feature-key');
        if (!key) {
          return;
        }
        this.sectionStates[key] = !section.classList.contains('-collapsed');
      });
    }

    destroyRangeSliders() {
      if (!this.rangeSliders || !this.rangeSliders.length) {
        this.rangeSliders = [];
        return;
      }

      this.rangeSliders.forEach((slider) => {
        if (slider && typeof slider.destroy === 'function') {
          slider.destroy();
        }
      });

      this.rangeSliders = [];
    }

    initializeRangeSliders() {
      this.destroyRangeSliders();
      if (!this.container) {
        return;
      }

      var sliderElements = this.container.querySelectorAll('[data-range-slider]');
      this.rangeSliders = [];

      Array.prototype.forEach.call(sliderElements, (element) => {
        var slider = new RangeSlider(element);
        slider.init();
        if (slider.leftHandle && slider.rightHandle) {
          this.rangeSliders.push(slider);
        }
      });
    }

    resolveFeatureKey(feature, fallbackIndex) {
      var candidates = [
        feature && feature.feature_id,
        feature && feature.feature_key,
        feature && feature.key,
        feature && feature.id
      ];

      for (var i = 0; i < candidates.length; i++) {
        var candidate = candidates[i];
        if (candidate === undefined || candidate === null) {
          continue;
        }

        var value = String(candidate);
        if (value) {
          return value;
        }
      }

      if (feature && feature.feature_name) {
        var nameValue = String(feature.feature_name);
        if (nameValue) {
          return nameValue + '-' + fallbackIndex;
        }
      }

      return 'feature-' + fallbackIndex;
    }

    readFiltersPayload() {
      if (!this.dataScript) {
        return null;
      }

      var raw = this.dataScript.textContent || this.dataScript.innerText || '';

      if (!raw || !raw.trim()) {
        return null;
      }

      var signature = raw.trim();

      try {
        var parsed = JSON.parse(raw);
        var list;

        if (Array.isArray(parsed)) {
          list = parsed;
        } else if (parsed && typeof parsed === 'object') {
          list = Object.keys(parsed).map(function(key) {
            var feature = parsed[key];
            if (!feature || typeof feature !== 'object') {
              return null;
            }

            var item = Object.assign({}, feature);
            if (!item.feature_id && key) {
              item.feature_id = key;
            }
            item.feature_key = key;
            return item;
          }).filter(Boolean);
        } else {
          list = [];
        }

        return {
          filters: Array.isArray(list) ? list : [],
          signature: signature
        };
      } catch (error) {
        return null;
      }
    }

    refreshFilters(force) {
      this.updateDomRefs();

      var payload = this.readFiltersPayload();
      if (!payload) {
        return;
      }

      var filters = Array.isArray(payload.filters) ? payload.filters : [];
      var signature = payload.signature;

      if (!filters.length) {
        if (this.dynamicMount) {
          this.destroyRangeSliders();
          this.dynamicMount.innerHTML = '';
        }
        this.lastFiltersSignature = signature;
        this.mainToggle = null;
        this.container = null;
        this.sectionToggles = [];
        this.resetBtn = null;
        return;
      }

      if (!force && this.lastFiltersSignature === signature && this.container) {
        return;
      }

      this.lastFiltersSignature = signature;
      this.renderFilters(filters);
      this.bindFilterEvents();
    }

    renderFilters(filters) {
      if (!this.dynamicMount || !Array.isArray(filters)) {
        return;
      }

      this.captureUiState();
      this.destroyRangeSliders();

      this.dynamicMount.innerHTML = '';
      this.mainToggle = null;
      this.container = null;
      this.sectionToggles = [];
      this.resetBtn = null;

      if (!filters.length) {
        this.containerVisible = false;
        this.sectionStates = {};
        return;
      }

      var globalSelectedCount = filters.reduce(function(total, feature) {
        return total + (Number(feature.selected_count) || 0);
      }, 0);

      var shouldShowContainer;
      if (typeof this.containerVisible === 'boolean') {
        shouldShowContainer = this.containerVisible;
      } else if (globalSelectedCount > 0) {
        shouldShowContainer = true;
      } else {
        //auf dem Desktop immer offen, auf Mobile nur wenn globalSelectedCount > 0
        shouldShowContainer = !isMobile();
      }

      var fragment = document.createDocumentFragment();
      var toggle = null;
      if (this.renderMode !== 'compact') {
        toggle = this.createMainToggle(globalSelectedCount, shouldShowContainer);
        if (toggle) {
          fragment.appendChild(toggle);
        }
      }

      var container = this.createFiltersContainer(this.renderMode === 'compact' ? true : shouldShowContainer);
      var currentKeys = [];

      filters.forEach((feature, index) => {
        if(feature.hidden) {
          return;
        }

        if (this.renderMode === 'compact') {
          var items = this.createCompactFilterItems(feature, index);
          items.forEach((item) => {
            if (item) {
              container.appendChild(item);
            }
          });
        } else {
          var section = this.createFilterSection(feature, globalSelectedCount, index);
          if (section) {
            container.appendChild(section);
            var key = section.getAttribute('data-feature-key');
            if (key) {
              currentKeys.push(key);
            }
          }
        }
      });

      if (globalSelectedCount > 0 && this.renderMode !== 'compact') {
        var resetButton = this.createResetButton(globalSelectedCount);
        if (resetButton) {
          container.appendChild(resetButton);
        }
      }

      fragment.appendChild(container);
      this.dynamicMount.appendChild(fragment);

      this.containerVisible = shouldShowContainer;

      if (currentKeys.length) {
        var keepLookup = {};
        currentKeys.forEach((key) => {
          keepLookup[key] = true;
        });

        Object.keys(this.sectionStates).forEach((key) => {
          if (!keepLookup[key]) {
            delete this.sectionStates[key];
          }
        });
      }

      if (window.ajaxLoader && typeof window.ajaxLoader.bindLinks === 'function') {
        window.ajaxLoader.bindLinks(this.dynamicMount);
      }
    }

    bindFilterEvents() {
      this.mainToggle = this.root.querySelector('[data-filter-main-toggle]');
      this.container = this.root.querySelector('[data-filters-container]');
      this.sectionToggles = this.root.querySelectorAll('[data-filter-toggle]');
      this.resetBtn = this.root.querySelector('[data-filter-reset]');

      if (this.mainToggle && this.container) {
        this.mainToggle.addEventListener('click', () => {
          var isActive = syncClassWithAriaState(this.mainToggle, '-active');
          this.container.classList.toggle('-visible', isActive);
          this.containerVisible = isActive;
          if (isActive && this.rangeSliders && this.rangeSliders.length) {
            this.rangeSliders.forEach((slider) => {
              if (slider && typeof slider.refreshLayout === 'function') {
                slider.refreshLayout();
              }
            });
          }
        });
      }

      this.sectionToggles.forEach((toggle) => {
        toggle.addEventListener('click', () => {
          var section = toggle.closest('[data-filter-section]');
          if (!section) return;
          section.classList.toggle('-collapsed');
          var isExpanded = !section.classList.contains('-collapsed');
          toggle.setAttribute('aria-expanded', String(isExpanded));
          var featureKey = section.getAttribute('data-feature-key');
          if (featureKey) {
            this.sectionStates[featureKey] = isExpanded;
          }
          if (isExpanded && this.rangeSliders && this.rangeSliders.length) {
            this.rangeSliders.forEach((slider) => {
              if (slider && slider.root && section.contains(slider.root) && typeof slider.refreshLayout === 'function') {
                slider.refreshLayout();
              }
            });
          }
        });

        toggle.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggle.click();
          }
        });
      });

      if (this.resetBtn) {
        this.resetBtn.addEventListener('click', () => {
          for(var el of this.goodieFilterCheckboxes) {
            el.checked = false;
          }

          var href = this.resetBtn.getAttribute('data-url-reset');
          this.loadUrl(href);
        });
      }

      if (!this.container) {
        return;
      }

      this.container.addEventListener('change', (e) => {
        var target = e.target;
        if (!(target instanceof HTMLInputElement)) return;
        if (target.type !== 'checkbox' && !target.classList.contains('checkbox') && !target.classList.contains('checkbox-input')) return;

        var addUrl = target.getAttribute('data-url-add');
        var removeUrl = target.getAttribute('data-url-remove');

        var filterKeyAttr = target.getAttribute('data-filter-key');
        if (filterKeyAttr) {
          var filterValueAttr = target.getAttribute('data-filter-value');
          if (!addUrl) {
            addUrl = this.buildFilterUrl(filterKeyAttr, filterValueAttr, true);
          }
          if (!removeUrl) {
            removeUrl = this.buildFilterUrl(filterKeyAttr, filterValueAttr, false);
          }
        }

        var href = target.checked ? addUrl : removeUrl;
        if (!href) {
          return;
        }
        this.loadUrl(href);
      });

      var priceForms = this.container.querySelectorAll('.filter_range_form[data-dynamic-range-form]');
      Array.prototype.forEach.call(priceForms, (priceRangeForm) => {
        priceRangeForm.addEventListener('submit', (e) => {
          e.preventDefault();

          var filterId = priceRangeForm.getAttribute('data-filter-id');
          var minInput = priceRangeForm.querySelector('input[name$="[value_min]"]');
          var maxInput = priceRangeForm.querySelector('input[name$="[value_max]"]');

          if (!filterId && minInput) {
            var match = minInput.name.match(/^feature\[(.+?)\]/);
            if (match) {
              filterId = match[1];
            }
          }

          if (!filterId) return;

          var minValue = (minInput && minInput.value.trim()) || '';
          var maxValue = (maxInput && maxInput.value.trim()) || '';

          var url = new URL(window.location.href);
          var minKey = 'feature[' + filterId + '][value_min]';
          var maxKey = 'feature[' + filterId + '][value_max]';

          if (minValue) {
            url.searchParams.set(minKey, minValue);
          } else {
            url.searchParams.delete(minKey);
          }

          if (maxValue) {
            url.searchParams.set(maxKey, maxValue);
          } else {
            url.searchParams.delete(maxKey);
          }

          url.searchParams.delete('feature[' + filterId + ']');
          this.loadUrl(url.toString());
        });
      });

      if (this.container) {
        this.containerVisible = this.container.classList.contains('-visible');
      }

      this.initializeRangeSliders();
    }

    scheduleRefresh(force) {
      if (force) {
        this.pendingForceRefresh = true;
      }

      if (this.refreshScheduled) {
        return;
      }

      this.refreshScheduled = true;

      var triggerRefresh = () => {
        this.refreshScheduled = false;
        var shouldForce = this.pendingForceRefresh;
        this.pendingForceRefresh = false;
        this.refreshFilters(shouldForce);
      };

      if (typeof window.requestAnimationFrame === 'function') {
        window.requestAnimationFrame(triggerRefresh);
      } else {
        setTimeout(triggerRefresh, 0);
      }
    }

    startFiltersObserver() {
      if (this.mutationObserver) {
        return;
      }

      if (!window.MutationObserver) {
        return;
      }

      this.mutationObserver = new MutationObserver((mutations) => {
        var shouldRefresh = false;
        var force = false;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType !== Node.ELEMENT_NODE) {
                return;
              }

              if (node.id === 'filters-data' || (typeof node.querySelector === 'function' && node.querySelector('#filters-data'))) {
                shouldRefresh = true;
                force = true;
              }
            });

            mutation.removedNodes.forEach((node) => {
              if (node.nodeType !== Node.ELEMENT_NODE) {
                return;
              }

              if (node.id === 'filters-data' || (typeof node.querySelector === 'function' && node.querySelector('#filters-data'))) {
                shouldRefresh = true;
                force = true;
              }
            });
          } else if (mutation.type === 'characterData') {
            var parent = mutation.target && mutation.target.parentElement;
            if (parent && parent.id === 'filters-data') {
              shouldRefresh = true;
            }
          }
        });

        if (shouldRefresh) {
          this.scheduleRefresh(force);
        }
      });

      try {
        this.mutationObserver.observe(document.body, {
          childList: true,
          characterData: true,
          subtree: true
        });
      } catch (error) {
        // Fallback: if observing fails we at least refresh once.
        this.refreshFilters(true);
      }
    }

    createMainToggle(globalSelectedCount, shouldBeActive) {
      var button = document.createElement('button');
      button.type = 'button';
      button.className = 'filter-toggle';
      var isActive = typeof shouldBeActive === 'boolean' ? shouldBeActive : (globalSelectedCount > 0);
      if (isActive) {
        button.classList.add('-active');
      }
      button.setAttribute('data-filter-main-toggle', '');
      button.setAttribute('aria-label', 'Filter');
      button.setAttribute('aria-expanded', isActive ? 'true' : 'false');

      var content = document.createElement('div');
      content.className = 'button-content';

      if (this.iconFilter) {
        var icon = document.createElement('img');
        icon.className = 'icon';
        icon.src = this.iconFilter;
        icon.alt = '';
        icon.width = 24;
        icon.height = 24;
        content.appendChild(icon);
      }

      var label = document.createElement('span');
      label.className = 'text';
      label.textContent = 'Filter';
      content.appendChild(label);

      button.appendChild(content);

      if (globalSelectedCount > 0) {
        var badge = document.createElement('span');
        badge.className = 'badge';
        badge.setAttribute('data-filter-count', '');
        badge.textContent = String(globalSelectedCount);
        button.appendChild(badge);
      }

      return button;
    }

    createFiltersContainer(isVisible) {
      var container = document.createElement('div');
      container.className = 'filters-container';
      if (isVisible) {
        container.classList.add('-visible');
      }
      container.setAttribute('data-filters-container', '');
      return container;
    }

    buildFilterUrl(filterKey, filterValue, isAdding) {
      const url = new URL(window.location.href);
      const paramName = `feature[${filterKey}]`;

      if (isAdding) {
        url.searchParams.set(paramName, filterValue);
      } else {
        url.searchParams.delete(paramName);
      }

      return url.toString();
    }

    buildEnumOptions(feature) {
      const nodes = [];
      const values = Array.isArray(feature.values) ? feature.values : [];

      values.forEach((value) => {
        if (!value || !value.feature_value_format) {
          return;
        }

        const wrapper = document.createElement('label');
        wrapper.className = 'filter-item checkbox-option';

        const input = document.createElement('input');
        input.type = 'checkbox';
        input.className = 'checkbox-input';

        if (value.selected) {
          input.checked = true;
        }

        if (value.url_add) {
          input.setAttribute('data-url-add', value.url_add);
        }

        if (value.url_remove) {
          input.setAttribute('data-url-remove', value.url_remove);
        }

        if (feature.feature_id) {
          input.setAttribute('data-filter-key', feature.feature_id);
        }

        const valueToken = value.feature_value !== undefined && value.feature_value !== null
            ? value.feature_value
            : value.feature_value_format;
        input.setAttribute('data-filter-value', valueToken);

        const checkmark = document.createElement('span');
        checkmark.className = 'checkmark';

        const text = document.createElement('span');
        text.className = 'label';

        let labelText = value.feature_value_format;
        if (this.renderMode === 'compact') {
          labelText = labelText.replace(/\s*\(\d+\)\s*$/, '');
        }
        text.textContent = labelText;

        const quantity = document.createElement('span');
        quantity.className = 'quantity';
        quantity.textContent = value.anzahl;
        text.appendChild(quantity);

        wrapper.appendChild(input);
        wrapper.appendChild(checkmark);
        wrapper.appendChild(text);

        if (value.anzahl) {
          wrapper.appendChild(quantity);
        }

        nodes.push(wrapper);
      });

      return nodes;
    }

    buildRangeFilter(feature) {
      const container = document.createElement('div');
      container.className = 'filter-item range-filter';

      const slider = document.createElement('div');
      slider.className = 'slider';
      slider.setAttribute('data-range-slider', '');
      if (feature.range_min !== undefined) {
        slider.setAttribute('data-min', feature.range_min);
      }
      if (feature.range_max !== undefined) {
        slider.setAttribute('data-max', feature.range_max);
      }

      if (feature.range_behavior) {
        slider.setAttribute('data-range-behavior', feature.range_behavior);
      }

      if (feature.range_behavior_threshold !== undefined && feature.range_behavior_threshold !== null) {
        slider.setAttribute('data-range-behavior-threshold', feature.range_behavior_threshold);
      }

      if (feature.range_behavior_factor !== undefined && feature.range_behavior_factor !== null) {
        slider.setAttribute('data-range-behavior-factor', feature.range_behavior_factor);
      }

      const prefix = feature.range_prefix || feature.value_prefix || '';
      const suffix = feature.range_suffix || feature.value_suffix || '';
      const decimals = (feature.range_decimals !== undefined && feature.range_decimals !== null)
          ? feature.range_decimals
          : feature.value_decimals;

      if (prefix) {
        slider.setAttribute('data-prefix', prefix);
      }
      if (suffix) {
        slider.setAttribute('data-suffix', suffix);
      }
      if (decimals !== undefined && decimals !== null && decimals !== '') {
        slider.setAttribute('data-decimals', decimals);
      }

      const activeRange = document.createElement('div');
      activeRange.className = 'slider-range';
      slider.appendChild(activeRange);

      const leftHandle = document.createElement('div');
      leftHandle.className = 'slider_handle left';
      const rightHandle = document.createElement('div');
      rightHandle.className = 'slider_handle right';
      slider.appendChild(leftHandle);
      slider.appendChild(rightHandle);

      const summary = document.createElement('div');
      summary.className = 'filter-range-summary';
      summary.setAttribute('data-range-summary', '');
      summary.textContent = '';

      const form = document.createElement('form');
      form.method = 'get';
      form.className = 'filter_range_form';
      if (feature.feature_id) {
        form.setAttribute('data-filter-id', feature.feature_id);
      }
      form.setAttribute('data-dynamic-range-form', '');

      const selectedValues = feature.selected_values || {};
      const minInput = document.createElement('input');
      minInput.type = 'hidden';
      minInput.className = 'filter_range';
      if (feature.feature_id) {
        minInput.name = `feature[${feature.feature_id}][value_min]`;
      }
      if (selectedValues.value_min !== undefined && selectedValues.value_min !== null) {
        minInput.value = selectedValues.value_min;
      }

      const maxInput = document.createElement('input');
      maxInput.type = 'hidden';
      maxInput.className = 'filter_range';
      if (feature.feature_id) {
        maxInput.name = `feature[${feature.feature_id}][value_max]`;
      }
      if (selectedValues.value_max !== undefined && selectedValues.value_max !== null) {
        maxInput.value = selectedValues.value_max;
      }

      const submit = document.createElement('button');
      submit.type = 'submit';
      submit.className = 'filter_range_button';
      const submitLabel = feature.range_apply_label || feature.apply_label || 'Auswahl anwenden';
      submit.textContent = submitLabel;

      form.appendChild(minInput);
      form.appendChild(maxInput);
      form.appendChild(submit);

      container.appendChild(slider);
      container.appendChild(summary);
      container.appendChild(form);

      return container;
    }

    createFilterSection(feature, globalSelectedCount, index) {
      const section = document.createElement('div');
      section.className = 'filter-section _card';
      section.setAttribute('data-filter-section', '');
      const featureKey = this.resolveFeatureKey(feature, index);
      section.setAttribute('data-feature-key', featureKey);

      const selectedCount = Number(feature.selected_count) || 0;
      const defaultExpanded = globalSelectedCount > 0 && selectedCount > 0;
      const hasStoredState = Object.prototype.hasOwnProperty.call(this.sectionStates, featureKey);
      const isExpanded = hasStoredState ? Boolean(this.sectionStates[featureKey]) : defaultExpanded;
      this.sectionStates[featureKey] = isExpanded;

      if (!isExpanded) {
        section.classList.add('-collapsed');
      }

      const header = document.createElement('div');
      header.className = 'filter-header';
      header.setAttribute('data-filter-toggle', '');
      header.setAttribute('role', 'button');
      header.setAttribute('tabindex', '0');
      header.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');

      const title = document.createElement('span');
      title.className = 'title';
      title.textContent = feature.feature_name || '';

      const actions = document.createElement('div');
      actions.className = 'header-actions';

      if (selectedCount > 0) {
        const countBadge = document.createElement('span');
        countBadge.className = 'badge';
        countBadge.textContent = String(selectedCount);
        actions.appendChild(countBadge);
      }

      if (this.iconChevron) {
        const chevron = document.createElement('img');
        chevron.className = 'chevron';
        chevron.src = this.iconChevron;
        chevron.alt = '';
        chevron.width = 24;
        chevron.height = 24;
        actions.appendChild(chevron);
      }

      header.appendChild(title);
      header.appendChild(actions);

      const items = document.createElement('div');
      items.className = 'filter-items';

      let hasValues = true;
      if (feature.type_frontend === 'range') {
        const rangeFilter = this.buildRangeFilter(feature);
        if (rangeFilter) {
          items.appendChild(rangeFilter);
        }
      } else {
        const options = this.buildEnumOptions(feature);
        hasValues = options.length > 0;
        options.forEach((option) => {
          items.appendChild(option);
        });
      }

      if (!hasValues) {
        section.classList.add('-empty');
      }

      section.appendChild(header);
      section.appendChild(items);

      return section;
    }

    createCompactFilterItems(feature, index) {
      const items = [];

      if (feature.type_frontend === 'range') {
        const rangeFilter = this.buildRangeFilter(feature);
        if (rangeFilter) {
          items.push(rangeFilter);
        }
      } else {
        const options = this.buildEnumOptions(feature);
        options.forEach((option) => {
          items.push(option);
        });
      }

      return items;
    }

    createResetButton(globalSelectedCount) {
      if (!this.resetUrl) return null;
      const button = document.createElement('button');
      button.type = 'button';
      button.className = 'filter-reset';
      button.setAttribute('data-filter-reset', '');
      button.setAttribute('data-url-reset', this.resetUrl);

      const text = document.createElement('span');
      text.className = 'text';
      text.textContent = 'Alle Filter zurücksetzen';

      const badge = document.createElement('span');
      badge.className = 'badge';
      badge.textContent = String(globalSelectedCount);

      button.appendChild(text);
      button.appendChild(badge);

      return button;
    }

    loadUrl(rawUrl) {
      if (!rawUrl) {
        return;
      }

      var ajaxLoader = window.ajaxLoader;
      var targetId = this.loaderTargetId || (this.dynamicMount && this.dynamicMount.getAttribute('data-loader-target')) || '';
      var targetElement = targetId ? document.getElementById(targetId) : null;

      if (ajaxLoader && targetElement) {
        var urlObject = rawUrl instanceof URL ? rawUrl : null;

        if (!urlObject) {
          try {
            urlObject = new URL(rawUrl, window.location.href);
          } catch (error) {
            urlObject = null;
          }
        }

        if (urlObject) {
          ajaxLoader.loadContent(urlObject, targetElement);
          return;
        }
      }

      window.location.href = rawUrl;
    }

    init() {
      this.refreshFilters(true);
      this.startFiltersObserver();

      this.initGoodies();
      this.initGoodieCatFilter();
    }

    initGoodies() {
      var paramName = "feature[goodie_id][]";

      this.goodieFilterCheckboxes = document.querySelectorAll('.goodie-filter-checkbox');
      for(var el of this.goodieFilterCheckboxes) {
        var goodie_id = (el.getAttribute('data-goodie-id') || '').replace('goodie-', '');
        if(!goodie_id) {
          continue;
        }
        el.checked = new URL(window.location.href).searchParams.has(paramName, goodie_id);

        el.addEventListener('change', () => {
          var url = new URL(window.location.href);
          url.searchParams.delete(paramName);
          url.searchParams.delete('offset'); // remove pagination

          for(var el of this.goodieFilterCheckboxes) {
            var goodie_id = (el.getAttribute('data-goodie-id') || '').replace('goodie-', '');
            if(!el.checked || !goodie_id) {
              continue;
            }

            url.searchParams.append(paramName, goodie_id);
          }

          this.loadUrl(url.toString());
        });
      }
    }

    initGoodieCatFilter() {
      document.addEventListener("AjaxLoader:loaded", () => {
        this.initGoodies();
      });
      var paramName = "cat_ids[]";
      var catFilterCheckboxes = document.querySelectorAll('.cat-filter-checkbox');
      for(var el of catFilterCheckboxes) {
        var cat_id = (el.getAttribute('data-cat-id') || '').replace('cat-', '');
        if(!cat_id) {
          continue;
        }
        var searchParams = new URL(window.location.href).searchParams;
        el.checked = searchParams.has(paramName, cat_id);

        el.addEventListener('change', (event) => {
          var sub = window.location.href.indexOf('?');
          if(sub === -1) {
            sub = window.location.href.length;
          }
          var url = new URL(window.location.href.substring(0, sub));

          for(var el of catFilterCheckboxes) {
            // unset all, remove this if you want to allow multiple selections
            if(el != event.currentTarget) {
              el.checked = false;
              continue;
            }

            var cat_id = (el.getAttribute('data-cat-id') || '').replace('cat-', '');
            if(!el.checked || !cat_id) {
              continue;
            }

            url.searchParams.append(paramName, cat_id);
          }

          var dynamicMount = document.querySelector('#filters-container-new');
          var targetId = dynamicMount ? dynamicMount.getAttribute('data-loader-target') : '';
          this.loadUrl(url.toString(), targetId);
        });
      }
    }
  }

  const boot = function() {
    const roots = document.querySelectorAll('.listing-filter, .category-filter');
    roots.forEach((root) => {
      const sorter = new ListingSort(root);
      sorter.init();

      const filter = new ListingFilter(root);
      filter.init();
    });
  };

  onReady(boot);
})();
