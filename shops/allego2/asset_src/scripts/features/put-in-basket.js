import ToastNotification from "../ui/toast-notification.js";
import modalWindow from "../ui/modal-window";

const BTN_SUCCESS_HOLD_MS = 1000;

export default class PutInBasket {
  constructor() {
    this._processed = new WeakSet();
    this._buttonTimeouts = new WeakMap();

    this.rootClass = 'basket-modal-window';
    this.modal = new modalWindow({
      id: 'basket-modal-window',
      classname: this.rootClass,
      height: 'auto'
    });
  }

  init() {
    this._enhance('#put_in_basket, .put_in_basket, .put_in_basket_service, .std_form.put_in_basket', (form) => {
      this._setupFormQuantityControls(form);
      this._setupFormSubmission(form);
    });

    this._enhance('#put_in_basket .expres_checkout, .put_in_basket .expres_checkout, .put_in_basket_service .expres_checkout', (button) => this._setupExpressCheckout(button));
    this._enhance('.qty[role="group"]', (root) => this._setupQtyGroup(root));
  }

  _enhance(selector, initializer) {
    document.querySelectorAll(selector).forEach((element) => {
      if (this._processed.has(element)) {
        return;
      }

      this._processed.add(element);
      try {
        initializer(element);
      } catch (e) {
        // noop
      }
    });
  }

  _setupFormQuantityControls(form) {
    const qty = form.querySelector('input.input[type="number"][name="menge"]');
    if (!qty) {
      return;
    }

    const dec = form.querySelector('.qty .button:first-child');
    if (dec) {
      dec.addEventListener('click', (event) => {
        event.preventDefault();
        const current = parseInt(qty.value, 10) || 1;
        qty.value = Math.max(1, current - 1);

        qty.dispatchEvent(new CustomEvent('change'));
      });
    }

    const inc = form.querySelector('.qty .button:last-child');
    if (inc) {
      inc.addEventListener('click', (event) => {
        event.preventDefault();
        const current = parseInt(qty.value, 10) || 1;
        qty.value = current + 1;

        qty.dispatchEvent(new CustomEvent('change'));
      });
    }
  }

  _setupFormSubmission(form) {
    if (!(form instanceof HTMLFormElement)) {
      return;
    }

    form.addEventListener('submit', (event) => this._handleFormSubmit(event, form));

    form.querySelectorAll('[data-click-submit]').forEach(element => {
      element.addEventListener('click', event => {
        event.stopPropagation();
        this._handleFormSubmit(event, form);
      });
    });
  }

  _setupExpressCheckout(button) {
    const element = button instanceof HTMLElement ? button : null;
    if (!element) {
      return;
    }

    element.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();

      const form = element.closest('form');
      if (!form) {
        return;
      }

      this._submitPaypal(form);
    });
  }

  _handleFormSubmit(event, form) {
    event.preventDefault();
    event.stopPropagation();

    if (!(form instanceof HTMLFormElement)) {
      return;
    }

    const submitButton = form.querySelector('button.action[type="submit"]');
    if (submitButton) {
      this._prepareSubmitButton(submitButton);
    }

    this._addItem(form, submitButton);
  }

  async _addItem(form, submitButton) {
    const params = this._serializeForm(form);
    params.set('modus', 'ajax');

    try {
      const response = await fetch('/warenkorb/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: params.toString(),
      });

      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      const payload = await this._parseResponsePayload(response);
      this._updateCartWidget(payload);
      this._showSuccess(submitButton, payload.html);

      if (payload && typeof payload === 'object' && payload.tracker) {
        this._executeTracker(payload.tracker);
      }

      const event = new CustomEvent("putInBasket:success");
      document.dispatchEvent(event);
    } catch (e) {
      console.error(e);
      this._handleSubmitError(submitButton);
      ToastNotification.show('error', 'Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.');
    }
  }

  async _parseResponsePayload(response) {
    const text = await response.text();
    try {
      return JSON.parse(text);
    } catch (e) {
      return text;
    }
  }

  _updateCartWidget(payload) {
    if (!payload || typeof payload !== 'object' || !payload.html || typeof payload.count === 'undefined') {
      return;
    }

    const countElement = document.querySelector('.cart-widget .count');
    if (countElement) {
      countElement.textContent = payload.count;
    }

    const cartWidget = document.querySelector('.cart-widget');
    if (!cartWidget) {
      return;
    }

    cartWidget.classList.toggle('-has-items', payload.count > 0);
    const cartIcon = cartWidget.querySelector('.icon-btn img');
    if (cartIcon) {
      const icon = payload.count > 0 ? cartWidget.dataset.filledIcon : cartWidget.dataset.emptyIcon;
      if (icon) {
        cartIcon.setAttribute('src', icon);
      }
    }
  }

  //wir ShopTracker rendert hierüber ggf ShopTracker::EVENT_BASKET_AJAX raus -> scripte ausführen...
  _executeTracker(trackerHtml) {
    if (!trackerHtml || typeof trackerHtml !== 'string') {
      return;
    }

    const container = document.createElement('div');
    container.innerHTML = trackerHtml;

    const scripts = container.querySelectorAll('script');
    scripts.forEach((oldScript) => {
      const script = document.createElement('script');

      for (let i = 0; i < oldScript.attributes.length; i++) {
        const attr = oldScript.attributes[i];
        script.setAttribute(attr.name, attr.value);
      }

      script.text = oldScript.textContent || oldScript.innerText || '';
      document.head.appendChild(script);
      document.head.removeChild(script);
    });
  }

  _showSuccess(submitButton, html) {
    if(this.modal.outer)
      this.modal.close(new CustomEvent('null'));

    this.modal.show();

    $(this.modal.content).html(html);

    const element = document.querySelector(`.${this.rootClass} input.input[type="number"][name="menge"]`);
    const form = document.querySelector(".amount form.put_in_basket");

    element.addEventListener("change", event => {
      this._handleFormSubmit(event, form);
    });

    //ToastNotification.show('success', 'Der Artikel wurde erfolgreich in den Warenkorb gelegt.');
    if (submitButton) {
      this._scheduleButtonReset(submitButton, BTN_SUCCESS_HOLD_MS);
    }
  }

  _handleSubmitError(submitButton) {
    if (!submitButton) {
      return;
    }

    this._clearButtonTimeout(submitButton);
    this._resetButton(submitButton);
  }

  _prepareSubmitButton(button) {
    this._clearButtonTimeout(button);
    button.classList.add('is-adding');
    button.disabled = true;
    button.setAttribute('aria-busy', 'true');
  }

  _scheduleButtonReset(button, delay) {
    this._clearButtonTimeout(button);
    const timeout = window.setTimeout(() => {
      this._resetButton(button);
      this._buttonTimeouts.delete(button);
    }, delay);

    this._buttonTimeouts.set(button, timeout);
  }

  _clearButtonTimeout(button) {
    const timeout = this._buttonTimeouts.get(button);
    if (timeout) {
      window.clearTimeout(timeout);
      this._buttonTimeouts.delete(button);
    }
  }

  _resetButton(button) {
    button.classList.remove('is-adding');
    button.disabled = false;
    button.setAttribute('aria-busy', 'false');
  }

  _submitPaypal(form) {
    const params = this._serializeForm(form);
    params.set('modus', 'paypal');
    window.location.href = `/warenkorb/?${params.toString()}`;
  }

  _serializeForm(form) {
    const formData = new FormData(form);
    return new URLSearchParams(formData);
  }

  _setupQtyGroup(root) {
    const dec = root.querySelector('button[aria-label], .button:first-child');
    const inc = root.querySelector('button:last-child');
    if (!dec || !inc) {
      return;
    }

    if (!root.style.cursor) {
      root.style.cursor = 'pointer';
    }

    root.addEventListener('click', (event) => {
      if (event.target.closest('button') || event.target.closest('input')) {
        return;
      }

      const rect = root.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const input = root.querySelector('input[type="number"]');
      const before = input ? parseInt(input.value, 10) || 1 : null;

      if (x < rect.width / 2) {
        dec.click();
        if (input && (parseInt(input.value, 10) || 1) === before) {
          input.value = Math.max(1, before - 1);
        }
      } else {
        inc.click();
        if (input && (parseInt(input.value, 10) || 1) === before) {
          input.value = before + 1;
        }
      }

      input.dispatchEvent(new CustomEvent('change'));
    });

    if (!root.closest('.product-sticky-cta')) {
      const updateHover = (event) => {
        if (event && event.target && event.target.closest('input')) {
          root.classList.remove('-hover-left');
          root.classList.remove('-hover-right');
          return;
        }

        const rect = root.getBoundingClientRect();
        const x = event && typeof event.clientX === 'number'
          ? event.clientX - rect.left
          : rect.width / 2;

        if (x < rect.width / 2) {
          root.classList.add('-hover-left');
          root.classList.remove('-hover-right');
        } else {
          root.classList.add('-hover-right');
          root.classList.remove('-hover-left');
        }
      };

      root.addEventListener('mousemove', updateHover);
      root.addEventListener('mouseenter', updateHover);
      root.addEventListener('mouseleave', () => {
        root.classList.remove('-hover-left');
        root.classList.remove('-hover-right');
      });
    }
  }
}
