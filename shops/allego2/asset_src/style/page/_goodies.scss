.page-goodies {
  > .image-banner {
    background: #fff;
    z-index: -999;
    position: relative;
    border-radius: var(--card-radius);

    margin-bottom: var(--space-6);

    @include up-lg {
      margin-bottom: var(--space-8);
    }
  }

  > .image-banner > .viewport {
    @include down-md {
      --banner-height: clamp(355px, 85vw, 500px);
    }

    background: url(/assets/goodies.png) no-repeat;
    background-size: contain;

    > .content {
      > .title > span {
        display: block;
        background: linear-gradient(90deg, #DE5A13 0%, #F80 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        font-size: min(7vw, 100px);
        font-style: normal;
        font-weight: 700;
        line-height: 100%;

        &.subsublabel {
          font-size: min(2vw, 23px);
          font-weight: 500;
          line-height: 100%;
          padding-left: 10px;
        }
      }
    }

    @include smiley-banner-mask(
      $size-mobile: 136vw,
      $rotation-mobile: -45deg,

      $size-desktop: 687px,
      $rotation-desktop: -45deg,

      $gradient: orange,
      $opacity: 0.45,
      $pos-x-mobile: 22%,
      $pos-y-mobile: 40%,
      $pos-x-desktop: 50%,
      $pos-y-desktop: 50%,
      $breakpoint: 'md'
    );

    &:before {
      z-index: -99;
    }

    @include up-md {
      padding: 0 106px;
      justify-content: start;

      >.content {
        > .title > span {
          text-align: left;

          &.subsublabel span {
            display: inline;
          }
        }
      }

      &:before {
        left: -50%;
        top: -62%;
      }

      background: url(/assets/goodies.png) no-repeat 86%;
    }

    @include down-lg {
      padding: 0 20px;
    }

    @include down-md {
      background-position: 50% calc(100% - 10px);
      background-size: min(80%, 400px);
      align-items: start;
      padding: 50px 0;

      > .content > .title > span {
        font-size: max(7vw, 38px);

        &.subsublabel {
          padding-top: 10px;
          font-size: max(3vw, 16px);
          font-style: normal;
          font-weight: 700;

          > span {
            display: block;
            padding-top: 7px;
          }
        }
      }
    }
  }
  //
  //> .image-banner > .viewport > .content {
  //  color: var(--color-black);
  //}
  //
  //> .image-banner > .viewport > .content > .title {
  //  display: flex;
  //  flex-direction: column;
  //  align-items: center;
  //  gap: var(--space-2);
  //}
  //
  //> .image-banner > .viewport > .content > .title > .label {
  //  font-size: var(--font-size-h1);
  //  font-weight: var(--font-weight-bold);
  //  line-height: 1.1;
  //  letter-spacing: -0.02em;
  //
  //  @include up-lg {
  //    font-size: 64px;
  //  }
  //}
  //
  //> .image-banner > .viewport > .content > .title > .sublabel {
  //  font-size: var(--font-size-h2);
  //  font-weight: var(--font-weight-bold);
  //  line-height: 1.2;
  //
  //  @include up-lg {
  //    font-size: var(--font-size-h1);
  //  }
  //}

  > .goodies-intro {
    @include surface-card(var(--space-5));
    width: 100%;
    margin-bottom: var(--space-6);

    @include up-lg {
      margin-bottom: var(--space-8);
      padding: var(--space-6);
    }

    > .headline {
      font-size: var(--font-size-h3);
      font-weight: var(--font-weight-bold);
      line-height: var(--font-lineheight-h3);
      color: var(--color-text);
      margin: 0 0 var(--space-5);
    }

    > .description {
      font-size: var(--font-size-text1);
      font-weight: var(--font-weight-regular);
      line-height: var(--font-lineheight-text1);
      color: var(--color-text);
      margin: 0 0 var(--space-5);
    }

    > .search-form {
      position: relative;
      display: flex;
      align-items: stretch;
      width: 100%;
      background: var(--color-base);

      > .search-input {
        flex: 1;
        min-width: 0;
        padding: var(--space-4) var(--space-5);
        border: 1px solid var(--color-highlight);
        border-radius: var(--radius-2);
        background: var(--color-base);
        font-family: var(--font-sans);
        font-size: var(--font-size-h3);
        font-weight: var(--font-weight-regular);
        line-height: var(--font-lineheight-h3);
        color: var(--color-text);
        outline: none;

        &::placeholder {
          color: var(--color-text-secondary);
        }

        &:focus-visible {
          outline: var(--focus-ring-width) solid var(--focus-ring-color);
          outline-offset: calc(-1 * var(--focus-ring-width));
        }
      }

      > .search-btn {
        all: unset;
        box-sizing: border-box;
        position: absolute;
        right: var(--space-4);
        top: 50%;
        transform: translateY(-50%);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: var(--icon-size);
        height: var(--icon-size);
        cursor: pointer;

        &:focus-visible {
          @include focus-ring();
        }

        > svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  > .goodies-grid {
    @include tiles-grid-utility(
      $mobile-cols: 1,
      $tablet-cols: 2,
      $desktop-cols: 4
    );
    align-items: start;
    width: 100%;
  }

  > .more-goodies-btn {
    @include button-outline(var(--color-highlight), var(--color-highlight));
    display: flex;
    gap: var(--space-2);
    width: 100%;
    border-radius: var(--radius-2);
    box-shadow: var(--shadow-1);
    margin-left: auto;
    margin-right: auto;
    margin-bottom: var(--space-6);

    @include up-lg {
      max-width: var(--module-size);
      margin-bottom: var(--space-8);
    }

    > .icon {
      width: var(--icon-size);
      height: var(--icon-size);
    }
  }

  > .goodie-card-wrapper {
    display: contents;
  }

  > .info-section {
    width: 100%;
    display: block;
    margin-top: var(--space-6);

    @include up-lg {
      margin-top: var(--space-8);
    }
  }

  > .info-section h2 {
    margin-bottom: var(--space-5);
  }

  > .info-section h3 {
    margin-top: var(--space-5);
  }

  > .info-section p {
    color: var(--color-text);
    margin-bottom: var(--space-5);
  }

  > .info-section ul {
    margin-bottom: var(--space-5);
  }
}

.page-goodie-detail {
  --goodie-start: var(--color-highlight);
  --goodie-end: var(--color-highlight-2);

  > .goodie-hero {
    @include surface-card(var(--space-4));
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: var(--space-6);

    @include up-lg {
      padding: var(--space-4);
      margin-bottom: var(--space-8);
    }

    > .headline {
      font-size: var(--font-size-h1);
      font-weight: var(--font-weight-bold);
      line-height: var(--font-lineheight-h1);
      background: linear-gradient(90deg, var(--goodie-start) 0%, var(--goodie-end) 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      margin: 0 0 var(--space-5);
    }

    > .media {
      position: relative;
      width: 100%;
      aspect-ratio: 16 / 9;
      background-color: var(--color-surface-muted);
      border-radius: var(--radius-1);
      overflow: hidden;
      margin: 0 0 var(--space-5);

      > .iframe {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
      }
    }

    > .description-wrapper {
      position: relative;

      > .description {
        position: relative;

        > .text {
          @include description-style();
          margin: 0;
        }

        &.-faded {
          max-height: 250px;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(transparent 60%, var(--color-base));
            pointer-events: none;
          }
        }

        &.-expanded {
          max-height: none;
          overflow: visible;
        }
      }

      > .toggle-container {
        display: flex;
        justify-content: center;
        margin-top: var(--space-4);

        > .toggle-btn {
          @include button-outline(var(--color-black), var(--color-text));
          width: 100%;
          border-radius: var(--radius-2);
          box-shadow: var(--shadow-1);

          @media (hover: hover) and (pointer: fine) {
            &:hover > .text {
              text-decoration: underline;
            }
          }

          @include up-lg {
            max-width: var(--module-size);
          }
        }
      }
    }
  }

  &.-orange {
    --goodie-start: var(--color-highlight);
    --goodie-end: var(--color-highlight-2);
  }

  &.-blue {
    --goodie-start: var(--goodie-blue-start);
    --goodie-end: var(--goodie-blue-end);
  }

  &.-green {
    --goodie-start: var(--goodie-green-start);
    --goodie-end: var(--goodie-green-end);
  }

  &.-purple {
    --goodie-start: var(--goodie-purple-start);
    --goodie-end: var(--goodie-purple-end);
  }

  > .sidebar-layout {
    > .sidebar {
      > ._card { gap: var(--space-4); }
    }

    > .primary {
      > .section-title {
        font-size: var(--font-size-h2);
        font-weight: var(--font-weight-bold);
        line-height: var(--font-lineheight-h2);
        color: var(--color-text);
        margin: 0 0 var(--space-4);

        @include up-lg {
          margin-bottom: var(--space-5);
        }
      }
    }
  }

  > .sidebar-layout .filter-title {
    font-size: var(--font-size-text1);
    font-weight: var(--font-weight-bold);
    line-height: var(--font-lineheight-text1);
    color: var(--color-text);
    margin: 0;
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--color-black);
  }

  > .sidebar-layout .category-filters {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
  }

  > .sidebar-layout .category-filters > .category-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }

  > .sidebar-layout .category-filters > .category-group > .category-main {
    font-size: var(--font-size-text1);
    font-weight: var(--font-weight-bold);
    line-height: var(--font-lineheight-text1);
    color: var(--color-text);
  }

  > .sidebar-layout .category-filters .category-sub {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    padding-left: var(--space-3);
  }

  > .sidebar-layout .category-filters .category-sub-name {
    font-size: var(--font-size-text1);
    font-weight: var(--font-weight-bold);
    line-height: var(--font-lineheight-text1);
    color: var(--color-text);
  }

  > .sidebar-layout .category-filters .category-items {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    padding-left: var(--space-3);
  }

  > .sidebar-layout .category-filters .checkbox-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
  }

  > .sidebar-layout .category-filters .checkbox-option > .label {
    flex: 1;
    font-weight: var(--font-weight-regular);
  }

  /* Outer spacing for the Listing Filter component on Category pages */
  > .listing-filter,
  > .sidebar-layout > .primary > .listing-filter,
  > .sidebar-layout > .sidebar > .listing-filter {
    margin-block: var(--space-6); /* 32px on mobile */
    @include up-lg { margin-block: var(--space-5); } /* 24px on desktop */
  }
}
