$num-banners: 2;

.page-home {
  @include down-md {
    .goodies-list {
      > .goodie-card:nth-child(n+6) {
        display: none;
      }
    }
  }

  .sidebar-layout > .primary { order: -1; }
  @include up-lg { .sidebar-layout > .primary { order: 0; } }

  .sidebar-layout > .primary > .image-banner {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 342px;

    .banner {
      display: block;
      position: absolute;
      top: 0;
      left: 100%;
      width: 100%;
      height: 100%;
      border-radius: var(--card-radius);
      box-shadow: var(--card-shadow);
      animation: $num-banners*5s slide 10s infinite;
      text-decoration: none;
      overflow: hidden;

      > .viewport {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        padding: var(--space-6);

        > .content {
          position: relative;
          z-index: 2;
          color: white;

          @include down-lg {
            position: absolute;
            bottom: var(--space-4);
            right: var(--space-4);
            margin: 0;

            > .title {
              font-family: var(--font-sans);
              font-size: 16px;
              font-weight: var(--font-weight-bold);
              line-height: 1.2;
              margin: 0;
            }

            > .subtitle {
              display: none;
            }
          }

          @include up-lg {
            > .title {
              font-family: var(--font-sans);
              font-size: 100px;
              font-weight: var(--font-weight-bold);
              line-height: var(--font-lineheight-h1);
              margin: 0;
            }

            > .subtitle {
              font-family: var(--font-sans);
              font-size: 24px;
              font-weight: var(--font-weight-medium);
              margin-top: -12px;
              opacity: 0.9;
            }
          }
        }

        > .image {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: auto;
          z-index: 1;

          @include down-lg {
            left: 50%;
            transform: translate(-50%, -50%);
            max-height: 80%;
          }

          @include up-lg {
            right: var(--space-8);
            max-height: 100%;
          }
        }
      }
    }

    @for $i from 1 through $num-banners {
      .banner.banner#{$i} {
        animation-delay: #{($i - 1) * 5s };
      }
    }

    .banner.banner1 {
      > .viewport {
        background: var(--Highlight, #DE5A13);
        mix-blend-mode: multiply;

        @include smiley-banner-mask(
                $size-mobile: 800px,
                $size-desktop: 800px,
                $rotation-mobile: -45deg,
                $rotation-desktop: -45deg,
                $pos-x-mobile: 56%,
                $pos-y-mobile: 87%,
                $pos-x-desktop: 56%,
                $pos-y-desktop: 87%,
                $gradient: linear-gradient(45deg, #FFFFFF, #FF8800),
                $opacity: 0.3
        );

        > .content {
          justify-self: flex-start;
        }
      }
    }

    .banner.banner2 {
      > .viewport {
        background-color: #333; /* Schwarzer Hintergrund */

        @include smiley-banner-mask(
                $size-mobile: 800px,
                $size-desktop: 800px,
                $rotation-mobile: -45deg,
                $rotation-desktop: -45deg,
                $pos-x-mobile: 45%,
                $pos-y-mobile: 37%,
                $pos-x-desktop: 45%,
                $pos-y-desktop: 34%,
                $gradient: orange,
                $opacity: 1
        );

        > .content {
          justify-self: flex-end;
          margin-left: auto;
          text-align: left;
        }

        > .image {
          @include down-lg {
            left: 50%;
            transform: translate(-50%, -50%);
          }

          @include up-lg {
            right: auto;
            left: var(--space-8);
          }
        }
      }
    }
  }

  /* Slide-Animation */
  @keyframes slide {
    0% { left: 100%; opacity: 0; }
    #{percentage((10/$num-banners)/100)}, #{percentage((100/$num-banners - 10/$num-banners)/100)} { left: 0; opacity: 1; }
    #{percentage((100/$num-banners)/100)} { left: -100%; opacity: 0; }
    100% { left: -100%; opacity: 0; }
  }

  @keyframes slide-reviews {
    0% { left: 100%; opacity: 0; }
    1.5% { left: 0; opacity: 1; }
    31.5% { left: 0; opacity: 1; }
    33% { left: -100%; opacity: 0; }
    100% { left: -100%; opacity: 0; }
  }

  > .logo-bar {
    width: 100%;
    overflow: hidden;
    position: relative;
    mask-image: linear-gradient(to right, transparent, black 20%, black 80%, transparent);

    > .scroller {
      display: flex;
      width: max-content;
      gap: var(--logo-scroll-gap);
      animation: home-logo-scroll 30s linear infinite;

      > .logo {
        height: 40px;
        width: auto;
        max-width: var(--logo-width-max);
        object-fit: contain;
      }
    }

    @media (hover: hover) and (pointer: fine) { &:hover > .scroller { animation-play-state: paused; } }

    @include up-lg {
      overflow: visible;
      mask-image: none;

      > .scroller {
        animation: none;
        transform: none;
        width: 100%;
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }

  .info-section {
    width: 100%;
    display: block;

    h2 { margin-bottom: var(--space-5); }
    h3 { margin-top: var(--space-5); }
    p  { color: var(--color-text); margin-bottom: var(--space-5); }
    ul { margin-bottom: var(--space-5); }
  }

  .sidebar-layout > .sidebar > .home-categories {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    padding: 0;
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;

    > .item {
      @include surface-card(var(--space-4));
      display: flex;
      align-items: center;
      gap: var(--space-4);
      text-decoration: none;
      color: var(--color-text);
      @media (hover: hover) and (pointer: fine) { &:hover > .name { text-decoration: underline; } }

      > .icon    { width: var(--icon-size); height: var(--icon-size); flex: 0 0 auto; }
      > .name    { font-size: var(--font-size-h3); font-weight: var(--font-weight-bold); line-height: var(--font-lineheight-h3); color: var(--color-text); white-space: nowrap; }
      > .chevron { width: var(--icon-size); height: var(--icon-size); margin-left: auto; }
    }

    @include up-lg {
      padding: var(--space-4);
      gap: var(--space-5);
      background-color: var(--color-base);
      border-radius: var(--radius-2);
      box-shadow: var(--shadow-1);

      > .item {
        padding: 0;
        background: transparent;
        box-shadow: none;
        border-radius: 0;
      }
    }
  }

  > .reviews { width: 100%; }

  > .reviews > .panel {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    align-items: stretch;

    > .review-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 160px;

      > .logo {
        height: 92px;
        width: auto;
        max-width: 100%;
        object-fit: contain;
      }
    }

    @include up-lg {
      flex-direction: row;
      align-items: stretch;

      > .review-box {
        width: var(--module-size);
        min-width: var(--module-size);
        height: var(--module-size);
        flex: 0 0 var(--module-size);
      }

      > .banner { flex: 1 1 auto; min-width: 0; }
    }

    > .reviews-slider {
      position: relative;
      width: 100%;
      height: var(--module-size);
      overflow: hidden;
      border-radius: var(--card-radius);
    }

    > .reviews-slider > .reviews-banner {
      position: absolute;
      top: 0;
      left: 100%;
      width: 100%;
      height: 100%;
      animation: slide-reviews 24s infinite;
      border-radius: var(--card-radius);
      overflow: hidden;

      &.slide1 { animation-delay: 0s; }
      &.slide2 { animation-delay: 8s; }
      &.slide3 { animation-delay: 16s; }

      > picture {
        width: 100%;
        height: 100%;
        display: block;
      }

      > picture > .reviews-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center center;
        display: block;
        border-radius: var(--card-radius);
      }
    }
  }
}

@media (prefers-reduced-motion: reduce) { .page-home > .logo-bar > .scroller { animation: none; } }

@keyframes home-logo-scroll { to { transform: translateX(-50%); } }
