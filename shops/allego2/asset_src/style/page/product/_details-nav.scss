.page-product .product-details-nav {
  gap: var(--space-4);
  margin-block: 0;

  @include up-lg {
    gap: var(--space-5);
  }

  > .detail-group {
    @extend .accordion-item;
  }

  > .detail-group > .detail-nav-item {
    @include surface-card(var(--space-4));
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: var(--button-height);
    border: none;
    cursor: pointer;
    width: 100%;

    @media (hover: hover) and (pointer: fine) {
      &:hover {
        > .item-content > .item-left > .label {
          text-decoration: underline;
        }
      }
    }

    &:focus-visible {
      outline: var(--focus-ring-width) solid var(--focus-ring-color);
      outline-offset: var(--focus-ring-offset);
    }

    > .item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      min-height: var(--icon-size);

      > .item-left {
        display: flex;
        align-items: center;
        gap: var(--space-2);

        > .icon {
          width: var(--icon-size);
          height: var(--icon-size);
          flex-shrink: 0;
        }

        > .label {
          font-size: var(--font-size-h3);
          font-weight: var(--font-weight-bold);
          color: var(--color-text);
          line-height: var(--font-lineheight-h3);
          text-wrap: balance;
          text-align: left;
        }
      }

      > .arrow {
        width: var(--icon-size);
        height: var(--icon-size);
        flex-shrink: 0;
      }
    }
  }

  > .detail-group > .detail-nav-item[aria-expanded="true"] {
    > .item-content > .arrow {
      transform: rotate(90deg);
    }
  }

  > .detail-group > .detail-section {
    @include surface-card(0 var(--space-4));
    max-height: 0;
    overflow: hidden;
    margin: 0;
    opacity: 0;

    &.-active {
      max-height: 100%;
      padding: var(--space-4);
      margin-top: var(--space-5);
      opacity: 1;
      margin-bottom: 0;
    }

    > .section-content {
      font-size: var(--font-size-text1);
      color: var(--color-text);

      /* Heading tweaks for CMS h2 inside financing */
      .h2,
      .h2.cs {
        font-size: var(--font-size-text1);
        line-height: var(--font-lineheight-text1);
        font-weight: var(--font-weight-bold);
        margin: 0 0 var(--space-3);
        color: var(--color-text);

        > small {
          display: block;
          margin-top: var(--space-1);
          font-size: var(--font-size-text3);
          line-height: var(--font-lineheight-text3);
          color: var(--color-text-secondary);
          font-weight: var(--font-weight-medium);
        }
      }

      /* CMS text content and inline table (class .tg) */
      .text_content {
        color: var(--color-text);
        font-size: var(--font-size-text2);
        line-height: var(--font-lineheight-text2);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        > p { margin: 0 0 var(--space-3); }
      }

      // CMS embedded table (.tg) now uses utility class from utilities/_tables.scss

      /* Financing calculator controls */
      .calc-controls {
        align-items: flex-end;
        --gap: var(--space-3);

        > label {
          white-space: nowrap;
          color: var(--color-text);
        }

        > .form-field {
          flex: 0 0 300px;
          max-width: 320px;
        }

        @include up-lg {
          > .form-field { flex-basis: 240px; max-width: 260px; }
        }

        @include down-lg {
          .h2,
          .h2.cs { font-size: var(--font-size-text2); line-height: var(--font-lineheight-text2); margin-bottom: var(--space-2); }

          .text_content { font-size: var(--font-size-text3); line-height: var(--font-lineheight-text3); }
          .text_content .tg th,
          .text_content .tg td { padding: var(--space-2) var(--space-2); font-size: var(--font-size-text3); line-height: var(--font-lineheight-text3); }

          /* tighten gaps */
          > ._space-4 { block-size: var(--space-3); }

          flex-wrap: wrap;
          align-items: stretch;
          > .form-field { flex: 1 1 auto; max-width: 100%; }
          > .btn { width: 100%; }
        }
      }

      /* Financing section: table styles */
      .table-wrap {
        inline-size: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-top: var(--space-3);
      }

      // Financing table now uses utility class .finanz_table from utilities/_tables.scss
    }
  }

  > .detail-group > #section-related.detail-section {
    background: transparent;
    box-shadow: none;
    border-radius: 0;
    padding: 0;

    &.-active {
      display: grid;
      gap: var(--space-4);
      padding: 0;
      margin-top: var(--space-4);

      @include up-lg {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-5);
      }
    }

    &.-expandable {
      > .product-wrapper.-hidden { display: none; }
      &.is-expanded {
        > .product-wrapper.-hidden { display: block; }
      }
    }

    > .action.show-all-products {
      @include button-outline();
      width: 100%;
      max-width: 100%;
      margin-left: auto;
      margin-right: auto;

      @media (hover: hover) and (pointer: fine) {
        &:hover > .text { text-decoration: underline; }
      }

      > .text { pointer-events: none; }

      @include up-lg {
        display: flex;
        width: var(--module-size);
        max-width: var(--module-size);
      }
    }

    
  }

  > .detail-group > #section-bewertung.detail-section {
    background: transparent;
    box-shadow: none;
    border-radius: 0;
    padding: 0;

    &.-active {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      padding: 0;
      margin-top: var(--space-5);

      @include up-lg {
        gap: var(--space-5);
      }
    }
  }

  /* Shipping (Versand) section styles */
  > .detail-group > #section-shipping.detail-section {
    > .h2 {
      font-size: var(--font-size-text1);
      line-height: var(--font-lineheight-text1);
      font-weight: var(--font-weight-bold);
      margin: 0 0 var(--space-3);
      color: var(--color-text);
    }

    // Shipping table now uses utility class .table_normal.-shipping from utilities/_tables.scss
    > .versandkosten_table {
      display: block;
      width: 100%;
    }

    > .text_content {
      color: var(--color-text);
      font-size: var(--font-size-text2);
      line-height: var(--font-lineheight-text2);
      > p { margin: 0 0 var(--space-3); }

      @include down-lg {
        font-size: var(--font-size-text3);
        line-height: var(--font-lineheight-text3);
      }
    }
  }

  @include down-lg {
    > .detail-group > .detail-nav-item {
      padding: var(--space-4);

      > .item-content > .item-left > .label {
        font-size: var(--font-size-h3);
      }
    }

    > .detail-group > .detail-section {
      display: none;
      padding: var(--space-3);
    }

    > .detail-group > .detail-section.-active {
      display: block;
    }

    > .detail-group > #section-related.detail-section.-active {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @include down-md {
    > .detail-group > #section-related.detail-section.-active {
      grid-template-columns: 1fr;
    }
  }
}
