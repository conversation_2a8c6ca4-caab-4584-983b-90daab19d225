.page-product .product-tabs {
  margin-block: 0;

  > .nav {
    display: flex;
    flex-direction: row;
    gap: var(--space-5);
    overflow-x: auto;
  }

  > .nav > .tab {
    @include surface-card(var(--space-4));
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--space-2-5);
    height: var(--button-height);
    min-height: var(--button-height);
    flex: 1 0 0;
    border: none;
    color: var(--color-text);
    font-size: var(--font-size-text1);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    text-decoration: none;

    @media (hover: hover) and (pointer: fine) {
      &:hover { > .label { text-decoration: underline; } }
    }

    &.-active {
      > .label {
        text-decoration: underline;
      }
    }

    > .icon {
      width: var(--icon-size);
      height: var(--icon-size);
      flex-shrink: 0;
    }

    > .label {
      font-size: var(--font-size-text1);
      font-weight: var(--font-weight-bold);
      line-height: var(--font-lineheight-text1);
      color: inherit;
    }
  }

  > .content {
    margin-top: var(--space-5);

    > .panel {
      display: none;
      padding: 0;

      > .heading-2 {
        margin-bottom: var(--space-4);
        color: var(--color-text);
      }

      > .product-tabs__products {
        display: grid;
        gap: var(--space-4);

        @include up-lg {
          grid-template-columns: repeat(3, 1fr);
          gap: var(--space-5);
        }
      }

      &.-active {
        display: block;
        padding: var(--space-5);
      }
    }

    > .panel.-blank {
      &.-active {
        background: transparent;
        box-shadow: none;
        border-radius: 0;
        padding: 0;
      }

      > .heading-2 {
        margin-bottom: var(--space-4);
      }
    }

    > #product-tab-devices > ._card > #device_list_container > .device_list {
        margin-top: var(--space-4);
        max-height: calc(var(--module-size) * 1.5);
        overflow: auto;
      }

    // Device list table now uses utility class .table_normal.-devices
    // See utilities/_tables.scss

    // Product description table now uses utility class .table_normal
    // See utilities/_tables.scss
  }

  @include down-lg {
    > .nav {
      flex-direction: column;
      gap: var(--space-4);
      overflow-x: visible;
    }

    > .nav > .tab {
      width: 100%;
      flex: 0 0 auto;
    }
  }
}
