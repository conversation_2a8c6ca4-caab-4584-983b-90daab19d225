.page-product #section-services .services-box {
  width: 100%;
  max-width: none;
  background: transparent;
  border: 0;
  border-radius: 0;
  box-shadow: none;
  padding: var(--space-4) 0;

  > .foot {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);

    @include up-lg {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }

    > .cart-action {
      order: 2;

      @include up-lg {
        order: 1;
      }

      > .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        width: 100%;

        @include up-lg {
          width: auto;
        }

        > .logo {
          width: 24px;
          height: 24px;
          flex-shrink: 0;
        }
      }
    }

    > .pricing-info {
      order: 1;
      text-align: right;

      display: grid;
      grid-template-columns: auto max-content;
      column-gap: 10px;

      > .vat-note {
        grid-column: 2;
      }

      @include up-lg {
        order: 2;
      }
    }
  }
}

.page-product #section-services .services-box .toggle.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-product #section-services .services-box .service-row:has(.toggle.disabled) {
  > .label > .name,
  > .price {
    color: var(--color-selection);
  }
}
