.page-category {
  > .sidebar-layout {
    > .primary {
      display: flex;
      flex-direction: column;
      gap: var(--space-5);

      > .category-short-description {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
        /* override _surface default padding to match previous design */
        padding: var(--space-4);

        > .title {
          margin: 0;
          color: var(--color-highlight);
        }

        > .text {
          margin: 0;
          color: var(--color-text);
          /* Normalize CMS content spacing inside the description */
          > * { margin: 0; }
          > * + * { margin-top: var(--space-3); }
        }
      }

      > .category-cards-grid {
        display: block;
        
        > .grid {
          @include tiles-grid-utility(
            $mobile-cols: 2,
            $desktop-cols: 3
          );

          > .card {
            box-sizing: border-box;
            @include surface-card(var(--space-4));
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: var(--space-4);
            color: var(--color-text);
            text-decoration: none;
            @media (hover: hover) and (pointer: fine) { &:hover > .name { text-decoration: underline; } }

            &:focus-visible {
              outline: var(--focus-ring-width) solid var(--focus-ring-color);
              outline-offset: var(--focus-ring-offset);
            }

            > .image {
              display: block;
              width: var(--icon-size-xl);  /* 50px on mobile */
              height: var(--icon-size-xl);
              aspect-ratio: 1;
              object-fit: contain;
              flex-shrink: 0;
            }

            @include up-lg {
              > .image {
                width: var(--icon-size-xxl); /* 100px on desktop */
                height: var(--icon-size-xxl);
              }
            }

            > .name {
              display: block;
              color: var(--color-text);
              text-align: center;
              leading-trim: both;
              text-edge: cap;
              /* Text1 (16 / 140%) */
              font-family: var(--font-sans);
              font-size: var(--font-size-text1);
              font-style: normal;
              font-weight: var(--font-weight-medium);
              line-height: var(--font-lineheight-text1);
              text-wrap: pretty;
              overflow-wrap: anywhere; /* break long words */
              word-break: break-word;
              hyphens: auto;
            }
          }

          @include down-lg {
            > a.card:last-of-type:nth-of-type(odd) {
              grid-column: 1 / -1;
            }
          }

          @include up-lg {
            > .card.-extra { display: none; }
          }

          > .action {
            @include button-outline();
            display: none;
            width: 100%;
            &:hover > .text { text-decoration: underline; }

            > .text { pointer-events: none; }

            @include up-lg { display: inline-flex; grid-column: 2 / span 1; justify-self: stretch; }
          }
        }

        &.is-expanded {
          > .grid {
            @include up-lg {
              > .card.-extra { display: flex; }
              > a.card:last-of-type:nth-of-type(odd) { grid-column: auto; }
            }
          }
        }
      }
    }
  }
  > .sidebar-layout {
    @include down-lg {
      align-items: stretch;
      > * { width: 100%; }

      > .sidebar,
      > .primary { display: contents; }


      /* 1. SEO short description */
      .category-short-description { order: 10; }

      /* 2. Subcategory cards */
      .category-cards-grid { order: 20; }

      /* 3. Goodies section (when not inside #product-list) */
      .goodies-list { order: 30; }

      /* 4. Filters sidebar */
      .listing-filter { order: 40; }

      /* 5. Product list container (includes goodies + products when filters exist) */
      #product-list { order: 50; }

      /* 6. Products section (when rendered without wrapper) */
      .top-products { order: 60; }

      /* 7. SEO long description */
      .category-long-description { order: 70; }
    }
  }

  #top-products {
    //scrollIntoView + stiycky header offset fix
    scroll-margin-top: 250px;
  }

  .device-search-widget {
    margin-top: var(--space-4);
    margin-bottom: var(--space-4);

    @include up-lg {
      margin-top: var(--space-5);
      margin-bottom: 0;
    }
  }

  @include down-lg { .listing-filter { display: block; } }

  > .goodies-list,
  > .sidebar-layout > .primary > .goodies-list {
    margin-bottom: var(--space-6);

    @include up-lg {
      margin-bottom: var(--space-8);
    }
    /* Global .action.more-goodies styles are defined in utilities/_more-goodies-action.scss */
  }

  > .category-long-description,
  > .sidebar-layout > .primary > .category-long-description {
    display: block;
    margin-top: var(--space-6);
    > .text { color: var(--color-text); > * { margin: 0; } > * + * { margin-top: var(--space-3); } }
    @include up-lg { margin-top: var(--space-8); }
  }

  > .section-title,
  > .sidebar-layout > .primary > .section-title { 
    margin-top: var(--space-6); 
    margin-bottom: var(--space-4); 
  }
  @include up-lg { 
    > .section-title,
    > .sidebar-layout > .primary > .section-title { 
      margin-top: var(--space-8); 
      margin-bottom: var(--space-5); 
    } 
  }

  /* Outer spacing for the Listing Filter component on Category pages */
  > .listing-filter,
  > .sidebar-layout > .primary > .listing-filter,
  > .sidebar-layout > .sidebar > .listing-filter {
    margin-block: var(--space-6); /* 32px on mobile */
    @include up-lg { margin-block: var(--space-5); } /* 24px on desktop */
  }
}
