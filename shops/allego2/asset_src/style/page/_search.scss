.page-search {
  > .image-banner.-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: var(--space-6) 0;
    
    > .viewport {
      @include smiley-banner-mask(
        $size-mobile: 320px,
        $size-desktop: 800px,
        $rotation-mobile: 25deg,
        $rotation-desktop: 25deg,
        $gradient: linear-gradient(135deg, #e9e9e9 0%, #f1f1f1 100%),
        $opacity: 1,
        $pos-x-mobile: 50%,
        $pos-y-mobile: 50%,
        $pos-x-desktop: 55%,
        $pos-y-desktop: 50%
      );

      height: var(--module-size);
      background: var(--color-base);
      border-radius: var(--radius-2);
      box-shadow: var(--shadow-1);
    }

    > .viewport > .content {
      max-width: 520px;
      width: 100%;
      padding-inline: var(--space-4);
      color: var(--color-black);
      gap: var(--space-4);
      text-align: center;
    }

    > .viewport > .content > .title {
      margin: 0;
    }

    > .viewport > .content > .title > .label {
      font-size: var(--font-size-h1);
      font-weight: var(--font-weight-bold);
      line-height: 1.1;
      letter-spacing: -0.01em;
    }

    > .viewport > .content > .heading-2 {
      margin: 0;
    }

    > .viewport > .content > span:not(.heading-2) {
      font-size: var(--font-size-text1);
      font-weight: var(--font-weight-medium);
      line-height: var(--font-lineheight-text1);
      color: var(--color-text);
    }

    > .viewport > .content > .action.primary {
      @include button-primary();
      width: 100%;
      max-width: var(--button-width);
    }
  }

  > .content {
    padding: var(--space-4);
    text-align: center;
  }

  > .sidebar-layout > .primary > .content {
    > .search-heading {
      font-family: var(--font-sans);
      font-size: 24px;
      font-weight: var(--font-weight-bold);
      line-height: 1.4;
      color: var(--color-text);
      margin: 0 0 var(--space-5) 0;
      text-align: center;
    }
  }
}
