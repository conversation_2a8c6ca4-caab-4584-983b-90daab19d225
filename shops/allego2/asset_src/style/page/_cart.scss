.page-cart {
  .basket-empty {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);

    @include up-lg { gap: var(--space-5); }

    > .image-banner.-empty {
      > .viewport {
        @include smiley-banner-mask(
                $size-mobile: 300px,
                $size-desktop: 1200px,
                $rotation-mobile: 45deg,
                $rotation-desktop: 25deg,
                $gradient: orange,
                $opacity: 0.45,
                $pos-x-mobile: 50%,
                $pos-y-mobile: 50%,
                $pos-x-desktop: 50%,
                $pos-y-desktop: 50%,
        );

        background-color: var(--color-base);
        box-shadow: var(--shadow-1);
        border-radius: var(--radius-2);

        > .content {
          color: var(--color-text);
          #basket-empty-heading {
            font-family: var(--font-sans);
            font-size: var(--font-size-h1);
            font-weight: var(--font-weight-bold);
            line-height: var(--font-lineheight-h1);
            text-align: center;
            color: var(--color-text);
          }
        }
      }
    }

    > ._surface {
      display: flex;
      flex-direction: column;
      gap: var(--space-5);
      padding: var(--space-4);

      @include up-lg { gap: var(--space-5); }

      > .text-bold {
        font-family: var(--font-sans);
        font-size: var(--font-size-text1);
        font-weight: var(--font-weight-bold);
        line-height: var(--font-lineheight-text1);
        color: var(--color-text);
      }

      > .text-1 {
        font-family: var(--font-sans);
        font-size: var(--font-size-text1);
        font-weight: var(--font-weight-medium);
        line-height: var(--font-lineheight-text1);
        color: var(--color-text);
      }

      > .actions {
        @include button-bar-utility(var(--space-5));
        width: 100%;

        > .action {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: var(--button-gap);
          flex: 1 0 0;
          width: 100%;
          text-decoration: none;
          min-height: var(--button-height);

          > .icon { width: var(--icon-size); height: var(--icon-size); flex-shrink: 0; }
          > .text { font-size: var(--font-size-h3); font-weight: var(--font-weight-bold); line-height: var(--font-lineheight-h3); }
        }

        > .action.back    { @include button-outline(); gap: var(--space-2); }
        > .action.primary { @include button-primary(); }
        > .action.goodies { @include button-outline(var(--color-highlight), var(--color-highlight)); gap: var(--space-2); }

        > .action,
        > .action.back,
        > .action.primary,
        > .action.goodies { flex: 1 1 0; width: 100%; min-height: var(--button-height); }

        > .action.goodies > .text.-gradient { color: inherit; -webkit-text-fill-color: initial; background: none; }
      }
    }
  }

  > .container > .container_product > .product_main > .section-title {
    font-family: var(--font-sans);
    font-size: var(--font-size-h2);
    font-weight: var(--font-weight-bold);
    line-height: var(--font-lineheight-h2);
    color: var(--color-text);
    margin-bottom: var(--space-5);
  }

  > .crossselling {
    .show-all-products {
      &.-hidden {
        display: none;
      }

      border-radius: 16px;
      border: 2px solid var(--color-text);
      width: 342px;
      height: 56px;
      padding: 16px;
      text-align: center;

      display: block;
      margin: 25px auto;

      color: var(--color-text);
      font-size: var(--font-size-text1);
      font-weight: 700;

      text-decoration: none;
      &:visited,
      &:hover,
      &:active {
        text-decoration: none;
      }

      &:hover {
        cursor: pointer;
      }
    }
  }

  .pay-now {
    text-decoration: none;
    > .button.-primary {
      background-color: #349357 !important;
    }
  }

  .payment-methods {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-2);

    @include down-md {
      justify-content: start;
    }

    img {
      width: 76px;
      height: 32px;
      object-fit: contain;

      border-radius: 10px;
      padding: 8px;
      display: inline;
      background-color: var(--color-bg);

      &.americanexpress {
        background-color: #0077A6;
      }
    }
  }
}

.page-cart {
  .cart-list {
    > .cart-item-card + .cart-item-card { margin-top: var(--space-5); }
    @include up-lg { > .cart-item-card + .cart-item-card { margin-top: var(--space-5); } }
    margin-bottom: var(--space-5);
  }
}

.page-cart .cart-summary {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--color-bg);
    > .container {
      width: 100%;
      max-width: 1440px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      > ._card { --card-padding: var(--space-4); --card-gap: var(--space-4); width: 100%; }
    }

    .breakdown {
      display: flex;
      flex-direction: column;
      gap: var(--space-5);
    }

    .line-items {
      display: flex;
      flex-direction: column;
      gap: var(--space-3);
      margin: 0;

      > .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--space-3);

        > .label {
          font-family: var(--font-sans);
          font-weight: var(--font-weight-medium);
          font-size: var(--font-size-text1);
          line-height: var(--font-lineheight-text1);
          color: var(--color-text);
          display: flex;
          align-items: center;
          gap: var(--space-2);
        }

        > .value {
          font-family: var(--font-sans);
          font-weight: var(--font-weight-medium);
          font-size: var(--font-size-text1);
          line-height: var(--font-lineheight-text1);
          color: var(--color-text);
          white-space: nowrap;
        }

        &.-total {
          > .label,
          > .value { font-weight: var(--font-weight-bold); }
        }

        &.-secondary {
          > .label,
          > .value { font-weight: var(--font-weight-medium); }
        }
      }
    }

    .info-button {
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      color: var(--color-text);

      &:hover {
        color: var(--color-highlight);
        transform: scale(1.1);
      }

      &:active { transform: scale(0.95); }

      &:focus-visible { outline: var(--focus-ring-width) solid var(--focus-ring-color); outline-offset: var(--focus-ring-offset); border-radius: 4px; }

      > .icon { width: 24px; height: 24px; display: block; }

      @media (prefers-reduced-motion: reduce) {
        transition: none;
        &:hover { transform: none; }
      }
    }

    .divider { width: 100%; height: 1px; background-color: var(--color-selection); border: none; }

    .actions {
      @include button-bar-utility(var(--space-5));
      width: 100%;

      @include down-lg {
        gap: var(--space-4);
      }

    }

    .paypal-row {
      width: 100%;

      @include up-lg {
        .later {
          padding-left: 50%;
        }
      }

      .later {
        width: 100%;
        margin: 5px;
      }
    }

    .button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-2);
      padding: var(--space-4);
      border-radius: var(--button-radius);
      border: none;
      cursor: pointer;
      font-family: var(--font-sans);
      font-weight: var(--font-weight-bold);
      font-size: var(--font-size-h3);
      line-height: var(--font-lineheight-h3);
      min-height: var(--button-height);
      max-height: 55px;

          > .text { display: inline-flex; align-items: center; }
          > .icon { width: 24px; height: 24px; display: block; }
          > .logo { width: 90px; height: 24px; display: block; }

          &.-primary {
            background-color: var(--color-highlight);
            text-decoration: none;
            color: var(--color-base);
            box-shadow: var(--shadow-button);
            &:active { transform: translateY(1px); }
            &:focus-visible { outline: var(--focus-ring-width) solid var(--focus-ring-color); outline-offset: var(--focus-ring-offset); }
          }

          &.-paypal {
            background-color: #ffc43a;
            color: var(--color-text);
            &:active { transform: translateY(1px); }
            &:focus-visible { outline: var(--focus-ring-width) solid var(--focus-ring-color); outline-offset: var(--focus-ring-offset); }
          }

      @media (prefers-reduced-motion: reduce) {
        transition: none;
        &:hover { transform: none; }
      }
    }

    @include down-lg {

      > .container > .card { padding: var(--space-3); gap: var(--space-4); }
      .button { width: 100%; }
      .line-items { gap: var(--space-3); }
      .breakdown { gap: var(--space-4); }
    }

    @media (prefers-contrast: high) {
      .button.-primary,
      .button.-paypal { border: 2px solid var(--color-text); }
      .divider { background-color: var(--color-text); }
    }

    *:focus-visible { outline: var(--focus-ring-width) solid var(--focus-ring-color); outline-offset: var(--focus-ring-offset); }
  }
