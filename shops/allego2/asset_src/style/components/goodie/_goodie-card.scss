.goodie-card {
  --goodie-start: var(--color-highlight);
  --goodie-end: var(--color-highlight-2);

  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 18px;
  width: 100%;
  background-color: var(--color-base);
  border-radius: var(--radius-2);
  box-shadow: var(--shadow-1);
  text-decoration: none;
  color: inherit;

  > .bar {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--space-5);
    min-height: 44px;
    cursor: pointer;
    user-select: none;

    > .brand {
      display: inline-flex;
      align-items: center;
      gap: var(--space-4);

      &:focus-visible {
        outline: var(--focus-ring-width) solid var(--focus-ring-color);
        outline-offset: var(--focus-ring-offset);
        border-radius: var(--radius-1);
      }

      > .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 42px;
        height: 42px;
        flex-shrink: 0;
        padding: var(--space-3);
        border-radius: 12px;
        box-shadow: var(--shadow-1);
        background: linear-gradient(90deg, var(--goodie-start) 0%, var(--goodie-end) 100%);

        > .icon { width: 23px; height: 19px; }
      }

      > .title {
        background: linear-gradient(90deg, var(--goodie-start) 0%, var(--goodie-end) 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-size: 20px;
        font-weight: var(--font-weight-bold);
        line-height: 1;
        max-width: 100%;
        max-height: 44px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        word-break: normal;

        &::after {
          content: "";
          display: block;
          height: 0;
          opacity: 0;
          margin-top: 2px;
          background: linear-gradient(90deg, var(--goodie-start) 0%, var(--goodie-end) 100%);
          transition: none;
        }
      }
    }

    > .nav {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      margin-left: auto;

      > .chevron,
      > .icon {
        width: 100%;
        height: 100%;
        transition: none;
        transform: rotate(0deg);
      }

      

      &:focus-visible {
        outline: var(--focus-ring-width) solid var(--focus-ring-color);
        outline-offset: var(--focus-ring-offset);
        border-radius: var(--radius-1);
      }
    }
  }

  > .media {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
    background-color: var(--color-surface-muted);
    border-radius: var(--radius-1);
    overflow: hidden;

    > .iframe {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;

      &.-desktop { display: none; }
      &.-mobile  { display: block; }

      @include up-lg {
        &.-desktop { display: block; }
        &.-mobile  { display: none; }
      }
    }
  }

  > .desc > .text {
    font-size: var(--font-size-text1);
    font-weight: var(--font-weight-medium);
    line-height: var(--font-lineheight-text1);
    color: var(--color-text);
    letter-spacing: -0.02em;
  }

  > .collapsible {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: none;
    margin: 0;
    padding: 0;
  }

  > input.exp:checked ~ .collapsible {
    max-height: 1000px;
    opacity: 1;
    margin: 0;
    padding: 0;
  }

  > input.exp:checked ~ .media { margin-top: var(--space-5); }
  > input.exp:checked ~ .desc  { margin-top: var(--space-4); }

  > input.exp:checked ~ .bar > .nav > .chevron,
  > input.exp:checked ~ .bar > .nav > .icon {
    transform: rotate(90deg);
  }

  // goodie hover underline
  //&:hover > .bar > .brand > .title::after {
  //  height: 2px;
  //  opacity: 1;
  //}

  &.-link {}

  &.-toggle {
    > .bar > .switch {
      position: relative;
      width: 24px;
      height: 24px;
      border-radius: 6px;
      border: 2px solid var(--color-selection);
      background-color: var(--color-base);
      box-shadow: var(--shadow-1);
      cursor: pointer;

      &::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 50%;
        width: 12px;
        height: 10px;
        transform: translate(-50%, -50%);
        opacity: 0;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 12px 10px;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="10" viewBox="0 0 12 10" fill="none"><path d="M4 9.4L0 5.4L1.4 4L4 6.6L10.6 0L12 1.4L4 9.4Z" fill="white"/></svg>');
      }
    }

    > .toggle:checked ~ .bar > .switch {
      border-color: var(--color-black);
      background-color: var(--color-black);

      &::after {
        opacity: 1;
        border-left-color: white;
        border-bottom-color: white;
      }
    }
  }

  &.-orange { --goodie-start: var(--color-highlight); --goodie-end: var(--color-highlight-2); }
  &.-blue   { --goodie-start: var(--goodie-blue-start);   --goodie-end: var(--goodie-blue-end); }
  &.-green  { --goodie-start: var(--goodie-green-start);  --goodie-end: var(--goodie-green-end); }
  &.-purple { --goodie-start: var(--goodie-purple-start); --goodie-end: var(--goodie-purple-end); }

  &.-disabled {
    background-color: #ccc;
    &> .checkbox-option > .goodie-filter-checkbox + .checkmark {
      background-color: #eee;
    }
  }

  > .bar,
  > .bar *,
  > .media,
  > .desc { user-select: none; }

  > .bar > .brand { text-decoration: none; }

  .goodie-more-btn {
    display: flex;
    height: 56px;
    padding: 16px;
    align-self: stretch;

    margin-top: 10px;

    color: var(--color-base);
    font-size: var(--font-size-text1);
    font-weight: 700;
    justify-content: center;
    text-decoration: none;
    border-radius: 16px;
    border: 2px solid var(--color-text);
    background: var(--color-text);
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.02), 0 6px 12px 0 rgba(0, 0, 0, 0.03);
  }
}
