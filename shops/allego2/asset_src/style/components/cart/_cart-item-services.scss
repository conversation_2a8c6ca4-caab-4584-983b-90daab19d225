.services-box {
  align-self: start;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-4);
  width: 100%;
  max-width: none;
  background-color: var(--color-base);
  border: 1px solid var(--color-border-strong);
  border-radius: var(--radius-2);
  box-shadow: var(--shadow-1);
  color: var(--color-text);
  @include up-lg { width: var(--module-size); }

  > .head {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    > .title-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--space-4);
      > .title { margin: 0; font-family: var(--font-sans); font-size: var(--font-size-h4); font-weight: var(--font-weight-bold); line-height: var(--font-lineheight-h4); }
      > .info { all: unset; cursor: pointer; display: inline-flex; align-items: center; justify-content: center; width: var(--icon-size); height: var(--icon-size); border-radius: var(--radius-1); &:focus-visible { outline: 2px solid var(--color-highlight); outline-offset: 2px; } > .icon { width: 100%; height: 100%; display: block; } }
    }
    > .subtitle { margin: 0; font-size: var(--font-size-text3); font-weight: var(--font-weight-medium); color: var(--color-text-secondary); }
  }

  > .services-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);

    > .notice-text {
      color: var(--color-text-secondary); //#626262
      font-weight: 700;
      font-size: var(--font-size-text1);
    }
  }

  .service-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-4);
    > .label { display: inline-flex; align-items: center; gap: var(--space-3); }
    > .price { font-size: var(--font-size-text1); font-weight: var(--font-weight-medium); line-height: var(--font-lineheight-text1); color: var(--color-text); white-space: nowrap; }
  }

  .name {
    all: unset;
    cursor: pointer;
    font-family: var(--font-sans);
    font-size: var(--font-size-text1);
    font-weight: var(--font-weight-bold);
    line-height: var(--font-lineheight-text1);
    color: var(--color-text);
    &:focus-visible { outline: 2px solid var(--color-highlight); outline-offset: 2px; }
    > .variant { font-weight: var(--font-weight-regular); margin-left: var(--space-2); }
  }

  > .foot {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-2);
    > .sum { font-size: var(--font-size-text1); font-weight: var(--font-weight-bold); }
    > .vat-note { font-size: var(--font-size-text3); color: var(--color-text); }
  }
  .service-row.-blocked {
    > .checkbox-option > .checkmark { border: 2px solid var(--color-selection); }
  }

  .checkbox-option .checkmark {
    border: 2px solid #000;
  }
}

.extra_services_description {
  > .extra_service_description {
    margin-bottom: 5px;
    > .title {
      font-size: 20px;
      padding: 15px 0;
    }
  }
}
