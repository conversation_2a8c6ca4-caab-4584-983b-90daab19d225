.product-card {
  @import "product-base";

  display: grid;
  gap: var(--space-4);
  grid-template-areas:
      "pdb pdb"
      "media meta"
      "badges badges"
      "title title"
      "details details"
      "actions actions"
  ;

  grid-template-rows: 24px 150px 32px 75px 56px 65px;
  grid-template-columns: repeat(2, 1fr);

  padding: var(--space-4);
  background-color: var(--color-base);
  border-radius: var(--radius-2);
  box-shadow: var(--shadow-1);
  color: var(--color-text);
  width: 100%;

  > .media {
    grid-area: media;

    img {
      max-height: 100%;
      object-fit: contain;
    }
  }

  > .meta {
    grid-area: meta;
    display: flex;
    flex-flow: column;
    gap: var(--space-2);
    align-items: end;

    > .brand-slot .brand {
      max-width: 100px;
      object-fit: contain;
    }

    > .delivery-info {
      font-size: var(--font-size-text2);

      display: flex;
      align-items: center;
      gap: 15px;

      > .icon {
        width: 24px;
      }

      > .text {
        width: 100px;
        word-break: break-all;
      }
    }

    > .extra-services {
      display: flex;
      gap: 15px;
      align-items: flex-start;

      > .text {
        width: 100px;
        font-size: 11px;
      }
    }
  }

  .text {
    color: var(--color-text) !important;
    font-size: var(--font-size-text2);
    font-weight: 700;
  }

  > .pdb {
    grid-area: pdb;
    display: flex;
    justify-content: space-between;

    > a, a:visited, a:active {
      color: var(--color-text-secondary);
      font-size: var(--font-size-text3);
      font-style: normal;
      font-weight: 500;
      .text {
        color: inherit !important;
        font-size: inherit;
        font-style: inherit;
        font-weight: inherit;
      }
    }
  }

  > .badges {
    grid-area: badges;
    display: flex;
    justify-content: space-between;

    .badge {
      border-radius: 8px;
      border: 2px solid var(--color-highlight);
      background: var(--color-base);
      height: 32px;
      margin: auto 0;

      color: var(--color-text);
      font-size: var(--font-size-text3);
      font-weight: 500;

      line-height: 28px;
      padding: 0 8px;
    }
  }

  > .title {
    grid-area: title;

    > .title-link {
      color: var(--color-text);
      font-size: var(--font-size-text2);
      font-weight: 700;
      text-decoration: none;

      &:visited,
      &:hover,
      &:active {
        text-decoration: none;
      }
    }
  }

  > .details {
    grid-area: details;
    display: flex;
    justify-content: space-between;
    flex-direction: row;

    > .price {
      grid-area: price;
      justify-self: end;

      color: var(--color-highlight);
      font-size: 24px;
      font-weight: 700;
    }

    > .codes {
      font-size: var(--font-size-text3);
      line-height: 150%;
      min-width: 50%;
    }

    > .price {
      text-align: right;
    }
  }

  > .actions {
    grid-area: actions;
  }
}
