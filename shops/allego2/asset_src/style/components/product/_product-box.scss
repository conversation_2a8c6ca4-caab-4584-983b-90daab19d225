/* ====================================================================== */
/* Product card                                                           */
/* ====================================================================== */

.product-box {
  /* local tokens just for this component */
  --thumbs-w: 56px;    /* width reserved for the vertical thumbnail rail */
  --thumb-size: 48px;  /* square size of each thumbnail */
  --dots-size: 12px;   /* size of mobile pager dots */

  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--space-5);
  padding: var(--space-4);
  background-color: var(--color-base);
  border-radius: var(--radius-2);
  box-shadow: var(--shadow-1);
  color: var(--color-text);
  width: 100%;

  @include up-lg {
    flex-direction: row;
    align-items: stretch;
    gap: var(--space-5);
    padding: var(--space-4);
  }

  /* ───────────────────────── media (image + thumbs + dots) ───────────── */

  > .media {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    @include up-lg {
      width: var(--module-size);
      min-width: var(--module-size);
      flex: 0 0 var(--module-size);
      align-items: center;
      justify-content: center;
    }

    > .photo {
      width: 200px;
      max-width: 100%;
      height: auto;
      aspect-ratio: 1;
      object-fit: cover;

      @include up-lg { width: var(--module-size); }
    }

    /* horizontal thumbs below image, hidden by default; shown with layout below */
    > .thumbs {
      display: none;

      @include up-lg {
        display: flex;
        flex-direction: row;
        gap: var(--space-3);
        justify-content: center;
      }

      .thumb {
        display: flex;
        align-items: center;
        justify-content: center;
        width: var(--thumb-size);
        height: var(--thumb-size);
        aspect-ratio: 1;
        border-radius: var(--radius-1);
        border: 2px solid transparent;
        background-color: var(--color-base);
        box-shadow: var(--shadow-1);
        overflow: hidden;
        cursor: pointer;
        transition: transform .15s ease, box-shadow .15s ease;
        will-change: transform;

        @media (hover: hover) and (pointer: fine) {
          &:hover { transform: scale(1.08); box-shadow: var(--shadow-elevated); }
        }
        &[aria-current="true"] { border-color: transparent; }

        > img { width: 100%; height: 100%; object-fit: cover; display: block; }

        &.thumb-hidden {
          display: none;
        }
      }
    }

    > .dots {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-2);
      list-style: none;
      margin-top: var(--space-3);
      padding: 0;

      > .dot {
        width: var(--dots-size);
        height: var(--dots-size);
        border-radius: 50%;
        border: 2px solid var(--color-highlight);
      }

      > .dot[aria-current="true"] {
        background-color: var(--color-text);
        border-color: var(--color-text);
      }
    }

    @include up-lg { > .dots { display: none; } }
  }

  /* Stack thumbs below hero image when they exist.
     Use either the class `.has-thumbs` (safest) or the :has() selector (modern). */
  > .media.has-thumbs,
  > .media:has(> .thumbs) {
    @include up-lg {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      align-items: center;
    }
  }

  /* ───────────────────────────── details (right column) ───────────────── */

  @import "product-base";

  > .head > .bar,
  > .details > .head > .bar {
    /* mobile: label left, links right; badges on its own row */
    @include down-lg {
      display: grid;
      grid-template-columns: auto 1fr;
      justify-content: space-between;
      align-items: center;
      gap: var(--space-4);

      > .badges { grid-column: 1 / -1; }
      > .label  { justify-self: start; }
      > .links  { justify-self: end; }
    }

    /* new links group (GPSR + Produktdatenblatt) */
    > .links {
      display: inline-flex;
      align-items: center;
      gap: var(--space-5);

      .gpsr,
      .doc {
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        color: inherit;

        > .text {
          font-size: var(--font-size-text3);
          font-weight: var(--font-weight-medium);
          line-height: var(--font-lineheight-text3);
          color: var(--color-text-secondary);
          text-decoration: underline;
          white-space: nowrap;
        }
      }
    }
  }

  > .info,
  > .details > .info {
    @include up-lg {
      grid-template-columns:
        minmax(auto, max-content)
        minmax(auto, max-content)
        minmax(max-content, 1fr);
    }

    > .price > .finanzierung {
      .finanzierung-link {
        display: inline-flex;
        align-items: center;
        color: var(--color-text-secondary);
        font-size: var(--font-size-text2);
        font-weight: var(--font-weight-medium);
        line-height: var(--font-lineheight-text2);
        transition: text-decoration 0.2s ease;
        &:hover { text-decoration: none; }

        > span {
          color: inherit;
        }
      }
    }
  }

  > .actions > .action {
    /* handle both .icon and .logo children */
    > .icon,
    > .logo { width: var(--icon-size); height: var(--icon-size); flex: 0 0 auto; }
  }

  /* mobile grid placement + button text hiding (unchanged) */
  @include down-lg {
    display: grid;
    gap: var(--space-5);
    grid-template-areas:
      "head"
      "media"
      "info"
      "actions";
    > .details { display: contents; }
    > .head,
    > .details > .head { grid-area: head; }
    > .media { grid-area: media; }
    > .info,
    > .details > .info { grid-area: info; }
    > .actions,
    > .details > .actions { grid-area: actions; }
    > .actions > .action > .text,
    > .details > .actions > .action > .text { display: none; }

    > .actions,
    > .details > .actions { 
      flex-direction: row; 
      align-items: center;
      flex-wrap: wrap;
    }

    > .actions > .qty,
    > .details > .actions > .qty {
      flex: 0 0 auto;
    }

    > .actions > .action,
    > .details > .actions > .action { 
      flex: 1 1 auto; 
      min-width: 0;
      width: auto;
      max-width: none;
    }
  }

  @include up-lg {
    display: flex;
    > .details { display: flex; }
  }

  /* ───────────────────────────── PayPal buttons row ───────────────── */
  > .actions,
  > .details > .actions {
    @include up-lg {
      flex-wrap: wrap;
      align-items: flex-start;
    }

    > .qty {
      @include up-lg {
        flex: 0 0 auto;
      }
    }

    > .action {
      @include up-lg {
        flex: 1 1 auto;
        max-width: calc(100% - 177px);
      }
    }

    .paypal-row {
      //height ist hier bewusst festgesetzt, damit das layout nicht springt.
      //-> risiko: wenn paypal da inhalte weglässt, haben wir damit weißraum.

      width: 100%;
      @include up-lg {
        max-width: calc(100% - 177px);

        .later {
          padding-left: 50%;
        }
        .direct {
          height: 55px ! important;
        }
      }

      .direct {
        height: 140px;
        overflow: hidden;
      }

      .later {
        height: 50px;
        width: 100%;
        margin: 5px;
        overflow: hidden;
      }
    }

    .payment-methods {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: var(--space-2);
      width: 100%;

      @include up-lg {
        max-width: calc(100% - 177px);
      }

      img {
        width: 76px;
        height: 32px;
        object-fit: contain;
        border-radius: 10px;
        padding: 8px;
        display: inline;
        background-color: var(--color-bg);

        &.americanexpress {
          background-color: #0077A6;
        }
      }
    }
  }
}
