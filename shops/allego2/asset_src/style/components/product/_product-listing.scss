.top-products {
    display: grid;
    gap: 24px;

    &.-expandable {
        > .product-wrapper.-hidden { display: none; }
        &.is-expanded { > .product-wrapper.-hidden { display: block; } }
    }

    > .action.show-all-products {
        @include button-outline();
        width: 100%;
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
        @media (hover: hover) and (pointer: fine) { &:hover > .text { text-decoration: underline; } }

        > .text { pointer-events: none; }

        @include up-lg {
            display: flex;
            width: var(--module-size);
            max-width: var(--module-size);
        }
    }

    /* Match vertical spacing to product cards */
    > .product-wrapper + .action.show-all-products,
    > .product-card + .action.show-all-products { margin-top: var(--space-4); }

    @include up-lg {
        grid-template-columns: repeat(3, 1fr);

        &.-full-row {
            grid-template-columns: repeat(4, 1fr);
        }

        > .product-wrapper + .action.show-all-products,
        > .product-card + .action.show-all-products { margin-top: var(--space-5); }
    }

    @include down-lg {
        grid-template-columns: repeat(2, 1fr);
    }

    @include down-md {
        grid-template-columns: 1fr;
    }
}
