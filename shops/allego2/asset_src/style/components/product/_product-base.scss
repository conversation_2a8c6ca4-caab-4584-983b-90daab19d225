/* Common nested rules for product components. Include INSIDE the component root. */

> .details {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--space-5);
  flex: 1 1 auto;
  width: 100%;

  @include up-lg {
    justify-content: space-between;
    min-height: 100%;
  }
}

> .head,
> .details > .head {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);

  @include up-lg { gap: var(--space-5); }

  > .bar {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);

    @include up-lg {
      display: grid;
      grid-template-columns: 1fr auto auto;
      align-items: center;
      gap: var(--space-5);
    }

    > .badges {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: var(--space-3);

      @include up-lg { gap: var(--space-5); }

      > .badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: var(--space-2);
        height: var(--space-6);
        border-radius: var(--radius-1);
        border: 2px solid var(--color-highlight);
        background-color: var(--color-base);

        > .text,
        > .text-bold {
          font-size: var(--font-size-text2);
          font-weight: var(--font-weight-medium);
          line-height: var(--font-lineheight-text2);
          color: var(--color-text);
          white-space: nowrap;
        }
      }
    }

    > .label {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      color: inherit;
      line-height: 0;
    }

    > .doc {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      gap: var(--space-5);
      align-self: stretch;
      text-decoration: none;
      color: inherit;

      @include up-lg {
        display: inline-flex;
        justify-content: flex-start;
        align-self: auto;
        width: auto;
      }

      > .text {
        font-size: var(--font-size-text3);
        font-weight: var(--font-weight-medium);
        line-height: var(--font-lineheight-text3);
        color: var(--color-text-secondary);
        text-decoration: underline;
        white-space: nowrap;
      }
    }
  }

  > .title {
    margin: 0;
    line-height: 100%;

    > .title-link {
      color: inherit;
      text-decoration: none;

      &:visited,
      &:hover,
      &:active {
        color: inherit;
        text-decoration: none;
      }
    }
  }
}

> .info,
> .details > .info {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: var(--space-4);
  row-gap: var(--space-4);
  width: 100%;

  > .price { grid-column: 1 / -1; }

  @include up-lg {
    align-items: start;
    gap: var(--space-5);
    > .price { grid-column: auto; }
  }

  > .brand-slot {
    min-height: 24px;
    display: flex;
    align-items: flex-start;

    > .brand { width: auto; height: 24px; }
  }

  > .meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-4);
    text-align: right;

    @include up-lg {
      align-items: flex-start;
      text-align: left;
      > .stars { margin-right: 0; }
    }

    > .stars {
      display: flex;
      align-items: center;

      > .star {
        width: var(--icon-size);
        height: var(--icon-size);
        aspect-ratio: 1;
      }
    }

    > .codes {
      font-size: var(--font-size-text2);
      font-weight: var(--font-weight-medium);
      line-height: var(--font-lineheight-text2);
      color: var(--color-text);
    }
  }

  > .price {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
    width: 100%;

    @include up-lg {
      width: auto;
      align-items: flex-end;
    }

    > .amount {
      font-size: 24px;
      font-weight: var(--font-weight-bold);
      line-height: 1.2;
      color: var(--color-highlight);
      letter-spacing: -0.02em;
      white-space: nowrap;

      @include up-lg { font-size: var(--product-price-size, 32px); }
    }

    > .note {
      margin: 0;
      font-size: var(--font-size-text2);
      line-height: var(--font-lineheight-text2);
      color: var(--color-text-secondary);
      text-align: right;
    }
    > .note a {
      color: var(--color-text-secondary);
      &:hover { text-decoration: none; }
    }

    > .avail {
      display: flex;
      align-items: center;
      gap: var(--space-3);

      > .icon {
        width: var(--icon-size);
        height: var(--icon-size);
        flex: 0 0 auto;
      }

      > .text {
        font-size: var(--font-size-text1);
        font-weight: var(--font-weight-bold);
        line-height: var(--font-lineheight-text1);
        color: var(--color-text);
        white-space: nowrap;
      }
    }
  }
}

> .actions,
> .details > .actions {
  display: flex;
  width: 100%;

  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: var(--space-5);
  margin-top: 0;

  > .qty {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    height: var(--button-height);
    padding-inline: var(--space-5);
    border-radius: var(--button-radius);
    border: 2px solid var(--color-border-strong);
    background-color: var(--color-base);
    box-shadow: var(--shadow-1);
    align-self: center;
    user-select: none;

    & * { user-select: none; }

    @include up-lg { align-self: auto; }

    > .button {
      all: unset;
      box-sizing: border-box;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-h3);
      font-weight: var(--font-weight-bold);
      color: var(--color-text);
      line-height: var(--font-lineheight-h3);
      &:hover { color: var(--color-highlight); }
    }

    > .input {
      all: unset;
      box-sizing: border-box;
      width: auto;
      -webkit-appearance: none;
      -moz-appearance: textfield;
      appearance: textfield;
      min-width: var(--qty-input-min-width);
      max-width: var(--qty-input-max-width);
      text-align: center;
      font-size: var(--font-size-h3);
      font-weight: var(--font-weight-bold);
      color: var(--color-text);
      line-height: var(--font-lineheight-h3);
      background: transparent;

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
    }
    &.-hover-left > .button:first-child { color: var(--color-highlight); }
    &.-hover-right > .button:last-child { color: var(--color-highlight); }
  }

  > .action {
    --btn-anim-fade: 500ms;
    @include button-primary();
    gap: var(--button-gap);
    padding-inline: var(--space-4);
    width: 100%;

    @include up-lg {
      flex: 1 1 auto;
      max-width: none;
    }

    > .icon {
      width: var(--icon-size);
      height: var(--icon-size);
      flex: 0 0 auto;
    }

    > .text { color: var(--color-base); }

    @include add-to-cart-cta-anim();
  }
}

.product-grid {
  display: grid;
  gap: 24px;
  margin: 25px 0;
  grid-template-columns: repeat(4, 1fr);

  @include down-lg {
    grid-template-columns: 1fr;
  }
}