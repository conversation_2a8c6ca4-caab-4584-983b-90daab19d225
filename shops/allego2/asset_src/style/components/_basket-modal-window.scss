.basket-modal-window {
    border-radius: 10px;
    height: unset !important;
    max-width: 708px;

    width: calc(100% - 30px) !important;
    margin: 15px;

    .window_content {
        padding-top: 45px !important;
        display: grid;
        gap: 16px;
    }

    .modalWindowInner_scroller {
        margin: 0;
    }

    .banner {
        grid-template-columns: max-content auto;
        border-radius: 16px;
        background: var(--color-confirm-box);
        color: var(--color-confirm);
        font-weight: 700;
        display: grid;
        padding: 16px;
        gap: 10px;
        align-self: stretch;

        &.success {
            &:before {
                content: " ";
                background: url(/assets/check_circle.svg) no-repeat;
                display: inline;
                width: 24px;
                height: 24px;
            }
        }
    }

    .products {
        .product {
            display: grid;
            gap: 16px;
            grid-template-areas:
                "image title title"
                "image details amount"
                "image lieferzeit amount"
                "image lieferzeit price"
            ;

            grid-template-rows: max-content max-content max-content 1fr;

            > .image {
                grid-area: image;
                max-height: 200px;
                object-fit: contain;
            }

            > .title {
                grid-area: title;
                font-size: 14px;
                font-weight: 700;
                display: flex;
                align-items: center;
            }

            > .details {
                grid-area: details;
                font-size: 12px;
                font-weight: 500;
                line-height: var(--font-lineheight-text1);
            }

            > .lieferzeit {
                grid-area: lieferzeit;
                display: flex;
                place-items: flex-start;
                gap: 5px;

                > .text {
                    color: var(--color-text) !important;
                    font-weight: 700;
                }
            }

            > .amount {
                grid-area: amount;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .qty {
                    align-self: flex-end !important;
                }

                @import "product/product-base";
            }

            > .price {
                grid-area: price;
                grid-template-rows: max-content max-content 1fr;
                display: grid;
                justify-content: end;
                text-align: right;
                gap: 10px;
                line-height: 100%;
            }

            @include down-lg {
                grid-template-areas:
                    "title title"
                    "lieferzeit details"
                    "image amount"
                    "image price"
                ;

                .details {
                    text-align: right;
                }

                .price {
                    gap: 8px;
                }
            }
        }
    }

    .foot {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);

        .button {
            border-radius: 16px;
            border: 2px solid var(--color-text);
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.02), 0 6px 12px 0 rgba(0, 0, 0, 0.03);

            color: var(--color-text);

            text-decoration: none;
            &:hover {
                text-decoration: none;
                cursor: pointer;
            }

            display: flex;
            gap: 8px;
            height: 56px;
            min-height: 56px;
            padding: 16px;
            justify-content: center;
            align-items: center;
            flex: 1 0 0;

            font-size: var(--font-size-text1);
            font-weight: 700;

            &:not(.proceed) {
                &:before {
                    content: " ";
                    mask-image: url(/assets/arrow_forward_ios.svg);
                    background-color: var(--color-text);
                    transform: rotate(180deg);
                    width: 24px;
                    height: 24px;
                }
            }

            &.proceed {
                color: var(--color-base);
                border: 0 solid var(--color-confirm);
                background: var(--color-confirm);

                &:after {
                    content: " ";
                    background: url(/assets/arrow_forward_ios.svg);
                    width: 24px;
                    height: 24px;
                }

                @include down-lg {
                    grid-row: 1;
                }
            }
        }
    }

    @include down-lg {
        .products {
            .product {
                row-gap: 16px;
                column-gap: 0;

                > .image {
                    max-height: 150px;
                }
            }
        }
        .foot {
            grid-template-columns: 1fr;
        }
    }
}