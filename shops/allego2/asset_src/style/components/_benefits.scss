.benefits-bar {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-4);
    width: 100%;

    > .item {
        background-color: var(--color-base);
        border-radius: var(--radius-2);
        box-shadow: var(--shadow-1);
        padding: var(--space-4);

        display: grid;
        grid-template-rows: repeat(3, min-content);
        gap: var(--space-4);
        text-align: left;

        > .item > .icon { width: 64px; height: 64px; }

        > .label {
            font-size: 24px;
            font-weight: 700;
            line-height: var(--font-lineheight-text1);
            color: var(--color-text);
        }
    }

    @include up-lg {
        grid-template-columns: repeat(5, 1fr);
        gap: var(--gap);
    }
}