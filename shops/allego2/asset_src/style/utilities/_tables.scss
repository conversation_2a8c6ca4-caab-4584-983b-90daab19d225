/* Standard table styling - reusable across all product sections */
.table_normal,
.tg,
.finanz_table {
  width: 100%;
  border: 1px solid var(--color-border-weak);
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed;

  th, td {
    padding: var(--space-2);
    border-bottom: 1px solid var(--color-border-weak);
    word-break: break-word;
    hyphens: auto;
    font-size: var(--font-size-text2);
    line-height: var(--font-lineheight-text2);
  }

  th {
    background: var(--color-surface-muted);
    font-weight: var(--font-weight-bold);
    text-align: left;
    border-right: 1px solid #d9d9d9;
    &:last-child { border-right: none; }
  }

  td {
    padding-block: var(--space-3);
    &:not(:last-child) { border-right: 1px solid var(--color-border-weak); }
  }

  tr:last-child td { border-bottom: none; }

  @include down-lg {
    th { 
      padding: var(--space-1);
      font-size: var(--font-size-text3);
    }
    td { 
      padding: var(--space-2);
      font-size: var(--font-size-text3);
    }
  }
}

/* Technical specs table */
.table_normal.-technical {
  td:first-child { border-right: 1px solid var(--color-border-weak); }
  td:last-child { width: 30%; }
  
  small {
    display: block;
    margin-top: var(--space-1);
    font-size: var(--font-size-text3);
    color: var(--color-text-secondary);
  }

  th[colspan="2"] {
    padding: var(--space-4) var(--space-2);
    font-size: var(--font-size-h3);
    border-top: 1px solid var(--color-border-weak);
    border-bottom: none;
    @include down-lg { 
      padding: var(--space-3) var(--space-2);
      font-size: var(--font-size-text2);
    }
  }

  tr:has(+ tr:has(th[colspan="2"])) td { border-bottom: none; }
  tr:has(th[colspan="2"]):first-child th { border-top: none; }
}

/* Device list table - _tables.scss weil es eine tabelle ist?!?! man */
.table_normal.-devices {
  th:nth-child(1), td:nth-child(1) { width: 20%; }
  th:nth-child(2), td:nth-child(2) { width: 35%; }
  th:nth-child(3), td:nth-child(3) { width: 25%; }
  th:nth-child(4), td:nth-child(4) { width: 20%; }

  td:nth-child(4) {
    text-align: center;
    color: var(--color-error)
  }

  .compatible td:nth-child(4) {
     color: var(--color-confirm);
  }

  td:nth-child(1),
  td:nth-child(2),
  td:nth-child(3) { border-right: 1px solid var(--color-border-weak); }

  thead .label-mobile { display: none; }
  thead .label-desktop { display: inline; }
  thead th { position: sticky; top: 0; z-index: 1; }
  
  @include down-lg {
    thead .label-mobile { display: inline; }
    thead .label-desktop { display: none; }
  }
}

/* Financing table */
.finanz_table {
  min-width: 520px;
  @include down-lg { min-width: 0; }
}

/* Shipping table */
.table_normal.-shipping {
  td.right { text-align: right; }
  tr.blue_sub td {
    background: var(--color-surface-muted);
    font-weight: var(--font-weight-medium);
  }
}

/* CMS content table */
.tg th:not(:last-child) { border-right: 1px solid #d9d9d9; }

/* Table wrapper for horizontal scroll */
.table-wrap {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-top: var(--space-3);
}
