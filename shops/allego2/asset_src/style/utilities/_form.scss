.form-field {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.form-field > .input,
.form-field > .select,
.form-field > .textarea {
  width: 100%;
  padding: var(--button-padding);
  border: 1px solid var(--color-border-strong);
  border-radius: var(--radius-2);
  box-shadow: var(--shadow-1);
  background: transparent;
  color: var(--color-text);
  font-size: var(--font-size-text1);
  font-weight: var(--font-weight-medium);
}

.form-field > .input,
.form-field > .select {
  height: var(--button-height);
}

.form-field > .textarea {
  min-height: calc(var(--button-height) * 2);
  resize: vertical;
}

.form-field > .input::placeholder,
.form-field > .textarea::placeholder {
  color: var(--color-text-secondary);
}

.form-field.-with-hint > .hint {
  font-size: var(--font-size-text3);
  color: var(--color-text-secondary);
  padding: 0 var(--button-padding);
  text-align: left;
}

.form-field > .input:focus-visible,
.form-field > .select:focus-visible,
.form-field > .textarea:focus-visible {
  outline: none;
  border-width: 2px;
}

.form-field.error > .input,
.form-field.error > .select,
.form-field.error > .textarea {
  border-color: var(--color-error);
}

.form-field > .error {
  color: var(--color-error);
  font-size: var(--font-size-text3);
  font-weight: var(--font-weight-medium);
  line-height: var(--font-lineheight-text3);
  padding: 0 var(--button-padding);
  background: none;
  border-radius: 0;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
  padding: 0;
  margin: 0;
  border: 0;
}

.radio-group > .legend {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.radio-group > .option {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  cursor: pointer;
  width: 100%;
}
.radio-group > .option > .input {
  position: absolute;
  opacity: 0;
  width: 24px;
  height: 24px;
  cursor: pointer;
}
.radio-group > .option > .indicator {
  position: relative;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  border-radius: 12px;
  border: 2px solid var(--color-selection);
  margin-top: 2px;
}
.radio-group > .option > .input:checked + .indicator::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--color-text);
  border-radius: 7px;
}
.radio-group > .option > .label {
  font-size: var(--font-size-text1);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}
.radio-group > .option > .meta {
  color: var(--color-text);
  font-size: var(--font-size-text1);
  font-weight: var(--font-weight-medium);
}
.radio-group > .option > .logo {
  height: 20px;
  width: auto;
  flex-shrink: 0;
}

.radio-group > .option > .input:focus-visible + .indicator {
  @include focus-ring();
}

.radio {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}
input[type="radio"] {
  width: 24px;
  height: 24px;
  accent-color: var(--color-text);
}
input[type="checkbox"] {
  width: 24px;
  height: 24px;
  accent-color: var(--color-text);
}

.checkbox-option {
  display: grid;
  grid-template-columns: min-content auto;
  align-items: flex-start;
  column-gap: var(--space-2);
  cursor: pointer;

  > .sublabel {
    color: var(--color-text-secondary);
    font-size: var(--font-size-text3);
    line-height: var(--font-lineheight-text3);
    grid-column: 2;
  }
}

.checkbox-option > .checkbox-input {
  position: absolute;
  opacity: 0;
  width: 24px;
  height: 24px;
}

.checkbox-option > .checkmark {
  position: relative;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  border: 2px solid var(--color-selection);
  border-radius: 5px;
  background: var(--color-base);
}

.checkbox-option > .checkbox-input:focus-visible + .checkmark {
  @include focus-ring();
}

.checkbox-option > .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 5px;
  width: 10px;
  height: 6px;
  border: solid var(--color-base);
  border-width: 0 0 2px 2px;
  transform: rotate(-45deg);
  display: none;
}

.checkbox-option > .checkbox-input:checked + .checkmark {
  background-color: var(--color-text);
  border-color: var(--color-text);
}

.checkbox-option > .checkbox-input:checked + .checkmark::after {
  display: block;
}

.checkbox-option > .label {
  font-size: var(--font-size-text1);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* legacy form row support (older checkout templates) */
.std_form .form_row { position: relative; }
