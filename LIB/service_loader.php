<?php

use bqp\Config\ConfigRegistry;
use bqp\db\db_generic;
use bqp\Event\EventDispatcher;
use bqp\Exceptions\DevException;
use bqp\KeyValueStore\KeyValueStoreDb;
use bqp\Lock\RuntimeLock;
use bqp\Lock\RuntimeLockMysql;
use bqp\Lock\RuntimeLockWithInSeriesErrorCounter;
use bqp\Pdf\WkhtmltopdfTolerant;
use bqp\storage\StorageFactory;
use bqp\url_parameter_signature;
use DI\ContainerBuilder;
use League\Flysystem\FilesystemNotFoundException;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use wws\business_structure\Shop;
use wws\Mails\MailAttachmentManager;
use wws\Order\OrderMailProfile;
use wws\Order\OrderRepository;
use wws\Product\Media\ProductMediaFileService;
use wws\Product\ProductRepository;
use wws\ShopBackend\UrlShortener;
use wws\Users\UserAuthenticator;

/**
 * Class service_loader
 *
 * Schweinerei-Klasse die sich um die Initialisierung kümmert
 * derzeigt existiert kein di konzept, daher notdürftig hierrein, damit sit das refactoring auch einfacher
 *
 * ggf. kann die Klasse auch als statische Facade umgesetzt werden
 */
class service_loader
{
    /**
     * @var \DI\Container
     */
    private static $di_container;

    /**
     * @return \DI\Container
     */
    public static function getDiContainer()
    {
        if (!self::$di_container) {
            $builder = new ContainerBuilder();

            $system_config = self::getConfigRegistry()->get('system');

            if (!$system_config->existsAndTrue('debug')) {
//                $cache = new Doctrine\Common\Cache\FilesystemCache(config::system('temp_dir').'/doctrine/');
//                $builder->setDefinitionCache($cache);
//                $builder->enableCompilation(config::system('temp_dir').'/doctrine/');
            }

            $builder->addDefinitions([
                'db.market_view' => function () {
                    return db::getInstance('market_view');
                },
                db_generic::class => function () {
                    return db::getInstance();
                },
                'db' => function () {
                    return db::getInstance();
                },
                ConfigRegistry::class => function () {
                    return service_loader::getConfigRegistry();
                }
            ]);

            foreach ($system_config->getArray('di_definitions') as $file) {
                $builder->addDefinitions($file);
            }

            $builder->useAutowiring(true);

            self::$di_container = $builder->build();
        }

        return self::$di_container;
    }

    public static function get(string $name)
    {
        return self::getDiContainer()->get($name);
    }

    /**
     * @return \bqp\Pdf\Wkhtmltopdf
     */
    public static function wkhtmltopdf()
    {
        $obj = new WkhtmltopdfTolerant();
        $obj->setMaxRetries(3);

        $bin = config::system('wkhtmltopdf_bin');
        if ($bin) {
            $obj->setBin($bin);
        }

        $zoom = config::system('wkhtmltopdf_zoom');
        if ($zoom) {
            $obj->setZoom($zoom);
        }

        return $obj;
    }

    /**
     * @return \bqp\url_parameter_signature
     */
    public static function urlParameterSignature()
    {
        $obj = new url_parameter_signature();

        $obj->setSecret(config::system('gateway_secret'));

        return $obj;
    }

    /**
     * @return \wws\ShopBackend\UrlShortener
     */
    public static function urlShortener(int $shop_id): UrlShortener
    {
        if ($shop_id == Shop::ERSATZTEILSHOP) {
            return new UrlShortener(db::getInstance(), 'https://ebay.ersatzteilshop.de/'); //ebay dereinfachheit halber, die requests landen in shops/ebay/
        }

        return new UrlShortener(db::getInstance(), config::shop_1('https') . '/');
    }

    /**
     * @return \wws\Users\UserAuthenticator
     */
    public static function userAuthenticator()
    {
        $authenticator = new UserAuthenticator();
        $authenticator->setAppSalt(config::system('app_salt'));
        $authenticator->setDb(db::getInstance());

        return $authenticator;
    }

    public static function xendPythonProxyAdapter()
    {
        $proxy = new bqp\python_proxy();
        $proxy->connect('127.0.0.1', 9999);

        $adapter = new xzend_http_proxy_node();
        $adapter->setHttpProxyNode($proxy);

        return $adapter;
    }

    public static function runtimeLock(): RuntimeLock
    {
        $db = db::getInstance();

        $runtime_lock_mysql = new RuntimeLockMysql($db);
        $store = new KeyValueStoreDb($db, 'runtime');

        return new RuntimeLockWithInSeriesErrorCounter($runtime_lock_mysql, $store);
    }

    /**
     * @return \wws\Mails\MailAttachmentManager
     */
    public static function getMailAttachmentManager(): MailAttachmentManager
    {
        static $service = null;

        if ($service === null) {
            $storage = self::getStorageFactory()->get('mail');

            try {
                $storage->getFilesystem('mail');
            } catch (FilesystemNotFoundException $e) {
                throw new DevException('mail:// filesystem is mandatory');
            }

            $service = new MailAttachmentManager($storage);
        }

        return $service;
    }

    public static function getSymfonyMailer(string $sender_mail = ''): Mailer
    {
        $dsn = null;

        if ($sender_mail) {
            $db = db::getInstance();

            $dsn = $db->fieldQuery("
                    SELECT
                        mailsystem_accounts.dsn
                    FROM
                        mailsystem_accounts
                    WHERE
                        mailsystem_accounts.email = '" . $db->escape($sender_mail) . "'
                    LIMIT
                        1
            ");
        }

        if (!$dsn) {
            $dsn = config::getLegacy('system', 'smtp');
        }

        $transport = Transport::fromDsn($dsn);
        $mailer = new Mailer($transport);
        return $mailer;
    }

    /**
     * @return \wws\Order\OrderMailProfile
     */
    public static function getOrderMailProfile(): OrderMailProfile
    {
        $order_mail_profile = new OrderMailProfile();

        return $order_mail_profile;
    }

    /**
     * @return \bqp\storage\StorageFactory
     */
    public static function getStorageFactory(): StorageFactory
    {
        static $factory = null;
        if ($factory === null) {
            $factory = new StorageFactory();
        }

        return $factory;
    }

    /**
     * @return ProductRepository
     */
    public static function getProductRepository(): ProductRepository
    {
        return self::get(ProductRepository::class);
    }

    /**
     * @return \wws\Order\OrderRepository
     */
    public static function getOrderRepository(): OrderRepository
    {
        static $repository = null;

        if ($repository === null) {
            $repository = new OrderRepository(db::getInstance());
        }

        return $repository;
    }

    /**
     * @return ProductMediaFileService
     */
    public static function getProductMediaFileService(): ProductMediaFileService
    {
        return self::getDiContainer()->get(ProductMediaFileService::class);
    }

    public static function getConfigRegistry(): ConfigRegistry
    {
        return ConfigRegistry::getInstance();
    }

    public static function getEventDispatcher(): EventDispatcher
    {
        return self::getDiContainer()->get(EventDispatcher::class);
    }
}
