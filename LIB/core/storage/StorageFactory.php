<?php

namespace bqp\storage;

use League\Flysystem\Filesystem;

class StorageFactory
{

    /**
     * erzeugt ein storage_mount_manger mit den übergebenen $storage_ids
     *
     * @param string ...$storage_id
     * @return storage_mount_manager
     **/
    public function get(string ...$storage_ids): storage_mount_manager
    {
        $manager = new storage_mount_manager();

        foreach ($storage_ids as $storage_id) {
            $this->mount($manager, $storage_id);
        }

        return $manager;
    }

    /**
     * lädt das entsprechenden $strorage_id filesystem und mounted ihn im $manager
     *
     * @param storage_mount_manager $manager
     * @param $storage_id
     */
    public function mount(storage_mount_manager $manager, string $storage_id): void
    {
        $config = $this->getStorageConfig($storage_id);
        $adapter = $this->loadStorageFilesystemByConfig($config);

        if (isset($config['url_creator'])) {
            $url_creator = $config['url_creator']();

            $manager->addUrlCreator($config['prefix'], $url_creator);
        }

        $manager->mountFilesystem($config['prefix'], $adapter);
    }

    public function getStorageConfig(string $storage_id): array
    {
        $config = \config::getLegacy('storage', $storage_id);

        if (!$config) {
            throw new \bqp\Exceptions\DevException('unknown storage_id');
        }

        return $config;
    }

    public function createAdapterByConfig(array $config): \League\Flysystem\AdapterInterface
    {
        switch ($config['adapter_config']['adapter']) {
            case 'local':
                $adapter = new \League\Flysystem\Adapter\Local(
                    $config['adapter_config']['root'],
                    LOCK_EX,
                    \League\Flysystem\Adapter\Local::DISALLOW_LINKS,
                    [
                        'file' => [
                            'public' => 0664,
                            'private' => 0600,
                        ],
                        'dir' => [
                            'public' => 0775,
                            'private' => 0700,
                        ],
                    ]
                );
                break;
            case 'ftp':
                $adapter = new \League\Flysystem\Adapter\Ftp($config['adapter_config']['config']);
                break;
            case 'callback':
                $adapter = call_user_func($config['adapter_config']['callback']);
                break;
            default:
                throw new \bqp\Exceptions\DevException('invalid adapter_config');
        }

        return $adapter;
    }

    /**
     * @param array $config
     * @return Filesystem
     */
    public function loadStorageFilesystemByConfig(array $config): Filesystem
    {
        $adapter = $this->createAdapterByConfig($config);

        $flysystem = new Filesystem($adapter);

        return $flysystem;
    }
}
