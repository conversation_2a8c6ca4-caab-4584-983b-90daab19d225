<?php

namespace bqp\Flysystem;

use League\Flysystem\AdapterInterface;
use League\Flysystem\Config;
use League\Flysystem\FileNotFoundException;

/**
 * OverlayAdapter - schichtet zwei Adapter übereinander. Layer- und Base-Adapter.
 * Der Base-Adapter ist read-only. Schreiboperationen erfolgen nur auf dem Layer-Adapter.
 * Bei Lesezugriffen wird zuerst der Layer-Adapter angesprochen und wenn die Datei nicht existiert, im Base-Adapter
 * geschaut.
 * ACHTUNG: Löschoperationen werden NICHT sauber unterstützt. Es können keine Dateien aus dem Base-Adapter gelöscht werden.
 *          Eine whiteout Ansatz wäre möglich, aber mit allen drum und dran schon aufwändig -> brauchen wir für den use case aber auch nicht zwingend.
 */
class OverlayAdapter implements AdapterInterface
{
    private AdapterInterface $layer_adapter;
    private AdapterInterface $base_adapter;

    public function __construct(AdapterInterface $base_adapter, AdapterInterface $layer_adapter)
    {
        $this->layer_adapter = $layer_adapter;
        $this->base_adapter = $base_adapter;
    }

    public function write($path, $contents, Config $config)
    {
        return $this->layer_adapter->write($path, $contents, $config);
    }

    public function writeStream($path, $resource, Config $config)
    {
        return $this->layer_adapter->writeStream($path, $resource, $config);
    }

    public function update($path, $contents, Config $config)
    {
        if ($this->layer_adapter->has($path)) {
            return $this->layer_adapter->update($path, $contents, $config);
        }

        return $this->layer_adapter->write($path, $contents, $config);
    }

    public function updateStream($path, $resource, Config $config)
    {
        if ($this->layer_adapter->has($path)) {
            return $this->layer_adapter->updateStream($path, $resource, $config);
        }

        return $this->layer_adapter->writeStream($path, $resource, $config);
    }

    public function rename($path, $newpath)
    {
        $exists_on_layer = $this->layer_adapter->has($path);
        $exists_on_base = $this->base_adapter->has($path);

        if (!$exists_on_layer && !$exists_on_base) {
            throw new FileNotFoundException($path);
        }

        if (!$exists_on_layer) {
            return false;
        }

        return $this->layer_adapter->rename($path, $newpath);
    }

    public function copy($path, $newpath)
    {
        if ($this->layer_adapter->has($path)) {
            return $this->layer_adapter->copy($path, $newpath);
        }

        $source_adapter = $this->getReadableAdapter($path);

        if (!$source_adapter) {
            throw new FileNotFoundException($path);
        }

        $read_result = $source_adapter->readStream($path);

        if ($read_result === false || !isset($read_result['stream'])) {
            return false;
        }

        $write_result = $this->layer_adapter->writeStream($newpath, $read_result['stream'], new Config());

        if (is_resource($read_result['stream'])) {
            fclose($read_result['stream']);
        }

        return $write_result !== false;
    }

    public function delete($path)
    {
        $exists_on_layer = $this->layer_adapter->has($path);
        $exists_on_base = $this->base_adapter->has($path);

        if (!$exists_on_layer && !$exists_on_base) {
            throw new FileNotFoundException($path);
        }

        $delete_result = false;

        if ($exists_on_layer) {
            $delete_result = $this->layer_adapter->delete($path);
        }

        if ($exists_on_base) {
            $this->triggerBaseNotDeleteableNotice($path);
        }

        return (bool) $delete_result;
    }

    public function deleteDir($dirname)
    {
        $exists_on_layer = $this->layer_adapter->has($dirname);
        $exists_on_base = $this->base_adapter->has($dirname);

        if (!$exists_on_layer && !$exists_on_base) {
            throw new FileNotFoundException($dirname);
        }

        if (!$exists_on_layer) {
            return false;
        }

        return $this->layer_adapter->deleteDir($dirname);
    }

    public function createDir($dirname, Config $config)
    {
        return $this->layer_adapter->createDir($dirname, $config);
    }

    public function setVisibility($path, $visibility)
    {
        $exists_on_layer = $this->layer_adapter->has($path);
        $exists_on_base = $this->base_adapter->has($path);

        if (!$exists_on_layer && !$exists_on_base) {
            throw new FileNotFoundException($path);
        }

        if (!$exists_on_layer) {
            return false;
        }

        return $this->layer_adapter->setVisibility($path, $visibility);
    }

    public function has($path)
    {
        if ($this->layer_adapter->has($path)) {
            return true;
        }

        //rumgepufsche... flysystem prüft beim Schreiben ob die Datei bereits existiert. Da wir aus der Base Layer nichts
        //löschen können, tun wir in solchen Fällen (assertAbsent) so, als würde es die Datei in der Base Layer nicht geben.
        $call_stack = debug_backtrace();
        if (isset($call_stack[2]['function']) && $call_stack[2]['function'] === 'assertAbsent') {
            return false;
        }

        return $this->base_adapter->has($path);
    }

    public function read($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->read($path);
    }

    public function readStream($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->readStream($path);
    }

    public function listContents($directory = '', $recursive = false)
    {
        $base_listing = $this->base_adapter->listContents($directory, $recursive);
        $layer_listing = $this->layer_adapter->listContents($directory, $recursive);

        $merged_listing = [];

        foreach ($base_listing as $item) {
            if (isset($item['path'])) {
                $merged_listing[$item['path']] = $item;
                continue;
            }

            $merged_listing[] = $item;
        }

        foreach ($layer_listing as $item) {
            if (isset($item['path'])) {
                $merged_listing[$item['path']] = $item;
                continue;
            }

            $merged_listing[] = $item;
        }

        return array_values($merged_listing);
    }

    public function getMetadata($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->getMetadata($path);
    }

    public function getSize($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->getSize($path);
    }

    public function getMimetype($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->getMimetype($path);
    }

    public function getTimestamp($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->getTimestamp($path);
    }

    public function getVisibility($path)
    {
        $adapter = $this->ensureReadableAdapter($path);

        return $adapter->getVisibility($path);
    }

    private function ensureReadableAdapter(string $path): AdapterInterface
    {
        $adapter = $this->getReadableAdapter($path);

        if (!$adapter) {
            throw new FileNotFoundException($path);
        }

        return $adapter;
    }

    private function getReadableAdapter(string $path): ?AdapterInterface
    {
        if ($this->layer_adapter->has($path)) {
            return $this->layer_adapter;
        }

        if ($this->base_adapter->has($path)) {
            return $this->base_adapter;
        }

        return null;
    }

    private function triggerBaseNotDeleteableNotice(string $path): void
    {
        trigger_error(sprintf('LayeringAdapter: "%s" is existing in the base layer and was not deleted from the base layer!', $path), E_USER_NOTICE);
    }
}
