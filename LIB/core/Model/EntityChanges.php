<?php

namespace bqp\Model;

use ArrayAccess;
use bqp\field_manager;
use db;
use Iterator;

/**
 * hilfsklasse um änderungen am model zu tracken, auszuwerten und protokollieren
 * @package bqp\Model
 */
class EntityChanges implements ArrayAccess, Iterator
{
    protected static $field_cache = null;

    protected array $changes = [];

    protected int $user_id;

    protected $entity_id;
    protected ?string $table = null;
    protected string $entity_id_field = 'entity_id';
    protected int $limit = 1000;

    public const FIELD_MODUS_STRING = 'string';
    public const FIELD_MODUS_ID = 'id';
    public const FIELD_MODUS_BOTH = 'both';

    public const TYPE_ID_MODUS_INT = 'int';
    public const TYPE_ID_MODUS_STRING = 'string';

    /**
     * legt fest, ob das Feld als String oder Id ins Protokoll eingetragen werden soll.
     * Die Felder werden automatisch in protokoll_field angelegt.
     */
    private string $field_modus = self::FIELD_MODUS_STRING;

    private string $type_id_modus = self::TYPE_ID_MODUS_INT;
    private int|string $default_type_id = 0;

    /**
     * Kann genutzt werden um Werte vor den Speichern zu filtern oder zu verändern.
     *
     * @var array<int, array{callback: callable, type: string}>
     */
    private array $save_callbacks = [];

    public function __construct($model_id = null)
    {
        if ($model_id !== null) {
            $this->configureByModelId($model_id);
        }

        $this->user_id = \env::getUserId();
    }

    public function configureByModelId(string $model_id): void
    {
        $config = field_manager::getInstance()->getProtokollConfig($model_id);

        if (!$config) {
            return;
        }

        $this->table = $config['table'];

        if (isset($config['entity_id_field'])) {
            $this->entity_id_field = $config['entity_id_field'];
        }

        if (isset($config['field_modus'])) {
            $this->field_modus = $config['field_modus'];
        }

        if (isset($config['limit'])) {
            $this->limit = $config['limit'];
        }

        if (isset($config['type_id_modus'])) {
            $this->type_id_modus = $config['type_id_modus'];

            if ($this->type_id_modus === self::TYPE_ID_MODUS_STRING) {
                $this->default_type_id = '';
            } else {
                $this->default_type_id = 0;
            }
        }

        if (isset($config['save_filter_callback'])) {
            $this->addSaveCallback('filter', $config['save_filter_callback']);
        }
    }

    public function addSaveCallback(string $type, callable $callback): void
    {
        $this->save_callbacks[] = [
            'callback' => $callback,
            'type' => $type
        ];
    }

    public function setEntityId($entity_id)
    {
        $this->entity_id = $entity_id;
    }

    public function getEntityId()
    {
        return $this->entity_id;
    }

    /**
     * prüft ob änderungen stattgefunden haben
     * -ohne parameter -> mindestens eine änderung
     * -mit parameter string -> ob dieses Feld geändert wurde
     * -mit parameter string[] -> ob eins dieser Felder geändert wurde
     *
     * Feld kann folgende Format haben:
     * type.field
     * type.*
     * *.field
     * type.type_id.*
     * type.type_id.field
     * field was *.field entspricht
     *
     * @param string[]|string $fields string
     * @return bool
     */
    public function isChange($fields = null)
    {
        if ($fields === null) {
            return (bool)count($this->changes);
        }

        if (!$this->isChange()) {
            return false;
        }

        if (!is_array($fields)) {
            $fields = [$fields];
        }

        foreach ($fields as $field) {
            list($type, $type_id, $field) = self::tokenizeField($field);

            foreach ($this->changes as $key => $change) {
                if ($type !== '*' && $change['type'] != $type) {
                    continue;
                }

                if ($field !== '*' && $change['field'] != $field) {
                    continue;
                }

                if ($type_id !== '*') {
                    if (is_object($change['type_id'])) {
                        //wenn objekte reingegeben werden, hier bei bedarf versuchen die aufzulösen
                        $this->changes[$key]['type_id'] = $this->typeId($change['type_id']);
                        $change = $this->changes[$key];
                    }

                    if ($change['type_id'] != $type_id) {
                        continue;
                    }
                }

                return true;
            }
        }

        return false;
    }

    public function getChanges($field)
    {
        $changes = [];

        if (!$this->isChange()) {
            return $changes;
        }

        list($type, $type_id, $field) = self::tokenizeField($field);

        foreach ($this->changes as $key => $change) {
            if ($type !== '*' && $change['type'] != $type) {
                continue;
            }

            if ($field !== '*' && $change['field'] != $field) {
                continue;
            }

            if ($type_id !== '*') {
                if (is_object($change['type_id'])) {
                    //wenn objekte reingegeben werden, hier bei bedarf versuchen die aufzulösen
                    $this->changes[$key]['type_id'] = $this->typeId($change['type_id']);
                    $change = $this->changes[$key];
                }

                if ($change['type_id'] != $type_id) {
                    continue;
                }
            }

            $changes[] = $change;
        }

        return $changes;
    }

    public function getAllChanges(): array
    {
        return $this->changes;
    }

    /**
     * Feld kann folgende Format haben:
     * type.field
     * type.*
     * *.field
     * type.type_id.*
     * type.type_id.field
     * field was *.field entspricht
     *
     * @return array [$type, $type_id, $field]
     */
    public static function tokenizeField(string $raw_field): array
    {
        $type = '*';
        $field = '*';
        $type_id = '*';

        $element_count = substr_count($raw_field, '.');

        if ($element_count) {
            if ($element_count == 1) {
                list($type, $field) = explode('.', $raw_field);
            } elseif ($element_count == 2) {
                list($type, $type_id, $field) = explode('.', $raw_field);
            } else {
                throw new \bqp\Exceptions\DevException('more then 3 sections');
            }
        } else {
            $field = $raw_field;
        }

        return [$type, $type_id, $field];
    }

    public function getChange(string $field): array
    {
        $changes = $this->getChanges($field);

        if (!$changes) {
            throw new \DomainException('no change for ' . $field . ' found');
        }

        if (count($changes) > 1) {
            throw new \DomainException('multiple changes for ' . $field . ' found');
        }

        return $changes[0];
    }

    public function count(): int
    {
        return count($this->changes);
    }

    public function save(): void
    {
        if (!$this->table) {
            throw new \bqp\Exceptions\DevException('table not configurated');
        }

        if (!$this->entity_id) {
            throw new \bqp\Exceptions\DevException('entity id not set');
        }

        if (!$this->isChange()) {
            return;
        }

        $db = db::getInstance();


        $fields = [
            $this->table . '.' . $this->entity_id_field,
            $this->table . '.date_added',
            $this->table . '.user_id',
            $this->table . '.type',
            $this->table . '.type_id',
            $this->table . '.new_value'
        ];


        if ($this->field_modus === self::FIELD_MODUS_STRING || $this->field_modus === self::FIELD_MODUS_BOTH) {
            $fields[] = $this->table . '.field';
        }

        if ($this->field_modus === self::FIELD_MODUS_ID || $this->field_modus === self::FIELD_MODUS_BOTH) {
            $fields[] = $this->table . '.field_id';
        }

        $insert = new \bqp\db\ExtendedInsertQuery($db, $this->table, $fields);

        $now = new \bqp\db\SqlFunction('NOW()');

        foreach ($this->changes as $change) {
            $new_value = (isset($change['new_value']) ? $change['new_value'] : '');

            if (is_array($new_value)) {
                $new_value = implode(', ', $new_value);
            }

            if (strlen($new_value) > $this->limit) {
                $new_value = mb_substr($new_value, 0, $this->limit);
            }

            $type_id = (isset($change['type_id']) ? $change['type_id'] : $this->default_type_id);
            $type_id = $this->typeId($type_id);

            if (is_object($type_id)) {
                $type_id = $this->default_type_id;
            }

            $entry = [
                $this->entity_id_field => $this->getEntityId(),
                'user_id' => $this->user_id,
                'type' => $change['type'],
                'type_id' => $type_id,
                'field' => $change['field'],
                'field_id' => $this->getFieldId($change['field']),
                'new_value' => $new_value,
                'date_added' => $now
            ];

            $entry = $this->preProcessEntry($entry);

            if ($entry) {
                $insert->add($entry);
            }
        }

        $insert->execute();
    }


    private function preProcessEntry(array $entry): ?array
    {
        foreach ($this->save_callbacks as $save_callback) {
            if ($save_callback['type'] === 'filter') {
                if ($save_callback['callback']($entry) === false) {
                    return null;
                }
                continue;
            }

            $entry = $save_callback['callback']($entry);
        }

        return $entry;
    }

    protected function getFieldId(string $field): int
    {
        if (self::$field_cache === null) {
            $this->loadFieldCache();
        }

        if (!isset(self::$field_cache[$field])) {
            $db = db::getInstance();
            $db->simpleInsert('protokoll_field', ['field_name' => $field]);
            self::$field_cache[$field] = $db->insert_id();
        }

        return self::$field_cache[$field];
    }

    protected function loadFieldCache(): void
    {
        self::$field_cache = db::getInstance()->query("
            SELECT
                protokoll_field.field_name,
                protokoll_field.field_id
            FROM
                protokoll_field
        ")->asSingleArray('field_name');
    }

    public function debug($echo = true)
    {
        $table = '<table border="1">';
        $table .= '<tr>';
        $table .= '<th>type</th>';
        $table .= '<th>type_id</th>';
        $table .= '<th>field</th>';
        $table .= '<th>new_value</th>';
        $table .= '<th>old_value</th>';
        $table .= '</tr>';

        foreach ($this->changes as $daten) {
            $table .= '<tr>';
            $table .= '<td>' . $daten['type'] . '</td>';
            $table .= '<td>';
            if (is_object($daten['type_id'])) {
                $table .= 'iterim (' . get_class($daten['type_id']) . ') / (current ModelPK: ' . $daten['type_id']->getModelPk() . ')';
            } else {
                $table .= $daten['type_id'];
            }
            $table .= '</td>';
            $table .= '<td>' . $daten['field'] . '</td>';
            $table .= '<td>';
            $new_value = (isset($daten['new_value']) ? $daten['new_value'] : '<i>null</i>');

            if (is_array($new_value)) {
                $new_value = implode(', ', $new_value);
            }

            $table .= $new_value;
            $table .= '</td>';
            $table .= '<td>' . $daten['old_value'] . '</td>';
            $table .= '</tr>';
        }

        $table .= '</table>';

        if ($echo) {
            echo $table;
        }
        return $table;
    }

    public function add($field, $new_value, $old_value = null, $type = null, $type_id = null)
    {
        $type_id = $this->typeId($type_id);

        $this->changes[] = [
            'type' => $type,
            'type_id' => $type_id,
            'field' => $field,
            'new_value' => $new_value,
            'old_value' => $old_value
        ];
    }

    public function addByArray($daten)
    {
        $temp = [
            'type' => null,
            'type_id' => null,
            'field' => null,
            'new_value' => null,
            'old_value' => null
        ];

        $temp['field'] = $daten['field'];

        if (array_key_exists('type_id', $daten)) {
            $temp['type_id'] = $daten['type_id'];
        }

        if (array_key_exists('type', $daten)) {
            $temp['type'] = $daten['type'];
        }

        if (array_key_exists('new_value', $daten)) {
            $temp['new_value'] = $daten['new_value'];
        }

        if (array_key_exists('old_value', $daten)) {
            $temp['old_value'] = $daten['old_value'];
        }

        $this->add($temp['field'], $temp['new_value'], $temp['old_value'], $temp['type'], $temp['type_id']);
    }


    public function addBySmartDataObj(SmartDataObjLegacy $obj, $type = null, $type_id = null)
    {
        $result = $obj->getChanges(SmartDataObj::CHANGES_BOTH_VALUES);
        foreach ($result as $key => $daten) {
            $this->add($key, $daten['new'], $daten['old'], $type, $type_id);
        }
    }

    public function addNew($type, $type_id)
    {
        $this->add('__action', 'new', null, $type, $type_id);
    }

    public function addRemove($type, $type_id, $old_value = null)
    {
        $this->add('__action', 'remove', $old_value, $type, $type_id);
    }

    public function addEntityDelete(): void
    {
        $this->add('__action', 'delete');
    }

    /**
     * entfernt sämtliche aktullen änderungen aus dem protokoll
     */
    public function clear()
    {
        $this->changes = [];
    }

    private function typeId($type_id)
    {
        if (!is_object($type_id)) {
            return $type_id;
        }

        if (!method_exists($type_id, 'getModelPk')) {
            throw new \bqp\Exceptions\DevException('object must implements getModelPk()');
        }

        $temp = $type_id->getModelPk();
        if ($temp) {
            return $temp;
        }

        return $type_id;
    }

    //<editor-fold desc="ArrayAccess">
    public function offsetSet(mixed $offset, mixed $value): void
    {
        if (!is_null($offset)) {
            unset($this->changes[$offset]);
        }

        $this->addByArray($value);
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->changes[$offset]);
    }

    public function offsetUnset(mixed $offset): void
    {
        unset($this->changes[$offset]);
    }

    public function offsetGet(mixed $offset): mixed
    {
        return isset($this->changes[$offset]) ? $this->changes[$offset] : null;
    }

    //</editor-fold>


    public function current(): mixed
    {
        return current($this->changes);
    }

    public function next(): void
    {
        next($this->changes);
    }

    public function key(): mixed
    {
        return key($this->changes);
    }

    public function valid(): bool
    {
        return $this->key() !== null;
    }

    public function rewind(): void
    {
        reset($this->changes);
    }
}
