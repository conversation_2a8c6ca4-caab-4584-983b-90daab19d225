<?php

namespace bqp\form;

use bqp\Exceptions\DevException;
use bqp\output\output_dependency;
use wws\business_structure\Shop;

class form_element_tinymce extends form_element_input implements FormRendererInline
{
    protected $width = 1000;
    protected $height = 500;
    protected $menubar = false;
    protected $plugins = ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview', 'anchor', 'searchreplace', 'visualblocks', 'code', 'media', 'table', 'contextmenu'];

    protected $filemanager = false;
    protected $content_css = ['/res/js/tinymce_plugins/variables/style.css'];
    protected $body_class = null;

    protected $cms_help = false;

    protected $shop_id = null;

    protected $template_url = null;

    protected $raw_options = [];

    protected $toolbars = [
        'styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | table | link image media',
        'undo redo | insert template | searchreplace code visualblocks | cms_help'
    ];

    public function getDependencies()
    {
        $dependencies = parent::getDependencies();

        $dependencies[] = new output_dependency('js', '/res/js/tinymce_4.9.4/js/tinymce/tinymce.min.js');
        //$dependencies[] = new output_dependency('js', '/res/js/tinymce_plugins/variables/plugin.js');
        if (in_array('shop_images', $this->plugins)) {
            $dependencies[] = new output_dependency('js', '/res/js/tinymce_plugins/shop_images.js');
        }
        if ($this->filemanager) {
            $dependencies[] = new output_dependency('js', '/vendor/filemanager/filemanager/plugin.min.js');
        }

        if ($this->cms_help) {
            $dependencies[] = new output_dependency('js', '/res/js/tinymce_plugins/cms_help.js');
        }

        return $dependencies;
    }

    public function getInputRenderer()
    {
        return $this;
    }

    public function setShopId($shop_id)
    {
        $this->shop_id = $shop_id;
    }

    public function renderInputInline(?string $mode = null): string
    {
        $extra = '';

        $classes = $this->getClass();

        if ($this->isError()) {
            $classes .= ' error';
        }

        $return = '<textarea name="' . $this->getName() . '" ' . \bqp\Utils\HtmlBuilder::attribute('id', $this->getId()) . ' ' . \bqp\Utils\HtmlBuilder::attribute('class', $classes) . ' ' . $extra . '>' . \bqp\Utils\StringUtils::htmlentities($this->getPreFilterValue() ?? '') . '</textarea>';

        $return .= "<script type=\"text/javascript\">
                tinymce.init({
                    selector: '#" . $this->getId() . "',
                    language: 'de',
                    browser_spellcheck: true,
                    convert_urls: false,
                    " . $this->getConfigAsString() . "
                    image_advtab: true,
                    statusbar: false
                });
        </script>";

        return $return;
    }

    protected function getConfigAsArray(): array
    {
        $config = [];

        $config['plugins'] = $this->plugins;

        if ($this->filemanager) {
            $config['plugins'][] = 'filemanager';
            $config['external_filemanager_path'] = '/vendor/filemanager/filemanager/';
            $config['filemanager_title'] = 'Dateimanger';
        }

        if ($this->cms_help) {
            $config['plugins'][] = 'cms_help';
        }

        if ($this->template_url) {
            $config['plugins'][] = 'template';
            $config['templates'] = $this->template_url;
        }

        $config['toolbar'] = $this->toolbars;

        //@todo in eine factory/builder auslagern
        if ($this->shop_id && in_array('shop_images', $this->plugins)) {
            $host = 'https://www.smartgoods.de';

            if ($this->shop_id == Shop::ERSATZTEILSHOP) {
                $host = 'https://www.ersatzteilshop.de';
            }

            $config['shop_images_host'] = $host;
        }

        if ($this->height) {
            $config['height'] = $this->height;
        }
        if ($this->width) {
            $config['width'] = $this->width;
        }

        $config['menubar'] = $this->menubar;

        $config['content_css'] = $this->content_css;

        if ($this->body_class) {
            $config['body_class'] = $this->body_class;
        }

        foreach ($this->raw_options as $key => $value) {
            $config[$key] = $value;
        }

        $config['plugins'] = array_unique($config['plugins']);

        return $config;
    }

    protected function getConfigAsString()
    {
        $config = $this->getConfigAsArray();

        $return = '';

        foreach ($config as $key => $value) {
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            } elseif (is_numeric($value)) {
                $value = $value;
            } elseif (is_array($value)) {
                $value = implode(', ', array_map(function ($value) {
                    return "'" . $value . "'";
                }, $value));
                $value = '[' . $value . ']';
            } else {
                $value = str_replace('\'', '\\\'', $value);
                $value = '"' . $value . '"';
            }

            $return .= $key . ': ' . $value . ",\n";
        }

        return $return;
    }

    public function addPlugin(string $plugin): void
    {
        $this->plugins[] = $plugin;
    }

    public function setFilemanger($status)
    {
        $this->filemanager = (bool)$status;
    }

    public function addContentCss($css_file)
    {
        $this->content_css[] = $css_file;
    }

    /**
     * ACHTUNG überschreibt eventuell auch CSS von Plugins, mit bedacht nutzen
     * @param string $css_file
     */
    public function setContentCss(string $css_file): void
    {
        $this->content_css = [$css_file];
    }

    public function setBodyClass($body_class)
    {
        $this->body_class = $body_class;
    }

    public function setCmsHelp($status)
    {
        $this->cms_help = (bool)$status;
    }

    public function setTemplateUrl(string $template_url): void
    {
        $this->template_url = $template_url;
    }

    /**
     * @param int $width
     */
    public function setWidth(int $width): void
    {
        $this->width = $width;
    }

    /**
     * @param int $height
     */
    public function setHeight(int $height): void
    {
        $this->height = $height;
    }

    /**
     * @param string $key
     * @param bool|string|array $value
     */
    public function setRawOption(string $key, $value): void
    {
        $this->raw_options[$key] = $value;
    }

    public function setToolbars(array $toolbars): void
    {
        $this->toolbars = $toolbars;
    }

    public function addToolbar(string $toolbar): void
    {
        $this->toolbars[] = $toolbar;
    }
}
