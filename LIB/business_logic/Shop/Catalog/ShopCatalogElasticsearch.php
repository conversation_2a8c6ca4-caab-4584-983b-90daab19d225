<?php

namespace wws\Shop\Catalog;

use stdClass;
use wws\business_structure\Shop;
use wws\Product\ProductConst;
use wws\Shop\ProductFeature\product_feature_search_shop_elastic;
use wws\Shop\ProductFeature\ProductFeature;
use wws\Shop\ProductFeature\ProductFeatureEnum;
use wws\Shop\ProductFeature\ProductFeatureRange;
use wws\Shop\ProductFeature\ProductFeatureSofortlieferbar;

class ShopCatalogElasticsearch extends ShopCatalogElasticsearchGeneric
{
    private $shop_id = Shop::ALLEGO;

    /**
     * @var product_feature_search_shop_elastic
     */
    protected $feature_search;

    protected $device_ids = [];

    /**
     * @var ShopCatalogDeviceContext
     */
    protected $device_context;

    public function setFeatureGroupId($feature_group_id)
    {
        $this->feature_search = new product_feature_search_shop_elastic($feature_group_id);
        $this->feature_search->setShopId($this->shop_id);

        $this->feature_search->init();
    }

    public function setPriceMin($price_min)
    {
        parent::setPriceMin($price_min);

        $this->feature_search->addFilter('preis_von', $price_min);
    }

    public function setPriceMax($price_max)
    {
        parent::setPriceMax($price_max);

        $this->feature_search->addFilter('preis_bis', $price_max);
    }

    public function addFilter($feature_id, $value)
    {
        $this->feature_search->addFilter($feature_id, $value);

        $this->show_invisible = true;
    }

    public function execute(): ShopCatalogResult
    {
        $do_sort_by_score = false;

        if ($this->max_results && ($this->offset - $this->limit) > $this->max_results) {
            $this->offset = $this->max_results - $this->limit;
        }

        $body = [];
        $body['size'] = $this->limit;

        if ($this->limit) {
            $body['_source'] = $this->headlist;
        }

        if ($this->offset) {
            $body['from'] = $this->offset;
        }

        $filters = [];
        $feature_specific_filters = [];

        $filters[] = ['term' => ['filter_list_status' => true]];
        //$filters[] = ['term' => ['is_sparepart' => false]];

        if ($this->cat_ids) {
            if (count($this->cat_ids) == 1) {
                $filters[] = ['term' => ['cat_ids_closure_list' => $this->cat_ids[0]]];
            } else {
                $filters[] = ['terms' => ['cat_ids_closure_list' => $this->cat_ids]];
            }
        }

        if ($this->goodie_ids) {
            if (count($this->goodie_ids) == 1) {
                $filters[] = ['term' => ['goodie_ids' => $this->goodie_ids[0]]];
            } else {
                $filters[] = ['terms' => ['goodie_ids' => $this->goodie_ids]];
            }
        }

        if ($this->expressable) {
            $filters[] = ['term' => ['expressable' => true]];
        }

        if ($this->brand_id) {
            $filters[] = ['term' => ['brand_ids' => $this->brand_id]];
        }

        if ($this->device_ids) {
            $filters[] = ['terms' => ['device_ids' => $this->device_ids]];
        }

        $base_filters = $filters;

        if ($this->feature_search) {
            $features = $this->feature_search->getFeatures();

            foreach ($features as $feature) {
                if (!$feature->isSelected()) {
                    continue;
                }

                if ($feature instanceof ProductFeatureSofortlieferbar) {
                    $filter = ['term' => ['lieferbaranzeige' => ProductConst::LIEFERBAR_ID_1_TO_3]];
                    $filters[] = $filter;
                    $feature_specific_filters[$feature->getFeatureId()][] = $filter;
                    continue;
                }

                if ($feature instanceof ProductFeatureEnum) {
                    $filter = $this->buildEnumFeatureFilter($feature);
                    if ($filter) {
                        $filters[] = $filter;
                        $feature_specific_filters[$feature->getFeatureId()][] = $filter;
                    }
                    continue;
                }

                if ($feature instanceof ProductFeatureRange) {
                    $filter = $this->buildRangeFeatureFilter($feature);
                    if ($filter) {
                        $filters[] = $filter;
                        $feature_specific_filters[$feature->getFeatureId()][] = $filter;
                    }
                }
            }
        }

        $must = [];
        if ($this->product_name_phrase) {
            $must = [
                'multi_match' => [
                    'query' => $this->product_name_phrase,
                    'fields' => ['ean^10', 'mpn^10', 'product_name.search^5', 'brand'], //'allpharses'
                    'operator' => 'and'
                ]
            ];
        } elseif ($this->generel_phrase) {
            $must = [
                'match' => [
                    'allpharses' => [
                        'query' => $this->generel_phrase,
                        'operator' => 'and'
                    ]
                ]
            ];
        }

        if ($must || $filters) {
            $body['query'] = [];
            $body['query']['bool'] = [];

            if ($must) {
                $body['query']['bool']['must'] = $must;
            }

            if ($filters) {
                $body['query']['bool']['filter'] = $filters;
            }
        }

        $body['aggregations'] = [];

        if ($this->agg_child_cats_conf) {
            $this->agg_child_cats_conf->check();

            //var_dump($this->agg_child_cats_conf);
            //nur direkte kind kategorien von $this->agg_child_cats_conf->getCatId() einbeziehen
            //$this->agg_child_cats_conf->getCatId()
            //$this->agg_child_cats_conf->getOnlyDirectChilds()

            if ($this->agg_child_cats_conf->getLevel() !== null) {
                $level = $this->agg_child_cats_conf->getLevel();
            } else {
                $level = $this->data_provider_cats->getCatEbene($this->agg_child_cats_conf->getCatId() ?? 0) + 2;
            }

            if ($level <= 4) {
                $limit = $this->agg_child_cats_conf->getLimit() ?: 25;
                $body['aggregations']['child_cats_ids'] = [
                    'terms' => [
                        'field' => 'cat_ids_ebene_' . $level,
                        'size' => $limit
                    ]
                ];
            }
        }

        if ($this->feature_search) {
            foreach ($this->getFeatureSearchAggregations($base_filters, $feature_specific_filters) as $aggregation_key => $agg) {
                $body['aggregations'][$aggregation_key] = $agg;
            }
        }

        if ($this->agg_brands) {
            $body['aggregations']['brands'] = [
                'terms' => [
                    'field' => 'brand_ids',
                    'size' => $this->agg_brands_limit
                ]
            ];
        }

        if ($this->agg_goodie_ids) {
            $body['aggregations']['goodie_ids'] = [
                'terms' => [
                    'field' => 'goodie_ids',
                    'size' => 250
                ]
            ];
        }

        if ($this->agg_cat_ids) {
            $body['aggregations']['cat_ids'] = [
                'terms' => [
                    'field' => 'cat_ids',
                    'size' => 250
                ]
            ];
        }

        if (!$body['aggregations']) {
            unset($body['aggregations']);
        }

        if ($this->device_context && $this->product_name_phrase) {
            $product_ids = $this->device_context->searchInDiagramPositions($this->product_name_phrase);

            if ($product_ids) {
                //query von must du should umschreiben -> OR
                $body['query']['bool']['should'] = [];

                foreach ($body['query']['bool']['must'] as $key => $value) {
                    $body['query']['bool']['should'][] = [$key => $value];
                }

                foreach ($product_ids as $product_id) {
                    $body['query']['bool']['should'][] = ['term' => ['product_id' => ['value' => $product_id, 'boost' => 10]]];
                }

                unset($body['query']['bool']['must']);

                $body['query']['bool']['minimum_should_match'] = 1;

                $do_sort_by_score = true;
            }
        }

        if (!$do_sort_by_score) {
            if ($this->order && isset($this->order_config[$this->order]['elastic'])) {
                $body['sort'] = $this->order_config[$this->order]['elastic'];
            }
        }

        $params = [
            'index' => $this->index,
            'body' => $body
        ];

        $result_raw = $this->client->search($params)->asArray();

        $result = $this->rawResultToResult($result_raw);
        $result->setLimit($this->limit);
        $result->setOffset($this->offset);

        return $result;
    }

    protected function getFeatureSearchAggregations(array $base_filters, array $feature_specific_filters): array
    {
        if (!$this->feature_search) {
            return [];
        }

        $aggregations = [];
        $limit = 20;

        foreach ($this->feature_search->getFeatures() as $feature) {
            $field = $this->resolveFeatureAggregationField($feature);

            if (!$field) {
                continue;
            }

            $aggregation_key = 'feature_' . $feature->getFeatureId();

            if ($feature instanceof ProductFeatureEnum) {
                $terms_aggregation = [
                    'terms' => [
                        'field' => $field,
                        'size' => $limit
                    ]
                ];

                if ($feature->useSelfExclusion() && $feature->isSelectedReal()) {
                    $filters_for_self_exclusion = $this->buildSelfExclusionFilters($base_filters, $feature_specific_filters, $feature->getFeatureId());
                    $aggregations[$aggregation_key] = $this->wrapAggregationWithSelfExclusion($terms_aggregation, $filters_for_self_exclusion);
                    continue;
                }

                $aggregations[$aggregation_key] = $terms_aggregation;
            }

            if ($feature instanceof ProductFeatureRange) {
                if ($feature->useSelfExclusion() && $feature->isSelected()) {
                    $filters_for_self_exclusion = $this->buildSelfExclusionFilters($base_filters, $feature_specific_filters, $feature->getFeatureId());

                    $aggregations[$aggregation_key] = [
                        'global' => new stdClass(),
                        'aggs' => [
                            $aggregation_key => [
                                'filter' => ['bool' => ['filter' => $filters_for_self_exclusion]],
                                'aggs' => [
                                    $aggregation_key . '.range_min' => [
                                        'min' => [
                                            'field' => 'vk_brutto',
                                        ]
                                    ],
                                    $aggregation_key . '.range_max' => [
                                        'max' => [
                                            'field' => 'vk_brutto',
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ];
                } else {
                    $range_aggregation_min = [
                        'min' => ['field' => $field],
                    ];

                    $range_aggregation_max = [
                        'max' => ['field' => $field],
                    ];

                    $aggregations[$aggregation_key . '.range_min'] = $range_aggregation_min;
                    $aggregations[$aggregation_key . '.range_max'] = $range_aggregation_max;
                }
            }
        }

        return $aggregations;
    }

    protected function rawResultToResult(array $result_raw): ShopCatalogResultGeneric
    {
        $catalog_result = new ShopCatalogResultGeneric();

        if ($this->max_results && $this->max_results < $result_raw['hits']['total']['value']) {
            $catalog_result->setTotal($this->max_results, true);
        } else {
            $catalog_result->setTotal($result_raw['hits']['total']['value']);
        }

        foreach ($result_raw['hits']['hits'] as $raw_product) {
            $catalog_result->addProductHead($this->createProductHeadFromSource($raw_product['_source']));
        }

        //ersatzteil spezifisch...
//        foreach ($hits['hits'] as $hit) {
//            $product = $this->createProductHeadFromSource($hit['_source']);
//
//            unset($hit['_source']);
//            if ($this->device_context) {
//                $diagram_position = $this->device_context->getDiagramPosition($product->getProductId());
//                if ($diagram_position) {
//                    $hit['diagram_position'] = $diagram_position['text'];
//                }
//            }
//
//            $product->setExtras($hit);
//            $catalog_result->addProductHead($product);
//        }

        if (isset($result_raw['aggregations']['child_cats_ids'])) {
            $agg = new ShopCatalogAggregationCats('child_cats');
            $agg->setDataServiceCats($this->data_provider_cats);

            //alle kinde kategorien laden
            if ($this->agg_child_cats_conf->isLoadAllChildCats()) {
                $childs = $this->data_provider_cats->getAllChildCats($this->agg_child_cats_conf->getCatId());
                foreach ($childs as $cat) {
                    $agg->add($cat['cat_id'], '', 0);
                }
            }

            $cat_result = $result_raw['aggregations']['child_cats_ids']['buckets'];

            foreach ($cat_result as $cat) {
                if ($cat['key']) {
                    $agg->add($cat['key'], '', $cat['doc_count']);
                }
            }

            $catalog_result->addAggregation($agg);
        }

        if (isset($result_raw['aggregations']['brands'])) {
            $agg = new ShopCatalogAggregationBrands('brands');
            $agg->setCountOther($result_raw['aggregations']['brands']['sum_other_doc_count']);

            foreach ($result_raw['aggregations']['brands']['buckets'] as $bucket) {
                $agg->add($bucket['key'], $bucket['key'], $bucket['doc_count']);
            }

            $agg->fill($this->data_provider_sparepart_brands);

            $catalog_result->addAggregation($agg);
        }

        if (isset($result_raw['aggregations']['goodie_ids'])) {
            $agg = new ShopCatalogAggregation('goodie_ids');
            $agg->setCountOther($result_raw['aggregations']['goodie_ids']['sum_other_doc_count']);

            foreach ($result_raw['aggregations']['goodie_ids']['buckets'] as $bucket) {
                $agg->add($bucket['key'], $bucket['key'], $bucket['doc_count']);
            }

            $catalog_result->addAggregation($agg);
        }

        if (isset($result_raw['aggregations']['cat_ids'])) {
            $agg = new ShopCatalogAggregation('cat_ids');
            $agg->setCountOther($result_raw['aggregations']['cat_ids']['sum_other_doc_count']);

            foreach ($result_raw['aggregations']['cat_ids']['buckets'] as $bucket) {
                $agg->add($bucket['key'], $bucket['key'], $bucket['doc_count']);
            }

            $catalog_result->addAggregation($agg);
        }


        //sonderbehandlung price_range
        if (isset($result_raw['aggregations']['feature_price_range.range_min']['value'])) {
            $catalog_result->setPriceMin(round($result_raw['aggregations']['feature_price_range.range_min']['value'], 2));
        }

        if (isset($result_raw['aggregations']['feature_price_range.range_max']['value'])) {
            $catalog_result->setPriceMax(round($result_raw['aggregations']['feature_price_range.range_max']['value'], 2));
        }
        //self exclusion mode
        if (isset($result_raw['aggregations']['feature_price_range']['feature_price_range']['feature_price_range.range_min']['value'])) {
            $catalog_result->setPriceMin(round($result_raw['aggregations']['feature_price_range']['feature_price_range']['feature_price_range.range_min']['value'], 2));
            $catalog_result->setPriceMax(round($result_raw['aggregations']['feature_price_range']['feature_price_range']['feature_price_range.range_max']['value'], 2));
        }
        //

        $catalog_result->setLimit($this->getLimit());
        $catalog_result->setOffset($this->getOffset());

        if ($this->feature_search) {
            $this->processAggregationsForFeatureSearch($result_raw['aggregations']);
            $catalog_result->setFeatureSearch($this->feature_search);
        }

        if ($this->set_preview) {
            $set_product_ids = [];

            foreach ($catalog_result->getProducts() as $product_head) {
                $set_product_ids = array_merge($set_product_ids, $product_head->getSetProductIds());
                $product_head['set_preview'] = [];
            }

            if ($set_product_ids) {
                $set_products = $this->fetchProductHeads($set_product_ids);

                foreach ($catalog_result->getProducts() as $product_head) {
                    $set_preview = [];

                    $product_ids = $product_head->getSetProductIds();

                    foreach ($product_ids as $product_id) {
                        if (!isset($set_products[$product_id])) {
                            continue;
                        }

                        $set_preview[$product_id] = $set_products[$product_id];
                    }

                    $product_head['set_preview'] = $set_preview;
                }
            }
        }

        return $catalog_result;
    }


    protected function processAggregationsForFeatureSearch(array $aggregations): void
    {
        $features = $this->feature_search->getFeatures();

        foreach ($aggregations as $aggregation_key => $aggregation) {
            if (!str_starts_with($aggregation_key, 'feature_')) {
                continue;
            }

            $feature_id = str_replace('feature_', '', $aggregation_key);
            $feature_id_extension = null;
            if (strpos($feature_id, '.') !== false) {
                [$feature_id, $feature_id_extension] = explode('.', $feature_id, 2);
            }

            $feature = $features[$feature_id] ?? null;

            if (!$feature) {
                continue;
            }

            if ($feature instanceof ProductFeatureRange) {
                switch ($feature_id_extension) {
                    case 'range_min':
                        $feature->setRangeRawMin($aggregation['value']);
                        break;
                    case 'range_max':
                        $feature->setRangeRawMax($aggregation['value']);
                        break;
                    default:
                        //self exclusion mode
                        $feature->setRangeRawMin($aggregation[$aggregation_key][$aggregation_key . '.range_min']['value']);
                        $feature->setRangeRawMax($aggregation[$aggregation_key][$aggregation_key . '.range_max']['value']);
                }
            } else {
                $buckets = $this->extractFeatureAggregationBuckets($aggregations[$aggregation_key], $feature);
                foreach ($buckets as $bucket) {
                    $this->feature_search->addResultValue($aggregation_key, $bucket['key'], $bucket['doc_count']);
                }
            }
        }
    }

    private function extractFeatureAggregationBuckets(array $aggregation_result, ProductFeature $feature): array
    {
        //self exclusion
        if (isset($aggregation_result['filtered_values']['values']['buckets'])) {
            return $aggregation_result['filtered_values']['values']['buckets'];
        }

        //normal
        return $aggregation_result['buckets'] ?? [];
    }

    private function buildEnumFeatureFilter(ProductFeatureEnum $feature): ?array
    {
        $values = $feature->getSelectedValues();

        if (!$values) {
            return null;
        }

        $field = $this->resolveFeatureAggregationField($feature);
        if (!$field) {
            return null;
        }

        if (count($values) === 1) {
            return ['term' => [$field => current($values)]];
        }

        if ($feature->getMultiSelectMode() === ProductFeatureEnum::MULTISELECT_MODE_OR) {
            return ['terms' => [$field => $values]];
        }

        $must = [];
        foreach ($values as $value) {
            $must[] = ['term' => [$field => $value]];
        }

        return ['bool' => ['must' => $must]];
    }

    private function resolveFeatureAggregationField(ProductFeature $feature): ?string
    {
        $feature_id = $feature->getFeatureId();

        if (is_numeric($feature_id)) {
            return 'features.feature_' . $feature_id;
        }

        if ($feature_id === 'goodie_id') {
            return 'goodie_ids';
        }

        if ($feature_id === 'brand_name') {
            return 'features.feature_brand_name';
        }

        if ($feature_id === 'price_range') {
            return 'vk_brutto';
        }

        return null;
    }

    private function buildSelfExclusionFilters(array $base_filters, array $feature_specific_filters, string $feature_id): array
    {
        $filters = $base_filters;

        foreach ($feature_specific_filters as $current_feature_id => $filters_for_feature) {
            //Aachtung: php arrays + nummerische strings -> int
            $current_feature_id = (string)$current_feature_id;

            if ($current_feature_id === $feature_id) {
                continue;
            }

            foreach ($filters_for_feature as $filter) {
                $filters[] = $filter;
            }
        }

        return $filters;
    }

    private function wrapAggregationWithSelfExclusion(array $aggregation, array $filters): array
    {
        return [
            'global' => new stdClass(),
            'aggs' => [
                'filtered_values' => [
                    'filter' => $this->buildAggregationFilterWrapper($filters),
                    'aggs' => [
                        'values' => $aggregation,
                    ],
                ],
            ],
        ];
    }

    private function buildAggregationFilterWrapper(array $filters): array
    {
        if (!$filters) {
            return ['match_all' => new stdClass()];
        }

        return ['bool' => ['filter' => $filters]];
    }

    private function buildRangeFeatureFilter(ProductFeatureRange $feature): ?array
    {
        $field = $feature->getFeatureId() === 'price_range'
            ? 'vk_brutto'
            : 'features.feature_' . $feature->getFeatureId();

        $range = [];

        if ($feature->getSelectedMinValue() !== null) {
            $range['gte'] = $feature->getSelectedMinValue();
        }

        if ($feature->getSelectedMaxValue() !== null) {
            $range['lte'] = $feature->getSelectedMaxValue();
        }

        if (!$range) {
            return null;
        }

        return ['range' => [$field => $range]];
    }

    public function getHersteller()
    {
        return $this->feature_search->getBrands();
    }


    public function getTopBrands(): array
    {
        $body = [
            'size' => 0,
            'query' => ['term' => ['filter_list_status' => true]],

            'aggregations' => [
                'brands' => [
                    'terms' => [
                        'field' => 'brand_ids',
                        'size' => 250
                    ]
                ]
            ]
        ];

        $params = [
            'index' => $this->index,
            'type' => 'product',
            'body' => $body
        ];

        $result_raw = $this->client->search($params)->asArray();

        $buckets = $result_raw['aggregations']['brands']['buckets'];

        $brands = $this->data_provider_sparepart_brands->getTopBrands();

        $result = [];

        foreach ($buckets as $value) {
            $brand_id = $value['key'];
            if (isset($brands[$brand_id])) {
                $result[] = $brands[$brand_id];
            }
        }

        return $result;
    }


    public function setDeviceId(int $device_id): void
    {
        $this->device_ids = [$device_id];
    }

    public function setDeviceIds(array $device_ids): void
    {
        $this->device_ids = $device_ids;
    }

    public function setDeviceContext(ShopCatalogDeviceContext $device_context, bool $change_scope = false): void
    {
        $this->device_context = $device_context;

        if ($change_scope) {
            $this->setDeviceId($device_context->getDeviceId());
        }
    }
}
