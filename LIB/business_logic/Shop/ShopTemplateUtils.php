<?php

namespace wws\Shop;

use Exception;
use wws\Product\Media\ProductMediaDisplayServiceUrlGenerator;
use wws\Product\ProductConst;
use wws\Shop\Profile\ShopProfile;

class ShopTemplateUtils
{

    /**
     * @var ShopProfile
     */
    private $profile;
    /**
     * @var ProductMediaDisplayServiceUrlGenerator
     */
    private $product_media_url_generator;

    public function __construct(
        ShopProfile $profile,
        ProductMediaDisplayServiceUrlGenerator $product_media_url_generator
    ) {
        $this->profile = $profile;
        $this->product_media_url_generator = $product_media_url_generator;
    }


    public function smartyCreatePictureUrl(array $params): string
    {
        if (!$params['product_media_id']) {
            return '/assets/no_pic' . $params['size'] . '.jpg';
        }

        return $this->product_media_url_generator->buildShopPictureUrl($params['product_media_id'], $params['product_shop_url'], $params['size']);
    }

    public function smartyPictureAlt(array $params): string
    {
        $product_name = $params['product_name'];
        $product_media_id = $params['product_media_id'];

        if (!$product_media_id) {
            return 'Leider ist für diesen Artikel kein Bild vorhanden!';
        }

        return 'Bild: ' . $product_name;
    }


    public function smartyLieferbaranzeige(array $params): string
    {
        $express = $params['express'] ? $params['express'] : false;

        return $this->getHtmlLieferbaranzeige($params['id'], false, true, true, $express);
    }

    public function smartyLieferbaranzeigeLong(array $params): string
    {
        $express = isset($params['express']) ? $params['express'] : false;

        return $this->getHtmlLieferbaranzeige($params['id'], true, true, true, $express);
    }

    public function smartyLieferbaranzeigeCompact(array $params): string
    {
        global $profile;
        $id = $params['id'] ?? null;
        if ($id === null) {
            return '';
        }

        $ver = $profile->getLieferbarkeiten();
        if (!isset($ver[$id])) {
            return '';
        }
        $text = (string)$ver[$id]['lieferbar_text'];
        $express = $params['express'] ?? false;

        $icon = match (true) {
            $id >= ProductConst::LIEFERBAR_ID_UNKNOWN => '/assets/icons/availability-out-of-stock.svg',
            $id >= ProductConst::LIEFERBAR_ID_MIN_2_WEEKS => '/assets/icons/availability-preorder.svg',
            $id >= ProductConst::LIEFERBAR_ID_8_TO_15 => '/assets/icons/availability-8-15wd.svg',
            $id >= ProductConst::LIEFERBAR_ID_4_TO_8 => '/assets/icons/availability-4-8wd.svg',
            $id >= ProductConst::LIEFERBAR_ID_2_TO_6 => '/assets/icons/availability-2-6wd.svg',
            $id >= ProductConst::LIEFERBAR_ID_1_TO_3 && $express => '/assets/icons/availability-express.svg',
            $id >= ProductConst::LIEFERBAR_ID_1_TO_3 => '/assets/icons/availability-1-3wd.svg',
            default => '/assets/icons/availability-2-6wd.svg',
        };

        // Check if text should be green (same color as green icons)
        $textStyle = '';
        if (($id >= ProductConst::LIEFERBAR_ID_2_TO_6 && $id < ProductConst::LIEFERBAR_ID_4_TO_8) ||
            ($id >= ProductConst::LIEFERBAR_ID_1_TO_3 && $id < ProductConst::LIEFERBAR_ID_2_TO_6)) {
//            $textStyle = ' style="color: #349357;"';
        }

        return '<img class="icon" src="' . $icon . '" alt="" />'
            . '<span class="text"' . $textStyle . '>' . htmlspecialchars($text, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8') . '</span>';
    }


    private function getHtmlLieferbaranzeige($lieferbar_id, $text = true, $link = true, $image = true, $express = false): string
    {
        global $profile;

        $ver = $profile->getLieferbarkeiten();

        if (!isset($ver[$lieferbar_id])) {
            return '';
        }

        $return = $ver[$lieferbar_id]['lieferbar_text'];

        $indicator_image = '<svg class="avaind"><circle cx="50%" cy="50%" r="50%" class="ava' . $lieferbar_id . '"/><title>' . $return . '</title></svg>';
        //'<img src="/assets/lieferzeit_'.$lieferbar_id.'.png" class="lieferzeitt" alt="'.$return.'" title="'.$return.'" width="15" height="15">';
        if (!$text) {
            return $indicator_image;
        }

        $mikrodaten_verfuegbarkeit = 'in_stock';

        if ($lieferbar_id >= 30) {
            $mikrodaten_verfuegbarkeit = 'out_of_stock';
        }
        if ($lieferbar_id >= 40) {
            $mikrodaten_verfuegbarkeit = 'preorder';
        }

        //if($express) $return = $return.' <img src="'.$_cfg['grafik_url'].'express.png" alt="Express fähig" style="vertical-align: middle; margin-left: 30px;" />';
        if ($express) {
            $return = $return . ', <b>Express möglich</b>';
        }

        if ($image) {
            $return = $indicator_image . ' &nbsp;' . $return;
        }

        if ($link) {
            $return = '<a href="javascript:wws_shop.verfuegbarkeit.show()" class="lieferzeit" title="' . $ver[$lieferbar_id]['lieferbar_text_long'] . '">' . $return . '</a>';
        }

        return $return;
    }

    public function smartyContent(array $params, $smarty): string
    {
        $entry = $this->profile->getShopCms()->getEntry($params['id']);

        if ($entry) {
            return $smarty->fetch('string:' . $entry->getContent());
        }

        trigger_error('Content "' . $params['id'] . '" not found', E_USER_NOTICE);

        return '';
    }

    public function smartyCmsParse(string $text): string
    {
        try {
            return $this->profile->getShopCms()->parse($text);
        } catch (Exception $e) {
            return $text;
        }
    }

    /**
     * Smarty modifier: Shorten a product name to at most N words, but stop earlier
     * if the word "für"/"fur" (case-insensitive, handles umlauts and common variants)
     * appears sooner. Example usage in templates:
     *   {{$name|product_name_short:3}}
     *
     * @param string $text Full product name
     * @param int $max_words Maximum number of words (default 3)
     * @return string Shortened name
     */
    public function smartyProductNameShort(string $text, int $max_words = 3): string
    {
        $text = trim((string)$text);
        if ($text === '') {
            return '';
        }

        if (!is_int($max_words) || $max_words < 1) {
            $max_words = 3;
        }

        $words = preg_split('/\s+/u', $text) ?: [];
        $out = [];
        foreach ($words as $w) {
            $norm = mb_strtolower($w, 'UTF-8');
            // Trim common punctuation/hyphen for the detection only
            $norm = trim($norm, ".,;:!?()[]{}\"'«»„“‚’");
            $norm = rtrim($norm, '-');

            // Detect standalone "für" variants: für, fur, fuer
            if (preg_match('/^f(?:ü|u|ue)r$/u', $norm)) {
                break;
            }

            $out[] = $w;
            if (count($out) >= $max_words) {
                break;
            }
        }

        return implode(' ', $out);
    }

    public function smartyShadowbox($params, $content, $smarty, $repeat): string
    {
        $attributes = '';

        if ($params['width']) {
            $attributes .= ' style="width: ' . $params['width'] . '; overflow: hidden; float: left;"';
        }

        if ($attributes) {
            $attributes = ' ' . $attributes;
        }

        $class = $params['class'] ? ' ' . $params['class'] : '';

        return '<div class="shadowbox' . $class . '"' . $attributes . '>' . $content . '</div>';
    }
}
