<?php

namespace wws\Shop\Tracking;

use wws\Shop\Tracking\Entities\TrackerProduct;

class ShopTrackingModuleGA4 implements ShopTrackingModule
{
    private string $tag_id = 'G-KHR4T3M0WT'; //AW-1000651291

    public function event(string $event_type, ShopTracker $shop_tracker): void
    {
    }

    public function render(string $event_type, ShopTracker $shop_tracker): string
    {
        if ($event_type === $shop_tracker::EVENT_HEADER_SCRIPT) {
            return $this->renderGa4Global($shop_tracker);
        }

        if ($event_type === $shop_tracker::EVENT_HEADER) {
            return $this->renderHeader();
        }

        if ($event_type === $shop_tracker::EVENT_BODY_BOTTOM_SCRIPT) {
            return $this->ga4PurchaseEvent($shop_tracker);
        }

        if ($event_type === $shop_tracker::EVENT_BASKET_AJAX) {
            return $this->renderBasketEvent($shop_tracker);
        }

        return '';
    }

    public function renderHeader(): string
    {
        if (isset($_SESSION['env-sgtm'])) {
            return '<script type="text/plain" data-cmp-src="https://cdn.smartgoods.de/gtag/js?id=' . $this->tag_id . '" class="cmplazyload" data-cmp-vendor="s26"></script>';
        }

        return '<script type="text/plain" data-cmp-src="https://www.googletagmanager.com/gtag/js?id=' . $this->tag_id . '" class="cmplazyload" data-cmp-vendor="s26"></script>';
        //return '<script async type="text/plain" src="https://www.googletagmanager.com/gtag/js?id=' . $this->tag_id . '"></script>';
    }

    public function ga4PurchaseEvent(ShopTracker $shop_tracker): string
    {
        $transaction = $shop_tracker->getTransaction();

        if (!$transaction->isTransaction()) {
            return '';
        }

        $data = [
            'transaction_id' => $transaction->getOrderId(),
            'value' => $transaction->getTotalAmountGross(),
            'tax' => $transaction->getTaxAmount(),
            'shipping' => $transaction->getShippingAmountGross(),
            'currency' => $transaction->getCurrency(),
            'items' => []
        ];

        foreach ($transaction->getItems() as $item) {
            $item_data = $this->transformTrackerProduct($item['tracker_product']);

            $item_data['price'] = $item['price'];
            $item_data['quantity'] = $item['quantity'];

            $data['items'][] = $item_data;
        }

        $return = "gtag('event', 'purchase',  " . json_encode($data) . ");\n";

        return $return;
    }

    public function renderGa4Global(ShopTracker $shop_tracker): string
    {
        $return = "gtag('js', new Date());";

        $data = [];
        if ($shop_tracker->getPageType()) {
            $data['page_type'] = $shop_tracker->getPageType();
        }

        $cat_details = $shop_tracker->getCatDetails();

        if (is_array($cat_details)) {
            $data['product_type'] = $cat_details['product_type'];
            $data['cat_main'] = $cat_details['cat_name_main_webshop'];
            $data['cat_path'] = $cat_details['cat_path_string'];
        }

        if ($shop_tracker->getPriceGroup()) {
            $data['price_group'] = $shop_tracker->getPriceGroup();
        }

        if ($data) {
            $return .= "gtag('set', " . json_encode($data) . ");";
        }

        if (isset($_SESSION['env-sgtm'])) {
            $return .= "gtag('config', '" . $this->tag_id . "', {'send_page_view':false, 'server_container_url':'https://cdn.smartgoods.de/'});";
        } else {
            $return .= "gtag('config', '" . $this->tag_id . "', {'send_page_view':false});";
        }

        $return .= "gtag('consent', 'default', {'ad_storage':'denied','analytics_storage':'denied','ad_user_data':'denied','ad_personalization':'denied','wait_for_update':500});
dataLayer.push({'event': 'default_consent'});";
        $return .= "gtag('event', 'page_view', " . json_encode($data) . ");\n";

        $tracker_product = $shop_tracker->getProductView();
        if ($tracker_product) {
            $data = [
                'currency' => 'EUR',
                'value' => $tracker_product->getVkBrutto(),
                'items' => [
                    $this->transformTrackerProduct($tracker_product)
                ]
            ];
            $return .= "gtag('event', 'view_item', " . json_encode($data) . ");\n";
        }

        if ($shop_tracker->getProductImpressions()) {
            $tracker_products = $shop_tracker->getProductImpressions();

            if ($shop_tracker->getSearchs()) {
                $search = current($shop_tracker->getSearchs());

                $type = 'view_search_results';
                $data = [
                    'search_term' => $search->getSearchTerm(),
                    'items' => []
                ];
            } else {
                $type = 'view_item_list';
                $data = [
                    'item_list_id' => 'category',
                    'item_list_name' => 'Kategorie',
                    'items' => []
                ];
            }

            foreach ($tracker_products as $entry) {
                $data['items'][] = $this->transformTrackerProduct($entry['tracker_product'], $entry['position']);
            }

            $return .= "gtag('event', '" . $type . "', " . json_encode($data) . ");\n";
        }

        return $return;
    }

    public function renderBasketEvent(ShopTracker $shop_tracker): string
    {
        if (!$shop_tracker->getBasketActions()) {
            return '';
        }

        $return = "<script>dataLayer.push({ ecommerce: null });\n";
        foreach ($shop_tracker->getBasketActions() as $action) {
            $data = [
                'currency' => 'EUR',
                'value' => $action['tracker_product']->getVkBrutto() * $action['quantity'],
                'items' => [
                    $this->transformTrackerProduct($action['tracker_product'])
                ]
            ];
            $return .= "gtag('event', 'add_to_cart', " . json_encode($data) . ")\n";
        }
        $return .= '</script>';

        return $return;
    }

    private function transformTrackerProduct(TrackerProduct $tracker_product, int $index = 0, int $quantity = 0): array
    {
        $result = [];

        $result['item_id'] = $tracker_product->getProductId();
        $result['item_name'] = $tracker_product->getProductName();

        $result['item_category'] = $tracker_product->getCatPathAsString();
        $result['item_brand'] = $tracker_product->getBrandName();
        $result['dimension1'] = $tracker_product->getProductTypeAsText();
        $result['dimension2'] = $tracker_product->getLieferbaranzeigeAsText();

        if ($tracker_product->isVkBrutto()) {
            $result['price'] = $tracker_product->getVkBrutto();
        }

        //zwecks clicks
        $result['index'] = $index;

        if ($quantity) {
            $result['quantity'] = $quantity;
        }
        //

        return $result;
    }
}
