<?php

namespace wws\Shop\Tracking;

class ShopTrackingModuleGTM implements ShopTrackingModule
{
    private string $gtm_id;

    public function __construct(string $gtm_id)
    {
        $this->gtm_id = $gtm_id;
    }

    public function event(string $event_type, ShopTracker $shop_tracker): void
    {
    }

    public function render(string $event_type, ShopTracker $shop_tracker): string
    {
        //Achtung: der google tag/ga4 verwendet auch die dataLayer, der TagManager bekommt dadurch schon über \wws\Shop\Tracking\ShopTrackingModuleGA4 die relvanten in die DataLayer

        if (isset($_SESSION['env-sgtm'])) {
            if ($event_type === $shop_tracker::EVENT_HEADER) {
                return "<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s);j.async=true;j.src=\"https://cdn.smartgoods.de/9deyzytod.js?\"+i;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','cv2i09=DQtJPi40SDBdWCY%2FODVWQhJSRldLXwEKWRwZGAgNAgsLCwdXHhw%3D');</script>";
            }

            if ($event_type === $shop_tracker::EVENT_BODY_TOP) {
                return '<noscript><iframe src="https://cdn.smartgoods.de/ns.html?id=GTM-T97RFBL3" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>';
            }
        }

        if ($event_type === $shop_tracker::EVENT_HEADER) {
            return "<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';
j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','" . $this->gtm_id . "');</script>";
        }

        if ($event_type === $shop_tracker::EVENT_BODY_TOP) {
            return '<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=' . $this->gtm_id . '" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>';
        }

        return '';
    }
}
