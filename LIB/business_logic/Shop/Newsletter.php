<?php

namespace wws\Shop;

use db;
use wws\business_structure\Shop;
use wws\Mails\Mail;

class Newsletter
{
    public static function subscribe($anrede, $vorname, $name, $email, $confirm = false, int $shop_id = Shop::ALLEGO)
    {
        $db = db::getInstance();

        $id = $db->fieldQuery("
            SELECT
                newsletter.id
            FROM
                newsletter
            WHERE
                newsletter.email LIKE '" . $db->escape($email) . "' AND
                newsletter.shop_id = '$shop_id'
        ");

        if ($id) {
            return false;
        }

        if ($anrede == 1) {
            $anrede_ = 'Frau';
        } else {
            $anrede_ = 'Herr';
        }

        $crypt = substr(md5(microtime()), 0, 8);
        if ($confirm) {
            $empf = 0;
        } else {
            $empf = 1;
        }

        $db->query("
            INSERT INTO
                newsletter
            SET
                newsletter.anrede = '$anrede_',
                newsletter.vorname = '" . $db->escape($vorname) . "',
                newsletter.name = '" . $db->escape($name) . "',
                newsletter.email = '" . $db->escape($email) . "',
                newsletter.shop_id = '" . $db->escape($shop_id) . "',
                newsletter.empfangen = '" . $db->escape($empf) . "',
                newsletter.added = NOW(),
                newsletter.crypt = '$crypt'
        ");

        $email_id = $db->insert_id();

        if ($confirm) {
            $mail = new Mail();

            //@todo url über __construct, email_id raus und nur über crypt machen (crypt dann natürlich nicht nur 8 hex zeichen!) (-> damit nicht nachvollziehbar ist weiveiel adressen wir haben)
            $mail->assignArray([
                'link' => 'https://www.smartgoods.de/newsletter.html?email_id=' . $email_id . '&tan=' . $crypt,
                'anrede' => $anrede,
                'name' => $name
            ]);

            $mail->setEmpf($email);
            $mail->setShop($shop_id);
            $mail->loadVorlage('newsletter_bestaetigung');

            $mail->trySend();
        } else {
            //$newsl->activatebyuid($newsl->email2uniqid($email)); // newsletter aktiviert
        }
        return true;
    }


    public static function unsubscribe($email, $shop_id = Shop::ALLEGO)
    {
        $shop_id = (int)$shop_id;

        db::getInstance()->query("
                        UPDATE
                            newsletter
                        SET
                            newsletter.empfangen = 2
                        WHERE
                            newsletter.email = '" . db::getInstance()->escape($email) . "' AND
                            newsletter.shop_id = '$shop_id'
        ");

        if (db::getInstance()->affected_rows() == 1) {
            return true;
        } else {
            return false;
        }
    }

    public static function confirmSubscription($email_id, $tan)
    {
        $db = db::getInstance();

        $db->query("
            UPDATE
                newsletter
            SET
                newsletter.empfangen = 1    
            WHERE
                newsletter.id = '" . (int)$email_id . "' AND
                newsletter.crypt = '" . $db->escape($tan) . "'
        ");

        if ($db->affected_rows() == 1) {
            $daten = db::getInstance()->singleQuery("
                SELECT
                    newsletter.email
                FROM
                    newsletter
                WHERE
                    newsletter.id = '" . (int)$email_id . "' 
            ");
        } else {
            return false;
        }

        return true;
    }
}
