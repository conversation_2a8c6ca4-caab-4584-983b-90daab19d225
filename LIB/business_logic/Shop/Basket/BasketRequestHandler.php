<?php

namespace wws\Shop\Basket;

class BasketRequestHandler
{
    /**
     * @var Basket
     */
    protected $warenkorb;

    /**
     * @var BasketService
     */
    protected $warenkorb_service;


    public function __construct(BasketService $warenkorb_service, Basket $warenkorb)
    {
        $this->warenkorb = $warenkorb;
        $this->warenkorb_service = $warenkorb_service;
    }

    /**
     * legacy code gebündelt und aus controller gelöst
     *
     * @return BasketRequestHandlerResult
     */
    public function execute()
    {
        $result = new BasketRequestHandlerResult();

        $warenkorb_item = false;

        if (!empty($_REQUEST['add_item'])) {
            $menge = isset($_REQUEST['menge']) ? ((int)$_REQUEST['menge']) : 1;
            $menge = max(1, $menge);

            $warenkorb_item = $this->warenkorb_service->addProductShopUnique((int)$_REQUEST['add_item'], $menge);
        }

        if (!empty($_POST['add_item_extended'])) {
            $menge = isset($_REQUEST['menge']) ? ((int)$_REQUEST['menge']) : 1;
            $menge = max(1, $menge);
            $warenkorb_item = $this->warenkorb_service->addProductShopUnique((int)$_REQUEST['product_id'], $menge);

            if ($warenkorb_item && isset($_POST['service']) && $_POST['service'] && is_array($_POST['service'])) {
                foreach ($_POST['service'] as $service_id => $value) {
                    if ($value != 1) {
                        continue;
                    }

                    $this->warenkorb_service->addService($warenkorb_item, $service_id);
                }

                $error = $this->warenkorb_service->servicesCheckError($warenkorb_item);

                if ($error) {
                    $error = $this->warenkorb_service->convertExtraServicesErrorToText($warenkorb_item->getProductId(), $error);
                    $result->setServicesMessage($error);
                    $this->warenkorb_service->removeItemByItemId($warenkorb_item->getItemId());

                    unset($warenkorb_item);
                }
            }
        }

        if (!empty($_POST['add_item_service'])) {
            $warenkorb_item = $this->warenkorb->searchItemByProductId((int)$_POST['product_id']);

            if ($warenkorb_item && $_POST['service'] && is_array($_POST['service'])) {
                foreach ($_POST['service'] as $service_id => $value) {
                    if ($value != 1) {
                        continue;
                    }

                    $this->warenkorb_service->addService($warenkorb_item, $service_id);
                }

                $error = $this->warenkorb_service->servicesCheckError($warenkorb_item);

                if ($error) {
                    $error = $this->warenkorb_service->convertExtraServicesErrorToText($warenkorb_item->getProductId(), $error);

                    $result->setServicesMessage($error);
                }
            }
        }

        if (!empty($_REQUEST['add_item_variation'])) {
            $warenkorb_item = $this->warenkorb_service->addProductVariationShop((int)$_REQUEST['add_item_variation'], $_REQUEST);
        }

        if (!empty($_REQUEST['service_set'])) {
            if ($_REQUEST['value']) {
                $_REQUEST['service_add'] = $_REQUEST['service_set'];
            } else {
                $_REQUEST['service_remove'] = $_REQUEST['service_set'];
            }
        }

        if (!empty($_REQUEST['service_add'])) {
            $warenkorb_item = $this->warenkorb->searchItem((int)$_REQUEST['item_id']);

            if ($warenkorb_item) {
                $this->warenkorb_service->addService($warenkorb_item, $_REQUEST['service_add']);
                $result->setIsService(true);

                $error = $this->warenkorb_service->servicesCheckError($warenkorb_item);

                if ($error) {
                    $error = $this->warenkorb_service->convertExtraServicesErrorToText($warenkorb_item->getProductId(), $error);

                    $result->setServicesMessage($error);
                }
            }
        }

        if (!empty($_REQUEST['service_remove'])) {
            $warenkorb_item = $this->warenkorb->searchItem((int)$_REQUEST['item_id']);

            if ($warenkorb_item) {
                $this->warenkorb_service->removeService($warenkorb_item, $_REQUEST['service_remove']);
                $result->setIsService(true);

                $error = $this->warenkorb_service->servicesCheckError($warenkorb_item);

                if ($error) {
                    $error = $this->warenkorb_service->convertExtraServicesErrorToText($warenkorb_item->getProductId(), $error);

                    $result->setServicesMessage($error);
                }
            }
        }

        if (!empty($_REQUEST['del_item'])) {
            try {
                $this->warenkorb_service->removeItemByItemId((int)$_REQUEST['del_item']);
            } catch (BasketExceptionProductNotFound $e) {

            }
        }


        //aus warenkorb controller
        if (isset($_REQUEST['cmd']) and $_REQUEST['cmd'] === 'delitem') {
            $items = explode(',', $_REQUEST['items']);
            foreach ($items as $item) {
                $this->warenkorb_service->removeItemByItemId($item);
            }
        }

        if (isset($_REQUEST['trusted_shops_product'])) {
            $trusted_shops_product = $_REQUEST['trusted_shops_product'];

            $this->warenkorb_service->removeTrustedshop();

            if ($trusted_shops_product) {
                $this->warenkorb_service->addTrustedshop($trusted_shops_product);
            }
        }
        /* @var $warenkorb Basket */

        if (isset($_POST['quantity'])) {
            if (is_array($_POST['quantity'])) {
                foreach ($_POST['quantity'] as $item_id => $quantity) {
                    if ($quantity === '0') {
                        $this->warenkorb_service->removeItemByItemId($item_id);
                    } elseif ((int)$quantity <= 0) {
                        $this->warenkorb_service->setQuantityByItemId($item_id, 1);
                    } else {
                        $this->warenkorb_service->setQuantityByItemId($item_id, (int)$quantity);
                    }
                }
            }
        }

        if ($warenkorb_item && !$result->getWarenkorbItem()) {
            $result->setWarenkorbItem($warenkorb_item);
        }

        return $result;
    }
}
