<?php

namespace wws\Shop\Basket;

use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\db\SqlFunction;
use bqp\Exceptions\DevException;
use bqp\extern\SpbGarantGarantie\SpbGarantGarantie;
use bqp\Utils\ArrayUtils;
use db;
use service_loader;
use wws\Country\CountryConst;
use wws\Country\CountryRepository;
use wws\Customer\Authentication\CustomerAuthentication;
use wws\Customer\Authentication\CustomerAuthenticationException;
use wws\Order\OrderConst;
use wws\Shipment\RateCalculator\RateCalculatorShipmentPosition;

class Basket
{
    protected $shop_id;

    /**
     * @var BasketItem[]
     */
    protected $warenkorb_items = [];

    private $shop_referenz;

    protected $order_id;

    protected $debug_id = null;

    /**
     * speichert zusätzliche Daten für die es kein Schema gibt z.B. für Zahlungsabwicklung
     * @var array
     */
    protected $extra_data = [];
    protected $extra_customer_data = [];

    protected $is_phone_required = true;

    /**
     * @var RateCalculatorShipmentPosition[]
     */
    protected $shipment_positions = [];

    protected $min_shipping_price;
    protected $shipping_valid = false;

    protected $customer_id;
    protected $customer_nr = '';
    protected $customer_data;
    protected $password_for_new_account;
    protected $delivery_data;
    protected $order_data;
    protected $is_paypal_express = false;

    public function __construct($shop_id)
    {
        $this->setDefaultCustomerData();
        $this->setDefaultDeliveryData();
        $this->setDefaultOrderData();

        $this->setShopId($shop_id);

        $this->loadDefaults();

        $this->debug_id = mt_rand(100000, 999999);
    }

    public function loadDefaults()
    {
        $this->setZahlungsart(OrderConst::PAYMENT_VORKASSE);
        $this->setVersandKundeOpt('nor');
        if (!$this->getCountryId()) {
            $this->setCountryId(CountryConst::DE);
        }
    }

    public function setShopId($shop_id)
    {
        $this->shop_id = $shop_id;
    }

    public function getShopId()
    {
        return $this->shop_id;
    }

    public function setDstCountryId($country_id)
    {
        $this->setCountryId($country_id);
    }

    public function getDstCountryId()
    {
        return $this->getCountryId();
    }

    public function getShopReferenz()
    {
        return $this->shop_referenz;
    }

    public function setShopReferenz($value)
    {
        $this->shop_referenz = $value;
    }

    public function createShopReferenz()
    {
        $this->shop_referenz = substr(md5(uniqid(mt_rand(), true)), 0, 24);

        return $this->shop_referenz;
    }

    public function setOrderId($order_id)
    {
        $this->order_id = $order_id;
    }

    public function getOrderId()
    {
        return $this->order_id;
    }

    // <editor-fold defaultstate="collapsed" desc="Hinzufügen/Modifizieren und Auslesen von Produkten">

    /**
     * @param $product_id
     * @return BasketItem
     * @throws DevException
     */
    public function searchItemByProductId($product_id)
    {
        $items = $this->searchItemsByProductId($product_id);

        $anzahl = count($items);
        if ($anzahl === 0) {
            return null;
        }

        if ($anzahl === 1) {
            return $items[0];
        }

        throw new DevException('multiple warenkorb_items with the same $product_id');
    }

    public function searchItemsByProductId($product_id)
    {
        $found_items = [];
        foreach ($this->warenkorb_items as $warenkorb_item) {
            if ($warenkorb_item->getProductId() == $product_id) {
                $found_items[] = $warenkorb_item;
            }
        }

        return $found_items;
    }

    public function searchItem($item_id)
    {
        if ($item_id instanceof BasketItem) {
            return $item_id;
        }

        if (!$this->warenkorb_items[$item_id]) {
            return false;
        }

        return $this->warenkorb_items[$item_id];
    }

    /**
     * @param BasketItem $warenkorb_item
     * @return BasketItem
     */
    public function addWarenkorbItem(BasketItem $warenkorb_item)
    {
        $item_id = ArrayUtils::getFreeNumericKey($this->warenkorb_items);

        $warenkorb_item->setItemId($item_id);
        $warenkorb_item->setWarenkorb($this);

        $this->warenkorb_items[$item_id] = $warenkorb_item;

        return $warenkorb_item;
    }

    public function removeItem($item_id)
    {
        if (!$this->warenkorb_items[$item_id]) {
            return;
        }
        unset($this->warenkorb_items[$item_id]);
    }

    public function clear(): void
    {
        $this->password_for_new_account = null;
        $this->is_paypal_express = false;
        $this->warenkorb_items = [];
        $this->shop_referenz = '';
        $this->clearExtraData();
        $this->loadDefaults();
    }

    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Methoden">
    /**
     * prüft ob der warenkorb leer ist
     * @return bool
     */
    public function isEmpty()
    {
        if ($this->warenkorb_items) {
            return false;
        }

        return true;
    }

    /**
     * gibt den Warenwert des Warenkorbs zurück
     * @return float
     */
    public function getWarenwert()
    {
        $summe = 0;

        foreach ($this->getWarenkorbItems() as $item) {
            $summe += $item->getVkBruttoTotal();
        }

        return $summe;
    }

    /**
     * gibt den Warenwert des Warenkorbs zurück
     * @return float
     */
    public function getWarenwertNetto()
    {
        $summe = 0;

        foreach ($this->getWarenkorbItems() as $item) {
            $summe += $item->getVkNettoTotal();
        }

        return $summe;
    }

    public function getTaxes()
    {
        $taxes = [];
        foreach ($this->getWarenkorbItemsWithVirtVersand() as $item) {
            if (!isset($taxes[$item->getMwstSatz()])) {
                $taxes[$item->getMwstSatz()] = 0;
            }

            $taxes[$item->getMwstSatz()] += $item->getVkBruttoTotal() - $item->getVkNettoTotal();
        }

        return $taxes;
    }

    /**
     * Summe aller echten Produktpositionen (brutto)
     * @return float
     */
    public function getArtikelSummeBrutto(): float
    {
        $summe = 0.0;

        foreach ($this->getWarenkorbItems() as $item) {
            if ($item->getOrgItemId() !== false) {
                continue;
            }

            $typ = $item->getWarenkorbProductTyp();

            if ($typ === OrderConst::WARENKORB_TYP_PRODUKT || $typ === OrderConst::WARENKORB_TYP_ASWO) {
                $summe += $item->getVkBruttoTotal();
            }
        }

        return $summe;
    }

    /**
     * Summe aller Service-Positionen (brutto)
     * @return float
     */
    public function getServicesSummeBrutto(): float
    {
        $summe = 0.0;

        foreach ($this->getWarenkorbItems() as $item) {
            if ($item->getOrgItemId() !== false || $item->getWarenkorbProductTyp() === OrderConst::WARENKORB_TYP_LEISTUNG) {
                $summe += $item->getVkBruttoTotal();
            }
        }

        return $summe;
    }


    /**
     * gibt den Wert des Warenkorbs inkl. Versandkosten zurück
     * @return float
     */
    public function getGesamtPreis()
    {
        $summe = $this->getWarenwert();

        //versandkosten
        foreach ($this->getShipmentPositions() as $shipment_position) {
            $summe += $shipment_position->getVkBrutto();
        }

        return $summe;
    }

    /**
     * gibt den Netto Warenkorbwert inkl. Versandkosten zurück
     * @return float
     */
    public function getGesamtPreisNetto()
    {
        $summe = 0;

        foreach ($this->getWarenkorbItemsWithVirtVersand() as $item) {
            $summe += $item->getVkNettoTotal();
        }

        return $summe;
    }

    /**
     * gibt die Summe aller Stuern für diesen Warenkorb zurück
     * @return float
     */
    public function getGesamtSteuern()
    {
        return $this->getGesamtPreis() - $this->getGesamtPreisNetto();
    }

    /**
     * gibt die Anzahl der Artikel zurück die sich im Warenkorb befinden
     * @return int
     */
    public function getArtikelCount()
    {
        $anzahl = 0;

        foreach ($this->warenkorb_items as $item) {
            $anzahl += $item->getAnzahl();
        }


        return $anzahl;
    }


    public function getMinShippingPrice()
    {
        return $this->min_shipping_price;
    }

    public function getCurrency()
    {
        return 'EUR';
    }

    // </editor-fold>

    public function hasAswoPositions()
    {
        foreach ($this->getWarenkorbItems() as $warenkorb_item) {
            if ($warenkorb_item->getWarenkorbProductTyp() === 'aswo') {
                return true;
            }
        }

        return false;
    }

    // <editor-fold defaultstate="collapsed" desc="Trustedshops">
    public function hasTrustedshops()
    {
        foreach ($this->warenkorb_items as $item) {
            if ($item->getWarenkorbProductTyp() === 'trusted_shops') {
                return true;
            }
        }

        return false;
    }

    public function getTrustedshopsProductCode()
    {
        foreach ($this->warenkorb_items as $item) {
            if ($item->getWarenkorbProductTyp() === 'trusted_shops') {
                return $item->getWarenkorbProductTypValue();
            }
        }

        return false;
    }

    public function getTrustedshopsVkBrutto()
    {
        foreach ($this->warenkorb_items as $item) {
            if ($item->getWarenkorbProductTyp() === 'trusted_shops') {
                return $item->getVkBrutto();
            }
        }

        return false;
    }
    // </editor-fold>

    /**
     * @return bool
     */
    public function hasCoupons()
    {
        return (bool)$this->getCoupons();
    }

    /**
     * @return BasketItem[]
     */
    public function getCoupons()
    {
        $return = [];
        foreach ($this->warenkorb_items as $warenkorb_item) {
            if ($warenkorb_item->getWarenkorbProductTyp() === 'gutschein') {
                $return[] = $warenkorb_item;
            }
        }

        return $return;
    }


    // <editor-fold defaultstate="collapsed" desc="Utils">

    /**
     * @param array $product_ids
     * @return bool
     */
    public function searchFreeProductId($product_ids)
    {
        foreach ($product_ids as $product_id) {
            $item = $this->searchItemByProductId($product_id);

            if (!$item) {
                return $product_id;
            }
        }

        return false;
    }
    // </editor-fold>

    //@todo rausehmen
    public function loadByCustomerId($customer_id)
    {
        $daten = db::getInstance()->singleQuery("
                SELECT
                    customers.customer_id,
                    customers.customer_nr,
                    customers.anrede,
                    customers.firma,
                    customers.name,
                    customers.vorname,
                    customers.strasse,
                    customers.adresse2,
                    customers.plz,
                    customers.ort,
                    customers.country_id,
                    customers.telefon,
                    customers.mobil,
                    customers.fax,
                    customers.email,

                    customers.zahlungsart,

                    customers.anrede2,
                    customers.name2,
                    customers.vorname2,
                    customers.strasse2,
                    customers.plz2,
                    customers.ort2,
                    customers.land2,
                    customers.firma2,
                    customers.telefon2
                FROM
                    customers
                WHERE
                    customers.customer_id = '" . (int)$customer_id . "'
            ");

        //$this->setZahlungsart($daten['zahlungsart']);

        $this->setName($daten['name']);
        $this->setVorname($daten['vorname']);
        $this->setAdresse1($daten['strasse']);
        $this->setPlz($daten['plz']);
        $this->setOrt($daten['ort']);
        $this->setTelefon($daten['telefon']);
        $this->setTelefon2($daten['mobil']);
        $this->setEmail($daten['email']);
        $this->setAnrede($daten['anrede']);
        $this->setCountryId($daten['country_id']);
        $this->setFirma($daten['firma']);
        $this->setTelefon2($daten['mobil']);
        $this->setFax($daten['fax']);

        $this->setLaAnrede($daten['anrede2']);
        $this->setLaVorname($daten['vorname2']);
        $this->setLaName($daten['name2']);
        $this->setLaAdresse1($daten['strasse2']);
        $this->setLaPlz($daten['plz2']);
        $this->setLaOrt($daten['ort2']);
        $this->setLaTelefon($daten['telefon2']);
        $this->setLaCountryId($daten['land2']);
        $this->setLaFirma($daten['firma2']);

        $this->setCustomerId($daten['customer_id']);
        $this->setCustomerNr($daten['customer_nr']);

        $this->setIsRegistrated(true);
    }

    public function customerLogout(): void
    {
        $this->setDefaultCustomerData();
        $this->customer_id = null;
    }

    //@todo rausehmen
    public function customerLogin($email, $password)
    {
        if (!trim($email) || !trim($password)) {
            return false;
        }

        $authentication = service_loader::getDiContainer()->get(CustomerAuthentication::class);

        try {
            $customer_id = $authentication->authenticateByEmail($email, $password, $this->getShopId());

            $this->loadByCustomerId($customer_id);

            return true;
        } catch (CustomerAuthenticationException $e) {
            return false;
        }
    }

    public function hasWertgarantie()
    {
        return count($this->searchWertgarantien()) > 0;
    }

    public function removeWertgarantie()
    {
        foreach ($this->searchWertgarantien() as $item_id) {
            $this->removeItem($item_id);
        }
    }

    public function searchWertgarantien()
    {
        $item_ids = [];

        $product_ids = SpbGarantGarantie::getGarantieProductIds();

        foreach ($this->getWarenkorbItems() as $warenkorb_item) {
            if (in_array($warenkorb_item->getProductId(), $product_ids)) {
                $item_ids[] = $warenkorb_item->getItemId();
            }
        }

        return $item_ids;
    }

    /**
     * @return BasketItem[]
     */
    public function getWarenkorbItems()
    {
        return $this->warenkorb_items;
    }

    /**
     * @return BasketItem[]
     */
    public function getWarenkorbItemsWithoutServices()
    {
        $warenkorb_items = [];

        foreach ($this->warenkorb_items as $warenkorb_item) {
            if ($warenkorb_item->getRawServiceId()) {
                continue;
            }
            if ($warenkorb_item->getWarenkorbProductTyp() === 'trusted_shops') {
                continue;
            }
            if ($warenkorb_item->getWarenkorbProductTyp() === 'gutschein') {
                continue;
            }

            $warenkorb_items[] = $warenkorb_item;
        }

        return $warenkorb_items;
    }

    /**
     * @return BasketItem[]
     */
    public function getWarenkorbItemsWithVirtVersand(): array
    {
        $warenkorb_items = $this->getWarenkorbItems();

        foreach ($this->getShipmentPositions() as $shipment_position) {
            $warenkorb_item = new BasketItem();
            $warenkorb_item->setWarenkorb($this);
            $warenkorb_item->setWarenkorbProductTyp(OrderConst::WARENKORB_TYP_VERSAND);
            $warenkorb_item->setProductName($shipment_position->getText());
            $warenkorb_item->setQuantity($shipment_position->getQuantity());
            $warenkorb_item->setVkBrutto($shipment_position->getVkBrutto());
            $warenkorb_item->setEkBrutto($shipment_position->getEkBrutto());
            $warenkorb_item->setMwstSatz($shipment_position->getVatRateValue());

            $warenkorb_items[] = $warenkorb_item;
        }

        return $warenkorb_items;
    }

    /**
     * @return RateCalculatorShipmentPosition[]
     */
    public function getShipmentPositions(): array
    {
        return $this->shipment_positions;
    }

    /**
     * @param RateCalculatorShipmentPosition[] $shipment_positions
     */
    public function setShipmentPositions(array $shipment_positions): void
    {
        $this->shipment_positions = $shipment_positions;

        $this->min_shipping_price = 0;

        foreach ($this->shipment_positions as $shipment_position) {
            $this->min_shipping_price += $shipment_position->getVkBrutto();
        }

        $this->shipping_valid = true;
    }

    public function setVersandInvalid()
    {
        $this->shipment_positions = [];
        $this->min_shipping_price = 0;
        $this->shipping_valid = false;
    }

    public function isVersandValid(): bool
    {
        return $this->shipping_valid;
    }

    public function getLieferadresse()
    {
        if ($this->delivery_data['name'] && $this->isAbweichendeLieferadresse()) {
            return [
                'name' => $this->delivery_data['name'],
                'vorname' => $this->delivery_data['vorname'],
                'land' => $this->delivery_data['country_id'],
                'country_id' => $this->delivery_data['country_id'],
                'adresse' => $this->delivery_data['str'],
                'adresse2' => '',
                'ort' => $this->delivery_data['ort'],
                'plz' => $this->delivery_data['plz'],
                'firma' => $this->delivery_data['firma'],
                'telefon' => $this->delivery_data['tel'],
                'land_sign' => CountryRepository::getCountrySignById($this->delivery_data['country_id'])
            ];
        } else {
            return [
                'name' => $this->customer_data['name'],
                'vorname' => $this->customer_data['vorname'],
                'land' => $this->customer_data['country_id'],
                'country_id' => $this->customer_data['country_id'],
                'adresse' => $this->customer_data['str'],
                'adresse2' => '',
                'ort' => $this->customer_data['ort'],
                'plz' => $this->customer_data['plz'],
                'firma' => $this->customer_data['firma'],
                'telefon' => $this->customer_data['tel'],
                'land_sign' => CountryRepository::getCountrySignById($this->customer_data['country_id'])
            ];
        }
    }

    public function isLieferAddress()
    {
        return $this->delivery_data['name'] or $this->customer_data['name'];
    }

    /**
     * gibt die Lieferadresse als address objekt zurück
     * @return Address
     */
    public function getLieferAddress()
    {
        $daten = $this->getLieferadresse();

        return new Address($daten);
    }

    /**
     * @return Address
     */
    public function getRechungsAddress()
    {
        $address = new Address($this->customer_data);
        $address->setAdresse1($this->customer_data['str']);
        $address->setCountryId($this->customer_data['country_id']);

        return $address;
    }

    /**
     *
     * @return Address
     */
    public function getAbweichendeLieferAddress()
    {
        $address = new Address();

        $address->setAnrede($this->getLaAnrede());
        $address->setName($this->getLaName());
        $address->setVorname($this->getLaVorname());
        $address->setAdresse1($this->getLaAdresse1());
        $address->setPlz($this->getLaPlz());
        $address->setOrt($this->getLaOrt());
        $address->setCountryId($this->getLaCountryId());
        $address->setFirma($this->getLaFirma());
        $address->setTel1($this->getLaTelefon());

        return $address;
    }

    public function getLieferCountryId()
    {
        $address = $this->getLieferAddress();

        return $address->getCountryId();
    }

    public function getLieferPlz()
    {
        $address = $this->getLieferAddress();

        return $address->getPlz();
    }

    public function isDomesticShipping(): bool
    {
        return $this->getLieferCountryId() == CountryConst::DE;
    }


    public function setZahlungsart($zahlungsart)
    {
        $this->order_data['zahlungsart'] = $zahlungsart;
    }

    public function getZahlungsart()
    {
        return (int)$this->order_data['zahlungsart'];
    }

    //@todo weg damit
    public function getZahlungsartName()
    {
        global $profile;

        return $profile->getPaymentMethods()[$this->getZahlungsart()]['beschreibung_shop'];
    }

    public function setVersandKundeOpt($versand_kunde_opt)
    {
        $this->order_data['versandart'] = $versand_kunde_opt;
    }

    public function getVersandKundeOpt()
    {
        return $this->order_data['versandart'];
    }

    public function getVersandart()
    {
        return $this->getVersandKundeOpt();
    }

    public function setVersandart($versandart)
    {
        $this->setVersandKundeOpt($versandart);
    }

    public function setLieferoption($lieferoption): void
    {
        $this->order_data['extralieferadresse'] = $lieferoption;

        $this->setVersandKundeOpt('nor');

        if ($lieferoption === 'abholen') {
            $this->setVersandKundeOpt('abh');
        }
    }

    public function getLieferoption()
    {
        return $this->order_data['extralieferadresse'];
    }

    /**
     * @return bool
     */
    public function isAbweichendeLieferadresse(): bool
    {
        return in_array($this->getLieferoption(), ['yes', 'dhlpackstation', 'dhlpostfiliale']);
    }

    public function isDhlLieferadresse(): bool
    {
        return in_array($this->getLieferoption(), ['dhlpackstation', 'dhlpostfiliale']);
    }

    public function isSelfPickup(): bool
    {
        return $this->getVersandart() === 'abh' || $this->getVersandart() === 'eu_block';
    }

    /**
     * @return bool
     */
    public function isAbweichendeLieferAdress(): bool
    {
        return $this->isAbweichendeLieferadresse();
    }

    public function setPaymentReferenz($payment_referenz)
    {
        $this->order_data['payment_referenz'] = $payment_referenz;
    }

    public function getPaymentReferenz()
    {
        return $this->order_data['payment_referenz'];
    }

    public function setKundenBemerkung($bemerkung)
    {
        $this->order_data['anmerkungen'] = $bemerkung;
    }

    public function getKundenBemerkung()
    {
        return $this->order_data['anmerkungen'] ?? '';
    }

    public function setExpressservice($express_service)
    {
        $this->order_data['expressservice'] = $express_service;
    }

    /**
     * setzt die Art der Bestellung
     * @param $art
     */
    public function setArt($art)
    {
        $this->order_data['art'] = $art;
    }

    public function setAnrede($anrede)
    {
        $this->customer_data['anrede'] = trim($anrede);
    }

    public function setFirma($firma)
    {
        $this->customer_data['firma'] = trim($firma);
    }

    public function setName($name)
    {
        $this->customer_data['name'] = trim($name);
    }

    public function setVorname($vorname)
    {
        $this->customer_data['vorname'] = trim($vorname);
    }

    public function setAdresse1($adresse)
    {
        $this->customer_data['str'] = trim($adresse);
    }

    public function setPlz($plz)
    {
        $this->customer_data['plz'] = trim($plz);
    }

    public function setOrt($ort)
    {
        $this->customer_data['ort'] = trim($ort);
    }

    public function setCountryId($country_id)
    {
        $this->customer_data['country_id'] = $country_id;
    }

    public function setEmail($email)
    {
        $this->customer_data['email'] = trim($email);
    }

    public function setTelefon($telefon)
    {
        $this->customer_data['tel'] = trim($telefon);
    }

    public function setTelefon2($telefon)
    {
        $this->customer_data['tel2'] = trim($telefon);
    }

    public function setFax($fax)
    {
        $this->customer_data['fax'] = trim($fax);
    }

    /**
     * @return string|null
     */
    public function getPasswordForNewAccount(): ?string
    {
        return $this->password_for_new_account;
    }

    /**
     * @param string $password
     */
    public function setPasswordForNewAccount(string $password): void
    {
        $this->password_for_new_account = $password;
    }

    public function setNewsletter($newsletter)
    {
        $this->order_data['newsletter'] = $newsletter;
    }

    public function setAddress(Address $address)
    {
        $this->setAnrede($address->getAnrede());
        $this->setFirma($address->getFirma());
        $this->setName($address->getName());
        $this->setVorname($address->getVorname());
        $this->setAdresse1($address->getAdresse1());
        $this->setPlz($address->getPlz());
        $this->setOrt($address->getOrt());
        $this->setCountryId($address->getCountryId());
    }


    public function setCustomerId($customer_id)
    {
        $this->customer_id = $customer_id;
    }

    public function setCustomerNr(string $customer_nr): void
    {
        $this->customer_nr = $customer_nr;
    }

    public function getCustomerId()
    {
        return $this->customer_id;
    }

    public function isNewCustomer(): bool
    {
        if ($this->getCustomerId()) {
            return false;
        }

        //@todo raus hier
        $customer_id = db::getInstance()->fieldQuery("
            SELECT
                customers.customer_id
            FROM
                customers
            WHERE
                customers.email = '" . db::getInstance()->escape(trim($this->getEmail())) . "' AND
                customers.shop_id = " . $this->getShopId() . "
        ");

        return $customer_id ? false : true;
    }

    public function getToken()
    {
        return '';
    }

    public function getCustomerNr(): string
    {
        return $this->customer_nr;
    }

    public function getAnrede()
    {
        return $this->customer_data['anrede'];
    }

    public function isFirma()
    {
        return (bool)trim($this->customer_data['firma']);
    }

    public function getFirma()
    {
        return $this->customer_data['firma'];
    }

    public function getName()
    {
        return $this->customer_data['name'];
    }

    public function getVorname()
    {
        return $this->customer_data['vorname'];
    }

    /**
     * gibt die Art der Bestellung zurück
     * @return string
     */
    public function getArt()
    {
        $art = $this->order_data['art'];

        if ($art === null) {
            $art = $this->isFirma() ? 'commercial' : 'private';
        }

        return $art;
    }

    public function getAdresse1()
    {
        return $this->customer_data['str'];
    }

    public function getPlz()
    {
        return $this->customer_data['plz'];
    }

    public function getOrt()
    {
        return $this->customer_data['ort'];
    }

    public function getCountryId()
    {
        return $this->customer_data['country_id'];
    }

    public function getCountryName()
    {
        return CountryRepository::getCountryNameById($this->getCountryId());
    }

    public function getEmail()
    {
        return $this->customer_data['email'];
    }

    public function getTelefon()
    {
        return $this->customer_data['tel'];
    }

    public function getTelefon2()
    {
        return $this->customer_data['tel2'];
    }

    public function getFax()
    {
        return $this->customer_data['fax'];
    }

    public function getNewsletter()
    {
        return $this->order_data['newsletter'];
    }

    public function getLaAnrede()
    {
        return $this->delivery_data['anrede'];
    }

    public function getLaFirma()
    {
        return $this->delivery_data['firma'];
    }

    public function getLaName()
    {
        return $this->delivery_data['name'];
    }

    public function getLaVorname()
    {
        return $this->delivery_data['vorname'];
    }

    public function getLaAdresse1()
    {
        return $this->delivery_data['str'];
    }

    public function getLaPlz()
    {
        return $this->delivery_data['plz'];
    }

    public function getLaOrt()
    {
        return $this->delivery_data['ort'];
    }

    public function getLaTelefon()
    {
        return $this->delivery_data['tel'];
    }

    public function getLaCountryId()
    {
        return $this->delivery_data['country_id'];
    }


    public function getLaCountryName()
    {
        return CountryRepository::getCountryNameById($this->getLaCountryId());
    }

    public function setLaAnrede($anrede)
    {
        $this->delivery_data['anrede'] = trim($anrede);
    }

    public function setLaFirma($firma)
    {
        $this->delivery_data['firma'] = trim($firma);
    }

    public function setLaName($name)
    {
        $this->delivery_data['name'] = trim($name);
    }

    public function setLaVorname($vorname)
    {
        $this->delivery_data['vorname'] = trim($vorname);
    }

    public function setLaAdresse1($adresse)
    {
        $this->delivery_data['str'] = trim($adresse);
    }

    public function setLaPlz($plz)
    {
        $this->delivery_data['plz'] = trim($plz);
    }

    public function setLaOrt($ort)
    {
        $this->delivery_data['ort'] = trim($ort);
    }

    public function setLaTelefon($telefon)
    {
        $this->delivery_data['tel'] = trim($telefon);
    }

    public function setLaCountryId($country_id)
    {
        $this->delivery_data['country_id'] = $country_id;
    }

    public function setLastOrderId($order_id)
    {
        $this->order_data['last_order_id'] = $order_id;
    }

    public function getLastOrderId()
    {
        return $this->order_data['last_order_id'];
    }

    public function isRegistrated()
    {
        return $this->customer_data['is_registrated'];
    }

    public function setIsRegistrated($value)
    {
        $this->customer_data['is_registrated'] = $value;
    }

    public function setBirthday(DateTime $date)
    {
        $this->customer_data['birthday'] = $date->format('Y-m-d');
    }

    public function getBirthday()
    {
        if ($this->customer_data['birthday']) {
            return new DateTime($this->customer_data['birthday']);
        }

        return false;
    }

    public function getBirthdayAsText()
    {
        $birthday = $this->getBirthday();

        if (!$birthday) {
            return '';
        }

        return $birthday->format(DateObj::DE_DATE);
    }

    public function clearExtraData(): void
    {
        $this->extra_data = [];
    }

    public function removeExtraData($key): void
    {
        unset($this->extra_data[$key]);
    }

    public function addExtraData($key, $value)
    {
        $this->extra_data[$key] = $value;
    }

    public function getExtraData($key)
    {
        return $this->extra_data[$key];
    }

    public function removeExtraCustomerData($key): void
    {
        unset($this->extra_customer_data[$key]);
    }

    public function addExtraCustomerData($key, $value)
    {
        $this->extra_customer_data[$key] = $value;
    }

    public function getExtraCustomerData($key)
    {
        return $this->extra_customer_data[$key];
    }

    public function setBillpayAgbAccept($status)
    {
        return $this->order_data['billpay_agb_accept'] = $status;
    }

    public function getBillpayAgbAccept()
    {
        return $this->order_data['billpay_agb_accept'];
    }

    public function getIp()
    {
        return $_SERVER['REMOTE_ADDR'];
    }

    public function getBillpayFirma()
    {
        return $this->order_data['billpay_firma'];
    }

    public function setBillpayFirma($firma)
    {
        $this->order_data['billpay_firma'] = $firma;
    }

    public function getBillpayLegalForm()
    {
        return $this->order_data['billpay_legal_form'];
    }

    public function setBillpayLegalForm($legal_form)
    {
        $this->order_data['billpay_legal_form'] = $legal_form;
    }


    public function getBillpayHolderName()
    {
        return $this->order_data['billpay_holder_name'];
    }

    public function getBillpayTaxNumber()
    {
        return $this->order_data['billpay_tax_number'];
    }

    public function getBillpayRegisterNumber()
    {
        return $this->order_data['billpay_register_number'];
    }

    public function setBillpayHolderName($value)
    {
        $this->order_data['billpay_holder_name'] = $value;
    }

    public function setBillpayTaxNumber($value)
    {
        $this->order_data['billpay_tax_number'] = $value;
    }

    public function setBillpayRegisterNumber($value)
    {
        $this->order_data['billpay_register_number'] = $value;
    }


    public function getDebugId()
    {
        return $this->debug_id;
    }

    /**
     * @param bool $status
     */
    public function setIsPhoneRequired($status)
    {
        $this->is_phone_required = (bool)$status;
    }

    /**
     * @return bool
     */
    public function isPhoneRequired()
    {
        return $this->is_phone_required;
    }

    /**
     * @return bool
     */
    public function isPaypalExpress(): bool
    {
        return $this->is_paypal_express;
    }

    /**
     * @param bool $is_paypal_express
     */
    public function setIsPaypalExpress(bool $is_paypal_express): void
    {
        $this->is_paypal_express = $is_paypal_express;
    }

    //@todo raus
    public function backup($id)
    {
        $content = serialize($_SESSION);

        $db = db::getInstance();

        $db->simpleInsertUpdate('shop_warenkorb_restore_sessions', ['id' => $id, 'date_added' => new SqlFunction('NOW()'), 'session' => $content]);
    }

    //@todo raus
    public function restore($id)
    {
        global $warenkorb;

        $db = db::getInstance();

        $content = $db->fieldQuery("
            SELECT
                shop_warenkorb_restore_sessions.session
            FROM
                shop_warenkorb_restore_sessions
            WHERE
                shop_warenkorb_restore_sessions.id = " . $db->quote($id) . "
        ");

        if (!$content) {
            return false;
        }

        $db->query("DELETE FROM shop_warenkorb_restore_sessions WHERE shop_warenkorb_restore_sessions.id = " . $db->quote($id));

        $session = unserialize($content);

        $_SESSION = $session;
        $warenkorb = &$_SESSION['warenkorb'];

        return true;
    }

    public function searchItemByAswoArticleNumber(string $article_number): ?BasketItem
    {
        $items = $this->searchItemsByAswoArticleNumber($article_number);

        $anzahl = count($items);
        if ($anzahl === 0) {
            return null;
        }

        if ($anzahl === 1) {
            return $items[0];
        }

        throw new DevException('multiple warenkorb_items with the same $article_number');
    }

    private function setDefaultCustomerData(): void
    {
        $this->customer_data = [
            'anrede' => '',
            'firma' => '',
            'name' => '',
            'vorname' => '',
            'str' => '',
            'plz' => '',
            'ort' => '',
            'country_id' => '',
            'email' => '',
            'tel' => '',
            'tel2' => '',
            'fax' => '',
            'birthday' => '',
            'is_registrated' => false
        ];
    }

    private function setDefaultDeliveryData(): void
    {
        $this->delivery_data = [
            'anrede' => '',
            'firma' => '',
            'name' => '',
            'vorname' => '',
            'str' => '',
            'plz' => '',
            'ort' => '',
            'country_id' => '',
            'email' => '',
            'tel' => '',
            'fax' => ''
        ];
    }

    private function setDefaultOrderData(): void
    {
        $this->order_data = [
            'versandart' => null,
            'zahlungsart' => null,
            'payment_referenz' => '',
            'extralieferadresse' => 'no',
            'anmerkungen' => '',
            'expressservice' => null,
            'art' => null,
            'newsletter' => false,
            'last_order_id' => null,
            'billpay_agb_accept' => false, //@todo über extra_data abwickeln
            'billpay_firma' => null,
            'billpay_legal_form' => null,
            'billpay_holder_name' => null,
            'billpay_tax_number' => null,
            'billpay_register_number' => null
        ];
    }

    private function searchItemsByAswoArticleNumber(string $article_number): array
    {
        $found_items = [];
        foreach ($this->warenkorb_items as $warenkorb_item) {
            if ($warenkorb_item->getAswoArticleNumber() === $article_number) {
                $found_items[] = $warenkorb_item;
            }
        }

        return $found_items;
    }

    public function getPossibleExtraServiceIds(): array
    {
        $extra_service_ids = [];
        foreach ($this->getWarenkorbItemsWithoutServices() as $warenkorb_item) {
            foreach ($warenkorb_item->getExtraServices() as $extra_service_id) {
                $extra_service_ids[] = $extra_service_id;
            }
        }
        return $extra_service_ids;
    }
}
