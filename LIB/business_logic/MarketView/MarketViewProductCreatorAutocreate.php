<?php

namespace wws\MarketView;

use bqp\Exceptions\FatalException;
use service_loader;
use wws\business_structure\Shop;
use wws\MarketView\Autocreate\MarketViewAutocreateGlobal;
use wws\MarketView\Autocreate\MarketViewAutocreateIndividual;
use wws\MarketView\Autocreate\MarketViewAutocreateService;
use wws\MarketView\Entities\MarketViewProduct;
use wws\Product\Product;
use wws\Product\ProductConst;

class MarketViewProductCreatorAutocreate extends MarketViewProductCreator
{
    protected ?int $default_cat_id = ProductConst::CAT_ID_UNGEPFLEGT;

    public function setDefaultCatId(?int $cat_id): void
    {
        $this->default_cat_id = $cat_id;
    }

    public function getMvsrcProductAsProduct(MarketViewProduct $mv_product): Product
    {
        $product = parent::getMvsrcProductAsProduct($mv_product);

        $product->setVerfuegbarkeitsAmAktiv(true);
        $product->addProductTag(ProductConst::TAG_AC);
        $product->setProductStatus(ProductConst::PRODUCT_STATUS_NORMAL);

        if ($this->default_cat_id) {
            $product->setCatIdWithDefaults($this->default_cat_id);
        }

        $product->resetPrices();

        $shop = $product->getShop(Shop::ALLEGO); //das der shop auch gleich initialisiert ist

        $shop->setLieferbaranzeige(ProductConst::LIEFERBAR_ID_1_TO_3);

        //gewicht und maße setzen falls noch nicht geschehen
        if ($mv_product->getEan()) { //funktioniert nur per ean
            $is_gewicht_empty = $product->getGewicht() > 0;
            $is_dimension_empty = $product->getSizeB() > 0 && $product->getSizeH() && $product->getSizeT();

            if ($is_gewicht_empty || $is_dimension_empty) {
                $size = $this->getProductSize($mv_product->getEan());

                if (!$is_gewicht_empty) {
                    $product->setGewicht($size['gewicht']);
                }

                if (!$is_dimension_empty) {
                    $product->setSizeB($size['breite']);
                    $product->setSizeH($size['hoehe']);
                    $product->setSizeT($size['tiefe']);
                }
            }
        }

        //versandkosten
        if (!$product->getShipmentTypeId()) {
            if ($product->getGewicht() > 28) {
                $this->getShipmentTypeSetter()->setByShipmentTypeId($product, ProductConst::SHIPMENT_TYPE_ID_EINWEGPALETTE);
            } else {
                $this->getShipmentTypeSetter()->setByShipmentTypeId($product, ProductConst::SHIPMENT_TYPE_ID_PAKET);
            }

            $product->addProductTag(ProductConst::TAG_AC_VERSAND);
        }

        return $product;
    }

    public function getSearchQuery(int $mvsrc_id): string
    {
        $market_view_autocreate_service = service_loader::get(MarketViewAutocreateService::class);
        $market_view_autocreate = $market_view_autocreate_service->autocreateLoader($mvsrc_id);

        if ($market_view_autocreate instanceof MarketViewAutocreateGlobal) {
            $brand_ids = $this->db->query("
                SELECT
                    product_brand.brand_id
                FROM
                    product_brand
                WHERE
                    product_brand.market_view_autocreate = 1
            ")->asSingleArray();
        } elseif ($market_view_autocreate instanceof MarketViewAutocreateIndividual) {
            $brand_ids = [];
        } else {
            throw new FatalException('Ungültiger Autocreate Type');
        }

        $brand_id_filter_sql = '';

        if ($brand_ids) {
            $brand_id_filter_sql = " OR market_view_hersteller.brand_id IN (" . $this->db->in($brand_ids) . ")";
        }


        //Es wurde vor einiger Zeit die Prüfung entfernt, ob es schon ein Angebot mit der EAN im System gibt. (Das Matching-Modul macht das besser und transparenter)
        //Es ist dadurch aber ein neues Problem zum Vorschein gekommen: wenn ein Lieferant mehrere Angebote mit der gleichen EAN hat, legen wir unter Umständen diese Angebot auch mehrfach an.
        //=>wenn für ein Angebot ein Matching auf wrong gesetzt worden ist, dann ignorieren wir das hier. (wrong setzen passiert manuell, edge case)
        //Ggf. könnte man das auch auf bestimmte Quellen beschränken, aber das ist allgemein eigentlich auch clean.
        $matching_wrong_join_sql = "
             LEFT JOIN
                market_view_matching ON (market_view_matching.mvsrc_id = market_view_product.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_product.mvsrc_product_id AND market_view_matching.matching_status = 1)
        ";

        $matching_wrong_where_sql = " AND market_view_matching.matching_id IS NULL ";
        //

        return "SELECT
                DISTINCT
                market_view_product.mvsrc_id,
                market_view_product.mvsrc_product_id,
                market_view_product.product_name,
                market_view_product.versand_netto
            FROM
                market_view_product INNER JOIN
                market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id) LEFT JOIN
                market_view_autocreate_hersteller ON (market_view_autocreate_hersteller.mvsrc_id = market_view_product.mvsrc_id AND market_view_product.mv_hersteller_id = market_view_autocreate_hersteller.mv_hersteller_id)
                " . $matching_wrong_join_sql . "
            WHERE
                market_view_product.mvsrc_id = " . $mvsrc_id . " AND
                market_view_product.availability_id IN (" . MarketViewConst::AVAILABILITY_ID_MORE_THEN_FIVE . ", " . MarketViewConst::AVAILABILITY_ID_LESS_THEN_FIVE . ") AND
                market_view_product.product_id = 0 AND
                market_view_hersteller.brand_id != 0 AND
                market_view_product.vk_netto BETWEEN 3 AND 5000 AND
                market_view_product.ean != '' AND
                market_view_product.product_name != '' AND
                (
                    market_view_autocreate_hersteller.status = 1
                    $brand_id_filter_sql
                )
                " . $matching_wrong_where_sql . "
        ";
    }
}
