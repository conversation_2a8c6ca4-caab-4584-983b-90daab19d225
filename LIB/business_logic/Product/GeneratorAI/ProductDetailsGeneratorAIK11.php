<?php

namespace wws\Product\GeneratorAI;

use bqp\extern\OpenRouter\OpenRouterApi;
use db;
use Exception;
use service_loader;
use Throwable;
use wws\Product\GeneratorAI\Prompts\PromptK11ShopArticleName;
use wws\Product\GeneratorAI\Prompts\PromptK11ShopProductDescription;
use wws\Product\GeneratorAI\Prompts\PromptK11ShopProductName;
use wws\Product\Product;
use wws\Product\ProductBrand\ProductBrandRepository;
use wws\Product\ProductConst;
use wws\ProductCat\ProductCatRepository;
use wws\Shopware\ShopwareFactory;

class ProductDetailsGeneratorAIK11
{
    private OpenRouterApi $api;
    private bool $generate_stamm_product_name = false;
    private bool $only_description = false;

    public function __construct(OpenRouterApi $api)
    {
        $this->api = $api;
    }

    public function setGenerateStammProductName(bool $value): self
    {
        $this->generate_stamm_product_name = $value;
        return $this;
    }

    public function setOnlyDescription(bool $value): self
    {
        $this->only_description = $value;
        return $this;
    }

    public function generate(int $product_id): array
    {
        $errors = [];
        $generated_data = [];

        $cfg = service_loader::getConfigRegistry()->get('k11/openrouter');

        $model = $cfg->getString('default_model') ?: OpenRouterApi::MODEL_GROK_4;
        $temperature = (float)($cfg->getValue('temperature') ?? 0.5);

        $product_info_json = $this->buildProductInfoJson($product_id);
        if (!$this->only_description) {
            try {
                $prompt = (new PromptK11ShopProductName())->getPrompt();
                $prompt = str_replace(['{PRODUCT_INFO}', '{product_info}'], $product_info_json, $prompt);

                $result = $this->api->simpleTextPrompt($prompt, $model, 5000, $temperature);
                $generated_data['de']['product_name_localized'] = trim($result);
            } catch (Exception $e) {
                $errors['de']['product_name_localized'] = $e->getMessage();
                $generated_data['de']['product_name_localized'] = '';
            }
        }
        try {
            $prompt = (new PromptK11ShopProductDescription())->getPrompt();
            $prompt = str_replace(['{PRODUCT_INFO}', '{product_info}'], $product_info_json, $prompt);

            $result = $this->api->simpleTextPrompt($prompt, $model, 5000, $temperature);
            $generated_data['de']['artikelinfo'] = trim($result);
        } catch (Exception $e) {
            $errors['de']['artikelinfo'] = $e->getMessage();
            $generated_data['de']['artikelinfo'] = '';
        }

        if ($this->generate_stamm_product_name) {
            try {
                $prompt = (new PromptK11ShopArticleName())->getPrompt();
                $prompt = str_replace(['{PRODUCT_INFO}', '{product_info}'], $product_info_json, $prompt);

                $result = $this->api->simpleTextPrompt($prompt, $model, 5000, $temperature);
                $generated_data['product_name'] = trim($result);
            } catch (Exception $e) {
                $errors['product_name'] = $e->getMessage();
                $generated_data['product_name'] = '';
            }
        }

        return compact('errors', 'generated_data');
    }

    public function save(int $product_id, array $data): void
    {
        $data = $data['generated_data'] ?? $data;

        $product = new Product($product_id);
        $pd = $product->getProductDetail(ProductConst::PRODUCT_LANG_DE);

        if (isset($data['de']['product_name_localized'])) {
            $pd->setProductNameLocalized($data['de']['product_name_localized']);
        }
        if (isset($data['de']['artikelinfo'])) {
            $pd->setDescription($data['de']['artikelinfo']);
        }
        if ($this->generate_stamm_product_name && isset($data['product_name']) && trim((string)$data['product_name']) !== '') {
            $product->setProductName(trim((string)$data['product_name']));
        }

        if ($pd->getSmartDataObj()->getChanges()) {
            $pd->setDescriptionSource(ProductConst::PRODUCT_DATA_SOURCE_AI);
        }

        $product->addProductTag(ProductConst::TAG_AI_CONTENT);
        $product->save();
    }

    public function getProductInfoJson(int $product_id): string
    {
        return $this->buildProductInfoJson($product_id);
    }

    private function buildProductInfoJson(int $product_id): string
    {
        $product = new Product($product_id);
        $pd = $product->getProductDetail(ProductConst::PRODUCT_LANG_DE);

        $name = $pd && $pd->getProductNameLocalized() ? $pd->getProductNameLocalized() : $product->getProductName();
        $name = trim($name, " \t\n\r\0\x0B-\xC2\xA0");

        $category_breadcrumb = '';
        if ($product->getCatId()) {
            $category_breadcrumb = ProductCatRepository::getShopCatPathAsString($product->getCatId(), ' > ');
        }

        $product_properties_raw = '[]';
        if ($pd) {
            $raw_props = $pd->getFeaturesUnboundRaw();
            if ($raw_props) {
                $product_properties_raw = $raw_props;
            }
        }
        $manufacturer_number = $product->getMpn();
        if (!$manufacturer_number) {
            try {
                $props = json_decode($product_properties_raw, true, 512, JSON_THROW_ON_ERROR);
                if (is_array($props)) {
                    foreach ($props as $p) {
                        if (isset($p['name']) && mb_strtolower($p['name']) === 'ersatzteilenummer' && !empty($p['value'])) {
                            $manufacturer_number = (string)$p['value'];
                            break;
                        }
                    }
                }
            } catch (Throwable) {
            }
        }

        $short_description = null;
        if ($pd) {
            $teaser = $pd->getTeaser();
            if ($teaser !== '') {
                $short_description = $teaser;
            }
        }

        $data = [
            'product_id' => (string)$product->getProductId(),
            'name' => $name,
            'description' => $pd ? $pd->getDescription() : '',
            'product_number' => $product->getProductNr(),
            'manufacturer_number' => $manufacturer_number ?: '',
            'manufacturer_name' => ProductBrandRepository::getBrandName($product->getBrandId()),
            'category_breadcrumb' => $category_breadcrumb,
            'weight' => $product->getGewicht() ?: 0,
            'width' => $product->getSizeB() ?: 0,
            'height' => $product->getSizeH() ?: 0,
            'length' => $product->getSizeT() ?: 0,
            'product_properties' => $product_properties_raw,
            'brand_compatibility' => '',
            'compatible_appliance_model_numbers' => '',
            'short_description' => $short_description,
            'product_type' => null,
            'article_description' => null,
            'alternative_codes' => '[]'
        ];

        try {
            $shopware_factory = service_loader::get(ShopwareFactory::class);
            $pds = $shopware_factory->getProductDataSync('k11/shopware_api');
            $swData = $pds->tryLoadProductsFromShopware([$product_id]);
            if ($swData) {
                $sw = current($swData);
                if ($sw) {
                    $data['name'] = $sw->getProductName() ?: $data['name'];
                    $data['description'] = $sw->getDescription() ?: $data['description'];
                    $data['manufacturer_number'] = $sw->getMpn() ?: $data['manufacturer_number'];
                    if ($sw->getFreeProductPropertiesRaw()) {
                        $data['product_properties'] = $sw->getFreeProductPropertiesRaw();
                    }
                }
            }
        } catch (Throwable) {
        }

        try {
            $rows = db::getInstance()->query(
                'SELECT d.brand_id, d.model_name, d.model_code
                 FROM product_device_mapping pdm
                 INNER JOIN product_device d ON d.device_id = pdm.device_id
                 WHERE pdm.product_id = ' . (int)$product->getProductId() . ' AND pdm.deleted = 0
                 LIMIT 10'
            );

            $brand_ids = [];
            $models = [];
            foreach ($rows as $r) {
                if (!empty($r['brand_id'])) {
                    $brand_ids[(int)$r['brand_id']] = true;
                }
                $code = trim((string)($r['model_code'] ?? ''));
                $name2 = trim((string)($r['model_name'] ?? ''));
                if ($code !== '') {
                    $models[$code] = true;
                } elseif ($name2 !== '') {
                    $models[$name2] = true;
                }
            }

            if ($brand_ids) {
                $brand_names = [];
                foreach (array_keys($brand_ids) as $bid) {
                    $bn = ProductBrandRepository::getBrandName($bid);
                    if ($bn) {
                        $brand_names[$bn] = true;
                    }
                }
                if ($brand_names) {
                    $data['brand_compatibility'] = implode(', ', array_keys($brand_names));
                }
            }

            if ($models) {
                $model_list = array_slice(array_keys($models), 0, 100);
                $data['compatible_appliance_model_numbers'] = implode(', ', $model_list);
            }
        } catch (Throwable $e) {
        }

        try {
            $altRows = db::getInstance()->query(
                'SELECT p2.mpn, p2.product_nr
                 FROM product_association pa
                 INNER JOIN product p2 ON p2.product_id = pa.dst_product_id
                 WHERE pa.product_id = ' . (int)$product->getProductId() . ' AND pa.association_type_id = ' . (int)ProductConst::ASSOCIATION_TYPE_ID_ALTERNATIVE
            );
            $codes = [];
            foreach ($altRows as $r) {
                $code = trim((string)($r['mpn'] ?? ''));
                if ($code === '' && !empty($r['product_nr'])) {
                    $code = (string)$r['product_nr'];
                }
                if ($code !== '') {
                    $codes[$code] = true;
                }
            }
            $data['alternative_codes'] = json_encode(array_keys($codes), JSON_UNESCAPED_UNICODE);
        } catch (Throwable $e) {
        }

        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    }
}
