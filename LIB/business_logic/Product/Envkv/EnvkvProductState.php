<?php

namespace wws\Product\Envkv;

use bqp\db\db_generic;
use wws\Product\actions\ProductFeatureStatus;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductFeature\ProductFeatureGroupRepository;

class EnvkvProductState
{
    public const ENVKV_SCALE_A_G_2020 = 'A-G-2020';

    public const FEATURE_GROUP_ID_DUNSTABZUGSHAUBE = 19;
    public const FEATURE_ID_DUNSTABZUGSHAUBE_EEK_SCALE = 2282;

    private db_generic $db;

    private ProductFeatureGroupRepository $feature_group_repository;
    private array $applicable_feature_groups;



    public function __construct(db_generic $db, ProductFeatureGroupRepository $feature_group_repository)
    {
        $this->db = $db;
        $this->feature_group_repository = $feature_group_repository;
    }


    public function processProduct(Product $product): void
    {
        //step1: wenn in den schon ein Pflichtfeld fehlt, dann Werten wir das schon automatisch als draußen
        if ($product->getFeaturesStatus() === ProductFeatureStatus::FEATURES_MANDATORY) {
            $product->addProductTag(ProductConst::TAG_ENVKV_FAIL);

            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_2020_OK);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_OK);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_FICHE);
            $product->setEek('');
            $product->setEekScaleId('');
            return;
        }

        //step2: die envkv daten für das Produkt bestimmen
        $eek_data = $this->getEekData($product);

        if (!$eek_data) {
            //ACHTUNG: Das funktioniert nur in Verbindung mit der vorherigen Prüfung auf ProductFeatureStatus::FEATURES_MANDATORY.
            //für sich alleine bedeutet ein leeres Ergebnis hier nicht, dass das Produkt nicht der ENVKV unterliegt.
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_FAIL);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_OK);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_2020_OK);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_FICHE);
            $product->setEek('');
            $product->setEekScaleId('');
            return;
        }

        $product->setEek($eek_data['eek']);
        $product->setEekScaleId($eek_data['eek_scale_id']);

        //step3: prüfen ob Label und Datenblatt vorhanden
        if ($product->getEekScaleId() === self::ENVKV_SCALE_A_G_2020) {
            $envkv_state = (bool)$product->getEekLabelProductMediaId() && (bool)$product->getEekFicheProductMediaId();

            //prüfen ob das ein neues label ist
            if ($envkv_state) {
                $media_topic_id = $this->getMediaTopicIdFromProductMediaId($product, $product->getEekLabelProductMediaId());

                if ($media_topic_id !== ProductConst::MEDIA_TOPIC_ENVKV_LABEL_2020) {
                    $envkv_state = false;
                }
            }
        } else {
            //bei den alten ENVKV Daten sind die Datenblätter in den Features enthalten -> es ist damit nur das Label nötig.
            //wäre halt ein Problem, falls wir das mal ändern sollten.
            $envkv_state = (bool)$product->getEekLabelProductMediaId();
        }

        if ($envkv_state) { //envkv daten vollständig
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_FAIL);
            $product->addProductTag(ProductConst::TAG_ENVKV_OK);

            if ($product->getEekScaleId() === self::ENVKV_SCALE_A_G_2020) {
                if ($product->addProductTag(ProductConst::TAG_ENVKV_2020_OK)) {
                    $this->disableOldEnvkvFeatures($product);
                }
            }

            $product->toggleProductTag(ProductConst::TAG_ENVKV_FICHE, (bool)$product->getEekFicheProductMediaId());
        } else {
            $product->addProductTag(ProductConst::TAG_ENVKV_FAIL);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_OK);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_2020_OK);
            $product->removeRestrictedProductTag(ProductConst::TAG_ENVKV_FICHE);
        }
    }

    public function getMediaTopicIdFromProductMediaId(Product $product, int $product_media_id): int
    {
        if ($product->isMediaLoaded()) {
            return $product->getMedia($product_media_id)->getMediaTopicId();
        }

        //es kann sein, dass die product_media_id schon nicht mehr gültig ist, weil die im event system erst nachträglich bestimmt wird... (macht aber nix, dieser prozess stößt das hier ggf. neu an.)
        return (int)$this->db->fieldQuery("
            SELECT
                product_media.media_topic_id
            FROM
                product_media
            WHERE
                product_media.product_media_id = " . $this->db->escape($product_media_id) . "
        ");
    }

    public function processProductIfNecessary(Product $product): void
    {
        if ($this->isNecessary($product)) {
            $this->processProduct($product);
        }
    }

    private function isNecessary(Product $product): bool
    {
        if ($product->isChange('features_status')) {
            return true;
        }

        if ($product->isChange('eek_label_product_media_id')) {
            return true;
        }

        if ($product->isChange('eek_fiche_product_media_id')) {
            return true;
        }

        if ($product->isFeatureContainerLoaded() && $product->getFeatureContainer()->getChanges()) {
            return true;
        }

        //label wird von alt auf neu/neu auf alt gestellt
        if ($product->getEekLabelProductMediaId()) {
            if ($product->isMediaLoaded()) {
                if ($product->getMedia($product->getEekLabelProductMediaId())->getSmartDataObj()->isChange('media_topic_id')) {
                    return true;
                }
            }
        }

        return false;
    }

    public function disableOldEnvkvFeatures(Product $product): void
    {
        $feature_values = $product->getFeatureContainer()->getFeatureValuesObject();

        $old_envkv_feature_ids = $this->feature_group_repository->getOldEnvkvFeatureIds();

        foreach ($old_envkv_feature_ids as $feature_id) {
            if ($feature_values->featureExists($feature_id)) {
                $feature_values->setHidden($feature_id, true);
            }
        }
    }

     /**
     * Sucht für das Produkt die ENVKV Daten anhand der zugeordneten Feature-Gruppen.
     *
     * Die erste Gruppe mit gesetzten Werten gewinnt. Es wird NICHT automatisch die Feature-Gruppe der Produkt-Kategorie genutzt.
     *
     * @param Product $product
     * @return array|null
     */
    private function getEekData(Product $product): ?array
    {
        $groups = $this->getAllApplicableFeatureGroups();


        $feature_container = $product->getFeatureContainer();

        $feature_group_ids = $feature_container->getFeatureGroupIds();

        $prefix = [
            'eek_2020_' => '',
            'eek_' => '',
        ];

        foreach ($prefix as $key => $value) {
            foreach ($feature_group_ids as $feature_group_id) {
                if (!isset($groups[$feature_group_id])) {
                    continue;
                }

                $eek_feature_id = $groups[$feature_group_id][$key . 'feature_id'];

                if (!$eek_feature_id) {
                    continue;
                }

                if (!$feature_container->hasFeatureValue($eek_feature_id)) {
                    continue;
                }

                $feature_value_object = $feature_container->getFeatureValue($eek_feature_id);

                if ($feature_value_object->isHide()) {
                    continue;
                }

                $eek_value = $feature_value_object->getValue();

                if ($eek_value) {
                    //Sonderbehandlung für Abzugshauben... hier hat die EU Labels definiert, die sich alle 2 Jahre ändern.
                    if (in_array(self::FEATURE_GROUP_ID_DUNSTABZUGSHAUBE, $feature_container->getFeatureGroupIds()) && ($eek_scale_name = $feature_container->getFeatureValue(self::FEATURE_ID_DUNSTABZUGSHAUBE_EEK_SCALE)->getValue())) {
                        $eek_scale_id = match ($eek_scale_name) {
                            'A bis G' => 'A-G',
                            'A+ bis F' => 'A1-F',
                            'A++ bis E' => 'A2-E',
                            'A+++ bis D' => 'A3-D',
                        };

                        return [
                            'eek' => $eek_value,
                            'eek_scale_id' => $eek_scale_id,
                            'product_name_envkv_suffix_template' => $groups[$feature_group_id]['product_name_envkv_suffix_template'],
                        ];
                    }

                    //normal
                    return [
                        'eek' => $eek_value,
                        'eek_scale_id' => $groups[$feature_group_id][$key . 'scale_id'],
                        'product_name_envkv_suffix_template' => $groups[$feature_group_id]['product_name_envkv_suffix_template'],
                    ];
                }
            }
        }

        return null;
    }

    private function getAllApplicableFeatureGroups(): array
    {
        if (!isset($this->applicable_feature_groups)) {
            $this->applicable_feature_groups = $this->db->query("
                SELECT
                    product_feature_groups.feature_group_id,
                    product_feature_groups.product_name_envkv_suffix_template,
                    product_feature_groups.eek_feature_id,
                    product_feature_groups.eek_scale_id,
                    product_feature_groups.eek_2020_feature_id,
                    product_feature_groups.eek_2020_scale_id
                FROM
                    product_feature_groups
                WHERE
                    product_feature_groups.eek_feature_id != 0 OR
                    product_feature_groups.eek_2020_feature_id != 0
            ")->asArray('feature_group_id');
        }

        return $this->applicable_feature_groups;
    }
}
