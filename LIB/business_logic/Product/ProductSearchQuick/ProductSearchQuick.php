<?php

namespace wws\Product\ProductSearchQuick;

use bqp\db\db_generic;
use bqp\db\ExtendedInsertQuery;
use bqp\db\SqlUtils;

class ProductSearchQuick
{
    private db_generic $db;
    private ExtendedInsertQuery $insert_statement;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
        $this->insert_statement = new ExtendedInsertQuery($db, 'product_search_quick', ['product_id', 'search_values', 'search_keywords']);
        $this->insert_statement->setAutoUpdate(true);
        $this->insert_statement->setAutoexecute(1000);
    }

    public function buildSql(string $search_phrase): string
    {
        $sql = [];

        if (strlen($search_phrase) < 15) {
            $sql[] = "product_search_quick.search_keywords LIKE '% " . $this->db->escape($search_phrase) . " %'";
        }

        $sql[] = "product_search_quick.search_values LIKE '" . SqlUtils::searchString($search_phrase) . "'";

        if (count($sql) > 1) {
            $sql_raw = '(' . implode(' OR ', $sql) . ')';
        } else {
            $sql_raw = $sql[0];
        }

        //return $sql_raw;

        //mit dem subquery kommt der query optimizer besser klar
        return "product.product_id IN (
            SELECT
                product_search_quick.product_id
            FROM
                product_search_quick
            WHERE
                $sql_raw
        )";
    }

    public function indexAll(): void
    {
        $product_ids = $this->db->query("
            SELECT
                product.product_id
            FROM
                product
        ")->asSingleArray();

        foreach (array_chunk($product_ids, 10000) as $chunk) {
            $this->indexProductIdsWithoutFlush($chunk);
        }

        $this->insert_statement->flush();
    }

    public function indexProductId(int $product_id): void
    {
        $this->indexProductIds([$product_id]);
    }

    public function indexProductIds(array $product_ids): void
    {
        $this->indexProductIdsWithoutFlush($product_ids);
        $this->insert_statement->flush();
    }

    private function indexProductIdsWithoutFlush(array $product_ids): void
    {
        $result = $this->db->query("
            SELECT
                product.product_id,
                product.product_name,
                product.ean,
                product.ean2,
                product.product_nr,
                product.mpn,
                product.asin,
                product.asin_in_use,
                ANY_VALUE(product_brand.brand_name) AS brand_name,
                GROUP_CONCAT(DISTINCT product_cat.cat_path_text SEPARATOR '|||') AS cat_path_text,
                /*GROUP_CONCAT(DISTINCT product_details.product_name_localized SEPARATOR '|||') AS product_name_localized,*/
                GROUP_CONCAT(DISTINCT product_ek.gros_product_id SEPARATOR '|||') AS gros_product_ids
            FROM
                product LEFT JOIN
                product_brand ON (product.brand_id = product_brand.brand_id) LEFT JOIN
                product_cat ON (product.cat_id = product_cat.cat_id) LEFT JOIN
                /*product_details ON (product.product_id = product_details.product_id) LEFT JOIN*/
                product_ek ON (product.product_id = product_ek.product_id AND product_ek.gros_product_id != '')
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")
            GROUP BY
                product.product_id
        ");

        foreach ($result as $row) {
            $this->insert_statement->add([
                'product_id' => $row['product_id'],
                'search_values' => $this->buildSearchValues($row),
                'search_keywords' => $this->buildSearchKeywords($row)
            ]);
        }
    }

    private function normalizeCatPath(string $cat_path): string
    {
        $paths = explode('|||', $cat_path);
        $values = [];
        foreach ($paths as $path) {
            $parts = array_map('trim', explode('|', $path));
            $parts = array_slice($parts, 2); // entferne die ersten zwei Elemente
            foreach ($parts as $part) {
                if ($part !== '') {
                    $values[] = $part;
                }
            }
        }
        return implode(' ', array_unique($values));
    }

    private function normalizeProductNameLocalized(string $product_name, string $product_name_localized): string
    {
        $product_names_localized = explode('|||', $product_name_localized);

        $product_names_localized = array_map(function ($name) use ($product_name) {
            return (stripos($name, $product_name) === 0) ? trim(substr($name, strlen($product_name))) : $name;
        }, $product_names_localized);

        return implode(' ', $product_names_localized);
    }

    private function buildSearchValues(array $row): string
    {
        $row['cat_path_text'] = $this->normalizeCatPath($row['cat_path_text']);

        if (empty($row['product_name_localized'])) {
            $row['product_name_localized'] = '';
        } else {
            $row['product_name_localized'] = ' ' . $this->normalizeProductNameLocalized($row['product_name'], $row['product_name_localized']);
        }

        $additional_values = ' ' . trim($row['mpn'] . ' ' . $row['ean2'] . ' ' . $row['asin'] . ' ' . $row['asin_in_use']);

        $brand_name = mb_strtolower($row['brand_name'] ?? '');
        //$search_values = mb_strtolower($row['cat_path_text'] . ' ' . $row['product_name'] . $row['product_name_localized']);
        $search_values = mb_strtolower($row['product_name']);
        $search_values = str_replace($brand_name, '', $search_values);

        $search_values = trim($row['product_id'] . ' ' . $row['ean'] . ' ' . $row['product_nr'] . ' ' . $brand_name . ' ' . $search_values);
        $search_values .= $additional_values;

        return $search_values;
    }

    private function buildSearchKeywords(array $row): string
    {
        $gros_product_ids = explode('|||', $row['gros_product_ids'] ?? '');
        $gros_product_ids = array_map('trim', $gros_product_ids);
        $gros_product_ids = array_filter($gros_product_ids);

        $search_keywords = implode(' ', $gros_product_ids);

        if ($row['asin']) {
            $search_keywords .= ' ' . $row['asin'];
        }

        if ($row['asin_in_use']) {
            $search_keywords .= ' ' . $row['asin'];
        }

        $search_keywords = trim($search_keywords);

        if ($search_keywords) {
            return ' ' . $search_keywords . ' ';
        }

        return '';
    }
}
