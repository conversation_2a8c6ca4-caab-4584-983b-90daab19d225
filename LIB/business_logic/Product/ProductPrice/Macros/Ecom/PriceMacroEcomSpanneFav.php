<?php

namespace wws\Product\ProductPrice\Macros\Ecom;

use wws\Product\ProductPrice\PriceMacro;
use wws\Product\ProductPrice\PriceValues;

class PriceMacroEcomSpanneFav implements PriceMacro
{
    public function getMacroKey(): string
    {
        return 'SPANNEFAV';
    }

    public function getMacroDescription(): string
    {
        return '
            VERSDIFFU = Unterdeckung zwischen tatsächlichen und den Endkunden-Versandkosten. Wenn wir Gewinn am Versand haben, wird 0€ verwendet. 
        <dl>
            <dt>bis 2€ EK-Brutto</dt>
            <dd>EK+28%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 2€ EK-Brutto</dt>
            <dd>EK+23%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 4€ EK-Brutto</dt>
            <dd>EK+20%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 7€ EK-Brutto</dt>
            <dd>EK+18%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 10€ EK-Brutto</dt>
            <dd>EK+16%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 20€ EK-Brutto</dt>
            <dd>EK+14%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 30€ EK-Brutto</dt>
            <dd>EK+13%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 50€ EK-Brutto</dt>
            <dd>EK+12%+VERSDIFFU</dd>
        </dl>
        <dl>
            <dt>ab 100€ EK-Brutto</dt>
            <dd>EK+9%+VERSDIFFU</dd>
        </dl>';
    }

    public function evaluate(string $formel, PriceValues $values): string
    {
        $ek_brutto = $values['EK'];

        if ($ek_brutto < 2) {
            $new_formel = 'EK+28%';
        } elseif ($ek_brutto < 4) {
            $new_formel = 'EK+23%';
        } elseif ($ek_brutto < 7) {
            $new_formel = 'EK+20%';
        } elseif ($ek_brutto < 10) {
            $new_formel = 'EK+18%';
        } elseif ($ek_brutto < 20) {
            $new_formel = 'EK+16%';
        } elseif ($ek_brutto < 30) {
            $new_formel = 'EK+14%';
        } elseif ($ek_brutto < 50) {
            $new_formel = 'EK+13%';
        } elseif ($ek_brutto < 100) {
            $new_formel = 'EK+12%';
        } else {
            $new_formel = 'EK+9%';
        }

        $new_formel .= '+VERSDIFFU';

        return $new_formel;
    }

    public function includesEk(): bool
    {
        return true;
    }
}
