<?php

namespace wws\Shopware\Api;

use bqp\Config\ConfigContainer;
use bqp\Exceptions\DevException;
use bqp\Json;
use debug;
use Exception;
use GuzzleHttp\Psr7\Request;
use InvalidArgumentException;
use K11\Shopware\Api\Exceptions\ProductNotFound;
use wws\Shopware\Api\Rest\RestClient;
use wws\Shopware\Api\Rest\SearchRequest;
use wws\Shopware\Api\Rest\SyncRequestSimple;
use wws\Shopware\EntityRegister\ShopwareEntityRegister;
use wws\Shopware\EntityRegister\ShopwareIdNotFound;
use wws\Shopware\EntityRegister\ShopwareTagRegister;
use wws\Shopware\Product\K11ShopwareProductOffer;
use wws\Shopware\Product\K11ShopwareProductOfferPrice;

/**
 * K11 Shopware Produkt API
 *
 * Weils so toll ist noch eine highlevel API on top auf K11\Shopware\Api. bäh...
 * Die bestehende API ist ungünstig geschnitten und da die ausgelagert ist, machts das alles umständlich.
 * ->primär gings jetzt erstmal API und Geschäftslogik zu trennen. Es war bisher nicht möglich ohne Shopware
 * den Zustand eines Angebots zu errechnen. Dadurch wurde Geschöftslogik teilweise kopiert und bei Batch abgleichen
 * gabs dadurch auch Performance Probleme.
 *
 */
class K11ShopwareProductApi
{
    private ConfigContainer $config;
    private K11ShopwareMetadataDictionary $metadata_dictionary;
    private ShopwareEntityRegister $entity_register;
    private ShopwareTagRegister $tag_register;
    private RestClient $rest_client;

    public function __construct(
        ConfigContainer $config,
        RestClient $rest_client,
        ShopwareEntityRegister $entity_register,
        K11ShopwareMetadataDictionary $metadata_dictionary,
        ShopwareTagRegister $tag_register
    ) {
        $this->config = $config;
        $this->entity_register = $entity_register;
        $this->metadata_dictionary = $metadata_dictionary;
        $this->tag_register = $tag_register;
        $this->rest_client = $rest_client;
    }

    //@todo... api client!
    private function getRequestHeaders(): array
    {
        $headers = $this->rest_client->getHeaders(true);
        unset($headers['debug']);

        $headers['fail-on-error'] = true;
        $headers['single-operation'] = true;

        $headers['indexing-behavior'] = $this->config->getValue('indexing_behavior');
        if ($headers['indexing-behavior'] === null) {
            unset($headers['indexing-behavior']);
        }

        return $headers;
    }

    /**
     * @param int[] $product_ids
     * @return K11ShopwareProductOffer[]
     */
    public function loadK11ShopwareProductOffers(array $product_ids): array
    {
        $parameters = [
            'filter' => [
                [
                    'type' => 'multi',
                    'operator' => 'and',
                    'queries' => [
                        ['type' => 'equalsAny', 'field' => 'productNumber', 'value' => $product_ids],
                    ]
                ]
            ],
            'includes' => [
                'product' => ['id', 'productNumber', 'active', 'price', 'stock', 'customFields', 'deliveryTimeId', 'prices', 'isCloseout', 'taxId', 'tagIds', 'referenceUnit', 'purchaseUnit', 'unit'],
                'product-price' => [],
                'unit' => ['name']
            ],
            'associations' => [
                'prices' => [],
                'unit' => []
            ],
            'sort' => [
                ['field' => 'id', 'order' => 'ASC']
            ]
        ];

        $request = new Request(
            'POST',
            'search/product',
            $this->getRequestHeaders(),
            json_encode($parameters)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        $price_rule_adwords_id = $this->config->getString('priceRuleAdwordsId');

        $sw_product_id_mapping = [];

        $product_offers = [];

        foreach ($result['data'] as $row) {
            $product_offer = new K11ShopwareProductOffer();
            $product_offer->setActive($row['active']);
            $product_offer->setStock($row['stock']);

            $delivery_time_id = $row['deliveryTimeId'];

            if ($delivery_time_id === null) { //nerv... alles kann nix muss. shopware nervt!
                debug::dump($row);
                $delivery_time_id = $this->metadata_dictionary->getShopwareDeliveryTimeIdUnknown();
            }

            $product_offer->setLieferzeitId($this->metadata_dictionary->shopwareDeliveryTimeIdToLieferbarId($delivery_time_id));
            $product_offer->setCloseout($row['isCloseout']);

            $product_id = $row['customFields']['internal_sku'] ?? null;

            if (!$product_id) {
                $product_id = $row['productNumber'];
            }

            if ($product_id != $row['productNumber']) {
                debug::dump($row);
                throw new DevException("productNumber und internal_sku sind nicht identisch");
            }

            $product_offer->setProductId($product_id);
            $product_offer->setShippingFree($row['customFields']['es_product_free_shipping_de'] ?? false);

            $product_offer->setProductVatRateId($this->metadata_dictionary->shopwareTaxIdToProductVatRateId($row['taxId']));

            if (count($row['price']) !== 1) {
                throw new Exception("Produkt mit mehr als ein Preis gefunden");
            }
            $k11_shopware_product_offer_price = new K11ShopwareProductOfferPrice();
            $k11_shopware_product_offer_price->setVkBrutto($row['price'][0]['gross']);
            $k11_shopware_product_offer_price->setVkNetto($row['price'][0]['net']);

            $currency_id = $row['price'][0]['currencyId'];

            $k11_shopware_product_offer_price->setCurrency($this->metadata_dictionary->shopwareCurrencyIdToCurrencyCode($currency_id));
            $product_offer->setPrice($k11_shopware_product_offer_price);

            $product_offer->setReferenceUnit($row['referenceUnit']);
            $product_offer->setPurchaseUnit($row['purchaseUnit']);
            if (isset($row['unit']['name'])) {
                $product_offer->setUnit($row['unit']['name']);
            }

            foreach ($row['prices'] as $price) {
                if ($price['ruleId'] === $price_rule_adwords_id) {
                    $price_adwords = new K11ShopwareProductOfferPrice();
                    $price_adwords->setVkBrutto($price['price'][0]['gross']);
                    $price_adwords->setVkNetto($price['price'][0]['net']);
                    $price_adwords->setCurrency($this->metadata_dictionary->shopwareCurrencyIdToCurrencyCode($price['price'][0]['currencyId']));
                    $product_offer->setPriceAdwords($price_adwords);
                }
            }

            $product_offer->setEsGoogleAdsMarginAmount($row['customFields']['es_google_ads_margin_amount'] ?? 0);
            $product_offer->setEsGoogleAdsMarginGroup($row['customFields']['es_google_ads_margin_group'] ?? '');
            $product_offer->setAlwaysShowFirstAt($row['customFields']['AlwaysShowFirstAT'] ?? false);
            $product_offer->setShadowProduct($row['customFields']['shadowProduct'] ?? false);
            $product_offer->setShadowProductOrderable($row['customFields']['shadowProductOrderable'] ?? false);
            $product_offer->setTestProduct($row['customFields']['testProduct'] ?? false);
            $product_offer->setShipmentCostDe($row['customFields']['shipment_cost_de'] ?? null);
            $product_offer->setShipmentCostAt($row['customFields']['shipment_cost_at'] ?? null);
            $product_offer->setShipmentType($row['customFields']['shipment_type'] ?? null);
            $product_offer->setAswoNumber($row['customFields']['aswo_number'] ?? '');

            $product_offer->setTags($this->tag_register->filterRelevantShopwareTagIdsAndConvertToProductTags($row['tagIds'] ?? []));

            $product_offers[$product_offer->getProductId()] = $product_offer;

            $sw_product_id_mapping[$row['id']] = $product_offer->getProductId();
        }

        //aswo mapping auslesen... muss sperat, in shopware ist keine assoziation in die richtung definiert.
        $parameters = [
            'filter' => [
                [
                    'type' => 'multi',
                    'operator' => 'and',
                    'queries' => [
                        ['type' => 'equalsAny', 'field' => 'productId', 'value' => array_keys($sw_product_id_mapping)],
                    ]
                ]
            ],
            'includes' => [
                'xanten_aswo' => ['productId', 'aswoNumber'],
            ],
        ];

        $request = new Request(
            'POST',
            'search/xanten-aswo',
            $this->getRequestHeaders(),
            json_encode($parameters)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        foreach ($result['data'] as $row) {
            $product_id = $sw_product_id_mapping[$row['productId']];
            $product_offers[$product_id]->addAswoNumberMapping($row['aswoNumber']);
        }

        return $product_offers;
    }


    public function updateK11ShopwareProductOffer(K11ShopwareProductOffer $k11_shopware_product_offer): void
    {
        $this->updateK11ShopwareProductOffers([$k11_shopware_product_offer]);
    }

    /**
     * @param K11ShopwareProductOffer[] $k11_shopware_product_offers
     * @return void
     */
    public function updateK11ShopwareProductOffers(array $k11_shopware_product_offers): void
    {
        if (!$k11_shopware_product_offers) {
            throw new InvalidArgumentException();
        }

        $payloads = $this->convertK11ShopwareProductOffers($k11_shopware_product_offers);

        $request = new SyncRequestSimple('product');
        $request->setPayload($payloads);

        $this->rest_client->send($request);

        $this->updateK11ShopwareProductOffersTags($k11_shopware_product_offers);
        $this->updateK11ShopwareProductOffersAswoMappings($k11_shopware_product_offers);
    }


    /**
     * @param K11ShopwareProductOffer[] $k11_shopware_product_offers
     * @return void
     */
    public function updateK11ShopwareProductOffersAswoMappings(array $k11_shopware_product_offers): void
    {
        //aktuelle mappings auslesen
        $product_ids = array_map(fn(K11ShopwareProductOffer $offer) => $offer->getProductId(), $k11_shopware_product_offers);

        $sw_product_ids = $this->entity_register->getShopwareIds($this->entity_register::ENTITY_PRODUCT, $product_ids);

        $mapping = [];

        foreach ($k11_shopware_product_offers as $offer) {
            foreach ($offer->getAswoNumberMapping() as $aswo_number) {
                $sw_product_id = $sw_product_ids[$offer->getProductId()];

                $key = md5($sw_product_id . '-' . $aswo_number);

                $mapping[$key] = [
                    'id' => $key,
                    'sw_product_id' => $sw_product_id,
                    'aswo_number' => $aswo_number,
                    'action' => 'add',
                ];
            }
        }

        $parameters = [
            'filter' => [
                [
                    'type' => 'multi',
                    'operator' => 'and',
                    'queries' => [
                        ['type' => 'equalsAny', 'field' => 'productId', 'value' => $sw_product_ids],
                    ]
                ]
            ],
        ];

        $request = new Request(
            'POST',
            'search/xanten-aswo',
            $this->getRequestHeaders(),
            json_encode($parameters)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        foreach ($result['data'] as $row) {
            $key = md5($row['productId'] . '-' . $row['aswoNumber']);

            $action = 'delete';
            if (isset($mapping[$key])) {
                $action = 'noop';
            }

            $mapping[$key] = [
                'id' => $row['id'],
                'sw_product_id' => $row['productId'],
                'aswo_number' => $row['aswoNumber'],
                'action' => $action,
            ];
        }

        $add = [];
        $delete = [];

        foreach ($mapping as $row) {
            switch ($row['action']) {
                case 'add':
                    $add[] = [
                        'id' => $row['id'],
                        'aswoNumber' => $row['aswo_number'],
                        'productId' => $row['sw_product_id'],
                    ];
                    break;
                case 'delete':
                    $delete[] = ['id' => $row['id']];
                    break;
            }
        }

        //ausführen (das kann in der sync api in einem request ausgführt werden, das interface gibts nicht her.)
        if ($delete) {
            $request = new SyncRequestSimple('xanten_aswo', SyncRequestSimple::ACTION_DELETE);
            $request->setPayload($delete);

            $this->rest_client->send($request);
        }

        if ($add) {
            $request = new SyncRequestSimple('xanten_aswo', SyncRequestSimple::ACTION_UPSERT);
            $request->setPayload($add);

            $this->rest_client->send($request);
        }
    }

    /**
     * @param K11ShopwareProductOffer[] $k11_shopware_product_offers
     * @return void
     */
    public function updateK11ShopwareProductOffersTags(array $k11_shopware_product_offers): void
    {
        //aktuelle tags auslesen
        $product_ids = array_map(fn(K11ShopwareProductOffer $offer) => $offer->getProductId(), $k11_shopware_product_offers);

        //*nerv* shopwares denormalisierung kann in sich inkonstent werden. das passiert teilweise asyncron und ist bei hoher auslastung ein problem.
        //die version fragt die product_tag relation explizit ab und dumpt ggf abweichungen auf stdout (wie oft kommt das vor?)
        $parameters = [
            'filter' => [
                [
                    'type' => 'multi',
                    'operator' => 'and',
                    'queries' => [
                        ['type' => 'equalsAny', 'field' => 'productNumber', 'value' => $product_ids],
                    ]
                ]
            ],
            'associations' => [
                'tags' => []
            ],
            'includes' => [
                'product' => ['productNumber', 'tags', 'tagIds'],
                'tag' => ['id', 'name']
            ],
        ];

        $request = new Request(
            'POST',
            'search/product',
            $this->getRequestHeaders(),
            json_encode($parameters)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        $current_shopware_tags_collection = [];

        foreach ($result['data'] as $product_data) {
            $product_id = (int)$product_data['productNumber'];

            $tag_ids = [];

            foreach ($product_data['tags'] as $tag) {
                $tag_ids[] = $tag['id'];
            }

            $current_shopware_tags_collection[$product_id] = $this->tag_register->filterRelevantShopwareTagIdsAndConvertToProductTags($tag_ids ?? []);

            //
            $denormalized_tag_ids = $product_data['tagIds'] ?? [];

            if (array_diff($tag_ids, $denormalized_tag_ids) || array_diff($denormalized_tag_ids, $tag_ids)) {
                debug::dump('product.tagIds != product_tag relation', $product_data);
            }
            //
        }

//naive version die über product.tagIds geht
//        $parameters = [
//            'filter' => [
//                [
//                    'type' => 'multi',
//                    'operator' => 'and',
//                    'queries' => [
//                        ['type' => 'equalsAny', 'field' => 'productNumber', 'value' => $product_ids],
//                    ]
//                ]
//            ],
//            'includes' => [
//                'product' => ['productNumber', 'tagIds'],
//            ],
//        ];
//
//        $request = new Request(
//            'POST',
//            'search/product',
//            $this->getRequestHeaders(),
//            json_encode($parameters)
//        );
//
//        $response = $this->rest_client->sendRequest($request);
//        $result = Json::decode((string)$response->getBody());
//
//        $current_shopware_tags_collection = [];
//
//        foreach ($result['data'] as $product_data) {
//            $product_id = (int)$product_data['productNumber'];
//            $current_shopware_tags_collection[$product_id] = $this->tag_register->filterRelevantShopwareTagIdsAndConvertToProductTags($product_data['tagIds'] ?? []);
//        }
//        //

        $add = [];
        $delete = [];

        foreach ($k11_shopware_product_offers as $offer) {
            $product_id = $offer->getProductId();

            $current_shopware_tags = $current_shopware_tags_collection[$product_id] ?? null;

            if ($current_shopware_tags !== null) {
                $added_tags = array_diff($offer->getTags(), $current_shopware_tags);
                foreach ($added_tags as $added_tag) {
                    $add[] = ['product_id' => $product_id, 'product_tag' => $added_tag];
                }

                $removed_tags = array_diff($current_shopware_tags, $offer->getTags());
                foreach ($removed_tags as $removed_tag) {
                    $delete[] = ['product_id' => $product_id, 'product_tag' => $removed_tag];
                }
            } else {
                $add = array_map(fn($tag) => ['product_id' => $product_id, 'product_tag' => $tag], $offer->getTags());
            }
        }

        //ausführen (das kann in der sync api in einem request ausgführt werden, das interface gibts nicht her.)
        if ($add) {
            $request = new SyncRequestSimple('product_tag', SyncRequestSimple::ACTION_UPSERT);
            $request->setPayload($this->updateK11ShopwareProductOffersTags_convertActionParameters($add));

            $this->rest_client->send($request);
        }

        if ($delete) {
            $request = new SyncRequestSimple('product_tag', SyncRequestSimple::ACTION_DELETE);
            $request->setPayload($this->updateK11ShopwareProductOffersTags_convertActionParameters($delete));

            $this->rest_client->send($request);
        }
    }

    private function updateK11ShopwareProductOffersTags_convertActionParameters(array $changes): array
    {
        $action_parameters = [];

        foreach ($changes as $change) {
            $action_parameters[] = [
                'productId' => $this->entity_register->getShopwareId($this->entity_register::ENTITY_PRODUCT, $change['product_id']),
                'tagId' => $this->tag_register->getOrCreateShopwareTagId($change['product_tag'])
            ];
        }

        return $action_parameters;
    }

    /**
     * @param K11ShopwareProductOffer[] $k11_shopware_product_offers
     * @return array
     */
    public function convertK11ShopwareProductOffers(array $k11_shopware_product_offers): array
    {
        $payloads = [];

        try {
            $sw_product_ids = $this->entity_register->getShopwareIds($this->entity_register::ENTITY_PRODUCT, array_map(fn(K11ShopwareProductOffer $offer) => $offer->getProductId(), $k11_shopware_product_offers));
        } catch (ShopwareIdNotFound $e) {
            throw new ProductNotFound($e->getMessage());
        }

        $special_price_ids_all = $this->findAdvancedPriceIds($sw_product_ids);

        foreach ($k11_shopware_product_offers as $k11_shopware_product_offer) {
            $special_price_ids = $special_price_ids_all[$sw_product_ids[$k11_shopware_product_offer->getProductId()]];

            $payloads[] = $this->convertK11ShopwareProductOffer($k11_shopware_product_offer, $sw_product_ids[$k11_shopware_product_offer->getProductId()], $special_price_ids);
        }

        return $payloads;
    }

    /**
     * @throws ProductNotFound
     */
    public function convertK11ShopwareProductOffer(K11ShopwareProductOffer $k11_shopware_product_offer, ?string $sw_product_id = null, ?array $special_price_ids = null): array
    {
        if ($sw_product_id === null) {
            try {
                $sw_product_id = $this->entity_register->getShopwareId($this->entity_register::ENTITY_PRODUCT, $k11_shopware_product_offer->getProductId());
            } catch (ShopwareIdNotFound) {
                throw new ProductNotFound($k11_shopware_product_offer->getProductId());
            }
        }

        $result = [];
        $result['id'] = $sw_product_id;

        $k11_shopware_product_offer_price = $k11_shopware_product_offer->getPrice();

        $result['price'] = [
            [
                'currencyId' => $this->metadata_dictionary->getCurrencyId($k11_shopware_product_offer_price->getCurrency()),
                'gross' => $k11_shopware_product_offer_price->getVkBrutto(),
                'net' => $k11_shopware_product_offer_price->getVkNetto(),
                'linked' => false
            ]
        ];

        $result['referenceUnit'] = $k11_shopware_product_offer->getReferenceUnit();
        $result['purchaseUnit'] = $k11_shopware_product_offer->getPurchaseUnit();
        if ($k11_shopware_product_offer->getUnit()) {
            $result['unitId'] = $this->metadata_dictionary->getShopwareUnitId($k11_shopware_product_offer->getUnit());
        } else {
            $result['unitId'] = null;
        }

        $price_containers = [];

        $adwords_price = $k11_shopware_product_offer->getPriceAdwords();
        if ($adwords_price) {
            if ($special_price_ids === null) {
                $special_price_ids = $this->findAdvancedPriceIdsForProduct($sw_product_id);
            }

            $special_price = [
                'id' => $special_price_ids['AdwordsPrice'], //wenn wir die bestehende id nicht angegeben wird, dann legen wir die preise immer wieder neu an
                'productId' => $sw_product_id,
                'quantityStart' => 1,
                'quantityEnd' => null,
                'ruleId' => $this->config->getString('priceRuleAdwordsId'),
                'price' => [
                    [
                        'currencyId' => $this->metadata_dictionary->getCurrencyId($adwords_price->getCurrency()),
                        'gross' => $adwords_price->getVkBrutto(),
                        'net' => $adwords_price->getVkNetto(),
                        'linked' => false
                    ]
                ]
            ];

            $price_containers[] = $special_price;
        }

        if ($price_containers) {
            $result['prices'] = $price_containers;
        }

        //in der api von nico wurde die productNumber explizit bei der serialisierung entfernt... das sollte aber eigentlich kein problem geben.
        $result['productNumber'] = $k11_shopware_product_offer->getProductId();
        $result['taxId'] = $this->metadata_dictionary->productVatRateIdToShopwareTaxId($k11_shopware_product_offer->getProductVatRateId());

        $result['stock'] = $k11_shopware_product_offer->getStock();
        $result['isCloseout'] = $k11_shopware_product_offer->isCloseout();

        $result['active'] = $k11_shopware_product_offer->isActive();

        $shopware_delivery_time_id = $this->metadata_dictionary->getShopwareDeliveryTimeId($k11_shopware_product_offer->getLieferzeitId());

        $result['deliveryTimeId'] = $shopware_delivery_time_id;
        $result['shippingFree'] = false;

        $custom_fields = [];
        $custom_fields['internal_sku'] = (string)$k11_shopware_product_offer->getProductId();
        $custom_fields['aswo_number'] = $k11_shopware_product_offer->getAswoNumber();

        //versand
        //die kombination shippingFree/es_product_free_shipping_de ist unklar... bin mir nicht sicher ob ich das nicht schon weiter vorne kaputt gemacht habe. Aber es muss auch den Grund für das custome_field haben
        $custom_fields['es_product_free_shipping_de'] = $k11_shopware_product_offer->isShippingFree();

        $custom_fields['shipment_cost_de'] = $k11_shopware_product_offer->getShipmentCostDe();
        $custom_fields['shipment_cost_at'] = $k11_shopware_product_offer->getShipmentCostAt();
        $custom_fields['shipment_type'] = $k11_shopware_product_offer->getShipmentType();
        //

        $custom_fields['shadowProduct'] = $k11_shopware_product_offer->isShadowProduct();
        $custom_fields['shadowProductOrderable'] = $k11_shopware_product_offer->isShadowProductOrderable();
        $custom_fields['testProduct'] = $k11_shopware_product_offer->isTestProduct();

        $custom_fields['es_google_ads_margin_amount'] = $k11_shopware_product_offer->getEsGoogleAdsMarginAmount();
        $custom_fields['es_google_ads_margin_group'] = $k11_shopware_product_offer->getEsGoogleAdsMarginGroup();
        $custom_fields['AlwaysShowFirstAT'] = (int)$k11_shopware_product_offer->isAlwaysShowFirstAt();

        $result['customFields'] = $custom_fields;

        return $result;
    }

    private function findAdvancedPriceIdsForProduct(string $shopware_product_id): array
    {
        return $this->findAdvancedPriceIds([$shopware_product_id])[$shopware_product_id];
    }

    private function findAdvancedPriceIds(array $shopware_product_ids): array
    {
        $request = new SearchRequest();
        $request->setEntity('product-price');
        $request->setFilter([
            [
                'field' => 'product.id',
                'type' => 'equalsAny',
                'value' => $shopware_product_ids
            ]
        ]);

        $response = $this->rest_client->send($request);

        $result = [];
        foreach ($shopware_product_ids as $shopware_product_id) {
            $result[$shopware_product_id] = [
                'AdwordsPrice' => null
            ];
        }

        foreach ($response->getData() as $row) {
            switch ($row['ruleId']) {
                case $this->config['priceRuleAdwordsId']:
                    $result[$row['productId']]['AdwordsPrice'] = $row['id'];
                    break;
            }
        }

        return $result;
    }

    /**
     * @param array $product_ids
     * @return array{
     *              known_product_ids: int[],
     *              unknown_product_ids: int[]
     *         }
     */
    public function splitProductIdsByShopware(array $product_ids): array
    {
        $known_product_ids = [];
        $unknown_product_ids = array_combine($product_ids, $product_ids);

        $matches = $this->entity_register->tryGetShopwareIds($this->entity_register::ENTITY_PRODUCT, $product_ids);

        foreach ($matches as $product_id => $shopware_id) {
            $known_product_ids[] = $product_id;
            unset($unknown_product_ids[$product_id]);
        }

        $unknown_product_ids = array_values($unknown_product_ids);

        return [
            'known_product_ids' => $known_product_ids,
            'unknown_product_ids' => $unknown_product_ids
        ];
    }
}
