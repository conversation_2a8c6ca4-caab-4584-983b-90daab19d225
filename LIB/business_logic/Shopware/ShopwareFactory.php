<?php

namespace wws\Shopware;

use bqp\Config\ConfigContainer;
use bqp\Config\ConfigRegistry;
use bqp\db\db_generic;
use bqp\EntityQueue\EntityQueue;
use bqp\EntityQueue\EntityQueueFactory;
use DI\Container;
use GuzzleHttp\Client;
use K11\Shopware\Api\Configuration;
use K11\Shopware\Api\Interfaces\RestClient;
use K11\Shopware\Api\NullLogger;
use K11\Shopware\Api\ShopwareApi;
use K11\Shopware\Api\ShopwareAuth;
use service_loader;
use wws\Order\OrderRepository;
use wws\Product\ProductConst;
use wws\Product\ProductRepository;
use wws\Product\Utils\K11PriceMarginCalculator;
use wws\Shopware\Api\Auth\ShopwareAuthData;
use wws\Shopware\Api\K11ShopwareCatApi;
use wws\Shopware\Api\K11ShopwareMetadataDictionary;
use wws\Shopware\Api\K11ShopwareProductApi;
use wws\Shopware\Api\K11ShopwareUrlApi;
use wws\Shopware\EntityRegister\ShopwareEntityRegister;
use wws\Shopware\EntityRegister\ShopwareTagRegister;
use wws\Shopware\Order\Converters\ShopwareOrderAddressToWwsAddress;
use wws\Shopware\Order\Converters\ShopwareOrderToWwsOrder;
use wws\Shopware\Order\K11ShopwareOrderApiMitigation;
use wws\Shopware\Order\OrderDeviceImporter;
use wws\Shopware\Order\OrderImporter;
use wws\Shopware\Product\AswoProductDeviceMappingSync;
use wws\Shopware\Product\K11ShopwareFeatureService;
use wws\Shopware\Product\ProductActiveSyncComplete;
use wws\Shopware\Product\ProductAssociationSync;
use wws\Shopware\Product\ProductBrandSync;
use wws\Shopware\Product\ProductCatSyncShopwareToWws;
use wws\Shopware\Product\ProductCatSyncWwsToShopware;
use wws\Shopware\Product\ProductDataSync;
use wws\Shopware\Product\ProductDeviceGroupSync;
use wws\Shopware\Product\ProductDeviceSync;
use wws\Shopware\Product\ProductMediaSync;
use wws\Shopware\Product\ProductOfferSync;
use wws\Shopware\Product\ProductOfferSyncComplete;
use wws\Shopware\Product\ProductToK11ShopwareProductOffer;
use wws\Shopware\Product\ShopwareProductQueueManager;

class ShopwareFactory
{
    public const QUEUE_PRODUCT_OFFER = 'product_offer';
    public const QUEUE_PRODUCT_DATA = 'product_data';
    public const QUEUE_PRODUCT_MEDIA = 'product_media';
    public const QUEUE_PRODUCT_DEVICE_MAPPING = 'product_device_mapping';
    public const QUEUE_PRODUCT_DEVICE = 'product_device';
    public const QUEUE_PRODUCT_DEVICE_GROUP = 'product_device_group';
    public const QUEUE_PRODUCT_ASSOCIATION = 'product_association';


    // identical part of all shopware api configurations
    private const CONFIG_PREFIX = 'k11/shopware_api';

    private array $instances = [];

    public function __construct(private readonly ConfigRegistry $config_registry, private readonly Container $container)
    {
    }

    public function getConfigNames(): array
    {
        return $this->config_registry->scanForConfigsByPrefix(self::CONFIG_PREFIX);
    }

    public function getShopwareApi(string $name): ShopwareApi
    {
        if (!isset($this->instances[$name][ShopwareApi::class])) {
            $config = $this->getConfig($name);

            $client = new Client(['base_uri' => $config->getString('baseUri')]);

            $auth = new ShopwareAuth(new ShopwareAuthData($config));

            //$logger = new ShopwareLogger(db::getInstance()->getDbHandle()); //datenmüll ohne ende
            $logger = new NullLogger();
            $shopware_api_config = new Configuration();

            $api = new ShopwareApi(
                $client,
                $shopware_api_config,
                $auth,
                $logger
            );

            $this->instances[$name][ShopwareApi::class] = $api;
        }

        return $this->instances[$name][ShopwareApi::class];
    }

    public function getConfig(string $name): ConfigContainer
    {
        return $this->config_registry->get($name);
    }

    public function getProductAssociationSync(string $name): ProductAssociationSync
    {
        if (!isset($this->instances[$name][ProductAssociationSync::class])) {
            $this->instances[$name][ProductAssociationSync::class] = new ProductAssociationSync(
                $this->getRestClient($name),
                $this->getEntityRegister($name),
            );
        }

        return $this->instances[$name][ProductAssociationSync::class];
    }

    public function getProductOfferSync(string $name): ProductOfferSync
    {
        if (!isset($this->instances[$name][ProductOfferSync::class])) {
            $this->instances[$name][ProductOfferSync::class] = new ProductOfferSync(
                $this->getProductToK11ShopwareProductOffer($name),
                $this->getK11ShopwareProductApi($name),
                $this->getShopwareProductQueueManager($name),
            );
        }

        return $this->instances[$name][ProductOfferSync::class];
    }

    public function getProductDataSync(string $name): ProductDataSync
    {
        if (!isset($this->instances[$name][ProductDataSync::class])) {
            $this->instances[$name][ProductDataSync::class] = new ProductDataSync(
                $this->getRestClient($name),
                $this->getEntityRegister($name),
                $this->getProductToK11ShopwareProductOffer($name),
                $this->getK11ShopwareProductApi($name),
                $this->getK11ShopwareFeatureService($name),
                $this->getShopwareProductQueueManager($name),
                $this->getConfig($name),
                $this->getProductCatSyncForProductCats($name),
                service_loader::get(ProductRepository::class),
            );
        }

        return $this->instances[$name][ProductDataSync::class];
    }

    public function getProductDeviceSync(string $name): ProductDeviceSync
    {
        if (!isset($this->instances[$name][ProductDeviceSync::class])) {
            $this->instances[$name][ProductDeviceSync::class] = new ProductDeviceSync(
                $this->getRestClient($name),
                $this->getEntityRegister($name),
                $this->getShopwareProductQueueManager($name),
            );
        }

        return $this->instances[$name][ProductDeviceSync::class];
    }

    public function getProductMediaSync(string $name): ProductMediaSync
    {
        if (!isset($this->instances[$name][ProductMediaSync::class])) {
            $this->instances[$name][ProductMediaSync::class] = new ProductMediaSync(
                $this->getRestClient($name),
                $this->getEntityRegister($name),
                $this->getConfig($name)->getInt('sync_sw_data_source_profile_id')
            );
        }

        return $this->instances[$name][ProductMediaSync::class];
    }

    public function getProductToK11ShopwareProductOffer(string $name): ProductToK11ShopwareProductOffer
    {
        if (!isset($this->instances[$name][ProductToK11ShopwareProductOffer::class])) {
            $config = $this->getConfig($name);

            $this->instances[$name][ProductToK11ShopwareProductOffer::class] = new ProductToK11ShopwareProductOffer(
                $this->container->get(db_generic::class),
                $this->container->get(K11PriceMarginCalculator::class),
                $config->getInt('filter_list_id'),
                ProductConst::FILTER_LIST_ID_ERSATZTEILSHOP_GOOGLE,
                ProductConst::FILTER_LIST_ID_ERSATZTEILSHOP_FOREIGN,
                $this->getShopwareTagRegister($name),
            );
        }

        return $this->instances[$name][ProductToK11ShopwareProductOffer::class];
    }

    public function getProductActiveSyncComplete(string $name): ProductActiveSyncComplete
    {
        if (!isset($this->instances[$name][ProductActiveSyncComplete::class])) {
            $config = $this->getConfig($name);

            $this->instances[$name][ProductActiveSyncComplete::class] = new ProductActiveSyncComplete(
                $this->getRestClient($name),
                $config->getInt('filter_list_id'),
                $this->container->get(db_generic::class),
                $this->getShopwareApi($name),
            );
        }

        return $this->instances[$name][ProductActiveSyncComplete::class];
    }

    public function getProductBrandSync(string $name): ProductBrandSync
    {
        if (!isset($this->instances[$name][ProductBrandSync::class])) {
            $this->instances[$name][ProductBrandSync::class] = new ProductBrandSync(
                $this->getRestClient($name),
                $this->getEntityRegister($name),
            );
        }

        return $this->instances[$name][ProductBrandSync::class];
    }

    public function getK11ShopwareProductApi(string $name): K11ShopwareProductApi
    {
        if (!isset($this->instances[$name][K11ShopwareProductApi::class])) {
            $config = $this->getConfig($name);

            $this->instances[$name][K11ShopwareProductApi::class] = new K11ShopwareProductApi(
                $config,
                $this->getRestClient($name),
                $this->getEntityRegister($name),
                $this->getK11ShopwareMetadataDictionary($name),
                $this->getShopwareTagRegister($name),
            );
        }

        return $this->instances[$name][K11ShopwareProductApi::class];
    }


    public function getK11ShopwareCatApi(string $name): K11ShopwareCatApi
    {
        if (!isset($this->instances[$name][K11ShopwareCatApi::class])) {
            $this->instances[$name][K11ShopwareCatApi::class] = new K11ShopwareCatApi(
                $this->getRestClient($name),
            );
        }

        return $this->instances[$name][K11ShopwareCatApi::class];
    }

    public function getProductOfferSyncComplete(string $name): ProductOfferSyncComplete
    {
        if (!isset($this->instances[$name][ProductOfferSyncComplete::class])) {
            $this->instances[$name][ProductOfferSyncComplete::class] = new ProductOfferSyncComplete(
                $this->getK11ShopwareProductApi($name),
                $this->getProductToK11ShopwareProductOffer($name),
            );
        }

        return $this->instances[$name][ProductOfferSyncComplete::class];
    }

    public function getOrderImporter(string $name): OrderImporter
    {
        if (!isset($this->instances[$name][OrderImporter::class])) {
            $shopware_order_to_wws_order = new ShopwareOrderToWwsOrder(
                $this->container->get(ShopwareOrderAddressToWwsAddress::class),
                new K11ShopwareMetadataDictionary($this->getShopwareApi($name))
            );

            $this->instances[$name][OrderImporter::class] = new OrderImporter(
                $this->getShopwareApi($name),
                $this->container->get(OrderRepository::class),
                $shopware_order_to_wws_order,
                $this->container->get(ShopwareOrderAddressToWwsAddress::class),
                $this->getK11ShopwareOrderApiMitigation($name),
                $this->getOrderDeviceImporter($name),
                $this->getRestClient($name)
            );
        }

        return $this->instances[$name][OrderImporter::class];
    }

    public function getEntityRegister(string $name): ShopwareEntityRegister
    {
        if (!isset($this->instances[$name][ShopwareEntityRegister::class])) {
            $shopware_entity_register = new ShopwareEntityRegister(
                $this->container->get(db_generic::class),
            );

            $shopware_entity_register->setShopwareInstanceId($this->getConfig($name)->getInt('shopware_entity_register_instance_id'));

            $this->instances[$name][ShopwareEntityRegister::class] = $shopware_entity_register;
        }

        return $this->instances[$name][ShopwareEntityRegister::class];
    }

    public function getK11ShopwareOrderApiMitigation(string $name): K11ShopwareOrderApiMitigation
    {
        if (!isset($this->instances[$name][K11ShopwareOrderApiMitigation::class])) {
            $this->instances[$name][K11ShopwareOrderApiMitigation::class] = new K11ShopwareOrderApiMitigation(
                $this->getRestClient($name),
            );
        }

        return $this->instances[$name][K11ShopwareOrderApiMitigation::class];
    }

    /**
     * Achtung: da der konfiguriert werden kann, muss hier immer eine neue instanz erzeugt werden, ansonsten kann es sein, dass die konfiguration sich überschneiden
     *
     * @param string $name
     * @return ProductCatSyncShopwareToWws
     */
    public function createProductCatSyncShopwareToWws(string $name): ProductCatSyncShopwareToWws
    {
        return new ProductCatSyncShopwareToWws(
            $this->getEntityRegister($name),
            $this->getRestClient($name),
        );
    }

    /**
     * Achtung: da der konfiguriert werden kann, muss hier immer eine neue instanz erzeugt werden, ansonsten kann es sein, dass die konfiguration sich überschneiden
     */
    public function createProductCatSyncWwsToShopware(string $name, int $cat_tree_id, ?string $shopware_root_cat_id, string $shopware_type, int $root_cat_id = 0): ProductCatSyncWwsToShopware
    {
        return new ProductCatSyncWwsToShopware(
            $this->getK11ShopwareCatApi($name),
            $this->getEntityRegister($name),
            $cat_tree_id,
            $shopware_root_cat_id,
            $shopware_type,
            $root_cat_id
        );
    }

    public function getProductCatSyncForProductCats(string $name): ProductCatSyncWwsToShopware
    {
        $config = $this->getConfig($name);

        $sync = $this->createProductCatSyncWwsToShopware(
            $name,
            $config->getInt('sync_cat_tree_id_standard'),
            $config->getString('sync_sw_root_cat_id_ersatzteile'),
            'product',
            $config->getInt('sync_root_cat_id'),
        );

        return $sync;
    }

    public function getProductCatSyncForDeviceCats(string $name): ProductCatSyncWwsToShopware
    {
        $config = $this->getConfig($name);

        $sync = $this->createProductCatSyncWwsToShopware(
            $name,
            $config->getInt('sync_cat_tree_id_geraete'),
            $config->getString('sync_sw_root_cat_id_geraete'),
            'appliance'
        );

        return $sync;
    }

    /**
     * @param string $name
     * @return RestClient
     * @deprecated use getRestClient instead. it also implement the RestClient interface
     * ->bleibt noch mal für den fall der fälle hier...
     *
     */
    public function getLegacyRestClient(string $name): RestClient
    {
        if (!isset($this->instances[$name][RestClient::class])) {
            $this->instances[$name][RestClient::class] = $this->getShopwareApi($name)->getRestClient();
        }

        return $this->instances[$name][RestClient::class];
    }

    public function getRestClient(string $name): Api\Rest\RestClient
    {
        if (!isset($this->instances[$name][RestClient::class])) {
            $this->instances[$name][RestClient::class] = new Api\Rest\RestClient(
                $this->getConfig($name)
            );
        }

        return $this->instances[$name][RestClient::class];
    }

    private function getK11ShopwareFeatureService(string $name): K11ShopwareFeatureService
    {
        if (!isset($this->instances[$name][K11ShopwareFeatureService::class])) {
            $this->instances[$name][K11ShopwareFeatureService::class] = new K11ShopwareFeatureService(
                $this->getEntityRegister($name),
                $this->getRestClient($name),
            );
        }

        return $this->instances[$name][K11ShopwareFeatureService::class];
    }

    private function getK11ShopwareMetadataDictionary(string $name): K11ShopwareMetadataDictionary
    {
        if (!isset($this->instances[$name][K11ShopwareMetadataDictionary::class])) {
            $this->instances[$name][K11ShopwareMetadataDictionary::class] = new K11ShopwareMetadataDictionary(
                $this->getShopwareApi($name),
            );
        }

        return $this->instances[$name][K11ShopwareMetadataDictionary::class];
    }


    public function getK11ShopwareUrlApi(string $name): K11ShopwareUrlApi
    {
        if (!isset($this->instances[$name][K11ShopwareUrlApi::class])) {
            $this->instances[$name][K11ShopwareUrlApi::class] = new K11ShopwareUrlApi(
                $this->getConfig($name),
                $this->getRestClient($name),
            );
        }

        return $this->instances[$name][K11ShopwareUrlApi::class];
    }

    public function getShopwareProductQueueManager(string $name): ShopwareProductQueueManager
    {
        if (!isset($this->instances[$name][ShopwareProductQueueManager::class])) {
            $this->instances[$name][ShopwareProductQueueManager::class] = new ShopwareProductQueueManager(
                $this->getConfig($name)->getInt('filter_list_id'),
                $this->container->get('db'),
                $this->getEntityQueue($name, self::QUEUE_PRODUCT_OFFER),
                $this->getEntityQueue($name, self::QUEUE_PRODUCT_DATA),
                $this->getEntityQueue($name, self::QUEUE_PRODUCT_MEDIA),
                $this->getEntityQueue($name, self::QUEUE_PRODUCT_DEVICE),
                $this->getEntityQueue($name, self::QUEUE_PRODUCT_DEVICE_MAPPING),
                $this->getEntityQueue($name, self::QUEUE_PRODUCT_ASSOCIATION),
            );
        }

        return $this->instances[$name][ShopwareProductQueueManager::class];
    }

    public function getAswoProductDeviceMappingSync(string $name): AswoProductDeviceMappingSync
    {
        if (!isset($this->instances[$name][AswoProductDeviceMappingSync::class])) {
            $this->instances[$name][AswoProductDeviceMappingSync::class] = new AswoProductDeviceMappingSync(
                $this->container->get('db'),
                $this->getRestClient($name),
                $this->getEntityRegister($name),
            );
        }

        return $this->instances[$name][AswoProductDeviceMappingSync::class];
    }


    public function getShopwareTagRegister(string $name): ShopwareTagRegister
    {
        if (!isset($this->instances[$name][ShopwareTagRegister::class])) {
            $this->instances[$name][ShopwareTagRegister::class] = new ShopwareTagRegister(
                $this->getRestClient($name)
            );
        }

        return $this->instances[$name][ShopwareTagRegister::class];
    }

    public function getOrderDeviceImporter(string $name): OrderDeviceImporter
    {
        if (!isset($this->instances[$name][OrderDeviceImporter::class])) {
            $this->instances[$name][OrderDeviceImporter::class] = new OrderDeviceImporter(
                $this->getEntityRegister($name),
                $this->getRestClient($name),
                $this->container->get('db')
            );
        }
        return $this->instances[$name][OrderDeviceImporter::class];
    }

    public function getProductDeviceGroupSync(string $name): ProductDeviceGroupSync
    {
        if (!isset($this->instances[$name][ProductDeviceGroupSync::class])) {
            $this->instances[$name][ProductDeviceGroupSync::class] = new ProductDeviceGroupSync(
                $this->getRestClient($name),
                $this->getEntityRegister($name),

            );
        }

        return $this->instances[$name][ProductDeviceGroupSync::class];
    }

    public function getEntityQueue(string $name, string $queue_type): EntityQueue
    {
        $key = 'entity_queue_' . $queue_type;

        if (!isset($this->instances[$name][$key])) {
            $queue = $this->container->get(EntityQueueFactory::class)->getEntityQueue($this->getConfig($name)->getInt('entity_queue_id_' . $queue_type));

            $this->instances[$name][$key] = $queue;
        }

        return $this->instances[$name][$key];
    }
}
