<?php

namespace wws\Shopware\Product;

use bqp\Config\ConfigContainer;
use bqp\Date\DateObj;
use bqp\EntityQueue\EntityQueueRunnerCollection;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Json;
use bqp\KeyValueStore\KeyValueStore;
use bqp\Model\SmartDataEntityNotFoundException;
use db;
use debug;
use GuzzleHttp\Psr7\Request;
use input;
use K11\Shopware\Api\Exceptions\ProductNotFound;
use K11\Shopware\Api\Interfaces\RestClient;
use service_loader;
use wws\Product\Product;
use wws\Product\ProductConst;
use wws\Product\ProductDataProvider;
use wws\Product\ProductFeature\ProductFeatureCache;
use wws\Product\ProductRepository;
use wws\Shopware\Api\K11ShopwareProductApi;
use wws\Shopware\EntityRegister\ShopwareEntityRegister;

class ProductDataSync
{
    private RestClient $rest_client;
    private ShopwareEntityRegister $entity_register;
    private ShopwareProductQueueManager $product_queue_manager;
    public K11ShopwareFeatureService $shopware_feature_service;
    private ProductCatSyncWwsToShopware $product_cat_sync_wws_to_shopware;

    private int $filter_list_id;
    private ConfigContainer $config;

    private ProductToK11ShopwareProductOffer $product_offer_sync;

    private K11ShopwareProductApi $product_api;

    private ProductRepository $product_repository;

    public function __construct(
        RestClient $rest_client,
        ShopwareEntityRegister $entity_register,
        ProductToK11ShopwareProductOffer $product_to_k11_shopware_product_offer,
        K11ShopwareProductApi $k11_shopware_product_api,
        K11ShopwareFeatureService $k11_shopware_feature_service,
        ShopwareProductQueueManager $shopware_product_queue_manager,
        ConfigContainer $config,
        ProductCatSyncWwsToShopware $product_cat_sync_wws_to_shopware,
        ProductRepository $product_repository,
    ) {
        $this->rest_client = $rest_client;
        $this->entity_register = $entity_register;

        $this->product_offer_sync = $product_to_k11_shopware_product_offer;
        $this->product_api = $k11_shopware_product_api;

        $this->config = $config;

        $this->filter_list_id = $config->getInt('filter_list_id');

        $this->shopware_feature_service = $k11_shopware_feature_service;

        $this->product_queue_manager = $shopware_product_queue_manager;
        $this->product_repository = $product_repository;

        $this->product_cat_sync_wws_to_shopware = $product_cat_sync_wws_to_shopware;
    }


    public function convertRawShopwareToK11ShopwareProductData(array $api_data): K11ShopwareProductData
    {
        $product_data = new K11ShopwareProductData();

        $product_id = (int)$api_data['productNumber'];
        if (!$product_id) {
            throw new DevException('nope');
        }

        $product_data->setProductId($product_id);
        $product_data->setMpn($api_data['manufacturerNumber'] ?? '');
        $product_data->setEan('');
        if ($api_data['ean'] && input::validateEAN($api_data['ean'])) { //da existieren auch falsche eans
            $product_data->setEan($api_data['ean']);
        }

        $product_data->setProductName($api_data['name']);

        $product_data->setDescription($api_data['description'] ?? '');
        $product_data->setTeaser($api_data['customFields']['product_short_desc'] ?? '');
        $product_data->setMetaTitle($api_data['metaTitle'] ?? '');
        $product_data->setMetaDescription($api_data['metaDescription'] ?? '');

        $product_data->setOrderCodesRaw($api_data['customFields']['order_codes'] ?? '');
        $product_data->setFreeProductPropertiesRaw($api_data['customFields']['product_properties'] ?? '');

        //das ist auch wieder käse. Es ist unklar wie keywords und customSearchKeywords zusammenhängen. Es scheint als wäre keywords irgendwas altes. Ich merge die beiden Felder, und werde dann nur customSearchKeywords bespielen
        $keywords = $api_data['customSearchKeywords'] ?? [];

        $temp = preg_split('~[,;]~', $api_data['keywords'] ?? '');
        foreach ($temp as $keyword) {
            $keywords[] = trim($keyword);
        }

        $keywords = array_unique($keywords);
        $keywords = array_filter($keywords);

        $product_data->setKeywords($keywords);

        $brand_id = $this->entity_register->getEntityId($this->entity_register::ENTITY_PRODUCT_BRAND, $api_data['manufacturerId']);
        $product_data->setBrandId($brand_id);

        $cat_ids = [];
        foreach (($api_data['categoryIds'] ?? []) as $sw_category_id) {
            $cat_ids[] = $this->entity_register->getEntityId($this->entity_register::ENTITY_PRODUCT_CAT, $sw_category_id);
        }

        $product_data->setCatIds($cat_ids);

        $product_data->setWeight($api_data['weight'] ?? 0);
        $product_data->setSizeH($api_data['height'] ?? 0);
        $product_data->setSizeB($api_data['width'] ?? 0);
        $product_data->setSizeT($api_data['length'] ?? 0);


        if ($api_data['properties'] !== null) {
            //bevor ich mir das hier unnötig kompliziert mache mit "propertyIds" zieh ich mir die assoziation einfach direkt
            $features = [];
            foreach ($api_data['properties'] as $property) {
                $feature_id = $this->entity_register->tryGetEntityId($this->entity_register::ENTITY_PRODUCT_FEATURE, $property['groupId']);
                if ($feature_id) {
                    $features[] = ['feature_id' => $feature_id, 'feature_value' => $property['name']];
                } else {
                    debug::dump('unbekannte property group gefunden', $property);
                }
            }

            $product_data->setFeatures($features);
        }

        if (isset($api_data['customFields']['seo_url_override'])) {
            $product_data->setSeoUrlOverride($api_data['customFields']['seo_url_override']);
        }

        return $product_data;
    }

    public function convertWwsToK11ShopwareProductData(Product $product): K11ShopwareProductData
    {
        return current($this->convertMultipleWwsToK11ShopwareProductData([$product]));
    }

    /**
     * @param Product[] $products
     * @return K11ShopwareProductData[]
     */
    public function convertMultipleWwsToK11ShopwareProductData(array $products): array
    {
        $product_data_collection = [];

        $product_ids = array_map(fn(Product $product) => $product->getProductId(), $products);

        $db = db::getInstance();

        $features_raw_collection = $db->query("
            SELECT
                product_feature_cache.product_id,
                product_feature_cache.features
            FROM
                product_feature_cache
            WHERE
                product_feature_cache.product_id IN (" . $db->in($product_ids) . ") AND
                product_feature_cache.product_feature_cache_profile = '" . ProductConst::PRODUCT_FEATURE_CACHE_PROFILE_SHOP . "'
        ")->asSingleArray('product_id');

        foreach ($products as $product) {
            $product_details = $product->getProductDetailDefault();

            $product_data = new K11ShopwareProductData();
            $product_data->setProductId($product->getProductId());
            $product_data->setProductName($product_details->getProductNameLocalized() ?: $product->getProductName());

            $product_data->setMpn($product->getMpn());
            $product_data->setEan($product->getEan());

            $product_data->setDescription($product_details->getDescription());
            $product_data->setTeaser($product_details->getTeaser());
            $product_data->setMetaTitle($product_details->getMetaTitle());
            $product_data->setMetaDescription($product_details->getMetaDescription());

            $keywords = explode("\n", $product_details->getKeywords());
            $keywords = array_map(fn($x) => trim($x), $keywords);
            $keywords = array_filter($keywords);

            $product_data->setKeywords($keywords);

            $free_product_properties_raw = $product_details->getFeaturesUnboundRaw() ?? '';

            //MPN mit in die features packen (keine lust das Template in Shopware anzupassen / ist so oder so intransparent)
            if ($product->getMpn()) {
                $free_product_properties = [];
                if ($free_product_properties_raw) {
                    $free_product_properties = Json::decode($free_product_properties_raw);
                }

                array_unshift($free_product_properties, ['name' => 'Ersatzteilenummer', 'value' => $product->getMpn()]);
                $free_product_properties_raw = Json::encode($free_product_properties);
            }
            //

            $product_data->setFreeProductPropertiesRaw($free_product_properties_raw);

            $product_data->setBrandId($product->getBrandId());

            $cat_ids = [$product->getCatId(), ...$product->getExtendedCatIds()];
            $cat_ids = array_filter($cat_ids, fn($x) => $this->isRelevantCatId($x));

            $product_data->setCatIds($cat_ids);

            $product_data->setWeight($product->getGewicht());
            $product_data->setSizeH($product->getSizeH());
            $product_data->setSizeB($product->getSizeB());
            $product_data->setSizeT($product->getSizeT());

            $order_codes = $this->product_repository->getOrderCodes($product->getProductId());
            $json = [];
            foreach ($order_codes as $order_code) {
                $json[] = ['vertreiber' => $order_code['distributor'], 'bestellcode' => $order_code['order_code']];
            }
            $product_data->setOrderCodesRaw(json_encode($json));

            //
            $product_details_google = $product->getProductDetail(ProductConst::PRODUCT_LANG_GOOGLE);

            $product_data->setGoogleDescription($product_details_google->getDescription());
            $product_data->setGoogleDetails($product_details_google->getExtraKey('google_details', []));
            $product_data->setGoogleHighlights($product_details_google->getExtraKey('google_highlights', []));

            //
            $product_data->setFeatures([]);

            $features_raw = $features_raw_collection[$product->getProductId()] ?? null;

            if ($features_raw) {
                $features = ProductFeatureCache::unserializeFlatten($features_raw);

                foreach ($features as $feature) {
                    if (trim($feature['feature_value_format'] ?? '') === '') {
                        continue;
                    }

                    //rumgepfusche... das wws unterstützt mehrere werte nur bei "enum multi". für den übergang machen wir die werte erstmal alle flach
                    $values = explode(' | ', $feature['feature_value_format']);
                    foreach ($values as $value) {
                        $product_data->addFeature($feature['feature_id'], $value);
                    }
                }
            }

            $product_data->setSeoUrlOverride($product->getProductShopUrl());

            $product_data_collection[$product->getProductId()] = $product_data;
        }

        return $product_data_collection;
    }

    private function getCurrentSwProductState(array $product_ids): array
    {
        if (!$product_ids) {
            throw new DevException('$product_ids is empty');
        }

        $parameters = [
            'filter' => [
                [
                    'type' => 'multi',
                    'operator' => 'and',
                    'queries' => [
                        ['type' => 'equalsAny', 'field' => 'productNumber', 'value' => $product_ids]
                    ]
                ]
            ],
            'includes' => [
                'product' => ['id', 'productNumber', 'categoryIds', 'propertyIds']
            ]
        ];

        $request = new Request(
            'POST',
            'search/product',
            $this->rest_client->getHeaders(),
            json_encode($parameters)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        $current_sw_state_list = [];

        foreach ($result['data'] as $row) {
            $current_sw_state_list[$row['productNumber']] = [
                'product_id' => $row['productNumber'],
                'sw_product_id' => $row['id'],
                'sw_cat_ids' => $row['categoryIds'] ?? [],
                'property_ids' => $row['propertyIds'] ?? [],
            ];
        }

        foreach ($product_ids as $product_id) {
            if (!isset($current_sw_state_list[$product_id])) {
                $current_sw_state_list[$product_id] = [
                    'product_id' => $product_id,
                    'sw_product_id' => null,
                    'sw_cat_ids' => [],
                    'property_ids' => []
                ];
            }
        }

        return $current_sw_state_list;
    }

    public function syncProductIdsToShopware(array $product_ids): void
    {
        //wir gehen über den normalen entityQueueConsumer, da steckt etwas mehr Logik dahinter.
        $collection = new EntityQueueRunnerCollection();
        foreach ($product_ids as $product_id) {
            $collection->addEntityId((string)$product_id, 0);
        }

        $this->entityQueueConsumer($collection);
    }

    /**
     * @param K11ShopwareProductData[] $k11_shopware_product_data_collection
     * @return void
     */
    public function syncProductDataToShopware(array $k11_shopware_product_data_collection): void
    {
        $product_ids = array_map(fn($x) => $x->getProductId(), $k11_shopware_product_data_collection);

        $current_sw_state_list = $this->getCurrentSwProductState($product_ids);

        $requests_collection = [
            'product' => null,
            'product_category_delete' => [],
            'product_property_delete' => []
        ];

        foreach ($k11_shopware_product_data_collection as $k11_shopware_product_data) {
            $current_sw_state = $current_sw_state_list[$k11_shopware_product_data->getProductId()];

            $requests = $this->buildProductDataShopwareRequests($k11_shopware_product_data, $current_sw_state['sw_product_id'], $current_sw_state['sw_cat_ids'], $current_sw_state['property_ids']);


            if ($requests['product_update']) {
                $requests_collection['product'][] = $requests['product_update'];
            }
            if ($requests['product_create']) {
                $requests_collection['product'][] = $requests['product_create'];
            }
            if ($requests['product_category_delete']) {
                $requests_collection['product_category_delete'] = array_merge($requests_collection['product_category_delete'], $requests['product_category_delete']);
            }
            if ($requests['product_property_delete']) {
                $requests_collection['product_property_delete'] = array_merge($requests_collection['product_property_delete'], $requests['product_property_delete']);
            }
        }

        $bulk_request_data = [];

        if ($requests_collection['product']) {
            $bulk_request_data['product'] = [
                'entity' => 'product',
                'action' => 'upsert',
                'payload' => $requests_collection['product']
            ];
        }

        if ($requests_collection['product_category_delete']) {
            $bulk_request_data['product_category_delete'] = [
                'entity' => 'product_category',
                'action' => 'delete',
                'payload' => $requests_collection['product_category_delete']
            ];
        }

        if ($requests_collection['product_property_delete']) {
            $bulk_request_data['product_property_delete'] = [
                'entity' => 'product_property',
                'action' => 'delete',
                'payload' => $requests_collection['product_property_delete']
            ];
        }

        $request = new Request(
            'POST',
            '_action/sync',
            $this->rest_client->getHeaders(true),
            json_encode($bulk_request_data)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        if (!$result['success']) {
            debug::extendXdebug();
            debug::dump($result);
            throw new DevException('shopware sync failed. anschauen, wie, wo, was.');
        }
    }


    public function buildProductDataShopwareRequests(K11ShopwareProductData $product_data, ?string $sw_product_id, array $org_sw_cat_ids, array $org_property_ids): array
    {
        $create_sw_product = $sw_product_id === null;

        if ($create_sw_product) {
            $sw_product_id = md5('product' . $product_data->getProductId());
        }

        $requests = [
            'product_update' => null, //ich lasse hier mal die unterscheidung zwischen update und create... die bulk api kennt nur ein upsert, die rest version nur patch und post
            'product_create' => null,
            'product_category_delete' => [],
            'product_property_create' => [],
            'product_property_delete' => []
        ];

        $dst_sw_cat_ids = [];

        $sw_manufacturer_id = $this->entity_register->getShopwareId($this->entity_register::ENTITY_PRODUCT_BRAND, $product_data->getBrandId());
        $sw_categories = [];
        foreach ($product_data->getCatIds() as $cat_id) {
            $sw_cat_id = $this->entity_register->getShopwareId($this->entity_register::ENTITY_PRODUCT_CAT, $cat_id);
            $dst_sw_cat_ids[] = $sw_cat_id;

            if (!in_array($sw_cat_id, $org_sw_cat_ids)) {
                $sw_categories[] = ['id' => $sw_cat_id];
            }
        }

        $google_shopping = [
            'id' => $sw_product_id,
            'product_id' => $sw_product_id,
            'description' => $product_data->getGoogleDescription(),
            'highlights' => $product_data->getGoogleHighlights(),
            'details' => $product_data->getGoogleDetails(),
        ];

        $custom_fields = [];
        $custom_fields['product_short_desc'] = $product_data->getTeaser();
        $custom_fields['order_codes'] = $product_data->getOrderCodesRaw();
        $custom_fields['product_properties'] = $product_data->getFreeProductPropertiesRaw();

        $seo_url_override = $product_data->getSeoUrlOverride();
        if ($seo_url_override) {
            $custom_fields['seo_url_override'] = $seo_url_override;
        }

        $sw_data_struct = [
            'id' => $sw_product_id,
            'productNumber' => (string)$product_data->getProductId(),
            'manufacturerNumber' => $product_data->getMpn(),
            'ean' => $product_data->getEan(),
            'name' => $product_data->getProductName(),
            'description' => $product_data->getDescription(),
            'metaTitle' => $product_data->getMetaTitle(),
            'metaDescription' => $product_data->getMetaDescription(),
            'keywords' => implode('; ', $product_data->getKeywords()),
            'customSearchKeywords' => $product_data->getKeywords(),
            'manufacturerId' => $sw_manufacturer_id,
            'weight' => $product_data->getWeight(),
            'width' => $product_data->getSizeB(),
            'height' => $product_data->getSizeH(),
            'length' => $product_data->getSizeT(),
            'customFields' => $custom_fields,
            'productGoogleShopping' => $google_shopping,
        ];

        if ($sw_categories) { //nur setzen, wenn es Änderungen gab
            $sw_data_struct['categories'] = $sw_categories;
        }

        if (!$create_sw_product) {
            $requests['product_update'] = $sw_data_struct;
        } else {
            //fürs anlegen sind daten aus den angebots daten erforderlich
            //@todo mal sauber auslagern
            $product = new Product($product_data->getProductId());
            $offer_data = $this->product_offer_sync->convertProduct($product);
            $raw_data = $this->product_api->convertK11ShopwareProductOffer($offer_data, $sw_product_id);
            //

            //mergen -> wäre hier ein generischer deep merge besser?!
            if (isset($raw_data['customFields']) && isset($sw_data_struct['customFields'])) {
                $sw_data_struct['customFields'] = array_merge($sw_data_struct['customFields'], $raw_data['customFields']);
            }

            $sw_data_struct = array_merge($raw_data, $sw_data_struct);

            //beim Anlegen müssen wir die Sichtbarkeit mitgeben
            $sw_data_struct['visibilities'] = [
                [
                    "visibility" => 30,
                    "salesChannelId" => "ec46d71abadf482c907cd9c79f8a4bdc"
                ]
            ];

            $requests['product_create'] = $sw_data_struct;
        }


        //ok... das ist auch wieder nervig.
        //categoryIds darf ich nicht schreiben, ich muss über categories gehen.
        //das ist eine association... da added shopware nur, aber löscht nicht.
        //löschen muss in so einem fall explizit passieren.
        if ($org_sw_cat_ids) {
            $delete_sw_cat_ids = array_diff($org_sw_cat_ids, $dst_sw_cat_ids);

            if ($delete_sw_cat_ids) {
                foreach ($delete_sw_cat_ids as $sw_cat_id) {
                    $requests['product_category_delete'][] = [
                        'productId' => $sw_product_id,
                        'categoryId' => $sw_cat_id
                    ];
                }
            }
        }

        //features
        $features = $product_data->getFeatures();

        $properties_new = [];
        $properties_delete = array_combine($org_property_ids, $org_property_ids);

        foreach ($features as $feature) {
            $property_group_id = $this->shopware_feature_service->getPropertyGroupIdByFeatureId($feature['feature_id']);
            $property_group_option_id = $this->shopware_feature_service->getPropertyGroupOptionId($property_group_id, $feature['feature_value']);

            if (isset($properties_delete[$property_group_option_id])) {
                unset($properties_delete[$property_group_option_id]);
            } else {
                $properties_new[] = [
                    'id' => $property_group_option_id,
                    'groupId' => $property_group_id
                ];
            }
        }

        if ($properties_new) {
            if ($requests['product_update']) {
                $requests['product_update']['properties'] = $properties_new;
            } else {
                $requests['product_create']['properties'] = $properties_new;
            }
        }

        $properties_delete = $this->shopware_feature_service->filterPropertyGroupOptionIdsForUnknownGroups($properties_delete);

        if ($properties_delete) {
            foreach ($properties_delete as $property_group_option_id) {
                $requests['product_property_delete'][] = [
                    'productId' => $sw_product_id,
                    'optionId' => $property_group_option_id
                ];
            }
        }

        return $requests;
    }


    /**
     * Übernimmt die Daten aus einer K11ShopwareProductData in das Produkt
     *
     * Product wird nicht automatisch gespeichert!
     *
     * ACHTUNG: das ist eher "destruktiv" setProductNameLocalized() setDescriptionSource()
     *
     * @param Product $product
     * @param K11ShopwareProductData $product_data
     * @return void
     * @throws FatalException
     */
    public function syncShopwareProductDataToProduct(Product $product, K11ShopwareProductData $product_data): void
    {
        $product->setMpn($product_data->getMpn());

        if (input::validateEAN($product_data->getEan())) { //da existieren auch falsche eans
            $product->setEan($product_data->getEan());
        }

        $product_details = $product->getProductDetailDefault();

        //
        if ($product_details->getProductNameLocalized()) {
            $product_details->setProductNameLocalized($product_data->getProductName());

            if ($product_details->getProductNameLocalized() === $product->getProductName()) {
                $product_details->setProductNameLocalized('');
            }
        } else {
            //vorerst für den übergang -> wobei das eigentlich alles nur für den übergang ist.
            if ($product_data->getProductName() !== $product->getProductName()) {
                $product_details->setProductNameLocalized($product_data->getProductName());
            }
        }

        $product_details->setDescription($product_data->getDescription());

        $product_details->setDescriptionSource(ProductConst::PRODUCT_DATA_SOURCE_SHOPWARE); //destruktiv!

        $product_details->setTeaser($product_data->getTeaser());

        $product_details->setMetaTitle($product_data->getMetaTitle());
        $product_details->setMetaDescription($product_data->getMetaDescription());
        $product_details->setKeywords(implode("\r\n", $product_data->getKeywords()));

        $product->setBrandId($product_data->getBrandId());

        $this->setCatIds($product, $product_data->getCatIds());

        $product->setGewicht($product->getGewicht());
        $product->setSizeH($product->getSizeH());
        $product->setSizeB($product->getSizeB());
        $product->setSizeT($product->getSizeT());

        //spiel aber eigentlich keine rolle... das wird ja auch defintiv nicht in shopware bearbeitet.
        //@todo unstrukturierte features
        //@todo order_codes

        if ($product_data->getFeatures()) {
            $raw_features = $product_data->getFeatures();

            //
            $feature_grouped = [];
            foreach ($raw_features as $feature) {
                if (!isset($feature_grouped[$feature['feature_id']])) {
                    $feature_grouped[$feature['feature_id']] = [];
                }
                $feature_grouped[$feature['feature_id']][] = $feature['feature_value'];
            }
            //
            $feature_container = $product->getFeatureContainer();

            foreach ($feature_grouped as $feature_id => $feature_value) {
                try {
                    $feature_value_obj = $feature_container->getFeatureValue($feature_id);
                } catch (SmartDataEntityNotFoundException) {
                    //die wahrscheinlichkeit ist hoch, dass die feature_group nicht dem produkt zugeordnet ist... blind hinzufügen sollte schon ausriechen.
                    $feature_container->addFeatureGroupId(ProductDataProvider::getInstance()->getFeatureGroupIdByFeatureId($feature_id));
                    $feature_value_obj = $feature_container->getFeatureValue($feature_id);
                }

                $feature_value_obj->setValue(implode(' | ', $feature_value));
                $feature_value_obj->setHide(false);
            }
        }
    }


    public function syncShopwareToProduct(int $product_id): void
    {
        $this->syncShopwareToProducts([$product_id]);
    }

    public function syncShopwareToProducts(array $product_ids): void
    {
        $products = service_loader::get(ProductRepository::class)->loadProducts($product_ids);

        $k11_product_datas = $this->tryLoadProductsFromShopware($product_ids);

        foreach ($k11_product_datas as $k11_product_data) {
            $product = $products[$k11_product_data->getProductId()];

            $this->syncShopwareProductDataToProduct($product, $k11_product_data);

            $product->save();
        }
    }


    /**
     * @param int[] $product_ids
     * @return K11ShopwareProductData[]
     */
    public function loadProductsFromShopware(array $product_ids): array
    {
        $result = [];
        foreach ($product_ids as $product_id) {
            $result[$product_id] = null;
        }

        $found_entities = $this->tryLoadProductsFromShopware($product_ids);
        foreach ($found_entities as $entity) {
            $result[$entity->getProductId()] = $entity;
        }

        foreach ($result as $product_id => $product_data) {
            if ($product_data === null) {
                throw new ProductNotFound('product not found in shopware: ' . $product_id);
            }
        }

        return $result;
    }


    /**
     * @param int[] $product_ids
     * @return K11ShopwareProductData[]
     */
    public function tryLoadProductsFromShopware(array $product_ids): array
    {
        $parameters = [
            'filter' => [
                [
                    'type' => 'multi',
                    'operator' => 'and',
                    'queries' => [
                        ['type' => 'equalsAny', 'field' => 'productNumber', 'value' => $product_ids]
                    ]
                ]
            ],
            'includes' => [
                //'product' => ['id', 'productNumber', 'active', 'price', 'stock', 'customFields', 'deliveryTimeId', 'prices', 'isCloseout',],
            ],
            'associations' => [
                'properties' => [] //vorerst für shopware -> wws
            ],
            'sort' => [
                ['field' => 'id', 'order' => 'ASC']
            ]
        ];

        $request = new Request(
            'POST',
            'search/product',
            $this->rest_client->getHeaders(),
            json_encode($parameters)
        );

        $response = $this->rest_client->sendRequest($request);
        $result = Json::decode((string)$response->getBody());

        $product_data_results = [];

        foreach ($result['data'] as $row) {
            $product_data_results[] = $this->convertRawShopwareToK11ShopwareProductData($row);
        }

        return $product_data_results;
    }


    /**
     * @param Product $product
     * @param int[] $cat_ids
     * @return void
     */
    private function setCatIds(Product $product, array $cat_ids): void
    {
        $existing_cat_ids = $product->getExtendedCatIds();

        foreach ($existing_cat_ids as $cat_id) {
            $is_already_set = in_array($cat_id, $cat_ids);

            if (!$is_already_set && $this->isRelevantCatId($cat_id)) {
                $product->removeCatId($cat_id);
            }
        }

        foreach ($cat_ids as $cat_id) {
            $product->addCatId($cat_id);
        }
    }

    private function isRelevantCatId(int $cat_id): bool
    {
        return $this->product_cat_sync_wws_to_shopware->isRelevantCatId($cat_id);
    }

    private function getFilterListResult(int $product_id): bool
    {
        return (bool)db::getInstance()->fieldQuery("
            SELECT
                product_filter_list_results.status
            FROM
                product_filter_list_results
            WHERE
                product_filter_list_results.product_id = " . $product_id . " AND
                product_filter_list_results.filter_list_id = " . $this->filter_list_id . "
        ");
    }

    public function entityQueueConsumer(EntityQueueRunnerCollection $collection): void
    {
        $collection->setExecutionDate(new DateObj());

        $product_ids = $collection->getEntityIds();

        $config = $this->product_repository->getDefaultLoadConfig();
        $config->setFeatureValues(true);
        $config->setFeatureGroupIds(true);
        $config->setExtendedCats(true);

        $products = $this->product_repository->loadProducts($product_ids, $config);

        $sw_product_ids = $this->entity_register->tryGetShopwareIds($this->entity_register::ENTITY_PRODUCT, $product_ids);
        $product_data_collection = $this->convertMultipleWwsToK11ShopwareProductData($products);

        $new_product_ids = [];

        //$product_data_collection filtern -> produkte die nicht in shopware existieren und in der filterliste gesperrt sind rauswerfen
        foreach ($products as $product) {
            $sw_product_id = $sw_product_ids[$product->getProductId()] ?? null;

            if (!$sw_product_id) {
                //produkt gibts noch nicht in shopware -> wenn in der filterliste gesperrt nicht in shopware anlegen
                $filter_list_status = $this->getFilterListResult($product->getProductId());
                if (!$filter_list_status) {
                    unset($product_data_collection[$product->getProductId()]);
                    continue;
                }

                $new_product_ids[] = $product->getProductId();
            }
        }

        if (!$product_data_collection) {
            $collection->markAllAsProcessed();
            return;
        }

        $this->syncProductDataToShopware($product_data_collection);

        if ($new_product_ids) {
            $result = $this->getCurrentSwProductState($new_product_ids);
            foreach ($result as $row) {
                $this->entity_register->setShopwareId($this->entity_register::ENTITY_PRODUCT, $row['product_id'], $row['sw_product_id']);
            }

            $this->product_queue_manager->newProductsCreatedInShopware($new_product_ids);
        }

        $collection->markAllAsProcessed();

        //Shopware geht auf dem Zahnfleisch, wenn da mal ein paar mehr Produkte angelegt werden... man!
        //wir geben Shopware eine Gedenkpause, wenn ein paar mehr Produkte gesynct werden.
        //->explizit hier drin, weil von außen nicht erkennbar ist, was gemacht wurde. Das würde den sync unnötigerweise verzögern.
        if (count($product_ids) > 150) {
            sleep(120);
        }
    }
}
