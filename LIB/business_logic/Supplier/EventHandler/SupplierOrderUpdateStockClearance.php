<?php

namespace wws\Supplier\EventHandler;

use wws\Supplier\Event\EventSupplierOrderPreSave;
use wws\Supplier\StockClearance;

class SupplierOrderUpdateStockClearance
{

    private StockClearance $stock_clearance;

    public function __construct(StockClearance $stock_clearance)
    {
        $this->stock_clearance = $stock_clearance;
    }

    public function handleEvent(EventSupplierOrderPreSave $event): void
    {
        $this->stock_clearance->setStockClearance($event->getSupplierOrder());
    }
}
