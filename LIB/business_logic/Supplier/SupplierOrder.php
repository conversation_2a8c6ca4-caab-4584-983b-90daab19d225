<?php

namespace wws\Supplier;

use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Exceptions\FatalException;
use bqp\Model\EntityChanges;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Model\SmartDataObj;
use cascading_agg_query;
use db;
use DomainException;
use env;
use Exception;
use order_repository;
use service_loader;
use wws\business_structure\Shop;
use wws\Lager\Lager;
use wws\Lager\LagerRepository;
use wws\Lager\WareneingangLieferscheinRepository;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Order\OrderRepository;
use wws\Product\ProductLager;
use wws\ProductStock\ProductStockConst;
use wws\Supplier\Event\EventSupplierOrderBestellt;
use wws\Supplier\Event\EventSupplierOrderChange;
use wws\Supplier\Event\EventSupplierOrderPreSave;
use wws\Supplier\Event\EventSupplierOrderStatusChange;
use wws\Supplier\Event\EventSupplierOrderStorniert;

class SupplierOrder
{
    public const STATUS_ENTWURF = 'entwurf';
    public const STATUS_ANFRAGE = 'anfrage';
    public const STATUS_BESTELLT = 'bestellt';
    public const STATUS_UNTERWEGS = 'unterwegs';
    public const STATUS_ERLEDIGT = 'erledigt';
    public const STATUS_STORNIERT = 'storniert';

    public const TRANSFER_STATUS_OPEN = 'open';
    public const TRANSFER_STATUS_QUEUE = 'queue';
    public const TRANSFER_STATUS_SUCCESS = 'success';
    public const TRANSFER_STATUS_FAIL = 'fail';


    protected SmartDataObj $daten;

    /**
     * @var SupplierOrderItem[]
     */
    protected array $items = [];

    protected ?int $old_lager_id = null;

    private int $shop_id;

    public function __construct(?int $supplier_order_id = null)
    {
        $this->daten = new SmartDataObj($this);

        if ($supplier_order_id !== null) {
            $this->load($supplier_order_id);
        } else {
            $this->loadDefaults();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="Laden/Speichern">
    public function save(): int
    {
        service_loader::getEventDispatcher()->dispatch(new EventSupplierOrderPreSave($this, $this->getAllChanges()));

        if (!$this->getUserId()) {
            $this->setUserId(env::getUserId());
        }

        $changes = $this->getAllChanges();

        //wenn die Bestellung auf bestellt gesetzt wird, alle Positionen bei den "anzahl_original" noch nicht gesetzt ist, mit der aktuellen Anzahl initialisieren
        //->das ist nicht 100%, weil derzeit der Status wechsel nicht unbedingt gleich das absenden der Bestellungen ist. Reicht aber vorerst.
        if ($changes->isChange('stamm.status') && $this->getStatus() === self::STATUS_BESTELLT) {
            foreach ($this->getItems() as $item) {
                if (!$item->getAnzahlOriginal()) {
                    $item->setAnzahlOriginal($item->getAnzahl());
                }
            }
        }

        if ($changes->isChange(['stamm.status', 'item.anzahl', 'item.anzahl_ist'])) {
            $this->checkIstBestand();
            $changes = $this->getAllChanges();
        }

        $this->saveStamm();
        $this->saveItems();

        if ($changes->isChange(['stamm.status', 'stamm.lager_id', 'stamm.product_id', 'item.anzahl', 'item.anzahl_ist', 'stamm.stock_clearance'])) {
            $this->calcOrdersForLager();
        }

        $changes->setEntityId($this->getSupplierOrderId());
        $changes->save();

        if ($changes->isChange()) {
            $event_dispatcher = service_loader::getEventDispatcher();
            $event_dispatcher->dispatch(new EventSupplierOrderChange($this, $changes));
        }

        if ($changes->isChange('stamm.status')) {
            $change = $changes->getChange('stamm.status');

            $event_dispatcher = service_loader::getEventDispatcher();

            if ($change['new_value'] === self::STATUS_BESTELLT) {
                $event_dispatcher->dispatch(new EventSupplierOrderBestellt($this));
            } elseif ($change['new_value'] === self::STATUS_STORNIERT) {
                $event_dispatcher->dispatch(new EventSupplierOrderStorniert($this));
            }

            $event_dispatcher->dispatch(new EventSupplierOrderStatusChange($this, $change['new_value'], $change['old_value'] ?: null));
        }

        return $this->getSupplierOrderId();
    }

    private function saveStamm(): void
    {
        if (!$this->daten->isChange()) {
            return;
        }

        $changes = $this->daten->getChanges();

        $db = db::getInstance();

        $sql = [];
        foreach ($changes as $field => $value) {
            $sql[] = "supplier_order.$field = '" . $db->escape($value) . "'";
        }

        switch ($this->daten->getObjStatus()) {
            case SmartDataObj::STATUS_NEW:
                $db->query("
                    INSERT INTO
                        supplier_order
                    SET
                        " . implode(',', $sql) . "
                ");

                $supplier_order_id = $db->insert_id();
                $this->daten->setterDirect('supplier_order_id', $supplier_order_id);

                $bestell_nr = $this->createBestellNr();
                $this->daten->setterDirect('bestell_nr', $bestell_nr);

                $db->query("
                    UPDATE
                        supplier_order
                    SET
                        supplier_order.bestell_nr = '" . $db->escape($bestell_nr) . "'
                    WHERE
                        supplier_order.supplier_order_id = " . $this->getSupplierOrderId() . "
                ");
                break;
            case SmartDataObj::STATUS_UPDATE:
                $db->query("
                    UPDATE
                        supplier_order
                    SET
                        " . implode(',', $sql) . "
                    WHERE
                        supplier_order.supplier_order_id = '" . $this->getSupplierOrderId() . "'
                ");
                break;
        }

        $this->daten->setSaved();
    }

    private function saveItems(): void
    {
        $db = db::getInstance();

        foreach ($this->items as $item) {
            $daten = $item->getSmartDataObj();

            if ($daten->getObjStatus() === SmartDataObj::STATUS_NEW) {
                $item->setSupplierOrderId($this->getSupplierOrderId());
            }

            if ($daten->getObjStatus() === SmartDataObj::STATUS_LOADED) {
                continue;
            }

            $sql = [];

            $order_item_ids = [];

            foreach ($daten->getChanges() as $field => $value) {
                switch ($field) {
                    case 'order_item_ids':
                        $order_item_ids = $value;
                        break;
                    case 'delivery_date':
                        $sql[] = "supplier_order_items.$field = " . $db->quote($value);
                        break;
                    default:
                        $sql[] = "supplier_order_items.$field = '" . $db->escape($value) . "'";
                }
            }

            switch ($daten->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    $db->query("
                        INSERT INTO
                            supplier_order_items
                        SET
                            " . implode(',', $sql) . "
                    ");

                    $daten->setterDirect('bestell_item_id', (int)$db->insert_id());
                    $daten->setSaved();
                    break;
                case SmartDataObj::STATUS_UPDATE:
                    $db->query("
                        UPDATE
                            supplier_order_items
                        SET
                            " . implode(',', $sql) . "
                        WHERE
                            supplier_order_items.bestell_item_id = '" . $item->getBestellItemId() . "' AND
                            supplier_order_items.supplier_order_id = '" . $this->getSupplierOrderId() . "'
                    ");
                    $daten->setSaved();
                    break;
                case SmartDataObj::STATUS_DEL:
                    $db->query("
                        DELETE FROM
                            supplier_order_items
                        WHERE
                            supplier_order_items.bestell_item_id = '" . $item->getBestellItemId() . "' AND
                            supplier_order_items.supplier_order_id = '" . $this->getSupplierOrderId() . "'
                    ");
                    break;
            }

            if ($order_item_ids) {
                foreach ($order_item_ids as $order_item_id) {
                    $db->query("
                            INSERT IGNORE INTO
                                supplier_order_order_item_ids
                            SET
                                supplier_order_order_item_ids.supplier_order_id = '" . $this->getSupplierOrderId() . "',
                                supplier_order_order_item_ids.bestell_item_id = '" . $item->getBestellItemId() . "',
                                supplier_order_order_item_ids.order_item_id = '" . (int)$order_item_id . "'
                    ");
                }
            }
        }
    }

    /**
     * @param int $supplier_order_id
     * @thorws SmartDataEntityNotFoundException
     */
    public function load(int $supplier_order_id): void
    {
        $db = db::getInstance();

        $daten = $db->singleQuery("
            SELECT
                supplier_order.supplier_order_id,
                supplier_order.bestell_nr,
                supplier_order.supplier_id,
                supplier_order.user_id,
                supplier_order.status,
                supplier_order.transfer_status,                
                supplier_order.delivery_date,
                supplier_order.delivery_date_min,
                supplier_order.bemerkung,
                supplier_order.lager_id,
                supplier_order.order_id,

                supplier_order.datum_anfrage,
                supplier_order.datum_bestellt,
                supplier_order.datum_erledigt,
                supplier_order.datum_tracking,

                supplier_order.kundennr,
                
                supplier_order.externe_referenz_1,
                supplier_order.externe_referenz_2,
                supplier_order.shipping_costs_net_expected,
                
                supplier_order.supplier_order_tags,
                                
                supplier_konten.mandant AS shop_id
            FROM
                supplier_order LEFT JOIN
                supplier_konten ON (supplier_order.supplier_id = supplier_konten.supplier_id AND supplier_order.kundennr = supplier_konten.kundennr)
            WHERE
                supplier_order.supplier_order_id = '$supplier_order_id'
        ");

        if (!$daten) {
            throw new SmartDataEntityNotFoundException('Lieferantenbestellung nicht gefunden. (' . $supplier_order_id . ')');
        }

        if (!$daten['shop_id']) {
            $this->shop_id = env::getDefaultMigrationShopId();
        } else {
            $this->shop_id = (int)$daten['shop_id'];
            unset($daten['shop_id']);
        }

        $this->daten->loadDaten($daten);

        $query = new cascading_agg_query("
            SELECT
                supplier_order_items.supplier_order_id,
                supplier_order_items.bestell_item_id,
                supplier_order_items.product_id,
                supplier_order_items.product_name,
                supplier_order_items.gros_product_id,
                supplier_order_items.delivery_date,
                supplier_order_items.anzahl,
                supplier_order_items.anzahl_original,
                supplier_order_items.anzahl_ist,
                supplier_order_items.ek_netto,
                supplier_order_items.item_bemerkung,
                supplier_order_items.mvsrc_availability_id,
                supplier_order_items.availability_info,
                [
                    supplier_order_order_item_ids.order_item_id AS order_item_id
                ] AS order_item_ids
            FROM
                supplier_order_items LEFT JOIN
                supplier_order_order_item_ids ON (
                    supplier_order_items.supplier_order_id = supplier_order_order_item_ids.supplier_order_id AND
                    supplier_order_items.bestell_item_id = supplier_order_order_item_ids.bestell_item_id
                )
            WHERE
                supplier_order_items.supplier_order_id = '$supplier_order_id'
            GROUP BY
                supplier_order_items.bestell_item_id
        ");
        $query->setEmptyArray(cascading_agg_query::EMPTY_ARRAY);

        $result = $db->execute($query);

        foreach ($result as $daten) {
            $daten['order_item_ids'] = $daten['order_item_ids'] ? array_column($daten['order_item_ids'], 'order_item_id') : [];

            $supploer_order_item = new SupplierOrderItem($daten);
            $supploer_order_item->setSupplierOrder($this);
            $this->items[] = $supploer_order_item;
        }
    }

    public function loadDefaults(): void
    {
        $this->setLagerId(ProductStockConst::LAGER_ID_LAGER);
        $this->setStatus(self::STATUS_ENTWURF);
        $this->setTransferStatus(self::TRANSFER_STATUS_OPEN);
        $this->setShippingCostsNetExpected(null);
        $this->setStockClearance(true);

        $this->daten->setter('bestell_nr', '');

        $date = new DateObj();
        $date->addWerktage(2);
        $this->setDeliveryDate($date);

        $this->setDatumTracking(null);

        //@todo...
        $invalid_date = DateObj::invalid()->db();
        $this->daten->setter('datum_anfrage', $invalid_date);
        $this->daten->setter('datum_bestellt', $invalid_date);
        $this->daten->setter('datum_erledigt', $invalid_date);
    }


    public function getAllChanges(): EntityChanges
    {
        $entity_changes = new EntityChanges('supplier_order');

        $changes = $this->daten->getChanges(SmartDataObj::CHANGES_BOTH_VALUES);

        foreach ($changes as $key => $daten) {
            $entity_changes->add($key, $daten['new'], $daten['old'], 'stamm');
        }

        foreach ($this->items as $item) {
            $changes = $item->getSmartDataObj()->getChanges(SmartDataObj::CHANGES_BOTH_VALUES);

            switch ($item->getSmartDataObj()->getObjStatus()) {
                case SmartDataObj::STATUS_NEW:
                    $entity_changes->addNew('item', $item);
                    break;
                case SmartDataObj::STATUS_DEL:
                    $entity_changes->addRemove('item', $item);
                    break;
            }

            foreach ($changes as $key => $daten) {
                $entity_changes->add($key, $daten['new'], $daten['old'], 'item', $item);
            }
        }

        return $entity_changes;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Getter/Setter">
    /**
     * @param $supplier_order_id
     * @return bool
     */
    public function setSupplierOrderId(int $supplier_order_id): bool
    {
        return $this->daten->setter('supplier_order_id', $supplier_order_id);
    }

    /**
     * @param $supplier_id
     * @return bool
     */
    public function setSupplierId($supplier_id): bool
    {
        return $this->daten->setter('supplier_id', $supplier_id);
    }

    public function setSupplierIdWithDefaults(int $supplier_id, ?int $shop_id = null): bool
    {
        $state = $this->setSupplierId($supplier_id);
        $this->setKundennr(SupplierOrderRepository::getDefaultKundennrForSupplierId($supplier_id, $shop_id));

        //zum testen... -> lieferanten im idealfall eigentlich als versandkostenfrei in db markieren (lager vs fulfillment -> wobei fulfillment von außen gesteuert wird)
        if ($supplier_id === SuppliersConst::SUPPLIER_ID_GAEFGEN) {
            $this->setShippingCostsNetExpected(0);
        }

        return $state;
    }

    /**
     * @param $user_id
     * @return bool
     */
    public function setUserId($user_id): bool
    {
        return $this->daten->setter('user_id', $user_id);
    }

    /**
     * @param string $status
     * @return bool
     */
    public function setStatus(string $status): bool
    {
        $changed = $this->daten->setter('status', $status);

        if ($changed && $status === self::STATUS_ERLEDIGT) {
            $this->setDatumErledigt(new DateObj());
        }

        if ($changed && $status === self::STATUS_BESTELLT) {
            $this->setDatumBestellt(new DateObj());
        }

        return $changed;
    }

    /**
     * @param DateObj $datum_anfrage
     * @return bool
     */
    public function setDatumAnfrage(DateObj $datum_anfrage): bool
    {
        return $this->daten->setter('datum_anfrage', $datum_anfrage->db());
    }

    /**
     * @param DateObj $datum_bestellt
     * @return bool
     */
    public function setDatumBestellt(DateObj $datum_bestellt): bool
    {
        return $this->daten->setter('datum_bestellt', $datum_bestellt->db());
    }

    /**
     * @param DateObj $datum_erledigt
     * @return bool
     */
    public function setDatumErledigt(DateObj $datum_erledigt): bool
    {
        return $this->daten->setter('datum_erledigt', $datum_erledigt->db());
    }

    /**
     * @param DateObj|null $datum_tracking
     * @return bool
     */
    public function setDatumTracking(?DateObj $datum_tracking): bool
    {
        if ($datum_tracking === null) {
            return $this->daten->setter('datum_tracking', null);
        }
        return $this->daten->setter('datum_tracking', $datum_tracking->db());
    }

    public function setDeliveryDate(DateObj $delivery_date): bool
    {
        $state = $this->daten->setter('delivery_date', $delivery_date->db('date'));

        $this->calcDeliveryDateMin();

        return $state;
    }

    public function setBemerkung(string $bemerkung): bool
    {
        return $this->daten->setter('bemerkung', $bemerkung);
    }

    /**
     * setzt das Lager an das ggf. geliefert werden soll
     * @param int $lager_id
     * @return bool
     */
    public function setLagerId(int $lager_id): bool
    {
        $old_lager_id = $this->getLagerId();

        $status = $this->daten->setter('lager_id', $lager_id);

        if ($status) {
            $this->old_lager_id = $old_lager_id;
        }

        return $status;
    }

    /**
     * @param $order_id
     * @return bool
     */
    public function setOrderId($order_id): bool
    {
        return $this->daten->setter('order_id', $order_id);
    }


    /**
     * @param $kundennr
     * @return bool
     */
    public function setKundennr($kundennr): bool
    {
        $kundennr = (string)$kundennr;

        $status = $this->daten->setter('kundennr', $kundennr);

        $this->shop_id = SupplierOrderRepository::getShopIdByKundenr($this->getSupplierId(), $kundennr);

        return $status;
    }

    public function hasSupplierOrderTag(string $supplier_order_tag): bool
    {
        return in_array($supplier_order_tag, $this->getSupplierOrderTags());
    }

    public function addSupplierOrderTag(string $supplier_order_tag): bool
    {
        $supplier_order_tags = $this->getSupplierOrderTags();
        if (!in_array($supplier_order_tag, $supplier_order_tags)) {
            $supplier_order_tags[] = $supplier_order_tag;
        }
        return $this->setSupplierOrderTags($supplier_order_tags);
    }

    public function removeSupplierOrderTag(string $supplier_order_tag): bool
    {
        $supplier_order_tags = $this->getSupplierOrderTags();
        if (in_array($supplier_order_tag, $supplier_order_tags)) {
            $supplier_order_tags = array_diff($supplier_order_tags, [$supplier_order_tag]);
        }
        return $this->setSupplierOrderTags($supplier_order_tags);
    }

    public function setSupplierOrderTags(array $supplier_order_tags): bool
    {
        $supplier_order_tag_repository = service_loader::getDiContainer()->get(SupplierOrderTagRepository::class);
        $supplier_order_tag_repository->validateSupplierOrderTags($supplier_order_tags);
        return $this->daten->setter('supplier_order_tags', implode(',', $supplier_order_tags));
    }


    public function getSupplierOrderId(): int
    {
        return (int)$this->daten->getter('supplier_order_id');
    }

    public function getSupplierId(): int
    {
        return (int)$this->daten->getter('supplier_id');
    }

    /**
     * @return string user_id
     */
    public function getUserId()
    {
        return $this->daten->getter('user_id');
    }

    /**
     * @return string status
     */
    public function getStatus()
    {
        return $this->daten->getter('status');
    }

    public function getStatusAsText()
    {
        return $this->getStatus();
    }

    /**
     * @return DateObj|null datum_anfrage
     */
    public function getDatumAnfrage(): ?DateObj
    {
        $datum_anfrage = new DateObj($this->daten->getter('datum_anfrage'));
        if (!$datum_anfrage->isValid()) {
            return null;
        }
        return $datum_anfrage;
    }

    /**
     * @return DateObj datum_bestellt
     */
    public function getDatumBestellt(): DateObj
    {
        return new DateObj($this->daten->getter('datum_bestellt'));
    }

    /**
     * @return DateObj|null datum_erledigt
     */
    public function getDatumErledigt(): ?DateObj
    {
        $datum_erledigt = new DateObj($this->daten->getter('datum_erledigt'));
        if (!$datum_erledigt->isValid()) {
            return null;
        }
        return $datum_erledigt;
    }

    /**
     * @return DateObj|null datum_tracking
     */
    public function getDatumTracking(): ?DateObj
    {
        $datum_tracking = new DateObj($this->daten->getter('datum_tracking'));
        if (!$datum_tracking->isValid()) {
            return null;
        }
        return $datum_tracking;
    }

    /**
     * @return DateObj delivery_date
     */
    public function getDeliveryDate(): DateObj
    {
        $date = new DateObj($this->daten->getter('delivery_date'));
        $date->clearTime();

        return $date;
    }

    public function getDeliveryDateMin(): DateObj
    {
        $date = new DateObj($this->daten->getter('delivery_date_min'));
        $date->clearTime();

        return $date;
    }


    private function setDeliveryDateMin(DateObj $delivery_date_min): bool
    {
        $delivery_date_min->clearTime();

        return $this->daten->setter('delivery_date_min', $delivery_date_min->db('date'));
    }

    /**
     * @return string bemerkung
     */
    public function getBemerkung()
    {
        return $this->daten->getter('bemerkung');
    }

    /**
     * @return int lager_id
     */
    public function getLagerId(): int
    {
        return $this->daten->getter('lager_id') ?: 0;
    }

    /**
     * @return int order_id
     */
    public function getOrderId(): int
    {
        return (int)$this->daten->getter('order_id');
    }

    /**
     * @return string kundennr
     */
    public function getKundennr()
    {
        return $this->daten->getter('kundennr');
    }

    /**
     * @return int
     */
    public function getShopId(): int
    {
        return (int)$this->shop_id;
    }

    /**
     * @param string $externe_referenz
     * @return bool
     */
    public function setExterneReferenz1(string $externe_referenz): bool
    {
        return $this->daten->setter('externe_referenz_1', $externe_referenz);
    }

    /**
     * @return string
     */
    public function getExterneReferenz1(): string
    {
        return $this->daten->getter('externe_referenz_1');
    }


    public function setExterneReferenz2(string $externe_referenz): bool
    {
        return $this->daten->setter('externe_referenz_2', $externe_referenz);
    }

    public function getExterneReferenz2(): string
    {
        return $this->daten->getter('externe_referenz_2');
    }

    public function setTransferStatus(string $transfer_status): bool
    {
        return $this->daten->setter('transfer_status', $transfer_status);
    }

    public function getTransferStatus(): string
    {
        return $this->daten->getter('transfer_status');
    }

    public function getSupplierOrderTags(): array
    {
        return $this->daten->getter('supplier_order_tags') ? explode(',', $this->daten->getter('supplier_order_tags')) : [];
    }

    public function setShippingCostsNetExpected(?float $shipping_costs_net_expected): bool
    {
        return $this->daten->setter('shipping_costs_net_expected', $shipping_costs_net_expected);
    }

    public function getShippingCostsNetExpected(): ?float
    {
        return $this->daten->getter('shipping_costs_net_expected');
    }


    //hilfs funktionen

    public function createBestellNr(): string
    {
        $bestell_nr = 'B' . str_pad($this->getSupplierOrderId(), 6, '0', STR_PAD_LEFT);

        if ($this->getSupplierId() == SuppliersConst::SUPPLIER_ID_SONEPAR_SUED_LANGWEID) {
            $bestell_nr .= ' LW';
        }

        if ($this->getSupplierId() == SuppliersConst::SUPPLIER_ID_UNI_ESCHBORN) {
            $bestell_nr .= '-EB';
        }

        if ($this->isDirektLieferung()) {
            $auftnr = order_repository::getAuftnrByOrderId($this->getOrderId());

            //wenn wir den Auftrag teilen, werden die referenzen zu lang. (generell UGL, oder?)
            if ($this->getSupplierId() === SuppliersConst::SUPPLIER_ID_EGH_FULFILLMENT) {
                $auftnr = order_repository::getBaseAuftnr($auftnr);
            }

            $bestell_nr .= ' ' . $auftnr;
        }

        return $bestell_nr;
    }

    /**
     * Gibt die Bestellnummer der Bestellung zurück
     * @return string $bestell_nr
     */
    public function getBestellNr(): string
    {
        return $this->daten->getter('bestell_nr');
    }

    public function getReferenzForExtern(): string
    {
        return $this->getBestellNr();
    }

    /**
     * gibt zurück ob die Bestellung direkt an den Lieferadresse der $order_id gesendet werden soll
     * @return bool
     */
    public function isDirektLieferung(): bool
    {
        return !$this->getLagerId();
    }

    /**
     * gibt die Lieferadresse der Bestellung als address zurück
     * @return Address
     */
    public function getDeliveryAddress(): Address
    {
        if ($this->isDirektLieferung()) {
            $order = new Order($this->getOrderId());

            $address = clone $order->getLieferAddress();
            if (!$address->getEmail()) {
                $address->setEmail($order->getCustomer()->getEmail());
            }

            return $address;
        }

        $lager = new Lager($this->getLagerId());

        return $lager->getAddress();
    }

    /**
     * our/buyer/invoice Address
     * @return Address
     */
    public function getInvoiceAddress(): Address
    {
        $shop = Shop::getInstance($this->getShopId());

        return $shop->getAddress();
    }


    /**
     * gibt den Grossist zurück
     * @return Supplier
     */
    public function getSupplier(): Supplier
    {
        return Supplier::getInstance($this->getSupplierId());
    }

    public function getStockClearance(): bool
    {
        return (bool)$this->daten->getter('stock_clearance');
    }

    /**
     * Legt fest, ob die Bestellung mit in der Kalkulation vom freien Bestands abgezogen werden soll.
     * Es geht hierbei um Fulfillment-Bestellungen. Durch die Verzögerung zwischen "Bestellung übermitteln" ->
     * "neuen Bestandsinformationen erhalten" haben wir hier immer eine Unsicherheit.
     * Wenn wir zum Zeitpunkt der Bestellung, diese schon nicht mehr in der freien Bestandskalkulation berücksichtigen,
     * haben wir in dem moment neuen Bestand erzeugt.
     * Wenn wir die Bestellung solange berücksichtigen, wie diese noch nicht beendet ist, blockieren wir ggf. zu viel Bestand.
     * Wir ziehen den Bestand ab und der Lieferante bei in den nächsten Bestandsinformationen auch.
     *
     * true -> die Bestellung fließt bei der Kalkulation des freien Bestands mit ein, wird also vom tatsächlichen Bestand abgezogen
     * false -> die Bestellung wird nicht mehr im freine Bestand berücksichtigt
     *
     */
    public function setStockClearance(bool $stock_clearance): bool
    {
        return $this->daten->setter('stock_clearance', (int)$stock_clearance);
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Bestell Items">


    public function addByOrderItemId(int $order_item_id, $ek_netto = 0): SupplierOrderItem
    {
        $daten = db::getInstance()->singleQuery('
            SELECT
                order_item.order_item_id,
                order_item.product_id,
                product.product_name,
                product_ek.gros_product_id,
                order_item.quantity,

                order_item.liefertermin_date
            FROM
                order_item LEFT JOIN
                product ON (product.product_id = order_item.product_id) LEFT JOIN
                product_ek ON (product_ek.product_id = order_item.product_id AND product_ek.supplier_id = ' . (int)$this->getSupplierId() . ')
            WHERE
                order_item.order_item_id = ' . $order_item_id . '
        ');

        $liefertermin_date = new DateObj($daten['liefertermin_date']);

        if ($liefertermin_date->isAfter($this->getDeliveryDate())) {
            $this->setDeliveryDate($liefertermin_date);
        }

        return $this->addProductNew(
            $daten['product_id'],
            $daten['quantity'],
            $ek_netto,
            $daten['product_name'],
            $daten['gros_product_id'] ?? '',
            $daten['order_item_id']
        );
    }


    public function addProduct(int $product_id, int $quantity, ?float $ek_netto = null, ?int $order_item_id = null): SupplierOrderItem
    {
        $result = db::getInstance()->singleQuery("
            SELECT
                product.product_name,
                product_ek.gros_product_id,
                product_ek.ek_netto
            FROM
                product LEFT JOIN
                product_ek ON (product_ek.product_id = product.product_id AND product_ek.supplier_id = " . (int)$this->getSupplierId() . ")
            WHERE
                product.product_id = $product_id
        ");

        if (!$result) {
            throw new FatalException('unbekanntes Produkt');
        }

        if ($ek_netto === null) {
            //@todo... ek thematik!
            $ek_netto = (float)$result['ek_netto'];
        }

        return $this->addProductNew($product_id, $quantity, $ek_netto, $result['product_name'], $result['gros_product_id'] ?? '', $order_item_id);
    }

    private function addProductNew(
        int $product_id,
        int $quantity,
        float $ek_netto,
        string $product_name,
        string $gros_product_id,
        ?int $order_item_id = null
    ): SupplierOrderItem {
        if ($this->getSupplierId() == SuppliersConst::SUPPLIER_ID_EURAS) {
            $item = null;
        } else {
            $item = $this->getItemByProductId($product_id);
        }

        if (!$item) {
            $item = new SupplierOrderItem();
            $item->setProductId($product_id);

            $this->addSupplierOrderItem($item);
        }

        $item->addQuantity($quantity);
        $item->setEkNetto($ek_netto);
        $item->setProductName($product_name);
        $item->setGrosProductId($gros_product_id);
        if ($order_item_id) {
            $item->addOrderItemId($order_item_id);
        }

        return $item;
    }

    public function addSupplierOrderItem(SupplierOrderItem $supplier_order_item): void
    {
        $supplier_order_item->setSupplierOrder($this);

        $this->items[] = $supplier_order_item;

        $this->calcDeliveryDateMin();
    }


    /**
     * @param $product_id
     * @return null|SupplierOrderItem
     */
    private function getItemByProductId(int $product_id): ?SupplierOrderItem
    {
        foreach ($this->getItems() as $item) {
            if ($item->getProductId() == $product_id) {
                return $item;
            }
        }

        return null;
    }

    public function getItemByBestellItemId(int $bestell_item_id): SupplierOrderItem
    {
        foreach ($this->getItems() as $item) {
            if ($item->getBestellItemId() === $bestell_item_id) {
                return $item;
            }
        }

        throw new DomainException('unknown $bestell_item_id ' . $bestell_item_id);
    }

    /**
     * @return SupplierOrderItemExtended[]
     */
    public function getItemsExtended(): array
    {
        $product_ids = [];
        $items = [];

        foreach ($this->getItems() as $item) {
            $product_ids[] = $item->getProductId();
            $items[] = $item;
        }

        $db = db::getInstance();

        $ext_daten = $db->query("
            SELECT
                product.product_id,
                product.product_nr,
                product.ean,
                product.mpn
            FROM
                product
            WHERE
                product.product_id IN (" . $db->in($product_ids) . ")
        ")->asArray('product_id');

        $items_extended = [];
        $pos = 1;

        foreach ($items as $item) {
            if (!isset($ext_daten[$item->getProductId()])) {
                throw new FatalException('Produkt nicht gefunden');
            }
            $items_extended[] = new SupplierOrderItemExtended(
                $item,
                $pos,
                $ext_daten[$item->getProductId()]['product_nr'],
                $ext_daten[$item->getProductId()]['ean'],
                $ext_daten[$item->getProductId()]['mpn']
            );
            $pos++;
        }

        return $items_extended;
    }

    /**
     * @return SupplierOrderItem[]
     */
    public function getItems(): array
    {
        $items = [];

        foreach ($this->items as $item) {
            if ($item->getSmartDataObj()->getObjStatus() == SmartDataObj::STATUS_DEL) {
                continue;
            }

            $items[] = $item;
        }
        return $items;
    }

    /**
     * @return int[]
     */
    public function getProductIds(): array
    {
        $product_ids = [];

        foreach ($this->getItems() as $item) {
            $product_ids[$item->getProductId()] = true;
        }

        return array_keys($product_ids);
    }

    /**
     * gibt die Anzahl der bestellten Einheiten zurück
     */
    public function getItemCount(): int
    {
        $quantity = 0;

        foreach ($this->getItems() as $item) {
            $quantity += $item->getQuantity();
        }

        return $quantity;
    }

    /**
     * gibt die gesamt Bestellsumme zurück
     */
    public function getTotalAmount(): float
    {
        $amount = 0;

        foreach ($this->getItems() as $item) {
            $amount += $item->getQuantity() * $item->getEkNetto();
        }

        return $amount;
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="Utils">

    /**
     * gibt den Bestellschein zurück
     */
    public function getOrderSheet(): SupplierOrderSheet
    {
        return new SupplierOrderSheet($this);
    }

    public function calcOrdersForLager(): void
    {
        foreach ($this->getItems() as $item) {
            ProductLager::calcOrdersForProduct($item->getProductId(), [$this->getLagerId(), $this->old_lager_id]);
        }
    }
    // </editor-fold>

    // <editor-fold defaultstate="collapsed" desc="state machine">

    private function checkIstBestand(): void
    {
        if (!in_array($this->getStatus(), [SupplierOrder::STATUS_BESTELLT, SupplierOrder::STATUS_UNTERWEGS])) {
            return;
        }

        $is_complete = true;

        foreach ($this->getItems() as $item) {
            if ($item->getAnzahl() > $item->getAnzahlIst()) {
                $is_complete = false;
            }
        }

        if ($is_complete) {
            if ($this->isDirektLieferung()) {
                //https://allego.myjetbrains.com/youtrack/issue/WWS-1185
                //es gibt hier ein Problem mit doBestellungErledigt()
                //Wenn das eine Direktlieferung ist, löst das Einbuchen auch diese Methode aus. Problem dabei ist, dass ist
                //in dem Moment eine andere Instanz. Der Auftrag wird damit zweimal erledigt. Als Workaround wird dieser
                //Aufruf hier ignroiert.

                //->ich hab das jetzt etwas umstrukturiert. Das eigentliche Problem bleibt bestehen, aber wurde um eine Ebene events reduziert.

                return;
            }

            $this->setStatus(self::STATUS_ERLEDIGT);
        }
    }

    public function bestellen(): void
    {
        $this->setStatus(self::STATUS_BESTELLT);
        $this->save();
    }

    public function storno(): void
    {
        if (!in_array($this->getStatus(), [self::STATUS_BESTELLT])) {
            throw new Exception('Es können nur Bestellungen storniert werden.');
        }

        $this->setStatus(self::STATUS_STORNIERT);
        $this->save();
    }

    public function del(): void
    {
        //nur möglich wenn auf anfrage
        if ($this->getStatus() != self::STATUS_ANFRAGE && $this->getStatus() != self::STATUS_ENTWURF) {
            throw new Exception('Es können nur Entwürfe und Anfragen gelöscht werden.');
        }

        $db = db::getInstance();
        $db->query("
            DELETE
                supplier_order,
                supplier_order_items
            FROM
                supplier_order INNER JOIN
                supplier_order_items ON (supplier_order.supplier_order_id = supplier_order_items.supplier_order_id)
            WHERE
                supplier_order.supplier_order_id = '" . $this->getSupplierOrderId() . "'
        ");

        $this->calcOrdersForLager();
    }

    public function doBestellungErledigt(): void
    {
        $customer_order_cancelled = false;

        if ($this->isDirektLieferung()) {
            $order = service_loader::get(OrderRepository::class)->loadCached($this->getOrderId());

            $customer_order_cancelled = $order->getMinStatus() === OrderConst::STATUS_STORNO;

            $book_customer_order = $order->hasStatus(OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND);

            if ($book_customer_order) {
                db::getInstance()->begin();
                $lager_id = SupplierRepository::getDvLagerId($this->getSupplierId());
                $lager = LagerRepository::load($lager_id);

                $create_wareneingang = true;

                //bei den manuell verwalteten Lägern buchen wir die Wareneingänge per Hand
                if ($lager->getLagerTyp() === LagerRepository::LAGER_TYPE_GROSSIST_MANUAL || $lager->getLagerTyp() === LagerRepository::LAGER_TYPE_NORMAL) {
                    $create_wareneingang = false;
                }

                if ($create_wareneingang) {
                    //Ware einbuchen mit Auftragsnummer und Bestellnummer
                    //Grund:
                    //1. um den Bestand zu korrigieren (der Bestand wird auch vom Lieferanten reduziert)
                    //2. controlling möglichkeit (für alles was wir berechnet bekommen, muss ein Wareneingang existieren)
                    $wareneingang = WareneingangLieferscheinRepository::create();
                    $wareneingang->setSupplierId($this->getSupplierId());
                    $wareneingang->setLagerId($lager_id);
                    $wareneingang->setLieferscheinNr('DV ' . $this->getBestellNr() . ' für ' . $order->getAuftnr());
                    $wareneingang->addSupplierOrderId($this->getSupplierOrderId());

                    foreach ($this->getItems() as $item) {
                        $wareneingang_item = $wareneingang->createItem();
                        $wareneingang_item->setAnzahl($item->getQuantity());
                        $wareneingang_item->setProductId($item->getProductId());
                    }

                    WareneingangLieferscheinRepository::save($wareneingang);
                    $wareneingang->doPreBook();
                    $wareneingang->doBook();
                }

                $order->setLagerId($lager_id);
                $order->save();

                $converter = service_loader::getDiContainer()->get(SupplierOrderToLieferschein::class);
                $warenausgang_lieferschein = $converter->convert($this);
                $warenausgang_lieferschein->save();
                $warenausgang_lieferschein->buchen();

                db::getInstance()->commit();
            }
        }

        if ($this->getStatus() === self::STATUS_STORNIERT) {
            //Versandmeldung auf eine stornierte Lieferantenbestellung
            $message_repository = service_loader::get(SupplierOrderMessageRepository::class);

            $message = $message_repository->create();
            $message->setSupplierOrderId($this->getSupplierOrderId());
            $message->setMessage('Statuswechsel von "Storniert" auf "Erledigt".');
            $message->setMessageTypeId(SuppliersConst::MESSAGE_TYPE_ERROR);
            $message->setMustRead(true);

            $message_repository->save($message);
        } elseif ($customer_order_cancelled) {
            //Versandmeldung auf eine Lieferantenbestellung, aber der zugehörige Endkundenauftrag ist storniert
            $message_repository = service_loader::get(SupplierOrderMessageRepository::class);

            $message = $message_repository->create();
            $message->setSupplierOrderId($this->getSupplierOrderId());
            $message->setMessage('Der zugehörige Endkundenauftrag ist storniert.');
            $message->setMessageTypeId(SuppliersConst::MESSAGE_TYPE_ERROR);
            $message->setMustRead(true);

            $message_repository->save($message);
        }

        $this->setStatus(self::STATUS_ERLEDIGT);
        $this->save();
    }

    public function endEntwurf(string $bestellart): void
    {
        switch ($bestellart) {
            case 'bestellen':
            case 'istBestellt':
                $this->bestellen();
                break;
            case 'wirdBestellt':
                //das wird noch immer verwendet um positionen, in den grossisten unter "in Bearbeitung (Lager)" zu bekommen
                $status = OrderConst::STATUS_WARTE_AUSLIEFERUNG;

                $order = new Order();
                $order->setAuftnr($this->getBestellNr());
                $order->prefillByCustomerId(1);
                $order->setTaxStatus(OrderConst::TAX_STATUS_NORMAL);
                $order->setOrderType(OrderConst::ORDER_TYPE_EK_BESTELLUNG);

                foreach ($this->getItems() as $item) {
                    $order_item = $order->createOrderItem($item->getProductId());
                    $order_item->setQuantity($item->getQuantity());
                    $order_item->setEkNetto($item->getEkNetto());
                    $order_item->setGrosMemo($this->getBemerkung());
                    $order_item->setSupplierId($this->getSupplierId());
                    $order_item->setLagerId($this->getLagerId());
                }

                $order->setStatus($status);
                $order->setZahlungsart(OrderConst::PAYMENT_RECHNUNG);
                $order->save();

                $this->del();
                break;
            case SupplierOrder::STATUS_ANFRAGE:
                $this->setStatus(SupplierOrder::STATUS_ANFRAGE);
                $this->save();
                break;
        }
    }

    // </editor-fold>

    public function calcDeliveryDateMin(): void
    {
        $delivery_date_min = $this->getDeliveryDate();

        foreach ($this->items as $item) {
            if ($item->getAnzahlIst() >= $item->getAnzahl()) {
                continue;
            }

            $date = $item->getDeliveryDate();

            if ($date && $date->isBefore($delivery_date_min)) {
                $delivery_date_min = $date;
            }
        }

        $this->setDeliveryDateMin($delivery_date_min);
    }
}
