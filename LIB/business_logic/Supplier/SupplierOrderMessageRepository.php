<?php

namespace wws\Supplier;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\InputException;
use bqp\FileAttachment\FileAttachment;
use bqp\Model\EntityChanges;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\storage\StorageFactory;
use env;
use InvalidArgumentException;
use service_loader;
use wws\Supplier\Event\EventSaveSupplierOrderMessage;

class SupplierOrderMessageRepository
{

    private db_generic $db;
    private SupplierOrderMessageFileAttachmentManager $file_attachment_manager;

    public function __construct(
        db_generic $db,
        StorageFactory $storage_factory,
    ) {
        $this->db = $db;

        $this->file_attachment_manager = new SupplierOrderMessageFileAttachmentManager($storage_factory);
    }


    public function create(): SupplierOrderMessage
    {
        $message = new SupplierOrderMessage();
        $message->setMessageDateCreated(new DateObj());
        $message->setMessageDate(null);
        $user_id = env::getUserId();
        if ($user_id === -1) {
            $user_id = 0;
        }
        $message->setUserId($user_id);
        $message->setMessage('');
        $message->setMessageTypeId(SuppliersConst::MESSAGE_TYPE_UNKNOWN);
        $message->setMessageKey('');
        $message->setMustRead(false);

        return $message;
    }

    public function save(SupplierOrderMessage $message): int
    {
        $this->validate($message);

        $this->file_attachment_manager->process($message);

        $data = $message->getSmartDataObj();

        $this->db->simpleInsertUpdate('supplier_order_message', $data->getAsArray());

        if (!$message->getMessageId()) {
            $data->setterDirect('message_id', $this->db->insert_id());
        }

        $changes = new EntityChanges();
        $changes->addBySmartDataObj($data);

        $event_new_supplier_order_message = new EventSaveSupplierOrderMessage($message, $changes);
        service_loader::getEventDispatcher()->dispatch($event_new_supplier_order_message);

        return $message->getMessageId();
    }

    public function load(int $message_id): SupplierOrderMessage
    {
        $row = $this->db->singleQuery("
            SELECT
                supplier_order_message.message_id,
                supplier_order_message.supplier_order_id,
                supplier_order_message.message_type_id,
                supplier_order_message.message_date_created,    
                supplier_order_message.message_date,
                supplier_order_message.must_read,
                supplier_order_message.message_key,
                supplier_order_message.user_id,
                supplier_order_message.message,
                supplier_order_message.message_attachments,
                supplier_order_message.message_read_by_user_id,
                supplier_order_message.message_read_date
            FROM
                supplier_order_message
            WHERE
                supplier_order_message.message_id = " . $message_id . "
        ");

        if (!$row) {
            throw new SmartDataEntityNotFoundException();
        }

        $message = new SupplierOrderMessage();
        $message->getSmartDataObj()->loadDaten($row);

        return $message;
    }

    /**
     * @param int $supplier_order_id
     * @return SupplierOrderMessage[]
     */
    public function loadBySupplierOrderId(int $supplier_order_id): array
    {
        $result = $this->db->query("
            SELECT
                supplier_order_message.message_id,
                supplier_order_message.supplier_order_id,
                supplier_order_message.message_type_id,
                supplier_order_message.message_date_created,
                supplier_order_message.message_date,
                supplier_order_message.must_read,
                supplier_order_message.message_key,
                supplier_order_message.user_id,
                supplier_order_message.message,
                supplier_order_message.message_attachments,
                supplier_order_message.message_read_by_user_id,
                supplier_order_message.message_read_date
            FROM
                supplier_order_message
            WHERE
                supplier_order_message.supplier_order_id = " . $supplier_order_id . "
        ");

        $messages = [];

        foreach ($result as $row) {
            $message = new SupplierOrderMessage();
            $message->getSmartDataObj()->loadDaten($row);

            $messages[] = $message;
        }

        return $messages;
    }

    public function validate(SupplierOrderMessage $message): void
    {
        $e = new InputException();
        if (!$message->getSupplierOrderId()) {
            $e->add('supplier_order_id', 'Keine Lieferanten-Bestellung angegeben.');
        }

        if ($e->isError()) {
            throw $e;
        }
    }


    public function existsMessageKey(int $supplier_order_id, string $message_key): bool
    {
        if (!$message_key) {
            throw new InvalidArgumentException('the message key must be not empty');
        }

        return (bool)$this->db->fieldQuery("
            SELECT
                supplier_order_message.message_id
            FROM
                supplier_order_message
            WHERE
                supplier_order_message.supplier_order_id = " . $supplier_order_id . " AND
                supplier_order_message.message_key = '" . $this->db->escape($message_key) . "'
        ");
    }

    public function getAttachmentDownloadUrl(FileAttachment $attachment): string
    {
        return $this->file_attachment_manager->getAttachmentDownloadUrl($attachment);
    }

    public function getAttachmentManger(): SupplierOrderMessageFileAttachmentManager
    {
        return $this->file_attachment_manager;
    }

    public function getMessageTypeNames(): array
    {
        static $cache = null;

        if ($cache === null) {
            $cache = $this->db->query("
                SELECT
                    supplier_order_message_type.message_type_id,
                    supplier_order_message_type.message_type_name
                FROM
                    supplier_order_message_type
            ")->asSingleArray('message_type_id');
        }

        return $cache;
    }

    public function getMessageTypeName(int $message_type_id): string
    {
        return $this->getMessageTypeNames()[$message_type_id];
    }

    public function isSupplierOrderMustRead(int $supplier_order_id): bool
    {
        return $this->db->fieldQuery("
            SELECT
                count(supplier_order_message.message_id)
            FROM
                supplier_order_message
            WHERE 
                supplier_order_message.supplier_order_id = $supplier_order_id AND
                supplier_order_message.must_read = 1
        ") > 0;
    }
}
