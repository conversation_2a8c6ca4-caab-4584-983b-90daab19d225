<?php

namespace wws\Supplier;

use bqp\Exceptions\FatalException;
use bqp\FileAttachment\FileAttachment;
use bqp\FileAttachment\FileAttachmentManager;
use bqp\FileAttachment\FileAttachmentSerializer;
use bqp\storage\StorageFactory;
use bqp\storage\storage_mount_manager;
use bqp\Utils\FileUtils;
use db;

class SupplierOrderMessageFileAttachmentManager extends FileAttachmentManager
{
    /**
     * @var storage_mount_manager
     */
    private $storage;

    public function __construct(StorageFactory $storage_factory)
    {
        $this->storage = $storage_factory->get('wws');
    }

    public function process(SupplierOrderMessage $message): void
    {
        $attachments = $message->getMessageAttachments();

        $this->loadTemp($attachments);

        $attachments_to_store = $attachments->getAttachmentsToStore();

        if ($attachments_to_store) {
            $sub_directory = $this->getSubDirectory($message);

            foreach ($attachments_to_store as $attachment) {
                $this->saveAttachment($attachment, $sub_directory);
            }
        }

        //foreach ($attachments->getRemovedAttachments() as $attachment) {
        //}

        $message->setMessageAttachmentsSerialized(FileAttachmentSerializer::serialize($attachments));
    }

    private function getSubDirectory(SupplierOrderMessage $message): string
    {
        $supplier_id = db::getInstance()->fieldQuery("
            SELECT
                supplier_order.supplier_id
            FROM
                supplier_order
            WHERE
                supplier_order.supplier_order_id = " . $message->getSupplierOrderId() . "
        ");

        if (!$supplier_id) {
            throw new FatalException('unknown supplier_order');
        }

        $path = $supplier_id;
        $path .= '/' . $message->getMessageDateCreated()->format('Y') . '/';
        return $path;
    }

    private function saveAttachment(FileAttachment $attachment, string $sub_directroy = ''): void
    {
        $content = $attachment->getAttachmentContent()->getContent();

        $path_add = '';

        if ($sub_directroy) {
            $path_add = $sub_directroy;
        }

        $clear_filename = FileUtils::normalizeFilename($attachment->getFilename());

        $storage_url = 'wws://grossisten_bestellung_attachments/' . $path_add . FileUtils::appendBeforExtenstion($clear_filename, md5($content));

        $attachment->setFilesize(strlen($content));
        $attachment->setStorageUrl($storage_url);

        if ($this->storage->has($storage_url)) {
            //muss nix getan werden -> der hash ist auf dem content
            //$this->storage->update($path, $content);
        } else {
            $this->storage->write($storage_url, $content);
        }
    }


    public function getAttachmentDownloadUrl(FileAttachment $attachment)
    {
        return $this->storage->createHttpUrl($attachment->getStorageUrl(), $attachment->getFilename());
    }

    public function loadAttachmentContent(FileAttachment $attachment)
    {
        return $this->storage->read($attachment->getStorageUrl());
    }
}