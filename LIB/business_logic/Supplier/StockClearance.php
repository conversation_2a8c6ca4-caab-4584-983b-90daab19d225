<?php

namespace wws\Supplier;

use bqp\Date\DateObj;
use bqp\db\db_generic;

class StockClearance
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function setStockClearance(SupplierOrder $supplier_order): void
    {
        $supplier_order->setStockClearance($this->calcStockClearance($supplier_order));
    }

    /**
     * true -> wird in der Bestandskalkulation einbezogen
     * false -> wird nicht einbezogen
     *
     * @param SupplierOrder $supplier_order
     * @return bool
     */
    public function calcStockClearance(SupplierOrder $supplier_order): bool
    {
        //Achtung: wird mit über EventSupplierOrderPreSave aufgerufen und auch für Bestellungen im Entwurf!
        //->wir nehmen einfach an, wenn kein Bestelldatum gesetzt ist, dass uns die Bestellung noch nicht interessiert
        if (!$supplier_order->getDatumBestellt()->isValid()) {
            return true;
        }

        $hours_since_order = $supplier_order->getDatumBestellt()->diffSimple(new DateObj(), 'hours');

        if ($supplier_order->getSupplierId() === SuppliersConst::SUPPLIER_ID_KREMPL) {
            return $hours_since_order <= 3;
        }

        //@todo konfigurierbar über lieferant? -> schwierig. ein statischer wert deckt das nicht ab. (werktage...)
        //wir haben jetzt pauschal 72h (3 Tage) innerhalb von arbeitstagen
        if ($hours_since_order >= 72) { //performance
            $order_date = $supplier_order->getDatumBestellt()->clone()->addWerktage(3);
            return $order_date->isFuture();
        }

        return true;
    }

    public function updateAll(): void
    {
        $supplier_order_ids = $this->db->query("
            SELECT
                supplier_order.supplier_order_id
            FROM
                supplier_order
            WHERE
                supplier_order.stock_clearance = 1 AND
                supplier_order.datum_bestellt >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND
                supplier_order.status IN ('" . SupplierOrder::STATUS_BESTELLT . "', '" . SupplierOrder::STATUS_UNTERWEGS . "')
        ")->asSingleArray();

        foreach ($supplier_order_ids as $supplier_order_id) {
            $supplier_order = new SupplierOrder($supplier_order_id);

            if ($supplier_order->setStockClearance($this->calcStockClearance($supplier_order))) {
                $supplier_order->save();
            }
        }
    }
}
