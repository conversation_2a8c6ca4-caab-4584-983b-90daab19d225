<?php

namespace wws\ProductDevice;

/**
 * hilfsstruktur, um seo richtig zu
 */
class ProductDeviceCacheDeviceList
{
    private array $seo_device_ids = [];
    private array $non_seo_device_ids = [];

    private array $added_device_ids = [];
    private array $removed_device_ids = [];

    public function __construct(array $seo_device_ids = [], array $non_seo_device_ids = [])
    {
        shuffle($seo_device_ids);
        shuffle($non_seo_device_ids);

        $this->seo_device_ids = array_combine($seo_device_ids, $seo_device_ids);
        $this->non_seo_device_ids = array_combine($non_seo_device_ids, $non_seo_device_ids);
    }

    public function isChange(): bool
    {
        return !empty($this->added_device_ids) || !empty($this->removed_device_ids);
    }

    public function addSeoDeviceId(int $device_id): void
    {
        $this->seo_device_ids[$device_id] = $device_id;
        $this->added_device_ids[$device_id] = $device_id;
    }

    public function addNonSeoDeviceId(int $device_id): void
    {
        $this->non_seo_device_ids[$device_id] = $device_id;
        $this->added_device_ids[$device_id] = $device_id;
    }

    public function getDeviceCount(): int
    {
        return count($this->getDeviceIds());
    }


    public function getSeoDeviceCount(): int
    {
        return count($this->seo_device_ids);
    }

    public function getNonSeoDeviceCount(): int
    {
        return count($this->non_seo_device_ids);
    }

    public function getDeviceIds(): array
    {
        return array_merge($this->seo_device_ids, $this->non_seo_device_ids);
    }

    public function getSeoDeviceIds(): array
    {
        return $this->seo_device_ids;
    }

    public function getNonSeoDeviceIds(): array
    {
        return $this->non_seo_device_ids;
    }

    public function getAddedDeviceIds(): array
    {
        return $this->added_device_ids;
    }

    public function getRemovedDeviceIds(): array
    {
        return $this->removed_device_ids;
    }

    public function removeDeviceId(int $device_id): void
    {
        if (array_key_exists($device_id, $this->seo_device_ids)) {
            unset($this->seo_device_ids[$device_id]);
            $this->removed_device_ids[$device_id] = $device_id;
            return;
        }

        if (array_key_exists($device_id, $this->non_seo_device_ids)) {
            unset($this->non_seo_device_ids[$device_id]);
            $this->removed_device_ids[$device_id] = $device_id;
        }
    }

    public function popSeoDeviceIds(int $quantity): array
    {
        $device_ids = array_splice($this->seo_device_ids, 0, $quantity);
        foreach ($device_ids as $device_id) {
            $this->removed_device_ids[$device_id] = $device_id;
        }

        return $device_ids;
    }

    public function popNonSeoDeviceIds(int $quantity): array
    {
        $device_ids = array_splice($this->non_seo_device_ids, 0, $quantity);
        foreach ($device_ids as $device_id) {
            $this->removed_device_ids[$device_id] = $device_id;
        }

        return $device_ids;
    }
}
