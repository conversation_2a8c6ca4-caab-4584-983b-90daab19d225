<?php

namespace wws\ProductDevice;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Utils\ArrayUtils;
use env;
use wws\Product\ProductConst;

class ProductDeviceCache
{
    private int $filter_list_id;
    private array $seo_brand_ids;
    private int $seo_threshold = 5;
    private db_generic $db;
    private ProductDeviceRepository $product_device_repository;

    private array $product_device_mappings_event_cache = [];
    private ProductDeviceMappingBulkEventServiceWws $product_device_mapping_bulk_event_service;

    public function __construct(
        db_generic $db,
        ProductDeviceRepository $product_device_repository,
        ProductDeviceMappingBulkEventServiceWws $product_device_mapping_bulk_event_service,
    ) {
        $this->db = $db;

        $this->product_device_repository = $product_device_repository;
        $this->product_device_mapping_bulk_event_service = $product_device_mapping_bulk_event_service;


//        $this->seo_brand_ids = [
//            ProductConst::BRAND_ID_BOSCH,
//            ProductConst::BRAND_ID_SIEMENS,
//            ProductConst::BRAND_ID_NEFF,
//            ProductConst::BRAND_ID_LIEBHERR,
//            ProductConst::BRAND_ID_MIELE,
//            ProductConst::BRAND_ID_AEG,
//            ProductConst::BRAND_ID_AMICA,
//            ProductConst::BRAND_ID_BEKO
//        ];

//
//        $this->seo_brand_ids = $db->query("
//            SELECT
//                product_device.brand_id
//            FROM
//                product_device_cache INNER JOIN
//                product_device ON  (product_device_cache.device_id = product_device.device_id) INNER JOIN
//            WHERE
//                product_device_cache.search_count > 10 AND
//                product_device_cache.products_online >= 5 AND
//                product_device_cache.brand_id NOT IN (".ProductConst::BRAND_ID_UNBEKANNT.")
//            GROUP BY
//                product_device.brand_id
//            ORDER BY
//                COUNT(*) DESC
//            LIMIT 50
//        ")->asSingleArray();

        //ergibt sich aus dem Query...
        $this->seo_brand_ids = [20, 1, 27, 92, 141, 109, 48, 91, 594, 83, 1330, 1269, 51, 7, 1227, 115, 1335, 668, 1084, 11, 1271, 1149, 2, 1249, 55, 32, 1217, 56, 82, 539, 586, 1121, 1334, 1122, 68, 1263, 1317, 52, 1359, 21, 1222, 1270, 67, 1182, 149, 28, 1343, 1214, 1325];

        $this->filter_list_id = ProductConst::FILTER_LIST_ID_SHOP;

        if (env::isK11()) {
            $this->filter_list_id = ProductConst::FILTER_LIST_ID_ERSATZTEILSHOP;
        }
    }

    public function initNewDeviceId(int $device_id): void
    {
        $this->db->query("
            INSERT INTO
                product_device_cache (device_id)
            VALUES
                (" . $device_id . ")
        ");
    }

    public function updateCache(): void
    {
        $this->db->begin();

        $this->db->query("
            INSERT INTO
                product_device_cache (device_id)
            SELECT
                product_device.device_id
            FROM
                product_device LEFT JOIN
                product_device_cache ON (product_device.device_id = product_device_cache.device_id)
            WHERE
                product_device_cache.device_id IS NULL 
        ");

        $device_ids_all = $this->db->query("
            SELECT
                product_device.device_id
            FROM
                product_device
        ")->asSingleArray();

        $chunks = ArrayUtils::split($device_ids_all, 10000);

        foreach ($chunks as $device_ids) {
            $product_online_sql = "
                SELECT
                    product_device.device_id,
                    SUM(product_filter_list_results.status IS NOT NULL) AS products_online
                FROM
                    product_device LEFT JOIN
                    product_device_mapping ON (product_device.device_id = product_device_mapping.device_id) LEFT JOIN
                    product_filter_list_results ON (
                        product_filter_list_results.product_id = product_device_mapping.product_id AND
                        product_filter_list_results.filter_list_id = " . $this->filter_list_id . " AND
                        product_filter_list_results.status = 1
                    )
                WHERE
                    product_device.device_id IN (" . $this->db->in($device_ids) . ")
                GROUP BY
                    product_device.device_id
            ";

            $sells_sql = "
                SELECT
                    product_device.device_id,
                    SUM(product_cache.statistik_orders_total) AS product_sells_total
                FROM
                    product_device LEFT JOIN
                    product_device_mapping ON (product_device.device_id = product_device_mapping.device_id) LEFT JOIN
                    product_cache ON (product_device_mapping.product_id = product_cache.product_id)
                WHERE
                    product_device.device_id IN (" . $this->db->in($device_ids) . ")
                GROUP BY
                    product_device.device_id
            ";

            $this->db->query("
                UPDATE
                    product_device_cache INNER JOIN
                    ($product_online_sql) AS products_online ON (product_device_cache.device_id = products_online.device_id) INNER JOIN
                    ($sells_sql) AS sells ON (product_device_cache.device_id = sells.device_id)
                SET
                    product_device_cache.products_online = products_online.products_online,
                    product_device_cache.product_sells_total = sells.product_sells_total
                WHERE
                    product_device_cache.products_online != products_online.products_online OR
                    product_device_cache.product_sells_total != sells.product_sells_total
            ");
        }

        if (env::isEcom()) {
            //bei k11 lassen wir für die migration erstmal die finger davon (der seo_index wurde vorerst aus den shopware-daten geseedet)

            //@todo seo index nicht so "einfach" deaktivieren... wenns mit dem krempl sortiment probleme gibt, kann das böse effekte haben
            //hier müsste ggf. geschaut werden wie lange es schon keine angebote mehr gibt.
            //->bleibt erstmal so.
            //        $this->db->query("
            //            UPDATE
            //                product_device_cache
            //            SET
            //                product_device_cache.seo_index = 0
            //            WHERE
            //                product_device_cache.seo_index = 1 AND
            //                product_device_cache.products_online < ".$this->seo_threshold."
            //        ");

            $this->db->query("
                UPDATE
                    product_device_cache INNER JOIN
                    product_device ON (product_device.device_id = product_device_cache.device_id)
                SET
                    product_device_cache.seo_index = 1
                WHERE
                    product_device.brand_id IN (" . $this->db->in($this->seo_brand_ids) . ") AND
                    product_device_cache.products_online >= " . $this->seo_threshold . " AND
                    product_device_cache.search_count > 10 AND
                    product_device_cache.seo_index = 0
            ");


            $result = $this->db->query("
                SELECT
                    product_device.device_id,
                    product_device_cache.seo_index
                FROM
                    product_device INNER JOIN
                    product_device_cache ON (product_device.device_id = product_device_cache.device_id)
                WHERE
                    product_device.seo_index != product_device_cache.seo_index
            ");

            foreach ($result as $row) {
                $device = $this->product_device_repository->load($row['device_id']);
                $device->setSeoIndex((bool)$row['seo_index']);
                $this->product_device_repository->save($device);
            }
        }

        $this->db->query('
            UPDATE
                product_device_cache INNER JOIN
                (
                    SELECT
                        order_device.device_id,
                        COUNT(*) AS resulted_orders
                    FROM
                        order_device
                    GROUP BY
                        order_device.device_id
                ) AS order_device_count ON (product_device_cache.device_id = order_device_count.device_id)
            SET
                product_device_cache.resulted_orders = order_device_count.resulted_orders
        ');

        $this->db->commit();
    }

    /**
     * führt für neue Produkte/Produkte ohne einen Eintrag mit product_device_mapping.seo das SEO Mapping aus.
     * Weniger Ressourcenintensiv als updateProductIdsSeo() welches alle Produkt betrachtet.
     * @return void
     */
    public function initProductIdsSeo(): void
    {
        $product_ids = $this->db->query("
            SELECT
                all_product_ids.product_id            
            FROM
                (
                    SELECT
                        DISTINCT
                        product_device_mapping.product_id
                    FROM
                        product_device_mapping
                ) AS all_product_ids LEFT JOIN
                (
                    SELECT
                        DISTINCT
                        product_device_mapping.product_id
                    FROM
                        product_device_mapping
                    WHERE
                        product_device_mapping.seo = 1
                ) AS product_ids_with_seo ON (all_product_ids.product_id = product_ids_with_seo.product_id)
            WHERE
                product_ids_with_seo.product_id IS NULL      
        ")->asSingleArray();

        foreach ($product_ids as $product_id) {
            $this->updateProductIdSeo($product_id);
        }

        $this->flushProductDeviceMappingEvent();
    }

    /**
     * Die Methode selektiert für jedes Ersatzteil n Geräte und markiert diese als SEO. Das dient als Basis um jedes Produkt mit n Geräten im Shop darzustellen.
     */
    public function updateProductIdsSeo(): void
    {
        $product_ids = $this->db->query("
            SELECT
                DISTINCT
                product_device_mapping.product_id
            FROM
                product_device_mapping
        ")->asSingleArray();

        foreach ($product_ids as $product_id) {
            $this->updateProductIdSeo($product_id);
        }

        $this->flushProductDeviceMappingEvent();
    }

    /**
     * ACHTUNG: flushProductDeviceMappingEvent() muss manuell aufgerufen, nachdem alle product_ids abgearbeitet wurden.
     *
     * @param int $product_id
     * @return void
     */
    public function updateProductIdSeo(int $product_id): void
    {
        $limit = 750;
        $limit_rotating_quantity = 50; //wieviele geräte wir maximal rotieren wollen (achtung: seo/non seo)

        $fill_only_with_seo = false;

        if (env::isK11()) {
            $fill_only_with_seo = true;
        }

        $do_rotate = $this->isRotatingRun($product_id);
        $is_rotated = false;

        $device_list = $this->getCurrentSeoDeviceList($product_id);
        $this->removeDeletedSeoDevices($product_id, $device_list);

        if (!$do_rotate && $device_list->getSeoDeviceCount() === $limit) {
            return;
        }

        $candidate_device_list = $this->getPossibleSeoDeviceList($product_id, $limit, !$fill_only_with_seo);

        //wenn wir mehr geräte in seo haben, als limit -> nur wenn limit geändert wird!
        if ($device_list->getSeoDeviceCount() > $limit) {
            $excess_seo_device_count = $device_list->getSeoDeviceCount() - $limit;
            $device_list->popSeoDeviceIds($excess_seo_device_count);
            \debug::dump('limit geändert?');
        }

        //liste mit seo device_ids auffüllen
        $missing_seo_device_count = $limit - $device_list->getSeoDeviceCount();

        if ($missing_seo_device_count > 0) {
            $device_ids = $candidate_device_list->popSeoDeviceIds($missing_seo_device_count);

            foreach ($device_ids as $device_id) {
                $device_list->addSeoDeviceId($device_id);
            }
        }

        //prüfen ob jetzt mehr geräte als limit vorhanden sind imd ggf. entfernen (das können nur nicht seo geräte sein!)
        $excess_total = $device_list->getDeviceCount() - $limit;
        if ($excess_total > 0) {
             $device_list->popNonSeoDeviceIds($excess_total);
        }

        //auffüllen -> es können hier nur non seo device_ids übrig bleiben
        $missing_seo_device_count = $limit - $device_list->getDeviceCount();
        if (!$fill_only_with_seo && $missing_seo_device_count) {
            $device_ids = $candidate_device_list->popNonSeoDeviceIds($missing_seo_device_count);

            foreach ($device_ids as $device_id) {
                $device_list->addNonSeoDeviceId($device_id);
            }
        }

        ///geräte rotieren...
        if ($do_rotate && !$device_list->isChange()) {
            //um es einfach zu halten, rotieren wir nur, wenn es davor keine Änderung gab. die komplexität ansonsten ist es nicht wert
            $rotate_amount = $candidate_device_list->getSeoDeviceCount();
            $rotate_amount = min($limit_rotating_quantity, $rotate_amount);

            //die zufälligkeit wird durch ProductDeviceCacheDeviceList
            $device_list->popSeoDeviceIds($rotate_amount);

            $device_ids = $candidate_device_list->popSeoDeviceIds($rotate_amount);
            foreach ($device_ids as $device_id) {
                $device_list->addSeoDeviceId($device_id);
            }

            //nicht seo geräte
            if (!$fill_only_with_seo) {
                $rotate_amount = $candidate_device_list->getNonSeoDeviceCount();
                $rotate_amount = min(round($limit_rotating_quantity / 2), $rotate_amount);

                $device_list->popNonSeoDeviceIds($rotate_amount);

                $device_ids = $candidate_device_list->popNonSeoDeviceIds($rotate_amount);
                foreach ($device_ids as $device_id) {
                    $device_list->addNonSeoDeviceId($device_id);
                }
            }

            $is_rotated = true;
        }

        if ($device_list->getAddedDeviceIds()) {
            $this->db->query("
                UPDATE
                    product_device_mapping
                SET
                    product_device_mapping.seo = 0
                WHERE
                    product_device_mapping.product_id = " . $product_id . " AND
                    product_device_mapping.device_id IN (" . $this->db->in($device_list->getRemovedDeviceIds()) . ")
            ");

            foreach ($device_list->getRemovedDeviceIds() as $device_id) {
                $this->addProductDeviceMappingEvent($product_id, $device_id);
            }
        }

        if ($device_list->getAddedDeviceIds()) {
            $this->db->query("
                UPDATE
                    product_device_mapping
                SET
                    product_device_mapping.seo = 1
                WHERE
                    product_device_mapping.product_id = " . $product_id . " AND
                    product_device_mapping.device_id IN (" . $this->db->in($device_list->getAddedDeviceIds()) . ")
            ");

            foreach ($device_list->getAddedDeviceIds() as $device_id) {
                $this->addProductDeviceMappingEvent($product_id, $device_id);
            }
        }

        if ($is_rotated) {
            $this->markAsRotated($product_id);
        }
    }

    private function isRotatingRun(int $product_id): bool
    {
        $rotating_days = 90; // wie oft werden die Geräte gewechselt

        $last_device_seo_index = new DateObj($this->db->fieldQuery("
            SELECT
                product.last_device_seo_index
            FROM
                product
            WHERE
                product.product_id = " . $product_id . "
        "));

        $is_rotate_timeframe = $last_device_seo_index->diffSimple(new DateObj(), 'days') >= $rotating_days;

        if ($is_rotate_timeframe && rand(0, 5) == 3) {
            return true;
        }

        return false;
    }

    private function markAsRotated(int $product_id): void
    {
        $this->db->query("
            UPDATE
                product
            SET
                product.last_device_seo_index = CURRENT_DATE
            WHERE
                product.product_id = " . $product_id . "
        ");
    }

    private function removeDeletedSeoDevices(int $product_id, ProductDeviceCacheDeviceList $device_list): void
    {
        $device_ids = $device_list->getDeviceIds();

        if (!$device_ids) {
            return;
        }

        $device_ids = $this->db->query("
            SELECT
                product_device.device_id
            FROM
                product_device
            WHERE
                product_device.deleted_at IS NOT NULL AND
                product_device.device_id IN (" . $this->db->in($device_ids) . ")
        ")->asSingleArray();

        foreach ($device_ids as $device_id) {
            $device_list->removeDeviceId($device_id);
        }
    }

    public function getCurrentSeoDeviceList(int $product_id): ProductDeviceCacheDeviceList
    {
        $result = $this->db->query("
            SELECT
                product_device.seo_index,
                product_device_mapping.device_id
            FROM
                product_device_mapping INNER JOIN
                product_device ON (product_device_mapping.device_id = product_device.device_id)
            WHERE
                product_device_mapping.product_id = " . $product_id . " AND
                product_device_mapping.seo = 1
        ")->asMultiArraySingle('seo_index', 'device_id');

        return new ProductDeviceCacheDeviceList($result[1] ?? [], $result[0] ?? []);
    }

    private function getPossibleSeoDeviceList(int $product_id, int $limit, bool $only_seo_devices): ProductDeviceCacheDeviceList
    {
        $sql = "
            SELECT
                product_device_cache.seo_index,
                product_device_mapping.device_id
            FROM
                product_device_mapping INNER JOIN
                product_device_cache ON (product_device_mapping.device_id = product_device_cache.device_id) INNER JOIN
                product_device ON (product_device_mapping.device_id = product_device.device_id)
            WHERE
                product_device_mapping.product_id = " . $product_id . " AND
                product_device_mapping.seo = 0 AND
                product_device.deleted_at IS NULL
        ";
        if ($only_seo_devices) {
            $sql .= " AND product_device_cache.seo_index = 1";
        }
        $sql .= "
            ORDER BY 
                product_device_cache.seo_index DESC, RAND()
            LIMIT
                $limit
        ";

        $result = $this->db->query($sql)->asMultiArraySingle('seo_index', 'device_id');

        return new ProductDeviceCacheDeviceList($result[1] ?? [], $result[0] ?? []);
    }

    private function addProductDeviceMappingEvent(int $product_id, int $device_id): void
    {
        $this->product_device_mappings_event_cache[] = [
            'product_id' => $product_id,
            'device_id' => $device_id
        ];

        if (count($this->product_device_mappings_event_cache) >= 1000) {
            $this->flushProductDeviceMappingEvent();
        }
    }

    public function flushProductDeviceMappingEvent(): void
    {
        if (!$this->product_device_mappings_event_cache) {
            return;
        }

        $this->product_device_mapping_bulk_event_service->processBulk($this->product_device_mappings_event_cache);

        $this->product_device_mappings_event_cache = [];
    }
}
