<?php

namespace wws\Tracking;

use ArrayAccess;
use bqp\Exceptions\DevException;

class TrackingInformation implements ArrayAccess
{
    private ?int $lieferschein_id = null;
    private string $spedition;
    private int $tracking_carrier_id;
    private string $tracking_carrier_name;
    private string $tracking_id;
    private string $tracking_url;
    private array $tracking_extra = [];

    public function getSpedition(): string
    {
        return $this->spedition;
    }

    public function setSpedition(string $spedition): void
    {
        $this->spedition = $spedition;
    }

    public function getTrackingId(): string
    {
        return $this->tracking_id;
    }

    public function setTrackingId(string $tracking_id): void
    {
        $this->tracking_id = $tracking_id;
    }

    public function getTrackingUrl(): string
    {
        return $this->tracking_url;
    }

    public function setTrackingUrl(string $tracking_url): void
    {
        $this->tracking_url = $tracking_url;
    }

    public function hasTrackingUrl(): bool
    {
        return (bool)$this->tracking_url;
    }

    public function getTrackingCarrierId(): int
    {
        return $this->tracking_carrier_id;
    }

    public function setTrackingCarrierId(int $tracking_carrier_id): void
    {
        $this->tracking_carrier_id = $tracking_carrier_id;
    }

    public function getTrackingCarrierName(): string
    {
        return $this->tracking_carrier_name;
    }

    public function setTrackingCarrierName(string $tracking_carrier_name): void
    {
        $this->tracking_carrier_name = $tracking_carrier_name;
    }

    public function setTrackingExtra(array $tracking_extra): void
    {
        $this->tracking_extra = $tracking_extra;
    }

    public function getLieferscheinId(): ?int
    {
        return $this->lieferschein_id;
    }

    public function setLieferscheinId(int $lieferschein_id): void
    {
        $this->lieferschein_id = $lieferschein_id;
    }

    public function getTrackingExtra(): array
    {
        return $this->tracking_extra;
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->$offset);
    }

    public function offsetGet(mixed $offset): mixed
    {
        switch ($offset) {
            case 'tracking_id':
                return $this->getTrackingId();
            case 'spedition':
                return $this->getSpedition();
            case 'tracking_url':
                return $this->getTrackingUrl();
        }

        throw new DevException('unknown property');
    }

    public function offsetSet(mixed $offset, mixed $value): void
    {
        throw new DevException('readonly');
    }

    public function offsetUnset(mixed $offset): void
    {
        throw new DevException('readonly');
    }
}
