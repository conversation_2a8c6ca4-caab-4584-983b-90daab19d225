<?php

namespace wws\Tracking;

use bqp\Date\DateObj;
use bqp\extern\Emons\TrackingLinkBuilderEmons;
use bqp\extern\ids_logistik\TrackingLinkBuilderIds;
use bqp\extern\Mueller\TrackingLinkBuilderMueller;
use bqp\extern\Noerpel\TrackingLinkBuilderNoerpel;
use bqp\extern\Sonepar\TrackingLinkBuilderSoneparKoester;
use bqp\extern\Zufall\TrackingLinkBuilderZufall;
use bqp\Json;
use bqp\Model\SmartDataEntityNotFoundException;
use db;
use service_loader;
use wws\Shipment\ShipmentRepository;
use wws\Tracking\Event\NewTrackingAdded;
use wws\Tracking\TrackingCarrier\TrackingCarrierConst;
use wws\Tracking\TrackingCarrier\TrackingCarrierRepository;

class TrackingLegacy
{


    /**
     * @param int $lieferschein_id
     * @return TrackingInformation[]
     */
    public static function getTrackingIdsByLieferscheinId(int $lieferschein_id): array
    {
        $daten = db::getInstance()->query("
            SELECT
                warenausgang_lieferschein_tracking_ids.lieferschein_id,
                warenausgang_lieferschein_tracking_ids.spedition,
                warenausgang_lieferschein_tracking_ids.tracking_id,
                warenausgang_lieferschein_tracking_ids.tracking_url,
                warenausgang_lieferschein_tracking_ids.tracking_extra,
                
                tracking_carrier.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                tracking_carrier.tracking_carrier_key
            FROM
                warenausgang_lieferschein_tracking_ids INNER JOIN
                tracking_carrier ON (warenausgang_lieferschein_tracking_ids.tracking_carrier_id = tracking_carrier.tracking_carrier_id)
            WHERE
                warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . (int)$lieferschein_id . "'
        ")->asArray();

        return self::buildTrackingInformations($daten);
    }

    /**
     * @param array $order_ids
     * @return TrackingInformation[][]
     */
    public static function getTrackingIdsByOrderIds(array $order_ids): array
    {
        $db = db::getInstance();

        $result = $db->query("
            SELECT
                warenausgang_lieferschein.order_id,
                warenausgang_lieferschein_tracking_ids.lieferschein_id,
                warenausgang_lieferschein_tracking_ids.spedition,
                warenausgang_lieferschein_tracking_ids.tracking_id,
                warenausgang_lieferschein_tracking_ids.datum,
                warenausgang_lieferschein_tracking_ids.tracking_url,
                warenausgang_lieferschein_tracking_ids.tracking_extra,

                tracking_carrier.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                tracking_carrier.tracking_carrier_key
            FROM
                warenausgang_lieferschein INNER JOIN
                warenausgang_lieferschein_tracking_ids ON (warenausgang_lieferschein.lieferschein_id = warenausgang_lieferschein_tracking_ids.lieferschein_id) INNER JOIN
                tracking_carrier ON (warenausgang_lieferschein_tracking_ids.tracking_carrier_id = tracking_carrier.tracking_carrier_id)
            WHERE
                warenausgang_lieferschein.order_id IN (" . $db->in($order_ids) . ")
        ")->asMultiArray('order_id');

        $return = [];

        foreach ($order_ids as $order_id) {
            $return[$order_id] = self::buildTrackingInformations($result[$order_id] ?? []);
        }

        return $return;
    }

    /**
     * @param int $order_id
     * @return TrackingInformation[]
     */
    public static function getTrackingIdsByOrderId(int $order_id): array
    {
        return self::getTrackingIdsByOrderIds([$order_id])[$order_id];
    }

    /**
     * Gibt die Trackingdaten für den auftrag zurück. Wenn eine Spedition ohne Tracking verwendet wird (Post, Abholung oder Allego), wird ein Eintrag ohne TrackingId geliefert.
     *
     * @param int $order_id
     * @return TrackingInformation[]
     */
    public static function getTrackingIdsByOrderIdIncludeNonTrackableCarriers(int $order_id): array
    {
        $tracking = self::getTrackingIdsByOrderId($order_id);

        if ($tracking) {
            return $tracking;
        }

        $sped_ids = [ShipmentRepository::SPED_POST, ShipmentRepository::SPED_ALLEGO, ShipmentRepository::SPED_ABHOLUNG];

        $tracking_dto = self::getVirtTrackingDto($order_id, $sped_ids);

        if ($tracking_dto === null) {
            return [];
        }

        return [$tracking_dto];
    }

    /**
     * Gibt die Trackingdaten für ein Auftrag zurück. Sollten keine Trackingdaten vorhanden sein, wird ein Eintrag ohne Trackingnummer aus dem Lieferschein erzeugt.
     *
     * @param int $order_id
     * @return array|TrackingInformation[]
     */
    public static function getTrackingIdsByOrderIdVirt(int $order_id): array
    {
        $tracking = self::getTrackingIdsByOrderIdIncludeNonTrackableCarriers($order_id);

        if ($tracking) {
            return $tracking;
        }

        $tracking_dto = self::getVirtTrackingDto($order_id);

        if ($tracking_dto === null) {
            return [];
        }

        return [$tracking_dto];
    }

    /**
     * Aktuell muss ein Warenausganglieferschein existierten. Schauen wie sich das in der Praxis verhält. Im Uweifel kann das auch, aus der order selbst erzeugt werden. (Direktverand?!)
     *
     * @param int $order_id
     * @param array|null $allowed_sped_ids
     * @return TrackingInformation|null
     */
    private static function getVirtTrackingDto(int $order_id, ?array $allowed_sped_ids = null): ?TrackingInformation
    {
        $db = db::getInstance();

        $sped_ids_where = '';
        if ($allowed_sped_ids) {
            $sped_ids_where = '  AND warenausgang_lieferschein.sped_id IN (' . $db->in($allowed_sped_ids) . ')';
        }

        $row = $db->singleQuery("
            SELECT
                warenausgang_lieferschein.lieferschein_id,
                warenausgang_lieferschein.sped_id,
                warenausgang_lieferschein.datum,
                
                tracking_carrier.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                tracking_carrier.tracking_carrier_key
            FROM
                warenausgang_lieferschein INNER JOIN
                einst_spedition ON (warenausgang_lieferschein.sped_id = einst_spedition.sped_id) INNER JOIN
                tracking_carrier ON (einst_spedition.tracking_carrier_id = tracking_carrier.tracking_carrier_id)
            WHERE
                warenausgang_lieferschein.order_id = $order_id AND
                einst_spedition.tracking_carrier_id != 0
                $sped_ids_where
        ");

        if ($row) {
            $tracking = new TrackingInformation();
            $tracking->setTrackingCarrierId($row['tracking_carrier_id']);
            $tracking->setSpedition($row['tracking_carrier_name']);
            $tracking->setTrackingCarrierName($row['tracking_carrier_key']);
            $tracking->setTrackingId('');
            $tracking->setTrackingUrl('');
            $tracking->setLieferscheinId($row['lieferschein_id']);

            return $tracking;
        }

        //es gibt eine beschränkung -> dann nicht wieter improvisieren
        if ($allowed_sped_ids) {
            return null;
        }

        //über den Auftrag... geht aber i.d.R. nicht über Direkt Spedis (weil kein tracking_carrier zugeordnet (hier könnte man ggf noch überlegen das pi mal daumen einzupflegen))
        $row = $db->singleQuery("
            SELECT
                orders.order_id,

                tracking_carrier.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                tracking_carrier.tracking_carrier_key
            FROM
                orders INNER JOIN
                order_item ON (orders.order_id = order_item.order_id) INNER JOIN
                einst_spedition ON (order_item.sped_id = einst_spedition.sped_id) INNER JOIN
                tracking_carrier ON (einst_spedition.tracking_carrier_id = tracking_carrier.tracking_carrier_id)
            WHERE
                orders.order_id = $order_id
        ");

        if ($row) {
            $tracking = new TrackingInformation();
            $tracking->setTrackingCarrierId($row['tracking_carrier_id']);
            $tracking->setSpedition($row['tracking_carrier_name']);
            $tracking->setTrackingCarrierName($row['tracking_carrier_key']);
            $tracking->setTrackingId('');
            $tracking->setTrackingUrl('');

            return $tracking;
        }

        return null;
    }


    /**
     * fügt die Sendungsnummer zum angegebenen Lieferschein hinzu
     * @see self::addTrackingIdWithTrackingCarrierId();
     * @deprecated
     */
    public static function addTrackingId($lieferschein_id, $tracking_carrier_key, $tracking_id, DateObj $date = null, $tracking_url = null)
    {
        $tracking_carrier_repository = service_loader::getDiContainer()->get(TrackingCarrierRepository::class);
        $tracking_carrier_id = $tracking_carrier_repository->getTrackingCarrierIdByKey($tracking_carrier_key);

        self::addTrackingIdWithTrackingCarrierId($lieferschein_id, $tracking_carrier_id, $tracking_id, $date, $tracking_url);
    }

    /**
     * @param int $lieferschein_id
     * @param int $tracking_carrier_id
     * @param string $tracking_id
     * @param DateObj|null $date
     * @param string|null $tracking_url
     * @todo alle abhängigkeiten zu warenausgang_lieferschein_tracking_ids.spedition auf tracking_carrier_name umstellen
     * @todo addTrackingId() entfernen
     */
    public static function addTrackingIdWithTrackingCarrierId(
        int $lieferschein_id,
        int $tracking_carrier_id,
        string $tracking_id,
        ?DateObj $date = null,
        ?string $tracking_url = null
    ): void {
        if (!$date) {
            $date = new DateObj();
        }

        $db = db::getInstance();

        $warenausgang_lieferschein_tracking_id_exists = $db->fieldQuery("
            SELECT 
                warenausgang_lieferschein_tracking_ids.lieferschein_id
            FROM
                warenausgang_lieferschein_tracking_ids
            WHERE
                warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . $lieferschein_id . "' AND
                warenausgang_lieferschein_tracking_ids.tracking_id = '" . $db->escape($tracking_id) . "'
        ");

        if ($warenausgang_lieferschein_tracking_id_exists) {
            return;
        }

        $tracking_carrier_repository = service_loader::getDiContainer()->get(TrackingCarrierRepository::class);
        $tracking_carrier_key = $tracking_carrier_repository->getTrackingKeyByTrackingCarrierId($tracking_carrier_id);

        if (!$tracking_url && $tracking_carrier_id === TrackingCarrierConst::CARRIER_ID_EMONS) {
            //für das emons tracking brauchen wir die Postleitzahl aus dem Auftrag.
            $url_builder = service_loader::get(TrackingLinkBuilderEmons::class);
            $tracking_url = $url_builder->buildLinkByLieferscheinId($lieferschein_id, $tracking_id);
        }

        if (!$tracking_url && $tracking_carrier_id === TrackingCarrierConst::CARRIER_ID_ZUFALL) {
            $url_builder = service_loader::get(TrackingLinkBuilderZufall::class);
            $tracking_url = $url_builder->buildLinkByLieferscheinId($lieferschein_id, $tracking_id);
        }

        if (!$tracking_url && $tracking_carrier_id === TrackingCarrierConst::CARRIER_ID_NOERPEL) {
            $url_builder = service_loader::get(TrackingLinkBuilderNoerpel::class);
            $tracking_url = $url_builder->buildLinkByLieferscheinId($lieferschein_id, $tracking_id);
        }

        if (!$tracking_url && in_array($tracking_carrier_id, [TrackingCarrierConst::CARRIER_ID_RIECK, TrackingCarrierConst::CARRIER_ID_GRAS])) {
            $url_builder = service_loader::get(TrackingLinkBuilderIds::class);
            $tracking_url = $url_builder->buildLinkByLieferscheinId($lieferschein_id, $tracking_id);
        }

        if (!$tracking_url && $tracking_carrier_id === TrackingCarrierConst::CARRIER_ID_MUELLER) {
            $url_builder = service_loader::get(TrackingLinkBuilderMueller::class);
            $tracking_url = $url_builder->buildLinkByLieferscheinId($lieferschein_id, $tracking_id);
        }

        if ($tracking_carrier_id === TrackingCarrierConst::CARRIER_ID_KOESTER && $tracking_url) {
            $url_builder = service_loader::get(TrackingLinkBuilderSoneparKoester::class);
            $tracking_url = $url_builder->improveUrl($lieferschein_id, $tracking_url);
        }

        $db->query("
            INSERT INTO
                warenausgang_lieferschein_tracking_ids
            SET
                warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . (int)$lieferschein_id . "',
                warenausgang_lieferschein_tracking_ids.date_added = NOW(),
                warenausgang_lieferschein_tracking_ids.spedition = '" . $db->escape($tracking_carrier_key) . "',
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id = '" . $tracking_carrier_id . "',
                warenausgang_lieferschein_tracking_ids.tracking_id = '" . $db->escape($tracking_id) . "',
                warenausgang_lieferschein_tracking_ids.datum = '" . $date->format(DateObj::MYSQL_DATE) . "',
                warenausgang_lieferschein_tracking_ids.tracking_url = " . $db->quote($tracking_url) . "
        ");

        $date_now = new DateObj();
        $new_tracking_added = new NewTrackingAdded($lieferschein_id, $tracking_id, $tracking_carrier_id, $date_now);
        service_loader::getEventDispatcher()->dispatch($new_tracking_added);
    }

    /**
     * @param array $result
     * @return TrackingInformation[]
     */
    private static function buildTrackingInformations(array $result): array
    {
        $return = [];

        foreach ($result as $row) {
            $tracking = new TrackingInformation();
            $tracking->setLieferscheinId($row['lieferschein_id']);
            $tracking->setSpedition($row['spedition']);
            $tracking->setTrackingCarrierId($row['tracking_carrier_id']);
            $tracking->setTrackingCarrierName($row['tracking_carrier_name']);
            $tracking->setTrackingId($row['tracking_id']);

            if ($row['tracking_url']) {
                $tracking->setTrackingUrl($row['tracking_url']);
            } else {
                $tracking->setTrackingUrl(TrackingLinkBuilder::buildTrackingUrl($row['tracking_carrier_id'], $row['tracking_id']) ?? '');
            }

            if ($row['tracking_extra']) {
                $tracking->setTrackingExtra(Json::decode($row['tracking_extra']));
            }

            $return[] = $tracking;
        }

        return $return;
    }


    public static function getTrackingExtra(int $lieferschein_id, string $tracking_id): array
    {
        $db = db::getInstance();

        $tracking_extra = $db->fieldQuery("
            SELECT
                warenausgang_lieferschein_tracking_ids.tracking_extra
            FROM
                warenausgang_lieferschein_tracking_ids
            WHERE
                warenausgang_lieferschein_tracking_ids.lieferschein_id = $lieferschein_id AND
                warenausgang_lieferschein_tracking_ids.tracking_id = '" . $db->escape($tracking_id) . "'
        ");

        if ($tracking_extra === null) {
            throw new SmartDataEntityNotFoundException('tracking entity not found ($lieferschein_id: ' . $lieferschein_id . ', $tracking_id: ' . $tracking_id . ')');
        }

        if (!$tracking_extra) {
            return [];
        }

        return Json::decode($tracking_extra);
    }

    public static function saveTrackingExtra(int $lieferschein_id, string $tracking_id, array $tracking_extra): void
    {
        $tracking_extra_encoded = '';
        if ($tracking_extra) {
            $tracking_extra_encoded = json_encode($tracking_extra);
        }

        $db = db::getInstance();

        $db->query("
            UPDATE
                warenausgang_lieferschein_tracking_ids
            SET
                warenausgang_lieferschein_tracking_ids.tracking_extra = '" . $db->escape($tracking_extra_encoded) . "'                
            WHERE
                warenausgang_lieferschein_tracking_ids.lieferschein_id = $lieferschein_id AND
                warenausgang_lieferschein_tracking_ids.tracking_id = '" . $db->escape($tracking_id) . "'
        ");
    }
}
