<?php

namespace wws\Shipment;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Model\EntityChanges;
use db;
use env;
use service_loader;
use wws\Lager\LagerRepository;
use wws\Order\Event\EventOrderShipmentPriorityChanged;
use wws\Order\OrderConst;
use wws\Supplier\SupplierOrder;

class ShipmentPriority
{
    private int $product_id;
    private array $laeger;
    private bool $is_delivery;
    private db_generic $db;

    public function __construct(int $product_id)
    {
        $this->product_id = $product_id;

        $this->db = db::getInstance();

        $this->laeger = LagerRepository::getAllLager();

        $this->is_delivery = (bool)$this->db->fieldQuery("
            SELECT
                product_warenkorb_types.delivery
            FROM
                product INNER JOIN
                product_warenkorb_types ON (product.product_warenkorb_typ = product_warenkorb_types.product_warenkorb_typ)
            WHERE
                product.product_id = $product_id
        ");
    }

    private function getInventoryPointsPerWarehouse(): array
    {
        $inventory_points_per_warehouse = [];

        $lagerbestaende = $this->db->query("
            SELECT
                product_lager_detail.lager_id,
                product_lager_detail.lager_bestand
            FROM
                product_lager_detail
            WHERE
                product_lager_detail.product_id = '{$this->product_id}' AND
                product_lager_detail.lager_bestand > 0
        ")->asSingleArray('lager_id');

        foreach ($this->laeger as $lager_id => $lager_name) {
            if (!isset($lagerbestaende[$lager_id])) {
                continue;
            }

            $inventory_points_per_warehouse[$lager_id] = [];

            $inventory_point = new ShipmentPriorityInventoryPoint();
            $inventory_point->setLabel('Lagerbestand');
            $inventory_point->setOnStock(true);
            $inventory_point->setQuantity($lagerbestaende[$lager_id]);

            $inventory_points_per_warehouse[$lager_id][] = $inventory_point;
        }

        $result = $this->db->query("
            SELECT
                supplier_order.bestell_nr,
                supplier_order.delivery_date_min,
                supplier_order.lager_id,
                supplier_order_items.anzahl,

                supplier.supplier_name
            FROM
                supplier_order INNER JOIN
                supplier_order_items ON (supplier_order.supplier_order_id = supplier_order_items.supplier_order_id) INNER JOIN
                supplier ON (supplier.supplier_id = supplier_order.supplier_id)
            WHERE
                supplier_order_items.product_id = '{$this->product_id}' AND
                supplier_order.lager_id != 0 AND
                supplier_order.status = '" . SupplierOrder::STATUS_BESTELLT . "'
            ORDER BY
                supplier_order.delivery_date_min
        ");

        foreach ($result as $row) {
            $lager_id = $row['lager_id'];

            $inventory_point = new ShipmentPriorityInventoryPoint();
            $inventory_point->setLabel($row['supplier_name'] . ' ' . $row['bestell_nr'] . ' - Einheiten: ' . $row['anzahl']);
            $inventory_point->setOnStock(false);
            $inventory_point->setQuantity($row['anzahl']);
            $inventory_point->setDate($row['delivery_date_min']);

            if (!isset($inventory_points_per_warehouse[$lager_id])) {
                $inventory_points_per_warehouse[$lager_id] = [];
            }

            $inventory_points_per_warehouse[$lager_id][] = $inventory_point;
        }

        return $inventory_points_per_warehouse;
    }

    public function getInventoryPointsPerWarehouseWithOrders(): array
    {
        $inventory_points_per_warehouse = $this->getInventoryPointsPerWarehouse();

        $order_items_per_warehouse = $this->db->query("
            SELECT
                order_item.order_item_id,
                order_item.order_id,
                order_item.quantity,
                order_item.quantity_open,
                order_item.lager_id,
                order_item.versand_status AS old_verstand_status,
                order_item.versand_datum AS old_versand_datum
            FROM
                order_item INNER JOIN
                orders ON (orders.order_id = order_item.order_id)
            WHERE
                orders.customer_id != 1 AND
                order_item.product_id = '{$this->product_id}' AND
                order_item.status < " . OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST . " AND
                orders.order_aktiv = 1
            ORDER BY
                order_item.versand_prioritaet
        ")->addCallback(function (array $row) {
            $row['old_verstand_status'] = (int)$row['old_verstand_status'];

            return $row;
        })->asMultiArray('lager_id');

        if (!$order_items_per_warehouse) {
            return [];
        }

        foreach ($order_items_per_warehouse as $lager_id => $order_items) {
            $current_order_item = current($order_items);

            $quantity = 0;
            if (!$order_items) {
                continue;
            }

            if (isset($inventory_points_per_warehouse[$lager_id])) {
                foreach ($inventory_points_per_warehouse[$lager_id] as $inventory_point) {
                    /* @var ShipmentPriorityInventoryPoint $inventory_point */
                    $quantity += $inventory_point->getQuantity();

                    while ($current_order_item && $quantity >= $current_order_item['quantity_open']) {
                        $current_order_item['versand_status'] = $inventory_point->isOnStock() ? 1 : 0;
                        $current_order_item['versand_datum'] = $inventory_point->getDate();
                        $inventory_point->addOrderItemEntry($current_order_item);

                        $quantity -= $current_order_item['quantity_open'];

                        $current_order_item = next($order_items);
                    }
                }
            }

            if (!$current_order_item) {
                continue;
            }

            $inventory_point = new ShipmentPriorityInventoryPoint();
            $inventory_point->setDate(null);
            $inventory_point->setOnStock(false);
            $inventory_point->setLabel('Bedarf/Unbekannt');

            if (!isset($inventory_points_per_warehouse[$lager_id])) {
                $inventory_points_per_warehouse[$lager_id] = [];
            }
            $inventory_points_per_warehouse[$lager_id][] = $inventory_point;

            do {
                if (!$current_order_item) {
                    continue;
                }

                $current_order_item['versand_status'] = 0;
                $current_order_item['versand_datum'] = null;

                $inventory_point->addOrderItemEntry($current_order_item);
            } while ($current_order_item = next($order_items));
        }

        return $inventory_points_per_warehouse;
    }


    public function calc(): void
    {
        if (!$this->is_delivery) {
            return;
        }

        $inventory_points_per_warehouse = $this->getInventoryPointsPerWarehouseWithOrders();

        foreach ($inventory_points_per_warehouse as $inventory_points) {
            foreach ($inventory_points as $inventory_point) {
                /* @var ShipmentPriorityInventoryPoint $inventory_point */

                foreach ($inventory_point->getOrderItemEntries() as $order_item) {
                    $this->changeVersandStatus($order_item['order_id'], $order_item['order_item_id'], $order_item['versand_status'], $order_item['versand_datum'], $order_item['old_verstand_status'], $order_item['old_versand_datum']);
                }
            }
        }
    }

    /**
     * @param int $lager_id
     * @return ShipmentPriorityInventoryPoint[]
     */
    public function getInventoryPointsWithExtendedOrderData(int $lager_id): array
    {
        $inventory_points_per_warehouse = $this->getInventoryPointsPerWarehouseWithOrders();

        /* @var ShipmentPriorityInventoryPoint[] $inventory_points */
        $inventory_points = $inventory_points_per_warehouse[$lager_id] ?? [];

        $order_item_ids = [];

        foreach ($inventory_points as $inventory_point) {
            foreach ($inventory_point->getOrderItemEntries() as $order_data) {
                $order_item_ids[] = $order_data['order_item_id'];
            }
        }

        if (!$order_item_ids) {
            return $inventory_points;
        }

        $order_items_extended = $this->db->query("
            SELECT
                order_item.order_item_id,
                order_item.auftnr,

                einst_status.status_name,
                einst_status.status_color,

                orders.order_id,

                customers.customer_id,
                customers.shop_id,
                customers.name
            FROM
                order_item INNER JOIN
                orders ON (orders.order_id = order_item.order_id) INNER JOIN
                customers ON (orders.customer_id = customers.customer_id) INNER JOIN
                einst_status ON (order_item.status = einst_status.status_id)
            WHERE
                order_item.order_item_id IN (" . $this->db->in($order_item_ids) . ")
        ")->asArray('order_item_id');

        foreach ($inventory_points as $inventory_point) {
            $inventory_point->extendOrderItemEntries($order_items_extended);
        }

        return $inventory_points;
    }

    private function changeVersandStatus(int $order_id, int $order_item_id, int $versand_status, ?string $versand_datum, ?int $old_versand_status = null, ?string $old_versand_datum = null): void
    {
        static $stmt;

        if (!$stmt) {
            $stmt = $this->db->prepare("
                UPDATE
                    order_item
                SET
                    order_item.versand_status = :versand_status,
                    order_item.versand_datum = :versand_datum
                WHERE
                    order_item.order_item_id = :order_item_id
            ");
        }

        if ($versand_status != $old_versand_status || $versand_datum != $old_versand_datum) {
            $stmt->execute([
                'versand_status' => $versand_status,
                'versand_datum' => $versand_datum,
                'order_item_id' => $order_item_id
            ]);
        }

        if ($versand_status != $old_versand_status) {
            $entity_changes = new EntityChanges('orders');
            $entity_changes->setEntityId($order_id);
            $entity_changes->add('versand_prioritaet', (int)$versand_status, (int)$old_versand_status, 'item', (int)$order_item_id);
            $entity_changes->save();

            $event = new EventOrderShipmentPriorityChanged((int)$order_id, (int)$order_item_id, (int)$versand_status);
            service_loader::getEventDispatcher()->dispatch($event);
        }
    }

    /**
     * @param string $direction
     * @param int $order_item_id
     * @return void
     */
    public function changeVersandPrio(string $direction, int $order_item_id): void
    {
        $old_prio = $this->db->singleQuery("
            SELECT
                order_item.order_item_id,
                order_item.versand_prioritaet,
                order_item.product_id,

                order_item.auftnr
            FROM
                order_item
            WHERE
                order_item.order_item_id = '" . $order_item_id . "'
        ");

        switch ($direction) {
            case 'up':
                $new_prio = $this->db->singleQuery("
                    SELECT
                        order_item.order_item_id,
                        order_item.versand_prioritaet
                    FROM
                        order_item
                    WHERE
                        order_item.product_id = $old_prio[product_id] AND
                        order_item.versand_prioritaet < $old_prio[versand_prioritaet] AND
                        order_item.status < " . OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST . "
                    ORDER BY
                        order_item.versand_prioritaet DESC
                    LIMIT
                        1
                ");
                break;
            case 'down':
                $new_prio = $this->db->singleQuery("
                    SELECT
                        order_item.order_item_id,
                        order_item.versand_prioritaet
                    FROM
                        order_item
                    WHERE
                        order_item.product_id = $old_prio[product_id] AND
                        order_item.versand_prioritaet > $old_prio[versand_prioritaet] AND
                        order_item.status < " . OrderConst::STATUS_ZUSTELLUNG_AUSGELOEST . "
                    ORDER BY
                        order_item.versand_prioritaet ASC
                    LIMIT
                        1
                ");
                break;
            default:
                throw new DevException('fehler unbekannte direction');
        }

        if (!$new_prio) {
            return;
        }

        //prioritäten flippen
        $this->db->query("
            UPDATE
                order_item
            SET
                order_item.versand_prioritaet = $old_prio[versand_prioritaet]
            WHERE
                order_item.order_item_id = $new_prio[order_item_id]
        ");

        $this->db->query("
            UPDATE
                order_item
            SET
                order_item.versand_prioritaet = $new_prio[versand_prioritaet]
            WHERE
                order_item.order_item_id = $old_prio[order_item_id]
        ");

        $this->calc();
    }
}
