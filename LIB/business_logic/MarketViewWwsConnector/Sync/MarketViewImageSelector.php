<?php

namespace wws\MarketViewWwsConnector\Sync;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Image\PHasher;
use config;
use db;
use service_loader;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\ImportScripts\market_view_import_hersteller_bilder;
use wws\MarketView\MarketViewConst;
use wws\MarketView\MarketViewMediaCache;
use wws\MarketView\MarketViewMediaRepository;
use wws\MarketView\MarketViewProductCreator;
use wws\MarketViewWwsConnector\Matching\Product\ProductMatching;
use wws\Product\Product;
use wws\Product\ProductBrand\ProductBrandRepository;
use wws\Product\ProductConst;
use wws\Product\ProductMedia;
use wws\Product\ProductRepository;
use wws\Product\ProductRepositoryLegacy;

class MarketViewImageSelector
{
    /**
     * threshold für dne Vergleich der PHasher fingerprints. (0 = komplett unterschiedlich, 100 = identisch)
     * 80 ist der sweetspot wo noch gut zwischen den Bildern unterscheiden wird,
     * aber z.B. Wasserzeichen, leichte verzerrungen/rotationen toleriert werden.
     */
    protected int $threshold = 80;

    /** @var MarketViewImageSelectorImage[] */
    protected array $images = [];
    /** @var MarketViewImageSelectorImage[][] */
    protected array $image_groups = [];

    protected array $ignore_fingerprints = [];

    private array $ignore_fingerprints_default = [
        '74fcf9b9b8f8c0c0' => true, //leider kein bild vorhanden
        '80e8e9c9a5253434' => true, //leider kein bild vorhanden
        '0000000000000000' => true, //weiß oder annähernd weiße bilder,
        '0686096919390584' => true, //ek servicegroup Logo + "Platzhalter"
        '06061569197915c4' => true, //ek servicegroup Logo + "Platzhalter"
    ];

    public bool $ignore_duplicates = true;

    private db_generic $db;
    private db_generic $db_mv;

    private ?int $data_source_profile_id = null;

    private market_view_import_hersteller_bilder $market_view_import;

    public function __construct(db_generic $db, db_generic $db_mv)
    {
        $this->db = $db;
        $this->db_mv = $db_mv;
    }

    public function setDataSourceProfileId(?int $data_source_profile_id): void
    {
        $this->data_source_profile_id = $data_source_profile_id;
    }


    public function reset(): void
    {
        $this->images = [];
        $this->image_groups = [];
        $this->ignore_fingerprints = $this->ignore_fingerprints_default;
    }

    public function loadProduct(int $product_id): void
    {
        $this->reset();

        $this->loadByProductId($product_id);

        $this->loadIgnoreFingerprintsFromProduct($product_id);
        $this->addMarketViewMediaByProductId($product_id);

        $this->compare();
    }

    public function addImage(MarketViewImageSelectorImage $image): void
    {
        if (isset($this->ignore_fingerprints[$image->getFingerprint()])) {
            return;
        }

        $this->images[$image->getKey()] = $image;
    }


    protected function loadIgnoreFingerprintsFromProduct(int $product_id): void
    {
        $ignore_fingerprints = $this->db->fieldQuery("
            SELECT
                product.deleted_picture_fingerprints
            FROM
                product
            WHERE
                product.product_id = " . $product_id . "
        ");

        $ignore_fingerprints = explode("\n", $ignore_fingerprints);

        foreach ($ignore_fingerprints as $fingerprint) {
            $this->addIgnoreFingerprint($fingerprint);
        }
    }

    /**
     * ACHTUNG: wird reseted bei jedem durchlauf!
     * @see addIgnoreFingerprintDefault()
     */
    public function addIgnoreFingerprint(string $fingerprint): void
    {
        $this->ignore_fingerprints[$fingerprint] = true;
    }

    public function addIgnoreFingerprintDefault(string $fingerprint): void
    {
        $this->ignore_fingerprints_default[$fingerprint] = true;
    }

    protected function loadByProductId(int $product_id): void
    {
        $select = '';
        $from = '';
        if ($this->data_source_profile_id) {
            $from = " LEFT JOIN
                product_media_data_source_usage ON (product_media.product_media_id = product_media_data_source_usage.product_media_id AND product_media_data_source_usage.data_source_profile_id = " . $this->data_source_profile_id . ")";
            $select = ", product_media_data_source_usage.show_picture ";
        }

        $result = $this->db->query("
            SELECT
                product_media.pos,
                product_media.product_media_id,
                product_media.mv_media_id,
                product_media.fingerprint,
                product_media.md5_hash,
                product_media.width,
                product_media.height,
                product_media.data_source_id
                $select
            FROM
                product_media
                $from
            WHERE
                product_media.product_id = " . $this->db->quote($product_id) . " AND
                product_media.media_type = '" . ProductMedia::MEDIA_TYPE_PICTURE . "'
            ORDER BY
                product_media.pos
        ");

        foreach ($result as $row) {
            $row['img_url'] = config::system('https_url') . 'getimage.php?product_media_id=' . $row['product_media_id'] . '&size=org';

            $image = new MarketViewImageSelectorImage();
            $image->setType(MarketViewImageSelectorImage::TYPE_PRODUCT);
            $image->setFingerprint($row['fingerprint']);
            $image->setKey('product_' . $row['product_media_id']);
            $image->setMd5($row['md5_hash']);
            $image->setProductMediaId($row['product_media_id']);
            $image->setMvMediaId($row['mv_media_id'] ?: null);
            $image->setImgUrl($row['img_url']);

            $image->setWidth($row['width']);
            $image->setHeight($row['height']);
            $image->setDataSourceId($row['data_source_id']);

            if (isset($row['show_picture'])) {
                $image->setShowPicture($row['show_picture']);
            }

            //unklar
            //if (isset($meta['product_media_json'])) {
                //$image->setProductMediaJson($meta['product_media_json']);
            //}

            $image->initPrio();
            $image->modifyPrio(50); //dafür dass das ein Produktbild ist kleine boost
            if ($row['data_source_id'] === ProductConst::PRODUCT_DATA_SOURCE_DEG) {
                $image->modifyPrio(-80); //
            }

            $this->addImage($image);
        }
    }

    protected function addMarketViewMediaByProductId(int $product_id): void
    {
        $data_source_usage_allowed = $this->getDataSourceUsageByProductId($product_id);

        $result = $this->db_mv->query("
            SELECT
                " . MarketViewMediaRepository::getSqlFieldsForLoad() . ",

                market_view_source.image_data_source_id AS data_source_id
            FROM
                market_view_matching INNER JOIN
                market_view_media ON (market_view_matching.mvsrc_id = market_view_media.mvsrc_id AND market_view_matching.mvsrc_product_id = market_view_media.mvsrc_product_id) INNER JOIN
                market_view_source ON (market_view_media.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_matching.product_id = " . $product_id . " AND
                market_view_matching.matching_status != '" . ProductMatching::MATCHING_WRONG . "' AND
                market_view_media.media_status IN ('" . MarketViewMedia::MEDIA_STATUS_ONLINE . "') AND
                market_view_media.topic NOT IN ('" . MarketViewMedia::MEDIA_TOPIC_ICON . "', '" . MarketViewMedia::MEDIA_TOPIC_ENERGIE_LABEL . "') AND
                market_view_media.media_type IN ('" . MarketViewMedia::MEDIA_TYPE_IMAGE . "', '" . MarketViewMedia::MEDIA_TYPE_IMAGE_URL . "') AND
                market_view_media.fingerprint NOT IN ('', 'error') AND
                market_view_source.image_data_source_id IS NOT NULL
        ");

        $media_cache = service_loader::getDiContainer()->get(MarketViewMediaCache::class);

        $mv_product_creator = service_loader::get(MarketViewProductCreator::class);

        foreach ($result as $row) {
            $row['img_url'] = $media_cache->buildUrl($row['mvsrc_id'], $row['url_storage'] ?: $row['url']);

            $market_view_media = new MarketViewMedia();
            $market_view_media->fromArray($row);

            //damit das handling einheitlich ist, ziehen wir hier uns das, was auch beim normalen bild übernhemen passiert
            //media_topic_id etc...
            $product_media = $mv_product_creator->convertMarketViewMediaToProductMedia($market_view_media);


            $image = new MarketViewImageSelectorImage();
            $image->setType(MarketViewImageSelectorImage::TYPE_MARKET_VIEW);
            $image->setFingerprint($row['fingerprint']);
            $image->setKey('mv_' . $row['mv_media_id']);
            $image->setMd5($row['md5']);
            $image->setMvMediaId($row['mv_media_id']);
            $image->setImgUrl($row['img_url']);
            $image->setWidth($row['width']);
            $image->setHeight($row['height']);
            $image->setDataSourceId($row['data_source_id']);

            $image->setUrl($row['url']);
            $image->setUrlStorage($row['url_storage']);

            if ($data_source_usage_allowed !== null) {
                //logik auch nochmal dort \wws\Product\Media\ProductMediaDataSourceUsage::calcPictureUsage()
                //köse, aber aufwand/nutze abstraktion ist hier unsinng -> daher doppelt
                if (ProductRepositoryLegacy::isMediaTopicIgnoringDataSourceUsage($product_media->getMediaTopicId())) {
                    $image->setShowPicture(1);
                } else {
                    if (!isset($data_source_usage_allowed[$image->getDataSourceId()])) {
                        throw new FatalException('product_data_source_usage');
                    }
                    $image->setShowPicture($data_source_usage_allowed[$image->getDataSourceId()] ? 1 : 0);
                }

            }

            $image->setProductMediaJson($product_media->serializeData());

            $image->initPrio();
            if ($row['mvsrc_id'] == MarketViewConst::MVSRC_ID_FRANZ_KERSTIN) { //wegen teils grauen hintergrund und rechte schlechter als hersteller bild
                $image->modifyPrio(-200);
            }

            $this->addImage($image);
        }
    }

    public function countProductPictures(): int
    {
        $count = 0;
        foreach ($this->images as $image) {
            if ($image->getType() === $image::TYPE_PRODUCT) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Wenn die $data_source_profile_id gesetzt wurde, liefert das die Anazhl der Bilder die dafür freigegeben sind
     *
     * @return int
     * @throws FatalException
     */
    public function countProductPicturesWithRights(array $excluded_data_source_ids = []): int
    {
        if (!$this->data_source_profile_id) {
            throw new FatalException('data_source_profile_id is not set');
        }

        $count = 0;
        foreach ($this->images as $image) {
            if ($image->getType() !== $image::TYPE_PRODUCT) {
                continue;
            }

            if (in_array($image->getDataSourceId(), $excluded_data_source_ids)) {
                continue;
            }

            $show_picture = $image->getShowPicture();
            if ($show_picture === null) {
                throw new DevException('show_picture is missing');
            }

            if ($show_picture == 1) {
                $count++;
            }
        }

        return $count;
    }

    public function compare(): void
    {
        if (!$this->images) {
            return;
        }

        foreach ($this->images as $image) {
            if ($image->getFingerprint() === '') {
                throw new FatalException('fingerprint is missing');
            }
        }

        $picture_group = 1;

        $hasher = PHasher::Instance();

        $compares = $this->images;

        foreach ($this->images as $image) {
            $fingerprint = $image->getFingerprint();

            foreach ($compares as $c_key => $c_image) {
                if ($image === $c_image) {
                    continue;
                }

                $c_fingerprint = $c_image->getFingerprint();

                if ($c_image->getMd5() === $image->getMd5()) {
                    $image->addSimilar($c_key);
                    continue;
                }

                $hash_difference = $hasher->CompareHashs($fingerprint, $c_fingerprint);

                if ($hash_difference > $this->threshold) {
                    $image->addSimilar($c_key);
                }
            }

            if ($image->getImageGroup()) {
                continue;
            }

            if (empty($image->getSimilar())) {
                $image->setImageGroup($picture_group++);
            } else {
                foreach ($image->getSimilar() as $c_key) {
                    if ($this->images[$c_key]->getImageGroup()) {
                        $image->setImageGroup($this->images[$c_key]->getImageGroup());
                        break;
                    }
                }

                if (!$image->getImageGroup()) {
                    $image->setImageGroup($picture_group++);

                    foreach ($image->getSimilar() as $c_key) {
                        $this->images[$c_key]->setImageGroup($image->getImageGroup());
                    }
                }
            }
        }

        //bilder gruppieren.
        $this->image_groups = [];

        foreach ($this->images as $key => $image) {
            $group = $image->getImageGroup();
            if (!isset($this->image_groups[$group])) {
                $this->image_groups[$group] = [];
            }

            $this->image_groups[$group][$key] = $image;
        }
    }

    public function showPictureGroups(array $commands = []): void
    {
        $image_actions = [];

        foreach ($commands as $command) {
            switch ($command['command']) {
                case 'add':
                    $image_actions[$command['picture']->getKey()] = '<span class="label label-success">hinzufügen</span>';
                    break;
                case 'replace':
                    $image_actions[$command['dst_picture']->getKey()] = '<span class="label label-success">hinzufügen (ersetzt bestehendes Bild)</span>';;
                    $image_actions[$command['src_picture']->getKey()] = '<span class="label label-warning">wird ersetzt</span>';
                    break;
                case 'remove':
                    $image_actions[$command['picture']->getKey()] = '<span class="label label-danger">löschen</span>';
                    break;
            }
        }

        foreach ($this->image_groups as $image_group => $images) {
            uasort($images, function ($p1, $p2) {
                if ($p1->getType() === $p1::TYPE_PRODUCT && $p2->getType() !== $p2::TYPE_PRODUCT) {
                    return -1;
                }
                if ($p1->getType() !== $p1::TYPE_PRODUCT && $p2->getType() === $p2::TYPE_PRODUCT) {
                    return 1;
                }
                return $p2->getPrio() <=> $p1->getPrio();
            });

            echo '<div style="border: 5px solid black; margin: 10px; padding: 10px;">';
            echo '<h1>' . $image_group . '</h1>';

            foreach ($images as $image) {
                echo '<img src="' . $image->getImgUrl() . '" style="max-height: 200px; max-width: 200px;">';
                echo '<br>';

                if (isset($image_actions[$image->getKey()])) {
                    echo $image_actions[$image->getKey()];
                    echo '<br><br>';
                }

                if ($image->getType() == $image::TYPE_PRODUCT) {
                    echo '<b>Produkt</b>';
                } elseif ($image->getType() === $image::TYPE_MARKET_VIEW) {
                    echo '<b>MarketView</b>';
                } else {
                    echo $image->getType();
                }
                echo '<br>';

                if ($this->data_source_profile_id) {
                    $show_picture = $image->getShowPicture();

                    $text = 'unbekannt ?!?!?';
                    if ($show_picture === 1) {
                        $text = '<b style="color: green;">ja</b>';
                    } elseif ($show_picture === 0) {
                        $text = '<b style="color: red;">nein</b>';
                    }

                    echo 'anzeigen möglich? (' . $this->data_source_profile_id . '): ' . $text;
                    echo '<br>';
                }

                echo $image->getKey();
                echo '<br>';
                echo $image->getWidth() . 'x' . $image->getHeight();
                echo '<br>';
                echo 'Datenquelle: ' . $image->getDataSourceId();
                echo '<br>';
                echo 'Prio: ' . $image->getPrio();
                echo '<br>';
                echo 'Fingerprint: ' . $image->getFingerprint();

                if ($image->getMvMediaId()) {
                    echo '<br>';
                    echo 'mv_media_id: ' . $image->getMvMediaId();
                }

                if ($image->getType() == $image::TYPE_MARKET_VIEW) {
                    echo '<br>';
                    echo 'url: ' . $image->getUrl();
                    echo '<br>';
                    echo 'url_storage: ' . $image->getUrlStorage();
                }
                echo '<hr>';
            }

            echo '</div>';
        }
    }

    public function getCommands(): array
    {
        $commands = [];

        foreach ($this->image_groups as $image_group => $images) {
            uasort($images, fn($p1, $p2) => $p2->getPrio() <=> $p1->getPrio());

            //dubletten entfernen
            $count = 0;
            foreach ($images as $key => $image) {
                if ($image->getType() === $image::TYPE_PRODUCT) {
                    $count++;

                    if ($count > 1) {
                        if (!$this->ignore_duplicates) {
                            $commands[] = ['command' => 'remove', 'reason_text' => 'duplicate', 'picture' => $image, 'image_group' => $image_group];
                        }
                        unset($images[$key]);
                    }
                }
            }

            //bild ersetzen
            if (count($images) > 1) {
                $i = 0;
                $replace = null;

                foreach ($images as $image) {
                    if ($i++ === 0) {
                        //wenn das erste bild (höchste prio) ein Produktbild ist, gibt es nix zu tun
                        if ($image->getType() === $image::TYPE_PRODUCT) {
                            break;
                        }

                        //wenn das bild mit der hächsten prio nicht anzeigbar ist, dann auch nix machen (->prio muss regeln, dass das entsprechend abgewertet wird!)
                        if ($image->getShowPicture() === 0) {
                            break;
                        }

                        $replace = $image;
                    } elseif ($image->getType() === $image::TYPE_PRODUCT) {
                        //da die prios manipuliert werden können, sicherstellen, dass wir nix mit dem gleichen bild ersetzen
                        if ($image->getMvMediaId() && $image->getMvMediaId() === $replace->getMvMediaId()) {
                            break;
                        }

                        $commands[] = ['command' => 'replace', 'reason_text' => 'better market_view image', 'src_picture' => $image, 'dst_picture' => $replace, 'image_group' => $image_group];
                        break;
                    }
                }
            }

            //neues bild hinzufügen
            if ($count == 0) {
                foreach ($images as $image) {
                    $commands[] = ['command' => 'add', 'reason_text' => 'no image', 'picture' => $image, 'image_group' => $image_group];
                    break;
                }
            }
        }

        return $commands;
    }

    public function countCommands(array $commands): array
    {
        $result = [
            'add' => 0,
            'replace' => 0,
            'remove' => 0
        ];

        foreach ($commands as $command) {
            $result[$command['command']]++;
        }

        return $result;
    }


    public function isType(int $id, string $type): bool
    {
        return $this->images[$id]->getType() == $type;
    }

    public function executeCommandsForProductId(int $product_id, array $commands = null): void
    {
        if ($commands === null) {
            $commands = $this->getCommands();
        }

        if (!$commands) {
            return;
        }

        $product = service_loader::get(ProductRepository::class)->loadProduct($product_id);

        foreach ($commands as $command) {
            $product->setEditReason('media_collector');

            switch ($command['command']) {
                case 'add':
                    /* @var $image MarketViewImageSelectorImage */
                    $image = $command['picture'];

                    $product_media = new ProductMedia();
                    $product_media->setDataSourceId($image->getDataSourceId());
                    $product_media_json = $image->getProductMediaJson();
                    if ($product_media_json) {
                        $product_media->serializeDataLoad($product_media_json);
                    }

                    $mv_media_id = $image->getMvMediaId();
                    if ($mv_media_id) {
                        $product_media->setMvMediaId($mv_media_id);
                    }

                    $product->addAsyncMedia($image->getImgUrl(), $product_media);
                    break;
                case 'replace':
                    /* @var $src_image MarketViewImageSelectorImage */
                    /* @var $dst_image MarketViewImageSelectorImage */
                    $src_image = $command['src_picture'];
                    $dst_image = $command['dst_picture'];

                    $src_media = $product->getMedia($src_image->getProductMediaId());
                    $src_media->del();

                    //bild in marketview
                    $this->backupPictureToMarketView($product, $src_media);

                    $product_media = new ProductMedia();
                    $product_media_json = $dst_image->getProductMediaJson();
                    if ($product_media_json) {
                        $product_media->serializeDataLoad($product_media_json);
                    }

                    $mv_media_id = $dst_image->getMvMediaId();
                    if ($mv_media_id) {
                        $product_media->setMvMediaId($mv_media_id);
                    }

                    $product_media->setBeschreibung($src_media->getBeschreibung());
                    $product_media->setPos($src_media->getPos());

                    $product->addAsyncMedia($dst_image->getImgUrl(), $product_media);
                    break;
                case 'remove':
                    /* @var $image MarketViewImageSelectorImage */
                    $image = $command['picture'];

                    $product_media = $product->getMedia($image->getProductMediaId());
                    $product_media->del();
                    break;
            }
        }

        $product->save();
    }

    public function backupPictureToMarketView(Product $product, ProductMedia $product_media): void
    {
        if ($product_media->getDataSourceId() !== ProductConst::PRODUCT_DATA_SOURCE_HERSTELLER) {
            return;
        }

        $storage = service_loader::getStorageFactory()->get('media');

        $raw_image = $storage->read('media://artimages/' . $product_media->getFilename());

        $storage_url = 'local://extern/hersteller_bilder/' . $product_media->getFilename();

        $market_view_import = $this->getMediaImportForBackup();
        $market_view_import->addPicture($product->getProductId(), $product->getProductName(), ProductBrandRepository::getBrandName($product->getBrandId()), $product->getEan(), $raw_image, $storage_url);
    }

    private function getMediaImportForBackup(): market_view_import_hersteller_bilder
    {
        if (!isset($this->market_view_import)) {
            $this->market_view_import = new market_view_import_hersteller_bilder([
                'mvsrc_id' => MarketViewConst::MVSRC_ID_HERSTELLER_BILDER,
                'db_mv' => db::getInstance('market_view')
            ]);
        }

        return $this->market_view_import;
    }

    private function getDataSourceUsageByProductId(int $product_id): ?array
    {
        if (!$this->data_source_profile_id) {
            return null;
        }

        return $this->db->query("
            SELECT
                product_data_source_usage.data_source_id,
                product_data_source_usage.usage_allowed
            FROM
                product INNER JOIN
                product_data_source_usage ON (product.brand_id = product_data_source_usage.brand_id)
            WHERE
                product.product_id = " . $product_id . " AND
                product_data_source_usage.data_source_profile_id = " . $this->data_source_profile_id . "
        ")->addCallback(function (array $row) {
            $row['usage_allowed'] = (int)$row['usage_allowed'];
            return $row;
        })->asSingleArray('data_source_id');
    }
}
