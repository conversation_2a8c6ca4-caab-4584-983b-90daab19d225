<?php

namespace wws\ShopBackend\Elasticsearch;

use bqp\Config\ConfigContainer;
use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\EntityQueue\EntityQueueRunnerCollection;
use bqp\Exceptions\DevException;
use bqp\Json;
use bqp\Utils\ArrayUtils;
use debug;
use InvalidArgumentException;
use Laminas\Http\Client;
use wws\Product\ProductConst;
use wws\Product\ProductExtraServices;
use wws\Product\ProductFeature\ProductFeatureCache;
use wws\ProductCat\ProductCatRepository;

class ElasticsearchProductIndexer
{
    protected db_generic $db;
    protected int $throttling = 0;
    private static array $bool_fields = ['filter_list_status', 'expressable', 'ad_price_strike', 'not_orderable', 'grundpreis_aktiv'];

    /**
     * Limitiert die Produkte die maximal auf einmal aus der Datenbank geladen werden
     */
    private int $batch_size = 500;

    /**
     * Limitiert die Produkte die maximal auf einmal an Elasticsearch gegeben werden
     */
    private int $batch_size_elasticsearch = 50;

    private string $host_url;

    // config
    private array $price_group_ids = [];
    private string $index;
    private int $shop_id;
    private int $cat_tree_id;
    private int $filter_list_id;
    private string $product_detail_lang;

    private bool $sparepartfulltext_enabled = true;

    public function __construct(ConfigContainer $config, db_generic $db)
    {
        $this->db = $db;
        $this->db->query("SET SESSION group_concat_max_len = ********;");

        $this->host_url = 'http://' . current($config['catalog']['hosts']); //"random" node auswählen

        $this->price_group_ids = $config->getArray('price_group_ids');
        if (!$this->price_group_ids) {
            throw new InvalidArgumentException('config value price_group_ids must be filled');
        }

        if (!$config->existsAndTrue('product_sparepart_fulltexts_enabled')) {
            $this->sparepartfulltext_enabled = false;
        }

        $index = $config['catalog']['index'];
        if (isset($config['catalog']['indexer_override_index'])) {
            $index = $config['catalog']['indexer_override_index'];
        }
        $this->index = $index;
        $this->shop_id = $config->getInt('shop_id');
        $this->cat_tree_id = $config->getInt('cat_tree_id');
        $this->filter_list_id = $config->getInt('filter_list_id');
        $this->product_detail_lang = $config->offsetGet('product_detail_lang');
    }

    public function entityQueueConsumer(EntityQueueRunnerCollection $collection): void
    {
        $collection->setExecutionDate(new DateObj());

        $product_ids = $collection->getEntityIds();

        $this->indexProductIds($product_ids);

        $collection->markAllAsProcessed();
    }

    public function getIndex(): string
    {
        return $this->index;
    }

    public function setThrottling(int $throttling): void
    {
        $this->throttling = $throttling;
    }

    /**
     * @param int[] $product_ids
     */
    private function indexProductIdBatches(array $product_ids): void
    {
        $result = $this->db->query(
            $this->getSql("product.product_id IN ({$this->db->makeIn($product_ids)})")
        )->asArray();

        $sparepartfulltext_data = $this->sparepartfulltext_enabled ? $this->getSparepartfulltextData($product_ids) : [];
        $order_codes = $this->getOrderCodes($product_ids);

        $this->processResult($result, $sparepartfulltext_data, $order_codes);
    }

    /**
     * @param int[] $product_ids
     */
    public function indexProductIds(array $product_ids): void
    {
        $chunks = ArrayUtils::split($product_ids, $this->batch_size);

        foreach ($chunks as $product_id_batch) {
            $this->indexProductIdBatches($product_id_batch);
        }
    }

    /**
     *
     * @param bool $only_active synchronisiert nur Produkte die im Shop aktiv (filter_list) sind
     * @return void
     */
    public function indexAll(bool $only_active = false): void
    {
        $max_product_id = $this->db->fieldQuery("
            SELECT
                MAX(product.product_id) 
            FROM
                product
        ");

        $only_active_sql = $only_active ? "AND product_filter_list_results.status = 1" : "";

        for ($product_id_start = 0; $product_id_start <= $max_product_id; $product_id_start += $this->batch_size) {
            $product_id_end = $product_id_start + $this->batch_size;

            $where = "product.product_id BETWEEN {$product_id_start} AND {$product_id_end} $only_active_sql";

            $result = $this->db->query($this->getSql($where))->asArray();

            $sparepartfulltext_data = $this->sparepartfulltext_enabled ? $this->getSparepartfulltextData($where) : [];
            $order_codes = $this->getOrderCodes($where);

            $this->processResult($result, $sparepartfulltext_data, $order_codes);
        }
    }

    public function getAllProductIds(): array
    {
        return $this->db->query("
            SELECT
                t.product_id
            FROM
                ({$this->getSql()}) AS t
        ")->asSingleArray();
    }

    protected function processResult(array $result, array $sparepartfulltext_data, array $order_codes): void
    {
        $client = new Client($this->host_url . '/_bulk', [
            'timeout' => 1200
        ]);

        $product_ids = ArrayUtils::extract($result, 'product_id');

        if (!$product_ids) {
            return;
        }

        $this->prefetchData($product_ids);

        $result = ArrayUtils::split($result, $this->batch_size_elasticsearch);

        foreach ($result as $sub_result) {
            $doc = '';

            foreach ($sub_result as $daten) {
                $price_groups_json = $daten['price_groups'];
                $daten['price_groups'] = [];

                if (!empty($price_groups_json)) {
                    $price_groups = Json::decode($price_groups_json);
                    foreach ($price_groups as $price_group) {
                        if ($price_group['price_group_id'] !== null) {
                            $daten['price_groups'][$price_group['price_group_id']] = $price_group;
                        }
                    }
                }

                $daten['is_sparepart'] = $daten['product_type'] === ProductConst::PRODUCT_TYPE_XET;

                $daten['price_groups'] = json_encode($daten['price_groups']);

                // brands
                $daten['brand_ids'] = explode(';', $sparepartfulltext_data[$daten['product_id']]['device_brand_ids'] ?? '');
                $daten['brand_ids'][] = $daten['brand_id'];

                // filter null, make values unique, cast to int, and reindex
                $daten['brand_ids'] = array_values(array_map(fn($brand_id) => (int)$brand_id, array_unique(array_filter($daten['brand_ids']))));
                if (!$daten['brand_ids']) {
                    $daten['brand_ids'] = null;
                    $daten['brands'] = '';
                } else {
                    $daten['brands'] = $this->brandIdsToBrandName($daten['brand_ids']);
                }

                if (isset($order_codes[$daten['product_id']])) {
                    $daten['bestellcodes'] = $order_codes[$daten['product_id']];
                }

                if (isset($sparepartfulltext_data[$daten['product_id']]['device_ids'])) {
                    $daten['device_ids'] = explode(';', $sparepartfulltext_data[$daten['product_id']]['device_ids']);
                }

                if (isset($sparepartfulltext_data[$daten['product_id']]['devices'])) {
                    $daten['devices'] = $sparepartfulltext_data[$daten['product_id']]['devices'];
                }

                $daten['cat_ids'] = explode(';', $daten['cat_ids']);

                $temp = explode(',', $daten['cat_ids_closure_list']);
                $temp = array_unique(array_filter($temp));
                $daten['cat_ids_closure_list'] = [];
                foreach ($temp as $cat_id) {
                    $cat_id = (int)$cat_id;
                    if ($cat_id) {
                        $daten['cat_ids_closure_list'][] = $cat_id;
                    }
                }

                $daten['cat_ids_ebene_1'] = explode(';', $daten['cat_ids_ebene_1']);
                $daten['cat_ids_ebene_2'] = explode(';', $daten['cat_ids_ebene_2']);
                $daten['cat_ids_ebene_3'] = explode(';', $daten['cat_ids_ebene_3']);
                $daten['cat_ids_ebene_4'] = explode(';', $daten['cat_ids_ebene_4']);

                $daten['cat_paths'] = [];
                foreach ($daten['cat_ids'] as $cat_id) {
                    $daten['cat_paths'][] = ProductCatRepository::getShopCatPathAsString($cat_id, '/');
                }

                if ($daten['goodie_ids']) {
                    $daten['goodie_ids'] = explode(';', $daten['goodie_ids']);
                }

                $keywords = [];

                $temp = explode(';', $daten['cat_keywords'] ?? '');
                foreach ($temp as $keyword) {
                    $keywords[] = $keyword;
                }
                unset($daten['cat_keywords']);

                $temp = explode("\n", $daten['product_keywords']);
                foreach ($temp as $keyword) {
                    $keywords[] = $keyword;
                }
                unset($daten['product_keywords']);

                if ($keywords) {
                    $daten['keywords'] = implode(' ', $keywords);
                }

                $daten['features'] = [];
                if ($daten['features_raw']) {
                    $daten['features'] = $this->convertFeatures($daten['features_raw']);
                }
                if ($daten['features_unbound']) {
                    $daten['features_unbound'] = json_decode($daten['features_unbound'], true);
                }
                $daten['features']['feature_brand_name'] = $daten['brand_name'];

                $daten['set_product_ids'] = $this->getSetProductIds($daten['product_id']);

                $daten['associations'] = $this->getAssociations($daten['product_id']);
                $daten['average_product_rating'] = $this->getAverageProductRating($daten['product_id']);
                $daten['total_count_product_rating'] = $this->getTotalCountProductRating($daten['product_id']);

                if ($daten['extra_services']) {
                    $daten['extra_services'] = explode(',', $daten['extra_services']);

                    // sorting
                    $extra_services = ProductExtraServices::getExtraServicesExtended($daten['extra_services'], true);
                    foreach ($extra_services as $extra_service_id => $extra_service) {
                        if (trim($extra_service['service_dependencies'])) {
                            $extra_service['service_dependencies'] = explode(',', $extra_service['service_dependencies']);

                            foreach ($extra_service['service_dependencies'] as $i => $dependency_id) {
                                if (!isset($extra_services[$dependency_id])) {
                                    unset($extra_service['service_dependencies'][$i]);
                                }
                            }
                        } else {
                            $extra_service['service_dependencies'] = [];
                        }

                        $extra_services[$extra_service_id] = $extra_service;
                    }

                    uasort($extra_services, function (array $service1, array $service2) {
                        if (in_array($service2['service_id'], $service1['service_dependencies'])) {
                            return 1;
                        }

                        if (in_array($service1['service_id'], $service2['service_dependencies'])) {
                            return -1;
                        }

                        return 0;
                    });

                    $daten['extra_services'] = array_column($extra_services, 'service_id');
                } else {
                    $daten['extra_services'] = [];
                }

                $daten['paketfaehig'] = (int)$daten['paketfaehig'];

                foreach (self::$bool_fields as $key) {
                    if (array_key_exists($key, $daten)) {
                        $daten[$key] = (bool)$daten[$key];
                    }
                }

                $doc .= sprintf(
                    '{ "index" : { "_index" : "%s", "_id" : "%s" } }',
                    $this->index,
                    $daten['product_id']
                );

                $doc .= "\n";
                $doc .= json_encode($daten) . "\n";
            }
            $client->setMethod('POST');
            $client->setEncType('application/json');
            $client->setRawBody($doc);
            $response = $client->send();

            if (!$this->isStatusOk($response->getStatusCode())) {
                //\debug::dumpJson($response->getBody());
                throw new DevException('fehler bei indizieren/elasticsearch');
            }

            $body = $response->getBody();
            $result = Json::decode($body, false);

            if ($result->errors) {
                foreach ($result->items as $item) {
                    if (!$this->isStatusOk($item->index->status)) {
                        debug::dump($item);
                    }
                }
            }

            if ($this->throttling) {
                sleep($this->throttling);
            }
        }
    }

    /**
     * @param $status
     * @return bool
     */
    private function isStatusOk($status): bool
    {
        return $status == 200 || $status == 201;
    }

    /**
     * @param string $where_product_id
     * @return string
     */
    protected function getSql(string $where_product_id = null): string
    {
        if ($where_product_id) {
            $where_product_id = "AND {$where_product_id}";
        }

        return "SELECT
                product.product_id,
                product.product_nr,
                product.product_name AS product_name_org,

                IF(product_details_local.product_name_localized != '', product_details_local.product_name_localized, product.product_name) AS product_name,
                product.ean,
                product.mpn,
                product.product_shop_url,
                product.product_warenkorb_typ,
                DATE(product.date_create) AS date_create,
                product.uvp,
                product.paketfaehig,
                product.eek,
                product.eek_scale_id,
                product.eek_label_product_media_id,
                product.eek_fiche_product_media_id, 
                product_media_eek_fiche.media_type AS eek_fiche_media_type,
                product.grundpreis_aktiv,
                product.grundpreis_faktor,
                product.grundpreis_einheit,
                product.product_type,
                product.brand_id,
                product_brand.brand_name,
                product_brand.brand_logo,
                
                IF(product_details_local.artikelinfo != '', product_details_local.artikelinfo, product_details.artikelinfo) AS beschreibung,
                IF(product_details_local.artikelinfo != '', product_details_local.lieferumfang, product_details.lieferumfang) AS lieferumfang,
                product_details.color,
                IF(product_details_local.artikelinfo != '', product_details_local.artikelinfo_style, product_details.artikelinfo_style) AS artikelinfo_style,
                IF(
                    product_details_local.teaser IS NOT NULL AND product_details_local.teaser != '',
                    product_details_local.teaser, 
                    IF(product_cat.cat_name_webshop != '', product_cat.cat_name_webshop, product_cat.cat_name)
                ) AS teaser,
    
                IF(product_details_local.meta_title != '', product_details_local.meta_title, product_details.meta_title) AS meta_title,
                IF(product_details_local.meta_description != '', product_details_local.meta_description, product_details.meta_description) AS meta_description,
                
                product_details.keywords AS product_keywords,
                GROUP_CONCAT(DISTINCT product_cat.cat_id_ebene_1 SEPARATOR ';') AS cat_ids_ebene_1,
                GROUP_CONCAT(DISTINCT product_cat.cat_id_ebene_2 SEPARATOR ';') AS cat_ids_ebene_2,
                GROUP_CONCAT(DISTINCT product_cat.cat_id_ebene_3 SEPARATOR ';') AS cat_ids_ebene_3,
                GROUP_CONCAT(DISTINCT product_cat.cat_id_ebene_4 SEPARATOR ';') AS cat_ids_ebene_4,
                GROUP_CONCAT(DISTINCT product_cat.cat_id SEPARATOR ';') AS cat_ids,
                GROUP_CONCAT(DISTINCT product_cat.cat_id_path SEPARATOR ',') AS cat_ids_closure_list,

                GROUP_CONCAT(DISTINCT product_goodies_product.goodie_id SEPARATOR ';') AS goodie_ids,
    
                product.product_score,
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'price_group_id', product_prices.price_group_id,
                        'vk_brutto', product_prices.vk_brutto
                    )
                ) AS price_groups,

                product_shop.vk_brutto,
                product_shop.mwst_satz,
                product_shop.lieferbaranzeige,
                product_shop.expressable,
                product_shop.ad_price_strike,
                IF(product_shop.extra_services_manuell = 1, product_shop.extra_services, product_cat.extra_services) AS extra_services,
                product_shop.not_orderable,
                product_shop.versandkosten_min AS min_versand,
                product_shop.versand_id,
                product_shop.show_product_media_id AS product_media_id,
                product_shop.shop_template,
                product_filter_list_results.status AS filter_list_status,

                IF(product_cat.cat_name_webshop != '', product_cat.cat_name_webshop, product_cat.cat_name) AS cat_name,
                product_cat.cat_id,
                product_cat.cat_shop_url,
                GROUP_CONCAT(DISTINCT product_cat_search_terms.search_term SEPARATOR ';') AS cat_keywords,
                product_feature_cache.features AS features_raw,
                product_details.features_unbound
            FROM
                product_cat INNER JOIN
                product_cat_product_mapping ON (product_cat.cat_id = product_cat_product_mapping.cat_id) INNER JOIN
                product ON (product.product_id = product_cat_product_mapping.product_id) LEFT JOIN
                product_brand ON (product.brand_id = product_brand.brand_id) INNER JOIN
                product_filter_list_results ON (product_filter_list_results.filter_list_id = " . $this->filter_list_id . "
                                            AND product_filter_list_results.product_id = product.product_id) INNER JOIN
                product_shop ON (product.product_id = product_shop.product_id
                             AND product_shop.shop_id = " . $this->shop_id . ") INNER JOIN
                product_details ON (product.product_id = product_details.product_id AND product_details.lang = '" . ProductConst::PRODUCT_LANG_DEFAULT . "') LEFT JOIN
                product_details AS product_details_local ON (product.product_id = product_details_local.product_id
                                                         AND product_details_local.lang = '" . $this->product_detail_lang . "') LEFT JOIN
                product_cat_search_terms ON (product_cat.cat_id = product_cat_search_terms.cat_id) LEFT JOIN
                product_feature_cache ON (product_feature_cache.product_id = product.product_id AND product_feature_cache.product_feature_cache_profile = '" . ProductConst::PRODUCT_FEATURE_CACHE_PROFILE_SHOP . "') LEFT JOIN
                product_prices ON ( product_prices.product_id = product.product_id
                                AND product_prices.price_active = '1'
                                AND product_prices.price_group_id IN(" . implode(',', $this->price_group_ids) . ")) LEFT JOIN
                product_media AS product_media_eek_fiche ON (product_media_eek_fiche.product_media_id = product.eek_fiche_product_media_id) LEFT JOIN
                product_goodies_product ON (product_goodies_product.product_id = product.product_id)
            WHERE
                product_cat.cat_tree_id = " . $this->cat_tree_id . "
                $where_product_id
            GROUP BY
                product.product_id
        ";
    }

    protected function convertFeatures(?string $features_raw): array
    {
        if (!$features_raw) {
            return [];
        }

        $return = [];

        $features = ProductFeatureCache::unserializeFlatten($features_raw);
        foreach ($features as $feature) {
            if (!$feature['feature_search']) {
                continue;
            }

            $return['feature_' . $feature['feature_id']] = $feature['feature_value_format'];
        }

        return $return;
    }


    /**
     * @param array $brand_ids
     * @return array
     */
    protected function brandIdsToBrandName(array $brand_ids): array
    {
        static $cache = null;
        if ($cache === null) {
            $cache = $this->db->query("
                SELECT
                    product_brand.brand_id,
                    product_brand.brand_name
                FROM
                    product_brand
            ")->asSingleArray('brand_id');
        }

        $result = [];

        foreach ($brand_ids as $brand_id) {
            if (isset($cache[$brand_id])) {
                $result[] = $cache[$brand_id];
            }
        }

        return $result;
    }

    protected $associations = [];
    protected $product_sets = [];
    protected $product_ratings = [];

    /**
     * @param array $product_ids
     */
    protected function prefetchData(array $product_ids): void
    {
        //zubehör laden
        $this->associations = [];

        $products = $this->db->query("
            SELECT
                product_association.product_id,
                product_association.dst_product_id,
                product_association.association_type_id
            FROM
                product_association
            WHERE
                product_association.product_id IN (" . $this->db->in($product_ids) . ")
        ")->asMultiArray('product_id');

        foreach ($products as $product_id => $result) {
            $this->associations[$product_id] = [];
            foreach ($result as $row) {
                $this->associations[$product_id][] = [
                    'product_id' => (int)$row['dst_product_id'],
                    'association_type_id' => (int)$row['association_type_id']
                ];
            }
        }

        //sets laden
        $this->product_sets = $this->db->query("
                SELECT
                    product_set_items.product_id AS set_product_id,
                    product_set_items.node_product_id AS product_id
                FROM
                    product_set_items
                WHERE
                    product_set_items.product_id IN (" . $this->db->in($product_ids) . ")
            ")->asMultiArraySingle('set_product_id', 'product_id');

        // Produktbewertungen laden
        $ratings_statement = $this->db->prepare('
            SELECT
               product.product_id,
               AVG(product_rating.rating) AS average_product_rating,
               COUNT(product_rating.product_id) as total_count_product_rating
            FROM
                product
            JOIN
                product_rating ON (product.product_id=product_rating.product_id)
            WHERE
                product.product_id IN (' . $this->db->in($product_ids) . ') AND
                product_rating.shop_id = :shop_id AND
                product_rating.status = 1
            GROUP BY
                product_rating.product_id
        ');

        $this->product_ratings = $ratings_statement->execute([
            'shop_id' => $this->shop_id
        ])->asArray('product_id');
    }

    /**
     * @param $product_id
     * @return array|mixed
     */
    protected function getSetProductIds($product_id)
    {
        return $this->product_sets[$product_id] ?? [];
    }

    /**
     * @param $product_id
     * @return array|mixed
     */
    protected function getAssociations($product_id)
    {
        return $this->associations[$product_id] ?? [];
    }

    protected function getAverageProductRating(int $product_id): int
    {
        $average_product_rating = $this->product_ratings[$product_id]['average_product_rating'] ?? 0;
        return (int)round($average_product_rating, 0, PHP_ROUND_HALF_UP);
    }

    protected function getTotalCountProductRating(int $product_id): int
    {
        $total_count_product_rating = $this->product_ratings[$product_id]['total_count_product_rating'] ?? 0;
        return (int)$total_count_product_rating;
    }


    private function getSparepartfulltextData(array|string $sql_or_product_ids): array
    {
        if (!$sql_or_product_ids) {
            return [];
        }

        if (is_string($sql_or_product_ids)) {
            $where = $sql_or_product_ids;
        } else {
            $where = " product_device_mapping.product_id IN (" . $this->db->makeIn($sql_or_product_ids) . ")";
        }

        return $this->db->query("
            SELECT
                product_device_mapping.product_id,
                GROUP_CONCAT(DISTINCT CONCAT(product_device.model_name,';', product_device.model_code,';',product_device.product_periode,';', product_device.ean,';',product_device.extra_1,';',product_device.extra_2) SEPARATOR ';') AS devices,
                COALESCE(GROUP_CONCAT(DISTINCT product_device.brand_id SEPARATOR ';'), '') AS device_brand_ids,
                GROUP_CONCAT(DISTINCT product_device.device_id SEPARATOR ';') AS device_ids
            FROM
                product_device_mapping INNER JOIN
                product_device ON (product_device_mapping.device_id = product_device.device_id)
            WHERE
                product_device_mapping.deleted = 0 AND
                $where
            GROUP BY
                product_device_mapping.product_id
        ")->addCallback(function ($row) {
            $row['devices'] = $this->normalizeDevices($row['devices']);
            return $row;
        })->asArray('product_id');
    }

    private function getOrderCodes(array|string $sql_or_product_ids): array
    {
        if (!$sql_or_product_ids) {
            return [];
        }

        if (is_string($sql_or_product_ids)) {
            $where = $sql_or_product_ids;
        } else {
            $where = " product.product_id IN (" . $this->db->makeIn($sql_or_product_ids) . ")";
        }

        return $this->db->query("
            SELECT
                product_order_codes.product_id,
                GROUP_CONCAT(product_order_codes.order_code SEPARATOR ' ') AS order_codes
            FROM
                product INNER JOIN
                product_order_codes ON (product.product_id = product_order_codes.product_id)
            WHERE
                product.product_type = '" . ProductConst::PRODUCT_TYPE_XET . "' AND
                $where
            GROUP BY
                product.product_id
        ")->asSingleArray('product_id');
    }

    /**
     * normalisiert die Gerätenamen und Nummern, so das diese als Basis für die Suche genutzt werden können
     * (hat hier eigentlich nix zu suchen -> Aufgaben von ProductDeviceIndexer())
     *
     * @param string $value
     * @return string
     */
    private function normalizeDevices(string $value): string
    {
        $keywords = $value;
        $keywords = strtolower($keywords);
        $keywords = str_replace('#', ' ', $keywords);
        $keywords = str_replace('_', ' ', $keywords);
        $keywords = preg_replace('~\(.*?\)~', '', $keywords);

        $keywords = explode(';', $keywords);
        $keywords = array_map(function ($value) {
            $value = trim($value, ' -_.');
            $value = preg_replace('~[ ]+~', ' ', $value);

            $value .= ';' . str_replace(' ', '', $value);

            return $value;
        }, $keywords);

        $keywords = implode(';', $keywords);
        $keywords = explode(';', $keywords);

        $keywords = array_filter($keywords);
        $keywords = array_unique($keywords);
        $keywords = implode(' ', $keywords);

        return $keywords;
    }
}
