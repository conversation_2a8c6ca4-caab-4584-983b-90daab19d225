<?php

namespace wws\Taxdoo\Converters;

use bqp\Exceptions\FatalException;
use DateTimeImmutable;
use K11\Taxdoo\Api\Models\Address;
use K11\Taxdoo\Api\Models\OrderItem;
use K11\Taxdoo\Api\Models\Platform;
use K11\Taxdoo\Api\Models\SenderAddress;
use service_loader;
use wws\buchhaltung\Invoice\InvoiceDocument;
use wws\buchhaltung\Invoice\InvoiceRepository;
use wws\Country\CountryValueNotDefinedException;
use wws\Order\Order;
use wws\Order\OrderConst;

class WwsReceiptToTaxdooOrder
{

    /**
     * @param array $receipt
     * @param Order $wws_order
     * @param DateTimeImmutable|null $payment_date
     * @return \K11\Taxdoo\Api\Models\Order
     * @throws CountryValueNotDefinedException
     * @throws FatalException
     */
    public function convert(
        array $receipt,
        Order $wws_order,
        ?DateTimeImmutable $payment_date = null
    ): \K11\Taxdoo\Api\Models\Order {
        $invoice_repository = service_loader::getDiContainer()->get(InvoiceRepository::class);


        $invoice_document = $invoice_repository->loadDocument($receipt['rechnungs_id']);
        $customer = $wws_order->getCustomer();

        $taxdoo_order = new \K11\Taxdoo\Api\Models\Order();
        $found_receipt_type = null;

        if ($receipt['rechnungs_type'] === InvoiceDocument::INVOICE_TYPE_INVOICE) {
            $found_receipt_type = 'Sale';
        } elseif ($receipt['rechnungs_type'] === InvoiceDocument::INVOICE_TYPE_GUTSCHRIFT) {
            $found_receipt_type = 'Refund';
        }

        if ($found_receipt_type === null) {
            throw new FatalException(
                'no receipt type found for invoice: ' . $receipt['rechnungs_id']
            );
        }

        $taxdoo_order->setType($found_receipt_type);

        $channel = new Platform();
        $channel->setIdentifier('SWR6');

        $channel->setTransactionNumber(
            $wws_order->getShopReferenz2() ?
                $wws_order->getShopReferenz2() :
                $wws_order->getAuftnr()
        );
        if ($found_receipt_type === 'Refund') {
            $channel->setRefundNumber($receipt['rechnungs_nr']);
        }
        $taxdoo_order->setChannel($channel);

        $source = new Platform();
        $source->setIdentifier('WWS');

        if ($wws_order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_EBAY) {
            $source->setIdentifier('WWSEBY');
        }

        $source->setTransactionNumber($wws_order->getAuftnr());
        if ($found_receipt_type === 'Refund') {
            $source->setRefundNumber($receipt['rechnungs_nr']);
        }
        $taxdoo_order->setSource($source);

        $taxdoo_order->setSentDate(
            (new DateTimeImmutable($receipt['rechnungs_datum']))
                ->format('Y-m-d\TH:i:s\Z')
        );

        //wir geben die UstIdNr nur wenn die auch gültig ist und ein Nutzen hat
        //(Gültig -> indirekt über die Umsatzsteuer der Rechnung.)
        if (!$invoice_document->isTax() && $customer->getUstidnr()) {
            $taxdoo_order->setBuyerVatNumber($customer->getUstidnr());
        }

        $wws_delivery_address = $wws_order->getLieferAddress();
        $delivery_address = new Address();
        $delivery_address->setFullName(sprintf(
            '%s %s',
            $wws_delivery_address->getVorname(),
            $wws_delivery_address->getName()
        ));

        $delivery_address->setCountry($wws_delivery_address->getCountry()->getCountrySign2());
        $delivery_address->setState('n/a');
        $delivery_address->setCity($wws_delivery_address->getOrt());
        $delivery_address->setZip($wws_delivery_address->getPlz());
        $delivery_address->setStreet($wws_delivery_address->getAdresse1());
        $taxdoo_order->setDeliveryAddress($delivery_address);

        $sender_address = new SenderAddress();
        $sender_address->setStreet('Am Gottesacker 16');
        $sender_address->setZip('01445');
        $sender_address->setCity('Radebeul');
        $sender_address->setState('Sachsen');
        $sender_address->setCountry('DE');
        $taxdoo_order->setSenderAddress($sender_address);

        $taxdoo_order->setTransactionCurrency($wws_order->getCurrency());

        $shipping_cost = 0;
        $discount = 0;

        $items = [];

        foreach ($invoice_document->getItems() as $wws_order_item) {
            $product_name = $wws_order_item->getProductName();
            if ((
                    $product_name === 'Gratisversand' ||
                    mb_stripos($product_name, 'Standardversand') === 0 ||
                    mb_stripos($product_name, 'Versand Standard') === 0 ||
                    mb_stripos($product_name, 'Standardversand - Ausland') === 0
                ) &&
                mb_stripos($product_name, ',') === false
            ) {
                $add_shipping_cost = $wws_order_item->getTotalAmountBrutto();
                if ($found_receipt_type === 'Refund') {
                    $add_shipping_cost *= -1;
                }
                $shipping_cost += $add_shipping_cost;
                continue;
            }

            if ($wws_order_item->getTotalAmountBrutto() < 0) {
                $add_discount = $wws_order_item->getTotalAmountBrutto();
                if ($found_receipt_type === 'Refund') {
                    $add_discount *= -1;
                }
                $discount += $add_discount;
                continue;
            }

            $item_price = $wws_order_item->getTotalAmountBrutto();
            $tax_amount = $wws_order_item->getTaxAmount();
            if ($found_receipt_type === 'Refund') {
                $item_price *= -1;
                $tax_amount *= -1;
            }

            $product_id = $wws_order_item->getProductId();
            $product_name = trim($product_name);

            //für gutschriften... toll
            if (empty($product_id)) {
                foreach ($wws_order->getOrderItems() as $item) {
                    $name = trim($item->getProductName());
                    if ($product_name === $name
                        || mb_strpos($product_name, $name) !== false) {
                        $product_id = $item->getProductId();
                        break;
                    }
                }
                if (empty($product_id)) {
                    foreach ($wws_order->getOrderItems() as $item) {
                        $product_id = $item->getProductId();
                        break;
                    }
                }
            }

            $order_item = new OrderItem();
            $order_item->setQuantity($wws_order_item->getQuantity());
            $order_item->setProductIdentifier($product_id);
            $order_item->setDescription($product_name);
            $order_item->setItemPrice((float)$item_price);
            $order_item->setChannelItemNumber($product_id);
            $order_item->setSourceItemNumber($product_id);

            $order_item->setTaxCollected($tax_amount);
            $items[] = $order_item;
        }

        if (!$items) { //when the order contains only "shipping"-positions, we need a dummy position for taxdoo
            //@todo its unclear how taxdoo is determin the vat rate... -> edge case, doens't matter now
            $order_item = new OrderItem();
            $order_item->setQuantity(1);
            $order_item->setProductIdentifier('dummy');
            $order_item->setDescription('Dummy-Position');
            $order_item->setItemPrice(0);
            $order_item->setChannelItemNumber('dummy');
            $order_item->setSourceItemNumber('dummy');
            $order_item->setTaxCollected(0);

            $items[] = $order_item;
        }


        $taxdoo_order->setTotalDiscount($discount);
        $taxdoo_order->setShipping($shipping_cost);

        $taxdoo_order->setItems($items);

        $taxdoo_order->setPaymentChannel($wws_order->getZahlungsartName());
        $taxdoo_order->setPaymentNumber(implode(' ', [
            $wws_order->getPaymentReferenz(),
            $wws_order->getPaymentReferenz2(),
            $wws_order->getPaymentReferenz3(),
        ]));
        $taxdoo_order->setInvoiceNumber($wws_order->getAuftnr());
        $taxdoo_order->setInvoiceDate(
            (new DateTimeImmutable($receipt['rechnungs_datum']))
                ->format('Y-m-d\TH:i:s\Z')
        );
        $taxdoo_order->setPaymentDate(
            $payment_date ?
                $payment_date->format('Y-m-d\TH:i:s\Z') : $taxdoo_order->getInvoiceDate()
        );

        $billing_address = new Address();
        $billing_address->setFullName(sprintf(
            '%s %s',
            $wws_order->getRechnungAddress()->getVorname(),
            $wws_order->getRechnungAddress()->getName()
        ));
        $billing_address->setCountry($wws_order->getRechnungAddress()->getCountry()->getCountrySign2());
        $billing_address->setState('n/a');
        $billing_address->setCity($wws_order->getRechnungAddress()->getOrt());
        $billing_address->setZip($wws_order->getRechnungAddress()->getPlz());
        $billing_address->setStreet($wws_order->getRechnungAddress()->getAdresse1());

        $taxdoo_order->setBillingAddress($billing_address);

        return $taxdoo_order;
    }
}
