<?php

namespace wws\Mails;

use bqp\Address\Address;
use bqp\Date\DateObj;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\Json;
use bqp\Model\SmartDataEntityNotFoundException;
use bqp\Utils\MailUtils;
use db;
use env;
use Exception;
use League\Flysystem\FileNotFoundException;
use output;
use service_loader;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mime\Address as SymfonyAddress;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Exception\RfcComplianceException;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\File as SymfonyFile;
use Throwable;
use wws\business_structure\Shop;
use wws\Customer\Customer;
use wws\Documents\DocumentRepository;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolver;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverIncludeCms;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverIncludeMail;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverK11;
use wws\Mails\MailTemplatePlaceholderResolver\MailTemplatePlaceholderResolverVariable;
use wws\Mails\Templates\MailTemplate;
use wws\Mails\Templates\MailTemplateRepository;
use wws\Order\Order;
use wws\Order\OrderCompletionService;
use wws\Order\OrderConst;

/**
 * Mailsystem zum senden von Kundebezogenen E-Mails
 */
class Mail
{
    private array $parse_data = [];

    protected array $mail_data = [];

    private string $receiver = '';
    private array $receivers = [];

    private ?string $reply_to = null;

    private bool $editor_is_set = false; // flag ob Editor geladen wurde, ansonsten passiert dies automatisch beim laden der vorlage

    private int $shop_id;

    private string $form_name = 'mail';
    private $form_legend = null;
    private bool $form_optional_send = false;

    protected array $attachments = [];
    protected string $signature = 'normal';

    private bool $auto_add_to_mailarchiv = true;

    public bool $body_is_html = false;

    private MailTemplateRepository $mail_template_repository;

    /**
     * @var MailTemplatePlaceholderResolver[]
     */
    private array $placeholder_resolver = [];

    public function __construct(bool|array $data = false)
    {
        $this->mail_template_repository = new MailTemplateRepository(db::getInstance());

        $this->mail_data = [
            'send_mail' => true,
            'empfaenger_mail' => '',
            'absender_name' => '',
            'absender_mail' => '',
            'betreff' => '',
            'text_mail' => '',
            'customer_memo_vorlage' => ''
        ];

        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverVariable($this->parse_data);
        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverIncludeMail();
        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverIncludeCms();
        $this->placeholder_resolver[] = new MailTemplatePlaceholderResolverK11();

        foreach ($this->placeholder_resolver as $resolver) {
            $resolver->setMail($this);
        }

        if (is_array($data)) {
            $this->loadByDaten($data);
        }
    }

    public function loadByDaten(array $data): void
    {
        $this->parse_data = Json::decode(base64_decode($data['prase_daten']));
        $this->mail_data = $data['mail_daten'];

        if (isset($this->mail_data['send_mail_input'])) {
            $this->mail_data['send_mail'] = isset($this->mail_data['send_mail']);
        }
    }

    public function setOrderId(int $order_id): void
    {
        $this->setOrder($order_id);
    }

    /**
     * Lädt Auftrags,Kunden und Shopdaten in den Parser
     */
    public function setOrder(Order|int $order_object_or_order_id): void
    {
        if ($order_object_or_order_id instanceof Order) {
            $order = $order_object_or_order_id;
        } else {
            $order = new Order($order_object_or_order_id);
        }

        $this->assign('ts_auftnr', urlencode(base64_encode($order->getAuftnr())));
        $this->assign('auft_wert', output::formatPrice($order->getRechnungsBetrag()));

        $this->assign('order_id', $order->getOrderId());
        $this->assign('tax_status', $order->getTaxStatus());
        $this->assign('shop_id', $order->getShopId());
        $this->assign('auft_auftnr', $order->getAuftnr());
        $this->assign('auft_datum', $order->getBestellDatum()->format(DateObj::DE_DATE));
        $this->assign('auft_bestell_datum', $order->getBestellDatum()->format(DateObj::DE_DATE));

        $this->assign('auft_kunde_beleg', $order->getKundeBeleg());
        $this->assign('auft_kunde_zeichen', $order->getKundeZeichen());

        $this->assign('auft_zahlungsart', $order->getZahlungsartName());
        $this->assign('auft_bemerkung', $order->getKundenBemerkung());

        // rechnungs/lieferadresse
        if ($order->isAbweichendeLieferadresse()) {
            $title = 'Rechnungsadresse';

            $address = $order->getLieferAddress();
            $this->assign('auft_different_delivery_address', "Lieferadresse:\n" . self::addIndent($address->format(Address::FROMAT_WITH_LABEL, false)));
        } else {
            $title = 'Rechnungs-/Lieferadresse';
            $this->assign('auft_different_delivery_address', '');
        }

        $address = $order->getRechnungAddress();
        $this->assign('auft_invoice_address', "$title:\n" . self::addIndent($address->format(Address::FROMAT_WITH_LABEL, false)));

        // auftragspositionen
        $mail_helper = new MailHelperOrder($order);

        $this->assign('auft_product_list_small', $mail_helper->getListSmall());
        $this->assign('auft_list_simple', $mail_helper->getListSimple());
        $this->assign('auft_list_extended', $mail_helper->getListExtended());

        foreach ($this->placeholder_resolver as $placeholder) {
            $placeholder->setOrder($order);
        }


        $this->assign('amazon', '');
        if ($order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
            $this->assign('amazon', "\n" . $order->getShopReferenz());
        }

        $this->setCustomer($order->getCustomerId());
    }

    protected static function addIndent(string $input): string
    {
        return '  ' . str_replace("\n", "\n  ", $input);
    }

    /**
     * Lädt Kunden und Shopdaten in den Parser
     */
    public function setCustomer(int $customer_id): void
    {
        $customer = new Customer($customer_id);

        foreach ($this->placeholder_resolver as $placeholder) {
            $placeholder->setCustomer($customer);
        }

        $this->setEmpf($customer->getEmail());
        $this->assignArray([
            'customer_id' => $customer->getCustomerId(),
            'kunde_customer_id' => $customer->getCustomerNr(),
            'kunde_customer_nr' => $customer->getCustomerNr(),
            'kunde_anrede' => $customer->getAnrede(),
            'kunde_name' => $customer->getName(),
            'kunde_vorname' => $customer->getVorname(),
            'kunde_firma' => $customer->getFirma(),
            'kunde_tel' => $customer->getTelefon(),
            'kunde_email' => $customer->getEmail(),
            'shop_id' => $customer->getShopId(),
            'kunde_ort' => $customer->getOrt(),
            'kunde_strasse' => $customer->getAdresse1(),
            'kunde_plz' => $customer->getPlz(),
            'kunde_land' => $customer->getCountryName(),
            'kunde_anrede_text' => $customer->getGrussformel(),
            'ts_kunde_email' => urlencode(base64_encode($customer->getEmail())),
        ]);
        $this->setShop($customer->getShopId());
    }

    /**
     * Lädt Shopdaten in den Parser
     */
    public function setShop(int $shop_id): void
    {
        $shop = Shop::getInstance($shop_id);

        foreach ($this->placeholder_resolver as $placeholder) {
            $placeholder->setShop($shop);
        }

        $address = $shop->getAddress();

        $this->assignArray([
            'shop_id' => $shop->getShopId(),
            'shop_name' => $shop->getShortName(),
            'shop_short_name' => $shop->getShortName(),
            'shop_hotline' => $shop->getHotline(),
            'shop_hotline_service' => $shop->getHotlineService(),
            'shop_adresse' => $address->getAdresse1(),
            'shop_plz' => $address->getPlz(),
            'shop_ort' => $address->getOrt(),
            'shop_ustidnr' => $shop->getUstidnr(),
            'shop_email' => $shop->getEmail(),
            'shop_service_email' => $shop->getServiceEmail(),
            'shop_noreply_email' => $shop->getEmailNoreply(),
            'shop_url' => $shop->getHumanUrl(),
            'shop_agb_link' => $shop->getUrl() . '/agb.html',
            'shop_bank_inhaber' => $shop->getKontoInhaber(),
            'shop_bank_iban' => trim(chunk_split($shop->getIban(), 4, ' ')),
            'shop_bank_bic' => $shop->getBic(),
            'shop_bank' => $shop->getBank(),
        ]);

        $this->shop_id = $shop_id;
    }

    /**
     * Lädt die Daten des Bearbeiters, wenn nix angegeben ist werden die Daten aus
     * der Sessiong gehollt. Alternativ kann auch die $user_id '0' oder 'system'
     * verwendet werden um eine anonyme Signatur zu verwenden
     * @throws DevException
     */
    public function setUserId(bool|int $user_id = false): void
    {
        if (!$this->shop_id) {
            throw new DevException('shop_id nicht festgelegt');
        }

        //flag setzen das editor geladen wurde
        $this->editor_is_set = true;

        //prüfen ob aufruf ohne parameter erfolgte... wenn ja $user_id  aus session hollen
        if ($user_id === false && isset($_SESSION['editor'])) {
            $user_id = $_SESSION['editor'];
        }

        if ($user_id == 'system' or !$user_id) {
            $data = self::loadSystemEditor($this->shop_id);
        } else {
            $data = self::loadEditor($user_id, $this->shop_id);
        }

        //überbleibsel
        $data['mitarbeiter_shop_id'] = $this->shop_id;

        $this->assignArray($data);
    }

    /**
     * Läde die Daten des Bearbeiters
     */
    protected static function loadEditor(int $user_id, int $shop_id): array
    {
        $data = db::getInstance()->singleQuery("
            SELECT
                user_accounts.anrede AS bearbeiter_anrede,
                user_accounts.firstname AS bearbeiter_vorname,
                user_accounts.surname AS bearbeiter_name,
                user_accounts.email AS bearbeiter_email,
                user_accounts.telefon AS bearbeiter_tel,
                user_accounts.signatur_endkunde,
                user_accounts.signatur_einkauf,
                user_accounts.shop_id,
                user_accounts.shop_id AS mitarbeiter_shop_id
            FROM
                user_accounts
            WHERE
                user_accounts.user_id = '$user_id' AND
                user_accounts.shop_id = '$shop_id'
        ");

        $shop = Shop::getInstance($shop_id);
        $data['system_signatur'] = $shop->getMailSignature();
        if (empty($data['signatur_endkunde'])) {
            $data['signatur_endkunde'] = $data['system_signatur'];
        }
        if (empty($data['signatur_einkauf'])) {
            $data['signatur_einkauf'] = $data['signatur_endkunde'];
        }

        $data['signatur'] = $data['signatur_endkunde'];

        return $data;
    }

    /**
     * Lädt die Daten eines Anonomyn bearbeiters für den entsrpechenden Shop
     */
    private static function loadSystemEditor(int $shop_id): array
    {
        $shop = Shop::getInstance($shop_id);

        return [
            'bearbeiter_anrede' => '',
            'bearbeiter_vorname' => '',
            'bearbeiter_name' => $shop->getShortName(),
            'bearbeiter_email' => $shop->getEmail(),
            'bearbeiter_tel' => $shop->getHotline(),

            'signatur' => $shop->getMailSignature(),
            'system_signatur' => $shop->getMailSignature(),
            'signatur_einkauf' => $shop->getMailSignature(),
            'signatur_endkunde' => $shop->getMailSignature(),
        ];
    }

    /**
     * Lädt die Daten des Bearbeiters
     * ACHTUNG: setzt die shop_id falls das noch nicht passiert ist, shop_id wird aus der Sesssion genommen
     * @deprecated nur noch mail::setEditor verwenden
     */
    public function bearbeiter(): void
    {
        if (!$this->shop_id) {
            $this->parse_data['shop_id'] = $_SESSION['shop_id'];
        }
        $this->setUserId();
    }

    /**
     * Fügt einen neuen Wert für den Parser hinzu
     */
    public function assign(string $key, string $value): void
    {
        $this->parse_data[$key] = $value;
    }

    /**
     * Fügt Werte aus ein assoziative Array den Parser hinzu
     */
    public function assignArray(array $data): void
    {
        foreach ($data as $key => $value) {
            $this->assign($key, $value);
        }
    }

    /**
     * Lädt eine Vorlage
     * @throws FileNotFoundException
     */
    public function loadVorlage(string $mail_id): bool
    {
        if (!$this->shop_id) {
            throw new DevException('shop_id nicht festgelegt');
        }

        try {
            $mail_template = $this->mail_template_repository->loadMailTemplate($mail_id, $this->shop_id);
        } catch (SmartDataEntityNotFoundException $smart_data_entity_not_found_exception) {
            return false;
        }

        $this->setMailTemplate($mail_template);

        return (bool)$this->mail_data;
    }

    public function setMailTemplate(MailTemplate $mail_template): void
    {
        $this->mail_data['absender_name'] = $mail_template->getAbsenderName();
        $this->mail_data['absender_mail'] = $mail_template->getAbsenderMail();
        $this->mail_data['betreff'] = $mail_template->getBetreff();
        $this->mail_data['text_mail'] = $mail_template->getTextMail();
        $this->mail_data['customer_memo_vorlage'] = $mail_template->getTextCustomerMemo();

        foreach ($mail_template->getAttachments() as $attachment) {
            $content = $this->fetchAttachmentContent($attachment['path']);
            $this->addAttachmentString($content, $attachment['name']);
        }

        if (!$this->editor_is_set) {
            $this->setUserId();
        }
        $this->parse();
    }

    public function setShopId(int $shop_id): void
    {
        $this->parse_data['shop_id'] = $shop_id;
        $this->shop_id = $shop_id;
    }

    public function parse(): void
    {
        $this->mail_data['text_mail'] = $this->_parse($this->mail_data['text_mail']);
        $this->mail_data['customer_memo_vorlage'] = ($this->_parse($this->mail_data['customer_memo_vorlage'] ?? ''));
        $this->mail_data['absender_name'] = $this->_parse($this->mail_data['absender_name']);
        $this->mail_data['absender_mail'] = $this->_parse($this->mail_data['absender_mail']);
        $this->mail_data['betreff'] = $this->_parse($this->mail_data['betreff']);
    }

    public function getMailBody(): string
    {
        return $this->mail_data['text_mail'];
    }

    public function setMailBody(string $body): void
    {
        $this->mail_data['text_mail'] = $body;
    }

    public function getMailCustomerMemoTemplate(): ?string
    {
        return $this->mail_data['customer_memo_vorlage'];
    }

    public function setMailCustomerMemoTemplate(string $customer_memo_vorlage): void
    {
        $this->mail_data['customer_memo_vorlage'] = $customer_memo_vorlage;
    }

    public function getBetreff(): string
    {
        return $this->mail_data['betreff'];
    }

    public function setBetreff(string $betreff): void
    {
        $this->mail_data['betreff'] = $betreff;
    }


    /**
     * legt fest ob im frontend getInlineEdit() eine Option angeboten werden soll zum senden/nicht senden
     *
     * ACHTUNG: diese Option wird nicht von dieser Klasse berücksichtigt!
     */
    public function setOptionalSend(bool $optional_send, bool $default = true): void
    {
        $this->mail_data['send_mail'] = $default;

        $this->form_optional_send = $optional_send;
    }

    public function shouldSend()
    {
        return $this->mail_data['send_mail'];
    }

    //erzeugt formular elemente für mail bearbeitung
    public function get_inline_edit($name = false, $hide = false, $css_class = 'mail_editor'): string
    {
        if ($name === false) {
            $name = $this->form_name;
        }

        $return = '';

        if (!$this->receiver) {
            $this->receiver = $this->mail_data['empfaenger_mail'];
        }

        $hide = $hide ? ' display: none;' : 'display: inline-block;';
        $return .= '<fieldset style="' . $hide . '; background-color: #fff; margin: 6px;" id="' . $name . '" class="mail_editor  ' . $css_class . '">';
        $return .= '<input type="hidden" name="' . $name . '[prase_daten]" value="' . base64_encode(json_encode($this->parse_data)) . '" />';
        $return .= '<table>';
        $return .= '<tr>';
        $return .= '<td>Emfänger:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][empfaenger_mail]" value="' . $this->receiver . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>Absendername:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][absender_name]" value="' . $this->mail_data['absender_name'] . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>Absenderemail:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][absender_mail]" value="' . $this->mail_data['absender_mail'] . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>Betreff:</td>';
        $return .= '<td><input type="text" name="' . $name . '[mail_daten][betreff]" value="' . $this->mail_data['betreff'] . '" size="50" /></td>';
        $return .= '</tr>';

        $return .= '<tr>';
        $return .= '<td>E-Mail</td>';
        $return .= '<td><textarea name="' . $name . '[mail_daten][text_mail]" rows="20" cols="80">' . $this->mail_data['text_mail'] . '</textarea></td>';
        $return .= '</tr>';
        $return .= '</table>';
        $return .= '</fieldset>';

        return $return;
    }

    public function getInlineEdit($name = false, $hide = false, $css_class = 'mail_editor')
    {
        if ($name === false) {
            $name = $this->form_name;
        }

        $return = '';

        if (!$this->receiver) {
            $this->receiver = $this->mail_data['empfaenger_mail'];
        }

        $hide = $hide ? ' display: none;' : 'display: inline-block;';

        $style = 'style="' . $hide . '; background-color: #fff; margin: 6px;"';
        if ($css_class == 'form_element_mail_editor_form') {
            $style = '';
        }
        $return .= '<fieldset ' . $style . ' id="' . $name . '" class="mail_editor ' . $css_class . '">';
        if ($this->form_legend) {
            $return .= '<legend>' . $this->form_legend . '</legend>';
        }
        $return .= '<input type="hidden" name="' . $name . '[prase_daten]" value="' . base64_encode(json_encode($this->parse_data)) . '" />';


        $show = true;

        if ($this->form_optional_send) {
            if (!$this->mail_data['send_mail']) {
                $show = false;
            }

            $return .= '<div class="row">';
            $return .= '<label>Mail senden</label>';

            $checked = $this->mail_data['send_mail'] ? 'checked="checked"' : '';

            $return .= '<input type="checkbox" name="' . $name . '[mail_daten][send_mail]" value="1" ' . $checked . ' onchange="this.checked?jQuery(\'#' . $name . '_inner\').show():jQuery(\'#' . $name . '_inner\').hide();">';

            $return .= '<input type="hidden" name="' . $name . '[mail_daten][send_mail_input]" value="1">';
            $return .= '</div>';
        }

        $show = $show ? '' : ' style="display: none;"';

        $return .= '<div id="' . $name . '_inner"' . $show . '>';
        $return .= '<div class="row">';
        $return .= '<label>Emfänger</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][empfaenger_mail]" value="' . $this->receiver . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>Absendername</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][absender_name]" value="' . $this->mail_data['absender_name'] . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>Absenderemail</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][absender_mail]" value="' . $this->mail_data['absender_mail'] . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>Betreff</label>';
        $return .= '<input type="text" name="' . $name . '[mail_daten][betreff]" value="' . $this->mail_data['betreff'] . '" size="50">';
        $return .= '</div>';

        $return .= '<div class="row">';
        $return .= '<label>E-Mail</label>';
        $return .= '<textarea name="' . $name . '[mail_daten][text_mail]" rows="20" cols="80">' . $this->mail_data['text_mail'] . '</textarea>';
        $return .= '</div>';
        $return .= '</div>';

        $return .= '</fieldset>';

        return $return;
    }


    public function __toString()
    {
        return $this->get_inline_edit();
    }

    protected function _parse(string $string): string
    {
        if (!preg_match_all('#<!--(.*?)/-->#', $string, $daten)) {
            return $string;
        }

        $rerun = false;

        foreach ($daten[1] as $placeholder) {
            $type = 'variable';
            $pattern_value = $placeholder;

            if (strpos($placeholder, ':')) {
                list($type, $pattern_value) = explode(':', $placeholder);
            }

            $type_resolved = false;

            foreach ($this->placeholder_resolver as $resolver) {
                if ($resolver->getType() === $type) {
                    $type_resolved = true;

                    $value = $resolver->resolve($pattern_value);

                    if ($value !== null) {
                        $string = str_replace("<!--$placeholder/-->", $value, $string);

                        if (strpos($value, '<!--') !== false) {
                            $rerun = true;
                        }
                        break;
                    }
                }
            }

            if (!$type_resolved) {
                throw new FatalException('unknown placeholder ' . $placeholder);
            }
        }

        if ($rerun) {
            $string = $this->_parse($string);
        }

        return $string;
    }

    /**
     * versucht eine Mail zu versenden, gibt true bei Erfolg zurück, ansonsten false
     * @return bool
     */
    public function trySend(): bool
    {
        try {
            $this->send();
            return true;
        } catch (Throwable $e) {
            return false;
        }
    }

    /**
     * Sendet die Mail und speichert sie in der DB
     * @throws RfcComplianceException|TransportExceptionInterface|Exception
     */
    public function send(): void
    {
        if (!$this->receiver) {
            $this->receiver = $this->mail_data['empfaenger_mail'];
        }

        if (str_contains($this->receiver, '<EMAIL>')) {
            //mitigation für 0815 galaxus bestellungen
            throw new Exception();
        }

        //letztes mal parsen
        $this->parse();

        $message = new Email();
        $message->from(new SymfonyAddress($this->mail_data['absender_mail'], $this->mail_data['absender_name']));

        $debug_mail_redirecting = service_loader::getConfigRegistry()->get('system')['debug_mail_redirecting'];
        $receiver = $debug_mail_redirecting ?: $this->receiver;

        $message->to($receiver, ...$this->receivers);

        if (is_array($this->mail_data['cc_empfaenger'] ?? false)) {
            $message->cc(...$this->mail_data['cc_empfaenger']);
        }

        if (is_array($this->mail_data['bcc_empfaenger'] ?? false)) {
            $message->bcc(...$this->mail_data['bcc_empfaenger']);
        }

        if ($this->reply_to) {
            $message->replyTo($this->reply_to);
        }

        $message->subject($this->mail_data['betreff']);
        $message->html($this->getMailBodyAsHtml());

        foreach ($this->attachments as $attachment) {
            if (isset($attachment['file'])) {
                $message->addPart(new DataPart(new SymfonyFile($attachment['file'], $attachment['name'])));
            } elseif (isset($attachment['string'])) {
                $message->addPart(new DataPart($attachment['string'], $attachment['name']));
            }
        }

        $mailer = service_loader::getSymfonyMailer($this->mail_data['absender_mail']);
        $mailer->send($message);

        if ($this->auto_add_to_mailarchiv && ($this->parse_data['customer_id'] ?? false)) {
            $mail = new MailArchivMail();
            $mail->setDatum(new DateObj());
            $mail->setOrderId($this->parse_data['order_id']);
            $mail->setCustomerId($this->parse_data['customer_id']);
            $mail->setEmailSender($this->mail_data['absender_mail']);
            $mail->setEmailReciver($this->receiver);
            $mail->setBetreff($this->mail_data['betreff']);
            $mail->setBody($this->mail_data['text_mail']);
            $mail->setUserId(env::getUserId());
            $mail->save();
        }
    }

    public function getMailBodyAsHtml(): string
    {
        if ($this->body_is_html) {
            return $this->mail_data['text_mail'];
        }

        $html = '<html><head><style>body { background-color: #fff; font-family:Arial, Helvetica, sans-serif; }';

        $shop_id = $this->parse_data['shop_id'];
        if ($shop_id == Shop::ALLEGO) {
            $html .= '#logo { float: right; width: 240px; }';
        }

        $html .= '</style></head><body>';

        if ($shop_id == Shop::ALLEGO) {
            $html .= '<img src="https://www.smartgoods.de/assets/logo_smartgoods.png" id="logo" alt=""><br><br style="clear:both;">';
        }

        $body = preg_replace("~^( {3}| {4})~m", '&nbsp; &nbsp; ', $this->mail_data['text_mail']);
        $html .= nl2br($this->replaceUrlsToLinks($body));

        $html .= '</body></html>';

        return $html;
    }

    public function replaceUrlsToLinks(string $text): string
    {
        $text = preg_replace_callback('~http://([-=_.a-z0-9!#&/\?+%@]*)~i', function ($token) {
            $url = $token[0];

            $domain = substr($url, 7);
            $pos = strpos($domain, '/');
            if ($pos) {
                $domain = substr($domain, 0, $pos);
            }
            return '<a href="' . $url . '">' . $domain . '</a>';
        }, $text);

        $text = preg_replace_callback('~https://([-=_.a-z0-9!#&/\?+%@]*)~i', function ($token) {
            $url = $token[0];

            $domain = substr($url, 8);
            $pos = strpos($domain, '/');
            if ($pos) {
                $domain = substr($domain, 0, $pos);
            }
            return '<a href="' . $url . '">' . $domain . '</a>';
        }, $text);

        return $text;
    }

    private function widerruf2html(string $string): string
    {
        return '<div style="border: 2px solid #000; padding: 10px;">' . trim(preg_replace('/# (.*) #/', '\\1', $string)) . '</div>';
    }

    public function addAnschrift(string $anschrift): void
    {
        $this->parse_data['anschrift'] = $anschrift;
    }

    public function printer($shop_id = '', $params = [])
    {
        $this->parse();
        echo '<body>';
        /*
        echo '<script type="text/javascript">';
            echo 'window.open("/LIB/new_mail.php?cmd=print&mail=&shop_id='.$shop_id.'","printer","height=600,width=680,menubar=yes,resizable=yes,scrollbars=yes,status=yes,toolbar=yes");';
        echo '</script>';
        */


        /*if(isset($_REQUEST['cmd']) AND $_REQUEST['cmd']=='print') {
            include('bootstrap.php');
            $mail = unserialize(base64_decode(str_replace(' ','',$_POST['mail'])));
            $mail->doPrint($_POST['shop_id'],unserialize(base64_decode($_POST['params'])));
        }*/

        throw new DevException('legacy code -> print funktion "controller" direkt im lib integriert!');

        echo '<form method="post" action="/LIB/business_logic/mails/new_mail.php?cmd=print" target="printer" id="printerform">';
        echo '<input type="hidden" name="mail" value="' . base64_encode(serialize($this)) . '" />';
        echo '<input type="hidden" name="shop_id" value="' . $shop_id . '" />';
        echo '<input type="hidden" name="params" value="' . base64_encode(serialize($params)) . '" />';
        echo '</form>';

        echo '<script type="text/javascript">';
        echo 'window.open("about:blank","printer","height=600,width=680,menubar=yes,resizable=yes,scrollbars=yes,status=yes,toolbar=yes");';
        echo 'document.getElementById("printerform").submit();';
        echo '</script>';
        echo '</body>';
        return true;
    }

    public function doPrint(?int $shop_id = null, array $params = []): void
    {
        if ($shop_id === null) {
            $shop_id = $this->parse_data['mitarbeiter_shop_id'];
        }

        $this->mail_data['text_mail'] = strip_tags($this->mail_data['text_mail']);

        //text
        $text = explode("\n", $this->mail_data['text_mail']);

        $zpp = 40;

        $seiten_max = ceil(count($text) / $zpp);

        $shop = Shop::getInstance($shop_id);

        switch ($params['absender']) {
            case 'service':
                $shop_adr = '<u style="font-size: 11px;">' . $shop->getServiceAddress()->format(Address::FORMAT_SINGLE_LINE) . '</u>';
                break;
            default:
                $shop_adr = '<u style="font-size: 11px;">' . $shop->getAddress()->format(Address::FORMAT_SINGLE_LINE) . '</u>';
        }

        $footer = '<br /><br /><hr />';
        $footer .= DocumentRepository::getHtmlFooter($shop->getShopId());
        $footer .= '<div style=" page-break-after:always;"></div>';

        echo '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">';
        echo '<html>';
        echo '<head>';
        echo '<title>Drucken</title>';
        echo '<link REL="STYLESHEET" TYPE="text/css" HREF="/res/images/rechlogo/1.0.css">';
        echo '<style type="text/css">
                    @media screen {
                        body {
                            margin: 0;
                            font-size: 14px;
                            background-color: #ccc;
                        }

                        .page {
                            background-color: #fff;
                            padding: 40px;

                            position: relative;
                            top: -10px;
                            left: -10px;
                        }

                        .page_shadow {
                            margin-top: 20px;
                            margin-bottom: 20px;

                            background-color: #444;


                            position: relative;
                            top: 10px;
                            left: 10px;

                        }
                    }

                    @media print {
                        body {
                            font-size: 14px;
                        }
                    }
                      </style>';
        echo '</head>';
        echo '<body style="margin-left: 2.5%; margin-right: 2.5%;">';

        $cur_line = 0;
        for ($seite_akt = 0; $seite_akt <= $seiten_max - 1; $seite_akt++) {
            echo '<div class="page_shadow">';
            echo '<div class="page">';
            echo '<div style="float: right;">';
            echo '<img src="/res/images/logos/invoice_' . $shop_id . '_1.0.jpg" width="160" border="0" />';
            echo '<div style="text-align: center; font-size: 11px;">';
            echo 'Seite ' . ($seite_akt + 1) . ' von ' . ($seiten_max);
            echo '</div>';
            echo '</div>';


            if ($seite_akt == 0) {
                echo '<br /><br /><br />';
                echo $shop_adr;
                echo '<br /><br />';
                echo nl2br($this->parse_data['anschrift']);

                echo '<br /><br /><br /><br />';

                echo '<div style="float: right;">Dresden, den ' . date('d.m.Y') . '</div><br /><br />';
                echo '<b>' . $this->mail_data['betreff'] . '</b>';

                echo '<br /><br />';
            } else {
                echo '<br /><br /><br /><br /><br /><br /><br /><br /><br />';
            }

            $zpp_ = $seite_akt == 0 ? $zpp - 8 : $zpp;

            for ($i = 0; $i <= $zpp_; $i++) {
                echo preg_replace("~^( {4}| {2})~", "&nbsp; &nbsp; &nbsp;", $text[$cur_line++]) . '<br />';
            }

            echo $footer;
            echo '</div>';
            echo '</div>';
        }


        if ($this->auto_add_to_mailarchiv) {
            if ($this->parse_data['customer_id']) {
                $mail = new MailArchivMail();
                $mail->setDatum(new DateObj());
                $mail->setOrderId($this->parse_data['order_id']);
                $mail->setCustomerId($this->parse_data['customer_id']);
                $mail->setEmailSender('per Post');
                $mail->setEmailReciver('per Post / ' . $this->receiver);
                $mail->setBetreff($this->mail_data['betreff']);
                $mail->setBody($this->mail_data['text_mail']);
                $mail->setUserId(env::getUserId());
                $mail->save();
            }
        }
    }

    public function setEmpf(string $email_adresse): void
    {
        $this->mail_data['empfaenger_mail'] = $email_adresse;

        $this->receiver = $email_adresse;
    }

    public function addEmpf(string $address): void
    {
        if (!$this->receiver) {
            $this->setEmpf($address);
        } else {
            $this->receivers[] = $address;
        }
    }

    public function getReciverEmail(): string
    {
        return $this->receiver ?: $this->mail_data['empfaenger_mail'];
    }

    public function setEmpfByConfigString(string $address_string): void
    {
        $dest = MailUtils::parseAddressConfigString($address_string);

        foreach ($dest['to'] as $address) {
            $this->addEmpf($address);
        }

        foreach ($dest['cc'] as $address) {
            $this->addCCEmpf($address);
        }

        foreach ($dest['bcc'] as $address) {
            $this->addBCCEmpf($address);
        }
    }

    public function setCCEmpf(string $email_adresse): void
    {
        $this->addCCEmpf($email_adresse);
    }

    public function addCCEmpf(string $address): void
    {
        if (empty($this->mail_data['cc_empfaenger'])) {
            $this->mail_data['cc_empfaenger'] = [];
        }

        $this->mail_data['cc_empfaenger'][] = $address;
    }

    public function addBCCEmpf(string $address): void
    {
        if (empty($this->mail_data['bcc_empfaenger'])) {
            $this->mail_data['bcc_empfaenger'] = [];
        }

        $this->mail_data['bcc_empfaenger'][] = $address;
    }

    public function setAbsender(string $mail, string $name = ''): void
    {
        $this->mail_data['absender_mail'] = $mail;
        $this->mail_data['absender_name'] = $name ?: $mail;
    }

    public function setReplyTo(string $mail): void
    {
        $this->reply_to = $mail;
    }

    public function addAttachment($file, $name): void
    {
        $this->attachments[] = ['file' => $file, 'name' => $name];
    }

    public function addAttachmentString(string $string, $name): void
    {
        $this->attachments[] = ['string' => $string, 'name' => $name];
    }

    public function hasAttachments(): bool
    {
        return count($this->attachments) > 0;
    }

    /**
     * @return array [filename, body]
     */
    public function getAttachmentsWithContent(): array
    {
        $result = [];

        foreach ($this->attachments as $att) {
            if (array_key_exists('file', $att)) {
                $result[] = ['filename' => $att['name'], 'body' => file_get_contents($att['file'])];
            } elseif (array_key_exists('string', $att)) {
                $result[] = ['filename' => $att['name'], 'body' => $att['string']];
            }
        }

        return $result;
    }

    /**
     * @return null
     */
    public function getFormLegend()
    {
        return $this->form_legend;
    }

    /**
     * @param null $form_legend
     */
    public function setFormLegend($form_legend): void
    {
        $this->form_legend = $form_legend;
    }

    public function setAutoAddToMailarchiv(bool $status): void
    {
        $this->auto_add_to_mailarchiv = $status;
    }

    public function setSignatureType(string $signature_type): void
    {
        $this->signature = $signature_type;
    }

    /**
     * @throws FileNotFoundException
     */
    public function fetchAttachmentContent(string $attachment_path): string
    {
        if ($this->mail_template_repository->isStorageAttachment($attachment_path)) {
            return $this->mail_template_repository->fetchAttachmentContent($attachment_path);
        }

        throw new DevException('unknown attachment type');
    }

    public function getValue(string $key): mixed
    {
        return $this->parse_data[$key] ?? null;
    }

    public function getShopId(): ?int
    {
        return $this->shop_id;
    }
}
