<?php

namespace wws\Order\Actions;

use bqp\AddressVerification\Verificators\GoogleMapsGeocodeApi;
use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Utils\TelephoneUtils;
use debug;
use env;
use Exception;
use input;
use service_loader;
use system_protokoll;
use wws\business_structure\Shop;
use wws\Country\CountryConst;
use wws\Customer\Customer;
use wws\Customer\CustomerBlockService;
use wws\Customer\CustomerDuplicateFinder;
use wws\Order\Order;
use wws\Order\OrderAddressVerification;
use wws\Order\OrderAutomaticLeerfeld\OrderAutomaticCommonRules;
use wws\Order\OrderAutomaticLeerfeld\OrderAutomaticRuleAbortException;
use wws\Order\OrderAutomaticLeerfeld\OrderAutomaticRuleIp;
use wws\Order\OrderAutomaticLeerfeld\OrderAutomaticStatusHandler;
use wws\Order\OrderConst;
use wws\Order\OrderMemo;
use wws\Shipment\ShipmentRepository;
use wws\Users\UserConst;

class OrderAutomaticNewShop
{
    private bool $abort_on_first = false;
    private bool $abort_reason_to_memo = false;

    private db_generic $db;
    private OrderAddressVerification $order_address_verification;
    private CustomerBlockService $customer_block_service;
    private CustomerDuplicateFinder $customer_finder;
    private DateObj $date_threshold;
    private OrderAutomaticCommonRules $rules;
    private bool $auto_correct_address = true;

    public function __construct(
        db_generic $db,
        OrderAddressVerification $order_address_verification,
        CustomerBlockService $customer_block_service,
        CustomerDuplicateFinder $customer_finder,
        OrderAutomaticCommonRules $rules
    ) {
        $this->db = $db;
        $this->order_address_verification = $order_address_verification;
        $this->customer_block_service = $customer_block_service;

        $this->customer_finder = $customer_finder;

        $this->rules = $rules;

        $date = new DateObj();
        $hour = $date->format('G');

        $is_working_time = $hour > 7 && $hour <= 19 && $date->isWorkingday(5);

        $this->date_threshold = new DateObj();

        if (env::isK11() && $is_working_time) {
            $this->date_threshold->subSimple('minutes', 30);
        }

        if (!env::isK11() && $is_working_time) {
            $this->date_threshold->subSimple('minutes', 15);
        }

        if (!$is_working_time) {
            $this->date_threshold->subSimple('minutes', 60);
        }
    }

    public function setAbortOnFirst(bool $abort_on_first): void
    {
        $this->abort_on_first = $abort_on_first;
    }

    public function setAddAbortReasonToMemo(bool $abort_reason_to_memo): void
    {
        $this->abort_reason_to_memo = $abort_reason_to_memo;
    }

    public function processOrder(Order $order): void
    {
        $log = system_protokoll::getInstance('order_automatic');

        //lieferanten bestellungen
        if ($order->getCustomerId() < 10) {
            return;
        }

        //die fba bestellungen verfälschen die "statistik", weil die quasi beendet reinkommen, aber trotzdem komplett geprüft werden
        if ($order->isOrderTag(OrderConst::TAG_AMAZON_FBA)) {
            return;
        }

        //wenn abweichende lieferadresse ohne tel -> tel aus rechnungsadresse übernehmen, wenn gültig
        if ($order->isAbweichendeLieferadresse() && !TelephoneUtils::isValidPhoneNumber($order->getLieferAddress()->getTel1())) {
            $num = $order->getRechnungAddress()->getTel1();
            if (TelephoneUtils::isValidPhoneNumber($num)) {
                $order->getLieferAddress()->setTel1($num);
                $order->save();
            }
        }

        $status_handler = new OrderAutomaticStatusHandler();
        $status_handler->setSimulate(!$this->abort_on_first);

        try {
            $this->check($order, $status_handler);

            if ($this->auto_correct_address) {
                $customer = $order->getCustomer();

                if ($this->checkAddressAndCorrect($order, $customer, $status_handler)) {
                    $memo = new OrderMemo("Lieferadresse automatisch korrigiert.", OrderMemo::SONSTIGES, UserConst::USER_ID_SYSTEM);
                    $order->addOrderMemo($memo);

                    $order->save();
                    $customer->save();
                }
            } else {
                $this->checkAddress($order, $status_handler);
            }
        } catch (OrderAutomaticRuleAbortException $e) {
        }

        if ($status_handler->isOk()) {
            $log->wlog(system_protokoll::DEBUG, 'NEU SHOP: Auftrag  ' . $order->getAuftnr() . ' automatisch weitergeschaltet.', '', $order->getOrderId());

            $action = new AuftAngenommen();
            $action->setOrder($order);
            $action->execute();
        } else {
            $reason_text = '- ' . implode("\n- ", $status_handler->getErrorReasons());

            if ($this->abort_reason_to_memo) {
                $memo = new OrderMemo("Automatik:\n" . $reason_text, OrderMemo::SONSTIGES, UserConst::USER_ID_SYSTEM);

                $order->addOrderMemo($memo);
                $order->save();
            }

            $log->wlog(system_protokoll::DEBUG, 'NEU SHOP: Auftrag nicht ' . $order->getAuftnr() . ' automatisch weitergeschaltet.', $reason_text, $order->getOrderId());
        }

        if (env::isEcom()) {
            if ($status_handler->isErrorByRuleDescription('Adresse')) {
                $this->testGoogleMapsApi($order);
            }
        }
    }

    public function testGoogleMapsApi(Order $order): void
    {
        $log = system_protokoll::getInstance('debug_order_google_maps');

        $address = $order->getLieferAddress();

        try {
            $api = service_loader::get(GoogleMapsGeocodeApi::class);
            $result = $api->debug($address);

            $org_address_data = [
                'order_id' => $order->getOrderId(),
                'auftnr' => $order->getAuftnr(),
                'plz' => $address->getPlz(),
                'ort' => $address->getOrt(),
                'strasse' => $address->getAdresse1()
            ];

            $message = '';
            $message .= json_encode($org_address_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $message .= "\n\nResponse\n\n";
            $message .= $result;

            $log->winfo('Adresse ' . $order->getAuftnr(), $message, $order->getOrderId());
        } catch (Exception $e) {
            debug::dump('testGoogleMapsApi');
            debug::dump($e->getMessage());
        }
    }

    /**
     * @param Order $order
     * @param OrderAutomaticStatusHandler $status
     * @throws OrderAutomaticRuleAbortException
     */
    public function check(Order $order, OrderAutomaticStatusHandler $status): void
    {
        $order_items = array_merge(
            $order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_PRODUKT),
            $order->getOrderItemsByWarenkorbTyp(OrderConst::WARENKORB_TYP_ASWO)
        );

        $this->rules->setContext($status, $order, $order_items);

        if ($order->getMaxStatus() !== OrderConst::STATUS_NEU_SHOP) {
            $status->add('Status ungleich "neu Shop"')->fail();
        }

        if ($order->getKundenBemerkung()) {
            $status->add('Kunde hat Bemerkung angegeben')->fail();
        }

        if ($order->getSpedId() === ShipmentRepository::SPED_UNBEKANNT) {
            $status->add('Spedition "unbekannt".')->fail();
        }

        if (!input::validateMail($order->getCustomer()->getEmail())) {
            $status->add('Mailadresse ungültig.')->fail();
        }

        if (!in_array($order->getLieferAddress()->getCountryId(), [CountryConst::COUNTRY_ID_DE, CountryConst::COUNTRY_ID_AT])) {
            $status->add('Lieferadresse außerhalb DE/AT.')->fail();
        }

        $this->rules->checkTestAddress($order->getLieferAddress());

        if ($order->isOrderTag(OrderConst::TAG_B_WARE)) {
            $status->add('Bestellung mit B-Ware')->fail();
        }

        //spedition abholung, aber keine Abholung gewünscht
        if ($order->getSpedId() === ShipmentRepository::SPED_ABHOLUNG && !in_array($order->getVersandKundeOpt(), ['abh', 'eu_block'])) {
            $status->add('Spedition "Abholung" obwohl keine Abholung.')->fail();
        }

        //nur grob prüfen ob die bestellung sinn ergibt -> der Rest erfolgt im Leerfeld, bzw Leerfeld automatik
        $this->rules->checkQuantity(10, 5);
        if (env::isK11()) {
            $this->rules->checkTotalOrderAmount(500);
        }
        if (env::isEcom()) {
            $this->rules->checkTotalOrderAmount(2000);
        }

        $this->rules->checkOrderTagsNotSetted([OrderConst::TAG_FAKE, OrderConst::TAG_FAKE_VERDACHT]);

        //bei speditionsware telefon
        if (!TelephoneUtils::isValidPhoneNumber($order->getLieferAddress()->getTel1())) {
            if (ShipmentRepository::isSpedShipmentByOrder($order)) {
                $status->add('Speditionsversand und keine gültige Telefonnummer.')->fail();
            }
        }

        //prüfen ob nicht bestellung doppelt

        $customer = $order->getCustomer();

        if ($this->customer_block_service->checkCustomer($customer)) {
            $status->add('Kunde als gesperrt markiert.')->fail();
        }

        //kunde existiert bereits
        $customer_ids = $this->customer_finder->findByCustomer($customer);

        $rule = $status->add('Dieser Kunde existiert bereits.');

        if ($this->isCheckExistingCustomerEnabled($order->getOrderOriginId())) {
            if ($customer_ids) {
                $rule->fail('(' . implode(',', $customer_ids) . ')');
            } else {
                $rule->success();
            }
        } else {
            $rule->success('Überprüfung für Bestellherkunft (' . $order->getOrderOriginId() . ') übersprungen.');
        }


        if ($order->getShopId() === Shop::ERSATZTEILSHOP) {
            return;
        }

        //bezahlung sicher
        if (!$order->isPayable()) {
            $status->add('Zahlungsabwicklung nicht vollständig abgeschlossen.')->fail();
        }

        //prüfen ob der kunde nicht schon in zeitraum x bestellt hat
        $customer_ids[] = $order->getCustomerId();

        $result = $this->db->query("
            SELECT
                orders.auftnr
            FROM
                orders
            WHERE
                orders.customer_id IN (" . $this->db->makeIn($customer_ids) . ") AND
                orders.added > '" . $order->getBestellDatum()->subSimple('hours', 72)->db() . "' AND
                orders.order_id != '" . $order->getOrderId() . "'
        ")->asSingleArray();

        if ($result) {
            $status->add('Es wurden mehrere Bestellungen innerhalb der letzten 72 Stunden aufgegeben.')->fail('(' . implode(',', $result) . ')');
        }

        $check = service_loader::get(OrderAutomaticRuleIp::class);

        $result = $check->checkOrder($order);
        if ($result->isMatch()) {
            $status->add('Es wurden weitere Bestellungen mit derselben IP-Adresse gefunden.')->fail('(' . $result->getAsTextSimple() . ')');
        }
    }

    /**
     * @param Order $order
     * @return bool
     */
    public function checkLater(Order $order): bool
    {
        if (!in_array($order->getMinStatus(), [OrderConst::STATUS_NEU_SHOP, OrderConst::STATUS_EBAY_ZAHLUNGSABWICKLUNG])) {
            return false;
        }

        // Es liegt eine ungelesene E-Mail zur Order vor
        if ($order->isOrderTag(OrderConst::TAG_UNREAD_MAIL)) {
            return true;
        }

        if ($order->getBestellDatum()->isAfter($this->date_threshold)) {
            return true;
        }

        //wenn bestellung älter ist als 24 stunden, nicht nochmal zurückstellen
        $date = new DateObj();
        $date->subSimple('hours', 24);

        if ($order->getBestellDatum()->isBefore($date)) {
            return false;
        }

        if ($order->getMinStatus() === OrderConst::STATUS_EBAY_ZAHLUNGSABWICKLUNG) {
            return true;
        }

        if (env::isEcom()) {
            if ($order->getZahlungsart() === OrderConst::PAYMENT_SOFORTUEBERWEISUNG && $order->getZahlungsartStatus() === OrderConst::PAYMENT_STATUS_SOFORTUEBERWEISUNG_OPEN) {
                return true;
            }

            if ($order->getZahlungsart() === OrderConst::PAYMENT_KREDITKARTE && $order->getZahlungsartStatus() === OrderConst::PAYMENT_STATUS_KREDITKARTE_OPEN) {
                return true;
            }

            if ($order->getZahlungsart() === OrderConst::PAYMENT_PAYPAL && $order->getZahlungsartStatus() === OrderConst::PAYMENT_STATUS_PAYPAL_OPEN) {
                return true;
            }

            if ($order->getZahlungsart() === OrderConst::PAYMENT_FINANZIERUNG_COMMERZ && $order->getZahlungsartStatus() === OrderConst::PAYMENT_STATUS_FINANZIERUNG_OHNE_ANTRAG) {
                return true;
            }

            if ($order->getZahlungsart() === OrderConst::PAYMENT_ECOM_UNZER && $order->getZahlungsartStatus() === OrderConst::PAYMENT_STATUS_UNZER_OPEN) {
                return true;
            }
        }

        return false;
    }


    private function checkAddress(Order $order, OrderAutomaticStatusHandler $status): void
    {
        $result = $this->order_address_verification->verifyDeliveryAddress($order);

        if ($result->canAutoProceed()) {
            return;
        }

        $status->add('Adresse')->fail($result->getAddressVerificationResult()->getReason());
    }

    /**
     * Prüft die Adresse und korrigiert diese falls möglich. Hat eine korrektur stattgefunden wird dies über den Rückgabewert signalisiert.
     *
     * @param Order $order
     * @param Customer $customer
     * @param OrderAutomaticStatusHandler $status
     * @return bool Adresse automatisch korrigiert, fehlgeschlagenen Verifikation wird in OrderAutomaticStatusHandler geschrieben
     */
    private function checkAddressAndCorrect(Order $order, Customer $customer, OrderAutomaticStatusHandler $status): bool
    {
        $result = $this->order_address_verification->verifyDeliveryAddress($order);

        $address = $order->getLieferAddress();

        if ($result->canAutoProceed()) {
            return false;
        }

        if ($result->hasCorrection()) {
            $override_customer_address = $address->getHash() == $customer->getAddress()->getHash();

            $address_new = clone $address;

            $result->getAddressVerificationResult()->runCorrection($address_new);

            $result = $this->order_address_verification->verifyDeliveryAddress($order, $address_new);
            if ($result->isOk()) {
                if ($order->isAbweichendeLieferadresse()) {
                    $order->setAddress($address_new, Order::ADDRESS_TYPE_LIEFER);
                } else {
                    $order->setAddress($address_new, Order::ADDRESS_TYPE_LIEFERRECHUNG);
                }

                if ($override_customer_address) {
                    $customer->setAddress($address_new);
                }
                return true;
            }
        }

        $status->add('Adresse')->fail($result->getAddressVerificationResult()->getReason());

        return false;
    }


    public function getDateThreshold(): DateObj
    {
        return $this->date_threshold;
    }

    private function isCheckExistingCustomerEnabled(string $order_origin_id): bool
    {
        static $cache = null;

        if ($cache === null) {
            $cache = $this->db->query("
                SELECT
                    einst_order_origin.order_origin_id,
                    einst_order_origin.skip_new_shop_customer_exisits_check
                FROM
                    einst_order_origin
            ")->asSingleArray('order_origin_id');
        }

        return $cache[$order_origin_id] == 0;
    }
}
