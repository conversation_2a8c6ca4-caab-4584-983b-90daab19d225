<?php

namespace wws\Order\Actions;

use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Model\EntityChanges;
use bqp\Model\SmartDataEntityNotFoundException;
use DomainException;
use env;
use Exception;
use order_repository;
use wws\Order\Order;
use wws\Order\OrderConst;
use wws\Product\ProductLager;

class ActionOrderDelete
{
    private db_generic $db;
    private int $user_id;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
        $this->user_id = env::getUserId();
    }

    public function deleteOrderByOrderId(int $order_id): void
    {
        $this->checkCommonConstraints($order_id);

        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.lieferschein_id
            FROM
                warenausgang_lieferschein
            WHERE
                warenausgang_lieferschein.order_id = '$order_id'
        ");

        if ($result->count()) {
            throw new Exception('Für diesen Auftrag existiert ein Lieferschein, löschen nicht möglich.');
        }

        //product_ids auslesen
        $product_ids = $this->db->query("
            SELECT
                order_item.product_id,
                order_item.lager_id
            FROM
                order_item
            WHERE
                order_item.order_id = '$order_id'
        ")->asArray();

        //bestellung löschen
        $this->db->query("
            DELETE
                orders,
                order_item,
                order_addresses
            FROM
                orders LEFT JOIN
                order_item ON (orders.order_id = order_item.order_id) LEFT JOIN
                order_addresses ON (orders.order_id = order_addresses.order_id)
            WHERE
                orders.order_id = $order_id
        ");

        foreach ($product_ids as $daten) {
            ProductLager::calcOrdersForProduct($daten['product_id'], $daten['lager_id']);
        }

        $entity_changes = new EntityChanges('orders');
        $entity_changes->setEntityId($order_id);
        $entity_changes->addEntityDelete(); //war vorher field = "_del_"
        $entity_changes->save();
    }

    public function deleteOrderItemById(Order $order, int $order_item_id): void
    {
        $order_item = $order->getOrderItemByOrderItemId($order_item_id);

        if ($order_item->getStatus() === OrderConst::STATUS_ARTIKEL_BESTELLT_DIREKTVERSAND) {
            throw new DomainException('Die Auftragsposition wurde bereits geliefert.');
        }

        //prüfen ob lieferschein
        $lieferschein_id = $this->db->fieldQuery("
            SELECT
                warenausgang_lieferschein_items.lieferschein_id
            FROM
                warenausgang_lieferschein_items
            WHERE
                warenausgang_lieferschein_items.order_item_id = $order_item_id
        ");

        if ($lieferschein_id) {
            throw new DomainException('Für die Auftragsposition existiert bereits ein Lieferschein.');
        }

        //
        if (count($order->getOrderItems()) === 1) {
            try {
                $order->getOrderItemByOrderItemId($order_item_id);
            } catch (SmartDataEntityNotFoundException $e) {
                throw new DevException('Bestellung mit einer Position, diese Position weicht von der zu Löschenden ab.', 0, $e);
            }

            $this->deleteOrderByOrderId($order->getOrderId());
            return;
        }

        $order->deleteOrderItemByOrderItemId($order_item_id);
        $order->save();
    }

    private function checkCommonConstraints(int $order_id): void
    {
        //prüfen ob rechnung da
        $rechnungs_id = order_repository::getRechnungsIdByOrderId($order_id);

        if ($rechnungs_id) {
            throw new DomainException('Für diesen Auftrag ist bereits eine Rechnung gelegt.');
        }
    }
}
