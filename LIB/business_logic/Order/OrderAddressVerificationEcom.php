<?php

namespace wws\Order;

use bqp\Address\Address;
use bqp\AddressVerification\AddressVerificationService;
use bqp\AddressVerification\Verificators\AtSyntax;
use bqp\AddressVerification\Verificators\De;
use bqp\AddressVerification\Verificators\DhlPickup;
use bqp\AddressVerification\Verificators\Name;
use Psr\Container\ContainerInterface;
use wws\Country\CountryConst;

class OrderAddressVerificationEcom extends OrderAddressVerification
{
    private AddressVerificationService $address_verification;

    public function __construct(ContainerInterface $container)
    {
        $this->address_verification = new AddressVerificationService();

        $verificators = [
            $container->get(Name::class),
            $container->get(DhlPickup::class),
            $container->get(De::class),
            $container->get(AtSyntax::class)
        ];

        foreach ($verificators as $verificator) {
            $this->address_verification->addVerificator($verificator);
        }
    }


    public function verifyDeliveryAddress(Order $order, ?Address $address = null): OrderAddressVerificationResult
    {
        if (!$address) {
            $address = $order->getLieferAddress();
        }

        $result = $this->address_verification->verify($address);

        if ($result->isValid()) {
            return new OrderAddressVerificationResult(OrderAddressVerificationResult::STATE_OK, $result);
        }

        if ($result->getVerificator() === 'De') {
            //soll laut manuel raus "stimmt i.d.R."
            //-> das verschiebt die Probleme damit letztendlich nur Tiefer ins Sstem... aber "probieren" wir.
            $whitelist_reason = [
                'Ort existiert in PLZ-Bereich nicht.',
                'Strasse existiert in PLZ-Bereich nicht.'
            ];

            if (in_array($result->getReason(), $whitelist_reason)) {
                return new OrderAddressVerificationResult(OrderAddressVerificationResult::STATE_WARN, $result);
            }
        }

        //@todo macht kein sinn... -> wenn das normale De Modul sagt "invalid". Dann ist die nie SyntacticValid (Logik aus OrderAutomaticNewShop übernommen)
        if (
            $address->getCountryId() === CountryConst::COUNTRY_ID_DE &&
            $result->isSyntacticValid() &&
            $order->getEkBrutto() < 5
        ) {
            return new OrderAddressVerificationResult(OrderAddressVerificationResult::STATE_WARN, $result);
        }

        if ($result->isValid()) {
            return new OrderAddressVerificationResult(OrderAddressVerificationResult::STATE_OK, $result);
        }

        if ($result->isSyntacticValid() && $address->isDhlService()) {
            return new OrderAddressVerificationResult(OrderAddressVerificationResult::STATE_WARN, $result);
        }

        return new OrderAddressVerificationResult(OrderAddressVerificationResult::STATE_ERROR, $result);
    }

    public function verifyInvoiceAddress(Order $order, ?Address $address = null): OrderAddressVerificationResult
    {
        if (!$address) {
            $address = $order->getRechnungAddress();
        }

        return $this->verifyDeliveryAddress($order, $address);
    }
}
