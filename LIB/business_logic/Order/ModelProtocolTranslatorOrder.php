<?php

namespace wws\Order;

use order_repository;
use wws\Lager\LagerRepository;
use wws\Shipment\ShipmentRepository;
use wws\Supplier\SupplierRepository;

/**
 * Class ModelProtocolTranslatorOrder
 *
 * Hilfsklasse um das Protokoll
 *
 * @package wws\Order
 */
class ModelProtocolTranslatorOrder
{
    private array $map = [];

    public function __construct()
    {
        $this->map['sped_id'] = ShipmentRepository::getAllSpeds();
        $this->map['lager_id'] = LagerRepository::getAllLager();
        $this->map['anlieferung_lager'] = $this->map['lager_id'];

        $this->map['supplier_id'] = $this->map['eink'] = SupplierRepository::getAllSupplierNames();

        $this->map['status'] = order_repository::getStatusNames();

        $this->map['versand_prioritaet'] = [1 => '<img src="/res/images/icons/package_go.png" alt="">'];
    }

    public function translate(array $rows): array
    {
        foreach ($rows as $key => $row) {
            $rows[$key] = $this->translateRow($row);
        }

        return $rows;
    }

    private function translateRow(array $row): array
    {
        $field = $row['field_name'];

        if (isset($this->map[$field][$row['new_value']])) {
            $text = $this->map[$field][$row['new_value']];

            $row['new_value'] = $text . ' <i style="color: #666;">(' . $row['new_value'] . ')</i>';
        } else {
            $row['new_value'] = htmlentities($row['new_value']);
        }

        return $row;
    }
}
