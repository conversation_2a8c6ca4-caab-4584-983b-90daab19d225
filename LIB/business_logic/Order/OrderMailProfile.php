<?php

namespace wws\Order;

use bqp\Exceptions\FatalException;
use wws\business_structure\Shop;
use wws\Product\ProductConst;

class OrderMailProfile
{
    public const MAIL_TYPE_VERSAND = 'versand';
    public const MAIL_TYPE_INVOICE = 'invoice';

    public const ASWO_INVOICE_MAIL_TEMPLATE = 'customer_rechnung_ASWO-product';
    public const DEFAULT_INVOICE_MAIL_TEMPLATE = 'customer_rechnung';
    public const CUSTOMER_SERVICE_INVOICE_MAIL_TEMPLATE = 'shop_zugang_reparatur_service_link';
    public const DEFAULT_INVOICE_INVOICE_MAIL_TEMPLATE = 'customer_rechnung_rechnung';

    private Order $order;

    public function setOrder(Order $order): void
    {
        $this->order = $order;
    }

    /**
     * prüft ob ein bestimmter mailtyp gesendet werden soll
     *
     * @param $mail_type
     * @return bool
     */
    public function isMail(string $mail_type): bool
    {
        if ($mail_type === self::MAIL_TYPE_VERSAND) {
            if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
                return false;
            }

            if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_KAUFLAND) {
                return false;
            }
        }

        if ($mail_type === self::MAIL_TYPE_INVOICE) {
            if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_REWE) {
                return false;
            }

            if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
                return false;
            }

            if ($this->order->getShopId() === Shop::ERSATZTEILSHOP) {
                if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
                    return false;
                }
                if ($this->order->getZahlungsart() == OrderConst::PAYMENT_VERRECHNUNG) {
                    return false;
                }
            }

            return true;
        }


        return true;
    }

    public function resolveMailId(string $mail_id): string
    {
        if (strpos($mail_id, '{') === 0) {
            switch ($mail_id) {
                case '{invoice}':
                    return $this->getInvoiceMailTemplate();
                default:
                    throw new FatalException('unknown macro template id');
            }
        }

        return $mail_id;
    }

    /**
     * als zwischenlösung -> erstmal die logik aus order rausziehen. gehört eigentlich mit in die resolveMailId() methode
     *
     * @return string
     */
    public function getInvoiceMailTemplate(): string
    {
        $mail_template = self::DEFAULT_INVOICE_MAIL_TEMPLATE;

        if ($this->order->getZahlungsart() == OrderConst::PAYMENT_RECHNUNG) {
            $mail_template = self::DEFAULT_INVOICE_INVOICE_MAIL_TEMPLATE;
        }

        if ($this->order->getShopId() === Shop::ERSATZTEILSHOP) {
            $mail_template = $this->changeMailTemplateIfAswoProductAvailable($mail_template);
            //$mail_template = $this->changeMailTemplateIfCustomerServiceProductAvailable($mail_template);
        }

        if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_AMAZON) {
            $mail_template = 'amz_customer_rechnung';
        }

        if ($this->order->getOrderOriginId() === OrderConst::ORDER_ORIGIN_KAUFLAND) {
            $mail_template = 'kaufland_customer_rechnung';
        }

        return $mail_template;
    }


    private function changeMailTemplateIfAswoProductAvailable(string $template_name): string
    {
        foreach ($this->order->getOrderItems() as $item) {
            if ($item->getTyp() === OrderConst::WARENKORB_TYP_ASWO) {
                return self::ASWO_INVOICE_MAIL_TEMPLATE;
            }
        }

        return $template_name;
    }

    private function changeMailTemplateIfCustomerServiceProductAvailable(string $template_name): string
    {
        foreach ($this->order->getOrderItems() as $item) {
            if (ProductConst::CUSTOMER_SERVICE_PRODUCT === $item->getProductId()) {
                return self::CUSTOMER_SERVICE_INVOICE_MAIL_TEMPLATE;
            }
        }

        return $template_name;
    }
}
