<?php

namespace bqp\extern\Sonepar;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use wws\Lager\WarenausgangLieferscheinRepository;
use wws\Tracking\TrackingCarrier\TrackingCarrierConst;

//Von sonepar bekommen wir Tracking Urls, wo bei Köster immer die PLZ angegeben werden muss, das modul erweitert die Urls entsprechend.
class TrackingLinkBuilderSoneparKoester
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function improveUrl(int $lieferschein_id, string $tracking_url): string
    {
        if (!$tracking_url) {
            return $tracking_url;
        }

        //prüfen ob die url genau so aussieht: https://weborder.koester-hapke-sped.com/tracking/result/4035899000004?colliNumber=00340358994009665807
        if (!preg_match('~^https://weborder\.koester-hapke-sped\.com/tracking/result/\d+\?colliNumber=\d+$~', $tracking_url)) {
            return $tracking_url;
        }

        $plz = WarenausgangLieferscheinRepository::getRecipientZipcodeByLieferscheinId($lieferschein_id);

        return $tracking_url . '&recipientZipcode=' . $plz;
    }


    public function updateExistingTrackingUrls(): void
    {
        $date_end = new DateObj();
        $date_begin = $date_end->clone()->subSimple('days', '356');

        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein_tracking_ids.lieferschein_id,
                warenausgang_lieferschein_tracking_ids.tracking_id,
                warenausgang_lieferschein_tracking_ids.tracking_url
            FROM
                warenausgang_lieferschein_tracking_ids
            WHERE
                warenausgang_lieferschein_tracking_ids.date_added BETWEEN '" . $date_begin->db('begin') . "' AND '" . $date_end->db('end') . "' AND
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id IN ('" . TrackingCarrierConst::CARRIER_ID_KOESTER . "') AND
                warenausgang_lieferschein_tracking_ids.tracking_url IS NOT NULL
        ")->addCallback(function (array $row) {
            $row['new_url'] = $this->improveUrl($row['lieferschein_id'], $row['tracking_url']);
            return $row;
        })->display();

        foreach ($result as $row) {
            if ($row['tracking_url'] === $row['new_url']) {
                continue;
            }

            $this->db->query("
                UPDATE
                    warenausgang_lieferschein_tracking_ids
                SET
                    warenausgang_lieferschein_tracking_ids.tracking_url = '" . $row['new_url'] . "'
                WHERE
                    warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . $row['lieferschein_id'] . "' AND
                    warenausgang_lieferschein_tracking_ids.tracking_id = '" . $row['tracking_id'] . "'
            ");
        }
    }
}
