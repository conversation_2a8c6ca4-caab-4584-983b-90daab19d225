<?php

namespace bqp\extern\dgh;

use bqp\Address\Address;
use bqp\Address\AddressFitting;
use bqp\Address\AddressFittingException;

class DghAddressFitting extends AddressFitting
{
    public function init(): void
    {
        $this->setMapping([
            'salutation' => ['description' => 'Anrede', 'max_length' => 10],
            'name1' => ['description' => 'Name 1', 'max_length' => 25],
            'name2' => ['description' => 'Name 2', 'max_length' => 25],
            'name3' => ['description' => 'Name 3', 'max_length' => 25],
            'name4' => ['description' => 'Name 4', 'max_length' => 25],
            'company' => ['description' => 'firma', 'max_length' => 25],
            'street' => ['description' => 'Strasse/Hausnummer', 'max_length' => 25],
            'city' => ['description' => 'Ort', 'max_length' => 25],
            'zipcode' => ['description' => 'PLZ', 'max_length' => 7],
            'country' => ['description' => 'Land (ISO)']
        ]);

        $this->setDstCharset('UTF8');
    }

    protected function processAddress(Address $address): void
    {
        switch ($address->getAnrede()) {
            case 'Herr':
                $this->set('salutation', 1);
                break;
            case 'Frau':
                $this->set('salutation', 2);
                break;
            case '':
                $this->set('salutation', 0); // 0 = keine Angabe / 3 = Firma
                break;
            default:
                throw new AddressFittingException('unknown salution');
        }

        $this->set('name1', $address->getName());
        $this->set('name2', $address->getVorname());
        $this->set('company', $address->getFirma());

        $this->set('street', $address->getAdresse1());
        $this->set('name3', $address->getAdresse2());

        $this->set('zipcode', $address->getPlz());
        $this->set('city', $address->getOrt());
        $this->set('country', $address->getCountrySign());
    }
}
