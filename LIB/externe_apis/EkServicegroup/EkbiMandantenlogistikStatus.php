<?php

namespace bqp\extern\EkServicegroup;

use bqp\Csv\TableReader;
use bqp\Csv\TableReaderCsvSource;
use bqp\Date\DateObj;
use bqp\Exceptions\DevException;
use bqp\FileImport\FileImportService;
use wws\Supplier\SuppliersConst;
use wws\Lager\WarenausgangLieferscheinRepository;
use wws\Order\Actions\fulfillment_finalize_collector;
use wws\Tracking\TrackingCarrier\TrackingCarrierConst;
use wws\Tracking\TrackingLegacy;

class EkbiMandantenlogistikStatus
{
    private FileImportService $file_import_service;

    public function __construct(FileImportService $file_import_service)
    {
        $this->file_import_service = $file_import_service;
    }

    public function pollNewMessages(): void
    {
        $this->file_import_service->fetchFiles('import_service.ekbi.mandantenlogitistik');
    }

    public function processNewMessages(): void
    {
        $messages = $this->file_import_service->getNewMessages('import_service.ekbi.mandantenlogitistik');

        foreach ($messages as $message) {

            throw new DevException('das sollte tot sein... / ansonsten brauchen wir das noch / bzw. muss das auf die supplier order confirmations umgestellt werden');

            try {
                $this->processMessage($message);

                $this->file_import_service->setMessageStatus($message, $this->file_import_service::MESSAGE_STATUS_PROCESSED);
            } catch (\Exception $e) {
                \debug::dumpException($e);
                $this->file_import_service->setMessageStatus($message, $this->file_import_service::MESSAGE_STATUS_FAILED);
            }
        }
    }

    public function processMessage(\bqp\FileImport\FileImportServiceMessage $message): void
    {
        $content = $message->getFileByPattern('~\.csv$~')->getContent();

        $src = new TableReaderCsvSource();
        $src->setCsvString(trim($content), 'ISO-8859-15');

        $csv = new TableReader($src);
        $csv->setInputFormatStatic([
            'kundennr' => ['typ' => 'string', 'checkHeader' => 'KundenNr'],
            'lieferdatum' => ['typ' => 'string', 'checkHeader' => 'Lieferdatum'],
            'name' => ['typ' => 'string', 'checkHeader' => 'Name'],
            'plz' => ['typ' => 'string', 'checkHeader' => 'PLZ'],
            'ort' => ['typ' => 'string', 'checkHeader' => 'Ort'],
            'artikelnr' => ['typ' => 'string', 'checkHeader' => 'ArtikelNr'],
            'bezeichnung' => ['typ' => 'string', 'checkHeader' => 'Bezeichnung'],
            'zulief_menge' => ['typ' => 'string', 'checkHeader' => 'Zulief. Menge'],
            'liefermenge' => ['typ' => 'string', 'checkHeader' => 'Liefermenge'],
            'lfsnr' => ['typ' => 'string', 'checkHeader' => 'LfsNr'],
            'bestellnr' => ['typ' => 'string', 'checkHeader' => 'BestellNr'],
            'spediteur' => ['typ' => 'string', 'checkHeader' => 'Spediteur'],
            'send_verfolgung' => ['typ' => 'string', 'checkHeader' => 'Send. Verfolgung'],
            'auftrnr' => ['typ' => 'string', 'checkHeader' => 'AuftrNr'],
        ]);

        //$csv->display();

        $exceptions = [];

        foreach ($csv as $row) {
            try {
                $this->processDaten($row['spediteur'], $row['send_verfolgung'], $row['auftrnr'] . ' ' . $row['bestellnr'], $row['lfsnr']);
            } catch (\Exception $e) {
                $exceptions[] = $e->getMessage();
                $exceptions[] = $row;
            }
        }

        if ($exceptions) {
            var_dump($exceptions);
            throw new \Exception('CSV konnte nicht vollständig verarbietet werden');
        }
    }

    private function processDaten(string $sped_name, string $tracking_url, string $reference, string $ekbi_delivery_slip_reference): void
    {
        $collector = new fulfillment_finalize_collector();
        $collector->addRestrictedSupplierId(SuppliersConst::SUPPLIER_ID_EKBI_MANDATENLOGISTIK);
        $collector->setSearchOpenDirectGrosOrder(true);
        $collector->setSearchAllDirectGrosOrder(true);
        $collector->setOrderlessReference($reference);
        $collector->fill();

        $supplier_order = $collector->getSupplierOrder();

        if ($supplier_order->getStatus() === $supplier_order::STATUS_BESTELLT) {
            $supplier_order->doBestellungErledigt();
        }

        $lieferschein_id = WarenausgangLieferscheinRepository::getLastLieferscheinIdByOrderId($supplier_order->getOrderId());

        if (!$lieferschein_id) {
            return;
        }

        switch ($sped_name) {
            case 'Dachser':
                $tracking_carrier_id = TrackingCarrierConst::CARRIER_ID_DACHSER;
                $tracking_id = substr($tracking_url, strrpos($tracking_url, '=') + 1);

                TrackingLegacy::addTrackingIdWithTrackingCarrierId(
                    $lieferschein_id,
                    $tracking_carrier_id,
                    $tracking_id,
                    new DateObj(),
                    $tracking_url
                );

                break;
            default:
                throw new \Exception('unknown sped');
        }
    }
}
