<?php

namespace bqp\extern\Zufall;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use wws\Lager\WarenausgangLieferscheinRepository;
use wws\Order\Order;

class TrackingLinkBuilderZufall
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }


    public function buildLinkByLieferscheinId(int $lieferschein_id, string $tracking_id): string
    {
        $plz = WarenausgangLieferscheinRepository::getRecipientZipcodeByLieferscheinId($lieferschein_id);

        return $this->buildLink($tracking_id, $plz);
    }

    public function buildLink(string $tracking_id, string $plz): string
    {
        return 'https://tracking.myzufall.de/Track/' . $tracking_id . '/' . $plz;
        //return 'https://tracking.myzufall.de/Track/' . $tracking_id . '?postcode=' . $plz; //natürlich... warum hab ich nur das gefühl, dass wir das in einem Monat wieder ändern...
    }

    public function updateExistingTrackingUrls(): void
    {
        $date_end = new DateObj();
        $date_begin = $date_end->clone()->subSimple('days', '356');

        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.lager_id,
                warenausgang_lieferschein.auftnr,
                warenausgang_lieferschein_tracking_ids.lieferschein_id,
                warenausgang_lieferschein_tracking_ids.date_added,
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                warenausgang_lieferschein_tracking_ids.tracking_id,
                warenausgang_lieferschein_tracking_ids.datum,
                warenausgang_lieferschein_tracking_ids.tracking_url,
                order_addresses.plz
            FROM
                warenausgang_lieferschein_tracking_ids INNER JOIN
                tracking_carrier ON (warenausgang_lieferschein_tracking_ids.tracking_carrier_id = tracking_carrier.tracking_carrier_id) INNER JOIN
                warenausgang_lieferschein ON (warenausgang_lieferschein_tracking_ids.lieferschein_id = warenausgang_lieferschein.lieferschein_id) INNER JOIN
                order_addresses ON (warenausgang_lieferschein.order_id = order_addresses.order_id AND order_addresses.address_type IN ('" . Order::ADDRESS_TYPE_LIEFER . "', '" . Order::ADDRESS_TYPE_LIEFERRECHUNG . "'))
            WHERE
                warenausgang_lieferschein_tracking_ids.date_added BETWEEN '" . $date_begin->db('begin') . "' AND '" . $date_end->db('end') . "' AND
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id IN ('" . \wws\Tracking\TrackingCarrier\TrackingCarrierConst::CARRIER_ID_ZUFALL . "')
        ")->addCallback(function (array $row) {
            $row['url'] = $this->buildLink($row['tracking_id'], $row['plz']);
            return $row;
        })->display();

        foreach ($result as $row) {
            if (!$row['url']) {
                continue;
            }

            $this->db->query("
                UPDATE
                    warenausgang_lieferschein_tracking_ids
                SET
                    warenausgang_lieferschein_tracking_ids.tracking_url = '" . $row['url'] . "'
                WHERE
                    warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . $row['lieferschein_id'] . "' AND
                    warenausgang_lieferschein_tracking_ids.tracking_id = '" . $row['tracking_id'] . "'
            ");
        }
    }
}
