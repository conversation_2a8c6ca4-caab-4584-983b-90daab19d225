<?php

namespace bqp\extern\Indoba;

use bqp\Csv\CsvReader;
use bqp\Date\DateObj;
use bqp\Utils\FileUtils;
use config;
use DateTime;
use wws\MarketView\Entities\MarketViewFeatures;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Import\MarketViewImportSimple;

class MarketViewImportIndoba extends MarketViewImportSimple
{
    public function updateComplete(): void
    {
        parent::updateComplete();
        /*if(!file_exists($this->config['csv'])) {
            return false;
        }

        $this->fullUpdate_prepare();

        $this->parseCsv($this->config['csv']);

        $this->list_logger->addRuntimeMark('read_1_end');

        if(isset($this->config['csv2']) && file_exists($this->config['csv2'])) {
            $this->clearOldDaten();
            $this->parseCsv($this->config['csv2']);
        }

        $this->list_logger->addRuntimeMark('read_2_end');

        $this->fullUpdate_ended();

        parent::updateComplete();

        $this->protokoll->wok('CSV eingelesen', $this->list_logger->getSummaryAsText());

        $this->clear($this->config['csv']);

        if(isset($this->config['csv2']) && file_exists($this->config['csv2'])) {
            $this->clear($this->config['csv2']);
        }*/
    }


    /*public function clear($csv) {
        $pos = strrpos($csv, '/');

        $path = substr($csv, 0, $pos);
        $file = substr($csv, $pos + 1);

        if(file_exists($path.'/last/'.$file)) {
            unlink($path.'/last/'.$file);
        }

        $file = date('Y-m-d-His').'-'.$file;

        rename($csv, $path.'/last/'.$file);
    }*/

    public function clear($csv)
    {
        $dst = FileUtils::appendDir($csv, 'LAST/');

        rename($csv, $dst);
    }

    public function processPricelist($file)
    {
        $csv = new CsvReader($file);

        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string', 'checkHeader' => 'Artikelnummer '],
            'product_name' => ['typ' => 'string', 'checkHeader' => 'Bezeichnung'],
            'product_line' => ['typ' => 'string', 'checkHeader' => 'Foto'],
            'material' => ['typ' => 'string', 'checkHeader' => 'Material'],
            'l_produkt' => ['typ' => 'float', 'checkHeader' => 'L produkt'],
            'b_produkt' => ['typ' => 'float', 'checkHeader' => 'B produkt'],
            'h_produkt' => ['typ' => 'float', 'checkHeader' => 'H produkt'],
            'l_packung' => ['typ' => 'float', 'checkHeader' => 'L packung'],
            'b_packung' => ['typ' => 'float', 'checkHeader' => 'B packung'],
            'h_packung' => ['typ' => 'float', 'checkHeader' => 'H packung'],
            'vpe' => ['typ' => 'string', 'checkHeader' => 'VPE'],
            'gewicht_netto_kg' => ['typ' => 'float', 'checkHeader' => 'Gewicht netto kg'],
            'gewicht_brutto_kg' => ['typ' => 'float', 'checkHeader' => 'Gewicht brutto kg'],
            'load_40hc' => ['typ' => 'string', 'checkHeader' => 'Load 40\'HC'],
            'volumen' => ['typ' => 'string', 'checkHeader' => 'Volumen'],
            'vk_netto' => ['typ' => 'float', 'checkHeader' => 'Listenpreis netto'],
            'uvp_brutto' => ['typ' => 'float', 'checkHeader' => 'UVP brutto'],
            'versand_classification' => ['typ' => 'string', 'checkHeader' => 'versand_classification'],
        ]);

        foreach ($csv as $daten) {
            $this->savePricelistProduct($daten);
        }
    }

    public function savePricelistProduct($daten)
    {

        //var_dump($daten);

        $product = [];
        $product['mvsrc_product_id'] = $daten['mvsrc_product_id'];
        if (preg_match('~^([^ ]*) \(B~', $product['mvsrc_product_id'], $temp)) {
            $product['mvsrc_product_id'] = $temp[1];
        }

        /*$product['hersteller_name'] = 'indoba';
        $product['product_name'] = preg_replace('~ {2,}~', ' ', $daten['product_name']);
        $product['product_line'] = $daten['product_line'];
        if($product['product_line']) {
            $product['product_name'] = $product['product_line'].' - '.$product['product_name'];
        }
        //$product['vk_netto'] = $daten['vk_netto'];
        $product['uvp'] = $daten['uvp_brutto'];
        $product['vpe'] = $daten['vpe'];
        if($daten['vpe'] > 1) {
            $product['vk_netto_info'] = 'VPE: '.$daten['vpe'];
            $product['vpe_zwang'] = 1;
        }

        $product['breite'] = $daten['b_produkt'];
        $product['hoehe'] = $daten['h_produkt'];
        $product['tiefe'] = $daten['l_packung'];
        $product['gewicht_kg'] = $daten['gewicht_netto_kg'];
        */

        $product['versand_netto'] = $this->versandClassificationToNettoPrice($daten['versand_classification']);

        /*$struct = $this->getFeatureStruct($product['mvsrc_product_id']);

        $struct->addFeature('material', preg_replace('~ {2,}~', ' ', $daten['material']), 'Material');
        $struct->addFeature('gewicht_brutto', $daten['gewicht_brutto_kg'], 'Gewicht Brutto', 'kg');
        $struct->addFeature('gewicht_netto', $daten['gewicht_netto_kg'], 'Gewicht Netto', 'kg');
        if($daten['l_produkt']) {
            $struct->addFeature('dimension', $daten['l_produkt'].'x'.$daten['b_produkt'].'x'.$daten['h_produkt'], 'Produkt Abmessungen', 'cm (LxBxH)');
        }

        if($daten['l_packung']) {
            $struct->addFeature('dimension_logistic', $daten['l_packung'].'x'.$daten['l_packung'].'x'.$daten['l_packung'], 'Versand Abmessungen', 'cm (LxBxH)');
        }

        $product['features_struct'] = $struct;
        */

        $this->saveProduct($product);
    }

    /**
     * @param $mvsrc_product_id
     * @return MarketViewFeatures
     */
    public function getFeatureStruct($mvsrc_product_id)
    {
        return new MarketViewFeatures();
    }


    public function readDescriptions($file)
    {
        $csv = new CsvReader($file);

        $csv->setInputFormatStatic([
            'itemno' => ['typ' => 'string', 'checkHeader' => 'ItemNo'],
            'itemean' => ['typ' => 'string', 'checkHeader' => 'ItemEAN'],
            'itemtextname' => ['typ' => 'string', 'checkHeader' => 'ItemTextName'],
            'itemtextdescription' => ['typ' => 'string', 'checkHeader' => 'ItemTextDescription'],
            'itemtexttechnicaldata' => ['typ' => 'string', 'checkHeader' => 'ItemTextTechnicalData'],
            'itemmrp' => ['typ' => 'string', 'checkHeader' => 'ItemMRP'],
        ]);

        foreach ($csv as $row) {
            if (!$row['itemno']) {
                continue;
            }

            $beschreibung = $row['itemtextdescription'] . '' . $row['itemtexttechnicaldata'];

            $beschreibung = preg_replace('~<br>\s*<a .*?</a>~', '', $beschreibung);

            $product = [
                'mvsrc_product_id' => $row['itemno'],
                'beschreibung' => $beschreibung,
                'ean' => $row['itemean']
            ];

            //<br><br></li>
            var_dump($product);

            $this->saveProduct($product);
        }
    }


    public function readStocklist()
    {
        $csv = $this->config['import_dir'] . 'stock_' . date('Ymd-His') . '.csv';

        $status = $this->loadStocklist($csv);

        if ($status) {
            $this->processStocklist($csv);
            $this->clear($csv);
            $this->protokoll->wok('bestandsliste erfolgreich eingelesen', $this->list_logger->getSummaryAsText());
        } else {
            $this->protokoll->winfo('no stocklist found');
        }
    }

    public function loadStocklist(string $dst_file): bool
    {
        $url = 'https://stock.indoba.com/bestand_haendler.csv';

        try {
            //vorsichtig mit deduplikation -> die Liste ändert sich u.U. relativ lange nicht. Das ext dann die Verfügbarkeiten.

            $stocklist = file_get_contents($url);
            $stocklist = trim($stocklist);

            if (!$stocklist || $stocklist === "\xEF\xBB\xBF") { //leere datei / bzw nur BOM
                return false;
            }

            file_put_contents($dst_file, $stocklist);
            return true;
        } catch (\ErrorException $e) {
            return false;
        }
    }

    public function processStocklist($file)
    {
        $csv = new CsvReader($file);
        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['typ' => 'string', 'checkHeader' => ['Artikelnummer', 'Artiklenummer']],
            'product_name' => ['typ' => 'string', 'checkHeader' => 'Artikelname'],
            'ean' => ['typ' => 'string', 'checkHeader' => 'EAN/Barcode'],
            'inventory' => ['typ' => 'int', 'checkHeader' => 'verfuegbare Menge'],
            'naechster_zulauf' => ['typ' => 'string', 'checkHeader' => 'naechster Zulauf'],
            'artikelsegment' => ['typ' => 'string', 'checkHeader' => 'Artikelsegment']
        ]);
        $csv->skipOffSizeLines(false);

        foreach ($csv as $row) {
            if ($row['product_name'] === 'Konditionen') {
                continue;
            }

            unset($row['ean']); //teilweise falsch in bestandsliste

            //produktnummern mit teilweise 28 Zeichen... klasse! kürzen stellt zumindestens derzeit kein Problem dar.
            $row['mvsrc_product_id'] = \bqp\Utils\StringUtils::limit($row['mvsrc_product_id'], 24);

            $this->saveProductStocklist($row);
        }

        $csv->closeFile();
    }

    protected function saveProductStocklist($daten)
    {
        $daten['mv_cat_id'] = $this->saveCategory(\wws\MarketView\MarketViewConst::TREE_ID_DEFAULT, $daten['artikelsegment']);
        $daten['hersteller_name'] = 'indoba';

        if ($daten['inventory']) {
            $daten['availability_id'] = $this->saveInventory($daten['inventory']);
            $daten['mv_availability'] = $daten['inventory'];
        } else {
            $date = new DateObj(DateTime::createFromFormat('d.m.Y', $daten['naechster_zulauf']));

            $daten['availability_id'] = $this->saveAvailabilityDate($date);
            $daten['mv_availability'] = $daten['naechster_zulauf'];
        }

        parent::saveProduct($daten);
    }

    public function syncpictures()
    {
        $path = config::system('res_dir') . '/market_view/extern/indoba/';
        $url = $this->getMarketViewMediaLokalBaseUrl() . 'extern/indoba/';

        $image_groups = [];

        foreach (glob($path . '*.jpg') as $file) {
            $img = basename($file);

            preg_match('~(.*?)-[0-9sh]{1,3}\.jpg~', $img, $temp);

            if (!isset($image_groups[$temp[1]])) {
                $image_groups[$temp[1]] = [];
            }
            $image_groups[$temp[1]][] = $img;
        }

        foreach ($image_groups as $mvsrc_product_id => $images) {
            sort($images, SORT_NATURAL);

            $s = $images[0];
            $images[0] = $images[1];
            $images[1] = $s;

            foreach ($images as $pos => $image) {
                $media = new MarketViewMedia();
                $media->setMediaType(MarketViewMedia::MEDIA_TYPE_IMAGE_URL);
                $media->setUrl($url . $image);
                $media->setMediaKey($image);
                $media->setPos($pos);
                $media->setMvsrcProductId($mvsrc_product_id);

                $this->addMarketViewMedia($media);
            }
        }
    }

    protected function versandClassificationToNettoPrice(string $versand_classification): ?float
    {
        $avis = 5;

        $package_price = 11.6;

        switch ($versand_classification) {
            case 'Päckchen':
            case 'päckchen':
                return round(7.9, 2);
            case 'Paketversand':
            case 'paket':
                return round($package_price, 2);
            case 'paket + sperrgutzuschlag':
                return $package_price + 6;
            case '2 x paket':
                return round($package_price * 2, 2);
            case '3 x paket':
                return round($package_price * 3, 2);
            case '3 x paket + sperrgutzuschlag':
                return round($package_price * 3 + 6, 2);
            case '4 x paket':
                return round($package_price * 4, 2);
            case '4 x paket + sperrgutzuschlag':
                return round($package_price * 4 + 6, 2);
            case 'Speditionsversand – Einzelkarton':
            case 'Speditions-versand Einzelkarton':
            case 'spedition karton':
                return round(31.9 + $avis, 2); //inkl avis
            case 'Speditions-versand Palette klein':
            case 'Speditionsversand – Palette klein':
            case 'spedition palette klein':
                return round(51.9 + $avis, 2); //inkl avis
            case 'Speditions-versand Palette groß':
            case 'Speditionsversand – Palette groß':
            case 'spedition palette groß':
                return round(62.9 + $avis, 2); //inkl avis
            case '2 Pakete groß + Sperrgutzuschlag':
                return round(2 * $package_price + 5, 2);
            case 'Paket groß + Sperrgutzuschlag':
                return round($package_price + 5, 2);
        }

        throw new \bqp\Exceptions\DevException('unknown shipping ' . $versand_classification);
    }
}
