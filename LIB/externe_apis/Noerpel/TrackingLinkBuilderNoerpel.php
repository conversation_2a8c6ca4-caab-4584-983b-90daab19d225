<?php

namespace bqp\extern\Noerpel;

use bqp\db\db_generic;
use wws\Lager\WarenausgangLieferscheinRepository;

class TrackingLinkBuilderNoerpel
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function buildLinkByLieferscheinId(int $lieferschein_id, string $tracking_id): string
    {
        $plz = WarenausgangLieferscheinRepository::getRecipientZipcodeByLieferscheinId($lieferschein_id);

        return $this->buildLink($tracking_id, $plz);
    }

    public function buildLink(string $tracking_id, string $plz): string
    {
        return 'https://portal.noerpel.de/dw/request/tracking?data=SDG;' . $tracking_id . ';' . $plz;
    }
}
