<?php

namespace bqp\extern\Unzer;

use bqp\Config\ConfigContainer;
use bqp\Csv\CsvReader;
use bqp\Csv\TableReader;
use bqp\Csv\TableReaderCsvSource;
use bqp\Exceptions\FatalException;
use service_loader;
use wws\BankAccount\BankAccountRepository;
use wws\buchhaltung\AccountingConsts;
use wws\business_structure\Shop;
use wws\Order\OrderConst;
use wws\Order\OrderRepository;

class UnzerFactory
{

    public function getConfig(): ConfigContainer
    {
        if (\env::getShopId() === Shop::ALLEGO) {
            return service_loader::getConfigRegistry()->get('ecom/payment/unzer');
        }

        if (\env::getShopId() === Shop::ERSATZTEILSHOP) {
            return service_loader::getConfigRegistry()->get('k11/payment/unzer');
        }
    }

    public function getUnzerWws(): UnzerWws
    {
        return new UnzerWws($this->getConfig());
    }

    public function getUnzerRefundExecuter(): UnzerOrderRefundExecuter
    {
        return new UnzerOrderRefundExecuter($this->getUnzerWws(), service_loader::get(OrderRepository::class));
    }

    public function getUnzerSettlementImport(): UnzerSettlementImport
    {
        return service_loader::get(UnzerSettlementImport::class);
    }

    /**
     * Da in den Unzer Abrechnungen keine Summen oder Salden enthalten sind, kann der Import nicht in sich validiert werden.
     * Es gibt eine Maske in der die Abrechnung zum Kontrollieren aufgelistet werden.
     * Die Methode gibt die relevanten account_ids zurück.
     *
     * @return int[]
     */
    public function getControllingAccountIds(): array
    {
        return [\wws\buchhaltung\AccountingConsts::BANK_ACCOUNT_UNZER, AccountingConsts::BANK_ACCOUNT_UNZER_KK_K11, AccountingConsts::BANK_ACCOUNT_UNZER_SOFORT_K11];
    }

    /**
     * gibt ein vorkonfiguierten (inkl. account_id und zahlungs_id) Importer für die entsprechende Datei zurück.
     */
    public function getUnzerSettlementImportByFile(string $filename, string $blob, bool $amex = false): UnzerSettlementImport
    {
        $account_id = null;
        $import_class = null;

        if (substr($filename, -4) === '.pdf') {
            $settlement_reference = UnzerSettlementImport::extractSettlementReferenceFromPdfFilename($filename);

            if ($amex) {
                $possible_account_ids = [AccountingConsts::BANK_ACCOUNT_UNZER_AMEX_K11];
            } else {
                $possible_account_ids = [AccountingConsts::BANK_ACCOUNT_UNZER, AccountingConsts::BANK_ACCOUNT_UNZER_KK_K11, AccountingConsts::BANK_ACCOUNT_UNZER_SOFORT_K11];
            }

            $db = \db::getInstance();
            $account_id = (int)$db->fieldQuery("
                SELECT
                    buchhaltung_bank_settlements.account_id
                FROM
                    buchhaltung_bank_settlements
                WHERE
                    buchhaltung_bank_settlements.settlement_reference = '" . $db->escape($settlement_reference) . "' AND
                    buchhaltung_bank_settlements.account_id IN (" . $db->in($possible_account_ids) . ")
                LIMIT
                    1
            ");

            if (!$account_id) {
                throw new \Exception('Es wurde noch keine Abrechnung für diese PDF gefunden.');
            }

            $import_class = UnzerSettlementImportPdf::class;
        } else {
            $src = new TableReaderCsvSource();
            $src->setCsvString($blob, 'UTF-8');

            $fixed_src = new UnzerCsvSoruceMitigation($src);

            $reader = new TableReader($fixed_src);
            $reader->setSkipOffSizeLineHandler(function () {
                throw new FatalException('malformed csv file!');
            });

            //die gehen mir auch aufm sack... jetzt knallen die da scheinbar Grundgebühren rein
            //statt $row = $reader->getRow(); die ersten Zeilen mit INTERNAL_SERVICES überspringen
            //und nochmal anders... jetzt taucht da ein block buchungen zu beginn auf, die komisch aussehen. (kein channelname, nur eine gesetzt id, etc, keine ahnung was das ist -> werden auch ignoriert. Summem gehen mit der PDF auf)
            $row_first = null;
            $row_channel = null;

            foreach ($reader as $row) {
                if (isset($row['method']) && $row['method'] === 'INTERNAL_SERVICES') {
                    continue;
                }

                if ($row_first === null) {
                    $row_first = $row;
                }

                if (!empty($row['channelname'])) {
                    $row_channel = $row;
                }

                if ($row_first && $row_channel) {
                    break;
                }
            }

            $row = $row_first;
            if ($row_channel) {
                $row = $row_channel;
            }
            //

            //ecom
            if (isset($row['channelname']) && isset($row['method']) && $row['channelname'] === 'allego.de' && $row['method'] === 'credit_card') {
                $account_id = AccountingConsts::BANK_ACCOUNT_UNZER;
                $import_class = UnzerSettlementImportEFormatCreditCard::class;
            }

            if (
                (
                    (isset($row['channelname']) && $row['channelname'] === 'smartgoods.de') ||
                    (isset($row['channelname']) && str_contains($row['descriptor'], 'smartgoods'))
                ) &&
                isset($row['method']) &&
                $row['method'] === 'credit_card'
            ) {
                $account_id = AccountingConsts::BANK_ACCOUNT_UNZER;
                $import_class = UnzerSettlementImportEFormatCreditCard::class;
            }

            //k11
            if (isset($row['channelname']) && isset($row['method']) && $row['channelname'] === 'ersatzteilshop.de' && $row['method'] === 'sofort') {
                $account_id = AccountingConsts::BANK_ACCOUNT_UNZER_SOFORT_K11;
                $import_class = UnzerSettlementImportEFormatSofort::class;
            }

            if (
                isset($row['descriptor']) &&
                isset($row['method']) && (
                    str_contains($row['descriptor'], 'ersatzteilshop.de') ||
                    ($row['channelname'] ?? '') === 'ersatzteilshop.de'
                ) &&
                $row['method'] === 'credit_card'
            ) {
                $account_id = AccountingConsts::BANK_ACCOUNT_UNZER_KK_K11;
                $import_class = UnzerSettlementImportEFormatCreditCard::class;

                if ($amex) {
                    $account_id = AccountingConsts::BANK_ACCOUNT_UNZER_AMEX_K11;
                    $import_class = UnzerSettlementImportEFormatAmex::class;
                }
            }

            if (isset($row['inv_no']) && in_array(substr($row['inv_no'], 0, 3), ['HSO', 'HSL'])) {
                throw new UnzerSettlementException('HSO/HSL werden nicht mehr unterstützt.');
            }

            //
            if (!$account_id) {
                \debug::dump($row);
                throw new \Exception('Unbekanntes Abrechnungsformat.');
            }

            if ($amex && $import_class !== UnzerSettlementImportEFormatAmex::class) {
                throw new \Exception('Für dieses Abrechnungsformat ist kein AMEX Import definiert!');
            }
        }

        service_loader::get(BankAccountRepository::class)->assertAccountIdExists($account_id);

        $instance = service_loader::getDiContainer()->make($import_class, [
            'account_id' => $account_id,
            'booker' => $this->getAccountBooker($account_id)
        ]);

        return $instance;
    }

    public function getAccountBooker(int $account_id): UnzerSettlementBooker
    {
        $db = \db::getInstance();

        return match($account_id) {
            AccountingConsts::BANK_ACCOUNT_UNZER => new UnzerSettlementBookerEcom($db, $account_id, OrderConst::PAYMENT_ECOM_UNZER),
            AccountingConsts::BANK_ACCOUNT_UNZER_KK_K11 => new UnzerSettlementBookerK11($db, $account_id, OrderConst::PAYMENT_KREDITKARTE),
            AccountingConsts::BANK_ACCOUNT_UNZER_SOFORT_K11 => new UnzerSettlementBookerK11($db, $account_id, OrderConst::PAYMENT_SOFORTUEBERWEISUNG),
            AccountingConsts::BANK_ACCOUNT_UNZER_AMEX_K11 => new UnzerSettlementBookerK11($db, $account_id, OrderConst::PAYMENT_KREDITKARTE)
        };
    }
}
