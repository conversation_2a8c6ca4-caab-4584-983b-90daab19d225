<?php

namespace bqp\extern\Unielektro;

use bqp\Csv\CsvWriter;
use bqp\Csv\TableReader;
use bqp\Csv\TableReaderSpreadsheetSource;
use bqp\Date\DateObj;
use bqp\db\db_generic;
use bqp\Exceptions\DevException;
use bqp\Exceptions\FatalException;
use bqp\FileImport\FileImportService;
use bqp\FileImport\FileImportServiceExceptionMultipleFiles;
use bqp\FileImport\FileImportServiceMessage;
use debug;
use system_protokoll;
use wws\Order\Order;
use wws\Supplier\SupplierOrder;
use wws\Lager\WarenausgangLieferscheinRepository;
use wws\Order\OrderMemo;
use wws\Tracking\TrackingCarrier\TrackingCarrierConst;
use wws\Tracking\TrackingLegacy;

/**
 * Modul um Trackingdaten für Speditionssendungen über Uni zu akquirieren.
 *
 * Wir bekommen von Uni per Mail eine Liste mit Lieferscheinen und über welches Lager diese verschickt wurden.
 * In dieser Liste können unserer Referenzen nicht einfügt werden!
 * Diese Liste wird mit den vermeidlichen Trackingdaten in einer extra Tabelle in der Datenbank gespeichert.
 * Über die Edifact-Schnittstelle bekommen wir die Uni Lieferscheinnummern. Diese werden beim Einlesen über diese Klasse
 * auch in diese Tabelle eingetragen.
 *
 * Was zuerst stattfindet, ist Zufall. Es muss beachtet werden
 * 1. Sendungsdaten vorhanden, aber noch keine Zuordnung zu unseren Aufträgen
 * 2. Zuordnung vorhanden, aber keine Sendungsdaten
 *
 * Die Klasse bildet dies ab, mit möglichst kurzen verzögerungen.
 */
class UnielektroTracking
{
    private db_generic $db;
    private FileImportService $file_import_service;

    private system_protokoll $logger;

    public function __construct(
        db_generic $db,
        FileImportService $file_import_service
    ) {
        $this->db = $db;
        $this->file_import_service = $file_import_service;

        $this->logger = system_protokoll::getInstance('uni_tracking2');
    }


    public function syncToCustomerOrder(string $delivery_slip_number): void
    {
        $state = $this->getDeliverySlipState($delivery_slip_number);

        if (!$state['has_supplier_order_id'] || !$state['has_tracking']) {
            throw new DevException('sync call ohne tracking oder supplier_order_id');
        }

        if ($state['state'] !== 'open') {
            throw new FatalException('wurde bereits gesynct');
        }

        $supplier_order = new SupplierOrder($state['supplier_order_id']);

        if ($supplier_order->isDirektLieferung()) {
            $tracking_date = new DateObj($state['tracking_date']);
            $lieferschein_id = WarenausgangLieferscheinRepository::getLastLieferscheinIdByOrderId($supplier_order->getOrderId());

            if (!$lieferschein_id) {
                debug::dump('Lieferung ohne $lieferschein_id (sollte nur bei stornierten Bestellungen vorkommen)', $supplier_order->getBestellNr());
                return;
            }

            switch ($state['carrier_name']) {
                case 'Emons':
                    $memo = new OrderMemo("Direktversand über Uni mit Emons.\nVersanddatum: " . $tracking_date->format('d.m.Y') . "\nUni-Lieferscheinnummer: " . $delivery_slip_number, OrderMemo::VERSAND);

                    $order = new Order($supplier_order->getOrderId());
                    $order->addOrderMemo($memo);
                    $order->save();

                    if ($state['tracking_id']) {
                        TrackingLegacy::addTrackingIdWithTrackingCarrierId($lieferschein_id, TrackingCarrierConst::CARRIER_ID_EMONS, $state['tracking_id'], $tracking_date);
                    }
                    break;
                case 'DHL Fracht':
                    TrackingLegacy::addTrackingIdWithTrackingCarrierId($lieferschein_id, TrackingCarrierConst::CARRIER_ID_DHL_FRACHT, $state['tracking_id'], $tracking_date);
                    break;
                case 'Dachser':
                    TrackingLegacy::addTrackingIdWithTrackingCarrierId($lieferschein_id, TrackingCarrierConst::CARRIER_ID_DACHSER, $state['tracking_id'], $tracking_date);
                    break;
                default:
                    debug::dump($state);
                    throw new DevException('unknown carrier name');
            }
        }

        $this->db->query("
            UPDATE
                ext_uni_tracking_queue
            SET
                ext_uni_tracking_queue.state = 'success'
            WHERE
                ext_uni_tracking_queue.delivery_slip_number = '" . $this->db->escape($delivery_slip_number) . "'
        ");
    }


    private function getDeliverySlipState(string $delivery_slip_number): array
    {
        $row = $this->db->singleQuery("
            SELECT
                ext_uni_tracking_queue.delivery_slip_number,
                ext_uni_tracking_queue.delivery_slip_date,
                ext_uni_tracking_queue.date_added,
                ext_uni_tracking_queue.supplier_order_id,
                ext_uni_tracking_queue.state,
               	ext_uni_tracking_queue.carrier_name,
               	ext_uni_tracking_queue.tracking_id,
               	ext_uni_tracking_queue.tracking_date
            FROM
                ext_uni_tracking_queue
            WHERE
                ext_uni_tracking_queue.delivery_slip_number = '" . $this->db->escape($delivery_slip_number) . "'
        ");

        if (!isset($row['delivery_slip_number'])) {
            $this->db->query("
                INSERT INTO
                    ext_uni_tracking_queue
                SET
                    ext_uni_tracking_queue.delivery_slip_number = '" . $this->db->escape($delivery_slip_number) . "',
                    ext_uni_tracking_queue.date_added = NOW(),
                    ext_uni_tracking_queue.state = 'open'
            ");
        }

        $row['has_tracking'] = (bool)($row['carrier_name'] ?? false);
        $row['has_supplier_order_id'] = (bool)($row['supplier_order_id'] ?? false);

        return $row;
    }


    public function addToQueue(string $delivery_slip_number, DateObj $delivery_slip_date, int $supplier_order_id): void
    {
        $state = $this->getDeliverySlipState($delivery_slip_number);

        if ($state['has_supplier_order_id']) {
            return;
        }

        $this->db->query("
            UPDATE
                ext_uni_tracking_queue
            SET
                ext_uni_tracking_queue.supplier_order_id = '" . $this->db->escape($supplier_order_id) . "',
                ext_uni_tracking_queue.delivery_slip_date = '" . $delivery_slip_date->db('date') . "'
            WHERE
                ext_uni_tracking_queue.delivery_slip_number = '" . $this->db->escape($delivery_slip_number) . "'
        ");

        if ($state['has_tracking']) {
            $this->syncToCustomerOrder($delivery_slip_number);
        }
    }


    public function pollMessages(): void
    {
        $this->file_import_service->fetchFiles('import_service.unitracking2');

        $messages = $this->file_import_service->getNewMessages('import_service.unitracking2');

        foreach ($messages as $message) {
            $this->processMessage($message);
        }
    }

    private function processMessage(FileImportServiceMessage $message): void
    {
        try {
            $file = $message->getFileByPattern('~\.xlsx$~');

            $this->createDebugCsv($file->getContent(), $message->getMetaData('date'));
            $this->processExcel($file->getContent());

            $this->file_import_service->setMessageStatus($message, $this->file_import_service::MESSAGE_STATUS_PROCESSED);

            $this->logger->wok($message->getName() . ' verarbeitet');
        } catch (FileImportServiceExceptionMultipleFiles $e) {
            //var_dump('fail: ',$message->getName());
            $this->file_import_service->setMessageStatus($message, $this->file_import_service::MESSAGE_STATUS_FAILED);

            $this->logger->wfail($message->getName() . ': ' . $e->getMessage());
        } catch (\bqp\Csv\CsvFormatViolationException $e) {
            debug::dump('TableReader Format Verletzung ' . $e->getMessage());
            debug::dump($message->getName());
            $this->file_import_service->setMessageStatus($message, $this->file_import_service::MESSAGE_STATUS_FAILED);

            $this->logger->wfail($message->getName() . ': ' . $e->getMessage());
        }
    }

    /**
     * Teilweise fehlen Daten. Das zu prüfen wenn die Daten in Excel Dateien in Mails verpackt stecken, ist richtig nervig.
     * ->die Excel Dateien zusätzlich als CSV archivieren. Da kann ggf. schnell und großflächig mit grep geprüft werden.
     *
     * @param string $xlsx_blob
     * @param string $date
     * @return void
     */
    public function createDebugCsv(string $xlsx_blob, string $date): void
    {
        $date = str_replace(':- ', '', $date);

        $source = new TableReaderSpreadsheetSource();
        $source->loadByString($xlsx_blob, $source::FORMAT_XLSX);

        $reader = new TableReader($source);

        $csv = new CsvWriter();

        foreach ($reader as $key => $row) {
            if ($key === 0) {
                $csv->setColumnsSimple(array_keys($row));
            }

            $csv->addDaten($row);
        }

        //nerv... durch den import service hab ich eigentlich kein dateisystem!
        $dst_path = \config::get('system')->import_dir . '/uni/tracking2/csv/';

        file_put_contents($dst_path . $date . '.csv', $csv->getAsString());
    }

    public function processExcel(string $content): void
    {
        $result = $this->extractTrackingData($content);

        foreach ($result as $row) {
            $this->saveTracking($row['delivery_slip_number'], $row['date'], $row['carrier_name'], $row['tracking_id']);
        }
    }

    public function saveTracking(string $delivery_slip_number, DateObj $date, string $carrier_name, string $tracking_id): void
    {
        $state = $this->getDeliverySlipState($delivery_slip_number);

        if ($state['has_tracking']) {
            return;
        }

        $this->db->query("
            UPDATE
                ext_uni_tracking_queue
            SET
                ext_uni_tracking_queue.carrier_name = '" . $this->db->escape($carrier_name) . "',
                ext_uni_tracking_queue.tracking_id = '" . $this->db->escape($tracking_id) . "',
                ext_uni_tracking_queue.tracking_date = '" . $date->db('date') . "'
            WHERE
                ext_uni_tracking_queue.delivery_slip_number = '" . $this->db->escape($delivery_slip_number) . "'
        ");

        if ($state['has_supplier_order_id']) {
            $this->syncToCustomerOrder($delivery_slip_number);
        }
    }

    public function extractTrackingData(string $content): array
    {
        $soruce = new TableReaderSpreadsheetSource();
        $soruce->loadByString($content, $soruce::FORMAT_XLSX);

        $reader = new TableReader($soruce);

        $reader->setInputFormatDynamic([
            'lieferung' => [
                'typ' => 'function',
                'function' => function ($value) {
                    return ltrim($value, 0);
                },
                'checkHeader' => 'Lieferung'
            ],
            'datum' => ['typ' => 'date', 'format' => 'm/d/Y', 'checkHeader' => 'Mat.Bereitst.Datum'],
            'versandstelle' => ['typ' => 'string', 'checkHeader' => 'Versandstelle/Annahmestelle'],
            'bezeichnung' => ['typ' => 'string', 'checkHeader' => 'Bezeichnung']
        ]);

        $result = [];

        foreach ($reader as $row) {
            if (!str_contains($row['bezeichnung'], 'Sperrgut')) {
                continue;
            }

            if (isset($result[$row['lieferung']])) {
                continue;
            }

            $tracking = [
                'delivery_slip_number' => $row['lieferung'],
                'date' => $row['datum'],
                'carrier_name' => null,
                'tracking_id' => null
            ];

            switch ($row['versandstelle']) {
                case '2202':
                    $tracking['carrier_name'] = 'Emons';
                    //die 928403 ist die Kundennummer von Uni bei Emons. Eventuell ist das über das Lager eine andere.
                    $tracking['tracking_id'] = '928403' . $row['lieferung'];
                    break;
                case '2402':
                    $tracking['carrier_name'] = 'Dachser';
                    $tracking['tracking_id'] = $row['lieferung'];
                    break;
                case '2504':
                    $tracking['carrier_name'] = 'Emons';
                    $tracking['tracking_id'] = '928403' . $row['lieferung'];
                    break;
                case '2507': //@todo auf gut glück (zack hat nicht geantwortet und das blockt ansonsten den import)
                    $tracking['carrier_name'] = 'Emons';
                    $tracking['tracking_id'] = '928403' . $row['lieferung'];
                    break;
                default:
                    debug::dump($row);
                    throw new FatalException('unknown versandstelle');
            }

            $result[$row['lieferung']] = $tracking;
        }

        return $result;
    }
}
