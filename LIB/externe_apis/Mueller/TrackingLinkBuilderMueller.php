<?php

namespace bqp\extern\Mueller;

use bqp\db\db_generic;
use wws\Lager\WarenausgangLieferscheinRepository;

class TrackingLinkBuilderMueller
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function buildLinkByLieferscheinId(int $lieferschein_id, string $tracking_id): string
    {
        $plz = WarenausgangLieferscheinRepository::getRecipientZipcodeByLieferscheinId($lieferschein_id);

        return $this->buildLink($tracking_id, $plz);
    }

    public function buildLink(string $tracking_id, string $plz): string
    {
        return 'https://www.go4.de/g?olist&cid=' . $tracking_id . '&pc=' . $plz . '&nspace=rm';
    }
}
