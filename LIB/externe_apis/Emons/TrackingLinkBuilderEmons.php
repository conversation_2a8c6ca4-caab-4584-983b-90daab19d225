<?php

namespace bqp\extern\Emons;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use wws\Lager\WarenausgangLieferscheinRepository;
use wws\Order\Order;

class TrackingLinkBuilderEmons
{

    private db_generic $db;

    public function __construct(db_generic $db)
    {

        $this->db = $db;
    }


    public function buildLinkByLieferscheinId(int $lieferschein_id, string $tracking_id): string
    {
        $plz = WarenausgangLieferscheinRepository::getRecipientZipcodeByLieferscheinId($lieferschein_id);

        return $this->buildLink($tracking_id, $plz);
    }

    public function buildLink(string $tracking_id, string $plz): string
    {
        $tracking_id = str_replace('-', '', $tracking_id); //gäfgen hat jetzt - eingefügt... man

        if (\bqp\Utils\StringUtils::begins($tracking_id, '706669')) {
            $kundennummer = substr($tracking_id, 0, 6);
            $auftragsnummer = substr($tracking_id, 6);

            return 'https://tracking.emons.de/inform/WRGenServlet?WrORDER=DRILLDOWNJOB&WrJOB=TrackAndTrace.Sendungsverfolgung&editKundennummer=' . $kundennummer . '&editVers_Auftragsnummer_1=' . $auftragsnummer . '&editempf_plz=' . $plz;
        }
        if (\bqp\Utils\StringUtils::begins($tracking_id, '928403')) {
            $kundennummer = substr($tracking_id, 0, 6);
            $projektnummer = substr($tracking_id, 6);

            return 'https://tracking.emons.de/inform/WRGenServlet?WrORDER=DRILLDOWNJOB&WrJOB=TrackAndTrace.Sendungsverfolgung&editKundennummer=' . $kundennummer . '&editProjektnummer=' . $projektnummer . '&editempf_plz=' . $plz;
        }

        if (\bqp\Utils\StringUtils::begins($tracking_id, '006406082810') || \bqp\Utils\StringUtils::begins($tracking_id, '0084060828') || \bqp\Utils\StringUtils::begins($tracking_id, '003403633')) {
            return 'https://tracking.emons.de/inform/WRGenServlet?WrORDER=DRILLDOWNJOB&WrJOB=TrackAndTrace.Sendungsverfolgung&NVE=' . $tracking_id . '&editempf_plz=' . $plz;
        }

        \debug::dump('Für $tracking_id ' . $tracking_id . ' kann kein Emons Tracking Link erzeugt werden. Die Link-Erzeugung muss per Hand angepasst werden.');

        return '';
    }

    public function updateExistingTrackingUrls(): void
    {
        $date_end = new DateObj();
        $date_begin = $date_end->clone()->subSimple('days', '356');

        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.lager_id,
                warenausgang_lieferschein.auftnr,
                warenausgang_lieferschein_tracking_ids.lieferschein_id,
                warenausgang_lieferschein_tracking_ids.date_added,
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                warenausgang_lieferschein_tracking_ids.tracking_id,
                warenausgang_lieferschein_tracking_ids.datum,
                warenausgang_lieferschein_tracking_ids.tracking_url,
                order_addresses.plz
            FROM
                warenausgang_lieferschein_tracking_ids INNER JOIN
                tracking_carrier ON (warenausgang_lieferschein_tracking_ids.tracking_carrier_id = tracking_carrier.tracking_carrier_id) INNER JOIN
                warenausgang_lieferschein ON (warenausgang_lieferschein_tracking_ids.lieferschein_id = warenausgang_lieferschein.lieferschein_id) INNER JOIN
                order_addresses ON (warenausgang_lieferschein.order_id = order_addresses.order_id AND order_addresses.address_type IN ('" . Order::ADDRESS_TYPE_LIEFER . "', '" . Order::ADDRESS_TYPE_LIEFERRECHUNG . "'))
            WHERE
                warenausgang_lieferschein_tracking_ids.date_added BETWEEN '" . $date_begin->db('begin') . "' AND '" . $date_end->db('end') . "' AND
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id IN ('" . \wws\Tracking\TrackingCarrier\TrackingCarrierConst::CARRIER_ID_EMONS . "')
        ")->addCallback(function (array $row) {
            $row['url'] = $this->buildLink($row['tracking_id'], $row['plz']);
            return $row;
        })->display();

        foreach ($result as $row) {
            if (!$row['url']) {
                continue;
            }

            $this->db->query("
                UPDATE
                    warenausgang_lieferschein_tracking_ids
                SET
                    warenausgang_lieferschein_tracking_ids.tracking_url = '" . $row['url'] . "'
                WHERE
                    warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . $row['lieferschein_id'] . "' AND
                    warenausgang_lieferschein_tracking_ids.tracking_id = '" . $row['tracking_id'] . "'
            ");
        }
    }
}
