<?php

namespace bqp\extern\Aswo;

use bqp\Exceptions\FatalException;
use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use K11\AswoEedClient\ClientService as AswoEedClient;
use K11\AswoEedClient\Entities\Order\Address as AswoAddress;
use K11\AswoEedClient\Entities\Order\Order as AswoOrder;
use K11\AswoEedClient\Exceptions\AswoEedClientException;
use K11\AswoEedClient\Interfaces\Order\ProvidesOrderSubmittedResponse;
use system_protokoll;
use wws\Order\OrderConst;
use wws\Supplier\OrderTransfer\SupplierOrderApi;
use wws\Supplier\OrderTransfer\SupplierOrderApiResult;
use wws\Supplier\OrderTransfer\SupplierOrderApiUnrecoverableException;
use wws\Supplier\SupplierOrder;

class AswoOrderApi implements SupplierOrderApi
{
    private AswoEedClient $aswo_eed_client;

    private system_protokoll $logger;

    private bool $api_error_mitigation_db_not_available = false;
    private string $system_mail_address;

    public function __construct(
        AswoEedClient $client,
        system_protokoll $logging,
        string $system_mail_address
    ) {
        $this->aswo_eed_client = $client;
        $this->logger = $logging;
        $this->system_mail_address = $system_mail_address;
    }

    public function sendSupplierOrder(SupplierOrder $supplier_order): SupplierOrderApiResult
    {
        if ($this->api_error_mitigation_db_not_available) {
            throw new \RuntimeException('MITIGATION - vorheriger ASWO Fehler (********): Freigabe ist nicht möglich, Datenbank nicht erreichbar.');
        }

        $result = new SupplierOrderApiResult();

        try {
            $aswo_order_items = $this->convertSupplierOrderItems($supplier_order);

            $aswo_order = $this->convertSupplierOrder($supplier_order);
            $this->aswo_eed_client->startNewSession();

            foreach ($aswo_order_items as $aswo_order_item) {
                try {
                    $this->aswo_eed_client
                        ->getBasketGateway()
                        ->modifyBasket(
                            $aswo_order_item['aswo_product_id'],
                            number_format($aswo_order_item['vk_brutto'], 2, '.', ''),
                            $aswo_order_item['quantity']
                        );
                } catch (AswoEedClientException $exception) {
                    throw new SupplierOrderApiUnrecoverableException($exception->getMessage() . ' (ASWO Artikelnummer ' . $aswo_order_item['aswo_product_id'] . ')', 0, $exception);
                }
            }

            $response = $this->submitAswoOrder($aswo_order, $supplier_order->getSupplierOrderId());

            $supplier_order->setExterneReferenz1($response->getOrderid());
            $supplier_order->save();

            $result->addMessageLine('ASWO Bestellnummer: ' . $response->getOrderid());
        } catch (AswoEedClientException | GuzzleException | JsonException $exception) {
            $this->logger->error(
                'order transfer exception for supplier_order_id: ' .
                $supplier_order->getSupplierOrderId() .
                ' message:' .
                $exception->getMessage()
            );

            if ($exception instanceof AswoEedClientException) {
                throw new SupplierOrderApiUnrecoverableException($exception->getMessage(), 0, $exception);
            }

            throw $exception;
        }

        //@todo das ist jetzt erstmal auseinander gezogen um zu schauen wie sich das verhält...
        try {
            $this->aswo_eed_client->getOrderGateway()->setOrderAsPayed($response->getOrderid());
        } catch (\Throwable $exception) {
            $this->logger->error(
                'order transfer exception setOrderAsPayed() for supplier_order_id: ' .
                $supplier_order->getSupplierOrderId() .
                ' message:' .
                $exception->getMessage()
            );

            \debug::dumpException($exception);
        }

        try {
            $this->aswo_eed_client->getOrderGateway()->releaseOrder($response->getOrderid());
        } catch (\Throwable $exception) {
            $this->logger->error(
                'order transfer exception releaseOrder() for supplier_order_id: ' .
                $supplier_order->getSupplierOrderId() .
                ' message:' . $exception::class . ' ' . $exception->getMessage()
            );

            //das macht zu viel "krach" bei der Anzahl Bestellung... das ist eine Kombination von technischen und geschäftlichen problemen.
            //kommt gebündelt am nächsten Tag über die order transfer alterts
            //\debug::dumpException($exception);
            //\debug::dump($supplier_order);

            //wenn wir viele bestellungen auf einmal übermitteln (speziell montags), kommt es bei aswo zu fehlern
            //"ASWO Fehler (********): Freigabe ist nicht möglich, Datenbank nicht erreichbar.". Auch ein sleep(30) bringt hier keine abhilfe.
            //->als mitigation: wenn dieer Fehler eintritt, dann setzen wir eine flag und bei allen weiteren Bestellungen werfen wir ein Fehler.
            //(Die aktuelle Bestellung wurde übermittelt, muss aber händisch freigegeben werden. Das machen die Kollegen aus dem Service.
            //Ab und zu bleiben so und so Aufträge hängen.)
            if (str_contains($exception->getMessage(), 'Datenbank nicht erreichbar')) {
                $this->api_error_mitigation_db_not_available = true;
            }
        }
        //

        return $result;
    }

    public function getOrderApiNote(SupplierOrder $supplier_order): string
    {
        return '';
    }

    public function isSupplierOrderApiAble(SupplierOrder $supplier_order): bool
    {
        foreach ($supplier_order->getItems() as $item) {
            if (!$item->getGrosProductId()) {
                return false;
            }
        }

        return true;
    }

    public function getTransferNote(): string
    {
        return 'Erfolgreich: Der Auftrag wurde von der API des EURAS-Endkundenshop angenommen.';
    }

    private function submitAswoOrder(\K11\AswoEedClient\Entities\Order\Order $aswo_order, int $supplier_order_id): ProvidesOrderSubmittedResponse
    {
        $response = $this->aswo_eed_client
            ->getOrderGateway()
            ->submitOrder($aswo_order);

        if ($response->getErrorNumber()) {
            throw new AswoEedClientException(sprintf(
                'ASWO error number: %s ASWO error message: %s',
                $response->getErrorNumber(),
                $response->getErrorMessage()
            ));
        }

        if (!$response->getOrderid()) {
            throw new AswoEedClientException('ASWO orderid is null');
        }

        $this->logger->info('Bestellung wurde an ASWO übermittelt.', [
            'supplier_order_id' => $supplier_order_id,
            'aswoorderid' => $response->getOrderid()
        ]);

        return $response;
    }


    public function convertSupplierOrder(SupplierOrder $supplier_order): AswoOrder
    {
        $delivery_address = $supplier_order->getDeliveryAddress();

        $address_fitter = new AswoAddressFitting($delivery_address);

        $aswo_delivery_address = new AswoAddress();
        $aswo_delivery_address->setName($address_fitter->get('name1'));
        $aswo_delivery_address->setSuffix($address_fitter->get('name2'));
        $aswo_delivery_address->setCity($address_fitter->get('ort'));
        $aswo_delivery_address->setCountryCode($address_fitter->get('land'));
        $aswo_delivery_address->setStreet($address_fitter->get('strasse'));
        $aswo_delivery_address->setHousenumber($address_fitter->get('hausnummer'));
        $aswo_delivery_address->setZip($address_fitter->get('plz'));

        $aswo_order = new AswoOrder();

        $aswo_order->setEmailAddress($this->getSystemMailAddress());
        $aswo_order->setExternalCustomerNumber($supplier_order->getBestellNr());
        //$aswo_order->setExternalInvoiceNumber();

        $aswo_order->setInvoiceAddress($aswo_delivery_address);
        $aswo_order->setShippingAddress($aswo_delivery_address);
        $aswo_order->setLanguageCode('de');
        $aswo_order->setPaperlessShippingNotice(true);
        $aswo_order->setPaymentMethod(3);
        $aswo_order->setShippingCost('0.00');

        $aswo_order->setNotice($delivery_address->getEmail());
        $aswo_order->setTelephone($delivery_address->getTel1());

        //bei bestellungen an unser lager den lieferschein als papier beilegen
        if (!$supplier_order->isDirektLieferung()) {
            $aswo_order->setPaperlessShippingNotice(false);
        }

        return $aswo_order;
    }

    private function getSystemMailAddress(): string
    {
        return $this->system_mail_address;
    }

    public function convertSupplierOrderItems(SupplierOrder $supplier_order): array
    {
        $aswo_order_items = [];

        $customer_order_price = [];

        if ($supplier_order->getOrderId()) {
            //wir übermitteln an aswo den endkundenpreis -> das übernehm ich jetzt einfachmal blind.
            //Aber ASWO muss eigentlich nicht unsere VKs kennen und nebenbei stimmen auch die Versandkosten nicht.
            $customer_order_price = \db::getInstance()->query("
                SELECT
                    IF (order_item.typ = '" . OrderConst::WARENKORB_TYP_ASWO . "', order_item.typ_value, product_ek.gros_product_id) AS gros_product_id,
                    order_item.preis
                FROM
                    order_item LEFT JOIN
                    product_ek ON (order_item.product_id = product_ek.product_id AND product_ek.supplier_id = " . $supplier_order->getSupplierId() . ")
                WHERE
                    order_item.order_id = " . $supplier_order->getOrderId() . "
                HAVING
                    gros_product_id != ''
            ")->asSingleArray('gros_product_id');
        }

        foreach ($supplier_order->getItems() as $item) {
            if (!$item->getGrosProductId()) {
                throw new FatalException('missing aswo product id');
            }

            if (isset($aswo_order_items[$item->getGrosProductId()])) {
                //die ASWO API erlaubt pro Bestellung keine Positionen mehrfach -> deduplizieren
                $aswo_order_item = $aswo_order_items[$item->getGrosProductId()];
                $aswo_order_item['quantity'] += $item->getQuantity();
            } else {
                $aswo_order_item = [
                    'aswo_product_id' => $item->getGrosProductId(),
                    'quantity' => $item->getQuantity(),
                    'vk_brutto' => round($item->getEkNetto() * 2) //wir setzen als dummy den doppelte ek an.
                ];
            }

            if (isset($customer_order_price[$item->getGrosProductId()])) {
                $aswo_order_item['vk_brutto'] = $customer_order_price[$item->getGrosProductId()];
            }

            $aswo_order_items[$item->getGrosProductId()] = $aswo_order_item;
        }

        return $aswo_order_items;
    }
}
