<?php

namespace bqp\extern\ComputerUniverse;

use bqp\Csv\TableReader;
use bqp\Csv\TableReaderCsvSource;
use bqp\Exceptions\FatalException;
use bqp\Http\HttpUtils;
use bqp\KeyValueStore\KeyValueStore;
use bqp\Utils\FileUtils;
use RuntimeException;
use wws\core\logger\wws_logger_interface;
use wws\MarketView\Import\MarketViewImport;
use wws\MarketView\Import\MarketViewImportProduct;
use wws\MarketView\Import\MarketViewImportUtils;
use wws\MarketView\MarketViewConst;
use wws\MarketView\Utils\MarketViewCsvOffsizeErrorHandler;

class MarketViewImportComputerUniverse
{
    private MarketViewImport $importer;
    private string $import_dir;
    private KeyValueStore $kv_store;
    private wws_logger_interface $log;


    public function __construct(string $import_dir, MarketViewImport $importer, wws_logger_interface $log, KeyValueStore $kv_store)
    {
        $this->import_dir = $import_dir;
        $this->kv_store = $kv_store;
        $this->importer = $importer;
        $this->log = $log;
    }

    public function update(): void
    {
        $pricelist = $this->fetchPricelist();

        if (!$pricelist) {
            return;
        }

        $this->importPricelist($pricelist);
        $this->archivePricelist($pricelist);
    }

    public function fetchPricelist(): ?string
    {
        $url = 'https://get.cpexp.de/WLLGAiPcjopd5Japlm_JMAyr5ApY8_tDVK1sF0Zv0QyuNngSaZCTHybcVsq_ICBb/cyberport-feedsmitmengen_synaxonvertriebcude.csv';

        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'User-Agent: ' . HttpUtils::getRandomUserAgent() . "\r\n",
            ],
        ]);

        $content = file_get_contents($url, false, $context);

        if ($content === false) {
            throw new RuntimeException('Fehler beim Laden der Preisliste von ComputerUniverse.');
        }

        $hash = md5($content);

        //prüfen ob hash bereits eingelesen wurden...
        $last_hash = $this->kv_store->get('computeruniverse_last_pricelist');

        if ($hash === $last_hash) {
            $this->log->warning('Preisliste bereits eingelesen.', ['hash' => $hash]);
            return null;
        }

        $this->kv_store->put('computeruniverse_last_pricelist', $hash, 3600 * 24 * 30);

//        $zip_file_temp = $this->import_dir.'pricelist.zip';
//
//        file_put_contents($zip_file_temp, $content);
//        ZipUtils::extractFirstFile($zip_file_temp, $this->import_dir.'pricelist.csv');
//        unlink($zip_file_temp);

        $file = $this->import_dir . 'pricelist_' . date('Ymd-His') . '.csv';
        //$file = $this->import_dir . 'pricelist.csv';
        file_put_contents($file, $content);
        return $file;
    }

    public function importPricelist(string $file): void
    {
        $src = new TableReaderCsvSource($file, 'UTF-8');

        $csv = new TableReader($src);

        $csv->setInputFormatStatic([
            'mvsrc_product_id' => ['type' => 'string', 'checkHeader' => "SKU"],
            'product_name' => ['type' => 'string', 'checkHeader' => "name/Artikelname"],
            'beschreibung' => ['type' => 'string', 'checkHeader' => "description/Beschreibung"],
            'vk_netto' => ['type' => 'float', 'checkHeader' => "price/Preis"],
            'hersteller_name' => ['type' => 'string', 'checkHeader' => "manufacturer/Hersteller"],
            'kategorie' => ['type' => 'string', 'checkHeader' => "category/Produktgruppe"],
            'ean' => ['type' => 'string', 'checkHeader' => "EAN"],
            'mpn' => ['type' => 'string', 'checkHeader' => "man. number/HBNR"],
            '_eecenergieeffizienzklasse' => ['type' => 'string', 'checkHeader' => "eec/Energieeffizienzklasse"],
            '_refurbished' => ['type' => 'string', 'checkHeader' => "refurbished"],
            'spedition' => ['type' => 'string', 'checkHeader' => "palett/Spedition"],
            'inventory' => ['type' => 'string', 'checkHeader' => "stock/Menge"],
            'uvp' => ['type' => 'float', 'checkHeader' => "UVP"],
        ]);

        $csv->setFilter(function (array $row) {
            if ($row['_refurbished'] === 'REF') {
                return false;
            }

            return true;
        });

        $csv->setSkipOffSizeLineHandler(new MarketViewCsvOffsizeErrorHandler(0));

        $this->importer->fullUpdatePrepare();

        $errors = 0;

        foreach ($csv as $row) {
            //es sind in letzter Zeit immer wieder Dateien aufgetaucht, wo es Zeilen ohne Bestand gibt...
            //wenn sich das in grenzen hält -> nehmen wir das als 0 an und gut ist...
            if (!is_numeric($row['inventory'])) {
                $errors++;

                if ($errors > 10) {
                    throw new FatalException('mehr als 10 Einträge mit ungültigem Lagerbestand gefunden.');
                }

                $row['inventory'] = 0;
            }
            //

            $product = new MarketViewImportProduct();
            $product->setMvsrcProductId($row['mvsrc_product_id']);
            $product->setProductName($row['product_name']);
            $product->setEan($row['ean']);
            $product->setMpn($row['mpn']);
            $product->setVkNetto($row['vk_netto']);
            $product->setUvp($row['uvp']);
            $product->setMvCatId($this->importer->saveCategory(MarketViewConst::TREE_ID_DEFAULT, $row['kategorie']));
            $product->setHerstellerName($row['hersteller_name']);
            $product->setBeschreibung($row['beschreibung']);
            $product->setInventory($row['inventory']);
            $product->setAvailabilityId($this->importer->saveInventory($row['inventory']));
            $product->setMvAvailability($row['inventory']);

            $product->setVersandNetto($this->getFulfillmentPrice($row['spedition']));

            $this->importer->saveProduct($product);
        }

        $this->importer->fullUpdateEnd();
        $this->importer->updateComplete();

        $summary = $this->importer->getListLogger()->getSummaryAsText();

        $this->log->wok('Preisliste erfolgreich eingelesen', $summary);
    }

    private function getFulfillmentPrice(string $spedition): float
    {
        return match ($spedition) {
            'no' => 3.35,
            'yes' => 37.82
        };
    }

    public function archivePricelist(string $file): void
    {
        $dst_file = FileUtils::appendDir($file, 'LAST');
        $dst_file = FileUtils::appendBeforExtenstion($dst_file, date('Ymd-His'));

        MarketViewImportUtils::moveFileCompressed($file, $dst_file);
    }
}
