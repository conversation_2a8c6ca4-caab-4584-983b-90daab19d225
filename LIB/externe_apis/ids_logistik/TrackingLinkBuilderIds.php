<?php

namespace bqp\extern\ids_logistik;

use bqp\Date\DateObj;
use bqp\db\db_generic;
use wws\Lager\WarenausgangLieferscheinRepository;
use wws\Order\Order;

class TrackingLinkBuilderIds
{
    private db_generic $db;

    public function __construct(db_generic $db)
    {
        $this->db = $db;
    }

    public function buildLinkByLieferscheinId(int $lieferschein_id, string $tracking_id): string
    {
        $plz = WarenausgangLieferscheinRepository::getRecipientZipcodeByLieferscheinId($lieferschein_id);

        return $this->buildLink($tracking_id, $plz);
    }

    public function buildLink(string $tracking_id, string $plz): string
    {
        if (
            \bqp\Utils\StringUtils::begins($tracking_id, '1006910') ||
            \bqp\Utils\StringUtils::begins($tracking_id, '52973000')
        ) {
            return 'https://www.ids-logistik.de/e-services/sendungsverfolgung/sendungssuche?id=' . $tracking_id . '&zip=' . $plz;
        }

        return '';
    }

    public function updateExistingTrackingUrls(): void
    {
        $date_end = new DateObj();
        $date_begin = $date_end->clone()->subSimple('days', '356');

        $result = $this->db->query("
            SELECT
                warenausgang_lieferschein.lager_id,
                warenausgang_lieferschein.auftnr,
                warenausgang_lieferschein_tracking_ids.lieferschein_id,
                warenausgang_lieferschein_tracking_ids.date_added,
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id,
                tracking_carrier.tracking_carrier_name,
                warenausgang_lieferschein_tracking_ids.tracking_id,
                warenausgang_lieferschein_tracking_ids.datum,
                warenausgang_lieferschein_tracking_ids.tracking_url,
                order_addresses.plz
            FROM
                warenausgang_lieferschein_tracking_ids INNER JOIN
                tracking_carrier ON (warenausgang_lieferschein_tracking_ids.tracking_carrier_id = tracking_carrier.tracking_carrier_id) INNER JOIN
                warenausgang_lieferschein ON (warenausgang_lieferschein_tracking_ids.lieferschein_id = warenausgang_lieferschein.lieferschein_id) INNER JOIN
                order_addresses ON (warenausgang_lieferschein.order_id = order_addresses.order_id AND order_addresses.address_type IN ('" . Order::ADDRESS_TYPE_LIEFER . "', '" . Order::ADDRESS_TYPE_LIEFERRECHUNG . "'))
            WHERE
                warenausgang_lieferschein_tracking_ids.date_added BETWEEN '" . $date_begin->db('begin') . "' AND '" . $date_end->db('end') . "' AND
                warenausgang_lieferschein_tracking_ids.tracking_carrier_id IN ('" . \wws\Tracking\TrackingCarrier\TrackingCarrierConst::CARRIER_ID_RIECK . "')
        ")->addCallback(function (array $row) {
            $row['url'] = $this->buildLink($row['tracking_id'], $row['plz']);
            return $row;
        })->display();

        foreach ($result as $row) {
            if (!$row['url']) {
                continue;
            }

            $this->db->query("
                UPDATE
                    warenausgang_lieferschein_tracking_ids
                SET
                    warenausgang_lieferschein_tracking_ids.tracking_url = '" . $row['url'] . "'
                WHERE
                    warenausgang_lieferschein_tracking_ids.lieferschein_id = '" . $row['lieferschein_id'] . "' AND
                    warenausgang_lieferschein_tracking_ids.tracking_id = '" . $row['tracking_id'] . "'
            ");
        }
    }
}
