<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.SplitBar.BasicLayoutAdapter-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.SplitBar.BasicLayoutAdapter-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.SplitBar.BasicLayoutAdapter-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.SplitBar.BasicLayoutAdapter"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/SplitBar.html#cls-Ext.SplitBar.BasicLayoutAdapter">Ext.SplitBar.BasicLayoutAdapter</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">SplitBar.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/SplitBar.html#cls-Ext.SplitBar.BasicLayoutAdapter">SplitBar.BasicLayoutAdapter</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.SplitBar.AbsoluteLayoutAdapter.html" ext:cls="Ext.SplitBar.AbsoluteLayoutAdapter">SplitBar.AbsoluteLayoutAdapter</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Default Adapter. It assumes the splitter and resizing element are not positioned
elements and only gets/sets the width of the element. Generally used for table based layouts.</div><div class="hr"></div><a id="Ext.SplitBar.BasicLayoutAdapter-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.SplitBar.BasicLayoutAdapter-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.BasicLayoutAdapter-getElementSize"></a><b><a href="source/SplitBar.html#method-Ext.SplitBar.BasicLayoutAdapter-getElementSize">getElementSize</a></b>(&nbsp;<code>Ext.SplitBar&nbsp;s</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Called before drag operations to get the current size of the resizing element.</div><div class="long">Called before drag operations to get the current size of the resizing element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Ext.SplitBar<div class="sub-desc">The SplitBar using this adapter</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">SplitBar.BasicLayoutAdapter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.BasicLayoutAdapter-setElementSize"></a><b><a href="source/SplitBar.html#method-Ext.SplitBar.BasicLayoutAdapter-setElementSize">setElementSize</a></b>(&nbsp;<code>Ext.SplitBar&nbsp;s</code>,&nbsp;<code>Number&nbsp;newSize</code>,&nbsp;<code>Function&nbsp;onComplete</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Called after drag operations to set the size of the resizing element.</div><div class="long">Called after drag operations to set the size of the resizing element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Ext.SplitBar<div class="sub-desc">The SplitBar using this adapter</div></li><li><code>newSize</code> : Number<div class="sub-desc">The new size to set</div></li><li><code>onComplete</code> : Function<div class="sub-desc">A function to be invoked when resizing is complete</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">SplitBar.BasicLayoutAdapter</td></tr></tbody></table><a id="Ext.SplitBar.BasicLayoutAdapter-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>