<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.tree.RootTreeNodeUI-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.tree.RootTreeNodeUI-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.tree.RootTreeNodeUI-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.tree.RootTreeNodeUI"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/TreeNodeUI.html#cls-Ext.tree.RootTreeNodeUI">Ext.tree.RootTreeNodeUI</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.tree</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TreeNodeUI.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TreeNodeUI.html#cls-Ext.tree.RootTreeNodeUI">RootTreeNodeUI</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">This class provides the default UI implementation for <b>root</b> Ext TreeNodes.
The RootTreeNode UI implementation allows customizing the appearance of the root tree node.<br>
<p>
If you are customizing the Tree's user interface, you
may need to extend this class, but you should never need to instantiate this class.<br></div><div class="hr"></div><a id="Ext.tree.RootTreeNodeUI-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.tree.RootTreeNodeUI-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.tree.RootTreeNodeUI-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>