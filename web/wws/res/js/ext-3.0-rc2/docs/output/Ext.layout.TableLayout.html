<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.TableLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.TableLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.TableLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.TableLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.TableLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.layout.ContainerLayout.html" ext:member="" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">TableLayout</pre></div><h1>Class <a href="source/TableLayout.html#cls-Ext.layout.TableLayout">Ext.layout.TableLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TableLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TableLayout.html#cls-Ext.layout.TableLayout">TableLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout" ext:member="">ContainerLayout</a></td></tr></table><div class="description"><p>This layout allows you to easily render content into an HTML table.  The total number of columns can be
specified, and rowspan and colspan can be used to create complex layouts within the table.
This class is intended to be extended or created via the layout:'table' <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">Ext.Container.layout</a> config,
and should generally not need to be created directly via the new keyword.</p>
<p>Note that when creating a layout via config, the layout-specific config properties must be passed in via
the <a href="output/Ext.Container.html#Ext.Container-layoutConfig" ext:member="layoutConfig" ext:cls="Ext.Container">Ext.Container.layoutConfig</a> object which will then be applied internally to the layout.  In the
case of TableLayout, the only valid layout config property is <a href="output/Ext.layout.TableLayout.html#Ext.layout.TableLayout-columns" ext:member="columns" ext:cls="Ext.layout.TableLayout">columns</a>.  However, the items added to a
TableLayout can supply the following table-specific config properties:</p>
<ul>
<li><b>rowspan</b> Applied to the table cell containing the item.</li>
<li><b>colspan</b> Applied to the table cell containing the item.</li>
<li><b>cellId</b> An id applied to the table cell containing the item.</li>
<li><b>cellCls</b> A CSS class name added to the table cell containing the item.</li>
</ul>
<p>The basic concept of building up a TableLayout is conceptually very similar to building up a standard
HTML table.  You simply add each panel (or "cell") that you want to include along with any span attributes
specified as the special config properties of rowspan and colspan which work exactly like their HTML counterparts.
Rather than explicitly creating and nesting rows and columns as you would in HTML, you simply specify the
total column count in the layoutConfig and start adding panels in their natural order from left to right,
top to bottom.  The layout will automatically figure out, based on the column count, rowspans and colspans,
how to position each panel within the table.  Just like with HTML tables, your rowspans and colspans must add
up correctly in your overall layout or you'll end up with missing and/or extra cells!  Example usage:</p>
<pre><code><i>// This code will generate a layout table that is 3 columns by 2 rows
</i>
<i>// <b>with</b> some spanning included.  The basic layout will be:
</i>
<i>// +--------+-----------------+
</i>
<i>// |   A    |   B             |
</i>
<i>// |        |--------+--------|
</i>
<i>// |        |   C    |   D    |
</i>
<i>// +--------+--------+--------+
</i>
<b>var</b> table = <b>new</b> Ext.Panel({
    title: <em>'Table Layout'</em>,
    layout:<em>'table'</em>,
    defaults: {
        <i>// applied to each contained panel
</i>
        bodyStyle:<em>'padding:20px'</em>
    },
    layoutConfig: {
        <i>// The total column count must be specified here
</i>
        columns: 3
    },
    items: [{
        html: <em>'&lt;p&gt;Cell A content&lt;/p&gt;'</em>,
        rowspan: 2
    },{
        html: <em>'&lt;p&gt;Cell B content&lt;/p&gt;'</em>,
        colspan: 2
    },{
        html: <em>'&lt;p&gt;Cell C content&lt;/p&gt;'</em>,
        cellCls: <em>'highlight'</em>
    },{
        html: <em>'&lt;p&gt;Cell D content&lt;/p&gt;'</em>
    }]
});</code></pre></div><div class="hr"></div><a id="Ext.layout.TableLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.TableLayout-columns"></a><b><a href="source/TableLayout.html#cfg-Ext.layout.TableLayout-columns">columns</a></b> : Number<div class="mdesc"><div class="short">The total number of columns to create in the table for this layout.  If not specified, all Components added to&#13;
this ...</div><div class="long">The total number of columns to create in the table for this layout.  If not specified, all Components added to
this layout will be rendered into a single row using one column per Component.</div></div></td><td class="msource">TableLayout</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#extraCls" ext:member="#extraCls" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-renderHidden"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-renderHidden">renderHidden</a></b> : Boolean<div class="mdesc">True to hide each contained item on render (defaults to false).</div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#renderHidden" ext:member="#renderHidden" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.TableLayout-tableAttrs"></a><b><a href="source/TableLayout.html#cfg-Ext.layout.TableLayout-tableAttrs">tableAttrs</a></b> : Object<div class="mdesc"><div class="short">An object containing properties which are added to the DomHelper specification&#13;
used to create the layout's &amp;lt;table...</div><div class="long"><p>An object containing properties which are added to the <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> specification
used to create the layout's <tt>&lt;table&gt;</tt> element. Example:</p><pre><code>{
    xtype: <em>'panel'</em>,
    layout: <em>'table'</em>,
    layoutConfig: {
        tableAttrs: {
        	style: {
        		width: <em>'100%'</em>
        	}
        },
        columns: 3
    }
}</code></pre></div></div></td><td class="msource">TableLayout</td></tr></tbody></table><a id="Ext.layout.TableLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#fieldTpl" ext:member="#fieldTpl" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.TableLayout-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.layout.TableLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>