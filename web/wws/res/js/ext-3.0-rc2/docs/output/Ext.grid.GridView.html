<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.GridView-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.GridView-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.GridView-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.grid.GridView-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.GridView"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">GridView</pre></div><h1>Class <a href="source/GridView.html#cls-Ext.grid.GridView">Ext.grid.GridView</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">GridView.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/GridView.html#cls-Ext.grid.GridView">GridView</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.grid.GroupingView.html" ext:cls="Ext.grid.GroupingView">GroupingView</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable" ext:member="">Observable</a></td></tr></table><div class="description"><p>This class encapsulates the user interface of an <a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel</a>.
Methods of this class may be used to access user interface elements to enable
special display effects. Do not change the DOM structure of the user interface.</p>
<p>This class does not provide ways to manipulate the underlying data. The data
model of a Grid is held in an <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>.</p></div><div class="hr"></div><a id="Ext.grid.GridView-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-autoFill"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-autoFill">autoFill</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to false.  Specify true to have the column widths re-proportioned
when the grid is initially rendered.  The ...</div><div class="long">Defaults to <tt>false</tt>.  Specify <tt>true</tt> to have the column widths re-proportioned
when the grid is <b>initially rendered</b>.  The 
<a href="output/Ext.grid.Column.html#Ext.grid.Column-width" ext:member="width" ext:cls="Ext.grid.Column">initially configured width</a></tt> of each column will be adjusted
to fit the grid width and prevent horizontal scrolling. If columns are later resized (manually
or programmatically), the other columns in the grid will <b>not</b> be resized to fit the grid width.
See <tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-forceFit" ext:member="forceFit" ext:cls="Ext.grid.GridView">forceFit</a></tt> also.</div></div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-cellSelector"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-cellSelector">cellSelector</a></b> : String<div class="mdesc">The selector used to find cells internally (defaults to <tt>'td.x-grid3-cell'</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-cellSelectorDepth"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-cellSelectorDepth">cellSelectorDepth</a></b> : Number<div class="mdesc">The number of levels to search for cells in event delegation (defaults to <tt>4</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-columnsText"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-columnsText">columnsText</a></b> : String<div class="mdesc">The text displayed in the "Columns" menu item (defaults to <tt>"Columns"</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-deferEmptyText"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-deferEmptyText">deferEmptyText</a></b> : Boolean<div class="mdesc">True to defer <tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-emptyText" ext:member="emptyText" ext:cls="Ext.grid.GridView">emptyText</a></tt> being applied until the store's
first load (defaults to <tt>true</tt>).</div></td><td class="msource">GridView</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-emptyText"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-emptyText">emptyText</a></b> : String<div class="mdesc"><div class="short">Default text (html tags are accepted) to display in the grid body when no rows
are available (defaults to ''). This v...</div><div class="long">Default text (html tags are accepted) to display in the grid body when no rows
are available (defaults to ''). This value will be used to update the <tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-mainBody" ext:member="mainBody" ext:cls="Ext.grid.GridView">mainBody</a></tt>:
    <pre><code>this.mainBody.update(<em>'&lt;div class=<em>"x-grid-empty"</em>>'</em> + this.emptyText + <em>'&lt;/div>'</em>);</code></pre></div></div></td><td class="msource">GridView</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-enableRowBody"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-enableRowBody">enableRowBody</a></b> : Boolean<div class="mdesc"><div class="short">True to add a second TR element per row that can be used to provide a row body
that spans beneath the data row.  Use ...</div><div class="long">True to add a second TR element per row that can be used to provide a row body
that spans beneath the data row.  Use the <a href="output/Ext.grid.GridView.html#Ext.grid.GridView-getRowClass" ext:member="getRowClass" ext:cls="Ext.grid.GridView">getRowClass</a> method's rowParams config to customize the row body.</div></div></td><td class="msource">GridView</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-forceFit"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-forceFit">forceFit</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to false.  Specify true to have the column widths re-proportioned
at all times.  The initially configured wi...</div><div class="long">Defaults to <tt>false</tt>.  Specify <tt>true</tt> to have the column widths re-proportioned
at <b>all times</b>.  The <a href="output/Ext.grid.Column.html#Ext.grid.Column-width" ext:member="width" ext:cls="Ext.grid.Column">initially configured width</a></tt> of each
column will be adjusted to fit the grid width and prevent horizontal scrolling. If columns are
later resized (manually or programmatically), the other columns in the grid <b>will</b> be resized
to fit the grid width. See <tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-autoFill" ext:member="autoFill" ext:cls="Ext.grid.GridView">autoFill</a></tt> also.</div></div></td><td class="msource">GridView</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-headersDisabled"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-headersDisabled">headersDisabled</a></b> : Boolean<div class="mdesc"><div class="short">True to disable the grid column headers (defaults to false). 
Use the ColumnModel menuDisabled
config to disable the ...</div><div class="long">True to disable the grid column headers (defaults to <tt>false</tt>). 
Use the <a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">ColumnModel</a> <tt><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-menuDisabled" ext:member="menuDisabled" ext:cls="Ext.grid.ColumnModel">menuDisabled</a></tt>
config to disable the <i>menu</i> for individual columns.  While this config is true the
following will be disabled:<div class="mdetail-params"><ul>
<li>clicking on header to sort</li>
<li>the trigger to reveal the menu.</li>
</ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-rowSelector"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-rowSelector">rowSelector</a></b> : String<div class="mdesc">The selector used to find rows internally (defaults to <tt>'div.x-grid3-row'</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-rowSelectorDepth"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-rowSelectorDepth">rowSelectorDepth</a></b> : Number<div class="mdesc">The number of levels to search for rows in event delegation (defaults to <tt>10</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-scrollOffset"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-scrollOffset">scrollOffset</a></b> : Number<div class="mdesc">The amount of space to reserve for the vertical scrollbar
(defaults to <tt>19</tt> pixels).</div></td><td class="msource">GridView</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-selectedRowClass"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-selectedRowClass">selectedRowClass</a></b> : String<div class="mdesc"><div class="short">The CSS class applied to a selected row (defaults to "x-grid3-row-selected"). An
example overriding the default styli...</div><div class="long">The CSS class applied to a selected row (defaults to <tt>"x-grid3-row-selected"</tt>). An
example overriding the default styling:
    <pre><code>.x-grid3-row-selected {background-color: yellow;}</code></pre>
Note that this only controls the row, and will not do anything for the text inside it.  To style inner
facets (like text) use something like:
    <pre><code>.x-grid3-row-selected .x-grid3-cell-inner {
        color: #FFCC00;
    }</code></pre></div></div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-sortAscText"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-sortAscText">sortAscText</a></b> : String<div class="mdesc">The text displayed in the "Sort Ascending" menu item (defaults to <tt>"Sort Ascending"</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-sortClasses"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-sortClasses">sortClasses</a></b> : Array<div class="mdesc">The CSS classes applied to a header when it is sorted. (defaults to <tt>["sort-asc", "sort-desc"]</tt>)</div></td><td class="msource">GridView</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-sortDescText"></a><b><a href="source/GridView.html#cfg-Ext.grid.GridView-sortDescText">sortDescText</a></b> : String<div class="mdesc">The text displayed in the "Sort Descending" menu item (defaults to <tt>"Sort Descending"</tt>)</div></td><td class="msource">GridView</td></tr></tbody></table><a id="Ext.grid.GridView-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-dragZone"></a><b><a href="source/GridView.html#prop-Ext.grid.GridView-dragZone">dragZone</a></b> : Ext.grid.GridDragZone<div class="mdesc"><div class="short">A customized implementation of a DragZone which provides default implementations
of the template methods of DragZone ...</div><div class="long"><p>A customized implementation of a <a href="output/Ext.dd.DragZone.html" ext:cls="Ext.dd.DragZone">DragZone</a> which provides default implementations
of the template methods of DragZone to enable dragging of the selected rows of a GridPanel.
See <a href="output/Ext.grid.GridDragZone.html" ext:cls="Ext.grid.GridDragZone">Ext.grid.GridDragZone</a> for details.</p>
<p>This will <b>only</b> be present:<div class="mdetail-params"><ul>
<li><i>if</i> the owning GridPanel was configured with <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-enableDragDrop" ext:member="enableDragDrop" ext:cls="Ext.grid.GridPanel">enableDragDrop</a>: <tt>true</tt>.</li>
<li><i>after</i> the owning GridPanel has been rendered.</li>
</ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-mainBody"></a><b><a href="source/GridView.html#prop-Ext.grid.GridView-mainBody">mainBody</a></b> : Ext.Element<div class="mdesc"><div class="short">Read-only. The GridView's body Element which encapsulates all rows in the Grid.
This Element is only available after ...</div><div class="long"><i>Read-only</i>. The GridView's body Element which encapsulates all rows in the Grid.
This <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> is only available after the GridPanel has been rendered.</div></div></td><td class="msource">GridView</td></tr></tbody></table><a id="Ext.grid.GridView-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-GridView"></a><b><a href="source/GridView.html#cls-Ext.grid.GridView">GridView</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-findRow"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-findRow">findRow</a></b>(&nbsp;<code>Element&nbsp;el</code>&nbsp;)
    :
                                        The<div class="mdesc"><div class="short">Return the HtmlElement representing the grid row which contains the passed element.</div><div class="long">Return the HtmlElement representing the grid row which contains the passed element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Element<div class="sub-desc">The target element</div></li></ul><strong>Returns:</strong><ul><li><code>The</code><div class="sub-desc">row element, or null if the target element is not within a row of this GridView.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-findRowIndex"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-findRowIndex">findRowIndex</a></b>(&nbsp;<code>Element&nbsp;el</code>&nbsp;)
    :
                                        The<div class="mdesc"><div class="short">Return the index of the grid row which contains the passed element.</div><div class="long">Return the index of the grid row which contains the passed element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Element<div class="sub-desc">The target element</div></li></ul><strong>Returns:</strong><ul><li><code>The</code><div class="sub-desc">row index, or &lt;b&gt;false&lt;/b&gt; if the target element is not within a row of this GridView.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-focusCell"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-focusCell">focusCell</a></b>(&nbsp;<code>Number&nbsp;row</code>,&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Focuses the specified cell.</div><div class="long">Focuses the specified cell.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>row</code> : Number<div class="sub-desc">The row index</div></li><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-focusRow"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-focusRow">focusRow</a></b>(&nbsp;<code>Number&nbsp;row</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Focuses the specified row.</div><div class="long">Focuses the specified row.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>row</code> : Number<div class="sub-desc">The row index</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-getCell"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-getCell">getCell</a></b>(&nbsp;<code>Number&nbsp;row</code>,&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        HtmlElement<div class="mdesc"><div class="short">Returns the grid's &amp;lt;TD&gt; HtmlElement at the specified coordinates.</div><div class="long">Returns the grid's &lt;TD> HtmlElement at the specified coordinates.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>row</code> : Number<div class="sub-desc">The row index in which to find the cell.</div></li><li><code>col</code> : Number<div class="sub-desc">The column index of the cell.</div></li></ul><strong>Returns:</strong><ul><li><code>HtmlElement</code><div class="sub-desc">The &amp;lt;TD&gt; at the specified coordinates.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-getHeaderCell"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-getHeaderCell">getHeaderCell</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        HtmlElement<div class="mdesc"><div class="short">Return the &amp;lt;TD&gt; HtmlElement which represents the Grid's header cell for the specified column index.</div><div class="long">Return the &lt;TD> HtmlElement which represents the Grid's header cell for the specified column index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>HtmlElement</code><div class="sub-desc">The &amp;lt;TD&gt; element.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-getRow"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-getRow">getRow</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        HtmlElement<div class="mdesc"><div class="short">Return the &amp;lt;TR&gt; HtmlElement which represents a Grid row for the specified index.</div><div class="long">Return the &lt;TR> HtmlElement which represents a Grid row for the specified index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The row index</div></li></ul><strong>Returns:</strong><ul><li><code>HtmlElement</code><div class="sub-desc">The &amp;lt;TR&gt; element.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-getRowClass"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-getRowClass">getRowClass</a></b>(&nbsp;<code>Record&nbsp;record</code>,&nbsp;<code>Number&nbsp;index</code>,&nbsp;<code>Object&nbsp;rowParams</code>,&nbsp;<code>Store&nbsp;store</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Override this function to apply custom CSS classes to rows during rendering.  You can also supply custom
parameters t...</div><div class="long">Override this function to apply custom CSS classes to rows during rendering.  You can also supply custom
parameters to the row template for the current row to customize how it is rendered using the <b>rowParams</b>
parameter.  This function should return the CSS class name (or empty string '' for none) that will be added
to the row's wrapping div.  To apply multiple class names, simply return them space-delimited within the string
(e.g., 'my-class another-class'). Example usage:
    <pre><code>viewConfig: {
    forceFit: true,
    showPreview: true, <i>// custom property</i>
    enableRowBody: true, <i>// required to create a second, full-width row to show expanded Record data</i>
    getRowClass: <b>function</b>(record, rowIndex, rp, ds){ <i>// rp = rowParams</i>
        <b>if</b>(this.showPreview){
            rp.body = <em>'&lt;p>'</em>+record.data.excerpt+<em>'&lt;/p>'</em>;
            <b>return</b> <em>'x-grid3-row-expanded'</em>;
        }
        <b>return</b> <em>'x-grid3-row-collapsed'</em>;
    }
},</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Record<div class="sub-desc">The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> corresponding to the current row.</div></li><li><code>index</code> : Number<div class="sub-desc">The row index.</div></li><li><code>rowParams</code> : Object<div class="sub-desc">A config object that is passed to the row template during rendering that allows
customization of various aspects of a grid row.
<p>If <a href="output/Ext.grid.GridView.html#Ext.grid.GridView-enableRowBody" ext:member="enableRowBody" ext:cls="Ext.grid.GridView">enableRowBody</a> is configured <b><tt></tt>true</b>, then the following properties may be set
by this function, and will be used to render a full-width expansion row below each grid row:</p>
<ul>
<li><code>body</code> : String <div class="sub-desc">An HTML fragment to be used as the expansion row's body content (defaults to '').</div></li>
<li><code>bodyStyle</code> : String <div class="sub-desc">A CSS style specification that will be applied to the expansion row's &lt;tr> element. (defaults to '').</div></li>
</ul>
The following property will be passed in, and may be appended to:
<ul>
<li><code>tstyle</code> : String <div class="sub-desc">A CSS style specification that willl be applied to the &lt;table> element which encapsulates
both the standard grid row, and any expansion row.</div></li>
</ul></div></li><li><code>store</code> : Store<div class="sub-desc">The <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> this grid is bound to</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">a CSS class name to add to the row.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-refresh"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-refresh">refresh</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;headersToo</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Refreshs the grid UI</div><div class="long">Refreshs the grid UI<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>headersToo</code> : Boolean<div class="sub-desc">(optional) True to also refresh the headers</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-scrollToTop"></a><b><a href="source/GridView.html#method-Ext.grid.GridView-scrollToTop">scrollToTop</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Scrolls the grid to the top</div><div class="long">Scrolls the grid to the top<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.grid.GridView-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-beforerefresh"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-beforerefresh">beforerefresh</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired before the view is refreshed.</div><div class="long">Internal UI Event. Fired before the view is refreshed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-beforerowremoved"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-beforerowremoved">beforerowremoved</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.data.Record&nbsp;record</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired before a row is removed.</div><div class="long">Internal UI Event. Fired before a row is removed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc">The index of the row to be removed.</div></li><li><code>record</code> : Ext.data.Record<div class="sub-desc">The Record to be removed</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-beforerowsinserted"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-beforerowsinserted">beforerowsinserted</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>,&nbsp;<code>Number&nbsp;firstRow</code>,&nbsp;<code>Number&nbsp;lastRow</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired before rows are inserted.</div><div class="long">Internal UI Event. Fired before rows are inserted.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li><li><code>firstRow</code> : Number<div class="sub-desc">The index of the first row to be inserted.</div></li><li><code>lastRow</code> : Number<div class="sub-desc">The index of the last row to be inserted.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-refresh"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-refresh">refresh</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired after the GridView's body has been refreshed.</div><div class="long">Internal UI Event. Fired after the GridView's body has been refreshed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-rowremoved"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-rowremoved">rowremoved</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.data.Record&nbsp;record</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired after a row is removed.</div><div class="long">Internal UI Event. Fired after a row is removed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc">The index of the row that was removed.</div></li><li><code>record</code> : Ext.data.Record<div class="sub-desc">The Record that was removed</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-rowsinserted"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-rowsinserted">rowsinserted</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>,&nbsp;<code>Number&nbsp;firstRow</code>,&nbsp;<code>Number&nbsp;lastRow</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired after rows are inserted.</div><div class="long">Internal UI Event. Fired after rows are inserted.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li><li><code>firstRow</code> : Number<div class="sub-desc">The index of the first inserted.</div></li><li><code>lastRow</code> : Number<div class="sub-desc">The index of the last row inserted.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridView-rowupdated"></a><b><a href="source/GridView.html#event-Ext.grid.GridView-rowupdated">rowupdated</a></b> :
                                      (&nbsp;<code>Ext.grid.GridView&nbsp;view</code>,&nbsp;<code>Number&nbsp;firstRow</code>,&nbsp;<code>Ext.data.record&nbsp;record</code>&nbsp;)
    <div class="mdesc"><div class="short">Internal UI Event. Fired after a row has been updated.</div><div class="long">Internal UI Event. Fired after a row has been updated.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>view</code> : Ext.grid.GridView<div class="sub-desc"></div></li><li><code>firstRow</code> : Number<div class="sub-desc">The index of the row updated.</div></li><li><code>record</code> : Ext.data.record<div class="sub-desc">The Record backing the row updated.</div></li></ul></div></div></div></td><td class="msource">GridView</td></tr></tbody></table></div>