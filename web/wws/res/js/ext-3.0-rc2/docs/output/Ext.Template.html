<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Template-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Template-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Template-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Template"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Template-more.html#cls-Ext.Template">Ext.Template</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Template-more.js,&#13;Template.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Template-more.html#cls-Ext.Template">Template</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.XTemplate.html" ext:cls="Ext.XTemplate">XTemplate</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Represents an HTML fragment template. Templates can be precompiled for greater performance.
For a list of available format functions, see <a href="output/Ext.util.Format.html" ext:cls="Ext.util.Format">Ext.util.Format</a>.<br />
Usage:
<pre><code><b>var</b> t = <b>new</b> Ext.Template(
    <em>'&lt;div name=<em>"{id}"</em>&gt;'</em>,
        <em>'&lt;span class=<em>"{cls}"</em>&gt;{name:trim} {value:ellipsis(10)}&lt;/span&gt;'</em>,
    <em>'&lt;/div&gt;'</em>
);
t.append(<em>'some-element'</em>, {id: <em>'myid'</em>, cls: <em>'myclass'</em>, name: <em>'foo'</em>, value: <em>'bar'</em>});</code></pre></div><div class="hr"></div><a id="Ext.Template-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-disableFormats"></a><b><a href="source/Template-more.html#prop-Ext.Template-disableFormats">disableFormats</a></b> : Boolean<div class="mdesc"><tt>true</tt> to disable format functions (defaults to <tt>false</tt>)</div></td><td class="msource">Template</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-re"></a><b><a href="source/Template.html#prop-Ext.Template-re">re</a></b> : RegExp<div class="mdesc">The regular expression used to match template variables</div></td><td class="msource">Template</td></tr></tbody></table><a id="Ext.Template-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-Template"></a><b><a href="source/Template-more.html#cls-Ext.Template">Template</a></b>(&nbsp;<code>String/Array&nbsp;html</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>html</code> : String/Array<div class="sub-desc">The HTML fragment or an array of fragments to join("") or multiple arguments to join("")</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-Template.from"></a><b><a href="source/Template.html#method-Ext.Template-Template.from">Template.from</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        Ext.Template<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Creates a template from the passed element's value (display:none textarea, preferred) or innerHTML.</div><div class="long">&lt;static&gt;&nbsp;Creates a template from the passed element's value (<i>display:none</i> textarea, preferred) or innerHTML.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">A DOM element or its id</div></li><li><code>config</code> : Object<div class="sub-desc">A configuration object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Template</code><div class="sub-desc">The created template</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-append"></a><b><a href="source/Template.html#method-Ext.Template-append">append</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and appends the new node(s) to el.</div><div class="long">Applies the supplied values to the template and appends the new node(s) to el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-apply"></a><b><a href="source/Template.html#method-Ext.Template-apply">apply</a></b>(&nbsp;<code>Object/Array&nbsp;values</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Alias for applyTemplate
Returns an HTML fragment of this template with the specified values applied.</div><div class="long">Alias for <a href="output/Ext.Template.html#Ext.Template-applyTemplate" ext:member="applyTemplate" ext:cls="Ext.Template">applyTemplate</a>
Returns an HTML fragment of this template with the specified values applied.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The HTML fragment</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-applyTemplate"></a><b><a href="source/Template.html#method-Ext.Template-applyTemplate">applyTemplate</a></b>(&nbsp;<code>Object/Array&nbsp;values</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns an HTML fragment of this template with the specified values applied.</div><div class="long">Returns an HTML fragment of this template with the specified values applied.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The HTML fragment</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-compile"></a><b><a href="source/Template.html#method-Ext.Template-compile">compile</a></b>()
    :
                                        Ext.Template<div class="mdesc"><div class="short">Compiles the template into an internal function, eliminating the RegEx overhead.</div><div class="long">Compiles the template into an internal function, eliminating the RegEx overhead.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Template</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-insertAfter"></a><b><a href="source/Template.html#method-Ext.Template-insertAfter">insertAfter</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and inserts the new node(s) after el.</div><div class="long">Applies the supplied values to the template and inserts the new node(s) after el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-insertBefore"></a><b><a href="source/Template.html#method-Ext.Template-insertBefore">insertBefore</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and inserts the new node(s) before el.</div><div class="long">Applies the supplied values to the template and inserts the new node(s) before el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-insertFirst"></a><b><a href="source/Template.html#method-Ext.Template-insertFirst">insertFirst</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and inserts the new node(s) as the first child of el.</div><div class="long">Applies the supplied values to the template and inserts the new node(s) as the first child of el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-overwrite"></a><b><a href="source/Template.html#method-Ext.Template-overwrite">overwrite</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and overwrites the content of el with the new node(s).</div><div class="long">Applies the supplied values to the template and overwrites the content of el with the new node(s).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource">Template</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-set"></a><b><a href="source/Template.html#method-Ext.Template-set">set</a></b>(&nbsp;<code>String&nbsp;html</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;compile</code>]</span>&nbsp;)
    :
                                        Ext.Template<div class="mdesc"><div class="short">Sets the HTML used as the template and optionally compiles it.</div><div class="long">Sets the HTML used as the template and optionally compiles it.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>html</code> : String<div class="sub-desc"></div></li><li><code>compile</code> : Boolean<div class="sub-desc">(optional) True to compile the template (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Template</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">Template</td></tr></tbody></table><a id="Ext.Template-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>