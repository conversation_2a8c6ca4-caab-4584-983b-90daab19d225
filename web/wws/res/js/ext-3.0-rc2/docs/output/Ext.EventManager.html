<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.EventManager-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.EventManager-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.EventManager-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.EventManager"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/EventManager-more.html#cls-Ext.EventManager">Ext.EventManager</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">EventManager-more.js,&#13;EventManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/EventManager-more.html#cls-Ext.EventManager">EventManager</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Registers event handlers that want to receive a normalized EventObject instead of the standard browser event and provides
several useful events directly.
See <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> for more details on normalized event objects.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.EventManager-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-A"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-A">A</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-ALT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-ALT">ALT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-B"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-B">B</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-BACKSPACE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-BACKSPACE">BACKSPACE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-C"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-C">C</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-CAPS_LOCK"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-CAPS_LOCK">CAPS_LOCK</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-CONTEXT_MENU"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-CONTEXT_MENU">CONTEXT_MENU</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-CTRL"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-CTRL">CTRL</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-D"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-D">D</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-DELETE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-DELETE">DELETE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-DOWN"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-DOWN">DOWN</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-E"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-E">E</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-EIGHT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-EIGHT">EIGHT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-END"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-END">END</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-ENTER"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-ENTER">ENTER</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-ESC"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-ESC">ESC</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F">F</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F1"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F1">F1</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F10"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F10">F10</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F11"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F11">F11</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F12"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F12">F12</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F2"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F2">F2</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F3"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F3">F3</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F4"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F4">F4</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F5"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F5">F5</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F6"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F6">F6</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F7"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F7">F7</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F8"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F8">F8</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-F9"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-F9">F9</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-FIVE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-FIVE">FIVE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-FOUR"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-FOUR">FOUR</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-G"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-G">G</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-H"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-H">H</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-HOME"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-HOME">HOME</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-I"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-I">I</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-INSERT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-INSERT">INSERT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-J"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-J">J</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-K"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-K">K</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-L"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-L">L</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-LEFT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-LEFT">LEFT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-M"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-M">M</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-N"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-N">N</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NINE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NINE">NINE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_CENTER"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_CENTER">NUM_CENTER</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_DIVISION"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_DIVISION">NUM_DIVISION</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_EIGHT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_EIGHT">NUM_EIGHT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_FIVE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_FIVE">NUM_FIVE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_FOUR"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_FOUR">NUM_FOUR</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_MINUS"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_MINUS">NUM_MINUS</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_MULTIPLY"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_MULTIPLY">NUM_MULTIPLY</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_NINE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_NINE">NUM_NINE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_ONE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_ONE">NUM_ONE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_PERIOD"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_PERIOD">NUM_PERIOD</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_PLUS"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_PLUS">NUM_PLUS</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_SEVEN"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_SEVEN">NUM_SEVEN</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_SIX"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_SIX">NUM_SIX</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_THREE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_THREE">NUM_THREE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_TWO"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_TWO">NUM_TWO</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-NUM_ZERO"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-NUM_ZERO">NUM_ZERO</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-O"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-O">O</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-ONE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-ONE">ONE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-P"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-P">P</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-PAGE_DOWN"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-PAGE_DOWN">PAGE_DOWN</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-PAGE_UP"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-PAGE_UP">PAGE_UP</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-PAUSE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-PAUSE">PAUSE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-PRINT_SCREEN"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-PRINT_SCREEN">PRINT_SCREEN</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-Q"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-Q">Q</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-R"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-R">R</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-RETURN"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-RETURN">RETURN</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-RIGHT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-RIGHT">RIGHT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-S"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-S">S</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-SEVEN"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-SEVEN">SEVEN</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-SHIFT"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-SHIFT">SHIFT</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-SIX"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-SIX">SIX</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-SPACE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-SPACE">SPACE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-T"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-T">T</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-TAB"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-TAB">TAB</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-THREE"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-THREE">THREE</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-TWO"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-TWO">TWO</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-U"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-U">U</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-UP"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-UP">UP</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-V"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-V">V</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-W"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-W">W</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-X"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-X">X</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-Y"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-Y">Y</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-Z"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-Z">Z</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-ZERO"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-ZERO">ZERO</a></b> : Number<div class="mdesc">Key constant</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-ieDeferSrc"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-ieDeferSrc">ieDeferSrc</a></b> : Object<div class="mdesc">Url used for onDocumentReady with using SSL (defaults to Ext.SSL_SECURE_URL)</div></td><td class="msource">EventManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-textResizeInterval"></a><b><a href="source/EventManager-more.html#prop-Ext.EventManager-textResizeInterval">textResizeInterval</a></b> : Object<div class="mdesc">The frequency, in milliseconds, to check for text resize events (defaults to 50)</div></td><td class="msource">EventManager</td></tr></tbody></table><a id="Ext.EventManager-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-addListener"></a><b><a href="source/EventManager.html#method-Ext.EventManager-addListener">addListener</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to an element.  The shorthand version on is equivalent.  Typically you will
use Ext.Element....</div><div class="long">Appends an event handler to an element.  The shorthand version <a href="output/Ext.EventManager.html#Ext.EventManager-on" ext:member="on" ext:cls="Ext.EventManager">on</a> is equivalent.  Typically you will
use <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">Ext.Element.addListener</a> directly on an Element in favor of calling this version.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The html element or id to assign the event handler to</div></li><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler function the event invokes This function is passed
the following parameters:<ul>
<li>evt : EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">EventObject</a> describing the event.</div></li>
<li>t : Element<div class="sub-desc">The <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> which was the target of the event.
Note that this may be filtered by using the <tt>delegate</tt> option.</div></li>
<li>o : Object<div class="sub-desc">The options object from the addListener call.</div></li>
</ul></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the handler
function (the handler function's "this" context)</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration properties.
This may contain any of the following properties:<ul>
<li>scope {Object} : The scope in which to execute the handler function. The handler function's "this" context.</li>
<li>delegate {String} : A simple selector to filter the target or look for a descendant of the target</li>
<li>stopEvent {Boolean} : True to stop the event. That is stop propagation, and prevent the default action.</li>
<li>preventDefault {Boolean} : True to prevent the default action</li>
<li>stopPropagation {Boolean} : True to prevent event propagation</li>
<li>normalized {Boolean} : False to pass a browser event to the handler function instead of an Ext.EventObject</li>
<li>delay {Number} : The number of milliseconds to delay the invocation of the handler after te event fires.</li>
<li>single {Boolean} : True to add a handler to handle just the next firing of the event, and then remove itself.</li>
<li>buffer {Number} : Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</li>
<li>target {Element} : Only call the handler if the event was fired on the target Element, <i>not</i>
if the event was bubbled up from a child node.</li>
</ul><br>
<p>See <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">Ext.Element.addListener</a> for examples of how to use these options.</p></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-hasModifier"></a><b><a href="source/EventManager-more.html#method-Ext.EventManager-hasModifier">hasModifier</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the control, meta, shift or alt key was pressed during this event.</div><div class="long">Returns true if the control, meta, shift or alt key was pressed during this event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-on"></a><b><a href="source/EventManager.html#method-Ext.EventManager-on">on</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to an element.  Shorthand for addListener.</div><div class="long">Appends an event handler to an element.  Shorthand for <a href="output/Ext.EventManager.html#Ext.EventManager-addListener" ext:member="addListener" ext:cls="Ext.EventManager">addListener</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The html element or id to assign the event handler to</div></li><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler function the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the handler
function (the handler function's "this" context)</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing standard <a href="output/Ext.EventManager.html#Ext.EventManager-addListener" ext:member="addListener" ext:cls="Ext.EventManager">addListener</a> options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-onDocumentReady"></a><b><a href="source/EventManager.html#method-Ext.EventManager-onDocumentReady">onDocumentReady</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>boolean&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Fires when the document is ready (before onload and before images are loaded). Can be
accessed shorthanded as Ext.onR...</div><div class="long">Fires when the document is ready (before onload and before images are loaded). Can be
accessed shorthanded as Ext.onReady().<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) An object that becomes the scope of the handler</div></li><li><code>options</code> : boolean<div class="sub-desc">(optional) An object containing standard <a href="output/Ext.EventManager.html#Ext.EventManager-addListener" ext:member="addListener" ext:cls="Ext.EventManager">addListener</a> options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-onTextResize"></a><b><a href="source/EventManager-more.html#method-Ext.EventManager-onTextResize">onTextResize</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>,&nbsp;<code>boolean&nbsp;options</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Fires when the user changes the active text size. Handler gets called with 2 params, the old size and the new size.</div><div class="long">Fires when the user changes the active text size. Handler gets called with 2 params, the old size and the new size.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">An object that becomes the scope of the handler</div></li><li><code>options</code> : boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-onWindowResize"></a><b><a href="source/EventManager-more.html#method-Ext.EventManager-onWindowResize">onWindowResize</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>,&nbsp;<code>boolean&nbsp;options</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Fires when the window is resized and provides resize event buffering (50 milliseconds), passes new viewport width and...</div><div class="long">Fires when the window is resized and provides resize event buffering (50 milliseconds), passes new viewport width and height to handlers.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">An object that becomes the scope of the handler</div></li><li><code>options</code> : boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-removeAll"></a><b><a href="source/EventManager.html#method-Ext.EventManager-removeAll">removeAll</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes all event handers from an element.  Typically you will use Ext.Element.removeAllListeners
directly on an Elem...</div><div class="long">Removes all event handers from an element.  Typically you will use <a href="output/Ext.Element.html#Ext.Element-removeAllListeners" ext:member="removeAllListeners" ext:cls="Ext.Element">Ext.Element.removeAllListeners</a>
directly on an Element in favor of calling this version.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The id or html element from which to remove the event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-removeListener"></a><b><a href="source/EventManager.html#method-Ext.EventManager-removeListener">removeListener</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;fn</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler from an element.  The shorthand version un is equivalent.  Typically
you will use Ext.Elemen...</div><div class="long">Removes an event handler from an element.  The shorthand version <a href="output/Ext.EventManager.html#Ext.EventManager-un" ext:member="un" ext:cls="Ext.EventManager">un</a> is equivalent.  Typically
you will use <a href="output/Ext.Element.html#Ext.Element-removeListener" ext:member="removeListener" ext:cls="Ext.Element">Ext.Element.removeListener</a> directly on an Element in favor of calling this version.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The id or html element from which to remove the event</div></li><li><code>eventName</code> : String<div class="sub-desc">The type of event</div></li><li><code>fn</code> : Function<div class="sub-desc">The handler function to remove</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-removeResizeListener"></a><b><a href="source/EventManager-more.html#method-Ext.EventManager-removeResizeListener">removeResizeListener</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes the passed window resize listener.</div><div class="long">Removes the passed window resize listener.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">The scope of handler</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventManager-un"></a><b><a href="source/EventManager.html#method-Ext.EventManager-un">un</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;fn</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Removes an event handler from an element.  Shorthand for removeListener.</div><div class="long">Removes an event handler from an element.  Shorthand for <a href="output/Ext.EventManager.html#Ext.EventManager-removeListener" ext:member="removeListener" ext:cls="Ext.EventManager">removeListener</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The id or html element from which to remove the event</div></li><li><code>eventName</code> : String<div class="sub-desc">The type of event</div></li><li><code>fn</code> : Function<div class="sub-desc">The handler function to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if a listener was actually removed, else false</div></li></ul></div></div></div></td><td class="msource">EventManager</td></tr></tbody></table><a id="Ext.EventManager-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>