<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.FlashProxy-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.FlashProxy-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.FlashProxy-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.FlashProxy"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/EventProxy.html#cls-Ext.FlashProxy">Ext.FlashProxy</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">EventProxy.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/EventProxy.html#cls-Ext.FlashProxy">FlashProxy</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.FlashProxy-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.FlashProxy-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.FlashProxy-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>