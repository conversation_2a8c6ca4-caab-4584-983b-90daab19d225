<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.direct.RemotingProvider-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.direct.RemotingProvider-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.direct.RemotingProvider-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.direct.RemotingProvider-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.direct.RemotingProvider"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.direct.Provider.html" ext:member="" ext:cls="Ext.direct.Provider">Provider</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.direct.JsonProvider.html" ext:member="" ext:cls="Ext.direct.JsonProvider">JsonProvider</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">RemotingProvider</pre></div><h1>Class <a href="source/RemotingProvider.html#cls-Ext.direct.RemotingProvider">Ext.direct.RemotingProvider</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.direct</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">RemotingProvider.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/RemotingProvider.html#cls-Ext.direct.RemotingProvider">RemotingProvider</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.direct.JsonProvider.html" ext:cls="Ext.direct.JsonProvider" ext:member="">JsonProvider</a></td></tr></table><div class="description"><p>Provides for remote procedure call (RPC) type of connection where the client can initiate
a procedure on the server.</p>
<p>When adding a provider via <a href="output/Ext.Direct.html" ext:cls="Ext.Direct">Ext.Direct</a>.<a href="output/Ext.Direct.html#Ext.Direct-add" ext:member="add" ext:cls="Ext.Direct">add</a> the
Ext.direct.RemotingProvider will be invoked to create a client-side stub of the
provider. This Class will never need to be invoked directly.</p>
<p>Configurations for this Class should be outputted by the server-side Ext.Direct
stack when the API description is built.</p></div><div class="hr"></div><a id="Ext.direct.RemotingProvider-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-actions"></a><b><a href="source/RemotingProvider.html#cfg-Ext.direct.RemotingProvider-actions">actions</a></b> : Object<div class="mdesc"><div class="short">Object literal defining the server side actions and methods. For example, if&#13;
the Provider is configured with:&#13;
"acti...</div><div class="long">Object literal defining the server side actions and methods. For example, if
the Provider is configured with:
<pre><code><em>"actions"</em>:{ <i>// each property within the <em>'actions'</em> object represents a server side Class 
</i>
    <em>"TestAction"</em>:[ <i>// array of methods within each server side Class to be   
</i>
    {              <i>// stubbed out on client
</i>
        <em>"name"</em>:<em>"doEcho"</em>, 
        <em>"len"</em>:1          
    },{
        <em>"name"</em>:<em>"multiply"</em>,<i>// name of method
</i>
        <em>"len"</em>:2           <i>// the number of parameters that will be used to create an
</i>
                          <i>// array of data to send to the server side <b>function</b> 
</i>
    },{
        <em>"name"</em>:<em>"doForm"</em>,
        <em>"formHandler"</em>:true, <i>// use specialized form handling method 
</i>
        <em>"len"</em>:1
    }]
}</code></pre>
a <b>client side</b> handler to call the server side method "multiply" in the
"TestAction" Class might look like this:
<pre><code>TestAction.multiply(
    2, 4, <i>// pass two arguments to server, so specify len=2
</i>
    <i>// callback <b>function</b> after the server is called
</i>
    <i>// result: the result returned by the server
</i>
    <i>//      e: Ext.Direct.RemotingEvent object
</i>
    <b>function</b>(result, e){
        <b>var</b> t = e.getTransaction();
        <b>var</b> action = t.action; <i>// server side Class called
</i>
        <b>var</b> method = t.method; <i>// server side method called
</i>
        <b>if</b>(e.status){
            <b>var</b> answer = Ext.encode(result); <i>// 8
</i>
    
        }<b>else</b>{
            <b>var</b> msg = e.message; <i>// failure message
</i>
        }
    }
);</code></pre>
In the example above, the server side "multiply" function will be passed two
arguments (2 and 4).  The "multiply" method should return the value 8 which will be
available as the <tt>result</tt> in the example above.</div></div></td><td class="msource">RemotingProvider</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-enableBuffer"></a><b><a href="source/RemotingProvider.html#cfg-Ext.direct.RemotingProvider-enableBuffer">enableBuffer</a></b> : Number/Boolean<div class="mdesc"><div class="short">true or false to enable or disable combining of method calls.&#13;
If a number is specified this is the amount of time in...</div><div class="long"><tt>true</tt> or <tt>false</tt> to enable or disable combining of method calls.
If a number is specified this is the amount of time in milliseconds to wait
before sending a batched request (defaults to <tt>10</tt>.</div></div></td><td class="msource">RemotingProvider</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-enableUrlEncode"></a><b><a href="source/RemotingProvider.html#cfg-Ext.direct.RemotingProvider-enableUrlEncode">enableUrlEncode</a></b> : String<div class="mdesc">Specify which param will hold the arguments for the method.
Defaults to <tt>'data'</tt>.</div></td><td class="msource">RemotingProvider</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-id"></a><b><a href="source/Provider.html#cfg-Ext.direct.Provider-id">id</a></b> : String<div class="mdesc"><div class="short">The unique id of the provider (defaults to an auto-assigned id).&#13;
You should assign an id if you need to be able to a...</div><div class="long">The unique id of the provider (defaults to an <a href="output/Ext.html#Ext-id" ext:member="id" ext:cls="Ext">auto-assigned id</a>).
You should assign an id if you need to be able to access the provider later and you do
not have an object reference available, for example:
<pre><code>Ext.Direct.addProvider(
    {
        type: <em>'polling'</em>,
        url:  <em>'php/poll.php'</em>,
        id:   <em>'poll-provider'</em>
    }
);
     
<b>var</b> p = <a href="output/Ext.Direct.html" ext:cls="Ext.Direct">Ext.Direct</a>.<a href="output/Ext.Direct.html#Ext.Direct-getProvider" ext:member="getProvider" ext:cls="Ext.Direct">getProvider</a>(<em>'poll-provider'</em>);
p.disconnect();</code></pre></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#id" ext:member="#id" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-maxRetries"></a><b><a href="source/RemotingProvider.html#cfg-Ext.direct.RemotingProvider-maxRetries">maxRetries</a></b> : Number<div class="mdesc">Number of times to re-attempt delivery on failure of a call.</div></td><td class="msource">RemotingProvider</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-namespace"></a><b><a href="source/RemotingProvider.html#cfg-Ext.direct.RemotingProvider-namespace">namespace</a></b> : String/Object<div class="mdesc"><div class="short">Namespace for the Remoting Provider (defaults to the browser global scope of window).&#13;
Explicitly specify the namespa...</div><div class="long">Namespace for the Remoting Provider (defaults to the browser global scope of <i>window</i>).
Explicitly specify the namespace Object, or specify a String to have a
<a href="output/Ext.html#Ext-namespace" ext:member="namespace" ext:cls="Ext">namespace created</a> implicitly.</div></div></td><td class="msource">RemotingProvider</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-priority"></a><b><a href="source/Provider.html#cfg-Ext.direct.Provider-priority">priority</a></b> : Number<div class="mdesc"><div class="short">Priority of the request. Lower is higher priority, 0 means "duplex" (always on).&#13;
All Providers default to 1 except f...</div><div class="long">Priority of the request. Lower is higher priority, <tt>0</tt> means "duplex" (always on).
All Providers default to <tt>1</tt> except for PollingProvider which defaults to <tt>3</tt>.</div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#priority" ext:member="#priority" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-type"></a><b><a href="source/Provider.html#cfg-Ext.direct.Provider-type">type</a></b> : String<div class="mdesc"><div class="short">Required, undefined by default.  The type of provider specified&#13;
to Ext.Direct.addProvider to create a&#13;
new Provider....</div><div class="long"><b>Required</b>, <tt>undefined</tt> by default.  The <tt>type</tt> of provider specified
to <a href="output/Ext.Direct.html" ext:cls="Ext.Direct">Ext.Direct</a>.<a href="output/Ext.Direct.html#Ext.Direct-addProvider" ext:member="addProvider" ext:cls="Ext.Direct">addProvider</a> to create a
new Provider. Acceptable values by default are:<div class="mdetail-params"><ul>
<li><b><tt>polling</tt></b> : <a href="output/Ext.direct.PollingProvider.html" ext:cls="Ext.direct.PollingProvider">PollingProvider</a></li>
<li><b><tt>remoting</tt></b> : <a href="output/Ext.direct.RemotingProvider.html" ext:cls="Ext.direct.RemotingProvider">RemotingProvider</a></li>
</ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#type" ext:member="#type" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-url"></a><b><a href="source/RemotingProvider.html#cfg-Ext.direct.RemotingProvider-url">url</a></b> : String<div class="mdesc"><b>Required<b>. The url to connect to the <a href="output/Ext.Direct.html" ext:cls="Ext.Direct">Ext.Direct</a> server-side router.</div></td><td class="msource">RemotingProvider</td></tr></tbody></table><a id="Ext.direct.RemotingProvider-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-connect"></a><b><a href="source/Provider.html#prop-Ext.direct.Provider-connect">connect</a></b> : Object<div class="mdesc">Abstract methods for subclasses to implement.</div></td><td class="msource"><a href="output/Ext.direct.Provider.html#connect" ext:member="#connect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-disconnect"></a><b><a href="source/Provider.html#prop-Ext.direct.Provider-disconnect">disconnect</a></b> : Object<div class="mdesc">Abstract methods for subclasses to implement.</div></td><td class="msource"><a href="output/Ext.direct.Provider.html#disconnect" ext:member="#disconnect" ext:cls="Ext.direct.Provider">Provider</a></td></tr></tbody></table><a id="Ext.direct.RemotingProvider-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-isConnected"></a><b><a href="source/Provider.html#method-Ext.direct.Provider-isConnected">isConnected</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns whether or not the server-side is currently connected.&#13;
Abstract method for subclasses to implement.</div><div class="long">Returns whether or not the server-side is currently connected.
Abstract method for subclasses to implement.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#isConnected" ext:member="#isConnected" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.direct.RemotingProvider-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-beforecall"></a><b><a href="source/RemotingProvider.html#event-Ext.direct.RemotingProvider-beforecall">beforecall</a></b> :
                                      (&nbsp;<code>Ext.direct.RemotingProvider&nbsp;provider</code>,&nbsp;<code>Ext.Direct.Transaction&nbsp;transaction</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires immediately before the client-side sends off the RPC call.&#13;
By returning false from an event handler you can pr...</div><div class="long">Fires immediately before the client-side sends off the RPC call.
By returning false from an event handler you can prevent the call from
executing.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.RemotingProvider<div class="sub-desc"></div></li><li><code>transaction</code> : Ext.Direct.Transaction<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RemotingProvider</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.RemotingProvider-call"></a><b><a href="source/RemotingProvider.html#event-Ext.direct.RemotingProvider-call">call</a></b> :
                                      (&nbsp;<code>Ext.direct.RemotingProvider&nbsp;provider</code>,&nbsp;<code>Ext.Direct.Transaction&nbsp;transaction</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires immediately after the request to the server-side is sent. This does&#13;
NOT fire after the response has come back ...</div><div class="long">Fires immediately after the request to the server-side is sent. This does
NOT fire after the response has come back from the call.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.RemotingProvider<div class="sub-desc"></div></li><li><code>transaction</code> : Ext.Direct.Transaction<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RemotingProvider</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-connect"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-connect">connect</a></b> :
                                      (&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the Provider connects to the server-side</div><div class="long">Fires when the Provider connects to the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#connect" ext:member="#connect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-data"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-data">data</a></b> :
                                      (&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>,&nbsp;<code>event&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the Provider receives data from the server-side</div><div class="long">Fires when the Provider receives data from the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li><li><code>e</code> : event<div class="sub-desc">The <a href="output/Ext.Direct.html#Ext.Direct-eventTypes" ext:member="eventTypes" ext:cls="Ext.Direct">Ext.Direct.Event type</a> that occurred.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#data" ext:member="#data" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-disconnect"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-disconnect">disconnect</a></b> :
                                      (&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the Provider disconnects from the server-side</div><div class="long">Fires when the Provider disconnects from the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#disconnect" ext:member="#disconnect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-exception"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-exception">exception</a></b> :
                                      ()
    <div class="mdesc"><div class="short">Fires when the Provider receives an exception from the server-side</div><div class="long">Fires when the Provider receives an exception from the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li>None.</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#exception" ext:member="#exception" ext:cls="Ext.direct.Provider">Provider</a></td></tr></tbody></table></div>