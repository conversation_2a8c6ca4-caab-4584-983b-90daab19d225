<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.RowSelectionModel-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.RowSelectionModel-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.RowSelectionModel-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.grid.RowSelectionModel-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.RowSelectionModel"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.grid.AbstractSelectionModel.html" ext:member="" ext:cls="Ext.grid.AbstractSelectionModel">AbstractSelectionModel</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">RowSelectionModel</pre></div><h1>Class <a href="source/RowSelectionModel.html#cls-Ext.grid.RowSelectionModel">Ext.grid.RowSelectionModel</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">RowSelectionModel.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/RowSelectionModel.html#cls-Ext.grid.RowSelectionModel">RowSelectionModel</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.grid.CheckboxSelectionModel.html" ext:cls="Ext.grid.CheckboxSelectionModel">CheckboxSelectionModel</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.grid.AbstractSelectionModel.html" ext:cls="Ext.grid.AbstractSelectionModel" ext:member="">AbstractSelectionModel</a></td></tr></table><div class="description">The default SelectionModel used by <a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel</a>.
It supports multiple selections and keyboard selection/navigation. The objects stored
as selections and returned by <a href="output/Ext.grid.RowSelectionModel.html#Ext.grid.RowSelectionModel-getSelected" ext:member="getSelected" ext:cls="Ext.grid.RowSelectionModel">getSelected</a>, and <a href="output/Ext.grid.RowSelectionModel.html#Ext.grid.RowSelectionModel-getSelections" ext:member="getSelections" ext:cls="Ext.grid.RowSelectionModel">getSelections</a> are
the <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>s which provide the data for the selected rows.</div><div class="hr"></div><a id="Ext.grid.RowSelectionModel-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-moveEditorOnEnter"></a><b><a href="source/RowSelectionModel.html#cfg-Ext.grid.RowSelectionModel-moveEditorOnEnter">moveEditorOnEnter</a></b> : Boolean<div class="mdesc"><div class="short">False to turn off moving the editor to the next row down when the enter key is pressed
or the next row up when shift ...</div><div class="long">False to turn off moving the editor to the next row down when the enter key is pressed
or the next row up when shift + enter keys are pressed.</div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-singleSelect"></a><b><a href="source/RowSelectionModel.html#cfg-Ext.grid.RowSelectionModel-singleSelect">singleSelect</a></b> : Boolean<div class="mdesc">True to allow selection of only one row at a time (defaults to false)</div></td><td class="msource">RowSelectionModel</td></tr></tbody></table><a id="Ext.grid.RowSelectionModel-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.grid.RowSelectionModel-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-RowSelectionModel"></a><b><a href="source/RowSelectionModel.html#cls-Ext.grid.RowSelectionModel">RowSelectionModel</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-clearSelections"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-clearSelections">clearSelections</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears all selections.</div><div class="long">Clears all selections.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-deselectRange"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-deselectRange">deselectRange</a></b>(&nbsp;<code>Number&nbsp;startRow</code>,&nbsp;<code>Number&nbsp;endRow</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Deselects a range of rows. All rows in between startRow and endRow are also deselected.</div><div class="long">Deselects a range of rows. All rows in between startRow and endRow are also deselected.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>startRow</code> : Number<div class="sub-desc">The index of the first row in the range</div></li><li><code>endRow</code> : Number<div class="sub-desc">The index of the last row in the range</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-deselectRow"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-deselectRow">deselectRow</a></b>(&nbsp;<code>Number&nbsp;row</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Deselects a row.</div><div class="long">Deselects a row.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>row</code> : Number<div class="sub-desc">The index of the row to deselect</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-each"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-each">each</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Calls the passed function with each selection. If the function returns false, iteration is
stopped and this function ...</div><div class="long">Calls the passed function with each selection. If the function returns false, iteration is
stopped and this function returns false. Otherwise it returns true.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">true if all selections were iterated</div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-getCount"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-getCount">getCount</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the number of selected rows.</div><div class="long">Gets the number of selected rows.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-getSelected"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-getSelected">getSelected</a></b>()
    :
                                        Record<div class="mdesc"><div class="short">Returns the first selected record.</div><div class="long">Returns the first selected record.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Record</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-getSelections"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-getSelections">getSelections</a></b>()
    :
                                        Array<div class="mdesc"><div class="short">Returns the selected records</div><div class="long">Returns the selected records<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">Array of selected records</div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-hasNext"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-hasNext">hasNext</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if there is a next record to select</div><div class="long">Returns true if there is a next record to select<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-hasPrevious"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-hasPrevious">hasPrevious</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if there is a previous record to select</div><div class="long">Returns true if there is a previous record to select<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-hasSelection"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-hasSelection">hasSelection</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns True if there is a selection.</div><div class="long">Returns True if there is a selection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-isIdSelected"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-isIdSelected">isIdSelected</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns True if the specified record id is selected.</div><div class="long">Returns True if the specified record id is selected.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The id of record to check</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.AbstractSelectionModel-isLocked"></a><b><a href="source/AbstractSelectionModel.html#method-Ext.grid.AbstractSelectionModel-isLocked">isLocked</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the selections are locked.</div><div class="long">Returns true if the selections are locked.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.AbstractSelectionModel.html#isLocked" ext:member="#isLocked" ext:cls="Ext.grid.AbstractSelectionModel">AbstractSelectionModel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-isSelected"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-isSelected">isSelected</a></b>(&nbsp;<code>Number/Record&nbsp;record</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns True if the specified row is selected.</div><div class="long">Returns True if the specified row is selected.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Number/Record<div class="sub-desc">The record or index of the record to check</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.AbstractSelectionModel-lock"></a><b><a href="source/AbstractSelectionModel.html#method-Ext.grid.AbstractSelectionModel-lock">lock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Locks the selections.</div><div class="long">Locks the selections.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.AbstractSelectionModel.html#lock" ext:member="#lock" ext:cls="Ext.grid.AbstractSelectionModel">AbstractSelectionModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectAll"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectAll">selectAll</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Selects all rows.</div><div class="long">Selects all rows.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectFirstRow"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectFirstRow">selectFirstRow</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Selects the first row in the grid.</div><div class="long">Selects the first row in the grid.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectLastRow"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectLastRow">selectLastRow</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Select the last row.</div><div class="long">Select the last row.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to keep existing selections</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectNext"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectNext">selectNext</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Selects the row immediately following the last selected row.</div><div class="long">Selects the row immediately following the last selected row.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to keep existing selections</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if there is a next row, else false</div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectPrevious"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectPrevious">selectPrevious</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Selects the row that precedes the last selected row.</div><div class="long">Selects the row that precedes the last selected row.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to keep existing selections</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if there is a previous row, else false</div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectRange"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectRange">selectRange</a></b>(&nbsp;<code>Number&nbsp;startRow</code>,&nbsp;<code>Number&nbsp;endRow</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Selects a range of rows. All rows in between startRow and endRow are also selected.</div><div class="long">Selects a range of rows. All rows in between startRow and endRow are also selected.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>startRow</code> : Number<div class="sub-desc">The index of the first row in the range</div></li><li><code>endRow</code> : Number<div class="sub-desc">The index of the last row in the range</div></li><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to retain existing selections</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectRecords"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectRecords">selectRecords</a></b>(&nbsp;<code>Array&nbsp;records</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Select records.</div><div class="long">Select records.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>records</code> : Array<div class="sub-desc">The records to select</div></li><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to keep existing selections</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectRow"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectRow">selectRow</a></b>(&nbsp;<code>Number&nbsp;row</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Selects a row.</div><div class="long">Selects a row.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>row</code> : Number<div class="sub-desc">The index of the row to select</div></li><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to keep existing selections</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectRows"></a><b><a href="source/RowSelectionModel.html#method-Ext.grid.RowSelectionModel-selectRows">selectRows</a></b>(&nbsp;<code>Array&nbsp;rows</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;keepExisting</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Selects multiple rows.</div><div class="long">Selects multiple rows.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rows</code> : Array<div class="sub-desc">Array of the indexes of the row to select</div></li><li><code>keepExisting</code> : Boolean<div class="sub-desc">(optional) True to keep existing selections (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.AbstractSelectionModel-unlock"></a><b><a href="source/AbstractSelectionModel.html#method-Ext.grid.AbstractSelectionModel-unlock">unlock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Unlocks the selections.</div><div class="long">Unlocks the selections.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.AbstractSelectionModel.html#unlock" ext:member="#unlock" ext:cls="Ext.grid.AbstractSelectionModel">AbstractSelectionModel</a></td></tr></tbody></table><a id="Ext.grid.RowSelectionModel-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-beforerowselect"></a><b><a href="source/RowSelectionModel.html#event-Ext.grid.RowSelectionModel-beforerowselect">beforerowselect</a></b> :
                                      (&nbsp;<code>SelectionModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Boolean&nbsp;keepExisting</code>,&nbsp;<code>Record&nbsp;record</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a row is being selected, return false to cancel.</div><div class="long">Fires when a row is being selected, return false to cancel.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : SelectionModel<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc">The index to be selected</div></li><li><code>keepExisting</code> : Boolean<div class="sub-desc">False if other selections will be cleared</div></li><li><code>record</code> : Record<div class="sub-desc">The record to be selected</div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-rowdeselect"></a><b><a href="source/RowSelectionModel.html#event-Ext.grid.RowSelectionModel-rowdeselect">rowdeselect</a></b> :
                                      (&nbsp;<code>SelectionModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Record&nbsp;record</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a row is deselected.</div><div class="long">Fires when a row is deselected.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : SelectionModel<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>record</code> : Record<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-rowselect"></a><b><a href="source/RowSelectionModel.html#event-Ext.grid.RowSelectionModel-rowselect">rowselect</a></b> :
                                      (&nbsp;<code>SelectionModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.data.Record&nbsp;r</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a row is selected.</div><div class="long">Fires when a row is selected.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : SelectionModel<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc">The selected index</div></li><li><code>r</code> : Ext.data.Record<div class="sub-desc">The selected record</div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowSelectionModel-selectionchange"></a><b><a href="source/RowSelectionModel.html#event-Ext.grid.RowSelectionModel-selectionchange">selectionchange</a></b> :
                                      (&nbsp;<code>SelectionModel&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the selection changes</div><div class="long">Fires when the selection changes<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : SelectionModel<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">RowSelectionModel</td></tr></tbody></table></div>