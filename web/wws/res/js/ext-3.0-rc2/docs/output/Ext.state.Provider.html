<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.state.Provider-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.state.Provider-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.state.Provider-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.state.Provider"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Provider1.html#cls-Ext.state.Provider">Ext.state.Provider</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.state</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Provider.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Provider1.html#cls-Ext.state.Provider">Provider</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.state.CookieProvider.html" ext:cls="Ext.state.CookieProvider">CookieProvider</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Abstract base class for state provider implementations. This class provides methods
for encoding and decoding <b>typed</b> variables including dates and defines the
Provider interface.</div><div class="hr"></div><a id="Ext.state.Provider-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.state.Provider-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-clear"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-clear">clear</a></b>(&nbsp;<code>String&nbsp;name</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Clears a value from the state</div><div class="long">Clears a value from the state<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Provider</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-decodeValue"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-decodeValue">decodeValue</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Decodes a string previously encoded with encodeValue.</div><div class="long">Decodes a string previously encoded with <a href="output/Ext.state.Provider.html#Ext.state.Provider-encodeValue" ext:member="encodeValue" ext:cls="Ext.state.Provider">encodeValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The value to decode</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">The decoded value</div></li></ul></div></div></div></td><td class="msource">Provider</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-encodeValue"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-encodeValue">encodeValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Encodes a value including type information.  Decode with decodeValue.</div><div class="long">Encodes a value including type information.  Decode with <a href="output/Ext.state.Provider.html#Ext.state.Provider-decodeValue" ext:member="decodeValue" ext:cls="Ext.state.Provider">decodeValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to encode</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The encoded value</div></li></ul></div></div></div></td><td class="msource">Provider</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-get"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-get">get</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;defaultValue</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Returns the current value for a key</div><div class="long">Returns the current value for a key<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li><li><code>defaultValue</code> : Mixed<div class="sub-desc">A default value to return if the key's value is not found</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">The state data</div></li></ul></div></div></div></td><td class="msource">Provider</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-set"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-set">set</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the value for a key</div><div class="long">Sets the value for a key<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li><li><code>value</code> : Mixed<div class="sub-desc">The value to set</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Provider</td></tr></tbody></table><a id="Ext.state.Provider-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-statechange"></a><b><a href="source/Provider1.html#event-Ext.state.Provider-statechange">statechange</a></b> :
                                      (&nbsp;<code>Provider&nbsp;this</code>,&nbsp;<code>String&nbsp;key</code>,&nbsp;<code>String&nbsp;value</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a state change occurs.</div><div class="long">Fires when a state change occurs.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Provider<div class="sub-desc">This state provider</div></li><li><code>key</code> : String<div class="sub-desc">The state key which was changed</div></li><li><code>value</code> : String<div class="sub-desc">The encoded value for the state</div></li></ul></div></div></div></td><td class="msource">Provider</td></tr></tbody></table></div>