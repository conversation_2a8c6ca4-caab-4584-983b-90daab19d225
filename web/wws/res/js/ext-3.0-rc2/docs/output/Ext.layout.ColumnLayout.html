<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.ColumnLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.ColumnLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.ColumnLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.ColumnLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.ColumnLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.layout.ContainerLayout.html" ext:member="" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">ColumnLayout</pre></div><h1>Class <a href="source/ColumnLayout.html#cls-Ext.layout.ColumnLayout">Ext.layout.ColumnLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">ColumnLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/ColumnLayout.html#cls-Ext.layout.ColumnLayout">ColumnLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout" ext:member="">ContainerLayout</a></td></tr></table><div class="description"><p>This is the layout style of choice for creating structural layouts in a multi-column format where the width of
each column can be specified as a percentage or fixed width, but the height is allowed to vary based on the content.
This class is intended to be extended or created via the layout:'column' <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">Ext.Container.layout</a> config,
and should generally not need to be created directly via the new keyword.</p>
<p>ColumnLayout does not have any direct config options (other than inherited ones), but it does support a
specific config property of <b><tt>columnWidth</tt></b> that can be included in the config of any panel added to it.  The
layout will use the columnWidth (if present) or width of each panel during layout to determine how to size each panel.
If width or columnWidth is not specified for a given panel, its width will default to the panel's width (or auto).</p>
<p>The width property is always evaluated as pixels, and must be a number greater than or equal to 1.
The columnWidth property is always evaluated as a percentage, and must be a decimal value greater than 0 and
less than 1 (e.g., .25).</p>
<p>The basic rules for specifying column widths are pretty simple.  The logic makes two passes through the
set of contained panels.  During the first layout pass, all panels that either have a fixed width or none
specified (auto) are skipped, but their widths are subtracted from the overall container width.  During the second
pass, all panels with columnWidths are assigned pixel widths in proportion to their percentages based on
the total <b>remaining</b> container width.  In other words, percentage width panels are designed to fill the space
left over by all the fixed-width and/or auto-width panels.  Because of this, while you can specify any number of columns
with different percentages, the columnWidths must always add up to 1 (or 100%) when added together, otherwise your
layout may not render as expected.  Example usage:</p>
<pre><code><i>// All columns are percentages -- they must add up to 1
</i>
<b>var</b> p = <b>new</b> Ext.Panel({
    title: <em>'Column Layout - Percentage Only'</em>,
    layout:<em>'column'</em>,
    items: [{
        title: <em>'Column 1'</em>,
        columnWidth: .25 
    },{
        title: <em>'Column 2'</em>,
        columnWidth: .6
    },{
        title: <em>'Column 3'</em>,
        columnWidth: .15
    }]
});

<i>// Mix of width and columnWidth -- all columnWidth values must add up
</i>
<i>// to 1. The first column will take up exactly 120px, and the last two
</i>
<i>// columns will fill the remaining container width.
</i>
<b>var</b> p = <b>new</b> Ext.Panel({
    title: <em>'Column Layout - Mixed'</em>,
    layout:<em>'column'</em>,
    items: [{
        title: <em>'Column 1'</em>,
        width: 120
    },{
        title: <em>'Column 2'</em>,
        columnWidth: .8
    },{
        title: <em>'Column 3'</em>,
        columnWidth: .2
    }]
});</code></pre></div><div class="hr"></div><a id="Ext.layout.ColumnLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#extraCls" ext:member="#extraCls" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-renderHidden"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-renderHidden">renderHidden</a></b> : Boolean<div class="mdesc">True to hide each contained item on render (defaults to false).</div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#renderHidden" ext:member="#renderHidden" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.ColumnLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#fieldTpl" ext:member="#fieldTpl" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.ColumnLayout-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.layout.ColumnLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>