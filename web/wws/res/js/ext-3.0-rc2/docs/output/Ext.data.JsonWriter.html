<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.JsonWriter-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.JsonWriter-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.JsonWriter-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.JsonWriter-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.JsonWriter"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.data.DataWriter.html" ext:member="" ext:cls="Ext.data.DataWriter">DataWriter</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">JsonWriter</pre></div><h1>Class <a href="source/JsonWriter.html#cls-Ext.data.JsonWriter">Ext.data.JsonWriter</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">JsonWriter.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/JsonWriter.html#cls-Ext.data.JsonWriter">JsonWriter</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.data.DataWriter.html" ext:cls="Ext.data.DataWriter" ext:member="">DataWriter</a></td></tr></table><div class="description">DataWriter extension for writing an array or single <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> object(s) in preparation for executing a remote CRUD action.</div><div class="hr"></div><a id="Ext.data.JsonWriter-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-createRecord"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-createRecord">createRecord</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.JsonWriter.html#Ext.data.JsonWriter-createRecord" ext:member="createRecord" ext:cls="Ext.data.JsonWriter">JsonWriter.createRecord</a></div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#createRecord" ext:member="#createRecord" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-destroyRecord"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-destroyRecord">destroyRecord</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.JsonWriter.html#Ext.data.JsonWriter-destroyRecord" ext:member="destroyRecord" ext:cls="Ext.data.JsonWriter">JsonWriter.destroyRecord</a></div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#destroyRecord" ext:member="#destroyRecord" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-listful"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-listful">listful</a></b> : Boolean<div class="mdesc"><div class="short">false by default.  Set true to have the DataWriter always write HTTP params as a list,
even when acting upon a single...</div><div class="long"><tt>false</tt> by default.  Set <tt>true</tt> to have the DataWriter <b>always</b> write HTTP params as a list,
even when acting upon a single record.</div></div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#listful" ext:member="#listful" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonWriter-returnJson"></a><b><a href="source/JsonWriter.html#cfg-Ext.data.JsonWriter-returnJson">returnJson</a></b> : Boolean<div class="mdesc"><div class="short">true to encode the
hashed data. Defaults to true.  When using
Ext.data.DirectProxy, set this to false since Ext.Direc...</div><div class="long"><tt>true</tt> to <a href="output/Ext.util.JSON.html#Ext.util.JSON-encode" ext:member="encode" ext:cls="Ext.util.JSON">encode</a> the
<a href="output/Ext.data.DataWriter.html#Ext.data.DataWriter-toHash" ext:member="toHash" ext:cls="Ext.data.DataWriter">hashed data</a>. Defaults to <tt>true</tt>.  When using
<a href="output/Ext.data.DirectProxy.html" ext:cls="Ext.data.DirectProxy">Ext.data.DirectProxy</a>, set this to <tt>false</tt> since Ext.Direct.JsonProvider will perform
its own json-encoding.</div></div></td><td class="msource">JsonWriter</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-saveRecord"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-saveRecord">saveRecord</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.JsonWriter.html#Ext.data.JsonWriter-saveRecord" ext:member="saveRecord" ext:cls="Ext.data.JsonWriter">JsonWriter.saveRecord</a></div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#saveRecord" ext:member="#saveRecord" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-writeAllFields"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-writeAllFields">writeAllFields</a></b> : Boolean<div class="mdesc"><div class="short">false by default.  Set true to have DataWriter return ALL fields of a modified
record -- not just those that changed....</div><div class="long"><tt>false</tt> by default.  Set <tt>true</tt> to have DataWriter return ALL fields of a modified
record -- not just those that changed.
<tt>false</tt> to have DataWriter only request modified fields from a record.</div></div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#writeAllFields" ext:member="#writeAllFields" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr></tbody></table><a id="Ext.data.JsonWriter-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-meta"></a><b><a href="source/DataWriter.html#prop-Ext.data.DataWriter-meta">meta</a></b> : Mixed<div class="mdesc">This DataWriter's configured metadata as passed to the constructor.</div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#meta" ext:member="#meta" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr></tbody></table><a id="Ext.data.JsonWriter-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonWriter-createRecord"></a><b><a href="source/JsonWriter.html#method-Ext.data.JsonWriter-createRecord">createRecord</a></b>(&nbsp;<code>Ext.data.Record&nbsp;rec</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">createRecord</div><div class="long">createRecord<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rec</code> : Ext.data.Record<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">JsonWriter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonWriter-destroyRecord"></a><b><a href="source/JsonWriter.html#method-Ext.data.JsonWriter-destroyRecord">destroyRecord</a></b>(&nbsp;<code>Ext.data.Record&nbsp;rec</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">destroyRecord</div><div class="long">destroyRecord<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rec</code> : Ext.data.Record<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">JsonWriter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonWriter-render"></a><b><a href="source/JsonWriter.html#method-Ext.data.JsonWriter-render">render</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Record[]&nbsp;rs</code>,&nbsp;<code>Object&nbsp;http</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Final action of a write event.  Apply the written data-object to params.</div><div class="long">Final action of a write event.  Apply the written data-object to params.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>rs</code> : Record[]<div class="sub-desc"></div></li><li><code>http</code> : Object<div class="sub-desc">params</div></li><li><code>data</code> : Object<div class="sub-desc">object populated according to DataReader meta-data "root" and "idProperty"</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">JsonWriter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonWriter-updateRecord"></a><b><a href="source/JsonWriter.html#method-Ext.data.JsonWriter-updateRecord">updateRecord</a></b>(&nbsp;<code>Ext.data.Record&nbsp;rec</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">updateRecord</div><div class="long">updateRecord<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rec</code> : Ext.data.Record<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">JsonWriter</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-write"></a><b><a href="source/DataWriter.html#method-Ext.data.DataWriter-write">write</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;params</code>,&nbsp;<code>Record/Record[]&nbsp;rs</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Writes data in preparation for server-write action.  Simply proxies to DataWriter#update, DataWriter#create
DataWrite...</div><div class="long">Writes data in preparation for server-write action.  Simply proxies to DataWriter#update, DataWriter#create
DataWriter#destroy.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc">[CREATE|UPDATE|DESTROY]</div></li><li><code>params</code> : Object<div class="sub-desc">The params-hash to write-to</div></li><li><code>rs</code> : Record/Record[]<div class="sub-desc">The recordset write.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataWriter.html#write" ext:member="#write" ext:cls="Ext.data.DataWriter">DataWriter</a></td></tr></tbody></table><a id="Ext.data.JsonWriter-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>