<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.SplitBar.AbsoluteLayoutAdapter-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.SplitBar.AbsoluteLayoutAdapter-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.SplitBar.AbsoluteLayoutAdapter-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.SplitBar.AbsoluteLayoutAdapter"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.SplitBar.BasicLayoutAdapter.html" ext:member="" ext:cls="Ext.SplitBar.BasicLayoutAdapter">SplitBar.BasicLayoutAdapter</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">SplitBar.AbsoluteLayoutAdapter</pre></div><h1>Class <a href="source/SplitBar.html#cls-Ext.SplitBar.AbsoluteLayoutAdapter">Ext.SplitBar.AbsoluteLayoutAdapter</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">SplitBar.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/SplitBar.html#cls-Ext.SplitBar.AbsoluteLayoutAdapter">SplitBar.AbsoluteLayoutAdapter</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.SplitBar.BasicLayoutAdapter.html" ext:cls="Ext.SplitBar.BasicLayoutAdapter" ext:member="">SplitBar.BasicLayoutAdapter</a></td></tr></table><div class="description">Adapter that  moves the splitter element to align with the resized sizing element. 
Used with an absolute positioned SplitBar.</div><div class="hr"></div><a id="Ext.SplitBar.AbsoluteLayoutAdapter-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.AbsoluteLayoutAdapter-BOTTOM"></a><b><a href="source/SplitBar.html#prop-Ext.SplitBar.AbsoluteLayoutAdapter-BOTTOM">BOTTOM</a></b> : Number<div class="mdesc">Placement constant - The resizing element is positioned under splitter element</div></td><td class="msource">SplitBar.AbsoluteLayoutAdapter</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.AbsoluteLayoutAdapter-HORIZONTAL"></a><b><a href="source/SplitBar.html#prop-Ext.SplitBar.AbsoluteLayoutAdapter-HORIZONTAL">HORIZONTAL</a></b> : Number<div class="mdesc">Orientation constant - Create a horizontal SplitBar</div></td><td class="msource">SplitBar.AbsoluteLayoutAdapter</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.AbsoluteLayoutAdapter-LEFT"></a><b><a href="source/SplitBar.html#prop-Ext.SplitBar.AbsoluteLayoutAdapter-LEFT">LEFT</a></b> : Number<div class="mdesc">Placement constant - The resizing element is to the left of the splitter element</div></td><td class="msource">SplitBar.AbsoluteLayoutAdapter</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.AbsoluteLayoutAdapter-RIGHT"></a><b><a href="source/SplitBar.html#prop-Ext.SplitBar.AbsoluteLayoutAdapter-RIGHT">RIGHT</a></b> : Number<div class="mdesc">Placement constant - The resizing element is to the right of the splitter element</div></td><td class="msource">SplitBar.AbsoluteLayoutAdapter</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.AbsoluteLayoutAdapter-TOP"></a><b><a href="source/SplitBar.html#prop-Ext.SplitBar.AbsoluteLayoutAdapter-TOP">TOP</a></b> : Number<div class="mdesc">Placement constant - The resizing element is positioned above the splitter element</div></td><td class="msource">SplitBar.AbsoluteLayoutAdapter</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.AbsoluteLayoutAdapter-VERTICAL"></a><b><a href="source/SplitBar.html#prop-Ext.SplitBar.AbsoluteLayoutAdapter-VERTICAL">VERTICAL</a></b> : Number<div class="mdesc">Orientation constant - Create a vertical SplitBar</div></td><td class="msource">SplitBar.AbsoluteLayoutAdapter</td></tr></tbody></table><a id="Ext.SplitBar.AbsoluteLayoutAdapter-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.BasicLayoutAdapter-getElementSize"></a><b><a href="source/SplitBar.html#method-Ext.SplitBar.BasicLayoutAdapter-getElementSize">getElementSize</a></b>(&nbsp;<code>Ext.SplitBar&nbsp;s</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Called before drag operations to get the current size of the resizing element.</div><div class="long">Called before drag operations to get the current size of the resizing element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Ext.SplitBar<div class="sub-desc">The SplitBar using this adapter</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.SplitBar.BasicLayoutAdapter.html#getElementSize" ext:member="#getElementSize" ext:cls="Ext.SplitBar.BasicLayoutAdapter">SplitBar.BasicLayoutAdapter</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.SplitBar.BasicLayoutAdapter-setElementSize"></a><b><a href="source/SplitBar.html#method-Ext.SplitBar.BasicLayoutAdapter-setElementSize">setElementSize</a></b>(&nbsp;<code>Ext.SplitBar&nbsp;s</code>,&nbsp;<code>Number&nbsp;newSize</code>,&nbsp;<code>Function&nbsp;onComplete</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Called after drag operations to set the size of the resizing element.</div><div class="long">Called after drag operations to set the size of the resizing element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Ext.SplitBar<div class="sub-desc">The SplitBar using this adapter</div></li><li><code>newSize</code> : Number<div class="sub-desc">The new size to set</div></li><li><code>onComplete</code> : Function<div class="sub-desc">A function to be invoked when resizing is complete</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.SplitBar.BasicLayoutAdapter.html#setElementSize" ext:member="#setElementSize" ext:cls="Ext.SplitBar.BasicLayoutAdapter">SplitBar.BasicLayoutAdapter</a></td></tr></tbody></table><a id="Ext.SplitBar.AbsoluteLayoutAdapter-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>