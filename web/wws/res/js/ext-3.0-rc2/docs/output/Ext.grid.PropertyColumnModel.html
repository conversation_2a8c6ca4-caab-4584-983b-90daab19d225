<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.PropertyColumnModel-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.PropertyColumnModel-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.PropertyColumnModel-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.grid.PropertyColumnModel-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.PropertyColumnModel"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.grid.ColumnModel.html" ext:member="" ext:cls="Ext.grid.ColumnModel">ColumnModel</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">PropertyColumnModel</pre></div><h1>Class <a href="source/PropertyGrid.html#cls-Ext.grid.PropertyColumnModel">Ext.grid.PropertyColumnModel</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">PropertyGrid.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/PropertyGrid.html#cls-Ext.grid.PropertyColumnModel">PropertyColumnModel</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel" ext:member="">ColumnModel</a></td></tr></table><div class="description">A custom column model for the <a href="output/Ext.grid.PropertyGrid.html" ext:cls="Ext.grid.PropertyGrid">Ext.grid.PropertyGrid</a>.  Generally it should not need to be used directly.</div><div class="hr"></div><a id="Ext.grid.PropertyColumnModel-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-columns"></a><b><a href="source/ColumnModel.html#cfg-Ext.grid.ColumnModel-columns">columns</a></b> : Array<div class="mdesc"><div class="short">An Array of object literals.  The config options defined by
Ext.grid.Column are the options which may appear in the o...</div><div class="long">An Array of object literals.  The config options defined by
<b><a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a></b> are the options which may appear in the object literal for each
individual column definition.</div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#columns" ext:member="#columns" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-defaultSortable"></a><b><a href="source/ColumnModel.html#cfg-Ext.grid.ColumnModel-defaultSortable">defaultSortable</a></b> : Boolean<div class="mdesc"><div class="short">Default sortable of columns which have no
sortable specified (defaults to false).  This property shall preferably be ...</div><div class="long">Default sortable of columns which have no
sortable specified (defaults to <tt>false</tt>).  This property shall preferably be configured
through the <tt><b><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-defaults" ext:member="defaults" ext:cls="Ext.grid.ColumnModel">defaults</a></b></tt> config property.</div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#defaultSortable" ext:member="#defaultSortable" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-defaultWidth"></a><b><a href="source/ColumnModel.html#cfg-Ext.grid.ColumnModel-defaultWidth">defaultWidth</a></b> : Number<div class="mdesc"><div class="short">The width of columns which have no width
specified (defaults to 100).  This property shall preferably be configured t...</div><div class="long">The width of columns which have no <tt><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-width" ext:member="width" ext:cls="Ext.grid.ColumnModel">width</a></tt>
specified (defaults to <tt>100</tt>).  This property shall preferably be configured through the
<tt><b><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-defaults" ext:member="defaults" ext:cls="Ext.grid.ColumnModel">defaults</a></b></tt> config property.</div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#defaultWidth" ext:member="#defaultWidth" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-defaults"></a><b><a href="source/ColumnModel.html#cfg-Ext.grid.ColumnModel-defaults">defaults</a></b> : Object<div class="mdesc"><div class="short">Object literal which will be used to apply Ext.grid.Column
configuration options to all columns.  Configuration optio...</div><div class="long">Object literal which will be used to apply <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a>
configuration options to all <tt><b><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-columns" ext:member="columns" ext:cls="Ext.grid.ColumnModel">columns</a></b></tt>.  Configuration options specified with
individual <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">column</a> configs will supersede these <tt><b><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-defaults" ext:member="defaults" ext:cls="Ext.grid.ColumnModel">defaults</a></b></tt>.</div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#defaults" ext:member="#defaults" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.grid.PropertyColumnModel-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-config"></a><b><a href="source/ColumnModel.html#prop-Ext.grid.ColumnModel-config">config</a></b> : Array<div class="mdesc"><div class="short">An Array of Column definition objects representing the configuration
of this ColumnModel.  See Ext.grid.Column for th...</div><div class="long">An Array of <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Column definition</a> objects representing the configuration
of this ColumnModel.  See <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a> for the configuration properties that may
be specified.</div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#config" ext:member="#config" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr></tbody></table><a id="Ext.grid.PropertyColumnModel-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.PropertyColumnModel-PropertyColumnModel"></a><b><a href="source/PropertyGrid.html#cls-Ext.grid.PropertyColumnModel">PropertyColumnModel</a></b>(&nbsp;<code>Ext.grid.Grid&nbsp;grid</code>,&nbsp;<code>Object&nbsp;source</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>grid</code> : Ext.grid.Grid<div class="sub-desc">The grid this store will be bound to</div></li><li><code>source</code> : Object<div class="sub-desc">The source data config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PropertyColumnModel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-destroy"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys this the column model by purging any event listeners, and removing any editors.</div><div class="long">Destroys this the column model by purging any event listeners, and removing any editors.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#destroy" ext:member="#destroy" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-findColumnIndex"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-findColumnIndex">findColumnIndex</a></b>(&nbsp;<code>String&nbsp;col</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Finds the index of the first matching column for the given dataIndex.</div><div class="long">Finds the index of the first matching column for the given dataIndex.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : String<div class="sub-desc">The dataIndex to find</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The column index, or -1 if no match was found</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#findColumnIndex" ext:member="#findColumnIndex" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getCellEditor"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getCellEditor">getCellEditor</a></b>(&nbsp;<code>Number&nbsp;colIndex</code>,&nbsp;<code>Number&nbsp;rowIndex</code>&nbsp;)
    :
                                        Ext.Editor<div class="mdesc"><div class="short">Returns the editor defined for the cell/column.</div><div class="long">Returns the editor defined for the cell/column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>colIndex</code> : Number<div class="sub-desc">The column index</div></li><li><code>rowIndex</code> : Number<div class="sub-desc">The row index</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Editor</code><div class="sub-desc">The {@link Ext.Editor Editor} that was created to wrap
the {@link Ext.form.Field Field} used to edit the cell.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getCellEditor" ext:member="#getCellEditor" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnById"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnById">getColumnById</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the column for a specified id.</div><div class="long">Returns the column for a specified id.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The column id</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">the column</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnById" ext:member="#getColumnById" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnCount"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnCount">getColumnCount</a></b>(&nbsp;<code>Boolean&nbsp;visibleOnly</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the number of columns.</div><div class="long">Returns the number of columns.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>visibleOnly</code> : Boolean<div class="sub-desc">Optional. Pass as true to only include visible columns.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnCount" ext:member="#getColumnCount" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnHeader"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnHeader">getColumnHeader</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the header for the specified column.</div><div class="long">Returns the header for the specified column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnHeader" ext:member="#getColumnHeader" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnId"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnId">getColumnId</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the id of the column at the specified index.</div><div class="long">Returns the id of the column at the specified index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">the id</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnId" ext:member="#getColumnId" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnTooltip"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnTooltip">getColumnTooltip</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the tooltip for the specified column.</div><div class="long">Returns the tooltip for the specified column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnTooltip" ext:member="#getColumnTooltip" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnWidth"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnWidth">getColumnWidth</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the width for the specified column.</div><div class="long">Returns the width for the specified column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnWidth" ext:member="#getColumnWidth" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getColumnsBy"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getColumnsBy">getColumnsBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Returns the column configs that return true by the passed function that is called
with (columnConfig, index)
// retur...</div><div class="long">Returns the column configs that return true by the passed function that is called
with (columnConfig, index)
<pre><code><i>// returns an array of column config objects <b>for</b> all hidden columns</i>
<b>var</b> columns = grid.getColumnModel().getColumnsBy(<b>function</b>(c){
  <b>return</b> c.hidden;
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">result</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getColumnsBy" ext:member="#getColumnsBy" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getDataIndex"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getDataIndex">getDataIndex</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the dataIndex for the specified column.
// Get field name for the column
var fieldName = grid.getColumnModel(...</div><div class="long">Returns the dataIndex for the specified column.
<pre><code><i>// Get field name <b>for</b> the column</i>
<b>var</b> fieldName = grid.getColumnModel().getDataIndex(columnIndex);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The column's dataIndex</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getDataIndex" ext:member="#getDataIndex" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getIndexById"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getIndexById">getIndexById</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the index for a specified column id.</div><div class="long">Returns the index for a specified column id.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The column id</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">the index, or -1 if not found</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getIndexById" ext:member="#getIndexById" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getRenderer"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getRenderer">getRenderer</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        Function<div class="mdesc"><div class="short">Returns the rendering (formatting) function defined for the column.</div><div class="long">Returns the rendering (formatting) function defined for the column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index.</div></li></ul><strong>Returns:</strong><ul><li><code>Function</code><div class="sub-desc">The function used to render the cell. See {@link #setRenderer}.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getRenderer" ext:member="#getRenderer" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-getTotalWidth"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-getTotalWidth">getTotalWidth</a></b>(&nbsp;<code>Boolean&nbsp;includeHidden</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the total width of all columns.</div><div class="long">Returns the total width of all columns.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>includeHidden</code> : Boolean<div class="sub-desc">True to include hidden column widths</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#getTotalWidth" ext:member="#getTotalWidth" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-isCellEditable"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-isCellEditable">isCellEditable</a></b>(&nbsp;<code>Number&nbsp;colIndex</code>,&nbsp;<code>Number&nbsp;rowIndex</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the cell is editable.
var store = new Ext.data.Store({...});
var colModel = new Ext.grid.ColumnModel(...</div><div class="long">Returns true if the cell is editable.
<pre><code><b>var</b> store = <b>new</b> Ext.data.Store({...});
<b>var</b> colModel = <b>new</b> Ext.grid.ColumnModel({
  columns: [...],
  isCellEditable: <b>function</b>(col, row) {
    <b>var</b> record = store.getAt(row);
    <b>if</b> (record.get(<em>'readonly'</em>)) { <i>// replace <b>with</b> your condition</i>
      <b>return</b> false;
    }
    <b>return</b> Ext.grid.ColumnModel.prototype.isCellEditable.call(this, col, row);
  }
});
<b>var</b> grid = <b>new</b> Ext.grid.GridPanel({
  store: store,
  colModel: colModel,
  ...
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>colIndex</code> : Number<div class="sub-desc">The column index</div></li><li><code>rowIndex</code> : Number<div class="sub-desc">The row index</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#isCellEditable" ext:member="#isCellEditable" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-isFixed"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-isFixed">isFixed</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns true if the column width cannot be changed</div><div class="long">Returns true if the column width cannot be changed<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#isFixed" ext:member="#isFixed" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-isHidden"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-isHidden">isHidden</a></b>(&nbsp;<code>Number&nbsp;colIndex</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the column is hidden.</div><div class="long">Returns true if the column is hidden.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>colIndex</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#isHidden" ext:member="#isHidden" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-isMenuDisabled"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-isMenuDisabled">isMenuDisabled</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the specified column menu is disabled.</div><div class="long">Returns true if the specified column menu is disabled.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#isMenuDisabled" ext:member="#isMenuDisabled" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-isResizable"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-isResizable">isResizable</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the column can be resized</div><div class="long">Returns true if the column can be resized<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#isResizable" ext:member="#isResizable" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-isSortable"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-isSortable">isSortable</a></b>(&nbsp;<code>Number&nbsp;col</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the specified column is sortable.</div><div class="long">Returns true if the specified column is sortable.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#isSortable" ext:member="#isSortable" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-moveColumn"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-moveColumn">moveColumn</a></b>(&nbsp;<code>Number&nbsp;oldIndex</code>,&nbsp;<code>Number&nbsp;newIndex</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Moves a column from one position to another.</div><div class="long">Moves a column from one position to another.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>oldIndex</code> : Number<div class="sub-desc">The index of the column to move.</div></li><li><code>newIndex</code> : Number<div class="sub-desc">The position at which to reinsert the coolumn.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#moveColumn" ext:member="#moveColumn" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setColumnHeader"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setColumnHeader">setColumnHeader</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>String&nbsp;header</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the header for a column.</div><div class="long">Sets the header for a column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>header</code> : String<div class="sub-desc">The new header</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setColumnHeader" ext:member="#setColumnHeader" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setColumnTooltip"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setColumnTooltip">setColumnTooltip</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>String&nbsp;tooltip</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the tooltip for a column.</div><div class="long">Sets the tooltip for a column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>tooltip</code> : String<div class="sub-desc">The new tooltip</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setColumnTooltip" ext:member="#setColumnTooltip" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setColumnWidth"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setColumnWidth">setColumnWidth</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>Number&nbsp;width</code>,&nbsp;<code>Boolean&nbsp;suppressEvent</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the width for a column.</div><div class="long">Sets the width for a column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>width</code> : Number<div class="sub-desc">The new width</div></li><li><code>suppressEvent</code> : Boolean<div class="sub-desc">True to suppress firing the <code><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-widthchange" ext:member="widthchange" ext:cls="Ext.grid.ColumnModel">widthchange</a></code>
event. Defaults to false.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setColumnWidth" ext:member="#setColumnWidth" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setConfig"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setConfig">setConfig</a></b>(&nbsp;<code>Array&nbsp;config</code>,&nbsp;<code>Boolean&nbsp;initial</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Reconfigures this column model according to the passed Array of column definition objects. 
For a description of the ...</div><div class="long"><p>Reconfigures this column model according to the passed Array of column definition objects. 
For a description of the individual properties of a column definition object, see the 
<a href="#Ext.grid.ColumnModel-configs">Config Options</a>.</p>
<p>Causes the <a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-configchange" ext:member="configchange" ext:cls="Ext.grid.ColumnModel">configchange</a> event to be fired. A <a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">GridPanel</a> 
using this ColumnModel will listen for this event and refresh its UI automatically.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Array<div class="sub-desc">Array of Column definition objects.</div></li><li><code>initial</code> : Boolean<div class="sub-desc">Specify <tt>true</tt> to bypass cleanup which deletes the <tt>totalWidth</tt>
and destroys existing editors.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setConfig" ext:member="#setConfig" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setDataIndex"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setDataIndex">setDataIndex</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>String&nbsp;dataIndex</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the dataIndex for a column.</div><div class="long">Sets the dataIndex for a column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>dataIndex</code> : String<div class="sub-desc">The new dataIndex</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setDataIndex" ext:member="#setDataIndex" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setEditable"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setEditable">setEditable</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>Boolean&nbsp;editable</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets if a column is editable.</div><div class="long">Sets if a column is editable.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>editable</code> : Boolean<div class="sub-desc">True if the column is editable</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setEditable" ext:member="#setEditable" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setEditor"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setEditor">setEditor</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>Object&nbsp;editor</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the editor for a column and destroys the prior editor.</div><div class="long">Sets the editor for a column and destroys the prior editor.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>editor</code> : Object<div class="sub-desc">The editor object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setEditor" ext:member="#setEditor" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setHidden"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setHidden">setHidden</a></b>(&nbsp;<code>Number&nbsp;colIndex</code>,&nbsp;<code>Boolean&nbsp;hidden</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets if a column is hidden.
myGrid.getColumnModel().setHidden(0, true); // hide column 0 (0 = the first column).</div><div class="long">Sets if a column is hidden.
<pre><code>myGrid.getColumnModel().setHidden(0, true); <i>// hide column 0 (0 = the first column).</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>colIndex</code> : Number<div class="sub-desc">The column index</div></li><li><code>hidden</code> : Boolean<div class="sub-desc">True if the column is hidden</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setHidden" ext:member="#setHidden" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-setRenderer"></a><b><a href="source/ColumnModel.html#method-Ext.grid.ColumnModel-setRenderer">setRenderer</a></b>(&nbsp;<code>Number&nbsp;col</code>,&nbsp;<code>Function&nbsp;fn</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the rendering (formatting) function for a column.  See Ext.util.Format for some
default formatting functions.</div><div class="long">Sets the rendering (formatting) function for a column.  See <a href="output/Ext.util.Format.html" ext:cls="Ext.util.Format">Ext.util.Format</a> for some
default formatting functions.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>col</code> : Number<div class="sub-desc">The column index</div></li><li><code>fn</code> : Function<div class="sub-desc">The function to use to process the cell's raw data
to return HTML markup for the grid view. The render function is called with
the following parameters:<ul>
<li><b>value</b> : Object<p class="sub-desc">The data value for the cell.</p></li>
<li><b>metadata</b> : Object<p class="sub-desc">An object in which you may set the following attributes:<ul>
<li><b>css</b> : String<p class="sub-desc">A CSS class name to add to the cell's TD element.</p></li>
<li><b>attr</b> : String<p class="sub-desc">An HTML attribute definition string to apply to the data container element <i>within</i> the table cell
(e.g. 'style="color:red;"').</p></li></ul></p></li>
<li><b>record</b> : Ext.data.record<p class="sub-desc">The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> from which the data was extracted.</p></li>
<li><b>rowIndex</b> : Number<p class="sub-desc">Row index</p></li>
<li><b>colIndex</b> : Number<p class="sub-desc">Column index</p></li>
<li><b>store</b> : Ext.data.Store<p class="sub-desc">The <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> object from which the Record was extracted.</p></li></ul></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#setRenderer" ext:member="#setRenderer" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.grid.PropertyColumnModel-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-columnmoved"></a><b><a href="source/ColumnModel.html#event-Ext.grid.ColumnModel-columnmoved">columnmoved</a></b> :
                                      (&nbsp;<code>ColumnModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;oldIndex</code>,&nbsp;<code>Number&nbsp;newIndex</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a column is moved.</div><div class="long">Fires when a column is moved.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : ColumnModel<div class="sub-desc"></div></li><li><code>oldIndex</code> : Number<div class="sub-desc"></div></li><li><code>newIndex</code> : Number<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#columnmoved" ext:member="#columnmoved" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-configchange"></a><b><a href="source/ColumnModel.html#event-Ext.grid.ColumnModel-configchange">configchange</a></b> :
                                      (&nbsp;<code>ColumnModel&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the configuration is changed</div><div class="long">Fires when the configuration is changed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : ColumnModel<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#configchange" ext:member="#configchange" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-headerchange"></a><b><a href="source/ColumnModel.html#event-Ext.grid.ColumnModel-headerchange">headerchange</a></b> :
                                      (&nbsp;<code>ColumnModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>String&nbsp;newText</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the text of a header changes.</div><div class="long">Fires when the text of a header changes.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : ColumnModel<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc">The column index</div></li><li><code>newText</code> : String<div class="sub-desc">The new header text</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#headerchange" ext:member="#headerchange" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-hiddenchange"></a><b><a href="source/ColumnModel.html#event-Ext.grid.ColumnModel-hiddenchange">hiddenchange</a></b> :
                                      (&nbsp;<code>ColumnModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Boolean&nbsp;hidden</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a column is hidden or "unhidden".</div><div class="long">Fires when a column is hidden or "unhidden".<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : ColumnModel<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc">The column index</div></li><li><code>hidden</code> : Boolean<div class="sub-desc">true if hidden, false otherwise</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#hiddenchange" ext:member="#hiddenchange" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.ColumnModel-widthchange"></a><b><a href="source/ColumnModel.html#event-Ext.grid.ColumnModel-widthchange">widthchange</a></b> :
                                      (&nbsp;<code>ColumnModel&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Number&nbsp;newWidth</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the width of a column is programmaticially changed using
setColumnWidth.
Note internal resizing suppresses...</div><div class="long">Fires when the width of a column is programmaticially changed using
<code><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-setColumnWidth" ext:member="setColumnWidth" ext:cls="Ext.grid.ColumnModel">setColumnWidth</a></code>.
Note internal resizing suppresses the event from firing. See also
<a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel</a>.<code><a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-columnresize" ext:member="columnresize" ext:cls="Ext.grid.ColumnModel">columnresize</a></code>.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : ColumnModel<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc">The column index</div></li><li><code>newWidth</code> : Number<div class="sub-desc">The new width</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.grid.ColumnModel.html#widthchange" ext:member="#widthchange" ext:cls="Ext.grid.ColumnModel">ColumnModel</a></td></tr></tbody></table></div>