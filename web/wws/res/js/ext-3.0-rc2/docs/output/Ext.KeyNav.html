<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.KeyNav-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.KeyNav-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.KeyNav-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.KeyNav-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.KeyNav"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/KeyNav.html#cls-Ext.KeyNav">Ext.KeyNav</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">KeyNav.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/KeyNav.html#cls-Ext.KeyNav">KeyNav</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>Provides a convenient wrapper for normalized keyboard navigation.  KeyNav allows you to bind
navigation keys to function calls that will get called when the keys are pressed, providing an easy
way to implement custom navigation schemes for any UI component.</p>
<p>The following are all of the possible keys that can be implemented: enter, left, right, up, down, tab, esc,
pageUp, pageDown, del, home, end.  Usage:</p>
 <pre><code><b>var</b> nav = <b>new</b> Ext.KeyNav(<em>"my-element"</em>, {
    <em>"left"</em> : <b>function</b>(e){
        this.moveLeft(e.ctrlKey);
    },
    <em>"right"</em> : <b>function</b>(e){
        this.moveRight(e.ctrlKey);
    },
    <em>"enter"</em> : <b>function</b>(e){
        this.save();
    },
    scope : this
});</code></pre></div><div class="hr"></div><a id="Ext.KeyNav-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-defaultEventAction"></a><b><a href="source/KeyNav.html#cfg-Ext.KeyNav-defaultEventAction">defaultEventAction</a></b> : String<div class="mdesc"><div class="short">The method to call on the Ext.EventObject after this KeyNav intercepts a key.  Valid values are
Ext.EventObject.stopE...</div><div class="long">The method to call on the <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> after this KeyNav intercepts a key.  Valid values are
<a href="output/Ext.EventObject.html#Ext.EventObject-stopEvent" ext:member="stopEvent" ext:cls="Ext.EventObject">Ext.EventObject.stopEvent</a>, <a href="output/Ext.EventObject.html#Ext.EventObject-preventDefault" ext:member="preventDefault" ext:cls="Ext.EventObject">Ext.EventObject.preventDefault</a> and
<a href="output/Ext.EventObject.html#Ext.EventObject-stopPropagation" ext:member="stopPropagation" ext:cls="Ext.EventObject">Ext.EventObject.stopPropagation</a> (defaults to 'stopEvent')</div></div></td><td class="msource">KeyNav</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-disabled"></a><b><a href="source/KeyNav.html#cfg-Ext.KeyNav-disabled">disabled</a></b> : Boolean<div class="mdesc">True to disable this KeyNav instance (defaults to false)</div></td><td class="msource">KeyNav</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-forceKeyDown"></a><b><a href="source/KeyNav.html#cfg-Ext.KeyNav-forceKeyDown">forceKeyDown</a></b> : Boolean<div class="mdesc"><div class="short">Handle the keydown event instead of keypress (defaults to false).  KeyNav automatically does this for IE since
IE doe...</div><div class="long">Handle the keydown event instead of keypress (defaults to false).  KeyNav automatically does this for IE since
IE does not propagate special keys on keypress, but setting this to true will force other browsers to also
handle keydown instead of keypress.</div></div></td><td class="msource">KeyNav</td></tr></tbody></table><a id="Ext.KeyNav-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.KeyNav-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-KeyNav"></a><b><a href="source/KeyNav.html#cls-Ext.KeyNav">KeyNav</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element to bind to</div></li><li><code>config</code> : Object<div class="sub-desc">The config</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">KeyNav</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-disable"></a><b><a href="source/KeyNav.html#method-Ext.KeyNav-disable">disable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Disable this KeyNav</div><div class="long">Disable this KeyNav<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">KeyNav</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-enable"></a><b><a href="source/KeyNav.html#method-Ext.KeyNav-enable">enable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Enable this KeyNav</div><div class="long">Enable this KeyNav<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">KeyNav</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.KeyNav-setDisabled"></a><b><a href="source/KeyNav.html#method-Ext.KeyNav-setDisabled">setDisabled</a></b>(&nbsp;<code>Boolean&nbsp;disabled</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Convenience function for setting disabled/enabled by boolean.</div><div class="long">Convenience function for setting disabled/enabled by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>disabled</code> : Boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">KeyNav</td></tr></tbody></table><a id="Ext.KeyNav-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>