<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.Column-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.Column-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.Column-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.grid.Column-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.Column"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Column.html#cls-Ext.grid.Column">Ext.grid.Column</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Column.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Column.html#cls-Ext.grid.Column">Column</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.grid.BooleanColumn.html" ext:cls="Ext.grid.BooleanColumn">BooleanColumn</a>,&#13;<a href="output/Ext.grid.DateColumn.html" ext:cls="Ext.grid.DateColumn">DateColumn</a>,&#13;<a href="output/Ext.grid.NumberColumn.html" ext:cls="Ext.grid.NumberColumn">NumberColumn</a>,&#13;<a href="output/Ext.grid.TemplateColumn.html" ext:cls="Ext.grid.TemplateColumn">TemplateColumn</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>This class encapsulates column configuration data to be used in the initialization of a 
<a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">ColumnModel</a>.</p>
<p>While subclasses are provided to render data in different ways, this class renders a passed
data field unchanged and is usually used for textual columns.</p></div><div class="hr"></div><a id="Ext.grid.Column-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-align"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-align">align</a></b> : String<div class="mdesc">Set the CSS text-align property of the column.  Defaults to undefined.</div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-css"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-css">css</a></b> : String<div class="mdesc"><div class="short">An inline style definition string which is applied to all table cells in the column&#13;
(excluding headers). Defaults to...</div><div class="long">An inline style definition string which is applied to all table cells in the column
(excluding headers). Defaults to undefined.</div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-dataIndex"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-dataIndex">dataIndex</a></b> : String<div class="mdesc"><div class="short">The name of the field in the grid's Ext.data.Store's&#13;
Ext.data.Record definition from which to draw the column's valu...</div><div class="long">The name of the field in the grid's <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>'s
<a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> definition from which to draw the column's value. If not
specified, the column's index is used as an index into the Record's data Array.</div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-editable"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-editable">editable</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to true, enabling the configured&#13;
editor.  Set to false to initially disable editing on this column.&#13;
The in...</div><div class="long">Defaults to <tt>true</tt>, enabling the configured
<tt><a href="output/Ext.grid.Column.html#Ext.grid.Column-editor" ext:member="editor" ext:cls="Ext.grid.Column">editor</a></tt>.  Set to <tt>false</tt> to initially disable editing on this column.
The initial configuration may be dynamically altered using
<a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">Ext.grid.ColumnModel</a>.<a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-setEditable" ext:member="setEditable" ext:cls="Ext.grid.ColumnModel">setEditable()</a>.</div></div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-editor"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-editor">editor</a></b> : Ext.form.Field<div class="mdesc">The <a href="output/Ext.form.Field.html" ext:cls="Ext.form.Field">Ext.form.Field</a> to use when editing values in this column
if editing is supported by the grid. See <tt><a href="output/Ext.grid.Column.html#Ext.grid.Column-editable" ext:member="editable" ext:cls="Ext.grid.Column">editable</a></tt> also.</div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-fixed"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-fixed">fixed</a></b> : Boolean<div class="mdesc"><tt>true</tt> if the column width cannot be changed.  Defaults to <tt>false</tt>.</div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-groupName"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-groupName">groupName</a></b> : String<div class="mdesc"><div class="short">If the grid is being rendered by an Ext.grid.GroupingView, this option&#13;
may be used to specify the text with which to...</div><div class="long">If the grid is being rendered by an <a href="output/Ext.grid.GroupingView.html" ext:cls="Ext.grid.GroupingView">Ext.grid.GroupingView</a>, this option
may be used to specify the text with which to prefix the group field value in the group header line.
See also <a href="output/Ext.grid.Column.html#Ext.grid.Column-groupRenderer" ext:member="groupRenderer" ext:cls="Ext.grid.Column">groupRenderer</a> and
<a href="output/Ext.grid.GroupingView.html" ext:cls="Ext.grid.GroupingView">Ext.grid.GroupingView</a>.<a href="output/Ext.grid.GroupingView.html#Ext.grid.GroupingView-showGroupName" ext:member="showGroupName" ext:cls="Ext.grid.GroupingView">showGroupName</a>.</div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-groupRenderer"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-groupRenderer">groupRenderer</a></b> : Function<div class="mdesc"><div class="short">If the grid is being rendered by an Ext.grid.GroupingView, this option&#13;
may be used to specify the function used to f...</div><div class="long"><p>If the grid is being rendered by an <a href="output/Ext.grid.GroupingView.html" ext:cls="Ext.grid.GroupingView">Ext.grid.GroupingView</a>, this option
may be used to specify the function used to format the grouping field value for display in the group 
<a href="output/Ext.grid.Column.html#Ext.grid.Column-groupName" ext:member="groupName" ext:cls="Ext.grid.Column">header</a>.  If a <tt><b>groupRenderer</b></tt> is not specified, the configured
<tt><b><a href="output/Ext.grid.Column.html#Ext.grid.Column-renderer" ext:member="renderer" ext:cls="Ext.grid.Column">renderer</a></b></tt> will be called; if a <tt><b><a href="output/Ext.grid.Column.html#Ext.grid.Column-renderer" ext:member="renderer" ext:cls="Ext.grid.Column">renderer</a></b></tt> is also not specified
the new value of the group field will be used.</p>
<p>The called function (either the <tt><b>groupRenderer</b></tt> or <tt><b><a href="output/Ext.grid.Column.html#Ext.grid.Column-renderer" ext:member="renderer" ext:cls="Ext.grid.Column">renderer</a></b></tt>) will be
passed the following parameters:
<div class="mdetail-params"><ul>
<li><b>v</b> : Object<p class="sub-desc">The new value of the group field.</p></li>
<li><b>unused</b> : undefined<p class="sub-desc">Unused parameter.</p></li>
<li><b>r</b> : Ext.data.Record<p class="sub-desc">The Record providing the data
for the row which caused group change.</p></li>
<li><b>rowIndex</b> : Number<p class="sub-desc">The row index of the Record which caused group change.</p></li>
<li><b>colIndex</b> : Number<p class="sub-desc">The column index of the group field.</p></li>
<li><b>ds</b> : Ext.data.Store<p class="sub-desc">The Store which is providing the data Model.</p></li>
</ul></div></p>
<p>The function should return a string value.</p></div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-groupable"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-groupable">groupable</a></b> : Boolean<div class="mdesc"><div class="short">If the grid is being rendered by an Ext.grid.GroupingView, this option&#13;
may be used to disable the header menu item t...</div><div class="long">If the grid is being rendered by an <a href="output/Ext.grid.GroupingView.html" ext:cls="Ext.grid.GroupingView">Ext.grid.GroupingView</a>, this option
may be used to disable the header menu item to group by the column selected. Defaults to <tt>true</tt>,
which enables the header menu group option.  Set to <tt>false</tt> to disable (but still show) the
group option in the header menu for the column. See also <code><a href="output/Ext.grid.Column.html#Ext.grid.Column-groupName" ext:member="groupName" ext:cls="Ext.grid.Column">groupName</a></code>.</div></div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-header"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-header">header</a></b> : String<div class="mdesc">The header text to be used as innerHTML (html tags are accepted)
to display in the Grid view.</div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-hidden"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-hidden">hidden</a></b> : Boolean<div class="mdesc"><tt>true</tt> to hide the column. Defaults to <tt>false</tt>.</div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-hideable"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-hideable">hideable</a></b> : Boolean<div class="mdesc"><div class="short">Specify as false to prevent the user from hiding this column&#13;
(defaults to true).  To disallow column hiding globally...</div><div class="long">Specify as <tt>false</tt> to prevent the user from hiding this column
(defaults to true).  To disallow column hiding globally for all columns in the grid, use
<a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-enableColumnHide" ext:member="enableColumnHide" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel.enableColumnHide</a> instead.</div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-id"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-id">id</a></b> : String<div class="mdesc"><div class="short">A name which identifies this column (defaults to the column's initial&#13;
ordinal position.) The id is used to create a ...</div><div class="long">A name which identifies this column (defaults to the column's initial
ordinal position.) The <tt>id</tt> is used to create a CSS <b>class</b> name which is applied to all
table cells (including headers) in that column (in this context the <tt>id</tt> does not need to be
unique). The class name takes the form of <pre>x-grid3-td-<b>id</b></pre>
Header cells will also receive this class name, but will also have the class <pre>x-grid3-hd</pre>
So, to target header cells, use CSS selectors such as:<pre>.x-grid3-hd.x-grid3-td-<b>id</b></pre>
The <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandColumn" ext:member="autoExpandColumn" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel.autoExpandColumn</a> grid config option references the column via this
unique identifier.</div></div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-menuDisabled"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-menuDisabled">menuDisabled</a></b> : Boolean<div class="mdesc"><tt>true</tt> to disable the column menu. Defaults to <tt>false</tt>.</div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-renderer"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-renderer">renderer</a></b> : Mixed<div class="mdesc"><div class="short">For an alternative to specifying a renderer see xtype&#13;
(optional) A renderer is an "interceptor" method which can be ...</div><div class="long"><p>For an alternative to specifying a renderer see <code><a href="output/Ext.grid.Column.html#Ext.grid.Column-xtype" ext:member="xtype" ext:cls="Ext.grid.Column">xtype</a></code></p>
<p>(optional) A renderer is an "interceptor" method which can be used transform data (value,
appearance, etc.) before it is rendered). This may be specified in either of three ways:
<div class="mdetail-params"><ul>
<li>A renderer function used to return HTML markup for a cell given the cell's data value.</li>
<li>A string which references a property name of the <a href="output/Ext.util.Format.html" ext:cls="Ext.util.Format">Ext.util.Format</a> class which
provides a renderer function.</li>
<li>An object specifying both the renderer function, and its execution scope (<tt><b>this</b></tt>
reference) eg:<pre style="margin-left:1.2em"><code>{
    fn: this.gridRenderer,
    scope: this
}</code></pre></li></ul></div>
If not specified, the default renderer uses the raw data value.</p>
<p>For information about the renderer function (passed parameters, etc.), see
<a href="output/Ext.grid.ColumnModel.html#Ext.grid.ColumnModel-setRenderer" ext:member="setRenderer" ext:cls="Ext.grid.ColumnModel">Ext.grid.ColumnModel.setRenderer</a>. An example of specifying renderer function inline:</p><pre><code><b>var</b> companyColumn = {
   header: <em>"Company Name"</em>,
   dataIndex: <em>"company"</em>,
   renderer: <b>function</b>(value, metaData, record, rowIndex, colIndex, store) {
      <i>// provide the logic depending on business rules 
</i>
      <i>// name of your own choosing to manipulate the cell depending upon
</i>
      <i>// the data <b>in</b> the underlying Record object.
</i>
      <b>if</b> (value == <em>'whatever'</em>) {
          <i>//metaData.css : String : A CSS class name to add to the TD element of the cell.
</i>
          <i>//metaData.attr : String : An html attribute definition string to apply to
</i>
          <i>//                         the data container element within the table
</i>
          <i>//                         cell (e.g. <em>'style=<em>"color:red;"</em>'</em>).      
</i>
          metaData.css = <em>'name-of-css-class-you-will-define'</em>;
      }
      <b>return</b> value;
   }
}</code></pre>
See also <a href="output/Ext.grid.Column.html#Ext.grid.Column-scope" ext:member="scope" ext:cls="Ext.grid.Column">scope</a>.</div></div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-resizable"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-resizable">resizable</a></b> : Boolean<div class="mdesc"><tt>false</tt> to disable column resizing. Defaults to <tt>true</tt>.</div></td><td class="msource">Column</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-scope"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-scope">scope</a></b> : Object<div class="mdesc">The scope (<tt><b>this</b></tt> reference) in which to execute the
renderer.  Defaults to the Column configuration object.</div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-sortable"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-sortable">sortable</a></b> : Boolean<div class="mdesc"><div class="short">true if sorting is to be allowed on this column.&#13;
Defaults to the value of the defaultSortable property.&#13;
Whether loc...</div><div class="long"><tt>true</tt> if sorting is to be allowed on this column.
Defaults to the value of the <a href="output/Ext.grid.Column.html#Ext.grid.Column-defaultSortable" ext:member="defaultSortable" ext:cls="Ext.grid.Column">defaultSortable</a> property.
Whether local/remote sorting is used is specified in <a href="output/Ext.data.Store.html#Ext.data.Store-remoteSort" ext:member="remoteSort" ext:cls="Ext.data.Store">Ext.data.Store.remoteSort</a>.</div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-tooltip"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-tooltip">tooltip</a></b> : String<div class="mdesc"><div class="short">A text string to use as the column header's tooltip.  If Quicktips&#13;
are enabled, this value will be used as the text ...</div><div class="long">A text string to use as the column header's tooltip.  If Quicktips
are enabled, this value will be used as the text of the quick tip, otherwise it will be set as the
header's HTML title attribute. Defaults to ''.</div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-width"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-width">width</a></b> : Number<div class="mdesc"><div class="short">The initial width in pixels of the column.&#13;
The width of each column can also be affected if any of the following are...</div><div class="long">The initial width in pixels of the column.
The width of each column can also be affected if any of the following are configured:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel</a>.<tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandColumn" ext:member="autoExpandColumn" ext:cls="Ext.grid.GridPanel">autoExpandColumn</a></tt></li>
<li><a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">Ext.grid.GridView</a>.<tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-forceFit" ext:member="forceFit" ext:cls="Ext.grid.GridView">forceFit</a></tt>
<div class="sub-desc">
<p>By specifying <tt>forceFit:true</tt>, <a href="output/Ext.grid.Column.html#Ext.grid.Column-fixed" ext:member="fixed" ext:cls="Ext.grid.Column">non-fixed width</a> columns will be
re-proportioned (based on the relative initial widths) to fill the width of the grid so
that no horizontal scrollbar is shown.</p>
</div></li>
<li><a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">Ext.grid.GridView</a>.<tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-autoFill" ext:member="autoFill" ext:cls="Ext.grid.GridView">autoFill</a></tt></li>
<li><a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">Ext.grid.GridPanel</a>.<tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-minColumnWidth" ext:member="minColumnWidth" ext:cls="Ext.grid.GridPanel">minColumnWidth</a></tt></li>
<br><p><b>Note</b>: when the width of each column is determined, a space on the right side
is reserved for the vertical scrollbar.  The
<a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">Ext.grid.GridView</a>.<tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-scrollOffset" ext:member="scrollOffset" ext:cls="Ext.grid.GridView">scrollOffset</a></tt>
can be modified to reduce or eliminate the reserved offset.</p></div></div></td><td class="msource">Column</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-xtype"></a><b><a href="source/Column.html#cfg-Ext.grid.Column-xtype">xtype</a></b> : String<div class="mdesc"><div class="short">A String which references a predefined Ext.grid.Column subclass&#13;
type which is preconfigured with an appropriate rend...</div><div class="long">A String which references a predefined <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a> subclass
type which is preconfigured with an appropriate <code><a href="output/Ext.grid.Column.html#Ext.grid.Column-renderer" ext:member="renderer" ext:cls="Ext.grid.Column">renderer</a></code> to be easily
configured into a ColumnModel. The predefined <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a> subclass types are:
<div class="mdetail-params"><ul>
<li><b><tt>gridcolumn</tt></b> : <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a> (<b>Default</b>)<p class="sub-desc"></p></li>
<li><b><tt>booleancolumn</tt></b> : <a href="output/Ext.grid.BooleanColumn.html" ext:cls="Ext.grid.BooleanColumn">Ext.grid.BooleanColumn</a><p class="sub-desc"></p></li>
<li><b><tt>numbercolumn</tt></b> : <a href="output/Ext.grid.NumberColumn.html" ext:cls="Ext.grid.NumberColumn">Ext.grid.NumberColumn</a><p class="sub-desc"></p></li>
<li><b><tt>datecolumn</tt></b> : <a href="output/Ext.grid.DateColumn.html" ext:cls="Ext.grid.DateColumn">Ext.grid.DateColumn</a><p class="sub-desc"></p></li>
<li><b><tt>templatecolumn</tt></b> : <a href="output/Ext.grid.TemplateColumn.html" ext:cls="Ext.grid.TemplateColumn">Ext.grid.TemplateColumn</a><p class="sub-desc"></p></li>
</ul></div>
<p>Configuration properties for the specified <code>xtype</code> may be specified with
the Column configuration properties, for example:</p>
<pre><code><b>var</b> grid = <b>new</b> Ext.grid.GridPanel({
    ...
    columns: [{
        header: <em>"Last Updated"</em>,
        dataIndex: <em>'lastChange'</em>,
        width: 85,
        sortable: true,
        <i>//renderer: Ext.util.Format.dateRenderer(<em>'m/d/Y'</em>), 
</i>
        xtype: <em>'datecolumn'</em>, <i>// use xtype instead of renderer
</i>
        format: <em>'M/d/Y'</em> <i>// configuration property <b>for</b> <a href="output/Ext.grid.DateColumn.html" ext:cls="Ext.grid.DateColumn">Ext.grid.DateColumn</a> 
</i>
    }, {
        ...
    }]
});</code></pre></div></div></td><td class="msource">Column</td></tr></tbody></table><a id="Ext.grid.Column-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-renderer"></a><b><a href="source/Column.html#prop-Ext.grid.Column-renderer">renderer</a></b> : Function&#13;
A function which returns displayable data when passed the following parameters:&#13;
&lt;div class="mdetail-params"&gt;&lt;ul&gt;&#13;
&lt;li&gt;&lt;b&gt;value&lt;/b&gt; : Object&lt;p class="sub-desc"&gt;The data value for the cell.&lt;/p&gt;&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;metadata&lt;/b&gt; : Object&lt;p class="sub-desc"&gt;An object in which you may set the following attributes:&lt;ul&gt;&#13;
&lt;li&gt;&lt;b&gt;css&lt;/b&gt; : String&lt;p class="sub-desc"&gt;A CSS class name to add to the cell's TD element.&lt;/p&gt;&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;attr&lt;/b&gt; : String&lt;p class="sub-desc"&gt;An HTML attribute definition string to apply to the data container&#13;
element &lt;i&gt;within&lt;/i&gt; the table cell (e.g. 'style="color:red;"').&lt;/p&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/p&gt;&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;record&lt;/b&gt; : Ext.data.record&lt;p class="sub-desc"&gt;The {@link Ext.data.Record} from which the data was&#13;
extracted.&lt;/p&gt;&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;rowIndex&lt;/b&gt; : Number&lt;p class="sub-desc"&gt;Row index&lt;/p&gt;&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;colIndex&lt;/b&gt; : Number&lt;p class="sub-desc"&gt;Column index&lt;/p&gt;&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;store&lt;/b&gt; : Ext.data.Store&lt;p class="sub-desc"&gt;The {@link Ext.data.Store} object from which the Record&#13;
was extracted.&lt;/p&gt;&lt;/li&gt;&#13;
&lt;/ul&gt;&lt;/div&gt;<div class="mdesc"></div></td><td class="msource">Column</td></tr></tbody></table><a id="Ext.grid.Column-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.Column-getCellEditor"></a><b><a href="source/Column.html#method-Ext.grid.Column-getCellEditor">getCellEditor</a></b>(&nbsp;<code>Number&nbsp;rowIndex</code>&nbsp;)
    :
                                        Ext.Editor<div class="mdesc"><div class="short">Returns the editor defined for this column.</div><div class="long">Returns the editor defined for this column.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rowIndex</code> : Number<div class="sub-desc">The row index</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Editor</code><div class="sub-desc">The {@link Ext.Editor Editor} that was created to wrap the {@link Ext.form.Field Field}&#13;
used to edit the cell.</div></li></ul></div></div></div></td><td class="msource">Column</td></tr></tbody></table><a id="Ext.grid.Column-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>