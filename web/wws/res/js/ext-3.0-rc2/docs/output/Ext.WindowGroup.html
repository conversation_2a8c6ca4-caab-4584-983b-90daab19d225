<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.WindowGroup-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.WindowGroup-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.WindowGroup-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.WindowGroup"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/WindowManager.html#cls-Ext.WindowGroup">Ext.WindowGroup</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">WindowManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/WindowManager.html#cls-Ext.WindowGroup">WindowGroup</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.WindowMgr.html" ext:cls="Ext.WindowMgr">WindowMgr</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">An object that represents a group of <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a> instances and provides z-order management
and window activation behavior.</div><div class="hr"></div><a id="Ext.WindowGroup-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-zseed"></a><b><a href="source/WindowManager.html#prop-Ext.WindowGroup-zseed">zseed</a></b> : Number The z-index value<div class="mdesc">The starting z-index for windows (defaults to 9000)</div></td><td class="msource">WindowGroup</td></tr></tbody></table><a id="Ext.WindowGroup-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-WindowGroup"></a><b><a href="source/WindowManager.html#cls-Ext.WindowGroup">WindowGroup</a></b>()
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-bringToFront"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-bringToFront">bringToFront</a></b>(&nbsp;<code>String/Object&nbsp;win</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Brings the specified window to the front of any other active windows.</div><div class="long">Brings the specified window to the front of any other active windows.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>win</code> : String/Object<div class="sub-desc">The id of the window or a <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a> instance</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the dialog was brought to the front, else false
if it was already in front</div></li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-each"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-each">each</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes the specified function once for every window in the group, passing each
window as the only parameter. Return...</div><div class="long">Executes the specified function once for every window in the group, passing each
window as the only parameter. Returning false from the function will stop the iteration.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to execute for each item</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the function</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-get"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-get">get</a></b>(&nbsp;<code>String/Object&nbsp;id</code>&nbsp;)
    :
                                        Ext.Window<div class="mdesc"><div class="short">Gets a registered window by id.</div><div class="long">Gets a registered window by id.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String/Object<div class="sub-desc">The id of the window or a <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a> instance</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Window</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-getActive"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-getActive">getActive</a></b>()
    :
                                        Ext.Window<div class="mdesc"><div class="short">Gets the currently-active window in the group.</div><div class="long">Gets the currently-active window in the group.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Window</code><div class="sub-desc">The active window</div></li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-getBy"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-getBy">getBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Returns zero or more windows in the group using the custom search function passed to this method.
The function should...</div><div class="long">Returns zero or more windows in the group using the custom search function passed to this method.
The function should accept a single <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a> reference as its only argument and should
return true if the window matches the search criteria, otherwise it should return false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The search function</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the function (defaults to the window
that gets passed to the function if not specified)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">An array of zero or more matching windows</div></li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-hideAll"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-hideAll">hideAll</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides all windows in the group.</div><div class="long">Hides all windows in the group.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.WindowGroup-sendToBack"></a><b><a href="source/WindowManager.html#method-Ext.WindowGroup-sendToBack">sendToBack</a></b>(&nbsp;<code>String/Object&nbsp;win</code>&nbsp;)
    :
                                        Ext.Window<div class="mdesc"><div class="short">Sends the specified window to the back of other active windows.</div><div class="long">Sends the specified window to the back of other active windows.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>win</code> : String/Object<div class="sub-desc">The id of the window or a <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a> instance</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Window</code><div class="sub-desc">The window</div></li></ul></div></div></div></td><td class="msource">WindowGroup</td></tr></tbody></table><a id="Ext.WindowGroup-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>