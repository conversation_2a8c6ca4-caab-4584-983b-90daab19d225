<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.state.Manager-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.state.Manager-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.state.Manager-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.state.Manager"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/StateManager.html#cls-Ext.state.Manager">Ext.state.Manager</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.state</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">StateManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/StateManager.html#cls-Ext.state.Manager">Manager</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">This is the global state manager. By default all components that are "state aware" check this class
for state information if you don't pass them a custom state provider. In order for this class
to be useful, it must be initialized with a provider when your application initializes. Example usage:
 <pre><code><i>// <b>in</b> your initialization <b>function</b>
</i>
init : <b>function</b>(){
   Ext.state.Manager.setProvider(<b>new</b> Ext.state.CookieProvider());
   <b>var</b> win = <b>new</b> Window(...);
   win.restoreState();
}</code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.state.Manager-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.state.Manager-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Manager-clear"></a><b><a href="source/StateManager.html#method-Ext.state.Manager-clear">clear</a></b>(&nbsp;<code>String&nbsp;name</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Clears a value from the state</div><div class="long">Clears a value from the state<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Manager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Manager-get"></a><b><a href="source/StateManager.html#method-Ext.state.Manager-get">get</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;defaultValue</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Returns the current value for a key</div><div class="long">Returns the current value for a key<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li><li><code>defaultValue</code> : Mixed<div class="sub-desc">The default value to return if the key lookup does not match</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">The state data</div></li></ul></div></div></div></td><td class="msource">Manager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Manager-getProvider"></a><b><a href="source/StateManager.html#method-Ext.state.Manager-getProvider">getProvider</a></b>()
    :
                                        Provider<div class="mdesc"><div class="short">Gets the currently configured state provider</div><div class="long">Gets the currently configured state provider<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Provider</code><div class="sub-desc">The state provider</div></li></ul></div></div></div></td><td class="msource">Manager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Manager-set"></a><b><a href="source/StateManager.html#method-Ext.state.Manager-set">set</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the value for a key</div><div class="long">Sets the value for a key<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li><li><code>value</code> : Mixed<div class="sub-desc">The state data</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Manager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Manager-setProvider"></a><b><a href="source/StateManager.html#method-Ext.state.Manager-setProvider">setProvider</a></b>(&nbsp;<code>Provider&nbsp;stateProvider</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Configures the default state provider for your application</div><div class="long">Configures the default state provider for your application<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>stateProvider</code> : Provider<div class="sub-desc">The state provider to set</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Manager</td></tr></tbody></table><a id="Ext.state.Manager-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>