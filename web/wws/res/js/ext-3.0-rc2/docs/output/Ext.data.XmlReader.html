<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.XmlReader-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.XmlReader-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.XmlReader-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.XmlReader-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.XmlReader"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.data.DataReader.html" ext:member="" ext:cls="Ext.data.DataReader">DataReader</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">XmlReader</pre></div><h1>Class <a href="source/XmlReader.html#cls-Ext.data.XmlReader">Ext.data.XmlReader</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">XmlReader.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/XmlReader.html#cls-Ext.data.XmlReader">XmlReader</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.data.DataReader.html" ext:cls="Ext.data.DataReader" ext:member="">DataReader</a></td></tr></table><div class="description"><p>Data reader class to create an Array of <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> objects from an XML document
based on mappings in a provided <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> constructor.</p>
<p><b>Note</b>: that in order for the browser to parse a returned XML document, the Content-Type
header in the HTTP response must be set to "text/xml" or "application/xml".</p>
<p>Example code:</p>
<pre><code><b>var</b> Employee = Ext.data.Record.create([
   {name: <em>'name'</em>, mapping: <em>'name'</em>},     <i>// <em>"mapping"</em> property not needed <b>if</b> it is the same as <em>"name"</em></i>
   {name: <em>'occupation'</em>}                 <i>// This field will use <em>"occupation"</em> as the mapping.</i>
]);
<b>var</b> myReader = <b>new</b> Ext.data.XmlReader({
   totalRecords: <em>"results"</em>, <i>// The element which contains the total dataset size (optional)</i>
   record: <em>"row"</em>,           <i>// The repeated element which contains row information</i>
   id: <em>"id"</em>                 <i>// The element within the row that provides an ID <b>for</b> the record (optional)</i>
}, Employee);</code></pre>
<p>
This would consume an XML file like this:
<pre><code>&lt;?xml version=<em>"1.0"</em> encoding=<em>"UTF-8"</em>?>
&lt;dataset>
 &lt;results>2&lt;/results>
 &lt;row>
   &lt;id>1&lt;/id>
   &lt;name>Bill&lt;/name>
   &lt;occupation>Gardener&lt;/occupation>
 &lt;/row>
 &lt;row>
   &lt;id>2&lt;/id>
   &lt;name>Ben&lt;/name>
   &lt;occupation>Horticulturalist&lt;/occupation>
 &lt;/row>
&lt;/dataset></code></pre></div><div class="hr"></div><a id="Ext.data.XmlReader-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-fields"></a><b><a href="source/DataReader.html#cfg-Ext.data.DataReader-fields">fields</a></b> : Array/Object<div class="mdesc"><div class="short">Either an Array of Field definition objects (which&#13;
will be passed to Ext.data.Record.create, or a Record&#13;
constructo...</div><div class="long"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created from <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#fields" ext:member="#fields" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-idPath"></a><b><a href="source/XmlReader.html#cfg-Ext.data.XmlReader-idPath">idPath</a></b> : String<div class="mdesc">The DomQuery path relative from the record element to the element that contains
a record identifier value.</div></td><td class="msource">XmlReader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-record"></a><b><a href="source/XmlReader.html#cfg-Ext.data.XmlReader-record">record</a></b> : String<div class="mdesc">The DomQuery path to the repeated element which contains record information.</div></td><td class="msource">XmlReader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-success"></a><b><a href="source/XmlReader.html#cfg-Ext.data.XmlReader-success">success</a></b> : String<div class="mdesc">The DomQuery path to the success attribute used by forms.</div></td><td class="msource">XmlReader</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-totalRecords"></a><b><a href="source/XmlReader.html#cfg-Ext.data.XmlReader-totalRecords">totalRecords</a></b> : String<div class="mdesc"><div class="short">The DomQuery path from which to retrieve the total number of records
in the dataset. This is only needed if the whole...</div><div class="long">The DomQuery path from which to retrieve the total number of records
in the dataset. This is only needed if the whole dataset is not passed in one go, but is being
paged from the remote server.</div></div></td><td class="msource">XmlReader</td></tr></tbody></table><a id="Ext.data.XmlReader-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-Error"></a><b><a href="source/DataReader.html#prop-Ext.data.DataReader-Error">Error</a></b> : Object<div class="mdesc">General error class for Ext.data.DataReader</div></td><td class="msource"><a href="output/Ext.data.DataReader.html#Error" ext:member="#Error" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-meta"></a><b><a href="source/DataReader.html#prop-Ext.data.DataReader-meta">meta</a></b> : Mixed<div class="mdesc">This DataReader's configured metadata as passed to the constructor.</div></td><td class="msource"><a href="output/Ext.data.DataReader.html#meta" ext:member="#meta" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-xmlData"></a><b><a href="source/XmlReader.html#prop-Ext.data.XmlReader-xmlData">xmlData</a></b> : XMLDocument<div class="mdesc">After any data loads/reads, the raw XML Document is available for further custom processing.</div></td><td class="msource">XmlReader</td></tr></tbody></table><a id="Ext.data.XmlReader-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-XmlReader"></a><b><a href="source/XmlReader.html#cls-Ext.data.XmlReader">XmlReader</a></b>(&nbsp;<code>Object&nbsp;meta</code>,&nbsp;<code>Object&nbsp;recordType</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new XmlReader.</div><div class="long">Create a new XmlReader.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>meta</code> : Object<div class="sub-desc">Metadata configuration options</div></li><li><code>recordType</code> : Object<div class="sub-desc">Either an Array of field definition objects as passed to
<a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a Record constructor object created using <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">XmlReader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-isData"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-isData">isData</a></b>(&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the supplied data-hash looks and quacks like data.  Checks to see if it has a key&#13;
corresponding to i...</div><div class="long">Returns true if the supplied data-hash <b>looks</b> and quacks like data.  Checks to see if it has a key
corresponding to idProperty defined in your DataReader config containing non-empty pk.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#isData" ext:member="#isData" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-read"></a><b><a href="source/XmlReader.html#method-Ext.data.XmlReader-read">read</a></b>(&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">This method is only used by a DataProxy which has retrieved data from a remote server.</div><div class="long">This method is only used by a DataProxy which has retrieved data from a remote server.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>response</code> : Object<div class="sub-desc">The XHR object which contains the parsed XML document.  The response is expected
to contain a property called <tt>responseXML</tt> which refers to an XML document object.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">records A data block which is used by an {@link Ext.data.Store} as
a cache of Ext.data.Records.</div></li></ul></div></div></div></td><td class="msource">XmlReader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.XmlReader-readRecords"></a><b><a href="source/XmlReader.html#method-Ext.data.XmlReader-readRecords">readRecords</a></b>(&nbsp;<code>Object&nbsp;doc</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Create a data block containing Ext.data.Records from an XML document.</div><div class="long">Create a data block containing Ext.data.Records from an XML document.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>doc</code> : Object<div class="sub-desc">A parsed XML document.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">records A data block which is used by an {@link Ext.data.Store} as
a cache of Ext.data.Records.</div></li></ul></div></div></div></td><td class="msource">XmlReader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-realize"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-realize">realize</a></b>(&nbsp;<code>Record/Record[]&nbsp;record</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from ser...</div><div class="long">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from server.
You <b>must</b> return at least the database pk using the idProperty defined in your DataReader configuration.  The incoming
data from server will be merged with the data in the local record.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Record/Record[]<div class="sub-desc">The phantom record to be realized.</div></li><li><code>data</code> : Object/Object[]<div class="sub-desc">The new record data to apply.  Must include the primary-key from database defined in idProperty field.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#realize" ext:member="#realize" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-update"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-update">update</a></b>(&nbsp;<code>Record/Record[]&nbsp;rs</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.&#13;
You must retu...</div><div class="long">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.
You <b>must</b> return a complete new record from the server.  If you don't, your local record's missing fields
will be populated with the default values specified in your Ext.data.Record.create specification.  Without a defaultValue,
local fields will be populated with empty string "".  So return your entire record's data after both remote create and update.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed as the record receives
a fresh new data-hash.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rs</code> : Record/Record[]<div class="sub-desc"></div></li><li><code>data</code> : Object/Object[]<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#update" ext:member="#update" ext:cls="Ext.data.DataReader">DataReader</a></td></tr></tbody></table><a id="Ext.data.XmlReader-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>