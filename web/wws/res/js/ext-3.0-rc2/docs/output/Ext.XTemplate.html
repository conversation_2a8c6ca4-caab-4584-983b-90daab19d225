<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.XTemplate-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.XTemplate-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.XTemplate-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.XTemplate"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.Template.html" ext:member="" ext:cls="Ext.Template">Template</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">XTemplate</pre></div><h1>Class <a href="source/XTemplate.html#cls-Ext.XTemplate">Ext.XTemplate</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">XTemplate.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/XTemplate.html#cls-Ext.XTemplate">XTemplate</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.Template.html" ext:cls="Ext.Template" ext:member="">Template</a></td></tr></table><div class="description"><p>A template class that supports advanced functionality like autofilling arrays, conditional processing with
basic comparison operators, sub-templates, basic math function support, special built-in template variables,
inline code execution and more.  XTemplate also provides the templating mechanism built into <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">Ext.DataView</a>.</p>
<p>XTemplate supports many special tags and built-in operators that aren't defined as part of the API, but are
supported in the templates that can be created.  The following examples demonstrate all of the supported features.
This is the data object used for reference in each code example:</p>
<pre><code><b>var</b> data = {
    name: <em>'Jack Slocum'</em>,
    title: <em>'Lead Developer'</em>,
    company: <em>'Ext JS, LLC'</em>,
    email: <em>'<EMAIL>'</em>,
    address: <em>'4 Red Bulls Drive'</em>,
    city: <em>'Cleveland'</em>,
    state: <em>'Ohio'</em>,
    zip: <em>'44102'</em>,
    drinks: [<em>'Red Bull'</em>, <em>'Coffee'</em>, <em>'Water'</em>],
    kids: [{
        name: <em>'Sara Grace'</em>,
        age:3
    },{
        name: <em>'Zachary'</em>,
        age:2
    },{
        name: <em>'John James'</em>,
        age:0
    }]
};</code></pre>
<p><b>Auto filling of arrays</b><br/>The <tt>tpl</tt> tag and the <tt>for</tt> operator are used
to process the provided data object. If <tt>for="."</tt> is specified, the data object provided
is examined. If the variable in <tt>for</tt> is an array, it will auto-fill, repeating the template
block inside the <tt>tpl</tt> tag for each item in the array:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b>for</b>=<em>"."</em>>'</em>,
        <em>'&lt;p>{name}&lt;/p>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>
);
tpl.overwrite(panel.body, data.kids); <i>// pass the kids property of the data object</i></code></pre>
<p><b>Scope switching</b><br/>The <tt>for</tt> property can be leveraged to access specified members
of the provided data object to populate the template:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Name: {name}&lt;/p>'</em>,
    <em>'&lt;p>Title: {title}&lt;/p>'</em>,
    <em>'&lt;p>Company: {company}&lt;/p>'</em>,
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b><b>for</b>=<em>"kids"</em></b>>'</em>, <i>// interrogate the kids property within the data</i>
        <em>'&lt;p>{name}&lt;/p>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>
);
tpl.overwrite(panel.body, data);</code></pre>
<p><b>Access to parent object from within sub-template scope</b><br/>When processing a sub-template, for example while
looping through a child array, you can access the parent object's members via the <tt>parent</tt> object:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Name: {name}&lt;/p>'</em>,
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b>for</b>=<em>"kids"</em>>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"age &amp;gt; 1"</em>>'</em>,  <i>// <-- Note that the &gt; is encoded</i>
            <em>'&lt;p>{name}&lt;/p>'</em>,
            <em>'&lt;p>Dad: {parent.name}&lt;/p>'</em>,
        <em>'&lt;/tpl>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>
);
tpl.overwrite(panel.body, data);</code></pre>
<p><b>Array item index and basic math support</b> <br/>While processing an array, the special variable <tt>{#}</tt>
will provide the current array index + 1 (starts at 1, not 0). Templates also support the basic math operators
+ - * and / that can be applied directly on numeric data values:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Name: {name}&lt;/p>'</em>,
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b>for</b>=<em>"kids"</em>>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"age &amp;gt; 1"</em>>'</em>,  <i>// <-- Note that the &gt; is encoded</i>
            <em>'&lt;p>{#}: {name}&lt;/p>'</em>,  <i>// <-- Auto-number each item</i>
            <em>'&lt;p>In 5 Years: {age+5}&lt;/p>'</em>,  <i>// <-- Basic math</i>
            <em>'&lt;p>Dad: {parent.name}&lt;/p>'</em>,
        <em>'&lt;/tpl>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>
);
tpl.overwrite(panel.body, data);</code></pre>
<p><b>Auto-rendering of flat arrays</b> <br/>Flat arrays that contain values (and not objects) can be auto-rendered
using the special <tt>{.}</tt> variable inside a loop.  This variable will represent the value of
the array at the current index:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>{name}\'</em>s favorite beverages:&lt;/p><em>',
    '</em>&lt;tpl <b>for</b>=<em>"drinks"</em>><em>',
       '</em>&lt;div> - {.}&lt;/div><em>',
    '</em>&lt;/tpl><em>'
);
tpl.overwrite(panel.body, data);</code></pre>
<p><b>Basic conditional logic</b> <br/>Using the <tt>tpl</tt> tag and the <tt>if</tt>
operator you can provide conditional checks for deciding whether or not to render specific parts of the template.
Note that there is no <tt>else</tt> operator &mdash; if needed, you should use two opposite <tt>if</tt> statements.
Properly-encoded attributes are required as seen in the following example:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Name: {name}&lt;/p>'</em>,
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b>for</b>=<em>"kids"</em>>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"age &amp;gt; 1"</em>>'</em>,  <i>// <-- Note that the &gt; is encoded</i>
            <em>'&lt;p>{name}&lt;/p>'</em>,
        <em>'&lt;/tpl>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>
);
tpl.overwrite(panel.body, data);</code></pre>
<p><b>Ability to execute arbitrary inline code</b> <br/>In an XTemplate, anything between {[ ... ]}  is considered
code to be executed in the scope of the template. There are some special variables available in that code:
<ul>
<li><b><tt>values</tt></b>: The values in the current scope. If you are using scope changing sub-templates, you
can change what <tt>values</tt> is.</li>
<li><b><tt>parent</tt></b>: The scope (values) of the ancestor template.</li>
<li><b><tt>xindex</tt></b>: If you are in a looping template, the index of the loop you are in (1-based).</li>
<li><b><tt>xcount</tt></b>: If you are in a looping template, the total length of the array you are looping.</li>
<li><b><tt>fm</tt></b>: An alias for <tt>Ext.util.Format</tt>.</li>
</ul>
This example demonstrates basic row striping using an inline code block and the <tt>xindex</tt> variable:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Name: {name}&lt;/p>'</em>,
    <em>'&lt;p>Company: {[values.company.toUpperCase() + <em>", "</em> + values.title]}&lt;/p>'</em>,
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b>for</b>=<em>"kids"</em>>'</em>,
       <em>'&lt;div class=<em>"{[xindex % 2 === 0 ? "</em>even<em>" : "</em>odd<em>"]}"</em>>'</em>,
        <em>'{name}'</em>,
        <em>'&lt;/div>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>
);
tpl.overwrite(panel.body, data);</code></pre>
<p><b>Template member functions</b> <br/>One or more member functions can be defined directly on the config
object passed into the XTemplate constructor for more complex processing:</p>
<pre><code><b>var</b> tpl = <b>new</b> Ext.XTemplate(
    <em>'&lt;p>Name: {name}&lt;/p>'</em>,
    <em>'&lt;p>Kids: '</em>,
    <em>'&lt;tpl <b>for</b>=<em>"kids"</em>>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"this.isGirl(name)"</em>>'</em>,
            <em>'&lt;p>Girl: {name} - {age}&lt;/p>'</em>,
        <em>'&lt;/tpl>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"this.isGirl(name) == false"</em>>'</em>,
            <em>'&lt;p>Boy: {name} - {age}&lt;/p>'</em>,
        <em>'&lt;/tpl>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"this.isBaby(age)"</em>>'</em>,
            <em>'&lt;p>{name} is a baby!&lt;/p>'</em>,
        <em>'&lt;/tpl>'</em>,
    <em>'&lt;/tpl>&lt;/p>'</em>, {
     isGirl: <b>function</b>(name){
         <b>return</b> name == <em>'Sara Grace'</em>;
     },
     isBaby: <b>function</b>(age){
        <b>return</b> age < 1;
     }
});
tpl.overwrite(panel.body, data);</code></pre></div><div class="hr"></div><a id="Ext.XTemplate-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.XTemplate-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.XTemplate-XTemplate"></a><b><a href="source/XTemplate.html#cls-Ext.XTemplate">XTemplate</a></b>(&nbsp;<code>String/Array/Object&nbsp;parts</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>parts</code> : String/Array/Object<div class="sub-desc">The HTML fragment or an array of fragments to join(""), or multiple arguments
to join("") that can also include a config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">XTemplate</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.XTemplate-XTemplate.from"></a><b><a href="source/XTemplate.html#method-Ext.XTemplate-XTemplate.from">XTemplate.from</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>&nbsp;)
    :
                                        Ext.Template<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Creates a template from the passed element's value (display:none textarea, preferred) or innerHTML.</div><div class="long">&lt;static&gt;&nbsp;Creates a template from the passed element's value (<i>display:none</i> textarea, preferred) or innerHTML.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">A DOM element or its id</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Template</code><div class="sub-desc">The created template</div></li></ul></div></div></div></td><td class="msource">XTemplate</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-append"></a><b><a href="source/Template.html#method-Ext.Template-append">append</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and appends the new node(s) to el.</div><div class="long">Applies the supplied values to the template and appends the new node(s) to el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Template.html#append" ext:member="#append" ext:cls="Ext.Template">Template</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.XTemplate-apply"></a><b><a href="source/XTemplate.html#method-Ext.XTemplate-apply">apply</a></b>(&nbsp;<code>Object/Array&nbsp;values</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Alias for applyTemplate
Returns an HTML fragment of this template with the specified values applied.</div><div class="long">Alias for <a href="output/Ext.XTemplate.html#Ext.XTemplate-applyTemplate" ext:member="applyTemplate" ext:cls="Ext.XTemplate">applyTemplate</a>
Returns an HTML fragment of this template with the specified values applied.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The HTML fragment</div></li></ul></div></div></div></td><td class="msource">XTemplate</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.XTemplate-applyTemplate"></a><b><a href="source/XTemplate.html#method-Ext.XTemplate-applyTemplate">applyTemplate</a></b>(&nbsp;<code>Object&nbsp;values</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns an HTML fragment of this template with the specified values applied.</div><div class="long">Returns an HTML fragment of this template with the specified values applied.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>values</code> : Object<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The HTML fragment</div></li></ul></div></div></div></td><td class="msource">XTemplate</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.XTemplate-compile"></a><b><a href="source/XTemplate.html#method-Ext.XTemplate-compile">compile</a></b>()
    :
                                        Function<div class="mdesc"><div class="short">Compile the template to a function for optimized performance.  Recommended if the template will be used frequently.</div><div class="long">Compile the template to a function for optimized performance.  Recommended if the template will be used frequently.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Function</code><div class="sub-desc">The compiled function</div></li></ul></div></div></div></td><td class="msource">XTemplate</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-insertAfter"></a><b><a href="source/Template.html#method-Ext.Template-insertAfter">insertAfter</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and inserts the new node(s) after el.</div><div class="long">Applies the supplied values to the template and inserts the new node(s) after el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Template.html#insertAfter" ext:member="#insertAfter" ext:cls="Ext.Template">Template</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-insertBefore"></a><b><a href="source/Template.html#method-Ext.Template-insertBefore">insertBefore</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and inserts the new node(s) before el.</div><div class="long">Applies the supplied values to the template and inserts the new node(s) before el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Template.html#insertBefore" ext:member="#insertBefore" ext:cls="Ext.Template">Template</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-insertFirst"></a><b><a href="source/Template.html#method-Ext.Template-insertFirst">insertFirst</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and inserts the new node(s) as the first child of el.</div><div class="long">Applies the supplied values to the template and inserts the new node(s) as the first child of el.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Template.html#insertFirst" ext:member="#insertFirst" ext:cls="Ext.Template">Template</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Template-overwrite"></a><b><a href="source/Template.html#method-Ext.Template-overwrite">overwrite</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object/Array&nbsp;values</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnElement</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Applies the supplied values to the template and overwrites the content of el with the new node(s).</div><div class="long">Applies the supplied values to the template and overwrites the content of el with the new node(s).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The context element</div></li><li><code>values</code> : Object/Array<div class="sub-desc">The template values. Can be an array if your params are numeric (i.e. {0}) or an object (i.e. {foo: 'bar'})</div></li><li><code>returnElement</code> : Boolean<div class="sub-desc">(optional) true to return a Ext.Element (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The new node or Element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Template.html#overwrite" ext:member="#overwrite" ext:cls="Ext.Template">Template</a></td></tr></tbody></table><a id="Ext.XTemplate-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>