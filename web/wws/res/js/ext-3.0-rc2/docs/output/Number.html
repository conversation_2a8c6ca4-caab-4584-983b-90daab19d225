<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Number-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Number-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Number-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Number"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Ext-more.html#cls-Number">Number</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Global</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Ext-more.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Ext-more.html#cls-Number">Number</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"></div><div class="hr"></div><a id="Number-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Number-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Number-constrain"></a><b><a href="source/Ext-more.html#method-Number-constrain">constrain</a></b>(&nbsp;<code>Number&nbsp;min</code>,&nbsp;<code>Number&nbsp;max</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Checks whether or not the current number is within a desired range.  If the number is already within the
range it is ...</div><div class="long">Checks whether or not the current number is within a desired range.  If the number is already within the
range it is returned, otherwise the min or max value is returned depending on which side of the range is
exceeded.  Note that this method returns the constrained value but does not change the current number.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>min</code> : Number<div class="sub-desc">The minimum number in the range</div></li><li><code>max</code> : Number<div class="sub-desc">The maximum number in the range</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The constrained value if outside the range, otherwise the current value</div></li></ul></div></div></div></td><td class="msource">Number</td></tr></tbody></table><a id="Number-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>