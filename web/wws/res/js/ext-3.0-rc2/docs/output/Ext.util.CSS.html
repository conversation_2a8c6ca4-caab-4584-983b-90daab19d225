<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.util.CSS-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.util.CSS-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.util.CSS-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.util.CSS"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/CSS.html#cls-Ext.util.CSS">Ext.util.CSS</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.util</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">CSS.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/CSS.html#cls-Ext.util.CSS">CSS</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Utility class for manipulating CSS rules<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.util.CSS-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.util.CSS-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-createStyleSheet"></a><b><a href="source/CSS.html#method-Ext.util.CSS-createStyleSheet">createStyleSheet</a></b>(&nbsp;<code>String&nbsp;cssText</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        StyleSheet<div class="mdesc"><div class="short">Creates a stylesheet from a text blob of rules.&#13;
These rules will be wrapped in a STYLE tag and appended to the HEAD ...</div><div class="long">Creates a stylesheet from a text blob of rules.
These rules will be wrapped in a STYLE tag and appended to the HEAD of the document.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cssText</code> : String<div class="sub-desc">The text containing the css rules</div></li><li><code>id</code> : String<div class="sub-desc">An id to add to the stylesheet for later removal</div></li></ul><strong>Returns:</strong><ul><li><code>StyleSheet</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">CSS</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-getRule"></a><b><a href="source/CSS.html#method-Ext.util.CSS-getRule">getRule</a></b>(&nbsp;<code>String/Array&nbsp;selector</code>,&nbsp;<code>Boolean&nbsp;refreshCache</code>&nbsp;)
    :
                                        CSSRule<div class="mdesc"><div class="short">Gets an an individual CSS rule by selector(s)</div><div class="long">Gets an an individual CSS rule by selector(s)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String/Array<div class="sub-desc">The CSS selector or an array of selectors to try. The first selector that is found is returned.</div></li><li><code>refreshCache</code> : Boolean<div class="sub-desc">true to refresh the internal cache if you have recently updated any rules or added styles dynamically</div></li></ul><strong>Returns:</strong><ul><li><code>CSSRule</code><div class="sub-desc">The CSS rule or null if one is not found</div></li></ul></div></div></div></td><td class="msource">CSS</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-getRules"></a><b><a href="source/CSS.html#method-Ext.util.CSS-getRules">getRules</a></b>(&nbsp;<code>Boolean&nbsp;refreshCache</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Gets all css rules for the document</div><div class="long">Gets all css rules for the document<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>refreshCache</code> : Boolean<div class="sub-desc">true to refresh the internal cache</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object (hash) of rules indexed by selector</div></li></ul></div></div></div></td><td class="msource">CSS</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-refreshCache"></a><b><a href="source/CSS.html#method-Ext.util.CSS-refreshCache">refreshCache</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Refresh the rule cache if you have dynamically added stylesheets</div><div class="long">Refresh the rule cache if you have dynamically added stylesheets<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object (hash) of rules indexed by selector</div></li></ul></div></div></div></td><td class="msource">CSS</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-removeStyleSheet"></a><b><a href="source/CSS.html#method-Ext.util.CSS-removeStyleSheet">removeStyleSheet</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes a style or link tag by id</div><div class="long">Removes a style or link tag by id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The id of the tag</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">CSS</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-swapStyleSheet"></a><b><a href="source/CSS.html#method-Ext.util.CSS-swapStyleSheet">swapStyleSheet</a></b>(&nbsp;<code>String&nbsp;id</code>,&nbsp;<code>String&nbsp;url</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Dynamically swaps an existing stylesheet reference for a new one</div><div class="long">Dynamically swaps an existing stylesheet reference for a new one<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The id of an existing link tag to remove</div></li><li><code>url</code> : String<div class="sub-desc">The href of the new stylesheet to include</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">CSS</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.CSS-updateRule"></a><b><a href="source/CSS.html#method-Ext.util.CSS-updateRule">updateRule</a></b>(&nbsp;<code>String/Array&nbsp;selector</code>,&nbsp;<code>String&nbsp;property</code>,&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Updates a rule property</div><div class="long">Updates a rule property<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String/Array<div class="sub-desc">If it's an array it tries each selector until it finds one. Stops immediately once one is found.</div></li><li><code>property</code> : String<div class="sub-desc">The css property</div></li><li><code>value</code> : String<div class="sub-desc">The new value for the property</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">true If a rule was found and updated</div></li></ul></div></div></div></td><td class="msource">CSS</td></tr></tbody></table><a id="Ext.util.CSS-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>