<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.JsonReader-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.JsonReader-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.JsonReader-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.JsonReader-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.JsonReader"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.data.DataReader.html" ext:member="" ext:cls="Ext.data.DataReader">DataReader</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">JsonReader</pre></div><h1>Class <a href="source/JsonReader.html#cls-Ext.data.JsonReader">Ext.data.JsonReader</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">JsonReader.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/JsonReader.html#cls-Ext.data.JsonReader">JsonReader</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.data.ArrayReader.html" ext:cls="Ext.data.ArrayReader">ArrayReader</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.data.DataReader.html" ext:cls="Ext.data.DataReader" ext:member="">DataReader</a></td></tr></table><div class="description"><p>Data reader class to create an Array of <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> objects from a JSON response
based on mappings in a provided <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> constructor.</p>
<p>Example code:</p>
<pre><code><b>var</b> Employee = Ext.data.Record.create([
    {name: <em>'firstname'</em>},                  <i>// map the Record<em>'s <em>"firstname"</em> field to the row object'</em>s key of the same name</i>
    {name: <em>'job'</em>, mapping: <em>'occupation'</em>}  <i>// map the Record<em>'s <em>"job"</em> field to the row object'</em>s <em>"occupation"</em> key</i>
]);
<b>var</b> myReader = <b>new</b> Ext.data.JsonReader(
    {                             <i>// The metadata property, <b>with</b> configuration options:</i>
        totalProperty: <em>"results"</em>, <i>//   the property which contains the total dataset size (optional)</i>
        root: <em>"rows"</em>,             <i>//   the property which contains an Array of record data objects</i>
        idProperty: <em>"id"</em>          <i>//   the property within each row object that provides an ID <b>for</b> the record (optional)</i>
    },
    Employee  <i>// <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> constructor that provides mapping <b>for</b> JSON object</i>
);</code></pre>
<p>This would consume a JSON data object of the form:</p><pre><code>{
    results: 2,  <i>// Reader<em>'s configured totalProperty</i>
    rows: [      <i>// Reader'</em>s configured root</i>
        { id: 1, firstname: <em>'Bill'</em>, occupation: <em>'Gardener'</em> },         <i>// a row object</i>
        { id: 2, firstname: <em>'Ben'</em> , occupation: <em>'Horticulturalist'</em> }  <i>// another row object</i>
    ]
}</code></pre>
<p><b><u>Automatic configuration using metaData</u></b></p>
<p>It is possible to change a JsonReader's metadata at any time by including a <b><tt>metaData</tt></b>
property in the JSON data object. If the JSON data object has a <b><tt>metaData</tt></b> property, a
<a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Store</a> object using this Reader will reconfigure itself to use the newly provided
field definition and fire its <a href="output/Ext.data.Store.html#Ext.data.Store-metachange" ext:member="metachange" ext:cls="Ext.data.Store">metachange</a> event. The metachange event
handler may interrogate the <b><tt>metaData</tt></b> property to perform any configuration required.
Note that reconfiguring a Store potentially invalidates objects which may refer to Fields or Records
which no longer exist.</p>
<p>The <b><tt>metaData</tt></b> property in the JSON data object may contain:</p>
<div class="mdetail-params"><ul>
<li>any of the configuration options for this class</li>
<li>a <b><tt><a href="output/Ext.data.Record.html#Ext.data.Record-fields" ext:member="fields" ext:cls="Ext.data.Record">fields</a></tt></b> property which the JsonReader will
use as an argument to the <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">data Record create method</a> in order to
configure the layout of the Records it will produce.</li>
<li>a <b><tt><a href="output/Ext.data.Store.html#Ext.data.Store-sortInfo" ext:member="sortInfo" ext:cls="Ext.data.Store">sortInfo</a></tt></b> property which the JsonReader will
use to set the <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>'s <a href="output/Ext.data.Store.html#Ext.data.Store-sortInfo" ext:member="sortInfo" ext:cls="Ext.data.Store">sortInfo</a> property</li>
<li>any user-defined properties needed</li>
</ul></div>
<p>To use this facility to send the same data as the example above (without having to code the creation
of the Record constructor), you would create the JsonReader like this:</p><pre><code><b>var</b> myReader = <b>new</b> Ext.data.JsonReader();</code></pre>
<p>The first data packet from the server would configure the reader by containing a
<b><tt>metaData</tt></b> property <b>and</b> the data. For example, the JSON data object might take
the form:</p>
<pre><code>{
    metaData: {
        idProperty: <em>'id'</em>,
        root: <em>'rows'</em>,
        totalProperty: <em>'results'</em>,
        fields: [
            {name: <em>'name'</em>},
            {name: <em>'job'</em>, mapping: <em>'occupation'</em>}
        ],
        sortInfo: {field: <em>'name'</em>, direction:<em>'ASC'</em>}, <i>// used by store to set its sortInfo</i>
        foo: <em>'bar'</em> <i>// custom property</i>
    },
    results: 2,
    rows: [
        { <em>'id'</em>: 1, <em>'name'</em>: <em>'Bill'</em>, occupation: <em>'Gardener'</em> },
        { <em>'id'</em>: 2, <em>'name'</em>: <em>'Ben'</em>, occupation: <em>'Horticulturalist'</em> }
    ]
}</code></pre></div><div class="hr"></div><a id="Ext.data.JsonReader-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-fields"></a><b><a href="source/DataReader.html#cfg-Ext.data.DataReader-fields">fields</a></b> : Array/Object<div class="mdesc"><div class="short">Either an Array of Field definition objects (which&#13;
will be passed to Ext.data.Record.create, or a Record&#13;
constructo...</div><div class="long"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created from <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#fields" ext:member="#fields" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-idProperty"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-idProperty">idProperty</a></b> : String<div class="mdesc">[id] Name of the property within a row object that contains a record identifier value.  Defaults to <tt>id</tt></div></td><td class="msource">JsonReader</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-root"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-root">root</a></b> : String<div class="mdesc"><div class="short">[undefined] Required.  The name of the property which contains the Array of row objects.  Defaults to undefined.  An ...</div><div class="long">[undefined] <b>Required</b>.  The name of the property which contains the Array of row objects.  Defaults to <tt>undefined</tt>.  An exception will be thrown if the root property is undefiend.</div></div></td><td class="msource">JsonReader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-successProperty"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-successProperty">successProperty</a></b> : String<div class="mdesc">[success] Name of the property from which to retrieve the success attribute used by forms.  Defaults to <tt>success</tt>.</div></td><td class="msource">JsonReader</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-totalProperty"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-totalProperty">totalProperty</a></b> : String<div class="mdesc"><div class="short">[total] Name of the property from which to retrieve the total number of records
in the dataset. This is only needed i...</div><div class="long">[total] Name of the property from which to retrieve the total number of records
in the dataset. This is only needed if the whole dataset is not passed in one go, but is being
paged from the remote server.  Defaults to <tt>total</tt>.</div></div></td><td class="msource">JsonReader</td></tr></tbody></table><a id="Ext.data.JsonReader-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-Error"></a><b><a href="source/JsonReader.html#prop-Ext.data.JsonReader-Error">Error</a></b> : Object<div class="mdesc">Error class for JsonReader</div></td><td class="msource">JsonReader</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-jsonData"></a><b><a href="source/JsonReader.html#prop-Ext.data.JsonReader-jsonData">jsonData</a></b> : Object<div class="mdesc"><div class="short">After any data loads, the raw JSON data is available for further custom processing.  If no data is
loaded or there is...</div><div class="long">After any data loads, the raw JSON data is available for further custom processing.  If no data is
loaded or there is a load exception this property will be undefined.</div></div></td><td class="msource">JsonReader</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-meta"></a><b><a href="source/JsonReader.html#prop-Ext.data.JsonReader-meta">meta</a></b> : Mixed<div class="mdesc">This JsonReader's metadata as passed to the constructor, or as passed in
the last data packet's <b><tt>metaData</tt></b> property.</div></td><td class="msource">JsonReader</td></tr></tbody></table><a id="Ext.data.JsonReader-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-JsonReader"></a><b><a href="source/JsonReader.html#cls-Ext.data.JsonReader">JsonReader</a></b>(&nbsp;<code>Object&nbsp;meta</code>,&nbsp;<code>Array/Object&nbsp;recordType</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new JsonReader</div><div class="long">Create a new JsonReader<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>meta</code> : Object<div class="sub-desc">Metadata configuration options.</div></li><li><code>recordType</code> : Array/Object<div class="sub-desc"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created from <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">JsonReader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-isData"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-isData">isData</a></b>(&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the supplied data-hash looks and quacks like data.  Checks to see if it has a key&#13;
corresponding to i...</div><div class="long">Returns true if the supplied data-hash <b>looks</b> and quacks like data.  Checks to see if it has a key
corresponding to idProperty defined in your DataReader config containing non-empty pk.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#isData" ext:member="#isData" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-read"></a><b><a href="source/JsonReader.html#method-Ext.data.JsonReader-read">read</a></b>(&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">This method is only used by a DataProxy which has retrieved data from a remote server.</div><div class="long">This method is only used by a DataProxy which has retrieved data from a remote server.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>response</code> : Object<div class="sub-desc">The XHR object which contains the JSON data in its responseText.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data A data block which is used by an Ext.data.Store object as
a cache of Ext.data.Records.</div></li></ul></div></div></div></td><td class="msource">JsonReader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-readRecords"></a><b><a href="source/JsonReader.html#method-Ext.data.JsonReader-readRecords">readRecords</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Create a data block containing Ext.data.Records from a JSON object.</div><div class="long">Create a data block containing Ext.data.Records from a JSON object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">An object which contains an Array of row objects in the property specified
in the config as 'root, and optionally a property, specified in the config as 'totalProperty'
which contains the total size of the dataset.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data A data block which is used by an Ext.data.Store object as
a cache of Ext.data.Records.</div></li></ul></div></div></div></td><td class="msource">JsonReader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-readResponse"></a><b><a href="source/JsonReader.html#method-Ext.data.JsonReader-readResponse">readResponse</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">readResponse
decodes a json response from server</div><div class="long">readResponse
decodes a json response from server<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>response</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">JsonReader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-realize"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-realize">realize</a></b>(&nbsp;<code>Record/Record[]&nbsp;record</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from ser...</div><div class="long">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from server.
You <b>must</b> return at least the database pk using the idProperty defined in your DataReader configuration.  The incoming
data from server will be merged with the data in the local record.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Record/Record[]<div class="sub-desc">The phantom record to be realized.</div></li><li><code>data</code> : Object/Object[]<div class="sub-desc">The new record data to apply.  Must include the primary-key from database defined in idProperty field.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#realize" ext:member="#realize" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-update"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-update">update</a></b>(&nbsp;<code>Record/Record[]&nbsp;rs</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.&#13;
You must retu...</div><div class="long">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.
You <b>must</b> return a complete new record from the server.  If you don't, your local record's missing fields
will be populated with the default values specified in your Ext.data.Record.create specification.  Without a defaultValue,
local fields will be populated with empty string "".  So return your entire record's data after both remote create and update.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed as the record receives
a fresh new data-hash.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rs</code> : Record/Record[]<div class="sub-desc"></div></li><li><code>data</code> : Object/Object[]<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#update" ext:member="#update" ext:cls="Ext.data.DataReader">DataReader</a></td></tr></tbody></table><a id="Ext.data.JsonReader-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>