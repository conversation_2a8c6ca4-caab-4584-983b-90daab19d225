<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.DataReader-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.DataReader-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.DataReader-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.DataReader-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.DataReader"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/DataReader.html#cls-Ext.data.DataReader">Ext.data.DataReader</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DataReader.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DataReader.html#cls-Ext.data.DataReader">DataReader</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.data.JsonReader.html" ext:cls="Ext.data.JsonReader">JsonReader</a>,&#13;<a href="output/Ext.data.XmlReader.html" ext:cls="Ext.data.XmlReader">XmlReader</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Abstract base class for reading structured data from a data source and converting
it into an object containing <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> objects and metadata for use
by an <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>.  This class is intended to be extended and should not
be created directly. For existing implementations, see <a href="output/Ext.data.ArrayReader.html" ext:cls="Ext.data.ArrayReader">Ext.data.ArrayReader</a>,
<a href="output/Ext.data.JsonReader.html" ext:cls="Ext.data.JsonReader">Ext.data.JsonReader</a> and <a href="output/Ext.data.XmlReader.html" ext:cls="Ext.data.XmlReader">Ext.data.XmlReader</a>.</div><div class="hr"></div><a id="Ext.data.DataReader-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-fields"></a><b><a href="source/DataReader.html#cfg-Ext.data.DataReader-fields">fields</a></b> : Array/Object<div class="mdesc"><div class="short">Either an Array of Field definition objects (which&#13;
will be passed to Ext.data.Record.create, or a Record&#13;
constructo...</div><div class="long"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created from <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></div></td><td class="msource">DataReader</td></tr></tbody></table><a id="Ext.data.DataReader-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-Error"></a><b><a href="source/DataReader.html#prop-Ext.data.DataReader-Error">Error</a></b> : Object<div class="mdesc">General error class for Ext.data.DataReader</div></td><td class="msource">DataReader</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-meta"></a><b><a href="source/DataReader.html#prop-Ext.data.DataReader-meta">meta</a></b> : Mixed<div class="mdesc">This DataReader's configured metadata as passed to the constructor.</div></td><td class="msource">DataReader</td></tr></tbody></table><a id="Ext.data.DataReader-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-DataReader"></a><b><a href="source/DataReader.html#cls-Ext.data.DataReader">DataReader</a></b>(&nbsp;<code>Object&nbsp;meta</code>,&nbsp;<code>Array/Object&nbsp;recordType</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new DataReader</div><div class="long">Create a new DataReader<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>meta</code> : Object<div class="sub-desc">Metadata configuration options (implementation-specific).</div></li><li><code>recordType</code> : Array/Object<div class="sub-desc"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created using <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataReader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-isData"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-isData">isData</a></b>(&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the supplied data-hash looks and quacks like data.  Checks to see if it has a key&#13;
corresponding to i...</div><div class="long">Returns true if the supplied data-hash <b>looks</b> and quacks like data.  Checks to see if it has a key
corresponding to idProperty defined in your DataReader config containing non-empty pk.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">DataReader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-realize"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-realize">realize</a></b>(&nbsp;<code>Record/Record[]&nbsp;record</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from ser...</div><div class="long">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from server.
You <b>must</b> return at least the database pk using the idProperty defined in your DataReader configuration.  The incoming
data from server will be merged with the data in the local record.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Record/Record[]<div class="sub-desc">The phantom record to be realized.</div></li><li><code>data</code> : Object/Object[]<div class="sub-desc">The new record data to apply.  Must include the primary-key from database defined in idProperty field.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataReader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-update"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-update">update</a></b>(&nbsp;<code>Record/Record[]&nbsp;rs</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.&#13;
You must retu...</div><div class="long">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.
You <b>must</b> return a complete new record from the server.  If you don't, your local record's missing fields
will be populated with the default values specified in your Ext.data.Record.create specification.  Without a defaultValue,
local fields will be populated with empty string "".  So return your entire record's data after both remote create and update.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed as the record receives
a fresh new data-hash.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rs</code> : Record/Record[]<div class="sub-desc"></div></li><li><code>data</code> : Object/Object[]<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataReader</td></tr></tbody></table><a id="Ext.data.DataReader-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>