<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.ArrayReader-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.ArrayReader-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.ArrayReader-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.ArrayReader-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.ArrayReader"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.data.DataReader.html" ext:member="" ext:cls="Ext.data.DataReader">DataReader</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.data.JsonReader.html" ext:member="" ext:cls="Ext.data.JsonReader">JsonReader</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">ArrayReader</pre></div><h1>Class <a href="source/ArrayReader.html#cls-Ext.data.ArrayReader">Ext.data.ArrayReader</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">ArrayReader.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/ArrayReader.html#cls-Ext.data.ArrayReader">ArrayReader</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.data.JsonReader.html" ext:cls="Ext.data.JsonReader" ext:member="">JsonReader</a></td></tr></table><div class="description"><p>Data reader class to create an Array of <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> objects from an Array.
Each element of that Array represents a row of data fields. The
fields are pulled into a Record object using as a subscript, the <code>mapping</code> property
of the field definition if it exists, or the field's ordinal position in the definition.</p>
<p>Example code:</p>
<pre><code><b>var</b> Employee = Ext.data.Record.create([
    {name: <em>'name'</em>, mapping: 1},         <i>// <em>"mapping"</em> only needed <b>if</b> an <em>"id"</em> field is present which</i>
    {name: <em>'occupation'</em>, mapping: 2}    <i>// precludes using the ordinal position as the index.</i>
]);
<b>var</b> myReader = <b>new</b> Ext.data.ArrayReader({
    <a href="output/Ext.data.ArrayReader.html#Ext.data.ArrayReader-idIndex" ext:member="idIndex" ext:cls="Ext.data.ArrayReader">idIndex</a>: 0
}, Employee);</code></pre>
<p>This would consume an Array like this:</p>
<pre><code>[ [1, <em>'Bill'</em>, <em>'Gardener'</em>], [2, <em>'Ben'</em>, <em>'Horticulturalist'</em>] ]</code></pre></div><div class="hr"></div><a id="Ext.data.ArrayReader-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-fields"></a><b><a href="source/DataReader.html#cfg-Ext.data.DataReader-fields">fields</a></b> : Array/Object<div class="mdesc"><div class="short">Either an Array of Field definition objects (which&#13;
will be passed to Ext.data.Record.create, or a Record&#13;
constructo...</div><div class="long"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created from <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#fields" ext:member="#fields" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.ArrayReader-id"></a><b><a href="source/ArrayReader.html#cfg-Ext.data.ArrayReader-id">id</a></b> : Number<div class="mdesc">The subscript within row Array that provides an ID for the Record.
Deprecated. Use <a href="output/Ext.data.ArrayReader.html#Ext.data.ArrayReader-idIndex" ext:member="idIndex" ext:cls="Ext.data.ArrayReader">idIndex</a> instead.</div></td><td class="msource">ArrayReader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.ArrayReader-idIndex"></a><b><a href="source/ArrayReader.html#cfg-Ext.data.ArrayReader-idIndex">idIndex</a></b> : Number<div class="mdesc">The subscript within row Array that provides an ID for the Record.</div></td><td class="msource">ArrayReader</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-idProperty"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-idProperty">idProperty</a></b> : String<div class="mdesc">[id] Name of the property within a row object that contains a record identifier value.  Defaults to <tt>id</tt></div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#idProperty" ext:member="#idProperty" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-root"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-root">root</a></b> : String<div class="mdesc"><div class="short">[undefined] Required.  The name of the property which contains the Array of row objects.  Defaults to undefined.  An ...</div><div class="long">[undefined] <b>Required</b>.  The name of the property which contains the Array of row objects.  Defaults to <tt>undefined</tt>.  An exception will be thrown if the root property is undefiend.</div></div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#root" ext:member="#root" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-totalProperty"></a><b><a href="source/JsonReader.html#cfg-Ext.data.JsonReader-totalProperty">totalProperty</a></b> : String<div class="mdesc"><div class="short">[total] Name of the property from which to retrieve the total number of records
in the dataset. This is only needed i...</div><div class="long">[total] Name of the property from which to retrieve the total number of records
in the dataset. This is only needed if the whole dataset is not passed in one go, but is being
paged from the remote server.  Defaults to <tt>total</tt>.</div></div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#totalProperty" ext:member="#totalProperty" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr></tbody></table><a id="Ext.data.ArrayReader-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-Error"></a><b><a href="source/JsonReader.html#prop-Ext.data.JsonReader-Error">Error</a></b> : Object<div class="mdesc">Error class for JsonReader</div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#Error" ext:member="#Error" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-jsonData"></a><b><a href="source/JsonReader.html#prop-Ext.data.JsonReader-jsonData">jsonData</a></b> : Object<div class="mdesc"><div class="short">After any data loads, the raw JSON data is available for further custom processing.  If no data is
loaded or there is...</div><div class="long">After any data loads, the raw JSON data is available for further custom processing.  If no data is
loaded or there is a load exception this property will be undefined.</div></div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#jsonData" ext:member="#jsonData" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-meta"></a><b><a href="source/JsonReader.html#prop-Ext.data.JsonReader-meta">meta</a></b> : Mixed<div class="mdesc">This JsonReader's metadata as passed to the constructor, or as passed in
the last data packet's <b><tt>metaData</tt></b> property.</div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#meta" ext:member="#meta" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr></tbody></table><a id="Ext.data.ArrayReader-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.ArrayReader-ArrayReader"></a><b><a href="source/ArrayReader.html#cls-Ext.data.ArrayReader">ArrayReader</a></b>(&nbsp;<code>Object&nbsp;meta</code>,&nbsp;<code>Array/Object&nbsp;recordType</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new ArrayReader</div><div class="long">Create a new ArrayReader<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>meta</code> : Object<div class="sub-desc">Metadata configuration options.</div></li><li><code>recordType</code> : Array/Object<div class="sub-desc"><p>Either an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a> definition objects (which
will be passed to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or a <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
constructor created from <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</p></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ArrayReader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-isData"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-isData">isData</a></b>(&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the supplied data-hash looks and quacks like data.  Checks to see if it has a key&#13;
corresponding to i...</div><div class="long">Returns true if the supplied data-hash <b>looks</b> and quacks like data.  Checks to see if it has a key
corresponding to idProperty defined in your DataReader config containing non-empty pk.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#isData" ext:member="#isData" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-read"></a><b><a href="source/JsonReader.html#method-Ext.data.JsonReader-read">read</a></b>(&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">This method is only used by a DataProxy which has retrieved data from a remote server.</div><div class="long">This method is only used by a DataProxy which has retrieved data from a remote server.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>response</code> : Object<div class="sub-desc">The XHR object which contains the JSON data in its responseText.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data A data block which is used by an Ext.data.Store object as
a cache of Ext.data.Records.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#read" ext:member="#read" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.ArrayReader-readRecords"></a><b><a href="source/ArrayReader.html#method-Ext.data.ArrayReader-readRecords">readRecords</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Create a data block containing Ext.data.Records from an Array.</div><div class="long">Create a data block containing Ext.data.Records from an Array.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">An Array of row objects which represents the dataset.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data A data block which is used by an Ext.data.Store object as
a cache of Ext.data.Records.</div></li></ul></div></div></div></td><td class="msource">ArrayReader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.JsonReader-readResponse"></a><b><a href="source/JsonReader.html#method-Ext.data.JsonReader-readResponse">readResponse</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">readResponse
decodes a json response from server</div><div class="long">readResponse
decodes a json response from server<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>response</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.JsonReader.html#readResponse" ext:member="#readResponse" ext:cls="Ext.data.JsonReader">JsonReader</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-realize"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-realize">realize</a></b>(&nbsp;<code>Record/Record[]&nbsp;record</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from ser...</div><div class="long">Used for un-phantoming a record after a successful database insert.  Sets the records pk along with new data from server.
You <b>must</b> return at least the database pk using the idProperty defined in your DataReader configuration.  The incoming
data from server will be merged with the data in the local record.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Record/Record[]<div class="sub-desc">The phantom record to be realized.</div></li><li><code>data</code> : Object/Object[]<div class="sub-desc">The new record data to apply.  Must include the primary-key from database defined in idProperty field.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#realize" ext:member="#realize" ext:cls="Ext.data.DataReader">DataReader</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataReader-update"></a><b><a href="source/DataReader.html#method-Ext.data.DataReader-update">update</a></b>(&nbsp;<code>Record/Record[]&nbsp;rs</code>,&nbsp;<code>Object/Object[]&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.&#13;
You must retu...</div><div class="long">Used for updating a non-phantom or "real" record's data with fresh data from server after remote-save.
You <b>must</b> return a complete new record from the server.  If you don't, your local record's missing fields
will be populated with the default values specified in your Ext.data.Record.create specification.  Without a defaultValue,
local fields will be populated with empty string "".  So return your entire record's data after both remote create and update.
In addition, you <b>must</b> return record-data from the server in the same order received.
Will perform a commit as well, un-marking dirty-fields.  Store's "update" event will be suppressed as the record receives
a fresh new data-hash.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rs</code> : Record/Record[]<div class="sub-desc"></div></li><li><code>data</code> : Object/Object[]<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.DataReader.html#update" ext:member="#update" ext:cls="Ext.data.DataReader">DataReader</a></td></tr></tbody></table><a id="Ext.data.ArrayReader-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>