<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.ComponentMgr-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.ComponentMgr-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.ComponentMgr-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.ComponentMgr"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/ComponentMgr.html#cls-Ext.ComponentMgr">Ext.ComponentMgr</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">ComponentMgr.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/ComponentMgr.html#cls-Ext.ComponentMgr">ComponentMgr</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>Provides a registry of all Components (instances of <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> or any subclass
thereof) on a page so that they can be easily accessed by <a href="output/Ext.Component.html" ext:cls="Ext.Component">component</a>
<a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a> (see <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-get" ext:member="get" ext:cls="Ext.ComponentMgr">get</a>, or the convenience method <a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">Ext.getCmp</a>).</p>
<p>This object also provides a registry of available Component <i>classes</i>
indexed by a mnemonic code known as the Component's <a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtype</a>.
The <tt><a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtype</a></tt> provides a way to avoid instantiating child Components
when creating a full, nested config object for a complete Ext page.</p>
<p>A child Component may be specified simply as a <i>config object</i>
as long as the correct <tt><a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtype</a></tt> is specified so that if and when the Component
needs rendering, the correct type can be looked up for lazy instantiation.</p>
<p>For a list of all available <tt><a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtypes</a></tt>, see <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.</p><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.ComponentMgr-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-all"></a><b><a href="source/ComponentMgr.html#prop-Ext.ComponentMgr-all">all</a></b> : MixedCollection<div class="mdesc"><div class="short">The MixedCollection used internally for the component cache. An example usage may be subscribing to
events on the Mix...</div><div class="long">The MixedCollection used internally for the component cache. An example usage may be subscribing to
events on the MixedCollection to monitor addition or removal.  Read-only.</div></div></td><td class="msource">ComponentMgr</td></tr></tbody></table><a id="Ext.ComponentMgr-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-create"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-create">create</a></b>(&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>Constructor&nbsp;defaultType</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Creates a new Component from the specified config object using the
config object's xtype to determine the class to in...</div><div class="long">Creates a new Component from the specified config object using the
config object's <a href="output/Ext.component.html#Ext.component-xtype" ext:member="xtype" ext:cls="Ext.component">xtype</a> to determine the class to instantiate.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">A configuration object for the Component you wish to create.</div></li><li><code>defaultType</code> : Constructor<div class="sub-desc">The constructor to provide the default Component type if
the config object does not contain a <tt>xtype</tt>. (Optional if the config contains a <tt>xtype</tt>).</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">The newly instantiated Component.</div></li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-createPlugin"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-createPlugin">createPlugin</a></b>(&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>Constructor&nbsp;defaultType</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Creates a new Plugin from the specified config object using the
config object's ptype to determine the class to insta...</div><div class="long">Creates a new Plugin from the specified config object using the
config object's <a href="output/Ext.component.html#Ext.component-ptype" ext:member="ptype" ext:cls="Ext.component">ptype</a> to determine the class to instantiate.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">A configuration object for the Plugin you wish to create.</div></li><li><code>defaultType</code> : Constructor<div class="sub-desc">The constructor to provide the default Plugin type if
the config object does not contain a <tt>ptype</tt>. (Optional if the config contains a <tt>ptype</tt>).</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">The newly instantiated Plugin.</div></li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-get"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-get">get</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns a component by id</div><div class="long">Returns a component by <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The component <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-onAvailable"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-onAvailable">onAvailable</a></b>(&nbsp;<code>String&nbsp;id</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Registers a function that will be called when a specified component is added to ComponentMgr</div><div class="long">Registers a function that will be called when a specified component is added to ComponentMgr<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The component <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></div></li><li><code>fn</code> : Function<div class="sub-desc">The callback function</div></li><li><code>scope</code> : Object<div class="sub-desc">The scope of the callback</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-register"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-register">register</a></b>(&nbsp;<code>Ext.Component&nbsp;c</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Registers a component.</div><div class="long">Registers a component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>c</code> : Ext.Component<div class="sub-desc">The component</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-registerPlugin"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-registerPlugin">registerPlugin</a></b>(&nbsp;<code>String&nbsp;ptype</code>,&nbsp;<code>Constructor&nbsp;cls</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Registers a new Plugin constructor, keyed by a new
Ext.Component.ptype.
Use this method (or its alias Ext.preg) to re...</div><div class="long"><p>Registers a new Plugin constructor, keyed by a new
<a href="output/Ext.Component.html#Ext.Component-ptype" ext:member="ptype" ext:cls="Ext.Component">Ext.Component.ptype</a>.</p>
<p>Use this method (or its alias <a href="output/Ext.html#Ext-preg" ext:member="preg" ext:cls="Ext">Ext.preg</a>) to register new
plugins for <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>s so that lazy instantiation may be used when specifying
Plugins.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>ptype</code> : String<div class="sub-desc">The mnemonic string by which the Plugin class may be looked up.</div></li><li><code>cls</code> : Constructor<div class="sub-desc">The new Plugin class.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-registerType"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-registerType">registerType</a></b>(&nbsp;<code>String&nbsp;xtype</code>,&nbsp;<code>Constructor&nbsp;cls</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Registers a new Component constructor, keyed by a new
Ext.Component.xtype.
Use this method (or its alias Ext.reg) to ...</div><div class="long"><p>Registers a new Component constructor, keyed by a new
<a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">Ext.Component.xtype</a>.</p>
<p>Use this method (or its alias <a href="output/Ext.html#Ext-reg" ext:member="reg" ext:cls="Ext">Ext.reg</a>) to register new
subclasses of <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> so that lazy instantiation may be used when specifying
child Components.
see <a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">Ext.Container.items</a></p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String<div class="sub-desc">The mnemonic string by which the Component class may be looked up.</div></li><li><code>cls</code> : Constructor<div class="sub-desc">The new Component class.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.ComponentMgr-unregister"></a><b><a href="source/ComponentMgr.html#method-Ext.ComponentMgr-unregister">unregister</a></b>(&nbsp;<code>Ext.Component&nbsp;c</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unregisters a component.</div><div class="long">Unregisters a component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>c</code> : Ext.Component<div class="sub-desc">The component</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComponentMgr</td></tr></tbody></table><a id="Ext.ComponentMgr-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>