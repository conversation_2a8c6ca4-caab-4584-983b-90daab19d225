<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.ArrayStore-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.ArrayStore-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.ArrayStore-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.ArrayStore-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.ArrayStore"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.data.Store.html" ext:member="" ext:cls="Ext.data.Store">Store</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">ArrayStore</pre></div><h1>Class <a href="source/ArrayStore.html#cls-Ext.data.ArrayStore">Ext.data.ArrayStore</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">ArrayStore.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/ArrayStore.html#cls-Ext.data.ArrayStore">ArrayStore</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store" ext:member="">Store</a></td></tr><tr><td class="label">xtype:</td><td class="hd-info">arraystore</td></tr></table><div class="description"><p>Formerly known as "SimpleStore".</p>
<p>Small helper class to make creating <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>s from Array data easier.
An ArrayStore will be automatically configured with a <a href="output/Ext.data.ArrayReader.html" ext:cls="Ext.data.ArrayReader">Ext.data.ArrayReader</a>.</p>
<p>A store configuration would be something like:<pre><code><b>var</b> store = <b>new</b> Ext.data.ArrayStore({
    <i>// store configs</i>
    autoDestroy: true,
    storeId: <em>'myStore'</em>,
    <i>// reader configs</i>
    idIndex: 0,  
    fields: [
       <em>'company'</em>,
       {name: <em>'price'</em>, type: <em>'float'</em>},
       {name: <em>'change'</em>, type: <em>'float'</em>},
       {name: <em>'pctChange'</em>, type: <em>'float'</em>},
       {name: <em>'lastChange'</em>, type: <em>'date'</em>, dateFormat: <em>'n/j h:ia'</em>}
    ]
});</code></pre></p>
<p>This store is configured to consume a returned object of the form:<pre><code><b>var</b> myData = [
    [<em>'3m Co'</em>,71.72,0.02,0.03,<em>'9/1 12:00am'</em>],
    [<em>'Alcoa Inc'</em>,29.01,0.42,1.47,<em>'9/1 12:00am'</em>],
    [<em>'Boeing Co.'</em>,75.43,0.53,0.71,<em>'9/1 12:00am'</em>],
    [<em>'Hewlett-Packard Co.'</em>,36.53,-0.03,-0.08,<em>'9/1 12:00am'</em>],
    [<em>'Wal-Mart Stores, Inc.'</em>,45.45,0.73,1.63,<em>'9/1 12:00am'</em>]
];</code></pre>
An object literal of this form could also be used as the <a href="output/Ext.data.ArrayStore.html#Ext.data.ArrayStore-data" ext:member="data" ext:cls="Ext.data.ArrayStore">data</a> config option.</p>
<p><b>*Note:</b> Although not listed here, this class accepts all of the configuration options of 
<b><a href="output/Ext.data.ArrayReader.html" ext:cls="Ext.data.ArrayReader">ArrayReader</a></b>.</p></div><div class="hr"></div><a id="Ext.data.ArrayStore-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-autoDestroy"></a><b><a href="source/Store.html#cfg-Ext.data.Store-autoDestroy">autoDestroy</a></b> : Boolean<div class="mdesc"><div class="short">true to destroy the store when the component the store is bound
to is destroyed (defaults to false).
Note: this shoul...</div><div class="long"><tt>true</tt> to destroy the store when the component the store is bound
to is destroyed (defaults to <tt>false</tt>).
<p><b>Note</b>: this should be set to true when using stores that are bound to only 1 component.</p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#autoDestroy" ext:member="#autoDestroy" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-autoLoad"></a><b><a href="source/Store.html#cfg-Ext.data.Store-autoLoad">autoLoad</a></b> : Boolean/Object<div class="mdesc"><div class="short">If data is not specified, and if autoLoad
is true or an Object, this store's load method is automatically called
afte...</div><div class="long">If <tt><a href="output/Ext.data.Store.html#Ext.data.Store-data" ext:member="data" ext:cls="Ext.data.Store">data</a></tt> is not specified, and if <tt>autoLoad</tt>
is <tt>true</tt> or an <tt>Object</tt>, this store's <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> method is automatically called
after creation. If the value of <tt>autoLoad</tt> is an <tt>Object</tt>, this <tt>Object</tt> will
be passed to the store's <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> method.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#autoLoad" ext:member="#autoLoad" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-autoSave"></a><b><a href="source/Store.html#cfg-Ext.data.Store-autoSave">autoSave</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to true causing the store to automatically save records to
the server when a record is modified (ie: becomes...</div><div class="long"><p>Defaults to <tt>true</tt> causing the store to automatically <a href="output/Ext.data.Store.html#Ext.data.Store-save" ext:member="save" ext:cls="Ext.data.Store">save</a> records to
the server when a record is modified (ie: becomes "dirty"). Specify <tt>false</tt> to manually call <a href="output/Ext.data.Store.html#Ext.data.Store-save" ext:member="save" ext:cls="Ext.data.Store">save</a>
to send all modifiedRecords to the server.</p>
<br><p><b>Note</b>: each CRUD action will be sent as a separate request.</p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#autoSave" ext:member="#autoSave" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-baseParams"></a><b><a href="source/Store.html#cfg-Ext.data.Store-baseParams">baseParams</a></b> : Object<div class="mdesc"><div class="short">An object containing properties which are to be sent as parameters
for every HTTP request.
Parameters are encoded as ...</div><div class="long"><p>An object containing properties which are to be sent as parameters
for <i>every</i> HTTP request.</p>
<p>Parameters are encoded as standard HTTP parameters using <a href="output/Ext.html#Ext-urlEncode" ext:member="urlEncode" ext:cls="Ext">Ext.urlEncode</a>.</p>
<p><b>Note</b>: <tt>baseParams</tt> will supersede any <tt>params</tt> provided in a
<tt><a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a></tt> request, see <tt><a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a></tt> for more details.</p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#baseParams" ext:member="#baseParams" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-batch"></a><b><a href="source/Store.html#cfg-Ext.data.Store-batch">batch</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to true (unless restful:true). Multiple
requests for each CRUD action (CREATE, READ, UPDATE and DESTROY) wil...</div><div class="long"><p>Defaults to <tt>true</tt> (unless <code><a href="output/Ext.data.Store.html#Ext.data.Store-restful" ext:member="restful" ext:cls="Ext.data.Store">restful</a>:true</code>). Multiple
requests for each CRUD action (CREATE, READ, UPDATE and DESTROY) will be combined
and sent as one transaction. Only applies when <code><a href="output/Ext.data.Store.html#Ext.data.Store-autoSave" ext:member="autoSave" ext:cls="Ext.data.Store">autoSave</a></code> is set
to <tt>false</tt>.</p>
<br><p>If Store is RESTful, the DataProxy is also RESTful, and a unique transaction is
generated for each record.</p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#batch" ext:member="#batch" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-data"></a><b><a href="source/Store.html#cfg-Ext.data.Store-data">data</a></b> : Array<div class="mdesc">An inline data object readable by the <code><a href="output/Ext.data.Store.html#Ext.data.Store-reader" ext:member="reader" ext:cls="Ext.data.Store">reader</a></code>.
Typically this option, or the <code><a href="output/Ext.data.Store.html#Ext.data.Store-url" ext:member="url" ext:cls="Ext.data.Store">url</a></code> option will be specified.</div></td><td class="msource"><a href="output/Ext.data.Store.html#data" ext:member="#data" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-proxy"></a><b><a href="source/Store.html#cfg-Ext.data.Store-proxy">proxy</a></b> : Ext.data.DataProxy<div class="mdesc">The <a href="output/Ext.data.DataProxy.html" ext:cls="Ext.data.DataProxy">DataProxy</a> object which provides
access to a data object.  See <code><a href="output/Ext.data.Store.html#Ext.data.Store-url" ext:member="url" ext:cls="Ext.data.Store">url</a></code>.</div></td><td class="msource"><a href="output/Ext.data.Store.html#proxy" ext:member="#proxy" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-pruneModifiedRecords"></a><b><a href="source/Store.html#cfg-Ext.data.Store-pruneModifiedRecords">pruneModifiedRecords</a></b> : Boolean<div class="mdesc"><div class="short">true to clear all modified record information each time
the store is loaded or when a record is removed (defaults to ...</div><div class="long"><tt>true</tt> to clear all modified record information each time
the store is loaded or when a record is removed (defaults to <tt>false</tt>). See <a href="output/Ext.data.Store.html#Ext.data.Store-getModifiedRecords" ext:member="getModifiedRecords" ext:cls="Ext.data.Store">getModifiedRecords</a>
for the accessor method to retrieve the modified records.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#pruneModifiedRecords" ext:member="#pruneModifiedRecords" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-remoteSort"></a><b><a href="source/Store.html#cfg-Ext.data.Store-remoteSort">remoteSort</a></b> : boolean<div class="mdesc"><div class="short">true if sorting is to be handled by requesting the Proxy
to provide a refreshed version of the data object in sorted ...</div><div class="long"><tt>true</tt> if sorting is to be handled by requesting the <tt><a href="output/Ext.data.Store.html#Ext.data.Store-proxy" ext:member="proxy" ext:cls="Ext.data.Store">Proxy</a></tt>
to provide a refreshed version of the data object in sorted order, as opposed to sorting the Record cache
in place (defaults to <tt>false</tt>).
<p>If <tt>remoteSort</tt> is <tt>true</tt>, then clicking on a <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Grid Column</a>'s
<a href="output/Ext.grid.Column.html#Ext.grid.Column-header" ext:member="header" ext:cls="Ext.grid.Column">header</a> causes the current page to be requested from the server appending
the following two parameters to the <b><tt><a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">params</a></tt></b>:<div class="mdetail-params"><ul>
<li><b><tt>sort</tt></b> : String<p class="sub-desc">The <tt>name</tt> (as specified in the Record's
<a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field definition</a>) of the field to sort on.</p></li>
<li><b><tt>dir</tt></b> : String<p class="sub-desc">The direction of the sort, "ASC" or "DESC" (case-sensitive).</p></li>
</ul></div></p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#remoteSort" ext:member="#remoteSort" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-restful"></a><b><a href="source/Store.html#cfg-Ext.data.Store-restful">restful</a></b> : Boolean<div class="mdesc"><div class="short">[false]
Defaults to false.  Set to true to have Store and set Proxy operate in a RESTful manner, utilizing the HTTP m...</div><div class="long">[false]
Defaults to <tt>false</tt>.  Set to <tt>true</tt> to have Store and set Proxy operate in a RESTful manner, utilizing the HTTP methods
GET, POST, PUT and DELETE for corresponding CREATE, READ, UPDATE and DESTROY actions.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#restful" ext:member="#restful" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-sortInfo"></a><b><a href="source/Store.html#cfg-Ext.data.Store-sortInfo">sortInfo</a></b> : Object<div class="mdesc"><div class="short">A config object to specify the sort order in the request of a Store's
load operation.  Note that for local sorting, t...</div><div class="long">A config object to specify the sort order in the request of a Store's
<a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> operation.  Note that for local sorting, the <tt>direction</tt> property is
case-sensitive. See also <a href="output/Ext.data.Store.html#Ext.data.Store-remoteSort" ext:member="remoteSort" ext:cls="Ext.data.Store">remoteSort</a> and <a href="output/Ext.data.Store.html#Ext.data.Store-paramNames" ext:member="paramNames" ext:cls="Ext.data.Store">paramNames</a>.
For example:<pre><code>sortInfo: {
    field: <em>"fieldName"</em>,
    direction: <em>"ASC"</em> <i>// or <em>"DESC"</em> (<b>case</b> sensitive <b>for</b> local sorting)</i>
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#sortInfo" ext:member="#sortInfo" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-storeId"></a><b><a href="source/Store.html#cfg-Ext.data.Store-storeId">storeId</a></b> : String<div class="mdesc"><div class="short">If passed, the id to use to register with the StoreMgr.
Note: if a (deprecated) id is specified it will supersede the...</div><div class="long">If passed, the id to use to register with the <b><a href="output/Ext.StoreMgr.html" ext:cls="Ext.StoreMgr">StoreMgr</a></b>.
<p><b>Note</b>: if a (deprecated) <tt><a href="output/Ext.data.Store.html#Ext.data.Store-id" ext:member="id" ext:cls="Ext.data.Store">id</a></tt> is specified it will supersede the <tt>storeId</tt>
assignment.</p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#storeId" ext:member="#storeId" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-url"></a><b><a href="source/Store.html#cfg-Ext.data.Store-url">url</a></b> : String<div class="mdesc"><div class="short">If a proxy is not specified the url will be used to
implicitly configure a HttpProxy if an url is specified.
Typicall...</div><div class="long">If a <tt><a href="output/Ext.data.Store.html#Ext.data.Store-proxy" ext:member="proxy" ext:cls="Ext.data.Store">proxy</a></tt> is not specified the <tt>url</tt> will be used to
implicitly configure a <a href="output/Ext.data.HttpProxy.html" ext:cls="Ext.data.HttpProxy">HttpProxy</a> if an <tt>url</tt> is specified.
Typically this option, or the <code><a href="output/Ext.data.Store.html#Ext.data.Store-data" ext:member="data" ext:cls="Ext.data.Store">data</a></code> option will be specified.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#url" ext:member="#url" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-writer"></a><b><a href="source/Store.html#cfg-Ext.data.Store-writer">writer</a></b> : Ext.data.DataWriter<div class="mdesc"><div class="short">The Writer object which processes a record object for being written
to the server-side database.
When a writer is ins...</div><div class="long"><p>The <a href="output/Ext.data.DataWriter.html" ext:cls="Ext.data.DataWriter">Writer</a> object which processes a record object for being written
to the server-side database.</p>
<br><p>When a writer is installed into a Store the <a href="output/Ext.data.Store.html#Ext.data.Store-add" ext:member="add" ext:cls="Ext.data.Store">add</a>, <a href="output/Ext.data.Store.html#Ext.data.Store-remove" ext:member="remove" ext:cls="Ext.data.Store">remove</a>, and <a href="output/Ext.data.Store.html#Ext.data.Store-update" ext:member="update" ext:cls="Ext.data.Store">update</a>
events on the store are monitored in order to remotely <a href="output/Ext.data.Store.html#Ext.data.Store-createRecords" ext:member="createRecords" ext:cls="Ext.data.Store">create records</a>,
<a href="output/Ext.data.Store.html#Ext.data.Store-destroyRecord" ext:member="destroyRecord" ext:cls="Ext.data.Store">destroy records</a>, or <a href="output/Ext.data.Store.html#Ext.data.Store-updateRecord" ext:member="updateRecord" ext:cls="Ext.data.Store">update records</a>.</p>
<br><p>The proxy for this store will relay any <a href="output/Ext.data.Store.html#Ext.data.Store-writexception" ext:member="writexception" ext:cls="Ext.data.Store">writexception</a> events to this store.</p>
<br><p>Sample implementation:
<pre><code><b>var</b> writer = <b>new</b> <a href="output/Ext.data.JsonWriter.html" ext:cls="Ext.data.JsonWriter">Ext.data.JsonWriter</a>({
    returnJson: true,
    writeAllFields: true <i>// write all fields, not just those that changed</i>
});

<i>// Typical Store collecting the Proxy, Reader and Writer together.</i>
<b>var</b> store = <b>new</b> Ext.data.Store({
    storeId: <em>'user'</em>,
    root: <em>'records'</em>,
    proxy: proxy,
    reader: reader,
    writer: writer,     <i>// <-- plug a DataWriter into the store just as you would a Reader</i>
    paramsAsHash: true,
    autoSave: false    <i>// <-- false to delay executing create, update, destroy requests</i>
                        <i>//     until specifically told to <b>do</b> so.</i>
});</code></pre></p></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#writer" ext:member="#writer" ext:cls="Ext.data.Store">Store</a></td></tr></tbody></table><a id="Ext.data.ArrayStore-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-baseParams"></a><b><a href="source/Store.html#prop-Ext.data.Store-baseParams">baseParams</a></b> : Object<div class="mdesc"><div class="short">An object containing properties which are used as parameters for every HTTP request.
Note: baseParams will supersede ...</div><div class="long">An object containing properties which are used as parameters for every HTTP request.
<b>Note</b>: <tt>baseParams</tt> will supersede any <tt>params</tt> provided in a
<tt><a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a></tt> request, see <tt><a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a></tt> for more details.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#baseParams" ext:member="#baseParams" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-fields"></a><b><a href="source/Store.html#prop-Ext.data.Store-fields">fields</a></b> : Ext.util.MixedCollection<div class="mdesc">A <a href="output/Ext.util.MixedCollection.html" ext:cls="Ext.util.MixedCollection">MixedCollection</a> containing the defined <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a>s
for the <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Records</a> stored in this Store. Read-only.</div></td><td class="msource"><a href="output/Ext.data.Store.html#fields" ext:member="#fields" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-lastOptions"></a><b><a href="source/Store.html#prop-Ext.data.Store-lastOptions">lastOptions</a></b> : Object<div class="mdesc"><div class="short">Contains the last options object used as the parameter to the load method. See load
for the details of what this may ...</div><div class="long">Contains the last options object used as the parameter to the <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> method. See <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a>
for the details of what this may contain. This may be useful for accessing any params which were used
to load the current Record cache.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#lastOptions" ext:member="#lastOptions" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-paramNames"></a><b><a href="source/Store.html#prop-Ext.data.Store-paramNames">paramNames</a></b> : Object<div class="mdesc"><div class="short">An object containing properties which specify the names of the paging and
sorting parameters passed to remote servers...</div><div class="long"><p>An object containing properties which specify the names of the paging and
sorting parameters passed to remote servers when loading blocks of data. By default, this
object takes the following form:</p><pre><code>{
    start : <em>"start"</em>,  <i>// The parameter name which specifies the start row</i>
    limit : <em>"limit"</em>,  <i>// The parameter name which specifies number of rows to <b>return</b></i>
    sort : <em>"sort"</em>,    <i>// The parameter name which specifies the column to sort on</i>
    dir : <em>"dir"</em>       <i>// The parameter name which specifies the sort direction</i>
}</code></pre>
<p>The server must produce the requested data block upon receipt of these parameter names.
If different parameter names are required, this property can be overriden using a configuration
property.</p>
<p>A <a href="output/Ext.PagingToolbar.html" ext:cls="Ext.PagingToolbar">PagingToolbar</a> bound to this Store uses this property to determine
the parameter names to use in its <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">requests</a>.</div></div></td><td class="msource"><a href="output/Ext.data.Store.html#paramNames" ext:member="#paramNames" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-recordType"></a><b><a href="source/Store.html#prop-Ext.data.Store-recordType">recordType</a></b> : Function<div class="mdesc"><div class="short">The Record constructor as supplied to (or created by) the
Reader. Read-only.
If the Reader was constructed by passing...</div><div class="long">The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a> constructor as supplied to (or created by) the
<a href="output/Ext.data.DataReader.html" ext:cls="Ext.data.DataReader">Reader</a>. Read-only.
<p>If the Reader was constructed by passing in an Array of <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Ext.data.Field</a> definition objects,
instead of a Record constructor, it will implicitly create a Record constructor from that Array (see
<a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a>.<a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">create</a> for additional details).</p>
<p>This property may be used to create new Records of the type held in this Store, for example:</p><pre><code><i>// create the data store</i>
<b>var</b> store = <b>new</b> Ext.data.ArrayStore({
    autoDestroy: true,
    fields: [
       {name: <em>'company'</em>},
       {name: <em>'price'</em>, type: <em>'float'</em>},
       {name: <em>'change'</em>, type: <em>'float'</em>},
       {name: <em>'pctChange'</em>, type: <em>'float'</em>},
       {name: <em>'lastChange'</em>, type: <em>'date'</em>, dateFormat: <em>'n/j h:ia'</em>}
    ]
});
store.loadData(myData);

<i>// create the Grid</i>
<b>var</b> grid = <b>new</b> Ext.grid.EditorGridPanel({
    store: store,
    colModel: <b>new</b> Ext.grid.ColumnModel({
        columns: [
            {id:<em>'company'</em>, header: <em>"Company"</em>, width: 160, dataIndex: <em>'company'</em>},
            {header: <em>"Price"</em>, renderer: <em>'usMoney'</em>, dataIndex: <em>'price'</em>},
            {header: <em>"Change"</em>, renderer: change, dataIndex: <em>'change'</em>},
            {header: <em>"% Change"</em>, renderer: pctChange, dataIndex: <em>'pctChange'</em>},
            {header: <em>"Last Updated"</em>, width: 85,
                renderer: Ext.util.Format.dateRenderer(<em>'m/d/Y'</em>),
                dataIndex: <em>'lastChange'</em>}
        ],
        defaults: {
            sortable: true,
            width: 75
        }
    }),
    autoExpandColumn: <em>'company'</em>, <i>// match the id specified <b>in</b> the column model</i>
    height:350,
    width:600,
    title:<em>'Array Grid'</em>,
    tbar: [{
        text: <em>'Add Record'</em>,
        handler : <b>function</b>(){
            <b>var</b> defaultData = {
                change: 0,
                company: <em>'New Company'</em>,
                lastChange: (<b>new</b> Date()).clearTime(),
                pctChange: 0,
                price: 10
            };
            <b>var</b> recId = 3; <i>// provide unique id</i>
            <b>var</b> p = <b>new</b> store.recordType(defaultData, recId); <i>// create <b>new</b> record</i>
            grid.stopEditing();
            store.insert(0, p); <i>// add <b>new</b> record to the store</i>
            grid.startEditing(0, 0);
        }
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#recordType" ext:member="#recordType" ext:cls="Ext.data.Store">Store</a></td></tr></tbody></table><a id="Ext.data.ArrayStore-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.ArrayStore-ArrayStore"></a><b><a href="source/ArrayStore.html#cls-Ext.data.ArrayStore">ArrayStore</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div><table><tr><td class="label">xtype:</td><td class="hd-info">arraystore</td></tr></table></div></div></td><td class="msource">ArrayStore</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-Store.Error"></a><b><a href="source/Store.html#method-Ext.data.Store-Store.Error">Store.Error</a></b>(&nbsp;<code>String&nbsp;name</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Store Error extension.
constructor</div><div class="long">Store Error extension.
constructor<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#Store.Error" ext:member="#Store.Error" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-add"></a><b><a href="source/Store.html#method-Ext.data.Store-add">add</a></b>(&nbsp;<code>Ext.data.Record[]&nbsp;records</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Add Records to the Store and fires the add event.</div><div class="long">Add Records to the Store and fires the <a href="output/Ext.data.Store.html#Ext.data.Store-add" ext:member="add" ext:cls="Ext.data.Store">add</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>records</code> : Ext.data.Record[]<div class="sub-desc">An Array of Ext.data.Record objects to add to the cache. See <a href="output/Ext.data.Store.html#Ext.data.Store-recordType" ext:member="recordType" ext:cls="Ext.data.Store">recordType</a>.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#add" ext:member="#add" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-addSorted"></a><b><a href="source/Store.html#method-Ext.data.Store-addSorted">addSorted</a></b>(&nbsp;<code>Ext.data.Record&nbsp;record</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">(Local sort only) Inserts the passed Record into the Store at the index where it
should go based on the current sort ...</div><div class="long">(Local sort only) Inserts the passed Record into the Store at the index where it
should go based on the current sort information.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Ext.data.Record<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#addSorted" ext:member="#addSorted" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-clearFilter"></a><b><a href="source/Store.html#method-Ext.data.Store-clearFilter">clearFilter</a></b>(&nbsp;<code>Boolean&nbsp;suppressEvent</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Revert to a view of the Record cache with no filtering applied.</div><div class="long">Revert to a view of the Record cache with no filtering applied.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>suppressEvent</code> : Boolean<div class="sub-desc">If <tt>true</tt> the filter is cleared silently without firing the
<a href="output/Ext.data.Store.html#Ext.data.Store-datachanged" ext:member="datachanged" ext:cls="Ext.data.Store">datachanged</a> event.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#clearFilter" ext:member="#clearFilter" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-collect"></a><b><a href="source/Store.html#method-Ext.data.Store-collect">collect</a></b>(&nbsp;<code>String&nbsp;dataIndex</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;allowNull</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;bypassFilter</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Collects unique values for a particular dataIndex from this store.</div><div class="long">Collects unique values for a particular dataIndex from this store.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>dataIndex</code> : String<div class="sub-desc">The property to collect</div></li><li><code>allowNull</code> : Boolean<div class="sub-desc">(optional) Pass true to allow null, undefined or empty string values</div></li><li><code>bypassFilter</code> : Boolean<div class="sub-desc">(optional) Pass true to collect from all records, even ones which are filtered</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">An array of the unique values</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#collect" ext:member="#collect" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-commitChanges"></a><b><a href="source/Store.html#method-Ext.data.Store-commitChanges">commitChanges</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Commit all Records with outstanding changes. To handle updates for changes,
subscribe to the Store's update event, an...</div><div class="long">Commit all Records with <a href="output/Ext.data.Store.html#Ext.data.Store-getModifiedRecords" ext:member="getModifiedRecords" ext:cls="Ext.data.Store">outstanding changes</a>. To handle updates for changes,
subscribe to the Store's <a href="output/Ext.data.Store.html#Ext.data.Store-update" ext:member="update" ext:cls="Ext.data.Store">update event</a>, and perform updating when the third parameter is
Ext.data.Record.COMMIT.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#commitChanges" ext:member="#commitChanges" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-destroy"></a><b><a href="source/Store.html#method-Ext.data.Store-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys the store.</div><div class="long">Destroys the store.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#destroy" ext:member="#destroy" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-each"></a><b><a href="source/Store.html#method-Ext.data.Store-each">each</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Calls the specified function for each of the Records in the cache.</div><div class="long">Calls the specified function for each of the <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Records</a> in the cache.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call. The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a> is passed as the first parameter.
Returning <tt>false</tt> aborts and exits the iteration.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to call the function (defaults to the <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>).</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#each" ext:member="#each" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-filter"></a><b><a href="source/Store.html#method-Ext.data.Store-filter">filter</a></b>(&nbsp;<code>String&nbsp;field</code>,&nbsp;<code>String/RegExp&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anyMatch</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;caseSensitive</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Filter the records by a specified property.</div><div class="long">Filter the <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">records</a> by a specified property.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>field</code> : String<div class="sub-desc">A field on your records</div></li><li><code>value</code> : String/RegExp<div class="sub-desc">Either a string that the field should begin with, or a RegExp to test
against the field.</div></li><li><code>anyMatch</code> : Boolean<div class="sub-desc">(optional) <tt>true</tt> to match any part not just the beginning</div></li><li><code>caseSensitive</code> : Boolean<div class="sub-desc">(optional) <tt>true</tt> for case sensitive comparison</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#filter" ext:member="#filter" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-filterBy"></a><b><a href="source/Store.html#method-Ext.data.Store-filterBy">filterBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Filter by a function. The specified function will be called for each
Record in this Store. If the function returns tr...</div><div class="long">Filter by a function. The specified function will be called for each
Record in this Store. If the function returns <tt>true</tt> the Record is included,
otherwise it is filtered out.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to be called. It will be passed the following parameters:<ul>
<li><b>record</b> : Ext.data.Record<p class="sub-desc">The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">record</a>
to test for filtering. Access field values using <a href="output/Ext.data.Record.html#Ext.data.Record-get" ext:member="get" ext:cls="Ext.data.Record">Ext.data.Record.get</a>.</p></li>
<li><b>id</b> : Object<p class="sub-desc">The ID of the Record passed.</p></li>
</ul></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to this)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#filterBy" ext:member="#filterBy" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-find"></a><b><a href="source/Store.html#method-Ext.data.Store-find">find</a></b>(&nbsp;<code>String&nbsp;property</code>,&nbsp;<code>String/RegExp&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;startIndex</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anyMatch</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;caseSensitive</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Finds the index of the first matching record in this store by a specific property/value.</div><div class="long">Finds the index of the first matching record in this store by a specific property/value.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>property</code> : String<div class="sub-desc">A property on your objects</div></li><li><code>value</code> : String/RegExp<div class="sub-desc">Either a string that the property value
should begin with, or a RegExp to test against the property.</div></li><li><code>startIndex</code> : Number<div class="sub-desc">(optional) The index to start searching at</div></li><li><code>anyMatch</code> : Boolean<div class="sub-desc">(optional) True to match any part of the string, not just the beginning</div></li><li><code>caseSensitive</code> : Boolean<div class="sub-desc">(optional) True for case sensitive comparison</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The matched index or -1</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#find" ext:member="#find" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-findBy"></a><b><a href="source/Store.html#method-Ext.data.Store-findBy">findBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;startIndex</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Find the index of the first matching Record in this Store by a function.
If the function returns true it is considere...</div><div class="long">Find the index of the first matching Record in this Store by a function.
If the function returns <tt>true</tt> it is considered a match.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to be called. It will be passed the following parameters:<ul>
<li><b>record</b> : Ext.data.Record<p class="sub-desc">The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">record</a>
to test for filtering. Access field values using <a href="output/Ext.data.Record.html#Ext.data.Record-get" ext:member="get" ext:cls="Ext.data.Record">Ext.data.Record.get</a>.</p></li>
<li><b>id</b> : Object<p class="sub-desc">The ID of the Record passed.</p></li>
</ul></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to this)</div></li><li><code>startIndex</code> : Number<div class="sub-desc">(optional) The index to start searching at</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The matched index or -1</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#findBy" ext:member="#findBy" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getAt"></a><b><a href="source/Store.html#method-Ext.data.Store-getAt">getAt</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        Ext.data.Record<div class="mdesc"><div class="short">Get the Record at the specified index.</div><div class="long">Get the Record at the specified index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index of the Record to find.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Record</code><div class="sub-desc">The Record at the passed index. Returns undefined if not found.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getAt" ext:member="#getAt" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getById"></a><b><a href="source/Store.html#method-Ext.data.Store-getById">getById</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Ext.data.Record<div class="mdesc"><div class="short">Get the Record with the specified id.</div><div class="long">Get the Record with the specified id.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The id of the Record to find.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Record</code><div class="sub-desc">The Record with the passed id. Returns undefined if not found.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getById" ext:member="#getById" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getCount"></a><b><a href="source/Store.html#method-Ext.data.Store-getCount">getCount</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the number of cached records.
If using paging, this may not be the total size of the dataset. If the data object...</div><div class="long">Gets the number of cached records.
<p>If using paging, this may not be the total size of the dataset. If the data object
used by the Reader contains the dataset size, then the <a href="output/Ext.data.Store.html#Ext.data.Store-getTotalCount" ext:member="getTotalCount" ext:cls="Ext.data.Store">getTotalCount</a> function returns
the dataset size.  <b>Note</b>: see the Important note in <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The number of Records in the Store's cache.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getCount" ext:member="#getCount" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getModifiedRecords"></a><b><a href="source/Store.html#method-Ext.data.Store-getModifiedRecords">getModifiedRecords</a></b>()
    :
                                        Ext.data.Record[]<div class="mdesc"><div class="short">Gets all records modified since the last commit.  Modified records are
persisted across load operations (e.g., during...</div><div class="long">Gets all <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">records</a> modified since the last commit.  Modified records are
persisted across load operations (e.g., during paging). <b>Note</b>: deleted records are not
included.  See also <tt><a href="output/Ext.data.Store.html#Ext.data.Store-pruneModifiedRecords" ext:member="pruneModifiedRecords" ext:cls="Ext.data.Store">pruneModifiedRecords</a></tt> and
<a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a><tt><a href="output/Ext.data.Record.html#Ext.data.Record-markDirty" ext:member="markDirty" ext:cls="Ext.data.Record">markDirty</a>.</tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Record[]</code><div class="sub-desc">An array of {@link Ext.data.Record Records} containing outstanding
modifications.  To obtain modified fields within a modified record see
{@link Ext.data.Record}&lt;tt&gt;{@link Ext.data.Record#modified modified}.&lt;/tt&gt;.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getModifiedRecords" ext:member="#getModifiedRecords" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getRange"></a><b><a href="source/Store.html#method-Ext.data.Store-getRange">getRange</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;startIndex</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;endIndex</code>]</span>&nbsp;)
    :
                                        Ext.data.Record[]<div class="mdesc"><div class="short">Returns a range of Records between specified indices.</div><div class="long">Returns a range of Records between specified indices.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>startIndex</code> : Number<div class="sub-desc">(optional) The starting index (defaults to 0)</div></li><li><code>endIndex</code> : Number<div class="sub-desc">(optional) The ending index (defaults to the last Record in the Store)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Record[]</code><div class="sub-desc">An array of Records</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getRange" ext:member="#getRange" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getSortState"></a><b><a href="source/Store.html#method-Ext.data.Store-getSortState">getSortState</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns an object describing the current sort state of this Store.</div><div class="long">Returns an object describing the current sort state of this Store.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The sort state of the Store. An object with two properties:&lt;ul&gt;
&lt;li&gt;&lt;b&gt;field : String&lt;p class="sub-desc"&gt;The name of the field by which the Records are sorted.&lt;/p&gt;&lt;/li&gt;
&lt;li&gt;&lt;b&gt;direction : String&lt;p class="sub-desc"&gt;The sort order, "ASC" or "DESC" (case-sensitive).&lt;/p&gt;&lt;/li&gt;
&lt;/ul&gt;
See &lt;tt&gt;{@link #sortInfo}&lt;/tt&gt; for additional details.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getSortState" ext:member="#getSortState" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-getTotalCount"></a><b><a href="source/Store.html#method-Ext.data.Store-getTotalCount">getTotalCount</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the total number of records in the dataset as returned by the server.
If using paging, for this to be accurate, ...</div><div class="long">Gets the total number of records in the dataset as returned by the server.
<p>If using paging, for this to be accurate, the data object used by the <a href="output/Ext.data.Store.html#Ext.data.Store-reader" ext:member="reader" ext:cls="Ext.data.Store">Reader</a>
must contain the dataset size. For remote data sources, the value for this property
(<tt>totalProperty</tt> for <a href="output/Ext.data.JsonReader.html" ext:cls="Ext.data.JsonReader">JsonReader</a>,
<tt>totalRecords</tt> for <a href="output/Ext.data.XmlReader.html" ext:cls="Ext.data.XmlReader">XmlReader</a>) shall be returned by a query on the server.
<b>Note</b>: see the Important note in <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The number of Records as specified in the data object passed to the Reader
by the Proxy.
&lt;p&gt;&lt;b&gt;Note&lt;/b&gt;: this value is not updated when changing the contents of the Store locally.&lt;/p&gt;</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#getTotalCount" ext:member="#getTotalCount" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-indexOf"></a><b><a href="source/Store.html#method-Ext.data.Store-indexOf">indexOf</a></b>(&nbsp;<code>Ext.data.Record&nbsp;record</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Get the index within the cache of the passed Record.</div><div class="long">Get the index within the cache of the passed Record.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Ext.data.Record<div class="sub-desc">The Ext.data.Record object to find.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The index of the passed Record. Returns -1 if not found.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#indexOf" ext:member="#indexOf" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-indexOfId"></a><b><a href="source/Store.html#method-Ext.data.Store-indexOfId">indexOfId</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Get the index within the cache of the Record with the passed id.</div><div class="long">Get the index within the cache of the Record with the passed id.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The id of the Record to find.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The index of the Record. Returns -1 if not found.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#indexOfId" ext:member="#indexOfId" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-insert"></a><b><a href="source/Store.html#method-Ext.data.Store-insert">insert</a></b>(&nbsp;<code>Number&nbsp;index</code>,&nbsp;<code>Ext.data.Record[]&nbsp;records</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Inserts Records into the Store at the given index and fires the add event.</div><div class="long">Inserts Records into the Store at the given index and fires the <a href="output/Ext.data.Store.html#Ext.data.Store-add" ext:member="add" ext:cls="Ext.data.Store">add</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The start index at which to insert the passed Records.</div></li><li><code>records</code> : Ext.data.Record[]<div class="sub-desc">An Array of Ext.data.Record objects to add to the cache.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#insert" ext:member="#insert" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-isFiltered"></a><b><a href="source/Store.html#method-Ext.data.Store-isFiltered">isFiltered</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this store is currently filtered</div><div class="long">Returns true if this store is currently filtered<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#isFiltered" ext:member="#isFiltered" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-load"></a><b><a href="source/Store.html#method-Ext.data.Store-load">load</a></b>(&nbsp;<code>Object&nbsp;options</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Loads the Record cache from the configured proxy using the configured reader.
If using remote paging, then the first ...</div><div class="long">Loads the Record cache from the configured <tt><a href="output/Ext.data.Store.html#Ext.data.Store-proxy" ext:member="proxy" ext:cls="Ext.data.Store">proxy</a></tt> using the configured <tt><a href="output/Ext.data.Store.html#Ext.data.Store-reader" ext:member="reader" ext:cls="Ext.data.Store">reader</a></tt>.
<p>If using remote paging, then the first load call must specify the <tt>start</tt>
and <tt>limit</tt> properties in the options.params property to establish the initial
position within the dataset, and the number of Records to cache on each read from the Proxy.</p>
<p><b>Important</b>: loading is asynchronous, so this call will return before the new data has been
loaded. To perform any post-processing where information from the load call is required, use the
<tt>callback</tt> function, or <a href="output/Ext.util.Observable.html#Ext.util.Observable-listeners" ext:member="listeners" ext:cls="Ext.util.Observable">a "load" event handler</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">An object containing properties which control loading options:<ul>
<li><b><tt>params</tt></b> :Object<div class="sub-desc"><p>An object containing properties to pass as HTTP
parameters to a remote data source. <b>Note</b>: <tt><a href="output/Ext.data.Store.html#Ext.data.Store-baseParams" ext:member="baseParams" ext:cls="Ext.data.Store">baseParams</a></tt> will supersede specified
<tt>parameters</tt>.</p>
<p>Parameters are encoded as standard HTTP parameters using <a href="output/Ext.html#Ext-urlEncode" ext:member="urlEncode" ext:cls="Ext">Ext.urlEncode</a>.</p></div></li>
<li><b><tt>callback</tt></b> : Function<div class="sub-desc"><p>A function to be called after the Records
have been loaded. The <tt>callback</tt> is called after the load event and is passed the following arguments:<ul>
<li><tt>r</tt> : Ext.data.Record[]</li>
<li><tt>options</tt>: Options object from the load call</li>
<li><tt>success</tt>: Boolean success indicator</li></ul></p></div></li>
<li><b><tt>scope</tt></b> : Object<div class="sub-desc"><p>Scope with which to call the callback (defaults
to the Store object)</p></div></li>
<li><b><tt>add</tt></b> : Boolean<div class="sub-desc"><p>Indicator to append loaded records rather than
replace the current cache.  <b>Note</b>: see note for <tt><a href="output/Ext.data.Store.html#Ext.data.Store-loadData" ext:member="loadData" ext:cls="Ext.data.Store">loadData</a></tt></p></div></li>
</ul></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">If the &lt;i&gt;developer&lt;/i&gt; provided &lt;tt&gt;{@link #beforeload}&lt;/tt&gt; event handler returns
&lt;tt&gt;false&lt;/tt&gt;, the load call will abort and will return &lt;tt&gt;false&lt;/tt&gt;; otherwise will return &lt;tt&gt;true&lt;/tt&gt;.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#load" ext:member="#load" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-loadData"></a><b><a href="source/Store.html#method-Ext.data.Store-loadData">loadData</a></b>(&nbsp;<code>Object&nbsp;data</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;append</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Loads data from a passed data block and fires the load event. A Reader
which understands the format of the data must ...</div><div class="long">Loads data from a passed data block and fires the <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> event. A <a href="output/Ext.data.Reader.html" ext:cls="Ext.data.Reader">Reader</a>
which understands the format of the data must have been configured in the constructor.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc">The data block from which to read the Records.  The format of the data expected
is dependent on the type of <a href="output/Ext.data.Reader.html" ext:cls="Ext.data.Reader">Reader</a> that is configured and should correspond to
that <a href="output/Ext.data.Reader.html" ext:cls="Ext.data.Reader">Reader</a>'s <tt><a href="output/Ext.data.Reader.html#Ext.data.Reader-readRecords" ext:member="readRecords" ext:cls="Ext.data.Reader">Ext.data.Reader.readRecords</a></tt> parameter.</div></li><li><code>append</code> : Boolean<div class="sub-desc">(Optional) <tt>true</tt> to append the new Records rather the default to replace
the existing cache.
<b>Note</b>: that Records in a Store are keyed by their <a href="output/Ext.data.Record.html#Ext.data.Record-id" ext:member="id" ext:cls="Ext.data.Record">id</a>, so added Records
with ids which are already present in the Store will <i>replace</i> existing Records. Records with new,
unique ids will be added.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#loadData" ext:member="#loadData" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-query"></a><b><a href="source/Store.html#method-Ext.data.Store-query">query</a></b>(&nbsp;<code>String&nbsp;field</code>,&nbsp;<code>String/RegExp&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anyMatch</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;caseSensitive</code>]</span>&nbsp;)
    :
                                        MixedCollection<div class="mdesc"><div class="short">Query the records by a specified property.</div><div class="long">Query the records by a specified property.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>field</code> : String<div class="sub-desc">A field on your records</div></li><li><code>value</code> : String/RegExp<div class="sub-desc">Either a string that the field
should begin with, or a RegExp to test against the field.</div></li><li><code>anyMatch</code> : Boolean<div class="sub-desc">(optional) True to match any part not just the beginning</div></li><li><code>caseSensitive</code> : Boolean<div class="sub-desc">(optional) True for case sensitive comparison</div></li></ul><strong>Returns:</strong><ul><li><code>MixedCollection</code><div class="sub-desc">Returns an Ext.util.MixedCollection of the matched records</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#query" ext:member="#query" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-queryBy"></a><b><a href="source/Store.html#method-Ext.data.Store-queryBy">queryBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        MixedCollection<div class="mdesc"><div class="short">Query the cached records in this Store using a filtering function. The specified function
will be called with each re...</div><div class="long">Query the cached records in this Store using a filtering function. The specified function
will be called with each record in this Store. If the function returns <tt>true</tt> the record is
included in the results.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to be called. It will be passed the following parameters:<ul>
<li><b>record</b> : Ext.data.Record<p class="sub-desc">The <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">record</a>
to test for filtering. Access field values using <a href="output/Ext.data.Record.html#Ext.data.Record-get" ext:member="get" ext:cls="Ext.data.Record">Ext.data.Record.get</a>.</p></li>
<li><b>id</b> : Object<p class="sub-desc">The ID of the Record passed.</p></li>
</ul></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to this)</div></li></ul><strong>Returns:</strong><ul><li><code>MixedCollection</code><div class="sub-desc">Returns an Ext.util.MixedCollection of the matched records</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#queryBy" ext:member="#queryBy" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-rejectChanges"></a><b><a href="source/Store.html#method-Ext.data.Store-rejectChanges">rejectChanges</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Reject outstanding changes on all modified records.</div><div class="long"><a href="output/Ext.data.Record.html#Ext.data.Record-reject" ext:member="reject" ext:cls="Ext.data.Record">Reject</a> outstanding changes on all <a href="output/Ext.data.Store.html#Ext.data.Store-getModifiedRecords" ext:member="getModifiedRecords" ext:cls="Ext.data.Store">modified records</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#rejectChanges" ext:member="#rejectChanges" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-reload"></a><b><a href="source/Store.html#method-Ext.data.Store-reload">reload</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Reloads the Record cache from the configured Proxy using the configured Reader and
the options from the last load ope...</div><div class="long"><p>Reloads the Record cache from the configured Proxy using the configured <a href="output/Ext.data.Reader.html" ext:cls="Ext.data.Reader">Reader</a> and
the options from the last load operation performed.</p>
<p><b>Note</b>: see the Important note in <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">(optional) An <tt>Object</tt> containing <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">loading options</a> which may
override the options used in the last <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> operation. See <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> for details (defaults to
<tt>null</tt>, in which case the <a href="output/Ext.data.Store.html#Ext.data.Store-lastOptions" ext:member="lastOptions" ext:cls="Ext.data.Store">lastOptions</a> are used).</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#reload" ext:member="#reload" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-remove"></a><b><a href="source/Store.html#method-Ext.data.Store-remove">remove</a></b>(&nbsp;<code>Ext.data.Record&nbsp;record</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Remove a Record from the Store and fires the remove event.</div><div class="long">Remove a Record from the Store and fires the <a href="output/Ext.data.Store.html#Ext.data.Store-remove" ext:member="remove" ext:cls="Ext.data.Store">remove</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>record</code> : Ext.data.Record<div class="sub-desc">The Ext.data.Record object to remove from the cache.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#remove" ext:member="#remove" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-removeAll"></a><b><a href="source/Store.html#method-Ext.data.Store-removeAll">removeAll</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Remove all Records from the Store and fires the clear event.</div><div class="long">Remove all Records from the Store and fires the <a href="output/Ext.data.Store.html#Ext.data.Store-clear" ext:member="clear" ext:cls="Ext.data.Store">clear</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#removeAll" ext:member="#removeAll" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-removeAt"></a><b><a href="source/Store.html#method-Ext.data.Store-removeAt">removeAt</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Remove a Record from the Store at the specified index. Fires the remove event.</div><div class="long">Remove a Record from the Store at the specified index. Fires the <a href="output/Ext.data.Store.html#Ext.data.Store-remove" ext:member="remove" ext:cls="Ext.data.Store">remove</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index of the record to remove.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#removeAt" ext:member="#removeAt" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-save"></a><b><a href="source/Store.html#method-Ext.data.Store-save">save</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Saves all pending changes to the store.  If the commensurate Ext.data.Api.actions action is not configured, then
the ...</div><div class="long">Saves all pending changes to the store.  If the commensurate Ext.data.Api.actions action is not configured, then
the configured <code><a href="output/Ext.data.Store.html#Ext.data.Store-url" ext:member="url" ext:cls="Ext.data.Store">url</a></code> will be used.
<pre>
change            url
---------------   --------------------
removed records   Ext.data.Api.actions.destroy
phantom records   Ext.data.Api.actions.create
<a href="output/Ext.data.Store.html#Ext.data.Store-getModifiedRecords" ext:member="getModifiedRecords" ext:cls="Ext.data.Store">modified records</a>  Ext.data.Api.actions.update
</pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#save" ext:member="#save" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-setBaseParam"></a><b><a href="source/Store.html#method-Ext.data.Store-setBaseParam">setBaseParam</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Set the value for a property name in this store's baseParams.  Usage:myStore.setBaseParam('foo', {bar:3});</div><div class="long">Set the value for a property name in this store's <a href="output/Ext.data.Store.html#Ext.data.Store-baseParams" ext:member="baseParams" ext:cls="Ext.data.Store">baseParams</a>.  Usage:</p><pre><code>myStore.setBaseParam(<em>'foo'</em>, {bar:3});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">Name of the property to assign</div></li><li><code>value</code> : Mixed<div class="sub-desc">Value to assign the <tt>name</tt>d property</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#setBaseParam" ext:member="#setBaseParam" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-setDefaultSort"></a><b><a href="source/Store.html#method-Ext.data.Store-setDefaultSort">setDefaultSort</a></b>(&nbsp;<code>String&nbsp;fieldName</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;dir</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the default sort column and order to be used by the next load operation.</div><div class="long">Sets the default sort column and order to be used by the next <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> operation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fieldName</code> : String<div class="sub-desc">The name of the field to sort by.</div></li><li><code>dir</code> : String<div class="sub-desc">(optional) The sort order, "ASC" or "DESC" (case-sensitive, defaults to <tt>"ASC"</tt>)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#setDefaultSort" ext:member="#setDefaultSort" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-sort"></a><b><a href="source/Store.html#method-Ext.data.Store-sort">sort</a></b>(&nbsp;<code>String&nbsp;fieldName</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;dir</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sort the Records.
If remote sorting is used, the sort is performed on the server, and the cache is reloaded. If local...</div><div class="long">Sort the Records.
If remote sorting is used, the sort is performed on the server, and the cache is reloaded. If local
sorting is used, the cache is sorted internally. See also <a href="output/Ext.data.Store.html#Ext.data.Store-remoteSort" ext:member="remoteSort" ext:cls="Ext.data.Store">remoteSort</a> and <a href="output/Ext.data.Store.html#Ext.data.Store-paramNames" ext:member="paramNames" ext:cls="Ext.data.Store">paramNames</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fieldName</code> : String<div class="sub-desc">The name of the field to sort by.</div></li><li><code>dir</code> : String<div class="sub-desc">(optional) The sort order, "ASC" or "DESC" (case-sensitive, defaults to <tt>"ASC"</tt>)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#sort" ext:member="#sort" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-sum"></a><b><a href="source/Store.html#method-Ext.data.Store-sum">sum</a></b>(&nbsp;<code>String&nbsp;property</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;start</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;end</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Sums the value of property for each record between start
and end and returns the result.</div><div class="long">Sums the value of <tt>property</tt> for each <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">record</a> between <tt>start</tt>
and <tt>end</tt> and returns the result.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>property</code> : String<div class="sub-desc">A field in each record</div></li><li><code>start</code> : Number<div class="sub-desc">(optional) The record index to start at (defaults to <tt>0</tt>)</div></li><li><code>end</code> : Number<div class="sub-desc">(optional) The last record index to include (defaults to length - 1)</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The sum</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#sum" ext:member="#sum" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.data.ArrayStore-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-add"></a><b><a href="source/Store.html#event-Ext.data.Store-add">add</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>,&nbsp;<code>Ext.data.Record[]&nbsp;records</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when Records have been added to the Store</div><div class="long">Fires when Records have been <a href="output/Ext.data.Store.html#Ext.data.Store-add" ext:member="add" ext:cls="Ext.data.Store">add</a>ed to the Store<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li><li><code>records</code> : Ext.data.Record[]<div class="sub-desc">The array of Records added</div></li><li><code>index</code> : Number<div class="sub-desc">The index at which the record(s) were added</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#add" ext:member="#add" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-beforeload"></a><b><a href="source/Store.html#event-Ext.data.Store-beforeload">beforeload</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>,&nbsp;<code>Object&nbsp;options</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a request is made for a new data object.  If the beforeload handler returns
false the load action will b...</div><div class="long">Fires before a request is made for a new data object.  If the beforeload handler returns
<tt>false</tt> the <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> action will be canceled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified (see <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> for details)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#beforeload" ext:member="#beforeload" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-beforewrite"></a><b><a href="source/Store.html#event-Ext.data.Store-beforewrite">beforewrite</a></b> :
                                      (&nbsp;<code>DataProxy&nbsp;this</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Record/Array[Record]&nbsp;rs</code>,&nbsp;<code>Object&nbsp;options</code>,&nbsp;<code>Object&nbsp;arg</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : DataProxy<div class="sub-desc"></div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|update|destroy]</div></li><li><code>rs</code> : Record/Array[Record]<div class="sub-desc"></div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified. Edit <code>options.params</code> to add Http parameters to the request.  (see <a href="output/Ext.data.Store.html#Ext.data.Store-save" ext:member="save" ext:cls="Ext.data.Store">save</a> for details)</div></li><li><code>arg</code> : Object<div class="sub-desc">The callback's arg object passed to the <a href="output/Ext.data.Store.html#Ext.data.Store-request" ext:member="request" ext:cls="Ext.data.Store">request</a> function</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#beforewrite" ext:member="#beforewrite" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-clear"></a><b><a href="source/Store.html#event-Ext.data.Store-clear">clear</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the data cache has been cleared.</div><div class="long">Fires when the data cache has been cleared.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#clear" ext:member="#clear" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-datachanged"></a><b><a href="source/Store.html#event-Ext.data.Store-datachanged">datachanged</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the data cache has changed in a bulk manner (e.g., it has been sorted, filtered, etc.) and a
widget that i...</div><div class="long">Fires when the data cache has changed in a bulk manner (e.g., it has been sorted, filtered, etc.) and a
widget that is using this Store as a Record cache should refresh its view.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#datachanged" ext:member="#datachanged" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-exception"></a><b><a href="source/Store.html#event-Ext.data.Store-exception">exception</a></b> :
                                      (&nbsp;<code>DataProxy&nbsp;sender</code>,&nbsp;<code>String&nbsp;type</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;options</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Mixed&nbsp;arg</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires if an exception occurs in the Proxy during a remote request.
(Note that this event is also relayed through Ext....</div><div class="long"><p>Fires if an exception occurs in the Proxy during a remote request.
(Note that this event is also relayed through <a href="output/Ext.data.DataProxy.html" ext:cls="Ext.data.DataProxy">Ext.data.DataProxy</a>).
This event can be fired for one of two reasons:</p>
<div class="mdetail-params"><ul>
<li>remote-request <b>failed</b> : <div class="sub-desc">
The server did not return status === 200.
</div></li>
<li>remote-request <b>succeeded</b> : <div class="sub-desc">
The remote-request succeeded but the reader could not read the response.
This means the server returned data, but the configured Reader threw an
error while reading the response.  In this case, this event will be
raised and the caught error will be passed along into this event.
</div></li>
</ul></div>
<br><p>This event fires with two different contexts based upon the 2nd
parameter <tt>type [remote|response]</tt>.  The first four parameters
are identical between the two contexts -- only the final two parameters
differ.</p><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>sender</code> : DataProxy<div class="sub-desc"></div></li><li><code>type</code> : String<div class="sub-desc"><p>The value of this parameter will be either <tt>"response"</tt> or <tt>"remote"</tt>.</p>
<div class="mdetail-params"><ul>
<li><b><tt>"response"</tt></b> : <div class="sub-desc">
<p>An <b>invalid</b> response from the server was returned: either 404,
500 or the response meta-data does not match that defined in the DataReader
(eg: root, idProperty, successProperty).</p>
</div></li>
<li><b><tt>"remote"</tt></b> : <div class="sub-desc">
<p>A <b>valid</b> response was returned from the server having
successProperty === false.  This response might contain an error-message
sent from the server.  For example, the user may have failed
authentication/authorization or a database validation error occurred.</p>
</div></li>
</ul></div></div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified (see <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> for details)</div></li><li><code>response</code> : Object<div class="sub-desc"><p>The value of this parameter depends on the value of the <code>type</code> parameter:</p>
<div class="mdetail-params"><ul>
<li><b><tt>"response"</tt></b> : <div class="sub-desc">
<p>The raw browser response object (eg: XMLHttpRequest)</p>
</div></li>
<li><b><tt>"remote"</tt></b> : <div class="sub-desc">
<p>The decoded response object sent from the server.</p>
</div></li>
</ul></div></div></li><li><code>arg</code> : Mixed<div class="sub-desc"><p>The type and value of this parameter depends on the value of the <code>type</code> parameter:</p>
<div class="mdetail-params"><ul>
<li><b><tt>"response"</tt></b> : Error<div class="sub-desc">
<p>The JavaScript Error object caught if the configured Reader could not read the data.
If the load call returned success===false, this parameter will be null.</p>
</div></li>
<li><b><tt>"remote"</tt></b> : Record/Record[]<div class="sub-desc">
<p>This parameter will only exist if the <tt>action</tt> was a <b>write</b> action
(Ext.data.Api.actions.create|update|destroy).</p>
</div></li>
</ul></div></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#exception" ext:member="#exception" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-load"></a><b><a href="source/Store.html#event-Ext.data.Store-load">load</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>,&nbsp;<code>Ext.data.Record[]&nbsp;records</code>,&nbsp;<code>Object&nbsp;options</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after a new set of Records has been loaded.</div><div class="long">Fires after a new set of Records has been loaded.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li><li><code>records</code> : Ext.data.Record[]<div class="sub-desc">The Records that were loaded</div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified (see <a href="output/Ext.data.Store.html#Ext.data.Store-load" ext:member="load" ext:cls="Ext.data.Store">load</a> for details)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#load" ext:member="#load" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-loadexception"></a><b><a href="source/Store.html#event-Ext.data.Store-loadexception">loadexception</a></b> :
                                      (&nbsp;<code>DataProxy&nbsp;this</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Object&nbsp;arg</code>&nbsp;)
    <div class="mdesc"><div class="short">This event is deprecated in favor of the catch-all exception
event instead. Fires only if the load request returned a...</div><div class="long">This event is <b>deprecated</b> in favor of the catch-all <b><code><a href="output/Ext.data.Store.html#Ext.data.Store-exception" ext:member="exception" ext:cls="Ext.data.Store">exception</a></code></b>
event instead. Fires only if the load request returned a valid response having
<code>successProperty === false</code>.  This means the server logic returned a failure
status and there is no data to read.  For example, the server might return
<code>successProperty === false</code> if authorization failed.  This event is
called with the signature of the Proxy's "loadexception" event.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : DataProxy<div class="sub-desc"></div></li><li><code>response</code> : Object<div class="sub-desc">The decoded response object from the server.</div></li><li><code>arg</code> : Object<div class="sub-desc">The request argument.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#loadexception" ext:member="#loadexception" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-metachange"></a><b><a href="source/Store.html#event-Ext.data.Store-metachange">metachange</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>,&nbsp;<code>Object&nbsp;meta</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this store's reader provides new metadata (fields). This is currently only supported for JsonReaders.</div><div class="long">Fires when this store's reader provides new metadata (fields). This is currently only supported for JsonReaders.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li><li><code>meta</code> : Object<div class="sub-desc">The JSON metadata</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#metachange" ext:member="#metachange" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-remove"></a><b><a href="source/Store.html#event-Ext.data.Store-remove">remove</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>,&nbsp;<code>Ext.data.Record&nbsp;record</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a Record has been removed from the Store</div><div class="long">Fires when a Record has been <a href="output/Ext.data.Store.html#Ext.data.Store-remove" ext:member="remove" ext:cls="Ext.data.Store">remove</a>d from the Store<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li><li><code>record</code> : Ext.data.Record<div class="sub-desc">The Record that was removed</div></li><li><code>index</code> : Number<div class="sub-desc">The index at which the record was removed</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#remove" ext:member="#remove" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-update"></a><b><a href="source/Store.html#event-Ext.data.Store-update">update</a></b> :
                                      (&nbsp;<code>Store&nbsp;this</code>,&nbsp;<code>Ext.data.Record&nbsp;record</code>,&nbsp;<code>String&nbsp;operation</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a Record has been updated</div><div class="long">Fires when a Record has been updated<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Store<div class="sub-desc"></div></li><li><code>record</code> : Ext.data.Record<div class="sub-desc">The Record that was updated</div></li><li><code>operation</code> : String<div class="sub-desc">The update operation being performed.  Value may be one of:
<pre><code>Ext.data.Record.EDIT
 Ext.data.Record.REJECT
 Ext.data.Record.COMMIT</code></pre></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#update" ext:member="#update" ext:cls="Ext.data.Store">Store</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Store-write"></a><b><a href="source/Store.html#event-Ext.data.Store-write">write</a></b> :
                                      (&nbsp;<code>Ext.data.Store&nbsp;store</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;result</code>,&nbsp;<code>Ext.Direct.Transaction&nbsp;res</code>,&nbsp;<code>Record/Record[]&nbsp;rs</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires if the server returns 200 after an Ext.data.Api.actions CRUD action.
Success or failure of the action is availa...</div><div class="long">Fires if the server returns 200 after an Ext.data.Api.actions CRUD action.
Success or failure of the action is available in the <code>result[<em>'successProperty'</em>]</code> property.
The server-code might set the <code>successProperty</code> to <tt>false</tt> if a database validation
failed, for example.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>store</code> : Ext.data.Store<div class="sub-desc"></div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|update|destroy]</div></li><li><code>result</code> : Object<div class="sub-desc">The "data" picked-out out of the response for convenience.</div></li><li><code>res</code> : Ext.Direct.Transaction<div class="sub-desc"></div></li><li><code>rs</code> : Record/Record[]<div class="sub-desc">Store's records, the subject(s) of the write-action</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Store.html#write" ext:member="#write" ext:cls="Ext.data.Store">Store</a></td></tr></tbody></table></div>