<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.chart.BarSeries-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.chart.BarSeries-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.chart.BarSeries-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.chart.BarSeries"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.chart.Series.html" ext:member="" ext:cls="Ext.chart.Series">Series</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.chart.CartesianSeries.html" ext:member="" ext:cls="Ext.chart.CartesianSeries">CartesianSeries</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">BarSeries</pre></div><h1>Class <a href="source/Chart.html#cls-Ext.chart.BarSeries">Ext.chart.BarSeries</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.chart</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Chart.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Chart.html#cls-Ext.chart.BarSeries">BarSeries</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.chart.CartesianSeries.html" ext:cls="Ext.chart.CartesianSeries" ext:member="">CartesianSeries</a></td></tr></table><div class="description">BarSeries class for the charts widget.</div><div class="hr"></div><a id="Ext.chart.BarSeries-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Series-displayName"></a><b><a href="source/Chart.html#prop-Ext.chart.Series-displayName">displayName</a></b> : String<div class="mdesc">The human-readable name of the series.</div></td><td class="msource"><a href="output/Ext.chart.Series.html#displayName" ext:member="#displayName" ext:cls="Ext.chart.Series">Series</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Series-type"></a><b><a href="source/Chart.html#prop-Ext.chart.Series-type">type</a></b> : String<div class="mdesc">The type of series.</div></td><td class="msource"><a href="output/Ext.chart.Series.html#type" ext:member="#type" ext:cls="Ext.chart.Series">Series</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.CartesianSeries-xField"></a><b><a href="source/Chart.html#prop-Ext.chart.CartesianSeries-xField">xField</a></b> : String<div class="mdesc">The field used to access the x-axis value from the items from the data source.</div></td><td class="msource"><a href="output/Ext.chart.CartesianSeries.html#xField" ext:member="#xField" ext:cls="Ext.chart.CartesianSeries">CartesianSeries</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.CartesianSeries-yField"></a><b><a href="source/Chart.html#prop-Ext.chart.CartesianSeries-yField">yField</a></b> : String<div class="mdesc">The field used to access the y-axis value from the items from the data source.</div></td><td class="msource"><a href="output/Ext.chart.CartesianSeries.html#yField" ext:member="#yField" ext:cls="Ext.chart.CartesianSeries">CartesianSeries</a></td></tr></tbody></table><a id="Ext.chart.BarSeries-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.chart.BarSeries-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>