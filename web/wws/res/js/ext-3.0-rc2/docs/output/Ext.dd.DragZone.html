<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.DragZone-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.DragZone-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.DragZone-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.dd.DragZone-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.DragZone"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.dd.DragDrop.html" ext:member="" ext:cls="Ext.dd.DragDrop">DragDrop</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.dd.DD.html" ext:member="" ext:cls="Ext.dd.DD">DD</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.dd.DDProxy.html" ext:member="" ext:cls="Ext.dd.DDProxy">DDProxy</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.dd.DragSource.html" ext:member="" ext:cls="Ext.dd.DragSource">DragSource</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">DragZone</pre></div><h1>Class <a href="source/DragZone.html#cls-Ext.dd.DragZone">Ext.dd.DragZone</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DragZone.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DragZone.html#cls-Ext.dd.DragZone">DragZone</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.grid.GridDragZone.html" ext:cls="Ext.grid.GridDragZone">GridDragZone</a>,&#13;<a href="output/Ext.tree.TreeDragZone.html" ext:cls="Ext.tree.TreeDragZone">TreeDragZone</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource" ext:member="">DragSource</a></td></tr></table><div class="description"><p>This class provides a container DD instance that allows dragging of multiple child source nodes.</p>
<p>This class does not move the drag target nodes, but a proxy element which may contain
any DOM structure you wish. The DOM element to show in the proxy is provided by either a
provided implementation of <a href="output/Ext.dd.DragZone.html#Ext.dd.DragZone-getDragData" ext:member="getDragData" ext:cls="Ext.dd.DragZone">getDragData</a>, or by registered draggables registered with <a href="output/Ext.dd.Registry.html" ext:cls="Ext.dd.Registry">Ext.dd.Registry</a></p>
<p>If you wish to provide draggability for an arbitrary number of DOM nodes, each of which represent some
application object (For example nodes in a <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>) then use of this class
is the most efficient way to "activate" those nodes.</p>
<p>By default, this class requires that draggable child nodes are registered with <a href="output/Ext.dd.Registry.html" ext:cls="Ext.dd.Registry">Ext.dd.Registry</a>.
However a simpler way to allow a DragZone to manage any number of draggable elements is to configure
the DragZone with  an implementation of the <a href="output/Ext.dd.DragZone.html#Ext.dd.DragZone-getDragData" ext:member="getDragData" ext:cls="Ext.dd.DragZone">getDragData</a> method which interrogates the passed
mouse event to see if it has taken place within an element, or class of elements. This is easily done
by using the event's <a href="output/Ext.EventObject.html#Ext.EventObject-getTarget" ext:member="getTarget" ext:cls="Ext.EventObject">getTarget</a> method to identify a node based on a
<a href="output/Ext.DomQuery.html" ext:cls="Ext.DomQuery">Ext.DomQuery</a> selector. For example, to make the nodes of a DataView draggable, use the following
technique. Knowledge of the use of the DataView is required:</p><pre><code>myDataView.on(<em>'render'</em>, <b>function</b>() {
    myDataView.dragZone = <b>new</b> Ext.dd.DragZone(myDataView.getEl(), {

<i>//      On receipt of a mousedown event, see <b>if</b> it is within a DataView node.
</i>
<i>//      Return a drag data object <b>if</b> so.
</i>
        getDragData: <b>function</b>(e) {

<i>//          Use the DataView<em>'s own itemSelector (a mandatory property) to
</i>
<i>//          test <b>if</b> the mousedown is within one of the DataView'</em>s nodes.
</i>
            <b>var</b> sourceEl = e.getTarget(myDataView.itemSelector, 10);

<i>//          If the mousedown is within a DataView node, clone the node to produce
</i>
<i>//          a ddel element <b>for</b> use by the drag proxy. Also add application data
</i>
<i>//          to the returned data object.
</i>
            <b>if</b> (sourceEl) {
                d = sourceEl.cloneNode(true);
                d.id = Ext.id();
                <b>return</b> {
                    ddel: d,
                    sourceEl: sourceEl,
                    repairXY: Ext.fly(sourceEl).getXY(),
                    sourceStore: myDataView.store,
                    draggedRecord: v.getRecord(sourceEl)
                }
            }
        },

<i>//      Provide coordinates <b>for</b> the proxy to slide back to on failed drag.
</i>
<i>//      This is the original XY coordinates of the draggable element captured
</i>
<i>//      <b>in</b> the getDragData method.
</i>
        getRepairXY: <b>function</b>() {
            <b>return</b> this.dragData.repairXY;
        }
    });
});</code></pre>
See the <a href="output/Ext.dd.DropZone.html" ext:cls="Ext.dd.DropZone">DropZone</a> documentation for details about building a DropZone which
cooperates with this DragZone.</div><div class="hr"></div><a id="Ext.dd.DragZone-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-containerScroll"></a><b><a href="source/DragZone.html#cfg-Ext.dd.DragZone-containerScroll">containerScroll</a></b> : Boolean<div class="mdesc">True to register this container with the Scrollmanager
for auto scrolling during drag operations.</div></td><td class="msource">DragZone</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-ddGroup"></a><b><a href="source/DragSource.html#cfg-Ext.dd.DragSource-ddGroup">ddGroup</a></b> : String<div class="mdesc"><div class="short">A named drag drop group to which this object belongs.  If a group is specified, then this object will only&#13;
interact ...</div><div class="long">A named drag drop group to which this object belongs.  If a group is specified, then this object will only
interact with other drag drop objects in the same group (defaults to undefined).</div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#ddGroup" ext:member="#ddGroup" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-dropAllowed"></a><b><a href="source/DragSource.html#cfg-Ext.dd.DragSource-dropAllowed">dropAllowed</a></b> : String<div class="mdesc">The CSS class returned to the drag source when drop is allowed (defaults to "x-dd-drop-ok").</div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#dropAllowed" ext:member="#dropAllowed" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-dropNotAllowed"></a><b><a href="source/DragSource.html#cfg-Ext.dd.DragSource-dropNotAllowed">dropNotAllowed</a></b> : String<div class="mdesc">The CSS class returned to the drag source when drop is not allowed (defaults to "x-dd-drop-nodrop").</div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#dropNotAllowed" ext:member="#dropNotAllowed" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-hlColor"></a><b><a href="source/DragZone.html#cfg-Ext.dd.DragZone-hlColor">hlColor</a></b> : String<div class="mdesc"><div class="short">The color to use when visually highlighting the drag source in the afterRepair&#13;
method after a failed drop (defaults ...</div><div class="long">The color to use when visually highlighting the drag source in the afterRepair
method after a failed drop (defaults to "c3daf9" - light blue)</div></div></td><td class="msource">DragZone</td></tr></tbody></table><a id="Ext.dd.DragZone-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DDProxy-Ext.dd.DDProxy.dragElId"></a><b><a href="source/DDCore.html#prop-Ext.dd.DDProxy-Ext.dd.DDProxy.dragElId">Ext.dd.DDProxy.dragElId</a></b> : String<div class="mdesc">The default drag frame div id</div></td><td class="msource"><a href="output/Ext.dd.DDProxy.html#Ext.dd.DDProxy.dragElId" ext:member="#Ext.dd.DDProxy.dragElId" ext:cls="Ext.dd.DDProxy">DDProxy</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-Only"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-Only">Only</a></b> : Object<div class="mdesc"><div class="short">The drag and drop utility provides a framework for building drag and drop
applications.  In addition to enabling drag...</div><div class="long">The drag and drop utility provides a framework for building drag and drop
applications.  In addition to enabling drag and drop for specific elements,
the drag and drop elements are tracked by the manager class, and the
interactions between the various elements are tracked during the drag and
the implementing code is notified about these important moments.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#Only" ext:member="#Only" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-available"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-available">available</a></b> : boolean<div class="mdesc">The availabe property is false until the linked dom element is accessible.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#available" ext:member="#available" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DDProxy-centerFrame"></a><b><a href="source/DDCore.html#prop-Ext.dd.DDProxy-centerFrame">centerFrame</a></b> : boolean<div class="mdesc"><div class="short">By default the frame is positioned exactly where the drag element is, so
we use the cursor offset provided by Ext.dd....</div><div class="long">By default the frame is positioned exactly where the drag element is, so
we use the cursor offset provided by Ext.dd.DD.  Another option that works only if
you do not have constraints on the obj is to have the drag frame centered
around the cursor.  Set centerFrame to true for this effect.</div></div></td><td class="msource"><a href="output/Ext.dd.DDProxy.html#centerFrame" ext:member="#centerFrame" ext:cls="Ext.dd.DDProxy">DDProxy</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-config"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-config">config</a></b> : object<div class="mdesc">Configuration attributes passed into the constructor</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#config" ext:member="#config" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-defaultPadding"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-defaultPadding">defaultPadding</a></b> : Object<div class="mdesc">Provides default constraint padding to "constrainTo" elements (defaults to {left: 0, right:0, top:0, bottom:0}).</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#defaultPadding" ext:member="#defaultPadding" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-dragData"></a><b><a href="source/DragZone.html#prop-Ext.dd.DragZone-dragData">dragData</a></b> : Object<div class="mdesc"><div class="short">This property contains the data representing the dragged object. This data is set up by the implementation&#13;
of the ge...</div><div class="long">This property contains the data representing the dragged object. This data is set up by the implementation
of the <a href="output/Ext.dd.DragZone.html#Ext.dd.DragZone-getDragData" ext:member="getDragData" ext:cls="Ext.dd.DragZone">getDragData</a> method. It must contain a <tt>ddel</tt> property, but can contain
any other data according to the application's needs.</div></div></td><td class="msource">DragZone</td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-groups"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-groups">groups</a></b> : object An object in the format {'group1':true, 'group2':true<div class="mdesc"><div class="short">The group defines a logical collection of DragDrop objects that are
related.  Instances only get events when interact...</div><div class="long">The group defines a logical collection of DragDrop objects that are
related.  Instances only get events when interacting with other
DragDrop object in the same group.  This lets us define multiple
groups using a single DragDrop subclass if we want.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#groups" ext:member="#groups" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-hasOuterHandles"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-hasOuterHandles">hasOuterHandles</a></b> : boolean<div class="mdesc"><div class="short">By default, drags can only be initiated if the mousedown occurs in the
region the linked element is.  This is done in...</div><div class="long">By default, drags can only be initiated if the mousedown occurs in the
region the linked element is.  This is done in part to work around a
bug in some browsers that mis-report the mousedown if the previous
mouseup happened outside of the window.  This property is set to true
if outer handles are defined.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#hasOuterHandles" ext:member="#hasOuterHandles" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-id"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-id">id</a></b> : String<div class="mdesc"><div class="short">The id of the element associated with this object.  This is what we
refer to as the "linked element" because the size...</div><div class="long">The id of the element associated with this object.  This is what we
refer to as the "linked element" because the size and position of
this element is used to determine when the drag and drop objects have
interacted.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#id" ext:member="#id" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-ignoreSelf"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-ignoreSelf">ignoreSelf</a></b> : Boolean<div class="mdesc"><div class="short">Set to false to enable a DragDrop object to fire drag events while dragging
over its own Element. Defaults to true - ...</div><div class="long">Set to false to enable a DragDrop object to fire drag events while dragging
over its own Element. Defaults to true - DragDrop objects do not by default
fire drag events to themselves.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#ignoreSelf" ext:member="#ignoreSelf" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-invalidHandleClasses"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-invalidHandleClasses">invalidHandleClasses</a></b> : Array<div class="mdesc">An Array of CSS class names for elements to be considered in valid as drag handles.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#invalidHandleClasses" ext:member="#invalidHandleClasses" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-invalidHandleIds"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-invalidHandleIds">invalidHandleIds</a></b> : Object<div class="mdesc"><div class="short">An object who's property names identify the IDs of elements to be considered invalid as drag handles.
A non-null prop...</div><div class="long">An object who's property names identify the IDs of elements to be considered invalid as drag handles.
A non-null property value identifies the ID as invalid. For example, to prevent
dragging from being initiated on element ID "foo", use:<pre><code>{
    foo: true
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#invalidHandleIds" ext:member="#invalidHandleIds" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-invalidHandleTypes"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-invalidHandleTypes">invalidHandleTypes</a></b> : Object<div class="mdesc"><div class="short">An object who's property names identify HTML tags to be considered invalid as drag handles.
A non-null property value...</div><div class="long">An object who's property names identify HTML tags to be considered invalid as drag handles.
A non-null property value identifies the tag as invalid. Defaults to the 
following value which prevents drag operations from being initiated by &lt;a> elements:<pre><code>{
    A: <em>"A"</em>
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#invalidHandleTypes" ext:member="#invalidHandleTypes" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-isTarget"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-isTarget">isTarget</a></b> : boolean<div class="mdesc">By default, all instances can be a drop target.  This can be disabled by
setting isTarget to false.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#isTarget" ext:member="#isTarget" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-maintainOffset"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-maintainOffset">maintainOffset</a></b> : boolean<div class="mdesc"><div class="short">Maintain offsets when we resetconstraints.  Set to true when you want
the position of the element relative to its par...</div><div class="long">Maintain offsets when we resetconstraints.  Set to true when you want
the position of the element relative to its parent to stay the same
when the page changes</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#maintainOffset" ext:member="#maintainOffset" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-moveOnly"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-moveOnly">moveOnly</a></b> : boolean<div class="mdesc"><div class="short">When set to true, other DD objects in cooperating DDGroups do not receive
notification events when this DD object is ...</div><div class="long">When set to true, other DD objects in cooperating DDGroups do not receive
notification events when this DD object is dragged over them. Defaults to false.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#moveOnly" ext:member="#moveOnly" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-padding"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-padding">padding</a></b> : int[] An array containing the 4 padding values: [top, right, bottom, left]<div class="mdesc">The padding configured for this drag and drop object for calculating
the drop zone intersection with this object.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#padding" ext:member="#padding" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-primaryButtonOnly"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-primaryButtonOnly">primaryButtonOnly</a></b> : boolean<div class="mdesc"><div class="short">By default the drag and drop instance will only respond to the primary
button click (left button for a right-handed m...</div><div class="long">By default the drag and drop instance will only respond to the primary
button click (left button for a right-handed mouse).  Set to true to
allow drag and drop to start with any mouse click that is propogated
by the browser</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#primaryButtonOnly" ext:member="#primaryButtonOnly" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DDProxy-resizeFrame"></a><b><a href="source/DDCore.html#prop-Ext.dd.DDProxy-resizeFrame">resizeFrame</a></b> : boolean<div class="mdesc"><div class="short">By default we resize the drag frame to be the same size as the element
we want to drag (this is to get the frame effe...</div><div class="long">By default we resize the drag frame to be the same size as the element
we want to drag (this is to get the frame effect).  We can turn it off
if we want a different behavior.</div></div></td><td class="msource"><a href="output/Ext.dd.DDProxy.html#resizeFrame" ext:member="#resizeFrame" ext:cls="Ext.dd.DDProxy">DDProxy</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-scroll"></a><b><a href="source/DDCore.html#prop-Ext.dd.DD-scroll">scroll</a></b> : boolean<div class="mdesc"><div class="short">When set to true, the utility automatically tries to scroll the browser
window when a drag and drop element is dragge...</div><div class="long">When set to true, the utility automatically tries to scroll the browser
window when a drag and drop element is dragged near the viewport boundary.
Defaults to true.</div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#scroll" ext:member="#scroll" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-xTicks"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-xTicks">xTicks</a></b> : int[]<div class="mdesc"><div class="short">Array of pixel locations the element will snap to if we specified a
horizontal graduation/interval.  This array is ge...</div><div class="long">Array of pixel locations the element will snap to if we specified a
horizontal graduation/interval.  This array is generated automatically
when you define a tick interval.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#xTicks" ext:member="#xTicks" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-yTicks"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-yTicks">yTicks</a></b> : int[]<div class="mdesc"><div class="short">Array of pixel locations the element will snap to if we specified a
vertical graduation/interval.  This array is gene...</div><div class="long">Array of pixel locations the element will snap to if we specified a
vertical graduation/interval.  This array is generated automatically
when you define a tick interval.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#yTicks" ext:member="#yTicks" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr></tbody></table><a id="Ext.dd.DragZone-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-DragZone"></a><b><a href="source/DragZone.html#cls-Ext.dd.DragZone">DragZone</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The container element</div></li><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addInvalidHandleClass"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addInvalidHandleClass">addInvalidHandleClass</a></b>(&nbsp;<code>string&nbsp;cssClass</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Lets you specify a css class of elements that will not initiate a drag</div><div class="long">Lets you specify a css class of elements that will not initiate a drag<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cssClass</code> : string<div class="sub-desc">the class of the elements you wish to ignore</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addInvalidHandleClass" ext:member="#addInvalidHandleClass" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addInvalidHandleId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addInvalidHandleId">addInvalidHandleId</a></b>(&nbsp;<code>string&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Lets you to specify an element id for a child of a drag handle
that should not initiate a drag</div><div class="long">Lets you to specify an element id for a child of a drag handle
that should not initiate a drag<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : string<div class="sub-desc">the element id of the element you wish to ignore</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addInvalidHandleId" ext:member="#addInvalidHandleId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addInvalidHandleType"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addInvalidHandleType">addInvalidHandleType</a></b>(&nbsp;<code>string&nbsp;tagName</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to specify a tag name that should not start a drag operation
when clicked.  This is designed to facilitate...</div><div class="long">Allows you to specify a tag name that should not start a drag operation
when clicked.  This is designed to facilitate embedding links within a
drag handle that do something other than start the drag.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>tagName</code> : string<div class="sub-desc">the type of element to exclude</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addInvalidHandleType" ext:member="#addInvalidHandleType" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addToGroup"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addToGroup">addToGroup</a></b>(&nbsp;<code>sGroup&nbsp;{string}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Add this instance to a group of related drag/drop objects.  All
instances belong to at least one group, and can belon...</div><div class="long">Add this instance to a group of related drag/drop objects.  All
instances belong to at least one group, and can belong to as many
groups as needed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{string}</code> : sGroup<div class="sub-desc">the name of the group</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addToGroup" ext:member="#addToGroup" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-afterDragDrop"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-afterDragDrop">afterDragDrop</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
after a valid drag drop has occur...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
after a valid drag drop has occurred by providing an implementation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dropped element</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#afterDragDrop" ext:member="#afterDragDrop" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-afterDragEnter"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-afterDragEnter">afterDragEnter</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
when the dragged item enters the ...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
when the dragged item enters the drop target by providing an implementation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#afterDragEnter" ext:member="#afterDragEnter" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-afterDragOut"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-afterDragOut">afterDragOut</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
after the dragged item is dragged...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
after the dragged item is dragged out of the target without dropping.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#afterDragOut" ext:member="#afterDragOut" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-afterDragOver"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-afterDragOver">afterDragOver</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
while the dragged item is over th...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
while the dragged item is over the drop target by providing an implementation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#afterDragOver" ext:member="#afterDragOver" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-afterInvalidDrop"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-afterInvalidDrop">afterInvalidDrop</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
after an invalid drop has occurre...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
after an invalid drop has occurred by providing an implementation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dropped element</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#afterInvalidDrop" ext:member="#afterInvalidDrop" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-afterRepair"></a><b><a href="source/DragZone.html#method-Ext.dd.DragZone-afterRepair">afterRepair</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Called after a repair of an invalid drop. By default, highlights this.dragData.ddel</div><div class="long">Called after a repair of an invalid drop. By default, highlights this.dragData.ddel<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-alignElWithMouse"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-alignElWithMouse">alignElWithMouse</a></b>(&nbsp;<code>HTMLElement&nbsp;el</code>,&nbsp;<code>int&nbsp;iPageX</code>,&nbsp;<code>int&nbsp;iPageY</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the element to the location of the mousedown or click event,
maintaining the cursor location relative to the loc...</div><div class="long">Sets the element to the location of the mousedown or click event,
maintaining the cursor location relative to the location on the element
that was clicked.  Override this if you want to place the element in a
location other than where the cursor is.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : HTMLElement<div class="sub-desc">the element to move</div></li><li><code>iPageX</code> : int<div class="sub-desc">the X coordinate of the mousedown or drag event</div></li><li><code>iPageY</code> : int<div class="sub-desc">the Y coordinate of the mousedown or drag event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#alignElWithMouse" ext:member="#alignElWithMouse" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-applyConfig"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-applyConfig">applyConfig</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Sets up config options specific to this class. Overrides
Ext.dd.DragDrop, but all versions of this method through the...</div><div class="long">Sets up config options specific to this class. Overrides
Ext.dd.DragDrop, but all versions of this method through the
inheritance chain are called<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#applyConfig" ext:member="#applyConfig" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-autoOffset"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-autoOffset">autoOffset</a></b>(&nbsp;<code>int&nbsp;iPageX</code>,&nbsp;<code>int&nbsp;iPageY</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the pointer offset to the distance between the linked element's top
left corner and the location the element was...</div><div class="long">Sets the pointer offset to the distance between the linked element's top
left corner and the location the element was clicked<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iPageX</code> : int<div class="sub-desc">the X coordinate of the click</div></li><li><code>iPageY</code> : int<div class="sub-desc">the Y coordinate of the click</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#autoOffset" ext:member="#autoOffset" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-b4Drag"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-b4Drag">b4Drag</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Event that fires prior to the onDrag event.  Overrides
Ext.dd.DragDrop.</div><div class="long">Event that fires prior to the onDrag event.  Overrides
Ext.dd.DragDrop.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#b4Drag" ext:member="#b4Drag" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-b4MouseDown"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-b4MouseDown">b4MouseDown</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Event that fires prior to the onMouseDown event.  Overrides
Ext.dd.DragDrop.</div><div class="long">Event that fires prior to the onMouseDown event.  Overrides
Ext.dd.DragDrop.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#b4MouseDown" ext:member="#b4MouseDown" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-beforeDragDrop"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-beforeDragDrop">beforeDragDrop</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action before the dragged&#13;
item is droppe...</div><div class="long">An empty function by default, but provided so that you can perform a custom action before the dragged
item is dropped onto the target and optionally cancel the onDragDrop.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">isValid True if the drag drop event is valid, else false to cancel</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#beforeDragDrop" ext:member="#beforeDragDrop" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-beforeDragEnter"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-beforeDragEnter">beforeDragEnter</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
before the dragged item enters th...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
before the dragged item enters the drop target and optionally cancel the onDragEnter.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">isValid True if the drag event is valid, else false to cancel</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#beforeDragEnter" ext:member="#beforeDragEnter" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-beforeDragOut"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-beforeDragOut">beforeDragOut</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action before the dragged&#13;
item is dragge...</div><div class="long">An empty function by default, but provided so that you can perform a custom action before the dragged
item is dragged out of the target without dropping, and optionally cancel the onDragOut.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">isValid True if the drag event is valid, else false to cancel</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#beforeDragOut" ext:member="#beforeDragOut" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-beforeDragOver"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-beforeDragOver">beforeDragOver</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action&#13;
while the dragged item is over th...</div><div class="long">An empty function by default, but provided so that you can perform a custom action
while the dragged item is over the drop target and optionally cancel the onDragOver.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">isValid True if the drag event is valid, else false to cancel</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#beforeDragOver" ext:member="#beforeDragOver" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-beforeInvalidDrop"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-beforeInvalidDrop">beforeInvalidDrop</a></b>(&nbsp;<code>Ext.dd.DragDrop&nbsp;target</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action after an invalid&#13;
drop has occurre...</div><div class="long">An empty function by default, but provided so that you can perform a custom action after an invalid
drop has occurred.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>target</code> : Ext.dd.DragDrop<div class="sub-desc">The drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li><li><code>id</code> : String<div class="sub-desc">The id of the dragged element</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">isValid True if the invalid drop should proceed, else false to cancel</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#beforeInvalidDrop" ext:member="#beforeInvalidDrop" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-cachePosition"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-cachePosition">cachePosition</a></b>(&nbsp;<code>iPageX&nbsp;the</code>,&nbsp;<code>iPageY&nbsp;the</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Saves the most recent position so that we can reset the constraints and
tick marks on-demand.  We need to know this s...</div><div class="long">Saves the most recent position so that we can reset the constraints and
tick marks on-demand.  We need to know this so that we can calculate the
number of pixels the element is offset from its original position.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : iPageX<div class="sub-desc">current x position (optional, this just makes it so we
don't have to look it up again)</div></li><li><code>the</code> : iPageY<div class="sub-desc">current y position (optional, this just makes it so we
don't have to look it up again)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#cachePosition" ext:member="#cachePosition" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-clearConstraints"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-clearConstraints">clearConstraints</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears any constraints applied to this instance.  Also clears ticks
since they can't exist independent of a constrain...</div><div class="long">Clears any constraints applied to this instance.  Also clears ticks
since they can't exist independent of a constraint at this time.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#clearConstraints" ext:member="#clearConstraints" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-clearTicks"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-clearTicks">clearTicks</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears any tick interval defined for this instance</div><div class="long">Clears any tick interval defined for this instance<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#clearTicks" ext:member="#clearTicks" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-constrainTo"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-constrainTo">constrainTo</a></b>(&nbsp;<code>Mixed&nbsp;constrainTo</code>,&nbsp;<span title="Optional" class="optional">[<code>Object/Number&nbsp;pad</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;inContent</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Initializes the drag drop object's constraints to restrict movement to a certain element.
Usage:
 var dd = new Ext.dd...</div><div class="long">Initializes the drag drop object's constraints to restrict movement to a certain element.
Usage:
 <pre><code><b>var</b> dd = <b>new</b> Ext.dd.DDProxy(<em>"dragDiv1"</em>, <em>"proxytest"</em>,
                { dragElId: <em>"existingProxyDiv"</em> });
 dd.startDrag = <b>function</b>(){
     this.constrainTo(<em>"parent-id"</em>);
 };</code></pre>
Or you can initalize it using the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> object:
 <pre><code>Ext.get(<em>"dragDiv1"</em>).initDDProxy(<em>"proxytest"</em>, {dragElId: <em>"existingProxyDiv"</em>}, {
     startDrag : <b>function</b>(){
         this.constrainTo(<em>"parent-id"</em>);
     }
 });</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>constrainTo</code> : Mixed<div class="sub-desc">The element to constrain to.</div></li><li><code>pad</code> : Object/Number<div class="sub-desc">(optional) Pad provides a way to specify "padding" of the constraints,
and can be either a number for symmetrical padding (4 would be equal to {left:4, right:4, top:4, bottom:4}) or
an object containing the sides to pad. For example: {right:10, bottom:10}</div></li><li><code>inContent</code> : Boolean<div class="sub-desc">(optional) Constrain the draggable in the content box of the element (inside padding and borders)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#constrainTo" ext:member="#constrainTo" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DDProxy-createFrame"></a><b><a href="source/DDCore.html#method-Ext.dd.DDProxy-createFrame">createFrame</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Creates the proxy element if it does not yet exist</div><div class="long">Creates the proxy element if it does not yet exist<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DDProxy.html#createFrame" ext:member="#createFrame" ext:cls="Ext.dd.DDProxy">DDProxy</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-endDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-endDrag">endDrag</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Fired when we are done dragging the object</div><div class="long">Fired when we are done dragging the object<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#endDrag" ext:member="#endDrag" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-getDragData"></a><b><a href="source/DragZone.html#method-Ext.dd.DragZone-getDragData">getDragData</a></b>(&nbsp;<code>EventObject&nbsp;e</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Called when a mousedown occurs in this container. Looks in Ext.dd.Registry&#13;
for a valid target to drag based on the m...</div><div class="long">Called when a mousedown occurs in this container. Looks in <a href="output/Ext.dd.Registry.html" ext:cls="Ext.dd.Registry">Ext.dd.Registry</a>
for a valid target to drag based on the mouse down. Override this method
to provide your own lookup logic (e.g. finding a child by class name). Make sure your returned
object has a "ddel" attribute (with an HTML Element) for other functions to work.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : EventObject<div class="sub-desc">The mouse down event</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The dragData</div></li></ul></div></div></div></td><td class="msource">DragZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-getDragEl"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-getDragEl">getDragEl</a></b>()
    :
                                        HTMLElement<div class="mdesc"><div class="short">Returns a reference to the actual element to drag.  By default this is
the same as the html element, but it can be as...</div><div class="long">Returns a reference to the actual element to drag.  By default this is
the same as the html element, but it can be assigned to another
element. An example of this can be found in Ext.dd.DDProxy<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc">the html element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#getDragEl" ext:member="#getDragEl" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-getEl"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-getEl">getEl</a></b>()
    :
                                        HTMLElement<div class="mdesc"><div class="short">Returns a reference to the linked element</div><div class="long">Returns a reference to the linked element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc">the html element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#getEl" ext:member="#getEl" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-getProxy"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-getProxy">getProxy</a></b>()
    :
                                        Ext.dd.StatusProxy<div class="mdesc"><div class="short">Returns the drag source's underlying Ext.dd.StatusProxy</div><div class="long">Returns the drag source's underlying <a href="output/Ext.dd.StatusProxy.html" ext:cls="Ext.dd.StatusProxy">Ext.dd.StatusProxy</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.dd.StatusProxy</code><div class="sub-desc">proxy The StatusProxy</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#getProxy" ext:member="#getProxy" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-getRepairXY"></a><b><a href="source/DragZone.html#method-Ext.dd.DragZone-getRepairXY">getRepairXY</a></b>(&nbsp;<code>EventObject&nbsp;e</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Called before a repair of an invalid drop to get the XY to animate to. By default returns&#13;
the XY of this.dragData.dd...</div><div class="long">Called before a repair of an invalid drop to get the XY to animate to. By default returns
the XY of this.dragData.ddel<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : EventObject<div class="sub-desc">The mouse up event</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The xy location (e.g. [100, 200])</div></li></ul></div></div></div></td><td class="msource">DragZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-hideProxy"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-hideProxy">hideProxy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides the drag source's Ext.dd.StatusProxy</div><div class="long">Hides the drag source's <a href="output/Ext.dd.StatusProxy.html" ext:cls="Ext.dd.StatusProxy">Ext.dd.StatusProxy</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#hideProxy" ext:member="#hideProxy" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-init"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-init">init</a></b>(&nbsp;<code>id&nbsp;the</code>,&nbsp;<code>String&nbsp;sGroup</code>,&nbsp;<code>object&nbsp;config</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets up the DragDrop object.  Must be called in the constructor of any
Ext.dd.DragDrop subclass</div><div class="long">Sets up the DragDrop object.  Must be called in the constructor of any
Ext.dd.DragDrop subclass<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : id<div class="sub-desc">id of the linked element</div></li><li><code>sGroup</code> : String<div class="sub-desc">the group of related items</div></li><li><code>config</code> : object<div class="sub-desc">configuration attributes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#init" ext:member="#init" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DDProxy-initFrame"></a><b><a href="source/DDCore.html#method-Ext.dd.DDProxy-initFrame">initFrame</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Initialization for the drag frame element.  Must be called in the
constructor of all subclasses</div><div class="long">Initialization for the drag frame element.  Must be called in the
constructor of all subclasses<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DDProxy.html#initFrame" ext:member="#initFrame" ext:cls="Ext.dd.DDProxy">DDProxy</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-initTarget"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-initTarget">initTarget</a></b>(&nbsp;<code>id&nbsp;the</code>,&nbsp;<code>String&nbsp;sGroup</code>,&nbsp;<code>object&nbsp;config</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Initializes Targeting functionality only... the object does not
get a mousedown handler.</div><div class="long">Initializes Targeting functionality only... the object does not
get a mousedown handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : id<div class="sub-desc">id of the linked element</div></li><li><code>sGroup</code> : String<div class="sub-desc">the group of related items</div></li><li><code>config</code> : object<div class="sub-desc">configuration attributes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#initTarget" ext:member="#initTarget" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-isLocked"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-isLocked">isLocked</a></b>()
    :
                                        boolean<div class="mdesc"><div class="short">Returns true if this instance is locked, or the drag drop mgr is locked
(meaning that all drag/drop is disabled on th...</div><div class="long">Returns true if this instance is locked, or the drag drop mgr is locked
(meaning that all drag/drop is disabled on the page.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if this obj or all drag/drop is locked, else
false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#isLocked" ext:member="#isLocked" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-isValidHandleChild"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-isValidHandleChild">isValidHandleChild</a></b>(&nbsp;<code>HTMLElement&nbsp;node</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">Checks the tag exclusion list to see if this click should be ignored</div><div class="long">Checks the tag exclusion list to see if this click should be ignored<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : HTMLElement<div class="sub-desc">the HTMLElement to evaluate</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if this is a valid tag type, false if not</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#isValidHandleChild" ext:member="#isValidHandleChild" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-lock"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-lock">lock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Lock this instance</div><div class="long">Lock this instance<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#lock" ext:member="#lock" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onAvailable"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onAvailable">onAvailable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Override the onAvailable method to do what is needed after the initial
position was determined.</div><div class="long">Override the onAvailable method to do what is needed after the initial
position was determined.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onAvailable" ext:member="#onAvailable" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-onBeforeDrag"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-onBeforeDrag">onBeforeDrag</a></b>(&nbsp;<code>Object&nbsp;data</code>,&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action before the initial&#13;
drag event beg...</div><div class="long">An empty function by default, but provided so that you can perform a custom action before the initial
drag event begins and optionally cancel it.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data to be shared with drop targets</div></li><li><code>e</code> : Event<div class="sub-desc">The event object</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">isValid True if the drag event is valid, else false to cancel</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#onBeforeDrag" ext:member="#onBeforeDrag" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDrag">onDrag</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called during the onMouseMove event while dragging an
object.</div><div class="long">Abstract method called during the onMouseMove event while dragging an
object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDrag" ext:member="#onDrag" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragDrop"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragDrop">onDragDrop</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this item is dropped on another DragDrop
obj</div><div class="long">Abstract method called when this item is dropped on another DragDrop
obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this was dropped on.  In INTERSECT mode, an array of dd items this
was dropped on.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragDrop" ext:member="#onDragDrop" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragEnter"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragEnter">onDragEnter</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this element fist begins hovering over
another DragDrop obj</div><div class="long">Abstract method called when this element fist begins hovering over
another DragDrop obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this is hovering over.  In INTERSECT mode, an array of one or more
dragdrop items being hovered over.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragEnter" ext:member="#onDragEnter" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragOut"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragOut">onDragOut</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when we are no longer hovering over an element</div><div class="long">Abstract method called when we are no longer hovering over an element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this was hovering over.  In INTERSECT mode, an array of dd items
that the mouse is no longer over.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragOut" ext:member="#onDragOut" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragOver"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragOver">onDragOver</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this element is hovering over another
DragDrop obj</div><div class="long">Abstract method called when this element is hovering over another
DragDrop obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this is hovering over.  In INTERSECT mode, an array of dd items
being hovered over.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragOver" ext:member="#onDragOver" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragZone-onInitDrag"></a><b><a href="source/DragZone.html#method-Ext.dd.DragZone-onInitDrag">onInitDrag</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Called once drag threshold has been reached to initialize the proxy element. By default, it clones the&#13;
this.dragData...</div><div class="long">Called once drag threshold has been reached to initialize the proxy element. By default, it clones the
this.dragData.ddel<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">The x position of the click on the dragged object</div></li><li><code>y</code> : Number<div class="sub-desc">The y position of the click on the dragged object</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">true to continue the drag, false to cancel</div></li></ul></div></div></div></td><td class="msource">DragZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onInvalidDrop"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onInvalidDrop">onInvalidDrop</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this item is dropped on an area with no
drop target</div><div class="long">Abstract method called when this item is dropped on an area with no
drop target<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onInvalidDrop" ext:member="#onInvalidDrop" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onMouseDown"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onMouseDown">onMouseDown</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Event handler that fires when a drag/drop obj gets a mousedown</div><div class="long">Event handler that fires when a drag/drop obj gets a mousedown<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousedown event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onMouseDown" ext:member="#onMouseDown" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onMouseUp"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onMouseUp">onMouseUp</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Event handler that fires when a drag/drop obj gets a mouseup</div><div class="long">Event handler that fires when a drag/drop obj gets a mouseup<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onMouseUp" ext:member="#onMouseUp" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragSource-onStartDrag"></a><b><a href="source/DragSource.html#method-Ext.dd.DragSource-onStartDrag">onStartDrag</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">An empty function by default, but provided so that you can perform a custom action once the initial&#13;
drag event has b...</div><div class="long">An empty function by default, but provided so that you can perform a custom action once the initial
drag event has begun.  The drag cannot be canceled from this function.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">The x position of the click on the dragged object</div></li><li><code>y</code> : Number<div class="sub-desc">The y position of the click on the dragged object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragSource.html#onStartDrag" ext:member="#onStartDrag" ext:cls="Ext.dd.DragSource">DragSource</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeFromGroup"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeFromGroup">removeFromGroup</a></b>(&nbsp;<code>string&nbsp;sGroup</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Remove's this instance from the supplied interaction group</div><div class="long">Remove's this instance from the supplied interaction group<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>sGroup</code> : string<div class="sub-desc">The group to drop</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeFromGroup" ext:member="#removeFromGroup" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeInvalidHandleClass"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeInvalidHandleClass">removeInvalidHandleClass</a></b>(&nbsp;<code>string&nbsp;cssClass</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unsets an invalid css class</div><div class="long">Unsets an invalid css class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cssClass</code> : string<div class="sub-desc">the class of the element(s) you wish to
re-enable</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeInvalidHandleClass" ext:member="#removeInvalidHandleClass" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeInvalidHandleId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeInvalidHandleId">removeInvalidHandleId</a></b>(&nbsp;<code>string&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unsets an invalid handle id</div><div class="long">Unsets an invalid handle id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : string<div class="sub-desc">the id of the element to re-enable</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeInvalidHandleId" ext:member="#removeInvalidHandleId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeInvalidHandleType"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeInvalidHandleType">removeInvalidHandleType</a></b>(&nbsp;<code>string&nbsp;tagName</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unsets an excluded tag name set by addInvalidHandleType</div><div class="long">Unsets an excluded tag name set by addInvalidHandleType<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>tagName</code> : string<div class="sub-desc">the type of element to unexclude</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeInvalidHandleType" ext:member="#removeInvalidHandleType" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-resetConstraints"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-resetConstraints">resetConstraints</a></b>(&nbsp;<code>boolean&nbsp;maintainOffset</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">resetConstraints must be called if you manually reposition a dd element.</div><div class="long">resetConstraints must be called if you manually reposition a dd element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>maintainOffset</code> : boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#resetConstraints" ext:member="#resetConstraints" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-setDelta"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-setDelta">setDelta</a></b>(&nbsp;<code>int&nbsp;iDeltaX</code>,&nbsp;<code>int&nbsp;iDeltaY</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the pointer offset.  You can call this directly to force the
offset to be in a particular location (e.g., pass i...</div><div class="long">Sets the pointer offset.  You can call this directly to force the
offset to be in a particular location (e.g., pass in 0,0 to set it
to the center of the object)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iDeltaX</code> : int<div class="sub-desc">the distance from the left</div></li><li><code>iDeltaY</code> : int<div class="sub-desc">the distance from the top</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#setDelta" ext:member="#setDelta" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setDragElId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setDragElId">setDragElId</a></b>(&nbsp;<code>id&nbsp;{string}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to specify that an element other than the linked element
will be moved with the cursor during a drag</div><div class="long">Allows you to specify that an element other than the linked element
will be moved with the cursor during a drag<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{string}</code> : id<div class="sub-desc">the id of the element that will be used to initiate the drag</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setDragElId" ext:member="#setDragElId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DD-setDragElPos"></a><b><a href="source/DDCore.html#method-Ext.dd.DD-setDragElPos">setDragElPos</a></b>(&nbsp;<code>int&nbsp;iPageX</code>,&nbsp;<code>int&nbsp;iPageY</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the drag element to the location of the mousedown or click event,
maintaining the cursor location relative to th...</div><div class="long">Sets the drag element to the location of the mousedown or click event,
maintaining the cursor location relative to the location on the element
that was clicked.  Override this if you want to place the element in a
location other than where the cursor is.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iPageX</code> : int<div class="sub-desc">the X coordinate of the mousedown or drag event</div></li><li><code>iPageY</code> : int<div class="sub-desc">the Y coordinate of the mousedown or drag event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DD.html#setDragElPos" ext:member="#setDragElPos" ext:cls="Ext.dd.DD">DD</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setHandleElId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setHandleElId">setHandleElId</a></b>(&nbsp;<code>id&nbsp;{string}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to specify a child of the linked element that should be
used to initiate the drag operation.  An example o...</div><div class="long">Allows you to specify a child of the linked element that should be
used to initiate the drag operation.  An example of this would be if
you have a content div with text and links.  Clicking anywhere in the
content area would normally start the drag operation.  Use this method
to specify that an element inside of the content div is the element
that starts the drag operation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{string}</code> : id<div class="sub-desc">the id of the element that will be used to
initiate the drag.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setHandleElId" ext:member="#setHandleElId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setInitPosition"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setInitPosition">setInitPosition</a></b>(&nbsp;<code>int&nbsp;diffX</code>,&nbsp;<code>int&nbsp;diffY</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Stores the initial placement of the linked element.</div><div class="long">Stores the initial placement of the linked element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>diffX</code> : int<div class="sub-desc">the X offset, default 0</div></li><li><code>diffY</code> : int<div class="sub-desc">the Y offset, default 0</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setInitPosition" ext:member="#setInitPosition" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setOuterHandleElId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setOuterHandleElId">setOuterHandleElId</a></b>(&nbsp;<code>id&nbsp;the</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to set an element outside of the linked element as a drag
handle</div><div class="long">Allows you to set an element outside of the linked element as a drag
handle<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : id<div class="sub-desc">id of the element that will be used to initiate the drag</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setOuterHandleElId" ext:member="#setOuterHandleElId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setPadding"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setPadding">setPadding</a></b>(&nbsp;<code>int&nbsp;iTop</code>,&nbsp;<code>int&nbsp;iRight</code>,&nbsp;<code>int&nbsp;iBot</code>,&nbsp;<code>int&nbsp;iLeft</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Configures the padding for the target zone in px.  Effectively expands
(or reduces) the virtual object size for targe...</div><div class="long">Configures the padding for the target zone in px.  Effectively expands
(or reduces) the virtual object size for targeting calculations.
Supports css-style shorthand; if only one parameter is passed, all sides
will have that padding, and if only two are passed, the top and bottom
will have the first param, the left and right the second.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iTop</code> : int<div class="sub-desc">Top pad</div></li><li><code>iRight</code> : int<div class="sub-desc">Right pad</div></li><li><code>iBot</code> : int<div class="sub-desc">Bot pad</div></li><li><code>iLeft</code> : int<div class="sub-desc">Left pad</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setPadding" ext:member="#setPadding" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setXConstraint"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setXConstraint">setXConstraint</a></b>(&nbsp;<code>int&nbsp;iLeft</code>,&nbsp;<code>int&nbsp;iRight</code>,&nbsp;<code>int&nbsp;iTickSize</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">By default, the element can be dragged any place on the screen.  Use
this method to limit the horizontal travel of th...</div><div class="long">By default, the element can be dragged any place on the screen.  Use
this method to limit the horizontal travel of the element.  Pass in
0,0 for the parameters if you want to lock the drag to the y axis.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iLeft</code> : int<div class="sub-desc">the number of pixels the element can move to the left</div></li><li><code>iRight</code> : int<div class="sub-desc">the number of pixels the element can move to the
right</div></li><li><code>iTickSize</code> : int<div class="sub-desc">optional parameter for specifying that the
element
should move iTickSize pixels at a time.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setXConstraint" ext:member="#setXConstraint" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setYConstraint"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setYConstraint">setYConstraint</a></b>(&nbsp;<code>int&nbsp;iUp</code>,&nbsp;<code>int&nbsp;iDown</code>,&nbsp;<code>int&nbsp;iTickSize</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">By default, the element can be dragged any place on the screen.  Set
this to limit the vertical travel of the element...</div><div class="long">By default, the element can be dragged any place on the screen.  Set
this to limit the vertical travel of the element.  Pass in 0,0 for the
parameters if you want to lock the drag to the x axis.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iUp</code> : int<div class="sub-desc">the number of pixels the element can move up</div></li><li><code>iDown</code> : int<div class="sub-desc">the number of pixels the element can move down</div></li><li><code>iTickSize</code> : int<div class="sub-desc">optional parameter for specifying that the
element should move iTickSize pixels at a time.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setYConstraint" ext:member="#setYConstraint" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-startDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-startDrag">startDrag</a></b>(&nbsp;<code>int&nbsp;X</code>,&nbsp;<code>int&nbsp;Y</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called after a drag/drop object is clicked
and the drag or mousedown time thresholds have beeen met.</div><div class="long">Abstract method called after a drag/drop object is clicked
and the drag or mousedown time thresholds have beeen met.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>X</code> : int<div class="sub-desc">click location</div></li><li><code>Y</code> : int<div class="sub-desc">click location</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#startDrag" ext:member="#startDrag" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-toString"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-toString">toString</a></b>()
    :
                                        string<div class="mdesc"><div class="short">toString method</div><div class="long">toString method<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>string</code><div class="sub-desc">string representation of the dd obj</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#toString" ext:member="#toString" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-unlock"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-unlock">unlock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Unlock this instace</div><div class="long">Unlock this instace<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#unlock" ext:member="#unlock" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-unreg"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-unreg">unreg</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Remove all drag and drop hooks for this element</div><div class="long">Remove all drag and drop hooks for this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#unreg" ext:member="#unreg" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr></tbody></table><a id="Ext.dd.DragZone-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>