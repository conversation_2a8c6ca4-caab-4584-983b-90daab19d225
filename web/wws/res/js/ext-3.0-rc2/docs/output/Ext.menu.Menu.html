<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.menu.Menu-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.menu.Menu-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.menu.Menu-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.menu.Menu-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.menu.Menu"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Component.html" ext:member="" ext:cls="Ext.Component">Component</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.BoxComponent.html" ext:member="" ext:cls="Ext.BoxComponent">BoxComponent</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Container.html" ext:member="" ext:cls="Ext.Container">Container</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">Menu</pre></div><h1>Class <a href="source/Menu.html#cls-Ext.menu.Menu">Ext.menu.Menu</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.menu</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Menu.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Menu.html#cls-Ext.menu.Menu">Menu</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.menu.ColorMenu.html" ext:cls="Ext.menu.ColorMenu">ColorMenu</a>,&#13;<a href="output/Ext.menu.DateMenu.html" ext:cls="Ext.menu.DateMenu">DateMenu</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.Container.html" ext:cls="Ext.Container" ext:member="">Container</a></td></tr><tr><td class="label">xtype:</td><td class="hd-info">menu</td></tr></table><div class="description"><p>A menu object.  This is the container to which you may add menu items.  Menu can also serve as a base class
when you want a specialized menu based off of another component (like <a href="output/Ext.menu.DateMenu.html" ext:cls="Ext.menu.DateMenu">Ext.menu.DateMenu</a> for example).</p>
<p>Menus may contain either <a href="output/Ext.menu.Item.html" ext:cls="Ext.menu.Item">menu items</a>, or general <a href="output/Ext.Component.html" ext:cls="Ext.Component">Component</a>s.</p>
<p>To make a contained general <a href="output/Ext.Component.html" ext:cls="Ext.Component">Component</a> line up with other <a href="output/Ext.menu.Item.html" ext:cls="Ext.menu.Item">menu items</a>
specify <tt>iconCls: 'no-icon'</tt>.  This reserves a space for an icon, and indents the Component in line
with the other menu items.  See <a href="output/Ext.form.ComboBox.html" ext:cls="Ext.form.ComboBox">Ext.form.ComboBox</a>.<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-getListParent" ext:member="getListParent" ext:cls="Ext.form.ComboBox">getListParent</a>
for an example.</p>
<p>By default, Menus are absolutely positioned, floating Components. By configuring a Menu with
<b><tt><a href="output/Ext.menu.Menu.html#Ext.menu.Menu-floating" ext:member="floating" ext:cls="Ext.menu.Menu">floating</a>:false</tt></b>, a Menu may be used as child of a Container.</p></div><div class="hr"></div><a id="Ext.menu.Menu-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-activeItem"></a><b><a href="source/Container.html#cfg-Ext.Container-activeItem">activeItem</a></b> : String/Number<div class="mdesc"><div class="short">A string component id or the numeric index of the component that should be initially activated within the
container's...</div><div class="long">A string component id or the numeric index of the component that should be initially activated within the
container's layout on render.  For example, activeItem: 'item-1' or activeItem: 0 (index 0 = the first
item in the container's collection).  activeItem only applies to layout styles that can display
items one at a time (like <a href="output/Ext.layout.AccordionLayout.html" ext:cls="Ext.layout.AccordionLayout">Ext.layout.AccordionLayout</a>, <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a> and
<a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout">Ext.layout.FitLayout</a>).  Related to <a href="output/Ext.layout.ContainerLayout.html#Ext.layout.ContainerLayout-activeItem" ext:member="activeItem" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout.activeItem</a>.</div></div></td><td class="msource"><a href="output/Ext.Container.html#activeItem" ext:member="#activeItem" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-allowDomMove"></a><b><a href="source/Component.html#cfg-Ext.Component-allowDomMove">allowDomMove</a></b> : Boolean<div class="mdesc">Whether the component can move the Dom node when rendering (defaults to true).</div></td><td class="msource"><a href="output/Ext.Component.html#allowDomMove" ext:member="#allowDomMove" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-allowOtherMenus"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-allowOtherMenus">allowOtherMenus</a></b> : Boolean<div class="mdesc">True to allow multiple menus to be displayed at the same time (defaults to false)</div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-anchor"></a><b><a href="source/Component.html#cfg-Ext.Component-anchor">anchor</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this Component is rendered
by a Container which has been configured to use the An...</div><div class="long"><p><b>Note</b>: this config is only used when this Component is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">AnchorLayout</a></b>
layout manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'anchor'</tt>).</p><br>
<p>See <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>.<a href="output/Ext.layout.AnchorLayout.html#Ext.layout.AnchorLayout-anchor" ext:member="anchor" ext:cls="Ext.layout.AnchorLayout">anchor</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#anchor" ext:member="#anchor" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-applyTo"></a><b><a href="source/Component.html#cfg-Ext.Component-applyTo">applyTo</a></b> : Mixed<div class="mdesc"><div class="short">Specify the id of the element, a DOM element or an existing Element corresponding to a DIV
that is already present in...</div><div class="long"><p>Specify the id of the element, a DOM element or an existing Element corresponding to a DIV
that is already present in the document that specifies some structural markup for this
component.</p><div><ul>
<li><b>Description</b> : <ul>
<div class="sub-desc">When <tt>applyTo</tt> is used, constituent parts of the component can also be specified
by id or CSS class name within the main element, and the component being created may attempt
to create its subcomponents from that markup if applicable.</div>
</ul></li>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When using this config, a call to render() is not required.</div>
<div class="sub-desc">If applyTo is specified, any value passed for <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a> will be ignored and the target
element's parent node will automatically be used as the component's container.</div>
</ul></li>
</ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#applyTo" ext:member="#applyTo" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-autoDestroy"></a><b><a href="source/Container.html#cfg-Ext.Container-autoDestroy">autoDestroy</a></b> : Boolean<div class="mdesc"><div class="short">If true the container will automatically destroy any contained component that is removed from it, else
destruction mu...</div><div class="long">If true the container will automatically destroy any contained component that is removed from it, else
destruction must be handled manually (defaults to true).</div></div></td><td class="msource"><a href="output/Ext.Container.html#autoDestroy" ext:member="#autoDestroy" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoEl"></a><b><a href="source/Component.html#cfg-Ext.Component-autoEl">autoEl</a></b> : Mixed<div class="mdesc"><div class="short">A tag name or DomHelper spec used to create the Element which will
encapsulate this Component.
You do not normally ne...</div><div class="long"><p>A tag name or <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> spec used to create the <a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">Element</a> which will
encapsulate this Component.</p>
<p>You do not normally need to specify this. For the base classes <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>, <a href="output/Ext.BoxComponent.html" ext:cls="Ext.BoxComponent">Ext.BoxComponent</a>,
and <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>, this defaults to <b><tt>'div'</tt></b>. The more complex Ext classes use a more complex
DOM structure created by their own onRender methods.</p>
<p>This is intended to allow the developer to create application-specific utility Components encapsulated by
different DOM elements. Example usage:</p><pre><code>{
    xtype: <em>'box'</em>,
    autoEl: {
        tag: <em>'img'</em>,
        src: <em>'http:<i>//www.example.com/example.jpg'</em></i>
    }
}, {
    xtype: <em>'box'</em>,
    autoEl: {
        tag: <em>'blockquote'</em>,
        html: <em>'autoEl is cool!'</em>
    }
}, {
    xtype: <em>'container'</em>,
    autoEl: <em>'ul'</em>,
    cls: <em>'ux-unordered-list'</em>,
    items: {
        xtype: <em>'box'</em>,
        autoEl: <em>'li'</em>,
        html: <em>'First list item'</em>
    }
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#autoEl" ext:member="#autoEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-autoHeight"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-autoHeight">autoHeight</a></b> : Boolean<div class="mdesc"><div class="short">True to use height:'auto', false to use fixed height (defaults to false). Note: Although many components 
inherit thi...</div><div class="long">True to use height:'auto', false to use fixed height (defaults to false). <b>Note</b>: Although many components 
inherit this config option, not all will function as expected with a height of 'auto'. Setting autoHeight:true 
means that the browser will manage height based on the element's contents, and that Ext will not manage it at all.</div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#autoHeight" ext:member="#autoHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoShow"></a><b><a href="source/Component.html#cfg-Ext.Component-autoShow">autoShow</a></b> : Boolean<div class="mdesc"><div class="short">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render...</div><div class="long">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render (defaults to false).</div></div></td><td class="msource"><a href="output/Ext.Component.html#autoShow" ext:member="#autoShow" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-autoWidth"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-autoWidth">autoWidth</a></b> : Boolean<div class="mdesc"><div class="short">True to use width:'auto', false to use fixed width (defaults to false). Note: Although many components 
inherit this ...</div><div class="long">True to use width:'auto', false to use fixed width (defaults to false). <b>Note</b>: Although many components 
inherit this config option, not all will function as expected with a width of 'auto'. Setting autoWidth:true 
means that the browser will manage width based on the element's contents, and that Ext will not manage it at all.</div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#autoWidth" ext:member="#autoWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-bufferResize"></a><b><a href="source/Container.html#cfg-Ext.Container-bufferResize">bufferResize</a></b> : Boolean/Number<div class="mdesc"><div class="short">When set to true (100 milliseconds) or a number of milliseconds, the layout assigned for this container will buffer
t...</div><div class="long">When set to true (100 milliseconds) or a number of milliseconds, the layout assigned for this container will buffer
the frequency it calculates and does a re-layout of components. This is useful for heavy containers or containers
with a large quantity of sub-components for which frequent layout calls would be expensive.</div></div></td><td class="msource"><a href="output/Ext.Container.html#bufferResize" ext:member="#bufferResize" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-clearCls"></a><b><a href="source/Component.html#cfg-Ext.Component-clearCls">clearCls</a></b> : String<div class="mdesc"><div class="short">The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide...</div><div class="long"><p>The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide field clearing (defaults to
<tt>'x-form-clear-left'</tt>).</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container
which has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout
manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>) and either a 
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> is specified or <tt>isFormField=true</tt> is specified.</p><br>
<p>See <a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#clearCls" ext:member="#clearCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-cls"></a><b><a href="source/Component.html#cfg-Ext.Component-cls">cls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's Element (defaults to '').  This can be
useful for ...</div><div class="long">An optional extra CSS class that will be added to this component's Element (defaults to '').  This can be
useful for adding customized styles to the component or any of its children using standard CSS rules.</div></div></td><td class="msource"><a href="output/Ext.Component.html#cls" ext:member="#cls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ctCls"></a><b><a href="source/Component.html#cfg-Ext.Component-ctCls">ctCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's container. This can be useful for
adding customize...</div><div class="long"><p>An optional extra CSS class that will be added to this component's container. This can be useful for
adding customized styles to the container or any of its children using standard CSS rules.  See
<a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a>.<a href="output/Ext.layout.ContainerLayout.html#Ext.layout.ContainerLayout-extraCls" ext:member="extraCls" ext:cls="Ext.layout.ContainerLayout">extraCls</a> also.</p>
<p><b>Note</b>: <tt>ctCls</tt> defaults to <tt>''</tt> except for the following class
which assigns a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-layout-ct'</tt></li>
</ul></div>
To configure the above Class with an extra CSS class append to the default.  For example,
for BoxLayout (Hbox and Vbox):<pre><code>ctCls: <em>'x-box-layout-ct custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ctCls" ext:member="#ctCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-defaultAlign"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-defaultAlign">defaultAlign</a></b> : String<div class="mdesc"><div class="short">The default Ext.Element.alignTo anchor position value for this menu&#13;
relative to its element of origin (defaults to "...</div><div class="long">The default <a href="output/Ext.Element.html#Ext.Element-alignTo" ext:member="alignTo" ext:cls="Ext.Element">Ext.Element.alignTo</a> anchor position value for this menu
relative to its element of origin (defaults to "tl-bl?")</div></div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-defaultType"></a><b><a href="source/Container.html#cfg-Ext.Container-defaultType">defaultType</a></b> : String<div class="mdesc"><div class="short">The default xtype of child Components to create in this Container when
a child item is specified as a raw configurati...</div><div class="long"><p>The default <a href="output/Ext.Component.html" ext:cls="Ext.Component">xtype</a> of child Components to create in this Container when
a child item is specified as a raw configuration object, rather than as an instantiated Component.</p>
<p>Defaults to <tt>'panel'</tt>, except <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Ext.Toolbar</a> and <a href="output/Ext.ButtonGroup.html" ext:cls="Ext.ButtonGroup">Ext.ButtonGroup</a> which
default to <tt>'button'</tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Container.html#defaultType" ext:member="#defaultType" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-defaults"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-defaults">defaults</a></b> : Object<div class="mdesc"><div class="short">A config object that will be applied to all items added to this container either via the items&#13;
config or via the add...</div><div class="long">A config object that will be applied to all items added to this container either via the <a href="output/Ext.menu.Menu.html#Ext.menu.Menu-items" ext:member="items" ext:cls="Ext.menu.Menu">items</a>
config or via the <a href="output/Ext.menu.Menu.html#Ext.menu.Menu-add" ext:member="add" ext:cls="Ext.menu.Menu">add</a> method.  The defaults config can contain any number of
name/value property pairs to be added to each item, and should be valid for the types of items
being added to the menu.</div></div></td><td class="msource">Menu</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disabled"></a><b><a href="source/Component.html#cfg-Ext.Component-disabled">disabled</a></b> : Boolean<div class="mdesc">Render this component disabled (default is false).</div></td><td class="msource"><a href="output/Ext.Component.html#disabled" ext:member="#disabled" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disabledClass"></a><b><a href="source/Component.html#cfg-Ext.Component-disabledClass">disabledClass</a></b> : String<div class="mdesc">CSS class added to the component when it is disabled (defaults to "x-item-disabled").</div></td><td class="msource"><a href="output/Ext.Component.html#disabledClass" ext:member="#disabledClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-enableScrolling"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-enableScrolling">enableScrolling</a></b> : Boolean<div class="mdesc">True to allow the menu container to have scroller controls if the menu is too long (defaults to true).</div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-fieldLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-fieldLabel">fieldLabel</a></b> : String<div class="mdesc"><div class="short">The label text to display next to this Component (defaults to '').
Note: this config is only used when this Component...</div><div class="long"><p>The label text to display next to this Component (defaults to '').</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container which
has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout manager (eg. 
<a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>).</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#fieldLabel" ext:member="#fieldLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-floating"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-floating">floating</a></b> : Boolean<div class="mdesc"><div class="short">May be specified as false to create a Menu which may be used as a child item of another Container&#13;
instead of a free-...</div><div class="long">May be specified as false to create a Menu which may be used as a child item of another Container
instead of a free-floating <a href="output/Ext.Layer.html" ext:cls="Ext.Layer">Layer</a>. (defaults to true).</div></div></td><td class="msource">Menu</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-height"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-height">height</a></b> : Number<div class="mdesc">The height of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#height" ext:member="#height" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#cfg-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">Render this component hidden (default is false).</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-hideBorders"></a><b><a href="source/Container.html#cfg-Ext.Container-hideBorders">hideBorders</a></b> : Boolean<div class="mdesc"><div class="short">True to hide the borders of each contained component, false to defer to the component's existing
border settings (def...</div><div class="long">True to hide the borders of each contained component, false to defer to the component's existing
border settings (defaults to false).</div></div></td><td class="msource"><a href="output/Ext.Container.html#hideBorders" ext:member="#hideBorders" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-hideLabel">hideLabel</a></b> : Boolean<div class="mdesc"><div class="short">true to completely hide the label element
(label and separator). Defaults to false.
By default, even if you do not sp...</div><div class="long"><p><tt>true</tt> to completely hide the label element
(<a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">label</a> and <a href="output/Ext.Component.html#Ext.Component-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.Component">separator</a>). Defaults to <tt>false</tt>.
By default, even if you do not specify a <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> the space will still be
reserved so that the field will line up with other fields that do have labels.
Setting this to <tt>true</tt> will cause the field to not reserve that space.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>
        hideLabel: true
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideLabel" ext:member="#hideLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideMode"></a><b><a href="source/Component.html#cfg-Ext.Component-hideMode">hideMode</a></b> : String<div class="mdesc"><div class="short">How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset p...</div><div class="long"><p>How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset position) and "display" (css display) - defaults to "display".</p>
<p>For Containers which may be hidden and shown as part of a <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">card layout</a> Container such as a
<a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">TabPanel</a>, it is recommended that hideMode is configured as "offsets". This ensures
that hidden Components still have height and width so that layout managers can perform measurements when
calculating layouts.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideMode" ext:member="#hideMode" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideParent"></a><b><a href="source/Component.html#cfg-Ext.Component-hideParent">hideParent</a></b> : Boolean<div class="mdesc"><div class="short">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the...</div><div class="long">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the component itself (defaults to false).  For example, this can be used as a shortcut for a hide
button on a window by setting hide:true on the button when adding it to its parent container.</div></div></td><td class="msource"><a href="output/Ext.Component.html#hideParent" ext:member="#hideParent" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-id"></a><b><a href="source/Component.html#cfg-Ext.Component-id">id</a></b> : String<div class="mdesc"><div class="short">The unique id of this component (defaults to an auto-assigned id).
You should assign an id if you need to be able to ...</div><div class="long"><p>The <b>unique</b> id of this component (defaults to an <a href="output/Ext.Component.html#Ext.Component-getId" ext:member="getId" ext:cls="Ext.Component">auto-assigned id</a>).
You should assign an id if you need to be able to access the component later and you do
not have an object reference available (e.g., using <a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>).</p>
<p>Note that this id will also be used as the element id for the containing HTML element
that is rendered to the page for this component. This allows you to write id-based CSS
rules to style the specific instance of this component uniquely, and also to select
sub-elements using this component's id as the parent.</p>
<p><b>Note</b>: to avoid complications imposed by a unique <tt>id</tt> see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#id" ext:member="#id" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-ignoreParentClicks"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-ignoreParentClicks">ignoreParentClicks</a></b> : Boolean<div class="mdesc"><div class="short">True to ignore clicks on any item in this menu that is a parent item (displays&#13;
a submenu) so that the submenu is not...</div><div class="long">True to ignore clicks on any item in this menu that is a parent item (displays
a submenu) so that the submenu is not dismissed when clicking the parent item (defaults to false).</div></div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemCls"></a><b><a href="source/Component.html#cfg-Ext.Component-itemCls">itemCls</a></b> : String<div class="mdesc"><div class="short">An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, itemCls at th...</div><div class="long"><p>An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, <tt>itemCls</tt> at the <b>field</b> level will override
the default <tt>itemCls</tt> supplied at the <b>container</b> level. The value specified for 
<tt>itemCls</tt> will be added to the default class (<tt>'x-form-item'</tt>).</p>
<p>Since it is applied to the item wrapper (see
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>), it allows
you to write standard CSS rules that can apply to the field, the label (if specified), or
any other element within the markup for the field.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.</p><br>
Example use:<pre><code><i>// Apply a style to the field<em>'s <b>label</b>:</i>
&lt;style>
    .required .x-form-item-<b>label</b> {font-weight:bold;color:red;}
&lt;/style>

<b>new</b> Ext.FormPanel({
	height: 100,
	renderTo: Ext.getBody(),
	items: [{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Name<em>',
		itemCls: '</em>required<em>' <i>//this <b>label</b> will be styled</i>
	},{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Favorite Color<em>'
	}]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemCls" ext:member="#itemCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemId"></a><b><a href="source/Component.html#cfg-Ext.Component-itemId">itemId</a></b> : String<div class="mdesc"><div class="short">An itemId can be used as an alternative way to get a reference to a component
when no object reference is available. ...</div><div class="long"><p>An <tt>itemId</tt> can be used as an alternative way to get a reference to a component
when no object reference is available.  Instead of using an <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt> with
<a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>, use <tt>itemId</tt> with
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a> which will retrieve
<tt>itemId</tt>'s or <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>'s. Since <tt>itemId</tt>'s are an index to the
container's internal MixedCollection, the <tt>itemId</tt> is scoped locally to the container -- 
avoiding potential conflicts with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a> which requires a <b>unique</b>
<tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<pre><code><b>var</b> c = <b>new</b> Ext.Panel({ <i>//</i>
    <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 300,
    <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a>: document.body,
    <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'auto'</em>,
    <a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">items</a>: [
        {
            itemId: <em>'p1'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 1'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        },
        {
            itemId: <em>'p2'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 2'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        }
    ]
})
p1 = c.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p1'</em>); <i>// not the same as <a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">Ext.getCmp()</a></i>
p2 = p1.<a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p2'</em>); <i>// reference via a sibling</i></code></pre>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemId" ext:member="#itemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-items"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-items">items</a></b> : Mixed<div class="mdesc">An array of items to be added to this menu. Menus may contain either <a href="output/Ext.menu.Item.html" ext:cls="Ext.menu.Item">menu items</a>,
or general <a href="output/Ext.Component.html" ext:cls="Ext.Component">Component</a>s.</div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelSeparator"></a><b><a href="source/Component.html#cfg-Ext.Component-labelSeparator">labelSeparator</a></b> : String<div class="mdesc"><div class="short">The separator to display after the text of each
fieldLabel.  This property may be configured at various levels.
The o...</div><div class="long"><p>The separator to display after the text of each
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.  This property may be configured at various levels.
The order of precedence is:
<div class="mdetail-params"><ul>
<li>field / component level</li>
<li>container level</li>
<li><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">layout level</a> (defaults to colon <tt>':'</tt>)</li>
</ul></div>
To display no separator for this field's label specify empty string ''.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    layoutConfig: {
        labelSeparator: <em>'~'</em>   <i>// layout config has lowest priority (defaults to <em>':'</em>)</i>
    },
    <a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">labelSeparator</a>: <em>'>>'</em>,     <i>// config at container level </i>
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 1'</em>,
        labelSeparator: <em>'...'</em> <i>// field/component level config supersedes others</i>
    },{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 2'</em> <i>// labelSeparator will be <em>'='</em></i>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelSeparator" ext:member="#labelSeparator" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelStyle"></a><b><a href="source/Component.html#cfg-Ext.Component-labelStyle">labelStyle</a></b> : String<div class="mdesc"><div class="short">A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle val...</div><div class="long"><p>A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle value if set (eg,
<tt><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelStyle" ext:member="labelStyle" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.labelStyle</a></tt> , or '').</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>,
        labelStyle: <em>'font-weight:bold;'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelStyle" ext:member="#labelStyle" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-layout"></a><b><a href="source/Container.html#cfg-Ext.Container-layout">layout</a></b> : String/Object<div class="mdesc"><div class="short">Specify the layout manager class for this container either as an Object or as a String:
&lt;ul class="mdetail-params"&gt;
S...</div><div class="long">Specify the layout manager class for this container either as an Object or as a String:
<div><ul class="mdetail-params">
<li><u>Specify as an Object</u></li>
<div><ul class="mdetail-params">
<li>Example usage:</li>
<pre><code>layout: {
    type: <em>'vbox'</em>,
    padding: <em>'5'</em>,
    align: <em>'left'</em>
}</code></pre>
<li><tt><b>type</b></tt></li>
<br/><p>The layout type to be used for this container.  If not specified, a default <a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a>
will be created and used.</p>
<br/><p>Valid layout <tt>type</tt> values are:</p>
<div class="sub-desc"><ul class="mdetail-params">
<li><tt><b><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">absolute</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.AccordionLayout.html" ext:cls="Ext.layout.AccordionLayout">accordion</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">anchor</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">auto</a></b></tt> &nbsp;&nbsp;&nbsp; <b>Default</b></li>
<li><tt><b><a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">border</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">card</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">column</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout">fit</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">form</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.HBoxLayout.html" ext:cls="Ext.layout.HBoxLayout">hbox</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.MenuLayout.html" ext:cls="Ext.layout.MenuLayout">menu</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">table</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.ToolbarLayout.html" ext:cls="Ext.layout.ToolbarLayout">toolbar</a></b></tt></li>
<li><tt><b><a href="output/Ext.layout.VBoxLayout.html" ext:cls="Ext.layout.VBoxLayout">vbox</a></b></tt></li>
</ul></div>
<li>Layout specific configuration properties</li>
<br/><p>Additional layout specific configuration properties may also be specified. For complete details regarding
the valid config options for each layout type, see the layout class corresponding to the <tt>type</tt> specified.</p>
</ul></div>
<li><u>Specify as a String</u></li>
<div><ul class="mdetail-params">
<li>Example usage:</li>
<pre><code>layout: <em>'vbox'</em>,
layoutConfig: {
    padding: <em>'5'</em>,
    align: <em>'left'</em>
}</code></pre>
<li><tt><b>layout</b></tt></li>
<br/><p>The layout <tt>type</tt> to be used for this container (see list of valid layout type values above).</p><br/>
<li><tt><b><a href="output/Ext.Container.html#Ext.Container-layoutConfig" ext:member="layoutConfig" ext:cls="Ext.Container">layoutConfig</a></b></tt></li>
<br/><p>Additional layout specific configuration properties. For complete details regarding the valid config
options for each layout type, see the layout class corresponding to the <tt>layout</tt> specified.</p>
</ul></div></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#layout" ext:member="#layout" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-layoutConfig"></a><b><a href="source/Container.html#cfg-Ext.Container-layoutConfig">layoutConfig</a></b> : Object<div class="mdesc">This is a config object containing properties specific to the chosen <tt><b><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></b></tt> if
<tt><b><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></b></tt> has been specified as a <i>string</i>.</p></div></td><td class="msource"><a href="output/Ext.Container.html#layoutConfig" ext:member="#layoutConfig" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-maxHeight"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-maxHeight">maxHeight</a></b> : Number<div class="mdesc">The maximum height of the menu. Only applies when enableScrolling is set to True (defaults to null).</div></td><td class="msource">Menu</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-minWidth"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-minWidth">minWidth</a></b> : Number<div class="mdesc">The minimum width of the menu in pixels (defaults to 120)</div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-monitorResize"></a><b><a href="source/Container.html#cfg-Ext.Container-monitorResize">monitorResize</a></b> : Boolean<div class="mdesc"><div class="short">True to automatically monitor window resize events to handle anything that is sensitive to the current size
of the vi...</div><div class="long">True to automatically monitor window resize events to handle anything that is sensitive to the current size
of the viewport.  This value is typically managed by the chosen <tt><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></tt> and should not need
to be set manually.</div></div></td><td class="msource"><a href="output/Ext.Container.html#monitorResize" ext:member="#monitorResize" ext:cls="Ext.Container">Container</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-overCls"></a><b><a href="source/Component.html#cfg-Ext.Component-overCls">overCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and...</div><div class="long">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and removed when the mouse moves out. (defaults to '').  This can be
useful for adding customized "active" or "hover" styles to the component or any of its children using standard CSS rules.</div></div></td><td class="msource"><a href="output/Ext.Component.html#overCls" ext:member="#overCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageX"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageX">pageX</a></b> : Number<div class="mdesc">The page level x coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageX" ext:member="#pageX" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageY"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageY">pageY</a></b> : Number<div class="mdesc">The page level y coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageY" ext:member="#pageY" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-plugins"></a><b><a href="source/Component.html#cfg-Ext.Component-plugins">plugins</a></b> : Object/Array<div class="mdesc"><div class="short">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a ...</div><div class="long">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a valid plugin is that it contain an init method that accepts a reference of type Ext.Component.
When a component is created, if any plugins are available, the component will call the init method on each
plugin, passing a reference to itself.  Each plugin can then call methods or respond to events on the
component as needed to provide its functionality.</div></div></td><td class="msource"><a href="output/Ext.Component.html#plugins" ext:member="#plugins" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ptype"></a><b><a href="source/Component.html#cfg-Ext.Component-ptype">ptype</a></b> : String<div class="mdesc"><div class="short">The registered ptype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>ptype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a Plugin is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>ptype</tt> will be looked up at render time up to determine what
type of Plugin to create.<br><br>
If you create your own Plugins, you may register them using
<a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerPlugin" ext:member="registerPlugin" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerPlugin</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#ptype" ext:member="#ptype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ref"></a><b><a href="source/Component.html#cfg-Ext.Component-ref">ref</a></b> : String<div class="mdesc"><div class="short">A path specification, relative to the Component's ownerCt specifying into which
ancestor Container to place a named r...</div><div class="long"><p>A path specification, relative to the Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a> specifying into which
ancestor Container to place a named reference to this Component.</p>
<p>The ancestor axis can be traversed by using '/' characters in the path.
For example, to put a reference to a Toolbar Button into <i>the Panel which owns the Toolbar</i>:</p><pre><code><b>var</b> myGrid = <b>new</b> Ext.grid.EditorGridPanel({
    title: <em>'My EditorGridPanel'</em>,
    store: myStore,
    colModel: myColModel,
    tbar: [{
        text: <em>'Save'</em>,
        handler: saveChanges,
        disabled: true,
        ref: <em>'../saveButton'</em>
    }],
    listeners: {
        afteredit: <b>function</b>() {
<i>//          The button reference is <b>in</b> the GridPanel</i>
            myGrid.saveButton.enable();
        }
    }
});</code></pre>
<p>In the code above, if the ref had been <code><em>'saveButton'</em></code> the reference would
have been placed into the Toolbar. Each '/' in the ref moves up one level from the
Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ref" ext:member="#ref" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-region"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-region">region</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the...</div><div class="long"><p><b>Note</b>: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a></b>
layout manager (eg. specifying <tt>layout:'border'</tt>).</p><br>
<p>See <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">Ext.layout.BorderLayout</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#region" ext:member="#region" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-renderTo"></a><b><a href="source/Component.html#cfg-Ext.Component-renderTo">renderTo</a></b> : Mixed<div class="mdesc"><div class="short">Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.
Notes ...</div><div class="long"><p>Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.</p><div><ul>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When using this config, a call to render() is not required.</div>
<div class="sub-desc">Do <u>not</u> use this option if the Component is to be a child item of
a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>. It is the responsibility of the
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>'s <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout manager</a>
to render its child items.</div>
</ul></li>
</ul></div>
<p>See <tt><a href="output/Ext.Component.html#Ext.Component-render" ext:member="render" ext:cls="Ext.Component">render</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#renderTo" ext:member="#renderTo" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-scrollIncrement"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-scrollIncrement">scrollIncrement</a></b> : Number<div class="mdesc">The amount to scroll the menu. Only applies when enableScrolling is set to True (defaults to 24).</div></td><td class="msource">Menu</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-shadow"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-shadow">shadow</a></b> : Boolean/String<div class="mdesc"><div class="short">True or "sides" for the default effect, "frame" for 4-way shadow, and "drop"&#13;
for bottom-right shadow (defaults to "s...</div><div class="long">True or "sides" for the default effect, "frame" for 4-way shadow, and "drop"
for bottom-right shadow (defaults to "sides")</div></div></td><td class="msource">Menu</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-showSeparator"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-showSeparator">showSeparator</a></b> : Boolean<div class="mdesc">True to show the icon separator. (defaults to true).</div></td><td class="msource">Menu</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateId"></a><b><a href="source/Component.html#cfg-Ext.Component-stateId">stateId</a></b> : String<div class="mdesc"><div class="short">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, o...</div><div class="long">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, otherwise null if the component is using a generated id).
<p>See <a href="output/Ext.Component.html#Ext.Component-stateful" ext:member="stateful" ext:cls="Ext.Component">stateful</a> for an explanation of saving and restoring Component state.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateId" ext:member="#stateId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateful"></a><b><a href="source/Component.html#cfg-Ext.Component-stateful">stateful</a></b> : Boolean<div class="mdesc"><div class="short">A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startu...</div><div class="long"><p>A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startup. The component must have either a <a href="output/Ext.Component.html#Ext.Component-stateId" ext:member="stateId" ext:cls="Ext.Component">stateId</a> or <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>
assigned for state to be managed.  Auto-generated ids are not guaranteed to be stable across page
loads and cannot be relied upon to save and restore the same state for a component.<p>
<p>For state saving to work, the state manager's provider must have been set to an implementation
of <a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> which overrides the <a href="output/Ext.state.Provider.html#Ext.state.Provider-set" ext:member="set" ext:cls="Ext.state.Provider">set</a>
and <a href="output/Ext.state.Provider.html#Ext.state.Provider-get" ext:member="get" ext:cls="Ext.state.Provider">get</a> methods to save and recall name/value pairs.
A built-in implementation, <a href="output/Ext.state.CookieProvider.html" ext:cls="Ext.state.CookieProvider">Ext.state.CookieProvider</a> is available.</p>
<p>To set the state provider for the current page:</p>
<pre><code>Ext.state.Manager.setProvider(<b>new</b> Ext.state.CookieProvider());</code></pre>
<p>A stateful Component attempts to save state when one of the events listed in the <a href="output/Ext.Component.html#Ext.Component-stateEvents" ext:member="stateEvents" ext:cls="Ext.Component">stateEvents</a>
configuration fires.</p>
To save state, A stateful Component first serializes its state by calling <b><tt>getState</tt></b>. By default,
this function does nothing. The developer must provide an implementation which returns an object hash
which represents the Component's restorable state.</p>
<p>The value yielded by getState is passed to <a href="output/Ext.state.Manager.html#Ext.state.Manager-set" ext:member="set" ext:cls="Ext.state.Manager">Ext.state.Manager.set</a> which uses the configured
<a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> to save the object keyed by the Component's <a href="output/stateId.html" ext:cls="stateId">stateId</a>, or,
if that is not specified, its <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>During construction, a stateful Component attempts to <i>restore</i> its state by calling
<a href="output/Ext.state.Manager.html#Ext.state.Manager-get" ext:member="get" ext:cls="Ext.state.Manager">Ext.state.Manager.get</a> passing the (@link #stateId}, or, if that is not specified, the <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>The resulting object is passed to <b><tt>applyState</tt></b>. The default implementation of applyState
simply copies properties into the object, but a developer may override this to support more behaviour.</p>
<p>You can perform extra processing on state save and restore by attaching handlers to the
<a href="output/Ext.Component.html#Ext.Component-beforestaterestore" ext:member="beforestaterestore" ext:cls="Ext.Component">beforestaterestore</a>, <a href="output/Ext.Component.html#Ext.Component-staterestore" ext:member="staterestore" ext:cls="Ext.Component">staterestore</a>, <a href="output/Ext.Component.html#Ext.Component-beforestatesave" ext:member="beforestatesave" ext:cls="Ext.Component">beforestatesave</a> and <a href="output/Ext.Component.html#Ext.Component-statesave" ext:member="statesave" ext:cls="Ext.Component">statesave</a> events</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateful" ext:member="#stateful" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-style"></a><b><a href="source/Component.html#cfg-Ext.Component-style">style</a></b> : String<div class="mdesc"><div class="short">A custom style specification to be applied to this component's Element.  Should be a valid argument to
Ext.Element.ap...</div><div class="long">A custom style specification to be applied to this component's Element.  Should be a valid argument to
<a href="output/Ext.Element.html#Ext.Element-applyStyles" ext:member="applyStyles" ext:cls="Ext.Element">Ext.Element.applyStyles</a>.
<pre><code><b>new</b> Ext.Panel({
    title: <em>'Some Title'</em>,
    renderTo: Ext.getBody(),
    width: 400, height: 300,
    layout: <em>'form'</em>,
    items: [{
        xtype: <em>'textarea'</em>,
        style: {
            width: <em>'95%'</em>,
            marginBottom: <em>'10px'</em>
        }
    },
        <b>new</b> Ext.Button({
            text: <em>'Send'</em>,
            minWidth: <em>'100'</em>,
            style: {
                marginBottom: <em>'10px'</em>
            }
        })
    ]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#style" ext:member="#style" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-subMenuAlign"></a><b><a href="source/Menu.html#cfg-Ext.menu.Menu-subMenuAlign">subMenuAlign</a></b> : String<div class="mdesc">The <a href="output/Ext.Element.html#Ext.Element-alignTo" ext:member="alignTo" ext:cls="Ext.Element">Ext.Element.alignTo</a> anchor position value to use for submenus of
this menu (defaults to "tl-tr?")</div></td><td class="msource">Menu</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-width"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-width">width</a></b> : Number<div class="mdesc">The width of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#width" ext:member="#width" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-x"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-x">x</a></b> : Number<div class="mdesc">The local x (left) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#x" ext:member="#x" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-xtype"></a><b><a href="source/Component.html#cfg-Ext.Component-xtype">xtype</a></b> : String<div class="mdesc"><div class="short">The registered xtype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>xtype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a child item of a Container is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>xtype</tt> will be looked up at render time up to determine what
type of child Component to create.<br><br>
The predefined xtypes are listed <a href="output/Ext.Component.html" ext:cls="Ext.Component">here</a>.
<br><br>
If you subclass Components to create your own Components, you may register
them using <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerType" ext:member="registerType" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerType</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#xtype" ext:member="#xtype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-y"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-y">y</a></b> : Number<div class="mdesc">The local y (top) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#y" ext:member="#y" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr></tbody></table><a id="Ext.menu.Menu-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disabled"></a><b><a href="source/Component.html#prop-Ext.Component-disabled">disabled</a></b> : Boolean<div class="mdesc">True if this component is disabled. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#disabled" ext:member="#disabled" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-el"></a><b><a href="source/Component.html#prop-Ext.Component-el">el</a></b> : Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will
usually be a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will
<i>usually</i> be a &lt;DIV> element created by the class's onRender method, but
that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<br><p><b>Note</b>: this element will not be available until this Component has been
rendered.</b></p><br>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners
for this Component's own Observable events), perform the adding of the listener in a
render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        }
    }
});</code></pre>
<p>See also <tt><a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">getEl</a></p></div></div></td><td class="msource"><a href="output/Ext.Component.html#el" ext:member="#el" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#prop-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">True if this component is hidden. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-initialConfig"></a><b><a href="source/Component.html#prop-Ext.Component-initialConfig">initialConfig</a></b> : Object<div class="mdesc">This Component's initial configuration specification. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#initialConfig" ext:member="#initialConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-items"></a><b><a href="source/Container.html#prop-Ext.Container-items">items</a></b> : MixedCollection<div class="mdesc">The collection of components in this container as a <a href="output/Ext.util.MixedCollection.html" ext:cls="Ext.util.MixedCollection">Ext.util.MixedCollection</a></div></td><td class="msource"><a href="output/Ext.Container.html#items" ext:member="#items" ext:cls="Ext.Container">Container</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ownerCt"></a><b><a href="source/Component.html#prop-Ext.Component-ownerCt">ownerCt</a></b> : Ext.Container<div class="mdesc"><div class="short">The component's owner Ext.Container (defaults to undefined, and is set automatically when
the component is added to a...</div><div class="long">The component's owner <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (defaults to undefined, and is set automatically when
the component is added to a container).  Read-only.
<p><b>Note</b>: to access items within the container see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ownerCt" ext:member="#ownerCt" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-rendered"></a><b><a href="source/Component.html#prop-Ext.Component-rendered">rendered</a></b> : Boolean<div class="mdesc">True if this component has been rendered. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#rendered" ext:member="#rendered" ext:cls="Ext.Component">Component</a></td></tr></tbody></table><a id="Ext.menu.Menu-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-add"></a><b><a href="source/Container.html#method-Ext.Container-add">add</a></b>(&nbsp;<code>Ext.Component/Object&nbsp;component</code>,&nbsp;<code>Ext.Component/Object&nbsp;component2</code>,&nbsp;<code>Ext.Component/Object&nbsp;etc</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Add a Component to this Container.

Description : 
&lt;div class="sub-desc"&gt;Fires the beforeadd event before adding, the...</div><div class="long"><p>Add a <a href="output/Ext.Component.html" ext:cls="Ext.Component">Component</a> to this Container.</p><br>
<div><ul>
<li><b>Description</b> : <ul>
<div class="sub-desc">Fires the <a href="output/Ext.Container.html#Ext.Container-beforeadd" ext:member="beforeadd" ext:cls="Ext.Container">beforeadd</a> event before adding, then fires
the <a href="output/Ext.Container.html#Ext.Container-add" ext:member="add" ext:cls="Ext.Container">add</a> event after the component has been added.</div>
</ul></li>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When creating complex UIs, it is important to remember that
sizing and positioning of child items is the responsibility of the Container's
<a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager. If you expect child items to be sized in response to user
interactions, <b>you must specify a layout manager</b> which creates and manages
the type of layout you have in mind.  For example:<pre><code><b>new</b> Ext.Window({
    width:300, height: 300,
    layout: <em>'fit'</em>, <i>// explicitly set layout manager: override the <b>default</b> (layout:<em>'auto'</em>)</i>
    items: [{
        title: <em>'Panel inside a Window'</em>
    }]
}).show();</code></pre> 
Omitting the <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> config means that the
<a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">default layout manager</a> will be used which does
nothing but render child components sequentially into the Container (no sizing or
positioning will be performed in this situation).</b></div>
<div class="sub-desc">You should never specify <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a>
or call the <a href="output/Ext.Component.html#Ext.Component-render" ext:member="render" ext:cls="Ext.Component">render method</a> of a child Component when
using a Container as child Components of this container are rendered/managed by
this container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager.</div>
<div class="sub-desc">Certain layout managers allow dynamic addition of child
components. Those that do include <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>,
<a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>, <a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>, and 
<a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">Ext.layout.TableLayout</a>. For example:<pre><code><b>var</b> myNewGrid = <b>new</b> Ext.grid.GridPanel({
    store: myStore,
    colModel: myColModel
});
myTabPanel.add(myNewGrid); <i>// <a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">Ext.TabPanel</a> implicitly uses <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">CardLayout</a></i>
myTabPanel.<a href="output/Ext.TabPanel.html#Ext.TabPanel-setActiveTab" ext:member="setActiveTab" ext:cls="Ext.TabPanel">setActiveTab</a>(myNewGrid);</code></pre></div>
<div class="sub-desc">If the Container is <i>already rendered</i> when <tt>add</tt>
is called, you may need to call <a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a> to refresh the view which causes
any unrendered child Components to be rendered. This is required so that you can
<tt>add</tt> multiple child components if needed while only refreshing the layout
once. For example:<pre><code><b>var</b> tb = <b>new</b> <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Ext.Toolbar</a>();
tb.render(document.body);  <i>// toolbar is rendered</i>
tb.add({text:<em>'Button 1'</em>}); <i>// add multiple items (<a href="output/Ext.Container.html#Ext.Container-defaultType" ext:member="defaultType" ext:cls="Ext.Container">defaultType</a> <b>for</b> <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Toolbar</a> is <em>'button'</em>)</i>
tb.add({text:<em>'Button 2'</em>});
tb.<a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a>();             <i>// refresh the layout</i></code></pre></div>
<div class="sub-desc"><i>Warning:</i> Containers directly managed by the BorderLayout layout manager
may not be removed or added.  See the Notes for <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a>
for more details.</div>
</ul></li>
</ul></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>component</code> : Ext.Component/Object<div class="sub-desc">The Component to add.<br><br>
Ext uses lazy rendering, and will only render the added Component should
it become necessary, that is: when the Container is layed out either on first render
or in response to a <a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a> call.<br><br>
A Component config object may be passed instead of an instantiated Component object.
The type of Component created from a config object is determined by the
<tt><a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtype</a></tt> config property. If no <tt>xtype</tt>
is configured, the Container's <a href="output/Ext.Container.html#Ext.Container-defaultType" ext:member="defaultType" ext:cls="Ext.Container">defaultType</a> is used.<br><br>
For a list of all available <tt><a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtypes</a></tt>, see
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<tt><a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtype</a></tt>.</div></li><li><code>component2</code> : Ext.Component/Object<div class="sub-desc"></div></li><li><code>etc</code> : Ext.Component/Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">component The Component (or config object) that was
added with the {@link #defaults Container's default config values} applied.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#add" ext:member="#add" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-addClass"></a><b><a href="source/Component.html#method-Ext.Component-addClass">addClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Adds a CSS class to the component's underlying element.</div><div class="long">Adds a CSS class to the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to add</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#addClass" ext:member="#addClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-addElement"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-addElement">addElement</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        Ext.menu.Item<div class="mdesc"><div class="short">Adds an Ext.Element object to the menu</div><div class="long">Adds an <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> object to the menu<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element or DOM node to add, or its id</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.menu.Item</code><div class="sub-desc">The menu item that was added</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-addItem"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-addItem">addItem</a></b>(&nbsp;<code>Ext.menu.Item&nbsp;item</code>&nbsp;)
    :
                                        Ext.menu.Item<div class="mdesc"><div class="short">Adds an existing object based on Ext.menu.BaseItem to the menu</div><div class="long">Adds an existing object based on <a href="output/Ext.menu.BaseItem.html" ext:cls="Ext.menu.BaseItem">Ext.menu.BaseItem</a> to the menu<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>item</code> : Ext.menu.Item<div class="sub-desc">The menu item to add</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.menu.Item</code><div class="sub-desc">The menu item that was added</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-addMenuItem"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-addMenuItem">addMenuItem</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        Ext.menu.Item<div class="mdesc"><div class="short">Creates a new Ext.menu.Item based an the supplied config object and adds it to the menu</div><div class="long">Creates a new <a href="output/Ext.menu.Item.html" ext:cls="Ext.menu.Item">Ext.menu.Item</a> based an the supplied config object and adds it to the menu<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">A MenuItem config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.menu.Item</code><div class="sub-desc">The menu item that was added</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-addSeparator"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-addSeparator">addSeparator</a></b>()
    :
                                        Ext.menu.Item<div class="mdesc"><div class="short">Adds a separator bar to the menu</div><div class="long">Adds a separator bar to the menu<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.menu.Item</code><div class="sub-desc">The menu item that was added</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-addText"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-addText">addText</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        Ext.menu.Item<div class="mdesc"><div class="short">Creates a new Ext.menu.TextItem with the supplied text and adds it to the menu</div><div class="long">Creates a new <a href="output/Ext.menu.TextItem.html" ext:cls="Ext.menu.TextItem">Ext.menu.TextItem</a> with the supplied text and adds it to the menu<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">The text to display in the menu item</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.menu.Item</code><div class="sub-desc">The menu item that was added</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-applyToMarkup"></a><b><a href="source/Component.html#method-Ext.Component-applyToMarkup">applyToMarkup</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Apply this component to existing markup that is valid. With this function, no call to render() is required.</div><div class="long">Apply this component to existing markup that is valid. With this function, no call to render() is required.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#applyToMarkup" ext:member="#applyToMarkup" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-bubble"></a><b><a href="source/Container.html#method-Ext.Container-bubble">bubble</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Bubbles up the component/container heirarchy, calling the specified function with each component. The scope (this) of...</div><div class="long">Bubbles up the component/container heirarchy, calling the specified function with each component. The scope (<i>this</i>) of
function call will be the scope provided or the current component. The arguments to the function
will be the args provided or the current component. If the function returns false at any point,
the bubble is stopped.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to current node)</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The args to call the function with (default to passing the current component)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#bubble" ext:member="#bubble" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-cascade"></a><b><a href="source/Container.html#method-Ext.Container-cascade">cascade</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Cascades down the component/container heirarchy from this component (called first), calling the specified function wi...</div><div class="long">Cascades down the component/container heirarchy from this component (called first), calling the specified function with
each component. The scope (<i>this</i>) of
function call will be the scope provided or the current component. The arguments to the function
will be the args provided or the current component. If the function returns false at any point,
the cascade is stopped on that branch.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to current component)</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The args to call the function with (defaults to passing the current component)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#cascade" ext:member="#cascade" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-cloneConfig"></a><b><a href="source/Component.html#method-Ext.Component-cloneConfig">cloneConfig</a></b>(&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Clone the current component using the original config values passed into this instance by default.</div><div class="long">Clone the current component using the original config values passed into this instance by default.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>overrides</code> : Object<div class="sub-desc">A new config containing any properties to override in the cloned version.
An id property can be passed on this object, otherwise one will be generated to avoid duplicates.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">clone The cloned copy of this component</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#cloneConfig" ext:member="#cloneConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#method-Ext.Component-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the c...</div><div class="long">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the component from its <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (if applicable) and unregistering it from
<a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>.  Destruction is generally handled automatically by the framework and this method
should usually not need to be called directly.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disable"></a><b><a href="source/Component.html#method-Ext.Component-disable">disable</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Disable this component.</div><div class="long">Disable this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#disable" ext:member="#disable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-doLayout"></a><b><a href="source/Container.html#method-Ext.Container-doLayout">doLayout</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;shallow</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;force</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Force this container's layout to be recalculated. A call to this function is required after adding a new component
to...</div><div class="long">Force this container's layout to be recalculated. A call to this function is required after adding a new component
to an already rendered container, or possibly after changing sizing/position properties of child components.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>shallow</code> : Boolean<div class="sub-desc">(optional) True to only calc the layout of this component, and let child components auto
calc layouts as required (defaults to false, which calls doLayout recursively for each subcontainer)</div></li><li><code>force</code> : Boolean<div class="sub-desc">(optional) True to force a layout to occur, even if the item is hidden.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#doLayout" ext:member="#doLayout" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-enable"></a><b><a href="source/Component.html#method-Ext.Component-enable">enable</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Enable this component.</div><div class="long">Enable this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#enable" ext:member="#enable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-find"></a><b><a href="source/Container.html#method-Ext.Container-find">find</a></b>(&nbsp;<code>String&nbsp;prop</code>,&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Find a component under this container at any level by property</div><div class="long">Find a component under this container at any level by property<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>prop</code> : String<div class="sub-desc"></div></li><li><code>value</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">Array of Ext.Components</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#find" ext:member="#find" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-findBy"></a><b><a href="source/Container.html#method-Ext.Container-findBy">findBy</a></b>(&nbsp;<code>Function&nbsp;fcn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Find a component under this container at any level by a custom function. If the passed function returns
true, the com...</div><div class="long">Find a component under this container at any level by a custom function. If the passed function returns
true, the component will be included in the results. The passed function is called with the arguments (component, this container).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fcn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">Array of Ext.Components</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#findBy" ext:member="#findBy" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-findById"></a><b><a href="source/Container.html#method-Ext.Container-findById">findById</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Find a component under this container at any level by id</div><div class="long">Find a component under this container at any level by id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#findById" ext:member="#findById" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-findByType"></a><b><a href="source/Container.html#method-Ext.Container-findByType">findByType</a></b>(&nbsp;<code>String/Class&nbsp;xtype</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;shallow</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Find a component under this container at any level by xtype or class</div><div class="long">Find a component under this container at any level by xtype or class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String/Class<div class="sub-desc">The xtype string for a component, or the class of the component directly</div></li><li><code>shallow</code> : Boolean<div class="sub-desc">(optional) False to check whether this Component is descended from the xtype (this is
the default), or true to check whether this Component is directly of the specified xtype.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">Array of Ext.Components</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#findByType" ext:member="#findByType" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentBy"></a><b><a href="source/Component.html#method-Ext.Component-findParentBy">findParentBy</a></b>(&nbsp;<code>Function&nbsp;fcn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by a custom function. If the passed function returns
true, the con...</div><div class="long">Find a container above this component at any level by a custom function. If the passed function returns
true, the container will be returned. The passed function is called with the arguments (container, this component).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fcn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container for which the custom function returns true</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentBy" ext:member="#findParentBy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentByType"></a><b><a href="source/Component.html#method-Ext.Component-findParentByType">findParentByType</a></b>(&nbsp;<code>String/Class&nbsp;xtype</code>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by xtype or class</div><div class="long">Find a container above this component at any level by xtype or class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String/Class<div class="sub-desc">The xtype string for a component, or the class of the component directly</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container which matches the given xtype or class</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentByType" ext:member="#findParentByType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-focus"></a><b><a href="source/Component.html#method-Ext.Component-focus">focus</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;selectText</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Number&nbsp;delay</code>]</span>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Try to focus this component.</div><div class="long">Try to focus this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selectText</code> : Boolean<div class="sub-desc">(optional) If applicable, true to also select the text in this component</div></li><li><code>delay</code> : Boolean/Number<div class="sub-desc">(optional) Delay the focus this number of milliseconds (true for 10 milliseconds)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#focus" ext:member="#focus" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-get"></a><b><a href="source/Container.html#method-Ext.Container-get">get</a></b>(&nbsp;<code>String/Number&nbsp;key</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Get a component contained by this container (alias for items.get(key))</div><div class="long">Get a component contained by this container (alias for items.get(key))<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String/Number<div class="sub-desc">The index or id of the component</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">Ext.Component</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#get" ext:member="#get" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getBox">getBox</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Gets the current box measurements of the component's underlying element.</div><div class="long">Gets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">box An object in the format {x, y, width, height}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getBox" ext:member="#getBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getBubbleTarget"></a><b><a href="source/Component.html#method-Ext.Component-getBubbleTarget">getBubbleTarget</a></b>()
    :
                                        Ext.Container<div class="mdesc"><div class="short">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.</div><div class="long">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">the Container which owns this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getBubbleTarget" ext:member="#getBubbleTarget" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-getComponent"></a><b><a href="source/Container.html#method-Ext.Container-getComponent">getComponent</a></b>(&nbsp;<code>String/Number&nbsp;id</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Gets a direct child Component by id, or by index.</div><div class="long">Gets a direct child Component by id, or by index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String/Number<div class="sub-desc">or index of child Component to return.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#getComponent" ext:member="#getComponent" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getEl"></a><b><a href="source/Component.html#method-Ext.Component-getEl">getEl</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will usually be
a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will <i>usually</i> be
a &lt;DIV> element created by the class's onRender method, but that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<p><b>The Element will not be available until this Component has been rendered.</b></p>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners for this Component's
own Observable events), perform the adding of the listener in a one-off render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        },
        single: true  <i>// Remove the listener after first invocation</i>
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element which encapsulates this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getEl" ext:member="#getEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getHeight">getHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current height of the component's underlying element.</div><div class="long">Gets the current height of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getHeight" ext:member="#getHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getId"></a><b><a href="source/Component.html#method-Ext.Component-getId">getId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the id of this component or generates an id:"ext-comp-" + (++Ext.Component.AUTO_ID)</div><div class="long">Returns the id of this component or generates an id:<pre><code><em>"ext-comp-"</em> + (++Ext.Component.AUTO_ID)</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getId" ext:member="#getId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getItemId"></a><b><a href="source/Component.html#method-Ext.Component-getItemId">getItemId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the item id of this component.</div><div class="long">Returns the item id of this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getItemId" ext:member="#getItemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-getLayout"></a><b><a href="source/Container.html#method-Ext.Container-getLayout">getLayout</a></b>()
    :
                                        ContainerLayout<div class="mdesc"><div class="short">Returns the layout currently in use by the container.  If the container does not currently have a layout
set, a defau...</div><div class="long">Returns the layout currently in use by the container.  If the container does not currently have a layout
set, a default <a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a> will be created and set as the container's layout.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>ContainerLayout</code><div class="sub-desc">layout The container's layout</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#getLayout" ext:member="#getLayout" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-getLayoutTarget"></a><b><a href="source/Container.html#method-Ext.Container-getLayoutTarget">getLayoutTarget</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Element to be used to contain the child Components of this Container.
An implementation is provided which...</div><div class="long"><p>Returns the Element to be used to contain the child Components of this Container.</p>
<p>An implementation is provided which returns the Container's <a href="output/Ext.Container.html#Ext.Container-getEl" ext:member="getEl" ext:cls="Ext.Container">Element</a>, but
if there is a more complex structure to a Container, this may be overridden to return
the element into which the <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> renders child Components.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element to render child Components into.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#getLayoutTarget" ext:member="#getLayoutTarget" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getOuterSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getOuterSize">getOuterSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element, including space taken by its margins.</div><div class="long">Gets the current size of the component's underlying element, including space taken by its margins.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width + left/right margins), height: (element height + top/bottom margins)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getOuterSize" ext:member="#getOuterSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getPosition">getPosition</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Gets the current XY position of the component's underlying element.</div><div class="long">Gets the current XY position of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The XY position of the element (e.g., [100, 200])</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getPosition" ext:member="#getPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getSize">getSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element.</div><div class="long">Gets the current size of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width), height: (element height)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getSize" ext:member="#getSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getWidth">getWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current width of the component's underlying element.</div><div class="long">Gets the current width of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getWidth" ext:member="#getWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXType"></a><b><a href="source/Component.html#method-Ext.Component-getXType">getXType</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Gets the xtype for this component as registered with Ext.ComponentMgr. For a list of all
available xtypes, see the Ex...</div><div class="long">Gets the xtype for this component as registered with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header. Example usage:
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXType());  <i>// alerts <em>'textfield'</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXType" ext:member="#getXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXTypes"></a><b><a href="source/Component.html#method-Ext.Component-getXTypes">getXTypes</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the Ext...</div><div class="long"><p>Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXTypes());  <i>// alerts <em>'component/box/field/textfield'</em></i>
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype hierarchy string</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXTypes" ext:member="#getXTypes" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-hide"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-hide">hide</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;deep</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Hides this menu and optionally all parent menus</div><div class="long">Hides this menu and optionally all parent menus<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>deep</code> : Boolean<div class="sub-desc">(optional) True to hide all parent menus recursively, if any (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-insert"></a><b><a href="source/Container.html#method-Ext.Container-insert">insert</a></b>(&nbsp;<code>Number&nbsp;index</code>,&nbsp;<code>Ext.Component&nbsp;component</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Inserts a Component into this Container at a specified index. Fires the
beforeadd event before inserting, then fires ...</div><div class="long">Inserts a Component into this Container at a specified index. Fires the
<a href="output/Ext.Container.html#Ext.Container-beforeadd" ext:member="beforeadd" ext:cls="Ext.Container">beforeadd</a> event before inserting, then fires the <a href="output/Ext.Container.html#Ext.Container-add" ext:member="add" ext:cls="Ext.Container">add</a> event after the
Component has been inserted.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index at which the Component will be inserted
into the Container's items collection</div></li><li><code>component</code> : Ext.Component<div class="sub-desc">The child Component to insert.<br><br>
Ext uses lazy rendering, and will only render the inserted Component should
it become necessary.<br><br>
A Component config object may be passed in order to avoid the overhead of
constructing a real Component object if lazy rendering might mean that the
inserted Component will not be rendered immediately. To take advantage of
this "lazy instantiation", set the <a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">Ext.Component.xtype</a> config
property to the registered type of the Component wanted.<br><br>
For a list of all available xtypes, see <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">component The Component (or config object) that was
inserted with the Container's default config values applied.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#insert" ext:member="#insert" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isVisible"></a><b><a href="source/Component.html#method-Ext.Component-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this component is visible.</div><div class="long">Returns true if this component is visible.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component is visible, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isVisible" ext:member="#isVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isXType"></a><b><a href="source/Component.html#method-Ext.Component-isXType">isXType</a></b>(&nbsp;<code>String&nbsp;xtype</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;shallow</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from th...</div><div class="long"><p>Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from the xtype (default) or whether it is directly of the xtype specified (shallow = true).</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>For a list of all available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
<b>var</b> isText = t.isXType(<em>'textfield'</em>);        <i>// true</i>
<b>var</b> isBoxSubclass = t.isXType(<em>'box'</em>);       <i>// true, descended from BoxComponent</i>
<b>var</b> isBoxInstance = t.isXType(<em>'box'</em>, true); <i>// false, not a direct BoxComponent instance</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String<div class="sub-desc">The xtype to check for this Component</div></li><li><code>shallow</code> : Boolean<div class="sub-desc">(optional) False to check whether this Component is descended from the xtype (this is
the default), or true to check whether this Component is directly of the specified xtype.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component descends from the specified xtype, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isXType" ext:member="#isXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-nextSibling"></a><b><a href="source/Component.html#method-Ext.Component-nextSibling">nextSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the next component in the owning container</div><div class="long">Returns the next component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#nextSibling" ext:member="#nextSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-previousSibling"></a><b><a href="source/Component.html#method-Ext.Component-previousSibling">previousSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the previous component in the owning container</div><div class="long">Returns the previous component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#previousSibling" ext:member="#previousSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-remove"></a><b><a href="source/Container.html#method-Ext.Container-remove">remove</a></b>(&nbsp;<code>Component/String&nbsp;component</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;autoDestroy</code>]</span>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Removes a component from this container.  Fires the beforeremove event before removing, then fires
the remove event a...</div><div class="long">Removes a component from this container.  Fires the <a href="output/Ext.Container.html#Ext.Container-beforeremove" ext:member="beforeremove" ext:cls="Ext.Container">beforeremove</a> event before removing, then fires
the <a href="output/Ext.Container.html#Ext.Container-remove" ext:member="remove" ext:cls="Ext.Container">remove</a> event after the component has been removed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>component</code> : Component/String<div class="sub-desc">The component reference or id to remove.</div></li><li><code>autoDestroy</code> : Boolean<div class="sub-desc">(optional) True to automatically invoke the removed Component's <a href="output/Ext.Component.html#Ext.Component-destroy" ext:member="destroy" ext:cls="Ext.Component">Ext.Component.destroy</a> function.
Defaults to the value of this Container's <a href="output/Ext.Container.html#Ext.Container-autoDestroy" ext:member="autoDestroy" ext:cls="Ext.Container">autoDestroy</a> config.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">component The Component that was removed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#remove" ext:member="#remove" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-removeAll"></a><b><a href="source/Container.html#method-Ext.Container-removeAll">removeAll</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;autoDestroy</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Removes all components from this container.</div><div class="long">Removes all components from this container.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>autoDestroy</code> : Boolean<div class="sub-desc">(optional) True to automatically invoke the removed Component's <a href="output/Ext.Component.html#Ext.Component-destroy" ext:member="destroy" ext:cls="Ext.Component">Ext.Component.destroy</a> function.
Defaults to the value of this Container's <a href="output/Ext.Container.html#Ext.Container-autoDestroy" ext:member="autoDestroy" ext:cls="Ext.Container">autoDestroy</a> config.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">Array of the destroyed components</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#removeAll" ext:member="#removeAll" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-removeClass"></a><b><a href="source/Component.html#method-Ext.Component-removeClass">removeClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Removes a CSS class from the component's underlying element.</div><div class="long">Removes a CSS class from the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#removeClass" ext:member="#removeClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#method-Ext.Component-render">render</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Element/HTMLElement/String&nbsp;container</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String/Number&nbsp;position</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Render this Component into the passed HTML element.
If you are using a Container object to house this Component, then...</div><div class="long"><p>Render this Component into the passed HTML element.</p>
<p><b>If you are using a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a> object to house this Component, then
do not use the render method.</b></p>
<p>A Container's child Components are rendered by that Container's
<a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager when the Container is first rendered.</p>
<p>Certain layout managers allow dynamic addition of child components. Those that do
include <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>, <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>,
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>, <a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">Ext.layout.TableLayout</a>.</p>
<p>If the Container is already rendered when a new child Component is added, you may need to call
the Container's <a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a> to refresh the view which causes any
unrendered child Components to be rendered. This is required so that you can add multiple
child components if needed while only refreshing the layout once.</p>
<p>When creating complex UIs, it is important to remember that sizing and positioning
of child items is the responsibility of the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager.
If you expect child items to be sized in response to user interactions, you must
configure the Container with a layout manager which creates and manages the type of layout you
have in mind.</p>
<p><b>Omitting the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> config means that a basic
layout manager is used which does nothing but render child components sequentially into the
Container. No sizing or positioning will be performed in this situation.</b></p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>container</code> : Element/HTMLElement/String<div class="sub-desc">(optional) The element this Component should be
rendered into. If it is being created from existing markup, this should be omitted.</div></li><li><code>position</code> : String/Number<div class="sub-desc">(optional) The element ID or DOM node index within the container <b>before</b>
which this component will be inserted (defaults to appending to the end of the container)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-setDisabled"></a><b><a href="source/Component.html#method-Ext.Component-setDisabled">setDisabled</a></b>(&nbsp;<code>Boolean&nbsp;disabled</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Convenience function for setting disabled/enabled by boolean.</div><div class="long">Convenience function for setting disabled/enabled by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>disabled</code> : Boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#setDisabled" ext:member="#setDisabled" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setHeight">setHeight</a></b>(&nbsp;<code>Number&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the height of the component.  This method fires the resize event.</div><div class="long">Sets the height of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>height</code> : Number<div class="sub-desc">The new height to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style.</li>
<li><i>undefined</i> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setHeight" ext:member="#setHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPagePosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPagePosition">setPagePosition</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the page XY position of the component.  To set the left and top instead, use setPosition.
This method fires the ...</div><div class="long">Sets the page XY position of the component.  To set the left and top instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPosition" ext:member="setPosition" ext:cls="Ext.BoxComponent">setPosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPagePosition" ext:member="#setPagePosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPosition">setPosition</a></b>(&nbsp;<code>Number&nbsp;left</code>,&nbsp;<code>Number&nbsp;top</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the left and top of the component.  To set the page XY position instead, use setPagePosition.
This method fires ...</div><div class="long">Sets the left and top of the component.  To set the page XY position instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPagePosition" ext:member="setPagePosition" ext:cls="Ext.BoxComponent">setPagePosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : Number<div class="sub-desc">The new left</div></li><li><code>top</code> : Number<div class="sub-desc">The new top</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPosition" ext:member="#setPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setSize">setSize</a></b>(&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<code>Mixed&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width and height of this BoxComponent. This method fires the resize event. This method can accept
either wid...</div><div class="long">Sets the width and height of this BoxComponent. This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event. This method can accept
either width and height as separate arguments, or you can pass a size object like <code>{width:10, height:20}</code>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Mixed<div class="sub-desc">The new width to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
<li>A size object in the format <code>{width: widthValue, height: heightValue}</code>.</li>
<li><code>undefined</code> to leave the width unchanged.</li>
</ul></div></div></li><li><code>height</code> : Mixed<div class="sub-desc">The new height to set (not required if a size object is passed as the first arg).
This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
<li><code>undefined</code> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setSize" ext:member="#setSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-setVisible"></a><b><a href="source/Component.html#method-Ext.Component-setVisible">setVisible</a></b>(&nbsp;<code>Boolean&nbsp;visible</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Convenience function to hide or show this component by boolean.</div><div class="long">Convenience function to hide or show this component by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>visible</code> : Boolean<div class="sub-desc">True to show, false to hide</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#setVisible" ext:member="#setVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setWidth">setWidth</a></b>(&nbsp;<code>Number&nbsp;width</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width of the component.  This method fires the resize event.</div><div class="long">Sets the width of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Number<div class="sub-desc">The new width to setThis may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setWidth" ext:member="#setWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-show"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-show">show</a></b>(&nbsp;<code>Mixed&nbsp;element</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;position</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Ext.menu.Menu&nbsp;parentMenu</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Displays this menu relative to another element</div><div class="long">Displays this menu relative to another element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : Mixed<div class="sub-desc">The element to align to</div></li><li><code>position</code> : String<div class="sub-desc">(optional) The <a href="output/Ext.Element.html#Ext.Element-alignTo" ext:member="alignTo" ext:cls="Ext.Element">Ext.Element.alignTo</a> anchor position to use in aligning to
the element (defaults to this.defaultAlign)</div></li><li><code>parentMenu</code> : Ext.menu.Menu<div class="sub-desc">(optional) This menu's parent menu, if applicable (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-showAt"></a><b><a href="source/Menu.html#method-Ext.menu.Menu-showAt">showAt</a></b>(&nbsp;<code>Array&nbsp;xyPosition</code>,&nbsp;<span title="Optional" class="optional">[<code>Ext.menu.Menu&nbsp;parentMenu</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Displays this menu at a specific xy position</div><div class="long">Displays this menu at a specific xy position<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xyPosition</code> : Array<div class="sub-desc">Contains X & Y [x, y] values for the position at which to show the menu (coordinates are page-based)</div></li><li><code>parentMenu</code> : Ext.menu.Menu<div class="sub-desc">(optional) This menu's parent menu, if applicable (defaults to undefined)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-syncSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-syncSize">syncSize</a></b>()
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Force the component's size to recalculate based on the underlying element's current height and width.</div><div class="long">Force the component's size to recalculate based on the underlying element's current height and width.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#syncSize" ext:member="#syncSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-updateBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-updateBox">updateBox</a></b>(&nbsp;<code>Object&nbsp;box</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the current box measurements of the component's underlying element.</div><div class="long">Sets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>box</code> : Object<div class="sub-desc">An object in the format {x, y, width, height}</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#updateBox" ext:member="#updateBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr></tbody></table><a id="Ext.menu.Menu-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-add"></a><b><a href="source/Container.html#event-Ext.Container-add">add</a></b> :
                                      (&nbsp;<code>Ext.Container&nbsp;this</code>,&nbsp;<code>Ext.Component&nbsp;component</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Container<div class="sub-desc"></div></li><li><code>component</code> : Ext.Component<div class="sub-desc">The component that was added</div></li><li><code>index</code> : Number<div class="sub-desc">The index at which the component was added to the container's items collection</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#add" ext:member="#add" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-afterlayout"></a><b><a href="source/Container.html#event-Ext.Container-afterlayout">afterlayout</a></b> :
                                      (&nbsp;<code>Ext.Container&nbsp;this</code>,&nbsp;<code>ContainerLayout&nbsp;layout</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the components in this container are arranged by the associated layout manager.</div><div class="long">Fires when the components in this container are arranged by the associated layout manager.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Container<div class="sub-desc"></div></li><li><code>layout</code> : ContainerLayout<div class="sub-desc">The ContainerLayout implementation for this container</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#afterlayout" ext:member="#afterlayout" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-afterrender"></a><b><a href="source/Component.html#event-Ext.Component-afterrender">afterrender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component rendering is finished.</div><div class="long">Fires after the component rendering is finished.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#afterrender" ext:member="#afterrender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-beforeadd"></a><b><a href="source/Container.html#event-Ext.Container-beforeadd">beforeadd</a></b> :
                                      (&nbsp;<code>Ext.Container&nbsp;this</code>,&nbsp;<code>Ext.Component&nbsp;component</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before any Ext.Component is added or inserted into the container.
A handler can return false to cancel the add.</div><div class="long">Fires before any <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> is added or inserted into the container.
A handler can return false to cancel the add.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Container<div class="sub-desc"></div></li><li><code>component</code> : Ext.Component<div class="sub-desc">The component being added</div></li><li><code>index</code> : Number<div class="sub-desc">The index at which the component will be added to the container's items collection</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#beforeadd" ext:member="#beforeadd" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforedestroy"></a><b><a href="source/Component.html#event-Ext.Component-beforedestroy">beforedestroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is destroyed. Return false to stop the destroy.</div><div class="long">Fires before the component is destroyed. Return false to stop the destroy.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforedestroy" ext:member="#beforedestroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-beforehide"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-beforehide">beforehide</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before this menu is hidden</div><div class="long">Fires before this menu is hidden<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-beforeremove"></a><b><a href="source/Container.html#event-Ext.Container-beforeremove">beforeremove</a></b> :
                                      (&nbsp;<code>Ext.Container&nbsp;this</code>,&nbsp;<code>Ext.Component&nbsp;component</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before any Ext.Component is removed from the container.  A handler can return
false to cancel the remove.</div><div class="long">Fires before any <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> is removed from the container.  A handler can return
false to cancel the remove.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Container<div class="sub-desc"></div></li><li><code>component</code> : Ext.Component<div class="sub-desc">The component being removed</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#beforeremove" ext:member="#beforeremove" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforerender"></a><b><a href="source/Component.html#event-Ext.Component-beforerender">beforerender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is rendered. Return false to stop the render.</div><div class="long">Fires before the component is rendered. Return false to stop the render.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforerender" ext:member="#beforerender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-beforeshow"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-beforeshow">beforeshow</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before this menu is displayed</div><div class="long">Fires before this menu is displayed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestaterestore"></a><b><a href="source/Component.html#event-Ext.Component-beforestaterestore">beforestaterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is restored. Return false to stop the restore.</div><div class="long">Fires before the state of the component is restored. Return false to stop the restore.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. If this
event is not vetoed, then the state object is passed to <b><tt>applyState</tt></b>. By default,
that simply copies property values into this Component. The method maybe overriden to
provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestaterestore" ext:member="#beforestaterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestatesave"></a><b><a href="source/Component.html#event-Ext.Component-beforestatesave">beforestatesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.</div><div class="long">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestatesave" ext:member="#beforestatesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-click"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-click">click</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>,&nbsp;<code>Ext.menu.Item&nbsp;menuItem</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this menu is clicked (or when the enter key is pressed while it is active)</div><div class="long">Fires when this menu is clicked (or when the enter key is pressed while it is active)<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li><li><code>menuItem</code> : Ext.menu.Item<div class="sub-desc">The menu item that was clicked</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#event-Ext.Component-destroy">destroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is destroyed.</div><div class="long">Fires after the component is destroyed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disable"></a><b><a href="source/Component.html#event-Ext.Component-disable">disable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is disabled.</div><div class="long">Fires after the component is disabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#disable" ext:member="#disable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-enable"></a><b><a href="source/Component.html#event-Ext.Component-enable">enable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is enabled.</div><div class="long">Fires after the component is enabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#enable" ext:member="#enable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-hide"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-hide">hide</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after this menu is hidden</div><div class="long">Fires after this menu is hidden<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-itemclick"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-itemclick">itemclick</a></b> :
                                      (&nbsp;<code>Ext.menu.BaseItem&nbsp;baseItem</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a menu item contained in this menu is clicked</div><div class="long">Fires when a menu item contained in this menu is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>baseItem</code> : Ext.menu.BaseItem<div class="sub-desc">The BaseItem that was clicked</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-mouseout"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-mouseout">mouseout</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>Ext.menu.Item&nbsp;menuItem</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the mouse exits this menu</div><div class="long">Fires when the mouse exits this menu<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li><li><code>menuItem</code> : Ext.menu.Item<div class="sub-desc">The menu item that was clicked</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-mouseover"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-mouseover">mouseover</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>Ext.menu.Item&nbsp;menuItem</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the mouse is hovering over this menu</div><div class="long">Fires when the mouse is hovering over this menu<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li><li><code>menuItem</code> : Ext.menu.Item<div class="sub-desc">The menu item that was clicked</div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-move"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-move">move</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is moved.</div><div class="long">Fires after the component is moved.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#move" ext:member="#move" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-remove"></a><b><a href="source/Container.html#event-Ext.Container-remove">remove</a></b> :
                                      (&nbsp;<code>Ext.Container&nbsp;this</code>,&nbsp;<code>Ext.Component&nbsp;component</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Container<div class="sub-desc"></div></li><li><code>component</code> : Ext.Component<div class="sub-desc">The component that was removed</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#remove" ext:member="#remove" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#event-Ext.Component-render">render</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component markup is rendered.</div><div class="long">Fires after the component markup is rendered.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-resize"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-resize">resize</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;adjWidth</code>,&nbsp;<code>Number&nbsp;adjHeight</code>,&nbsp;<code>Number&nbsp;rawWidth</code>,&nbsp;<code>Number&nbsp;rawHeight</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is resized.</div><div class="long">Fires after the component is resized.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>adjWidth</code> : Number<div class="sub-desc">The box-adjusted width that was set</div></li><li><code>adjHeight</code> : Number<div class="sub-desc">The box-adjusted height that was set</div></li><li><code>rawWidth</code> : Number<div class="sub-desc">The width that was originally specified</div></li><li><code>rawHeight</code> : Number<div class="sub-desc">The height that was originally specified</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#resize" ext:member="#resize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.Menu-show"></a><b><a href="source/Menu.html#event-Ext.menu.Menu-show">show</a></b> :
                                      (&nbsp;<code>Ext.menu.Menu&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after this menu is displayed</div><div class="long">Fires after this menu is displayed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.menu.Menu<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Menu</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-staterestore"></a><b><a href="source/Component.html#event-Ext.Component-staterestore">staterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is restored.</div><div class="long">Fires after the state of the component is restored.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. This is passed
to <b><tt>applyState</tt></b>. By default, that simply copies property values into this
Component. The method maybe overriden to provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#staterestore" ext:member="#staterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-statesave"></a><b><a href="source/Component.html#event-Ext.Component-statesave">statesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is saved to the configured state provider.</div><div class="long">Fires after the state of the component is saved to the configured state provider.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#statesave" ext:member="#statesave" ext:cls="Ext.Component">Component</a></td></tr></tbody></table></div>