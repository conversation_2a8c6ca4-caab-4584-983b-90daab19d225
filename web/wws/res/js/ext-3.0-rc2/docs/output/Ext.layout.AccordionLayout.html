<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.AccordionLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.AccordionLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.AccordionLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.AccordionLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.AccordionLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.layout.ContainerLayout.html" ext:member="" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.layout.FitLayout.html" ext:member="" ext:cls="Ext.layout.FitLayout">FitLayout</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">AccordionLayout</pre></div><h1>Class <a href="source/AccordionLayout.html#cls-Ext.layout.AccordionLayout">Ext.layout.AccordionLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">AccordionLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/AccordionLayout.html#cls-Ext.layout.AccordionLayout">AccordionLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout" ext:member="">FitLayout</a></td></tr></table><div class="description"><p>This is a layout that contains multiple panels in an expandable accordion style such that only
<b>one panel can be open at any given time</b>.  Each panel has built-in support for expanding and collapsing.
<p>This class is intended to be extended or created via the <tt><b><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></b></tt>
configuration property.  See <tt><b><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">Ext.Container.layout</a></b></tt> for additional details.</p>
<p>Example usage:</p>
<pre><code><b>var</b> accordion = <b>new</b> Ext.Panel({
    title: <em>'Accordion Layout'</em>,
    layout:<em>'accordion'</em>,
    defaults: {
        <i>// applied to each contained panel
</i>
        bodyStyle: <em>'padding:15px'</em>
    },
    layoutConfig: {
        <i>// layout-specific configs go here
</i>
        titleCollapse: false,
        animate: true,
        activeOnTop: true
    },
    items: [{
        title: <em>'Panel 1'</em>,
        html: <em>'&lt;p&gt;Panel content!&lt;/p&gt;'</em>
    },{
        title: <em>'Panel 2'</em>,
        html: <em>'&lt;p&gt;Panel content!&lt;/p&gt;'</em>
    },{
        title: <em>'Panel 3'</em>,
        html: <em>'&lt;p&gt;Panel content!&lt;/p&gt;'</em>
    }]
});</code></pre></div><div class="hr"></div><a id="Ext.layout.AccordionLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-activeOnTop"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-activeOnTop">activeOnTop</a></b> : Boolean<div class="mdesc"><div class="short">True to swap the position of each panel as it is expanded so that it becomes the first item in the container,&#13;
false ...</div><div class="long">True to swap the position of each panel as it is expanded so that it becomes the first item in the container,
false to keep the panels in the rendered order. <b>This is NOT compatible with "animate:true"</b> (defaults to false).</div></div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-animate"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-animate">animate</a></b> : Boolean<div class="mdesc"><div class="short">True to slide the contained panels open and closed during expand/collapse using animation, false to open and&#13;
close d...</div><div class="long">True to slide the contained panels open and closed during expand/collapse using animation, false to open and
close directly with no animation (defaults to false).  Note: to defer to the specific config setting of each
contained panel for this property, set this to undefined at the layout level.</div></div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-autoWidth"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-autoWidth">autoWidth</a></b> : Boolean<div class="mdesc"><div class="short">True to set each contained item's width to 'auto', false to use the item's current width (defaults to true).&#13;
Note th...</div><div class="long">True to set each contained item's width to 'auto', false to use the item's current width (defaults to true).
Note that some components, in particular the <a href="output/Ext.grid.GridPanel.html" ext:cls="Ext.grid.GridPanel">grid</a>, will not function properly within
layouts if they have auto width, so in such cases this config should be set to false.</div></div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-collapseFirst"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-collapseFirst">collapseFirst</a></b> : Boolean<div class="mdesc"><div class="short">True to make sure the collapse/expand toggle button always renders first (to the left of) any other tools&#13;
in the con...</div><div class="long">True to make sure the collapse/expand toggle button always renders first (to the left of) any other tools
in the contained panels' title bars, false to render it last (defaults to false).</div></div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#extraCls" ext:member="#extraCls" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-fill"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-fill">fill</a></b> : Boolean<div class="mdesc"><div class="short">True to adjust the active item's height to fill the available space in the container, false to use the&#13;
item's curren...</div><div class="long">True to adjust the active item's height to fill the available space in the container, false to use the
item's current height, or auto height if not explicitly set (defaults to true).</div></div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-hideCollapseTool"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-hideCollapseTool">hideCollapseTool</a></b> : Boolean<div class="mdesc"><div class="short">True to hide the contained panels' collapse/expand toggle buttons, false to display them (defaults to false).&#13;
When s...</div><div class="long">True to hide the contained panels' collapse/expand toggle buttons, false to display them (defaults to false).
When set to true, <a href="output/Ext.layout.AccordionLayout.html#Ext.layout.AccordionLayout-titleCollapse" ext:member="titleCollapse" ext:cls="Ext.layout.AccordionLayout">titleCollapse</a> should be true also.</div></div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-renderHidden"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-renderHidden">renderHidden</a></b> : Boolean<div class="mdesc">True to hide each contained item on render (defaults to false).</div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#renderHidden" ext:member="#renderHidden" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-sequence"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-sequence">sequence</a></b> : Boolean<div class="mdesc"><b>Experimental</b>. If animate is set to true, this will result in each animation running in sequence.</div></td><td class="msource">AccordionLayout</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-titleCollapse"></a><b><a href="source/AccordionLayout.html#cfg-Ext.layout.AccordionLayout-titleCollapse">titleCollapse</a></b> : Boolean<div class="mdesc"><div class="short">True to allow expand/collapse of each contained panel by clicking anywhere on the title bar, false to allow&#13;
expand/c...</div><div class="long">True to allow expand/collapse of each contained panel by clicking anywhere on the title bar, false to allow
expand/collapse only when the toggle tool button is clicked (defaults to true).  When set to false,
<a href="output/Ext.layout.AccordionLayout.html#Ext.layout.AccordionLayout-hideCollapseTool" ext:member="hideCollapseTool" ext:cls="Ext.layout.AccordionLayout">hideCollapseTool</a> should be false also.</div></div></td><td class="msource">AccordionLayout</td></tr></tbody></table><a id="Ext.layout.AccordionLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-activeItem"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-activeItem">activeItem</a></b> : Ext.Component<div class="mdesc"><div class="short">A reference to the Ext.Component that is active.  For example, if(myPanel.layout.activeItem.id == 'item-1') { ... }
a...</div><div class="long">A reference to the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> that is active.  For example, <pre><code><b>if</b>(myPanel.layout.activeItem.id == <em>'item-1'</em>) { ... }</code></pre>
<tt>activeItem</tt> only applies to layout styles that can display items one at a time
(like <a href="output/Ext.layout.AccordionLayout.html" ext:cls="Ext.layout.AccordionLayout">Ext.layout.AccordionLayout</a>, <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>
and <a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout">Ext.layout.FitLayout</a>).  Read-only.  Related to <a href="output/Ext.Container.html#Ext.Container-activeItem" ext:member="activeItem" ext:cls="Ext.Container">Ext.Container.activeItem</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#activeItem" ext:member="#activeItem" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#fieldTpl" ext:member="#fieldTpl" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.AccordionLayout-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.AccordionLayout-setActiveItem"></a><b><a href="source/AccordionLayout.html#method-Ext.layout.AccordionLayout-setActiveItem">setActiveItem</a></b>(&nbsp;<code>String/Number&nbsp;item</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the active (expanded) item in the layout.</div><div class="long">Sets the active (expanded) item in the layout.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>item</code> : String/Number<div class="sub-desc">The string component id or numeric index of the item to activate</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">AccordionLayout</td></tr></tbody></table><a id="Ext.layout.AccordionLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>