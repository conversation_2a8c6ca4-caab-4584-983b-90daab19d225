<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.GridPanel-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.GridPanel-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.GridPanel-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.grid.GridPanel-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.GridPanel"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Component.html" ext:member="" ext:cls="Ext.Component">Component</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.BoxComponent.html" ext:member="" ext:cls="Ext.BoxComponent">BoxComponent</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Container.html" ext:member="" ext:cls="Ext.Container">Container</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Panel.html" ext:member="" ext:cls="Ext.Panel">Panel</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">GridPanel</pre></div><h1>Class <a href="source/GridPanel.html#cls-Ext.grid.GridPanel">Ext.grid.GridPanel</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">GridPanel.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/GridPanel.html#cls-Ext.grid.GridPanel">GridPanel</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.grid.EditorGridPanel.html" ext:cls="Ext.grid.EditorGridPanel">EditorGridPanel</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.Panel.html" ext:cls="Ext.Panel" ext:member="">Panel</a></td></tr><tr><td class="label">xtype:</td><td class="hd-info">grid</td></tr></table><div class="description"><p>This class represents the primary interface of a component based grid control to represent data
in a tabular format of rows and columns. The GridPanel is composed of the following:</p>
<div class="mdetail-params"><ul>
<li><b><a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Store</a></b> : The Model holding the data records (rows)
<div class="sub-desc"></div></li>
<li><b><a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">Column model</a></b> : Column makeup
<div class="sub-desc"></div></li>
<li><b><a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">View</a></b> : Encapsulates the user interface 
<div class="sub-desc"></div></li>
<li><b><a href="output/Ext.grid.AbstractSelectionModel.html" ext:cls="Ext.grid.AbstractSelectionModel">selection model</a></b> : Selection behavior 
<div class="sub-desc"></div></li>
</ul></div>
<p>Example usage:</p>
<pre><code><b>var</b> grid = <b>new</b> Ext.grid.GridPanel({
    <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-store" ext:member="store" ext:cls="Ext.grid.GridPanel">store</a>: <b>new</b> (@link Ext.data.Store}({
        <a href="output/Ext.data.Store.html#Ext.data.Store-autoDestroy" ext:member="autoDestroy" ext:cls="Ext.data.Store">autoDestroy</a>: true,
        <a href="output/Ext.data.Store.html#Ext.data.Store-reader" ext:member="reader" ext:cls="Ext.data.Store">reader</a>: reader,
        <a href="output/Ext.data.Store.html#Ext.data.Store-data" ext:member="data" ext:cls="Ext.data.Store">data</a>: xg.dummyData
    }),
    <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-columns" ext:member="columns" ext:cls="Ext.grid.GridPanel">columns</a>: [
        {id: <em>'company'</em>, header: <em>'Company'</em>, width: 200, sortable: true, dataIndex: <em>'company'</em>},
        {header: <em>'Price'</em>, width: 120, sortable: true, renderer: Ext.util.Format.usMoney, dataIndex: <em>'price'</em>},
        {header: <em>'Change'</em>, width: 120, sortable: true, dataIndex: <em>'change'</em>},
        {header: <em>'% Change'</em>, width: 120, sortable: true, dataIndex: <em>'pctChange'</em>},
        <i>// instead of specifying renderer: Ext.util.Format.dateRenderer(<em>'m/d/Y'</em>) use xtype
</i>
        {header: <em>'Last Updated'</em>, width: 135, sortable: true, dataIndex: <em>'lastChange'</em>, xtype: <em>'datecolumn'</em>, format: <em>'M d, Y'</em>}
    ],
    <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-viewConfig" ext:member="viewConfig" ext:cls="Ext.grid.GridPanel">viewConfig</a>: {
        <a href="output/Ext.grid.GridView.html#Ext.grid.GridView-forceFit" ext:member="forceFit" ext:cls="Ext.grid.GridView">forceFit</a>: true,

<i>//      Return CSS class to apply to rows depending upon data values
</i>
        <a href="output/Ext.grid.GridView.html#Ext.grid.GridView-getRowClass" ext:member="getRowClass" ext:cls="Ext.grid.GridView">getRowClass</a>: <b>function</b>(record, index) {
            <b>var</b> c = record.<a href="output/Ext.data.Record.html#Ext.data.Record-get" ext:member="get" ext:cls="Ext.data.Record">get</a>(<em>'change'</em>);
            <b>if</b> (c < 0) {
                <b>return</b> <em>'price-fall'</em>;
            } <b>else</b> <b>if</b> (c > 0) {
                <b>return</b> <em>'price-rise'</em>;
            }
        }
    },
    <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-sm" ext:member="sm" ext:cls="Ext.grid.GridPanel">sm</a>: <b>new</b> Ext.grid.RowSelectionModel({singleSelect:true}),
    width: 600,
    height: 300,
    frame: true,
    title: <em>'Framed <b>with</b> Row Selection and Horizontal Scrolling'</em>,
    iconCls: <em>'icon-grid'</em>
});</code></pre>
<p><b><u>Notes:</u></b></p>
<div class="mdetail-params"><ul>
<li>Although this class inherits many configuration options from base classes, some of them
(such as autoScroll, autoWidth, layout, items, etc) are not used by this class, and will
have no effect.</li>
<li>A grid <b>requires</b> a width in which to scroll its columns, and a height in which to
scroll its rows. These dimensions can either be set explicitly through the
<tt><a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a></tt> and <tt><a href="output/Ext.BoxComponent.html#Ext.BoxComponent-width" ext:member="width" ext:cls="Ext.BoxComponent">width</a></tt>
configuration options or implicitly set by using the grid as a child item of a
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a> which will have a <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout manager</a>
provide the sizing of its child items (for example the Container of the Grid may specify
<tt><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>:'fit'</tt>).</li>
<li>To access the data in a Grid, it is necessary to use the data model encapsulated
by the <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-store" ext:member="store" ext:cls="Ext.grid.GridPanel">Store</a>. See the <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-cellclick" ext:member="cellclick" ext:cls="Ext.grid.GridPanel">cellclick</a> event for more details.</li>
</ul></div></div><div class="hr"></div><a id="Ext.grid.GridPanel-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-anchor"></a><b><a href="source/Component.html#cfg-Ext.Component-anchor">anchor</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this Component is rendered
by a Container which has been configured to use the An...</div><div class="long"><p><b>Note</b>: this config is only used when this Component is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">AnchorLayout</a></b>
layout manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'anchor'</tt>).</p><br>
<p>See <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>.<a href="output/Ext.layout.AnchorLayout.html#Ext.layout.AnchorLayout-anchor" ext:member="anchor" ext:cls="Ext.layout.AnchorLayout">anchor</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#anchor" ext:member="#anchor" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-animCollapse"></a><b><a href="source/Panel.html#cfg-Ext.Panel-animCollapse">animCollapse</a></b> : Boolean<div class="mdesc"><div class="short">true to animate the transition when the panel is collapsed, false to skip the
animation (defaults to true if the Ext....</div><div class="long"><tt>true</tt> to animate the transition when the panel is collapsed, <tt>false</tt> to skip the
animation (defaults to <tt>true</tt> if the <a href="output/Ext.Fx.html" ext:cls="Ext.Fx">Ext.Fx</a> class is available, otherwise <tt>false</tt>).</div></div></td><td class="msource"><a href="output/Ext.Panel.html#animCollapse" ext:member="#animCollapse" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-autoExpandColumn"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-autoExpandColumn">autoExpandColumn</a></b> : String<div class="mdesc"><div class="short">The id of a column in&#13;
this grid that should expand to fill unused space. This value specified here can not&#13;
be 0.&#13;
N...</div><div class="long"><p>The <tt><a href="output/Ext.grid.Column.html#Ext.grid.Column-id" ext:member="id" ext:cls="Ext.grid.Column">id</a></tt> of a <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">column</a> in
this grid that should expand to fill unused space. This value specified here can not
be <tt>0</tt>.</p>
<br><p><b>Note</b>: If the Grid's <a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">view</a> is configured with
<tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-forceFit" ext:member="forceFit" ext:cls="Ext.grid.GridView">forceFit</a>=true</tt> the <tt>autoExpandColumn</tt>
is ignored. See <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">Ext.grid.Column</a>.<tt><a href="output/Ext.grid.Column.html#Ext.grid.Column-width" ext:member="width" ext:cls="Ext.grid.Column">width</a></tt>
for additional details.</p>
<p>See <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandMax" ext:member="autoExpandMax" ext:cls="Ext.grid.GridPanel">autoExpandMax</a></tt> and <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandMin" ext:member="autoExpandMin" ext:cls="Ext.grid.GridPanel">autoExpandMin</a></tt> also.</p></div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-autoExpandMax"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-autoExpandMax">autoExpandMax</a></b> : Number<div class="mdesc">The maximum width the <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandColumn" ext:member="autoExpandColumn" ext:cls="Ext.grid.GridPanel">autoExpandColumn</a></tt>
can have (if enabled). Defaults to <tt>1000</tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-autoExpandMin"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-autoExpandMin">autoExpandMin</a></b> : Number<div class="mdesc">The minimum width the <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandColumn" ext:member="autoExpandColumn" ext:cls="Ext.grid.GridPanel">autoExpandColumn</a></tt>
can have (if enabled). Defaults to <tt>50</tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-autoHeight"></a><b><a href="source/Panel.html#cfg-Ext.Panel-autoHeight">autoHeight</a></b> : Boolean<div class="mdesc"><div class="short">true to use height:'auto', false to use fixed height (defaults to false).
Note: Setting autoHeight:true means that th...</div><div class="long"><tt>true</tt> to use height:'auto', <tt>false</tt> to use fixed height (defaults to <tt>false</tt>).
<b>Note</b>: Setting <tt>autoHeight:true</tt> means that the browser will manage the panel's height
based on its contents, and that Ext will not manage it at all. If the panel is within a layout that
manages dimensions (<tt>fit</tt>, <tt>border</tt>, etc.) then setting <tt>autoHeight:true</tt>
can cause issues with scrolling and will not generally work as expected since the panel will take
on the height of its contents rather than the height required by the Ext layout.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#autoHeight" ext:member="#autoHeight" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoShow"></a><b><a href="source/Component.html#cfg-Ext.Component-autoShow">autoShow</a></b> : Boolean<div class="mdesc"><div class="short">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render...</div><div class="long">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render (defaults to false).</div></div></td><td class="msource"><a href="output/Ext.Component.html#autoShow" ext:member="#autoShow" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-baseCls"></a><b><a href="source/Panel.html#cfg-Ext.Panel-baseCls">baseCls</a></b> : String<div class="mdesc"><div class="short">The base CSS class to apply to this panel's element (defaults to 'x-panel').
Another option available by default is t...</div><div class="long">The base CSS class to apply to this panel's element (defaults to <tt>'x-panel'</tt>).
<p>Another option available by default is to specify <tt>'x-plain'</tt> which strips all styling
except for required attributes for Ext layouts to function (e.g. overflow:hidden).
See <tt><a href="output/Ext.Panel.html#Ext.Panel-unstyled" ext:member="unstyled" ext:cls="Ext.Panel">unstyled</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#baseCls" ext:member="#baseCls" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bbar"></a><b><a href="source/Panel.html#cfg-Ext.Panel-bbar">bbar</a></b> : Object/Array<div class="mdesc"><div class="short">The bottom toolbar of the panel. This can be a Ext.Toolbar object, a toolbar config, or an array of
buttons/button co...</div><div class="long"><p>The bottom toolbar of the panel. This can be a <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Ext.Toolbar</a> object, a toolbar config, or an array of
buttons/button configs to be added to the toolbar.  Note that this is not available as a property after render.
To access the bottom toolbar after render, use <a href="output/Ext.Panel.html#Ext.Panel-getBottomToolbar" ext:member="getBottomToolbar" ext:cls="Ext.Panel">getBottomToolbar</a>.</p>
<p><b>Note:</b> Although a Toolbar may contain Field components, these will <b>not<b> be updated by a load
of an ancestor FormPanel. A Panel's toolbars are not part of the standard Container->Component hierarchy, and
so are not scanned to collect form items. However, the values <b>will</b> be submitted because form
submission parameters are collected from the DOM tree.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#bbar" ext:member="#bbar" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bbarCfg"></a><b><a href="source/Panel.html#cfg-Ext.Panel-bbarCfg">bbarCfg</a></b> : Object<div class="mdesc"><div class="short">A DomHelper element specification object specifying the element structure
of this Panel's bbar Element.  See bodyCfg ...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element specification object specifying the element structure
of this Panel's <a href="output/Ext.Panel.html#Ext.Panel-bbar" ext:member="bbar" ext:cls="Ext.Panel">bbar</a> Element.  See <tt><a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#bbarCfg" ext:member="#bbarCfg" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bodyCfg"></a><b><a href="source/Panel.html#cfg-Ext.Panel-bodyCfg">bodyCfg</a></b> : Object<div class="mdesc"><div class="short">A DomHelper element specification object may be specified for any
Panel Element.
By default, the Default element in t...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element specification object may be specified for any
Panel Element.</p>
<p>By default, the Default element in the table below will be used for the html markup to
create a child element with the commensurate Default class name (<tt>baseCls</tt> will be
replaced by <tt><a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a></tt>):</p>
<pre>
Panel      Default  Default             Custom      Additional       Additional
Element    element  class               element     class            style
========   ==========================   =========   ==============   ===========
<a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a>     div      <a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a>+'-header'   <a href="output/Ext.Panel.html#Ext.Panel-headerCfg" ext:member="headerCfg" ext:cls="Ext.Panel">headerCfg</a>   headerCssClass   headerStyle
<a href="output/Ext.Panel.html#Ext.Panel-bwrap" ext:member="bwrap" ext:cls="Ext.Panel">bwrap</a>      div      <a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a>+'-bwrap'     <a href="output/Ext.Panel.html#Ext.Panel-bwrapCfg" ext:member="bwrapCfg" ext:cls="Ext.Panel">bwrapCfg</a>    bwrapCssClass    bwrapStyle
+ tbar     div      <a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a>+'-tbar'       <a href="output/Ext.Panel.html#Ext.Panel-tbarCfg" ext:member="tbarCfg" ext:cls="Ext.Panel">tbarCfg</a>     tbarCssClass     tbarStyle
+ <a href="output/Ext.Panel.html#Ext.Panel-body" ext:member="body" ext:cls="Ext.Panel">body</a>     div      <a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a>+'-body'       <a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a>     <a href="output/Ext.Panel.html#Ext.Panel-bodyCssClass" ext:member="bodyCssClass" ext:cls="Ext.Panel">bodyCssClass</a>     <a href="output/Ext.Panel.html#Ext.Panel-bodyStyle" ext:member="bodyStyle" ext:cls="Ext.Panel">bodyStyle</a>
+ bbar     div      <a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a>+'-bbar'       <a href="output/Ext.Panel.html#Ext.Panel-bbarCfg" ext:member="bbarCfg" ext:cls="Ext.Panel">bbarCfg</a>     bbarCssClass     bbarStyle
+ <a href="output/Ext.Panel.html#Ext.Panel-footer" ext:member="footer" ext:cls="Ext.Panel">footer</a>   div      <a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a>+'-footer'   <a href="output/Ext.Panel.html#Ext.Panel-footerCfg" ext:member="footerCfg" ext:cls="Ext.Panel">footerCfg</a>   footerCssClass   footerStyle
</pre>
<p>Configuring a Custom element may be used, for example, to force the <a href="output/Ext.Panel.html#Ext.Panel-body" ext:member="body" ext:cls="Ext.Panel">body</a> Element
to use a different form of markup than is created by default. An example of this might be
to <a href="output/Ext.Element.html#Ext.Element-createChild" ext:member="createChild" ext:cls="Ext.Element">create a child</a> Panel containing a custom content, such as
a header, or forcing centering of all Panel content by having the body be a &lt;center&gt;
element:</p>
<pre><code><b>new</b> Ext.Panel({
    title: <em>'Message Title'</em>,
    renderTo: Ext.getBody(),
    width: 200, height: 130,
    <b>bodyCfg</b>: {
        tag: <em>'center'</em>,
        cls: <em>'x-panel-body'</em>,  <i>// Default class not applied <b>if</b> Custom element specified</i>
        html: <em>'Message'</em>
    },
    <a href="output/Ext.Panel.html#Ext.Panel-footer" ext:member="footer" ext:cls="Ext.Panel">footer</a>: true,
    footerCfg: {
        tag: <em>'h2'</em>,
        cls: <em>'x-panel-footer'</em>        <i>// same as the Default class</i>
        html: <em>'footer html'</em>
    },
    footerCssClass: <em>'custom-footer'</em>, <i>// additional css class, see <a href="output/Ext.element.html#Ext.element-addClass" ext:member="addClass" ext:cls="Ext.element">addClass</a></i>
    footerStyle:    <em>'background-color:red'</em> <i>// see <a href="output/Ext.Panel.html#Ext.Panel-bodyStyle" ext:member="bodyStyle" ext:cls="Ext.Panel">bodyStyle</a></i>
});</code></pre>
<p>The example above also explicitly creates a <tt><a href="output/Ext.Panel.html#Ext.Panel-footer" ext:member="footer" ext:cls="Ext.Panel">footer</a></tt> with custom markup and
styling applied.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#bodyCfg" ext:member="#bodyCfg" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bodyCssClass"></a><b><a href="source/Panel.html#cfg-Ext.Panel-bodyCssClass">bodyCssClass</a></b> : String/Object/Function<div class="mdesc"><div class="short">Additional css class selector to be applied to the body element in the format expected by
Ext.Element.addClass (defau...</div><div class="long">Additional css class selector to be applied to the <a href="output/Ext.Panel.html#Ext.Panel-body" ext:member="body" ext:cls="Ext.Panel">body</a> element in the format expected by
<a href="output/Ext.Element.html#Ext.Element-addClass" ext:member="addClass" ext:cls="Ext.Element">Ext.Element.addClass</a> (defaults to null). See <a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a>.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#bodyCssClass" ext:member="#bodyCssClass" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-border"></a><b><a href="source/Panel.html#cfg-Ext.Panel-border">border</a></b> : Boolean<div class="mdesc"><div class="short">True to display the borders of the panel's body element, false to hide them (defaults to true).  By default,
the bord...</div><div class="long">True to display the borders of the panel's body element, false to hide them (defaults to true).  By default,
the border is a 2px wide inset border, but this can be further altered by setting <a href="output/Ext.Panel.html#Ext.Panel-bodyBorder" ext:member="bodyBorder" ext:cls="Ext.Panel">bodyBorder</a> to false.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#border" ext:member="#border" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-buttonAlign"></a><b><a href="source/Panel.html#cfg-Ext.Panel-buttonAlign">buttonAlign</a></b> : String<div class="mdesc"><div class="short">The alignment of any buttons added to this panel.  Valid values are 'right',
'left' and 'center' (defaults to 'right'...</div><div class="long">The alignment of any <a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a> added to this panel.  Valid values are <tt>'right'</tt>,
<tt>'left'</tt> and <tt>'center'</tt> (defaults to <tt>'right'</tt>).</div></div></td><td class="msource"><a href="output/Ext.Panel.html#buttonAlign" ext:member="#buttonAlign" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-buttons"></a><b><a href="source/Panel.html#cfg-Ext.Panel-buttons">buttons</a></b> : Array<div class="mdesc"><div class="short">buttons will be used as items for the toolbar in
the footer (fbar). Typically the value of this configuration propert...</div><div class="long"><tt>buttons</tt> will be used as <tt><a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">items</a></tt> for the toolbar in
the footer (<tt><a href="output/Ext.Panel.html#Ext.Panel-fbar" ext:member="fbar" ext:cls="Ext.Panel">fbar</a></tt>). Typically the value of this configuration property will be
an array of <a href="output/Ext.Button.html" ext:cls="Ext.Button">Ext.Button</a>s or <a href="output/Ext.Button.html" ext:cls="Ext.Button">Ext.Button</a> configuration objects.
If an item is configured with <tt>minWidth</tt> or the Panel is configured with <tt>minButtonWidth</tt>,
that width will be applied to the item.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#buttons" ext:member="#buttons" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bwrapCfg"></a><b><a href="source/Panel.html#cfg-Ext.Panel-bwrapCfg">bwrapCfg</a></b> : Object<div class="mdesc"><div class="short">A DomHelper element specification object specifying the element structure
of this Panel's bwrap Element.  See bodyCfg...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element specification object specifying the element structure
of this Panel's <a href="output/Ext.Panel.html#Ext.Panel-bwrap" ext:member="bwrap" ext:cls="Ext.Panel">bwrap</a> Element.  See <tt><a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#bwrapCfg" ext:member="#bwrapCfg" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-clearCls"></a><b><a href="source/Component.html#cfg-Ext.Component-clearCls">clearCls</a></b> : String<div class="mdesc"><div class="short">The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide...</div><div class="long"><p>The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide field clearing (defaults to
<tt>'x-form-clear-left'</tt>).</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container
which has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout
manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>) and either a 
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> is specified or <tt>isFormField=true</tt> is specified.</p><br>
<p>See <a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#clearCls" ext:member="#clearCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-closable"></a><b><a href="source/Panel.html#cfg-Ext.Panel-closable">closable</a></b> : Boolean<div class="mdesc"><div class="short">Panels themselves do not directly support being closed, but some Panel subclasses do (like
Ext.Window) or a Panel Cla...</div><div class="long">Panels themselves do not directly support being closed, but some Panel subclasses do (like
<a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a>) or a Panel Class within an <a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">Ext.TabPanel</a>.  Specify <tt>true</tt>
to enable closing in such situations. Defaults to <tt>false</tt>.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#closable" ext:member="#closable" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-cls"></a><b><a href="source/Component.html#cfg-Ext.Component-cls">cls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's Element (defaults to '').  This can be
useful for ...</div><div class="long">An optional extra CSS class that will be added to this component's Element (defaults to '').  This can be
useful for adding customized styles to the component or any of its children using standard CSS rules.</div></div></td><td class="msource"><a href="output/Ext.Component.html#cls" ext:member="#cls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-cm"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-cm">cm</a></b> : Object<div class="mdesc">Shorthand for <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-colModel" ext:member="colModel" ext:cls="Ext.grid.GridPanel">colModel</a></tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-colModel"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-colModel">colModel</a></b> : Object<div class="mdesc">The <a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">Ext.grid.ColumnModel</a> to use when rendering the grid (required).</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-collapseFirst"></a><b><a href="source/Panel.html#cfg-Ext.Panel-collapseFirst">collapseFirst</a></b> : Boolean<div class="mdesc"><div class="short">true to make sure the collapse/expand toggle button always renders first (to the left of)
any other tools in the pane...</div><div class="long"><tt>true</tt> to make sure the collapse/expand toggle button always renders first (to the left of)
any other tools in the panel's title bar, <tt>false</tt> to render it last (defaults to <tt>true</tt>).</div></div></td><td class="msource"><a href="output/Ext.Panel.html#collapseFirst" ext:member="#collapseFirst" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-collapsed"></a><b><a href="source/Panel.html#cfg-Ext.Panel-collapsed">collapsed</a></b> : Boolean<div class="mdesc"><tt>true</tt> to render the panel collapsed, <tt>false</tt> to render it expanded (defaults to
<tt>false</tt>).</div></td><td class="msource"><a href="output/Ext.Panel.html#collapsed" ext:member="#collapsed" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-collapsedCls"></a><b><a href="source/Panel.html#cfg-Ext.Panel-collapsedCls">collapsedCls</a></b> : String<div class="mdesc">A CSS class to add to the panel's element after it has been collapsed (defaults to
<tt>'x-panel-collapsed'</tt>).</div></td><td class="msource"><a href="output/Ext.Panel.html#collapsedCls" ext:member="#collapsedCls" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-collapsible"></a><b><a href="source/Panel.html#cfg-Ext.Panel-collapsible">collapsible</a></b> : Boolean<div class="mdesc"><div class="short">True to make the panel collapsible and have the expand/collapse toggle button automatically rendered into
the header ...</div><div class="long">True to make the panel collapsible and have the expand/collapse toggle button automatically rendered into
the header tool button area, false to keep the panel statically sized with no button (defaults to false).</div></div></td><td class="msource"><a href="output/Ext.Panel.html#collapsible" ext:member="#collapsible" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-columnLines"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-columnLines">columnLines</a></b> : Boolean<div class="mdesc"><tt>true</tt> to add css for column separation lines.
Default is <tt>false</tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-columns"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-columns">columns</a></b> : Array<div class="mdesc"><div class="short">An array of columns to auto create a&#13;
Ext.grid.ColumnModel.  The ColumnModel may be explicitly created via the&#13;
colMo...</div><div class="long">An array of <a href="output/Ext.grid.Column.html" ext:cls="Ext.grid.Column">columns</a> to auto create a
<a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">Ext.grid.ColumnModel</a>.  The ColumnModel may be explicitly created via the
<tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-colModel" ext:member="colModel" ext:cls="Ext.grid.GridPanel">colModel</a></tt> configuration property.</div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ctCls"></a><b><a href="source/Component.html#cfg-Ext.Component-ctCls">ctCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's container. This can be useful for
adding customize...</div><div class="long"><p>An optional extra CSS class that will be added to this component's container. This can be useful for
adding customized styles to the container or any of its children using standard CSS rules.  See
<a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a>.<a href="output/Ext.layout.ContainerLayout.html#Ext.layout.ContainerLayout-extraCls" ext:member="extraCls" ext:cls="Ext.layout.ContainerLayout">extraCls</a> also.</p>
<p><b>Note</b>: <tt>ctCls</tt> defaults to <tt>''</tt> except for the following class
which assigns a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-layout-ct'</tt></li>
</ul></div>
To configure the above Class with an extra CSS class append to the default.  For example,
for BoxLayout (Hbox and Vbox):<pre><code>ctCls: <em>'x-box-layout-ct custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ctCls" ext:member="#ctCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-ddText"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-ddText">ddText</a></b> : String<div class="mdesc"><div class="short">Configures the text in the drag proxy.  Defaults to:&#13;
ddText : "{0} selected row{1}"&#13;
{0} is replaced with the number...</div><div class="long">Configures the text in the drag proxy.  Defaults to:
<pre><code>ddText : <em>"{0} selected row{1}"</em></code></pre>
<tt>{0}</tt> is replaced with the number of selected rows.</div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-deferRowRender"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-deferRowRender">deferRowRender</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to true to enable deferred row rendering.&#13;
This allows the GridPanel to be initially rendered empty, with th...</div><div class="long"><P>Defaults to <tt>true</tt> to enable deferred row rendering.</p>
<p>This allows the GridPanel to be initially rendered empty, with the expensive update of the row
structure deferred so that layouts with GridPanels appear more quickly.</p></div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-disableSelection"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-disableSelection">disableSelection</a></b> : Boolean<div class="mdesc"><p><tt>true</tt> to disable selections in the grid. Defaults to <tt>false</tt>.</p>
<p>Ignored if a <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-selModel" ext:member="selModel" ext:cls="Ext.grid.GridPanel">SelectionModel</a> is specified.</p></div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-disabled"></a><b><a href="source/Panel.html#cfg-Ext.Panel-disabled">disabled</a></b> : Boolean<div class="mdesc"><div class="short">Render this panel disabled (default is false). An important note when using the disabled
config on panels is that IE ...</div><div class="long">Render this panel disabled (default is <tt>false</tt>). An important note when using the disabled
config on panels is that IE will often fail to initialize the disabled mask element correectly if
the panel's layout has not yet completed by the time the Panel is disabled during the render process.
If you experience this issue, you may need to instead use the <a href="output/Ext.Panel.html#Ext.Panel-afterlayout" ext:member="afterlayout" ext:cls="Ext.Panel">afterlayout</a> event to initialize
the disabled state:
<pre><code><b>new</b> Ext.Panel({
    ...
    listeners: {
        <em>'afterlayout'</em>: {
            fn: <b>function</b>(p){
                p.disable();
            },
            single: true <i>// important, as many layouts can occur</i>
        }
    }
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Panel.html#disabled" ext:member="#disabled" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-draggable"></a><b><a href="source/Panel.html#cfg-Ext.Panel-draggable">draggable</a></b> : Boolean/Object<div class="mdesc"><div class="short">true to enable dragging of this Panel (defaults to false).
For custom drag/drop implementations, an Ext.Panel.DD conf...</div><div class="long"><p><tt>true</tt> to enable dragging of this Panel (defaults to <tt>false</tt>).</p>
<p>For custom drag/drop implementations, an <b>Ext.Panel.DD</b> config could also be passed
in this config instead of <tt>true</tt>. Ext.Panel.DD is an internal, undocumented class which
moves a proxy Element around in place of the Panel's element, but provides no other behaviour
during dragging or on drop. It is a subclass of <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a>, so behaviour may be
added by implementing the interface methods of <a href="output/Ext.dd.DragDrop.html" ext:cls="Ext.dd.DragDrop">Ext.dd.DragDrop</a> eg:
<pre><code><b>new</b> Ext.Panel({
    title: <em>'Drag me'</em>,
    x: 100,
    y: 100,
    renderTo: Ext.getBody(),
    floating: true,
    frame: true,
    width: 400,
    height: 200,
    draggable: {
<i>//      Config option of Ext.Panel.DD class.</i>
<i>//      It&#39;s a floating Panel, so <b>do</b> not show a placeholder proxy <b>in</b> the original position.</i>
        insertProxy: false,

<i>//      Called <b>for</b> each mousemove event <b>while</b> dragging the DD object.</i>
        onDrag : <b>function</b>(e){
<i>//          Record the x,y position of the drag proxy so that we can</i>
<i>//          position the Panel at end of drag.</i>
            <b>var</b> pel = this.proxy.getEl();
            this.x = pel.getLeft(true);
            this.y = pel.getTop(true);

<i>//          Keep the Shadow aligned <b>if</b> there is one.</i>
            <b>var</b> s = this.panel.getEl().shadow;
            <b>if</b> (s) {
                s.realign(this.x, this.y, pel.getWidth(), pel.getHeight());
            }
        },

<i>//      Called on the mouseup event.</i>
        endDrag : <b>function</b>(e){
            this.panel.setPosition(this.x, this.y);
        }
    }
}).show();</code></pre></div></div></td><td class="msource"><a href="output/Ext.Panel.html#draggable" ext:member="#draggable" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-enableColumnHide"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-enableColumnHide">enableColumnHide</a></b> : Boolean<div class="mdesc">Defaults to <tt>true</tt> to enable hiding of columns with the header context menu.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-enableColumnMove"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-enableColumnMove">enableColumnMove</a></b> : Boolean<div class="mdesc">Defaults to <tt>true</tt> to enable drag and drop reorder of columns. <tt>false</tt>
to turn off column reordering via drag drop.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-enableColumnResize"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-enableColumnResize">enableColumnResize</a></b> : Boolean<div class="mdesc"><tt>false</tt> to turn off column resizing for the whole grid. Defaults to <tt>true</tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-enableDragDrop"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-enableDragDrop">enableDragDrop</a></b> : Boolean<div class="mdesc"><div class="short">Enables dragging of the selected rows of the GridPanel. Defaults to false.&#13;
Setting this to true causes this GridPane...</div><div class="long"><p>Enables dragging of the selected rows of the GridPanel. Defaults to <tt>false</tt>.</p>
<p>Setting this to <b><tt>true</tt></b> causes this GridPanel's <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-getView" ext:member="getView" ext:cls="Ext.grid.GridPanel">GridView</a> to
create an instance of <a href="output/Ext.grid.GridDragZone.html" ext:cls="Ext.grid.GridDragZone">Ext.grid.GridDragZone</a>. <b>Note</b>: this is available only <b>after</b>
the Grid has been rendered as the GridView's <tt><a href="output/Ext.grid.GridView.html#Ext.grid.GridView-dragZone" ext:member="dragZone" ext:cls="Ext.grid.GridView">dragZone</a></tt>
property.</p>
<p>A cooperating <a href="output/Ext.dd.DropZone.html" ext:cls="Ext.dd.DropZone">DropZone</a> must be created who's implementations of
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeEnter" ext:member="onNodeEnter" ext:cls="Ext.dd.DropZone">onNodeEnter</a>, <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOver" ext:member="onNodeOver" ext:cls="Ext.dd.DropZone">onNodeOver</a>,
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOut" ext:member="onNodeOut" ext:cls="Ext.dd.DropZone">onNodeOut</a> and <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeDrop" ext:member="onNodeDrop" ext:cls="Ext.dd.DropZone">onNodeDrop</a> are able
to process the <a href="output/Ext.grid.GridDragZone.html#Ext.grid.GridDragZone-getDragData" ext:member="getDragData" ext:cls="Ext.grid.GridDragZone">data</a> which is provided.</p></div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-enableHdMenu"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-enableHdMenu">enableHdMenu</a></b> : Boolean<div class="mdesc">Defaults to <tt>true</tt> to enable the drop down button for menu in the headers.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-fbar"></a><b><a href="source/Panel.html#cfg-Ext.Panel-fbar">fbar</a></b> : Object/Array<div class="mdesc"><div class="short">A Toolbar object, a Toolbar config, or an array of
Buttons/Button configs, describing a Toolbar to be rendered into t...</div><div class="long"><p>A <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Toolbar</a> object, a Toolbar config, or an array of
<a href="output/Ext.Button.html" ext:cls="Ext.Button">Button</a>s/<a href="output/Ext.Button.html" ext:cls="Ext.Button">Button</a> configs, describing a <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Toolbar</a> to be rendered into this Panel's footer element.</p>
<p>After render, the <code>fbar</code> property will be an <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Toolbar</a> instance.</p>
<p>If <tt><a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a></tt> are specified, they will supersede the <tt>fbar</tt> configuration property.</p>
The Panel's <tt><a href="output/Ext.Panel.html#Ext.Panel-buttonAlign" ext:member="buttonAlign" ext:cls="Ext.Panel">buttonAlign</a></tt> configuration affects the layout of these items, for example:
<pre><code><b>var</b> w = <b>new</b> Ext.Window({
    height: 250,
    width: 500,
    bbar: <b>new</b> Ext.Toolbar({
        items: [{
            text: <em>'bbar Left'</em>
        },<em>'->'</em>,{
            text: <em>'bbar Right'</em>
        }]
    }),
    <a href="output/Ext.Panel.html#Ext.Panel-buttonAlign" ext:member="buttonAlign" ext:cls="Ext.Panel">buttonAlign</a>: <em>'left'</em>, <i>// anything but <em>'center'</em> or <em>'right'</em> and you can use <em>"-"</em>, and <em>"->"</em></i>
                                  <i>// to control the alignment of fbar items</i>
    fbar: [{
        text: <em>'fbar Left'</em>
    },<em>'->'</em>,{
        text: <em>'fbar Right'</em>
    }]
}).show();</code></pre>
<p><b>Note:</b> Although a Toolbar may contain Field components, these will <b>not<b> be updated by a load
of an ancestor FormPanel. A Panel's toolbars are not part of the standard Container->Component hierarchy, and
so are not scanned to collect form items. However, the values <b>will</b> be submitted because form
submission parameters are collected from the DOM tree.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#fbar" ext:member="#fbar" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-fieldLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-fieldLabel">fieldLabel</a></b> : String<div class="mdesc"><div class="short">The label text to display next to this Component (defaults to '').
Note: this config is only used when this Component...</div><div class="long"><p>The label text to display next to this Component (defaults to '').</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container which
has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout manager (eg. 
<a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>).</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#fieldLabel" ext:member="#fieldLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-floating"></a><b><a href="source/Panel.html#cfg-Ext.Panel-floating">floating</a></b> : Mixed<div class="mdesc"><div class="short">This property is used to configure the underlying Ext.Layer. Acceptable values for this
configuration property are:&lt;d...</div><div class="long"><p>This property is used to configure the underlying <a href="output/Ext.Layer.html" ext:cls="Ext.Layer">Ext.Layer</a>. Acceptable values for this
configuration property are:</p><div class="mdetail-params"><ul>
<li><b><tt>false</tt></b> : <b>Default.</b><div class="sub-desc">Display the panel inline where it is
rendered.</div></li>
<li><b><tt>true</tt></b> : <div class="sub-desc">Float the panel (absolute position it with automatic
shimming and shadow).<ul>
<div class="sub-desc">Setting floating to true will create an Ext.Layer for this panel and display the
panel at negative offsets so that it is hidden.</div>
<div class="sub-desc">Since the panel will be absolute positioned, the position must be set explicitly
<i>after</i> render (e.g., <tt>myPanel.setPosition(100,100);</tt>).</div>
<div class="sub-desc"><b>Note</b>: when floating a panel you should always assign a fixed width,
otherwise it will be auto width and will expand to fill to the right edge of the viewport.</div>
</ul></div></li>
<li><b><tt><a href="output/Ext.Layer.html" ext:cls="Ext.Layer">object</a></tt></b> : <div class="sub-desc">The specified object will be used
as the configuration object for the <a href="output/Ext.Layer.html" ext:cls="Ext.Layer">Ext.Layer</a> that will be created.</div></li>
</ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#floating" ext:member="#floating" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-footer"></a><b><a href="source/Panel.html#cfg-Ext.Panel-footer">footer</a></b> : Boolean<div class="mdesc"><div class="short">true to create the footer element explicitly, false to skip creating it. The footer
will be created automatically if ...</div><div class="long"><tt>true</tt> to create the footer element explicitly, false to skip creating it. The footer
will be created automatically if <tt><a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a></tt> or a <tt><a href="output/Ext.Panel.html#Ext.Panel-fbar" ext:member="fbar" ext:cls="Ext.Panel">fbar</a></tt> have
been configured.  See <tt><a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a></tt> for an example.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#footer" ext:member="#footer" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-footerCfg"></a><b><a href="source/Panel.html#cfg-Ext.Panel-footerCfg">footerCfg</a></b> : Object<div class="mdesc"><div class="short">A DomHelper element specification object specifying the element structure
of this Panel's footer Element.  See bodyCf...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element specification object specifying the element structure
of this Panel's <a href="output/Ext.Panel.html#Ext.Panel-footer" ext:member="footer" ext:cls="Ext.Panel">footer</a> Element.  See <tt><a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#footerCfg" ext:member="#footerCfg" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-frame"></a><b><a href="source/Panel.html#cfg-Ext.Panel-frame">frame</a></b> : Boolean<div class="mdesc"><div class="short">false by default to render with plain 1px square borders. true to render with
9 elements, complete with custom rounde...</div><div class="long"><tt>false</tt> by default to render with plain 1px square borders. <tt>true</tt> to render with
9 elements, complete with custom rounded corners (also see <a href="output/Ext.Element.html#Ext.Element-boxWrap" ext:member="boxWrap" ext:cls="Ext.Element">Ext.Element.boxWrap</a>).
<p>The template generated for each condition is depicted below:</p><pre><code><i>// frame = false</i>
&lt;div id=<em>"developer-specified-id-goes-here"</em> class=<em>"x-panel"</em>>

    &lt;div class=<em>"x-panel-header"</em>>&lt;span class=<em>"x-panel-header-text"</em>>Title: (frame:false)&lt;/span>&lt;/div>

    &lt;div class=<em>"x-panel-bwrap"</em>>
        &lt;div class=<em>"x-panel-body"</em>>&lt;p>html value goes here&lt;/p>&lt;/div>
    &lt;/div>
&lt;/div>

<i>// frame = true (create 9 elements)</i>
&lt;div id=<em>"developer-specified-id-goes-here"</em> class=<em>"x-panel"</em>>
    &lt;div class=<em>"x-panel-tl"</em>>&lt;div class=<em>"x-panel-tr"</em>>&lt;div class=<em>"x-panel-tc"</em>>
        &lt;div class=<em>"x-panel-header"</em>>&lt;span class=<em>"x-panel-header-text"</em>>Title: (frame:true)&lt;/span>&lt;/div>
    &lt;/div>&lt;/div>&lt;/div>

    &lt;div class=<em>"x-panel-bwrap"</em>>
        &lt;div class=<em>"x-panel-ml"</em>>&lt;div class=<em>"x-panel-mr"</em>>&lt;div class=<em>"x-panel-mc"</em>>
            &lt;div class=<em>"x-panel-body"</em>>&lt;p>html value goes here&lt;/p>&lt;/div>
        &lt;/div>&lt;/div>&lt;/div>

        &lt;div class=<em>"x-panel-bl"</em>>&lt;div class=<em>"x-panel-br"</em>>&lt;div class=<em>"x-panel-bc"</em>/>
        &lt;/div>&lt;/div>&lt;/div>
&lt;/div></code></pre></div></div></td><td class="msource"><a href="output/Ext.Panel.html#frame" ext:member="#frame" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-header"></a><b><a href="source/Panel.html#cfg-Ext.Panel-header">header</a></b> : Boolean<div class="mdesc"><div class="short">true to create the Panel's header element explicitly, false to skip creating
it.  If a title is set the header will b...</div><div class="long"><tt>true</tt> to create the Panel's header element explicitly, <tt>false</tt> to skip creating
it.  If a <tt><a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a></tt> is set the header will be created automatically, otherwise it will not.
If a <tt><a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a></tt> is set but <tt>header</tt> is explicitly set to <tt>false</tt>, the header
will not be rendered.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#header" ext:member="#header" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-headerAsText"></a><b><a href="source/Panel.html#cfg-Ext.Panel-headerAsText">headerAsText</a></b> : Boolean<div class="mdesc"><tt>true</tt> to display the panel <tt><a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a></tt> in the <tt><a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a></tt>,
<tt>false</tt> to hide it (defaults to <tt>true</tt>).</div></td><td class="msource"><a href="output/Ext.Panel.html#headerAsText" ext:member="#headerAsText" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-headerCfg"></a><b><a href="source/Panel.html#cfg-Ext.Panel-headerCfg">headerCfg</a></b> : Object<div class="mdesc"><div class="short">A DomHelper element specification object specifying the element structure
of this Panel's header Element.  See bodyCf...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element specification object specifying the element structure
of this Panel's <a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a> Element.  See <tt><a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#headerCfg" ext:member="#headerCfg" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-height"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-height">height</a></b> : Number<div class="mdesc">The height of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#height" ext:member="#height" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#cfg-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">Render this component hidden (default is false).</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-hideCollapseTool"></a><b><a href="source/Panel.html#cfg-Ext.Panel-hideCollapseTool">hideCollapseTool</a></b> : Boolean<div class="mdesc"><tt>true</tt> to hide the expand/collapse toggle button when <tt><a href="output/Ext.Panel.html#Ext.Panel-collapsible" ext:member="collapsible" ext:cls="Ext.Panel">collapsible</a> = true</tt>,
<tt>false</tt> to display it (defaults to <tt>false</tt>).</div></td><td class="msource"><a href="output/Ext.Panel.html#hideCollapseTool" ext:member="#hideCollapseTool" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-hideHeaders"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-hideHeaders">hideHeaders</a></b> : Boolean<div class="mdesc">True to hide the grid's header. Defaults to <code>false</code>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-hideLabel">hideLabel</a></b> : Boolean<div class="mdesc"><div class="short">true to completely hide the label element
(label and separator). Defaults to false.
By default, even if you do not sp...</div><div class="long"><p><tt>true</tt> to completely hide the label element
(<a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">label</a> and <a href="output/Ext.Component.html#Ext.Component-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.Component">separator</a>). Defaults to <tt>false</tt>.
By default, even if you do not specify a <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> the space will still be
reserved so that the field will line up with other fields that do have labels.
Setting this to <tt>true</tt> will cause the field to not reserve that space.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>
        hideLabel: true
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideLabel" ext:member="#hideLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideMode"></a><b><a href="source/Component.html#cfg-Ext.Component-hideMode">hideMode</a></b> : String<div class="mdesc"><div class="short">How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset p...</div><div class="long"><p>How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset position) and "display" (css display) - defaults to "display".</p>
<p>For Containers which may be hidden and shown as part of a <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">card layout</a> Container such as a
<a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">TabPanel</a>, it is recommended that hideMode is configured as "offsets". This ensures
that hidden Components still have height and width so that layout managers can perform measurements when
calculating layouts.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideMode" ext:member="#hideMode" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideParent"></a><b><a href="source/Component.html#cfg-Ext.Component-hideParent">hideParent</a></b> : Boolean<div class="mdesc"><div class="short">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the...</div><div class="long">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the component itself (defaults to false).  For example, this can be used as a shortcut for a hide
button on a window by setting hide:true on the button when adding it to its parent container.</div></div></td><td class="msource"><a href="output/Ext.Component.html#hideParent" ext:member="#hideParent" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-iconCls"></a><b><a href="source/Panel.html#cfg-Ext.Panel-iconCls">iconCls</a></b> : String<div class="mdesc"><div class="short">The CSS class selector that specifies a background image to be used as the header icon (defaults to '').
An example o...</div><div class="long">The CSS class selector that specifies a background image to be used as the header icon (defaults to '').
<p>An example of specifying a custom icon class would be something like:
</p><code><pre>
<i>// specify the property <b>in</b> the config <b>for</b> the class:</i>
     ...
     iconCls: <em>'my-icon'</em>

<i>// css class that specifies background image to be used as the icon image:</i>
.my-icon { background-image: url(../images/my-icon.gif) 0 6px no-repeat !important; }
</pre></code></div></div></td><td class="msource"><a href="output/Ext.Panel.html#iconCls" ext:member="#iconCls" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-id"></a><b><a href="source/Component.html#cfg-Ext.Component-id">id</a></b> : String<div class="mdesc"><div class="short">The unique id of this component (defaults to an auto-assigned id).
You should assign an id if you need to be able to ...</div><div class="long"><p>The <b>unique</b> id of this component (defaults to an <a href="output/Ext.Component.html#Ext.Component-getId" ext:member="getId" ext:cls="Ext.Component">auto-assigned id</a>).
You should assign an id if you need to be able to access the component later and you do
not have an object reference available (e.g., using <a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>).</p>
<p>Note that this id will also be used as the element id for the containing HTML element
that is rendered to the page for this component. This allows you to write id-based CSS
rules to style the specific instance of this component uniquely, and also to select
sub-elements using this component's id as the parent.</p>
<p><b>Note</b>: to avoid complications imposed by a unique <tt>id</tt> see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#id" ext:member="#id" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemCls"></a><b><a href="source/Component.html#cfg-Ext.Component-itemCls">itemCls</a></b> : String<div class="mdesc"><div class="short">An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, itemCls at th...</div><div class="long"><p>An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, <tt>itemCls</tt> at the <b>field</b> level will override
the default <tt>itemCls</tt> supplied at the <b>container</b> level. The value specified for 
<tt>itemCls</tt> will be added to the default class (<tt>'x-form-item'</tt>).</p>
<p>Since it is applied to the item wrapper (see
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>), it allows
you to write standard CSS rules that can apply to the field, the label (if specified), or
any other element within the markup for the field.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.</p><br>
Example use:<pre><code><i>// Apply a style to the field<em>'s <b>label</b>:</i>
&lt;style>
    .required .x-form-item-<b>label</b> {font-weight:bold;color:red;}
&lt;/style>

<b>new</b> Ext.FormPanel({
	height: 100,
	renderTo: Ext.getBody(),
	items: [{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Name<em>',
		itemCls: '</em>required<em>' <i>//this <b>label</b> will be styled</i>
	},{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Favorite Color<em>'
	}]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemCls" ext:member="#itemCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemId"></a><b><a href="source/Component.html#cfg-Ext.Component-itemId">itemId</a></b> : String<div class="mdesc"><div class="short">An itemId can be used as an alternative way to get a reference to a component
when no object reference is available. ...</div><div class="long"><p>An <tt>itemId</tt> can be used as an alternative way to get a reference to a component
when no object reference is available.  Instead of using an <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt> with
<a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>, use <tt>itemId</tt> with
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a> which will retrieve
<tt>itemId</tt>'s or <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>'s. Since <tt>itemId</tt>'s are an index to the
container's internal MixedCollection, the <tt>itemId</tt> is scoped locally to the container -- 
avoiding potential conflicts with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a> which requires a <b>unique</b>
<tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<pre><code><b>var</b> c = <b>new</b> Ext.Panel({ <i>//</i>
    <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 300,
    <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a>: document.body,
    <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'auto'</em>,
    <a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">items</a>: [
        {
            itemId: <em>'p1'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 1'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        },
        {
            itemId: <em>'p2'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 2'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        }
    ]
})
p1 = c.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p1'</em>); <i>// not the same as <a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">Ext.getCmp()</a></i>
p2 = p1.<a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p2'</em>); <i>// reference via a sibling</i></code></pre>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemId" ext:member="#itemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-keys"></a><b><a href="source/Panel.html#cfg-Ext.Panel-keys">keys</a></b> : Object/Array<div class="mdesc"><div class="short">A Ext.KeyMap config object (in the format expected by Ext.KeyMap.addBinding
used to assign custom key handling to thi...</div><div class="long">A <a href="output/Ext.KeyMap.html" ext:cls="Ext.KeyMap">Ext.KeyMap</a> config object (in the format expected by <a href="output/Ext.KeyMap.html#Ext.KeyMap-addBinding" ext:member="addBinding" ext:cls="Ext.KeyMap">Ext.KeyMap.addBinding</a>
used to assign custom key handling to this panel (defaults to <tt>null</tt>).</div></div></td><td class="msource"><a href="output/Ext.Panel.html#keys" ext:member="#keys" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelSeparator"></a><b><a href="source/Component.html#cfg-Ext.Component-labelSeparator">labelSeparator</a></b> : String<div class="mdesc"><div class="short">The separator to display after the text of each
fieldLabel.  This property may be configured at various levels.
The o...</div><div class="long"><p>The separator to display after the text of each
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.  This property may be configured at various levels.
The order of precedence is:
<div class="mdetail-params"><ul>
<li>field / component level</li>
<li>container level</li>
<li><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">layout level</a> (defaults to colon <tt>':'</tt>)</li>
</ul></div>
To display no separator for this field's label specify empty string ''.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    layoutConfig: {
        labelSeparator: <em>'~'</em>   <i>// layout config has lowest priority (defaults to <em>':'</em>)</i>
    },
    <a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">labelSeparator</a>: <em>'>>'</em>,     <i>// config at container level </i>
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 1'</em>,
        labelSeparator: <em>'...'</em> <i>// field/component level config supersedes others</i>
    },{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 2'</em> <i>// labelSeparator will be <em>'='</em></i>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelSeparator" ext:member="#labelSeparator" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelStyle"></a><b><a href="source/Component.html#cfg-Ext.Component-labelStyle">labelStyle</a></b> : String<div class="mdesc"><div class="short">A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle val...</div><div class="long"><p>A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle value if set (eg,
<tt><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelStyle" ext:member="labelStyle" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.labelStyle</a></tt> , or '').</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>,
        labelStyle: <em>'font-weight:bold;'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelStyle" ext:member="#labelStyle" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-loadMask"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-loadMask">loadMask</a></b> : Object<div class="mdesc">An <a href="output/Ext.LoadMask.html" ext:cls="Ext.LoadMask">Ext.LoadMask</a> config or true to mask the grid while
loading. Defaults to <code>false</code>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-maskDisabled"></a><b><a href="source/Panel.html#cfg-Ext.Panel-maskDisabled">maskDisabled</a></b> : Boolean<div class="mdesc"><div class="short">true to mask the panel when it is disabled, false to not mask it (defaults
to true).  Either way, the panel will alwa...</div><div class="long"><tt>true</tt> to mask the panel when it is <a href="output/Ext.Panel.html#Ext.Panel-disabled" ext:member="disabled" ext:cls="Ext.Panel">disabled</a>, <tt>false</tt> to not mask it (defaults
to <tt>true</tt>).  Either way, the panel will always tell its contained elements to disable themselves
when it is disabled, but masking the panel can provide an additional visual cue that the panel is
disabled.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#maskDisabled" ext:member="#maskDisabled" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-maxHeight"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-maxHeight">maxHeight</a></b> : Number<div class="mdesc">Sets the maximum height of the grid - ignored if <tt>autoHeight</tt> is not on.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-minButtonWidth"></a><b><a href="source/Panel.html#cfg-Ext.Panel-minButtonWidth">minButtonWidth</a></b> : Number<div class="mdesc">Minimum width in pixels of all <a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a> in this panel (defaults to <tt>75</tt>)</div></td><td class="msource"><a href="output/Ext.Panel.html#minButtonWidth" ext:member="#minButtonWidth" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-minColumnWidth"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-minColumnWidth">minColumnWidth</a></b> : Number<div class="mdesc">The minimum width a column can be resized to. Defaults to <tt>25</tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-overCls"></a><b><a href="source/Component.html#cfg-Ext.Component-overCls">overCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and...</div><div class="long">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and removed when the mouse moves out. (defaults to '').  This can be
useful for adding customized "active" or "hover" styles to the component or any of its children using standard CSS rules.</div></div></td><td class="msource"><a href="output/Ext.Component.html#overCls" ext:member="#overCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageX"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageX">pageX</a></b> : Number<div class="mdesc">The page level x coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageX" ext:member="#pageX" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageY"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageY">pageY</a></b> : Number<div class="mdesc">The page level y coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageY" ext:member="#pageY" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-plugins"></a><b><a href="source/Component.html#cfg-Ext.Component-plugins">plugins</a></b> : Object/Array<div class="mdesc"><div class="short">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a ...</div><div class="long">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a valid plugin is that it contain an init method that accepts a reference of type Ext.Component.
When a component is created, if any plugins are available, the component will call the init method on each
plugin, passing a reference to itself.  Each plugin can then call methods or respond to events on the
component as needed to provide its functionality.</div></div></td><td class="msource"><a href="output/Ext.Component.html#plugins" ext:member="#plugins" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-preventBodyReset"></a><b><a href="source/Panel.html#cfg-Ext.Panel-preventBodyReset">preventBodyReset</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to false.  When set to true, an extra css class 'x-panel-normal'
will be added the the panel's element, effe...</div><div class="long">Defaults to <tt>false</tt>.  When set to <tt>true</tt>, an extra css class <tt>'x-panel-normal'</tt>
will be added the the panel's element, effectively applying css styles suggested by the W3C
(see http://www.w3.org/TR/CSS21/sample.html) to the Panel's body element.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#preventBodyReset" ext:member="#preventBodyReset" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ptype"></a><b><a href="source/Component.html#cfg-Ext.Component-ptype">ptype</a></b> : String<div class="mdesc"><div class="short">The registered ptype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>ptype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a Plugin is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>ptype</tt> will be looked up at render time up to determine what
type of Plugin to create.<br><br>
If you create your own Plugins, you may register them using
<a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerPlugin" ext:member="registerPlugin" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerPlugin</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#ptype" ext:member="#ptype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ref"></a><b><a href="source/Component.html#cfg-Ext.Component-ref">ref</a></b> : String<div class="mdesc"><div class="short">A path specification, relative to the Component's ownerCt specifying into which
ancestor Container to place a named r...</div><div class="long"><p>A path specification, relative to the Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a> specifying into which
ancestor Container to place a named reference to this Component.</p>
<p>The ancestor axis can be traversed by using '/' characters in the path.
For example, to put a reference to a Toolbar Button into <i>the Panel which owns the Toolbar</i>:</p><pre><code><b>var</b> myGrid = <b>new</b> Ext.grid.EditorGridPanel({
    title: <em>'My EditorGridPanel'</em>,
    store: myStore,
    colModel: myColModel,
    tbar: [{
        text: <em>'Save'</em>,
        handler: saveChanges,
        disabled: true,
        ref: <em>'../saveButton'</em>
    }],
    listeners: {
        afteredit: <b>function</b>() {
<i>//          The button reference is <b>in</b> the GridPanel</i>
            myGrid.saveButton.enable();
        }
    }
});</code></pre>
<p>In the code above, if the ref had been <code><em>'saveButton'</em></code> the reference would
have been placed into the Toolbar. Each '/' in the ref moves up one level from the
Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ref" ext:member="#ref" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-region"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-region">region</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the...</div><div class="long"><p><b>Note</b>: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a></b>
layout manager (eg. specifying <tt>layout:'border'</tt>).</p><br>
<p>See <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">Ext.layout.BorderLayout</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#region" ext:member="#region" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-renderTo"></a><b><a href="source/Component.html#cfg-Ext.Component-renderTo">renderTo</a></b> : Mixed<div class="mdesc"><div class="short">Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.
Notes ...</div><div class="long"><p>Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.</p><div><ul>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When using this config, a call to render() is not required.</div>
<div class="sub-desc">Do <u>not</u> use this option if the Component is to be a child item of
a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>. It is the responsibility of the
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>'s <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout manager</a>
to render its child items.</div>
</ul></li>
</ul></div>
<p>See <tt><a href="output/Ext.Component.html#Ext.Component-render" ext:member="render" ext:cls="Ext.Component">render</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#renderTo" ext:member="#renderTo" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-selModel"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-selModel">selModel</a></b> : Object<div class="mdesc"><div class="short">Any subclass of Ext.grid.AbstractSelectionModel that will provide&#13;
the selection model for the grid (defaults to Ext....</div><div class="long">Any subclass of <a href="output/Ext.grid.AbstractSelectionModel.html" ext:cls="Ext.grid.AbstractSelectionModel">Ext.grid.AbstractSelectionModel</a> that will provide
the selection model for the grid (defaults to <a href="output/Ext.grid.RowSelectionModel.html" ext:cls="Ext.grid.RowSelectionModel">Ext.grid.RowSelectionModel</a> if not specified).</div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-shadow"></a><b><a href="source/Panel.html#cfg-Ext.Panel-shadow">shadow</a></b> : Boolean/String<div class="mdesc"><div class="short">true (or a valid Ext.Shadow Ext.Shadow.mode value) to display a shadow behind the
panel, false to display no shadow (...</div><div class="long"><tt>true</tt> (or a valid Ext.Shadow <a href="output/Ext.Shadow.html#Ext.Shadow-mode" ext:member="mode" ext:cls="Ext.Shadow">Ext.Shadow.mode</a> value) to display a shadow behind the
panel, <tt>false</tt> to display no shadow (defaults to <tt>'sides'</tt>).  Note that this option
only applies when <tt><a href="output/Ext.Panel.html#Ext.Panel-floating" ext:member="floating" ext:cls="Ext.Panel">floating</a> = true</tt>.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#shadow" ext:member="#shadow" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-shadowOffset"></a><b><a href="source/Panel.html#cfg-Ext.Panel-shadowOffset">shadowOffset</a></b> : Number<div class="mdesc"><div class="short">The number of pixels to offset the shadow if displayed (defaults to 4). Note that this
option only applies when float...</div><div class="long">The number of pixels to offset the shadow if displayed (defaults to <tt>4</tt>). Note that this
option only applies when <tt><a href="output/Ext.Panel.html#Ext.Panel-floating" ext:member="floating" ext:cls="Ext.Panel">floating</a> = true</tt>.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#shadowOffset" ext:member="#shadowOffset" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-shim"></a><b><a href="source/Panel.html#cfg-Ext.Panel-shim">shim</a></b> : Boolean<div class="mdesc"><div class="short">false to disable the iframe shim in browsers which need one (defaults to true).
Note that this option only applies wh...</div><div class="long"><tt>false</tt> to disable the iframe shim in browsers which need one (defaults to <tt>true</tt>).
Note that this option only applies when <tt><a href="output/Ext.Panel.html#Ext.Panel-floating" ext:member="floating" ext:cls="Ext.Panel">floating</a> = true</tt>.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#shim" ext:member="#shim" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-sm"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-sm">sm</a></b> : Object<div class="mdesc">Shorthand for <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-selModel" ext:member="selModel" ext:cls="Ext.grid.GridPanel">selModel</a></tt>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-stateEvents"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-stateEvents">stateEvents</a></b> : Array<div class="mdesc"><div class="short">An array of events that, when fired, should trigger this component to save its state.&#13;
Defaults to:stateEvents: ["col...</div><div class="long">An array of events that, when fired, should trigger this component to save its state.
Defaults to:<pre><code>stateEvents: [<em>"columnmove"</em>, <em>"columnresize"</em>, <em>"sortchange"</em>]</code></pre>
<p>These can be any types of events supported by this component, including browser or
custom events (e.g., <tt>['click', 'customerchange']</tt>).</p>
<p>See <a href="output/Ext.Component.html#Ext.Component-stateful" ext:member="stateful" ext:cls="Ext.Component">Ext.Component.stateful</a> for an explanation of saving and restoring
Component state.</p></div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateId"></a><b><a href="source/Component.html#cfg-Ext.Component-stateId">stateId</a></b> : String<div class="mdesc"><div class="short">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, o...</div><div class="long">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, otherwise null if the component is using a generated id).
<p>See <a href="output/Ext.Component.html#Ext.Component-stateful" ext:member="stateful" ext:cls="Ext.Component">stateful</a> for an explanation of saving and restoring Component state.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateId" ext:member="#stateId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateful"></a><b><a href="source/Component.html#cfg-Ext.Component-stateful">stateful</a></b> : Boolean<div class="mdesc"><div class="short">A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startu...</div><div class="long"><p>A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startup. The component must have either a <a href="output/Ext.Component.html#Ext.Component-stateId" ext:member="stateId" ext:cls="Ext.Component">stateId</a> or <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>
assigned for state to be managed.  Auto-generated ids are not guaranteed to be stable across page
loads and cannot be relied upon to save and restore the same state for a component.<p>
<p>For state saving to work, the state manager's provider must have been set to an implementation
of <a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> which overrides the <a href="output/Ext.state.Provider.html#Ext.state.Provider-set" ext:member="set" ext:cls="Ext.state.Provider">set</a>
and <a href="output/Ext.state.Provider.html#Ext.state.Provider-get" ext:member="get" ext:cls="Ext.state.Provider">get</a> methods to save and recall name/value pairs.
A built-in implementation, <a href="output/Ext.state.CookieProvider.html" ext:cls="Ext.state.CookieProvider">Ext.state.CookieProvider</a> is available.</p>
<p>To set the state provider for the current page:</p>
<pre><code>Ext.state.Manager.setProvider(<b>new</b> Ext.state.CookieProvider());</code></pre>
<p>A stateful Component attempts to save state when one of the events listed in the <a href="output/Ext.Component.html#Ext.Component-stateEvents" ext:member="stateEvents" ext:cls="Ext.Component">stateEvents</a>
configuration fires.</p>
To save state, A stateful Component first serializes its state by calling <b><tt>getState</tt></b>. By default,
this function does nothing. The developer must provide an implementation which returns an object hash
which represents the Component's restorable state.</p>
<p>The value yielded by getState is passed to <a href="output/Ext.state.Manager.html#Ext.state.Manager-set" ext:member="set" ext:cls="Ext.state.Manager">Ext.state.Manager.set</a> which uses the configured
<a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> to save the object keyed by the Component's <a href="output/stateId.html" ext:cls="stateId">stateId</a>, or,
if that is not specified, its <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>During construction, a stateful Component attempts to <i>restore</i> its state by calling
<a href="output/Ext.state.Manager.html#Ext.state.Manager-get" ext:member="get" ext:cls="Ext.state.Manager">Ext.state.Manager.get</a> passing the (@link #stateId}, or, if that is not specified, the <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>The resulting object is passed to <b><tt>applyState</tt></b>. The default implementation of applyState
simply copies properties into the object, but a developer may override this to support more behaviour.</p>
<p>You can perform extra processing on state save and restore by attaching handlers to the
<a href="output/Ext.Component.html#Ext.Component-beforestaterestore" ext:member="beforestaterestore" ext:cls="Ext.Component">beforestaterestore</a>, <a href="output/Ext.Component.html#Ext.Component-staterestore" ext:member="staterestore" ext:cls="Ext.Component">staterestore</a>, <a href="output/Ext.Component.html#Ext.Component-beforestatesave" ext:member="beforestatesave" ext:cls="Ext.Component">beforestatesave</a> and <a href="output/Ext.Component.html#Ext.Component-statesave" ext:member="statesave" ext:cls="Ext.Component">statesave</a> events</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateful" ext:member="#stateful" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-store"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-store">store</a></b> : Ext.data.Store<div class="mdesc">The <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> the grid should use as its data source (required).</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-stripeRows"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-stripeRows">stripeRows</a></b> : Boolean<div class="mdesc"><div class="short">true to stripe the rows. Default is false.&#13;
This causes the CSS class x-grid3-row-alt to be added to alternate rows o...</div><div class="long"><tt>true</tt> to stripe the rows. Default is <tt>false</tt>.
<p>This causes the CSS class <tt><b>x-grid3-row-alt</b></tt> to be added to alternate rows of
the grid. A default CSS rule is provided which sets a background colour, but you can override this
with a rule which either overrides the <b>background-color</b> style using the "!important"
modifier, or which uses a CSS selector of higher specificity.</p></div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-style"></a><b><a href="source/Component.html#cfg-Ext.Component-style">style</a></b> : String<div class="mdesc"><div class="short">A custom style specification to be applied to this component's Element.  Should be a valid argument to
Ext.Element.ap...</div><div class="long">A custom style specification to be applied to this component's Element.  Should be a valid argument to
<a href="output/Ext.Element.html#Ext.Element-applyStyles" ext:member="applyStyles" ext:cls="Ext.Element">Ext.Element.applyStyles</a>.
<pre><code><b>new</b> Ext.Panel({
    title: <em>'Some Title'</em>,
    renderTo: Ext.getBody(),
    width: 400, height: 300,
    layout: <em>'form'</em>,
    items: [{
        xtype: <em>'textarea'</em>,
        style: {
            width: <em>'95%'</em>,
            marginBottom: <em>'10px'</em>
        }
    },
        <b>new</b> Ext.Button({
            text: <em>'Send'</em>,
            minWidth: <em>'100'</em>,
            style: {
                marginBottom: <em>'10px'</em>
            }
        })
    ]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#style" ext:member="#style" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-tabTip"></a><b><a href="source/Panel.html#cfg-Ext.Panel-tabTip">tabTip</a></b> : String<div class="mdesc"><div class="short">A string to be used as innerHTML (html tags are accepted) to show in a tooltip when mousing over
the tab of a Ext.Pan...</div><div class="long">A string to be used as innerHTML (html tags are accepted) to show in a tooltip when mousing over
the tab of a Ext.Panel which is an item of a <a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">Ext.TabPanel</a>. <a href="output/Ext.QuickTips.html" ext:cls="Ext.QuickTips">Ext.QuickTips</a>.init()
must be called in order for the tips to render.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#tabTip" ext:member="#tabTip" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-tbar"></a><b><a href="source/Panel.html#cfg-Ext.Panel-tbar">tbar</a></b> : Object/Array<div class="mdesc"><div class="short">The top toolbar of the panel. This can be a Ext.Toolbar object, a toolbar config, or an array of
buttons/button confi...</div><div class="long"><p>The top toolbar of the panel. This can be a <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Ext.Toolbar</a> object, a toolbar config, or an array of
buttons/button configs to be added to the toolbar.  Note that this is not available as a property after render.
To access the top toolbar after render, use <a href="output/Ext.Panel.html#Ext.Panel-getTopToolbar" ext:member="getTopToolbar" ext:cls="Ext.Panel">getTopToolbar</a>.</p>
<p><b>Note:</b> Although a Toolbar may contain Field components, these will <b>not<b> be updated by a load
of an ancestor FormPanel. A Panel's toolbars are not part of the standard Container->Component hierarchy, and
so are not scanned to collect form items. However, the values <b>will</b> be submitted because form
submission parameters are collected from the DOM tree.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#tbar" ext:member="#tbar" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-tbarCfg"></a><b><a href="source/Panel.html#cfg-Ext.Panel-tbarCfg">tbarCfg</a></b> : Object<div class="mdesc"><div class="short">A DomHelper element specification object specifying the element structure
of this Panel's tbar Element.  See bodyCfg ...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element specification object specifying the element structure
of this Panel's <a href="output/Ext.Panel.html#Ext.Panel-tbar" ext:member="tbar" ext:cls="Ext.Panel">tbar</a> Element.  See <tt><a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#tbarCfg" ext:member="#tbarCfg" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-title"></a><b><a href="source/Panel.html#cfg-Ext.Panel-title">title</a></b> : String<div class="mdesc"><div class="short">The title text to be used as innerHTML (html tags are accepted) to display in the panel
header (defaults to ''). When...</div><div class="long">The title text to be used as innerHTML (html tags are accepted) to display in the panel
<tt><a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a></tt> (defaults to ''). When a <tt>title</tt> is specified the
<tt><a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a></tt> element will automatically be created and displayed unless
<a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a> is explicitly set to <tt>false</tt>.  If you do not want to specify a
<tt>title</tt> at config time, but you may want one later, you must either specify a non-empty
<tt>title</tt> (a blank space ' ' will do) or <tt>header:true</tt> so that the container
element will get created.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#title" ext:member="#title" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-titleCollapse"></a><b><a href="source/Panel.html#cfg-Ext.Panel-titleCollapse">titleCollapse</a></b> : Boolean<div class="mdesc"><div class="short">true to allow expanding and collapsing the panel (when collapsible = true)
by clicking anywhere in the header bar, fa...</div><div class="long"><tt>true</tt> to allow expanding and collapsing the panel (when <tt><a href="output/Ext.Panel.html#Ext.Panel-collapsible" ext:member="collapsible" ext:cls="Ext.Panel">collapsible</a> = true</tt>)
by clicking anywhere in the header bar, <tt>false</tt>) to allow it only by clicking to tool button
(defaults to <tt>false</tt>)). If this panel is a child item of a border layout also see the
<a href="output/Ext.layout.BorderLayout.Region.html" ext:cls="Ext.layout.BorderLayout.Region">BorderLayout.Region</a>
<tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-floatable" ext:member="floatable" ext:cls="Ext.layout.BorderLayout.Region">floatable</a></tt> config option.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#titleCollapse" ext:member="#titleCollapse" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-toolTemplate"></a><b><a href="source/Panel.html#cfg-Ext.Panel-toolTemplate">toolTemplate</a></b> : Ext.Template/Ext.XTemplate<div class="mdesc"><div class="short">A Template used to create tools in the header Element. Defaults to:new Ext.Template('&amp;lt;div class="x-tool x-tool-{id...</div><div class="long"><p>A Template used to create <a href="output/Ext.Panel.html#Ext.Panel-tools" ext:member="tools" ext:cls="Ext.Panel">tools</a> in the <a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a> Element. Defaults to:</p><pre><code><b>new</b> Ext.Template(<em>'&lt;div class=<em>"x-tool x-tool-{id}"</em>>&amp;#160;&lt;/div>'</em>)</code></pre>
<p>This may may be overridden to provide a custom DOM structure for tools based upon a more
complex XTemplate. The template's data is a single tool configuration object (Not the entire Array)
as specified in <a href="output/Ext.Panel.html#Ext.Panel-tools" ext:member="tools" ext:cls="Ext.Panel">tools</a>.  In the following example an &lt;a> tag is used to provide a
visual indication when hovering over the tool:</p><pre><code><b>var</b> win = <b>new</b> Ext.Window({
    tools: [{
        id: <em>'download'</em>,
        href: <em>'/MyPdfDoc.pdf'</em>
    }],
    toolTemplate: <b>new</b> Ext.XTemplate(
        <em>'&lt;tpl <b>if</b>=<em>"id==\'</em>download\<em>'"</em>>'</em>,
            <em>'&lt;a class=<em>"x-tool x-tool-pdf"</em> href=<em>"{href}"</em>>&lt;/a>'</em>,
        <em>'&lt;/tpl>'</em>,
        <em>'&lt;tpl <b>if</b>=<em>"id!=\'</em>download\<em>'"</em>>'</em>,
            <em>'&lt;div class=<em>"x-tool x-tool-{id}"</em>>&amp;#160;&lt;/div>'</em>,
        <em>'&lt;/tpl>'</em>
    ),
    width:500,
    height:300,
    closeAction:<em>'hide'</em>
});</code></pre>
<p>Note that the CSS class "x-tool-pdf" should have an associated style rule which provides an
appropriate background image, something like:</p>
    <pre><code>a.x-tool-pdf {background-image: url(../shared/extjs/images/pdf.gif)!important;}</code></pre></div></div></td><td class="msource"><a href="output/Ext.Panel.html#toolTemplate" ext:member="#toolTemplate" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-tools"></a><b><a href="source/Panel.html#cfg-Ext.Panel-tools">tools</a></b> : Array<div class="mdesc"><div class="short">An array of tool button configs to be added to the header tool area. When rendered, each tool is
stored as an Element...</div><div class="long">An array of tool button configs to be added to the header tool area. When rendered, each tool is
stored as an <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> referenced by a public property called <tt><b></b>tools.<i>&lt;tool-type&gt;</i></tt>
<p>Each tool config may contain the following properties:
<div class="mdetail-params"><ul>
<li><b>id</b> : String<div class="sub-desc"><b>Required.</b> The type
of tool to create. By default, this assigns a CSS class of the form <tt>x-tool-<i>&lt;tool-type&gt;</i></tt> to the
resulting tool Element. Ext provides CSS rules, and an icon sprite containing images for the tool types listed below.
The developer may implement custom tools by supplying alternate CSS rules and background images:
<ul>
<div class="x-tool x-tool-toggle" style="float:left; margin-right:5;"> </div><div><tt> toggle</tt> (Created by default when <a href="output/Ext.Panel.html#Ext.Panel-collapsible" ext:member="collapsible" ext:cls="Ext.Panel">collapsible</a> is <tt>true</tt>)</div>
<div class="x-tool x-tool-close" style="float:left; margin-right:5;"> </div><div><tt> close</tt></div>
<div class="x-tool x-tool-minimize" style="float:left; margin-right:5;"> </div><div><tt> minimize</tt></div>
<div class="x-tool x-tool-maximize" style="float:left; margin-right:5;"> </div><div><tt> maximize</tt></div>
<div class="x-tool x-tool-restore" style="float:left; margin-right:5;"> </div><div><tt> restore</tt></div>
<div class="x-tool x-tool-gear" style="float:left; margin-right:5;"> </div><div><tt> gear</tt></div>
<div class="x-tool x-tool-pin" style="float:left; margin-right:5;"> </div><div><tt> pin</tt></div>
<div class="x-tool x-tool-unpin" style="float:left; margin-right:5;"> </div><div><tt> unpin</tt></div>
<div class="x-tool x-tool-right" style="float:left; margin-right:5;"> </div><div><tt> right</tt></div>
<div class="x-tool x-tool-left" style="float:left; margin-right:5;"> </div><div><tt> left</tt></div>
<div class="x-tool x-tool-up" style="float:left; margin-right:5;"> </div><div><tt> up</tt></div>
<div class="x-tool x-tool-down" style="float:left; margin-right:5;"> </div><div><tt> down</tt></div>
<div class="x-tool x-tool-refresh" style="float:left; margin-right:5;"> </div><div><tt> refresh</tt></div>
<div class="x-tool x-tool-minus" style="float:left; margin-right:5;"> </div><div><tt> minus</tt></div>
<div class="x-tool x-tool-plus" style="float:left; margin-right:5;"> </div><div><tt> plus</tt></div>
<div class="x-tool x-tool-help" style="float:left; margin-right:5;"> </div><div><tt> help</tt></div>
<div class="x-tool x-tool-search" style="float:left; margin-right:5;"> </div><div><tt> search</tt></div>
<div class="x-tool x-tool-save" style="float:left; margin-right:5;"> </div><div><tt> save</tt></div>
<div class="x-tool x-tool-print" style="float:left; margin-right:5;"> </div><div><tt> print</tt></div>
</ul></div></li>
<li><b>handler</b> : Function<div class="sub-desc"><b>Required.</b> The function to
call when clicked. Arguments passed are:<ul>
<li><b>event</b> : Ext.EventObject<div class="sub-desc">The click event.</div></li>
<li><b>toolEl</b> : Ext.Element<div class="sub-desc">The tool Element.</div></li>
<li><b>panel</b> : Ext.Panel<div class="sub-desc">The host Panel</div></li>
<li><b>tc</b> : Ext.Panel<div class="sub-desc">The tool configuration object</div></li>
</ul></div></li>
<li><b>stopEvent</b> : Boolean<div class="sub-desc">Defaults to true. Specify as false to allow click event to propagate.</div></li>
<li><b>scope</b> : Object<div class="sub-desc">The scope in which to call the handler.</div></li>
<li><b>qtip</b> : String/Object<div class="sub-desc">A tip string, or
a config argument to <a href="output/Ext.QuickTip.html#Ext.QuickTip-register" ext:member="register" ext:cls="Ext.QuickTip">Ext.QuickTip.register</a></div></li>
<li><b>hidden</b> : Boolean<div class="sub-desc">True to initially render hidden.</div></li>
<li><b>on</b> : Object<div class="sub-desc">A listener config object specifiying
event listeners in the format of an argument to <a href="output/Ext.Panel.html#Ext.Panel-addListener" ext:member="addListener" ext:cls="Ext.Panel">addListener</a></div></li>
</ul></div>
<p>Note that, apart from the toggle tool which is provided when a panel is collapsible, these
tools only provide the visual button. Any required functionality must be provided by adding
handlers that implement the necessary behavior.</p>
<p>Example usage:</p>
<pre><code>tools:[{
    id:<em>'refresh'</em>,
    qtip: <em>'Refresh form Data'</em>,
    <i>// hidden:true,</i>
    handler: <b>function</b>(event, toolEl, panel){
        <i>// refresh logic</i>
    }
},
{
    id:<em>'help'</em>,
    qtip: <em>'Get Help'</em>,
    handler: <b>function</b>(event, toolEl, panel){
        <i>// whatever</i>
    }
}]</code></pre>
<p>For the custom id of <tt>'help'</tt> define two relevant css classes with a link to
a 15x15 image:</p>
<pre><code>.x-tool-help {background-image: url(images/help.png);}
.x-tool-help-over {background-image: url(images/help_over.png);}
<i>// <b>if</b> using an image sprite:</i>
.x-tool-help {background-image: url(images/help.png) no-repeat 0 0;}
.x-tool-help-over {background-position:-15px 0;}</code></pre></div></div></td><td class="msource"><a href="output/Ext.Panel.html#tools" ext:member="#tools" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-trackMouseOver"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-trackMouseOver">trackMouseOver</a></b> : Boolean<div class="mdesc">True to highlight rows when the mouse is over. Default is <tt>true</tt>
for GridPanel, but <tt>false</tt> for EditorGridPanel.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-unstyled"></a><b><a href="source/Panel.html#cfg-Ext.Panel-unstyled">unstyled</a></b> : Boolean<div class="mdesc"><div class="short">Overrides the baseCls setting to baseCls = 'x-plain' which renders
the panel unstyled except for required attributes ...</div><div class="long">Overrides the <tt><a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a></tt> setting to <tt><a href="output/Ext.Panel.html#Ext.Panel-baseCls" ext:member="baseCls" ext:cls="Ext.Panel">baseCls</a> = 'x-plain'</tt> which renders
the panel unstyled except for required attributes for Ext layouts to function (e.g. overflow:hidden).</div></div></td><td class="msource"><a href="output/Ext.Panel.html#unstyled" ext:member="#unstyled" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-view"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-view">view</a></b> : Object<div class="mdesc">The <a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">Ext.grid.GridView</a> used by the grid. This can be set
before a call to <a href="output/Ext.Component.html#Ext.Component-render" ext:member="render" ext:cls="Ext.Component">render()</a>.</div></td><td class="msource">GridPanel</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-viewConfig"></a><b><a href="source/GridPanel.html#cfg-Ext.grid.GridPanel-viewConfig">viewConfig</a></b> : Object<div class="mdesc"><div class="short">A config object that will be applied to the grid's UI view.  Any of&#13;
the config options available for Ext.grid.GridVi...</div><div class="long">A config object that will be applied to the grid's UI view.  Any of
the config options available for <a href="output/Ext.grid.GridView.html" ext:cls="Ext.grid.GridView">Ext.grid.GridView</a> can be specified here. This option
is ignored if <tt><a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-view" ext:member="view" ext:cls="Ext.grid.GridPanel">view</a></tt> is specified.</div></div></td><td class="msource">GridPanel</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-width"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-width">width</a></b> : Number<div class="mdesc">The width of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#width" ext:member="#width" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-x"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-x">x</a></b> : Number<div class="mdesc">The local x (left) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#x" ext:member="#x" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-xtype"></a><b><a href="source/Component.html#cfg-Ext.Component-xtype">xtype</a></b> : String<div class="mdesc"><div class="short">The registered xtype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>xtype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a child item of a Container is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>xtype</tt> will be looked up at render time up to determine what
type of child Component to create.<br><br>
The predefined xtypes are listed <a href="output/Ext.Component.html" ext:cls="Ext.Component">here</a>.
<br><br>
If you subclass Components to create your own Components, you may register
them using <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerType" ext:member="registerType" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerType</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#xtype" ext:member="#xtype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-y"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-y">y</a></b> : Number<div class="mdesc">The local y (top) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#y" ext:member="#y" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr></tbody></table><a id="Ext.grid.GridPanel-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-body"></a><b><a href="source/Panel.html#prop-Ext.Panel-body">body</a></b> : Ext.Element<div class="mdesc"><div class="short">The Panel's body Element which may be used to contain HTML content.
The content may be specified in the html config, ...</div><div class="long">The Panel's body <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> which may be used to contain HTML content.
The content may be specified in the <a href="output/Ext.Panel.html#Ext.Panel-html" ext:member="html" ext:cls="Ext.Panel">html</a> config, or it may be loaded using the
<a href="output/autoLoad.html" ext:cls="autoLoad">autoLoad</a> config, or through the Panel's <a href="output/Ext.Panel.html#Ext.Panel-getUpdater" ext:member="getUpdater" ext:cls="Ext.Panel">Updater</a>. Read-only.
<p>If this is used to load visible HTML elements in either way, then
the Panel may not be used as a Layout for hosting nested Panels.</p>
<p>If this Panel is intended to be used as the host of a Layout (See <a href="output/Ext.Panel.html#Ext.Panel-layout" ext:member="layout" ext:cls="Ext.Panel">layout</a>
then the body Element must not be loaded or changed - it is under the control
of the Panel's Layout.
<br><p><b>Note</b>: see the Note for <tt><a href="output/Ext.Component.html#Ext.Component-el" ext:member="el" ext:cls="Ext.Component">el</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#body" ext:member="#body" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-buttons"></a><b><a href="source/Panel.html#prop-Ext.Panel-buttons">buttons</a></b> : Array<div class="mdesc">This Panel's Array of buttons as created from the <tt><a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a></tt>
config property. Read only.</div></td><td class="msource"><a href="output/Ext.Panel.html#buttons" ext:member="#buttons" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bwrap"></a><b><a href="source/Panel.html#prop-Ext.Panel-bwrap">bwrap</a></b> : Ext.Element<div class="mdesc">The Panel's bwrap <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> used to contain other Panel elements
(tbar, body, bbar, footer). See <a href="output/Ext.Panel.html#Ext.Panel-bodyCfg" ext:member="bodyCfg" ext:cls="Ext.Panel">bodyCfg</a>. Read-only.</div></td><td class="msource"><a href="output/Ext.Panel.html#bwrap" ext:member="#bwrap" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-dd"></a><b><a href="source/Panel.html#prop-Ext.Panel-dd">dd</a></b> : Ext.dd.DragSource.<div class="mdesc"><div class="short">If this Panel is configured draggable, this property will contain
an instance of Ext.dd.DragSource which handles drag...</div><div class="long"><p>If this Panel is configured <a href="output/Ext.Panel.html#Ext.Panel-draggable" ext:member="draggable" ext:cls="Ext.Panel">draggable</a>, this property will contain
an instance of <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> which handles dragging the Panel.</p>
The developer must provide implementations of the abstract methods of <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a>
in order to supply behaviour for each stage of the drag/drop process. See <a href="output/Ext.Panel.html#Ext.Panel-draggable" ext:member="draggable" ext:cls="Ext.Panel">draggable</a>.</div></div></td><td class="msource"><a href="output/Ext.Panel.html#dd" ext:member="#dd" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-el"></a><b><a href="source/Component.html#prop-Ext.Component-el">el</a></b> : Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will
usually be a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will
<i>usually</i> be a &lt;DIV> element created by the class's onRender method, but
that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<br><p><b>Note</b>: this element will not be available until this Component has been
rendered.</b></p><br>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners
for this Component's own Observable events), perform the adding of the listener in a
render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        }
    }
});</code></pre>
<p>See also <tt><a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">getEl</a></p></div></div></td><td class="msource"><a href="output/Ext.Component.html#el" ext:member="#el" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-footer"></a><b><a href="source/Panel.html#prop-Ext.Panel-footer">footer</a></b> : Ext.Element<div class="mdesc"><div class="short">The Panel's footer Element. Read-only.
This Element is used to house the Panel's buttons or fbar.
Note: see the Note ...</div><div class="long">The Panel's footer <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a>. Read-only.
<p>This Element is used to house the Panel's <tt><a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a></tt> or <tt><a href="output/Ext.Panel.html#Ext.Panel-fbar" ext:member="fbar" ext:cls="Ext.Panel">fbar</a></tt>.</p>
<br><p><b>Note</b>: see the Note for <tt><a href="output/Ext.Component.html#Ext.Component-el" ext:member="el" ext:cls="Ext.Component">el</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#footer" ext:member="#footer" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-header"></a><b><a href="source/Panel.html#prop-Ext.Panel-header">header</a></b> : Ext.Element<div class="mdesc"><div class="short">The Panel's header Element. Read-only.
This Element is used to house the title and tools
Note: see the Note for el al...</div><div class="long">The Panel's header <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a>. Read-only.
<p>This Element is used to house the <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a> and <a href="output/Ext.Panel.html#Ext.Panel-tools" ext:member="tools" ext:cls="Ext.Panel">tools</a></p>
<br><p><b>Note</b>: see the Note for <tt><a href="output/Ext.Component.html#Ext.Component-el" ext:member="el" ext:cls="Ext.Component">el</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Panel.html#header" ext:member="#header" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#prop-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">True if this component is hidden. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-initialConfig"></a><b><a href="source/Component.html#prop-Ext.Component-initialConfig">initialConfig</a></b> : Object<div class="mdesc">This Component's initial configuration specification. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#initialConfig" ext:member="#initialConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ownerCt"></a><b><a href="source/Component.html#prop-Ext.Component-ownerCt">ownerCt</a></b> : Ext.Container<div class="mdesc"><div class="short">The component's owner Ext.Container (defaults to undefined, and is set automatically when
the component is added to a...</div><div class="long">The component's owner <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (defaults to undefined, and is set automatically when
the component is added to a container).  Read-only.
<p><b>Note</b>: to access items within the container see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ownerCt" ext:member="#ownerCt" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-rendered"></a><b><a href="source/Component.html#prop-Ext.Component-rendered">rendered</a></b> : Boolean<div class="mdesc">True if this component has been rendered. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#rendered" ext:member="#rendered" ext:cls="Ext.Component">Component</a></td></tr></tbody></table><a id="Ext.grid.GridPanel-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-GridPanel"></a><b><a href="source/GridPanel.html#cls-Ext.grid.GridPanel">GridPanel</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div><table><tr><td class="label">xtype:</td><td class="hd-info">grid</td></tr></table></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-addButton"></a><b><a href="source/Panel.html#method-Ext.Panel-addButton">addButton</a></b>(&nbsp;<code>String/Object&nbsp;config</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        Ext.Button<div class="mdesc"><div class="short">Adds a button to this panel.  Note that this method must be called prior to rendering.  The preferred
approach is to ...</div><div class="long">Adds a button to this panel.  Note that this method must be called prior to rendering.  The preferred
approach is to add buttons via the <a href="output/Ext.Panel.html#Ext.Panel-buttons" ext:member="buttons" ext:cls="Ext.Panel">buttons</a> config.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : String/Object<div class="sub-desc">A valid <a href="output/Ext.Button.html" ext:cls="Ext.Button">Ext.Button</a> config.  A string will become the text for a default
button config, an object will be treated as a button config object.</div></li><li><code>handler</code> : Function<div class="sub-desc">The function to be called on button <a href="output/Ext.Button.html#Ext.Button-click" ext:member="click" ext:cls="Ext.Button">Ext.Button.click</a></div></li><li><code>scope</code> : Object<div class="sub-desc">The scope to use for the button handler function</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Button</code><div class="sub-desc">The button that was added</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#addButton" ext:member="#addButton" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-addClass"></a><b><a href="source/Component.html#method-Ext.Component-addClass">addClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Adds a CSS class to the component's underlying element.</div><div class="long">Adds a CSS class to the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to add</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#addClass" ext:member="#addClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-bubble"></a><b><a href="source/Container.html#method-Ext.Container-bubble">bubble</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Bubbles up the component/container heirarchy, calling the specified function with each component. The scope (this) of...</div><div class="long">Bubbles up the component/container heirarchy, calling the specified function with each component. The scope (<i>this</i>) of
function call will be the scope provided or the current component. The arguments to the function
will be the args provided or the current component. If the function returns false at any point,
the bubble is stopped.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to current node)</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The args to call the function with (default to passing the current component)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#bubble" ext:member="#bubble" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-cloneConfig"></a><b><a href="source/Component.html#method-Ext.Component-cloneConfig">cloneConfig</a></b>(&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Clone the current component using the original config values passed into this instance by default.</div><div class="long">Clone the current component using the original config values passed into this instance by default.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>overrides</code> : Object<div class="sub-desc">A new config containing any properties to override in the cloned version.
An id property can be passed on this object, otherwise one will be generated to avoid duplicates.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">clone The cloned copy of this component</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#cloneConfig" ext:member="#cloneConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-collapse"></a><b><a href="source/Panel.html#method-Ext.Panel-collapse">collapse</a></b>(&nbsp;<code>Boolean&nbsp;animate</code>&nbsp;)
    :
                                        Ext.Panel<div class="mdesc"><div class="short">Collapses the panel body so that it becomes hidden.  Fires the beforecollapse event which will
cancel the collapse ac...</div><div class="long">Collapses the panel body so that it becomes hidden.  Fires the <a href="output/Ext.Panel.html#Ext.Panel-beforecollapse" ext:member="beforecollapse" ext:cls="Ext.Panel">beforecollapse</a> event which will
cancel the collapse action if it returns false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean<div class="sub-desc">True to animate the transition, else false (defaults to the value of the
<a href="output/Ext.Panel.html#Ext.Panel-animCollapse" ext:member="animCollapse" ext:cls="Ext.Panel">animCollapse</a> panel config)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Panel</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#collapse" ext:member="#collapse" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#method-Ext.Component-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the c...</div><div class="long">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the component from its <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (if applicable) and unregistering it from
<a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>.  Destruction is generally handled automatically by the framework and this method
should usually not need to be called directly.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-expand"></a><b><a href="source/Panel.html#method-Ext.Panel-expand">expand</a></b>(&nbsp;<code>Boolean&nbsp;animate</code>&nbsp;)
    :
                                        Ext.Panel<div class="mdesc"><div class="short">Expands the panel body so that it becomes visible.  Fires the beforeexpand event which will
cancel the expand action ...</div><div class="long">Expands the panel body so that it becomes visible.  Fires the <a href="output/Ext.Panel.html#Ext.Panel-beforeexpand" ext:member="beforeexpand" ext:cls="Ext.Panel">beforeexpand</a> event which will
cancel the expand action if it returns false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean<div class="sub-desc">True to animate the transition, else false (defaults to the value of the
<a href="output/Ext.Panel.html#Ext.Panel-animCollapse" ext:member="animCollapse" ext:cls="Ext.Panel">animCollapse</a> panel config)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Panel</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#expand" ext:member="#expand" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentBy"></a><b><a href="source/Component.html#method-Ext.Component-findParentBy">findParentBy</a></b>(&nbsp;<code>Function&nbsp;fcn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by a custom function. If the passed function returns
true, the con...</div><div class="long">Find a container above this component at any level by a custom function. If the passed function returns
true, the container will be returned. The passed function is called with the arguments (container, this component).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fcn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container for which the custom function returns true</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentBy" ext:member="#findParentBy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentByType"></a><b><a href="source/Component.html#method-Ext.Component-findParentByType">findParentByType</a></b>(&nbsp;<code>String/Class&nbsp;xtype</code>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by xtype or class</div><div class="long">Find a container above this component at any level by xtype or class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String/Class<div class="sub-desc">The xtype string for a component, or the class of the component directly</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container which matches the given xtype or class</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentByType" ext:member="#findParentByType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-focus"></a><b><a href="source/Component.html#method-Ext.Component-focus">focus</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;selectText</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Number&nbsp;delay</code>]</span>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Try to focus this component.</div><div class="long">Try to focus this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selectText</code> : Boolean<div class="sub-desc">(optional) If applicable, true to also select the text in this component</div></li><li><code>delay</code> : Boolean/Number<div class="sub-desc">(optional) Delay the focus this number of milliseconds (true for 10 milliseconds)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#focus" ext:member="#focus" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-get"></a><b><a href="source/Container.html#method-Ext.Container-get">get</a></b>(&nbsp;<code>String/Number&nbsp;key</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Get a component contained by this container (alias for items.get(key))</div><div class="long">Get a component contained by this container (alias for items.get(key))<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String/Number<div class="sub-desc">The index or id of the component</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">Ext.Component</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#get" ext:member="#get" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getBottomToolbar"></a><b><a href="source/Panel.html#method-Ext.Panel-getBottomToolbar">getBottomToolbar</a></b>()
    :
                                        Ext.Toolbar<div class="mdesc"><div class="short">Returns the toolbar from the bottom (bbar) section of the panel.</div><div class="long">Returns the <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">toolbar</a> from the bottom (<tt><a href="output/Ext.Panel.html#Ext.Panel-bbar" ext:member="bbar" ext:cls="Ext.Panel">bbar</a></tt>) section of the panel.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Toolbar</code><div class="sub-desc">The toolbar</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getBottomToolbar" ext:member="#getBottomToolbar" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getBox">getBox</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Gets the current box measurements of the component's underlying element.</div><div class="long">Gets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">box An object in the format {x, y, width, height}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getBox" ext:member="#getBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getBubbleTarget"></a><b><a href="source/Component.html#method-Ext.Component-getBubbleTarget">getBubbleTarget</a></b>()
    :
                                        Ext.Container<div class="mdesc"><div class="short">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.</div><div class="long">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">the Container which owns this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getBubbleTarget" ext:member="#getBubbleTarget" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-getColumnModel"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-getColumnModel">getColumnModel</a></b>()
    :
                                        Ext.grid.ColumnModel<div class="mdesc"><div class="short">Returns the grid's ColumnModel.</div><div class="long">Returns the grid's ColumnModel.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.grid.ColumnModel</code><div class="sub-desc">The column model</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-getDragDropText"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-getDragDropText">getDragDropText</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Called to get grid's drag proxy text, by default returns this.ddText.</div><div class="long">Called to get grid's drag proxy text, by default returns this.ddText.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The text</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getEl"></a><b><a href="source/Component.html#method-Ext.Component-getEl">getEl</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will usually be
a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will <i>usually</i> be
a &lt;DIV> element created by the class's onRender method, but that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<p><b>The Element will not be available until this Component has been rendered.</b></p>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners for this Component's
own Observable events), perform the adding of the listener in a one-off render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        },
        single: true  <i>// Remove the listener after first invocation</i>
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element which encapsulates this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getEl" ext:member="#getEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getFrameHeight"></a><b><a href="source/Panel.html#method-Ext.Panel-getFrameHeight">getFrameHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the height in pixels of the framing elements of this panel (including any top and bottom bars and
header and ...</div><div class="long">Returns the height in pixels of the framing elements of this panel (including any top and bottom bars and
header and footer elements, but not including the body height).  To retrieve the body height see <a href="output/Ext.Panel.html#Ext.Panel-getInnerHeight" ext:member="getInnerHeight" ext:cls="Ext.Panel">getInnerHeight</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The frame height</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getFrameHeight" ext:member="#getFrameHeight" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getFrameWidth"></a><b><a href="source/Panel.html#method-Ext.Panel-getFrameWidth">getFrameWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the width in pixels of the framing elements of this panel (not including the body width).  To
retrieve the bo...</div><div class="long">Returns the width in pixels of the framing elements of this panel (not including the body width).  To
retrieve the body width see <a href="output/Ext.Panel.html#Ext.Panel-getInnerWidth" ext:member="getInnerWidth" ext:cls="Ext.Panel">getInnerWidth</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The frame width</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getFrameWidth" ext:member="#getFrameWidth" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-getGridEl"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-getGridEl">getGridEl</a></b>()
    :
                                        Element<div class="mdesc"><div class="short">Returns the grid's underlying element.</div><div class="long">Returns the grid's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The element</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getHeight">getHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current height of the component's underlying element.</div><div class="long">Gets the current height of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getHeight" ext:member="#getHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getId"></a><b><a href="source/Component.html#method-Ext.Component-getId">getId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the id of this component or generates an id:"ext-comp-" + (++Ext.Component.AUTO_ID)</div><div class="long">Returns the id of this component or generates an id:<pre><code><em>"ext-comp-"</em> + (++Ext.Component.AUTO_ID)</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getId" ext:member="#getId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getInnerHeight"></a><b><a href="source/Panel.html#method-Ext.Panel-getInnerHeight">getInnerHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the height in pixels of the body element (not including the height of any framing elements).
For the frame he...</div><div class="long">Returns the height in pixels of the body element (not including the height of any framing elements).
For the frame height see <a href="output/Ext.Panel.html#Ext.Panel-getFrameHeight" ext:member="getFrameHeight" ext:cls="Ext.Panel">getFrameHeight</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The body height</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getInnerHeight" ext:member="#getInnerHeight" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getInnerWidth"></a><b><a href="source/Panel.html#method-Ext.Panel-getInnerWidth">getInnerWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the width in pixels of the body element (not including the width of any framing elements).
For the frame widt...</div><div class="long">Returns the width in pixels of the body element (not including the width of any framing elements).
For the frame width see <a href="output/Ext.Panel.html#Ext.Panel-getFrameWidth" ext:member="getFrameWidth" ext:cls="Ext.Panel">getFrameWidth</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The body width</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getInnerWidth" ext:member="#getInnerWidth" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getItemId"></a><b><a href="source/Component.html#method-Ext.Component-getItemId">getItemId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the item id of this component.</div><div class="long">Returns the item id of this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getItemId" ext:member="#getItemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-getLayoutTarget"></a><b><a href="source/Container.html#method-Ext.Container-getLayoutTarget">getLayoutTarget</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Element to be used to contain the child Components of this Container.
An implementation is provided which...</div><div class="long"><p>Returns the Element to be used to contain the child Components of this Container.</p>
<p>An implementation is provided which returns the Container's <a href="output/Ext.Container.html#Ext.Container-getEl" ext:member="getEl" ext:cls="Ext.Container">Element</a>, but
if there is a more complex structure to a Container, this may be overridden to return
the element into which the <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> renders child Components.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element to render child Components into.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#getLayoutTarget" ext:member="#getLayoutTarget" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getOuterSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getOuterSize">getOuterSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element, including space taken by its margins.</div><div class="long">Gets the current size of the component's underlying element, including space taken by its margins.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width + left/right margins), height: (element height + top/bottom margins)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getOuterSize" ext:member="#getOuterSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getPosition">getPosition</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Gets the current XY position of the component's underlying element.</div><div class="long">Gets the current XY position of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The XY position of the element (e.g., [100, 200])</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getPosition" ext:member="#getPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-getSelectionModel"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-getSelectionModel">getSelectionModel</a></b>()
    :
                                        SelectionModel<div class="mdesc"><div class="short">Returns the grid's SelectionModel.</div><div class="long">Returns the grid's SelectionModel.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>SelectionModel</code><div class="sub-desc">The selection model configured by the</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getSize">getSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element.</div><div class="long">Gets the current size of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width), height: (element height)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getSize" ext:member="#getSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-getStore"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-getStore">getStore</a></b>()
    :
                                        Ext.data.Store<div class="mdesc"><div class="short">Returns the grid's data store.</div><div class="long">Returns the grid's data store.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Store</code><div class="sub-desc">The store</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getTool"></a><b><a href="source/Panel.html#method-Ext.Panel-getTool">getTool</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Retrieve a tool by id.</div><div class="long">Retrieve a tool by id.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">tool</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getTool" ext:member="#getTool" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-getTopToolbar"></a><b><a href="source/Panel.html#method-Ext.Panel-getTopToolbar">getTopToolbar</a></b>()
    :
                                        Ext.Toolbar<div class="mdesc"><div class="short">Returns the toolbar from the top (tbar) section of the panel.</div><div class="long">Returns the <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">toolbar</a> from the top (<tt><a href="output/Ext.Panel.html#Ext.Panel-tbar" ext:member="tbar" ext:cls="Ext.Panel">tbar</a></tt>) section of the panel.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Toolbar</code><div class="sub-desc">The toolbar</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#getTopToolbar" ext:member="#getTopToolbar" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-getView"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-getView">getView</a></b>()
    :
                                        Ext.grid.GridView<div class="mdesc"><div class="short">Returns the grid's GridView object.</div><div class="long">Returns the grid's GridView object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.grid.GridView</code><div class="sub-desc">The grid view</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getWidth">getWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current width of the component's underlying element.</div><div class="long">Gets the current width of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getWidth" ext:member="#getWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXType"></a><b><a href="source/Component.html#method-Ext.Component-getXType">getXType</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Gets the xtype for this component as registered with Ext.ComponentMgr. For a list of all
available xtypes, see the Ex...</div><div class="long">Gets the xtype for this component as registered with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header. Example usage:
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXType());  <i>// alerts <em>'textfield'</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXType" ext:member="#getXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXTypes"></a><b><a href="source/Component.html#method-Ext.Component-getXTypes">getXTypes</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the Ext...</div><div class="long"><p>Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXTypes());  <i>// alerts <em>'component/box/field/textfield'</em></i>
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype hierarchy string</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXTypes" ext:member="#getXTypes" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hide"></a><b><a href="source/Component.html#method-Ext.Component-hide">hide</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Hide this component.</div><div class="long">Hide this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#hide" ext:member="#hide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isVisible"></a><b><a href="source/Component.html#method-Ext.Component-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this component is visible.</div><div class="long">Returns true if this component is visible.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component is visible, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isVisible" ext:member="#isVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isXType"></a><b><a href="source/Component.html#method-Ext.Component-isXType">isXType</a></b>(&nbsp;<code>String&nbsp;xtype</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;shallow</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from th...</div><div class="long"><p>Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from the xtype (default) or whether it is directly of the xtype specified (shallow = true).</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>For a list of all available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
<b>var</b> isText = t.isXType(<em>'textfield'</em>);        <i>// true</i>
<b>var</b> isBoxSubclass = t.isXType(<em>'box'</em>);       <i>// true, descended from BoxComponent</i>
<b>var</b> isBoxInstance = t.isXType(<em>'box'</em>, true); <i>// false, not a direct BoxComponent instance</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String<div class="sub-desc">The xtype to check for this Component</div></li><li><code>shallow</code> : Boolean<div class="sub-desc">(optional) False to check whether this Component is descended from the xtype (this is
the default), or true to check whether this Component is directly of the specified xtype.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component descends from the specified xtype, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isXType" ext:member="#isXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-nextSibling"></a><b><a href="source/Component.html#method-Ext.Component-nextSibling">nextSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the next component in the owning container</div><div class="long">Returns the next component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#nextSibling" ext:member="#nextSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-previousSibling"></a><b><a href="source/Component.html#method-Ext.Component-previousSibling">previousSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the previous component in the owning container</div><div class="long">Returns the previous component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#previousSibling" ext:member="#previousSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-reconfigure"></a><b><a href="source/GridPanel.html#method-Ext.grid.GridPanel-reconfigure">reconfigure</a></b>(&nbsp;<code>Ext.data.Store&nbsp;store</code>,&nbsp;<code>Ext.grid.ColumnModel&nbsp;colModel</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Reconfigures the grid to use a different Store and Column Model.&#13;
The View will be bound to the new objects and refre...</div><div class="long"><p>Reconfigures the grid to use a different Store and Column Model.
The View will be bound to the new objects and refreshed.</p>
<p>Be aware that upon reconfiguring a GridPanel, certain existing settings <i>may</i> become
invalidated. For example the configured <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-autoExpandColumn" ext:member="autoExpandColumn" ext:cls="Ext.grid.GridPanel">autoExpandColumn</a> may no longer exist in the
new ColumnModel. Also, an existing <a href="output/Ext.PagingToolbar.html" ext:cls="Ext.PagingToolbar">PagingToolbar</a> will still be bound
to the old Store, and will need rebinding. Any <a href="output/Ext.grid.GridPanel.html#Ext.grid.GridPanel-plugins" ext:member="plugins" ext:cls="Ext.grid.GridPanel">plugins</a> might also need reconfiguring
with the new data.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>store</code> : Ext.data.Store<div class="sub-desc">The new <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> object</div></li><li><code>colModel</code> : Ext.grid.ColumnModel<div class="sub-desc">The new <a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">Ext.grid.ColumnModel</a> object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-removeAll"></a><b><a href="source/Container.html#method-Ext.Container-removeAll">removeAll</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;autoDestroy</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Removes all components from this container.</div><div class="long">Removes all components from this container.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>autoDestroy</code> : Boolean<div class="sub-desc">(optional) True to automatically invoke the removed Component's <a href="output/Ext.Component.html#Ext.Component-destroy" ext:member="destroy" ext:cls="Ext.Component">Ext.Component.destroy</a> function.
Defaults to the value of this Container's <a href="output/Ext.Container.html#Ext.Container-autoDestroy" ext:member="autoDestroy" ext:cls="Ext.Container">autoDestroy</a> config.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">Array of the destroyed components</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#removeAll" ext:member="#removeAll" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-removeClass"></a><b><a href="source/Component.html#method-Ext.Component-removeClass">removeClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Removes a CSS class from the component's underlying element.</div><div class="long">Removes a CSS class from the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#removeClass" ext:member="#removeClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#method-Ext.Component-render">render</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Element/HTMLElement/String&nbsp;container</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String/Number&nbsp;position</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Render this Component into the passed HTML element.
If you are using a Container object to house this Component, then...</div><div class="long"><p>Render this Component into the passed HTML element.</p>
<p><b>If you are using a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a> object to house this Component, then
do not use the render method.</b></p>
<p>A Container's child Components are rendered by that Container's
<a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager when the Container is first rendered.</p>
<p>Certain layout managers allow dynamic addition of child components. Those that do
include <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>, <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>,
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>, <a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">Ext.layout.TableLayout</a>.</p>
<p>If the Container is already rendered when a new child Component is added, you may need to call
the Container's <a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a> to refresh the view which causes any
unrendered child Components to be rendered. This is required so that you can add multiple
child components if needed while only refreshing the layout once.</p>
<p>When creating complex UIs, it is important to remember that sizing and positioning
of child items is the responsibility of the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager.
If you expect child items to be sized in response to user interactions, you must
configure the Container with a layout manager which creates and manages the type of layout you
have in mind.</p>
<p><b>Omitting the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> config means that a basic
layout manager is used which does nothing but render child components sequentially into the
Container. No sizing or positioning will be performed in this situation.</b></p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>container</code> : Element/HTMLElement/String<div class="sub-desc">(optional) The element this Component should be
rendered into. If it is being created from existing markup, this should be omitted.</div></li><li><code>position</code> : String/Number<div class="sub-desc">(optional) The element ID or DOM node index within the container <b>before</b>
which this component will be inserted (defaults to appending to the end of the container)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setHeight">setHeight</a></b>(&nbsp;<code>Number&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the height of the component.  This method fires the resize event.</div><div class="long">Sets the height of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>height</code> : Number<div class="sub-desc">The new height to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style.</li>
<li><i>undefined</i> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setHeight" ext:member="#setHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-setIconClass"></a><b><a href="source/Panel.html#method-Ext.Panel-setIconClass">setIconClass</a></b>(&nbsp;<code>String&nbsp;cls</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the CSS class that provides the icon image for this panel.  This method will replace any existing
icon class if ...</div><div class="long">Sets the CSS class that provides the icon image for this panel.  This method will replace any existing
icon class if one has already been set and fire the <a href="output/Ext.Panel.html#Ext.Panel-iconchange" ext:member="iconchange" ext:cls="Ext.Panel">iconchange</a> event after completion.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : String<div class="sub-desc">The new CSS class name</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#setIconClass" ext:member="#setIconClass" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPagePosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPagePosition">setPagePosition</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the page XY position of the component.  To set the left and top instead, use setPosition.
This method fires the ...</div><div class="long">Sets the page XY position of the component.  To set the left and top instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPosition" ext:member="setPosition" ext:cls="Ext.BoxComponent">setPosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPagePosition" ext:member="#setPagePosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPosition">setPosition</a></b>(&nbsp;<code>Number&nbsp;left</code>,&nbsp;<code>Number&nbsp;top</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the left and top of the component.  To set the page XY position instead, use setPagePosition.
This method fires ...</div><div class="long">Sets the left and top of the component.  To set the page XY position instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPagePosition" ext:member="setPagePosition" ext:cls="Ext.BoxComponent">setPagePosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : Number<div class="sub-desc">The new left</div></li><li><code>top</code> : Number<div class="sub-desc">The new top</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPosition" ext:member="#setPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setSize">setSize</a></b>(&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<code>Mixed&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width and height of this BoxComponent. This method fires the resize event. This method can accept
either wid...</div><div class="long">Sets the width and height of this BoxComponent. This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event. This method can accept
either width and height as separate arguments, or you can pass a size object like <code>{width:10, height:20}</code>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Mixed<div class="sub-desc">The new width to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
<li>A size object in the format <code>{width: widthValue, height: heightValue}</code>.</li>
<li><code>undefined</code> to leave the width unchanged.</li>
</ul></div></div></li><li><code>height</code> : Mixed<div class="sub-desc">The new height to set (not required if a size object is passed as the first arg).
This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
<li><code>undefined</code> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setSize" ext:member="#setSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-setTitle"></a><b><a href="source/Panel.html#method-Ext.Panel-setTitle">setTitle</a></b>(&nbsp;<code>String&nbsp;title</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;iconCls</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the title text for the panel and optionally the icon class.
In order to be able to set the title, a header eleme...</div><div class="long"><p>Sets the title text for the panel and optionally the <a href="output/Ext.Panel.html#Ext.Panel-iconCls" ext:member="iconCls" ext:cls="Ext.Panel">icon class</a>.</p>
<p>In order to be able to set the title, a header element must have been created
for the Panel. This is triggered either by configuring the Panel with a non-blank <tt><a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a></tt>,
or configuring it with <tt><b><a href="output/Ext.Panel.html#Ext.Panel-header" ext:member="header" ext:cls="Ext.Panel">header</a>: true</b></tt>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>title</code> : String<div class="sub-desc">The title text to set</div></li><li><code>iconCls</code> : String<div class="sub-desc">(optional) <a href="output/Ext.Panel.html#Ext.Panel-iconCls" ext:member="iconCls" ext:cls="Ext.Panel">iconCls</a> A user-defined CSS class that provides the icon image for this panel</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#setTitle" ext:member="#setTitle" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-setVisible"></a><b><a href="source/Component.html#method-Ext.Component-setVisible">setVisible</a></b>(&nbsp;<code>Boolean&nbsp;visible</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Convenience function to hide or show this component by boolean.</div><div class="long">Convenience function to hide or show this component by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>visible</code> : Boolean<div class="sub-desc">True to show, false to hide</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#setVisible" ext:member="#setVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setWidth">setWidth</a></b>(&nbsp;<code>Number&nbsp;width</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width of the component.  This method fires the resize event.</div><div class="long">Sets the width of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Number<div class="sub-desc">The new width to setThis may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setWidth" ext:member="#setWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-show"></a><b><a href="source/Component.html#method-Ext.Component-show">show</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Show this component.</div><div class="long">Show this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#show" ext:member="#show" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-syncSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-syncSize">syncSize</a></b>()
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Force the component's size to recalculate based on the underlying element's current height and width.</div><div class="long">Force the component's size to recalculate based on the underlying element's current height and width.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#syncSize" ext:member="#syncSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-toggleCollapse"></a><b><a href="source/Panel.html#method-Ext.Panel-toggleCollapse">toggleCollapse</a></b>(&nbsp;<code>Boolean&nbsp;animate</code>&nbsp;)
    :
                                        Ext.Panel<div class="mdesc"><div class="short">Shortcut for performing an expand or collapse based on the current state of the panel.</div><div class="long">Shortcut for performing an <a href="output/Ext.Panel.html#Ext.Panel-expand" ext:member="expand" ext:cls="Ext.Panel">expand</a> or <a href="output/Ext.Panel.html#Ext.Panel-collapse" ext:member="collapse" ext:cls="Ext.Panel">collapse</a> based on the current state of the panel.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean<div class="sub-desc">True to animate the transition, else false (defaults to the value of the
<a href="output/Ext.Panel.html#Ext.Panel-animCollapse" ext:member="animCollapse" ext:cls="Ext.Panel">animCollapse</a> panel config)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Panel</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#toggleCollapse" ext:member="#toggleCollapse" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-updateBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-updateBox">updateBox</a></b>(&nbsp;<code>Object&nbsp;box</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the current box measurements of the component's underlying element.</div><div class="long">Sets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>box</code> : Object<div class="sub-desc">An object in the format {x, y, width, height}</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#updateBox" ext:member="#updateBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr></tbody></table><a id="Ext.grid.GridPanel-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-activate"></a><b><a href="source/Panel.html#event-Ext.Panel-activate">activate</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel has been visually activated.
Note that Panels do not directly support being activated, but some...</div><div class="long">Fires after the Panel has been visually activated.
Note that Panels do not directly support being activated, but some Panel subclasses
do (like <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a>). Panels which are child Components of a TabPanel fire the
activate and deactivate events under the control of the TabPanel.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">The Panel that has been activated.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#activate" ext:member="#activate" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Container-afterlayout"></a><b><a href="source/Container.html#event-Ext.Container-afterlayout">afterlayout</a></b> :
                                      (&nbsp;<code>Ext.Container&nbsp;this</code>,&nbsp;<code>ContainerLayout&nbsp;layout</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the components in this container are arranged by the associated layout manager.</div><div class="long">Fires when the components in this container are arranged by the associated layout manager.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Container<div class="sub-desc"></div></li><li><code>layout</code> : ContainerLayout<div class="sub-desc">The ContainerLayout implementation for this container</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Container.html#afterlayout" ext:member="#afterlayout" ext:cls="Ext.Container">Container</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-afterrender"></a><b><a href="source/Component.html#event-Ext.Component-afterrender">afterrender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component rendering is finished.</div><div class="long">Fires after the component rendering is finished.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#afterrender" ext:member="#afterrender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-beforeclose"></a><b><a href="source/Panel.html#event-Ext.Panel-beforeclose">beforeclose</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the Panel is closed.  Note that Panels do not directly support being closed, but some
Panel subclasses d...</div><div class="long">Fires before the Panel is closed.  Note that Panels do not directly support being closed, but some
Panel subclasses do (like <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a>) or a Panel within a Ext.TabPanel.  This event only
applies to such subclasses.
A handler can return false to cancel the close.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">The Panel being closed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#beforeclose" ext:member="#beforeclose" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-beforecollapse"></a><b><a href="source/Panel.html#event-Ext.Panel-beforecollapse">beforecollapse</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>,&nbsp;<code>Boolean&nbsp;animate</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the Panel is collapsed.  A handler can return false to cancel the collapse.</div><div class="long">Fires before the Panel is collapsed.  A handler can return false to cancel the collapse.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">the Panel being collapsed.</div></li><li><code>animate</code> : Boolean<div class="sub-desc">True if the collapse is animated, else false.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#beforecollapse" ext:member="#beforecollapse" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforedestroy"></a><b><a href="source/Component.html#event-Ext.Component-beforedestroy">beforedestroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is destroyed. Return false to stop the destroy.</div><div class="long">Fires before the component is destroyed. Return false to stop the destroy.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforedestroy" ext:member="#beforedestroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-beforeexpand"></a><b><a href="source/Panel.html#event-Ext.Panel-beforeexpand">beforeexpand</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>,&nbsp;<code>Boolean&nbsp;animate</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the Panel is expanded.  A handler can return false to cancel the expand.</div><div class="long">Fires before the Panel is expanded.  A handler can return false to cancel the expand.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">The Panel being expanded.</div></li><li><code>animate</code> : Boolean<div class="sub-desc">True if the expand is animated, else false.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#beforeexpand" ext:member="#beforeexpand" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforehide"></a><b><a href="source/Component.html#event-Ext.Component-beforehide">beforehide</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is hidden. Return false to stop the hide.</div><div class="long">Fires before the component is hidden. Return false to stop the hide.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforehide" ext:member="#beforehide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforerender"></a><b><a href="source/Component.html#event-Ext.Component-beforerender">beforerender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is rendered. Return false to stop the render.</div><div class="long">Fires before the component is rendered. Return false to stop the render.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforerender" ext:member="#beforerender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforeshow"></a><b><a href="source/Component.html#event-Ext.Component-beforeshow">beforeshow</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is shown. Return false to stop the show.</div><div class="long">Fires before the component is shown. Return false to stop the show.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforeshow" ext:member="#beforeshow" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestaterestore"></a><b><a href="source/Component.html#event-Ext.Component-beforestaterestore">beforestaterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is restored. Return false to stop the restore.</div><div class="long">Fires before the state of the component is restored. Return false to stop the restore.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. If this
event is not vetoed, then the state object is passed to <b><tt>applyState</tt></b>. By default,
that simply copies property values into this Component. The method maybe overriden to
provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestaterestore" ext:member="#beforestaterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestatesave"></a><b><a href="source/Component.html#event-Ext.Component-beforestatesave">beforestatesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.</div><div class="long">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestatesave" ext:member="#beforestatesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-bodyresize"></a><b><a href="source/Panel.html#event-Ext.Panel-bodyresize">bodyresize</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>,&nbsp;<code>Number&nbsp;width</code>,&nbsp;<code>Number&nbsp;height</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel has been resized.</div><div class="long">Fires after the Panel has been resized.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">the Panel which has been resized.</div></li><li><code>width</code> : Number<div class="sub-desc">The Panel's new width.</div></li><li><code>height</code> : Number<div class="sub-desc">The Panel's new height.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#bodyresize" ext:member="#bodyresize" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-bodyscroll"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-bodyscroll">bodyscroll</a></b> :
                                      (&nbsp;<code>Number&nbsp;scrollLeft</code>,&nbsp;<code>Number&nbsp;scrollTop</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the body element is scrolled</div><div class="long">Fires when the body element is scrolled<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>scrollLeft</code> : Number<div class="sub-desc"></div></li><li><code>scrollTop</code> : Number<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-cellclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-cellclick">cellclick</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a cell is clicked.&#13;
The data for the cell is drawn from the Record&#13;
for this row. To access the data in th...</div><div class="long">Fires when a cell is clicked.
The data for the cell is drawn from the <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Record</a>
for this row. To access the data in the listener function use the
following technique:
<pre><code><b>function</b>(grid, rowIndex, columnIndex, e) {
    <b>var</b> record = grid.getStore().getAt(rowIndex);  <i>// Get the Record
</i>
    <b>var</b> fieldName = grid.getColumnModel().getDataIndex(columnIndex); <i>// Get field name
</i>
    <b>var</b> data = record.get(fieldName);
}</code></pre><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-cellcontextmenu"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-cellcontextmenu">cellcontextmenu</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Number&nbsp;cellIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a cell is right clicked</div><div class="long">Fires when a cell is right clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>cellIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-celldblclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-celldblclick">celldblclick</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a cell is double clicked</div><div class="long">Fires when a cell is double clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-cellmousedown"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-cellmousedown">cellmousedown</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a cell is clicked</div><div class="long">Fires before a cell is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-click"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-click">click</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw click event for the entire grid.</div><div class="long">The raw click event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-close"></a><b><a href="source/Panel.html#event-Ext.Panel-close">close</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel is closed.  Note that Panels do not directly support being closed, but some
Panel subclasses do...</div><div class="long">Fires after the Panel is closed.  Note that Panels do not directly support being closed, but some
Panel subclasses do (like <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a>) or a Panel within a Ext.TabPanel.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">The Panel that has been closed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#close" ext:member="#close" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-collapse"></a><b><a href="source/Panel.html#event-Ext.Panel-collapse">collapse</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel has been collapsed.</div><div class="long">Fires after the Panel has been collapsed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">the Panel that has been collapsed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#collapse" ext:member="#collapse" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-columnmove"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-columnmove">columnmove</a></b> :
                                      (&nbsp;<code>Number&nbsp;oldIndex</code>,&nbsp;<code>Number&nbsp;newIndex</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the user moves a column</div><div class="long">Fires when the user moves a column<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>oldIndex</code> : Number<div class="sub-desc"></div></li><li><code>newIndex</code> : Number<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-columnresize"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-columnresize">columnresize</a></b> :
                                      (&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Number&nbsp;newSize</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the user resizes a column</div><div class="long">Fires when the user resizes a column<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>newSize</code> : Number<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-contextmenu"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-contextmenu">contextmenu</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw contextmenu event for the entire grid.</div><div class="long">The raw contextmenu event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-dblclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-dblclick">dblclick</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw dblclick event for the entire grid.</div><div class="long">The raw dblclick event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-deactivate"></a><b><a href="source/Panel.html#event-Ext.Panel-deactivate">deactivate</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel has been visually deactivated.
Note that Panels do not directly support being deactivated, but ...</div><div class="long">Fires after the Panel has been visually deactivated.
Note that Panels do not directly support being deactivated, but some Panel subclasses
do (like <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a>). Panels which are child Components of a TabPanel fire the
activate and deactivate events under the control of the TabPanel.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">The Panel that has been deactivated.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#deactivate" ext:member="#deactivate" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#event-Ext.Component-destroy">destroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is destroyed.</div><div class="long">Fires after the component is destroyed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disable"></a><b><a href="source/Component.html#event-Ext.Component-disable">disable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is disabled.</div><div class="long">Fires after the component is disabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#disable" ext:member="#disable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-enable"></a><b><a href="source/Component.html#event-Ext.Component-enable">enable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is enabled.</div><div class="long">Fires after the component is enabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#enable" ext:member="#enable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-expand"></a><b><a href="source/Panel.html#event-Ext.Panel-expand">expand</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel has been expanded.</div><div class="long">Fires after the Panel has been expanded.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">The Panel that has been expanded.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#expand" ext:member="#expand" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-headerclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-headerclick">headerclick</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a header is clicked</div><div class="long">Fires when a header is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-headercontextmenu"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-headercontextmenu">headercontextmenu</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a header is right clicked</div><div class="long">Fires when a header is right clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-headerdblclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-headerdblclick">headerdblclick</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a header cell is double clicked</div><div class="long">Fires when a header cell is double clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-headermousedown"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-headermousedown">headermousedown</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;columnIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a header is clicked</div><div class="long">Fires before a header is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>columnIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hide"></a><b><a href="source/Component.html#event-Ext.Component-hide">hide</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is hidden.</div><div class="long">Fires after the component is hidden.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#hide" ext:member="#hide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-iconchange"></a><b><a href="source/Panel.html#event-Ext.Panel-iconchange">iconchange</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>,&nbsp;<code>String&nbsp;The</code>,&nbsp;<code>String&nbsp;The</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel icon class has been set or changed.</div><div class="long">Fires after the Panel icon class has been <a href="output/Ext.Panel.html#Ext.Panel-iconCls" ext:member="iconCls" ext:cls="Ext.Panel">set</a> or <a href="output/Ext.Panel.html#Ext.Panel-setIconClass" ext:member="setIconClass" ext:cls="Ext.Panel">changed</a>.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">the Panel which has had its <a href="output/Ext.Panel.html#Ext.Panel-iconCls" ext:member="iconCls" ext:cls="Ext.Panel">icon class</a> changed.</div></li><li><code>The</code> : String<div class="sub-desc">new icon class.</div></li><li><code>The</code> : String<div class="sub-desc">old icon class.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#iconchange" ext:member="#iconchange" ext:cls="Ext.Panel">Panel</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-keydown"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-keydown">keydown</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw keydown event for the entire grid.</div><div class="long">The raw keydown event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-keypress"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-keypress">keypress</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw keypress event for the entire grid.</div><div class="long">The raw keypress event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-mousedown"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-mousedown">mousedown</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw mousedown event for the entire grid.</div><div class="long">The raw mousedown event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-mouseout"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-mouseout">mouseout</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw mouseout event for the entire grid.</div><div class="long">The raw mouseout event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-mouseover"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-mouseover">mouseover</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw mouseover event for the entire grid.</div><div class="long">The raw mouseover event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-mouseup"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-mouseup">mouseup</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">The raw mouseup event for the entire grid.</div><div class="long">The raw mouseup event for the entire grid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-move"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-move">move</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is moved.</div><div class="long">Fires after the component is moved.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#move" ext:member="#move" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#event-Ext.Component-render">render</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component markup is rendered.</div><div class="long">Fires after the component markup is rendered.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-resize"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-resize">resize</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;adjWidth</code>,&nbsp;<code>Number&nbsp;adjHeight</code>,&nbsp;<code>Number&nbsp;rawWidth</code>,&nbsp;<code>Number&nbsp;rawHeight</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is resized.</div><div class="long">Fires after the component is resized.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>adjWidth</code> : Number<div class="sub-desc">The box-adjusted width that was set</div></li><li><code>adjHeight</code> : Number<div class="sub-desc">The box-adjusted height that was set</div></li><li><code>rawWidth</code> : Number<div class="sub-desc">The width that was originally specified</div></li><li><code>rawHeight</code> : Number<div class="sub-desc">The height that was originally specified</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#resize" ext:member="#resize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-rowclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-rowclick">rowclick</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a row is clicked</div><div class="long">Fires when a row is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-rowcontextmenu"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-rowcontextmenu">rowcontextmenu</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a row is right clicked</div><div class="long">Fires when a row is right clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-rowdblclick"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-rowdblclick">rowdblclick</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a row is double clicked</div><div class="long">Fires when a row is double clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-rowmousedown"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-rowmousedown">rowmousedown</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Number&nbsp;rowIndex</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a row is clicked</div><div class="long">Fires before a row is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>rowIndex</code> : Number<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-show"></a><b><a href="source/Component.html#event-Ext.Component-show">show</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is shown.</div><div class="long">Fires after the component is shown.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#show" ext:member="#show" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.GridPanel-sortchange"></a><b><a href="source/GridPanel.html#event-Ext.grid.GridPanel-sortchange">sortchange</a></b> :
                                      (&nbsp;<code>Grid&nbsp;this</code>,&nbsp;<code>Object&nbsp;sortInfo</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the grid's store sort changes</div><div class="long">Fires when the grid's store sort changes<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Grid<div class="sub-desc"></div></li><li><code>sortInfo</code> : Object<div class="sub-desc">An object with the keys field and direction</div></li></ul></div></div></div></td><td class="msource">GridPanel</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-staterestore"></a><b><a href="source/Component.html#event-Ext.Component-staterestore">staterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is restored.</div><div class="long">Fires after the state of the component is restored.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. This is passed
to <b><tt>applyState</tt></b>. By default, that simply copies property values into this
Component. The method maybe overriden to provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#staterestore" ext:member="#staterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-statesave"></a><b><a href="source/Component.html#event-Ext.Component-statesave">statesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is saved to the configured state provider.</div><div class="long">Fires after the state of the component is saved to the configured state provider.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#statesave" ext:member="#statesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Panel-titlechange"></a><b><a href="source/Panel.html#event-Ext.Panel-titlechange">titlechange</a></b> :
                                      (&nbsp;<code>Ext.Panel&nbsp;p</code>,&nbsp;<code>String&nbsp;The</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the Panel title has been set or changed.</div><div class="long">Fires after the Panel title has been <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">set</a> or <a href="output/Ext.Panel.html#Ext.Panel-setTitle" ext:member="setTitle" ext:cls="Ext.Panel">changed</a>.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>p</code> : Ext.Panel<div class="sub-desc">the Panel which has had its title changed.</div></li><li><code>The</code> : String<div class="sub-desc">new title.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Panel.html#titlechange" ext:member="#titlechange" ext:cls="Ext.Panel">Panel</a></td></tr></tbody></table></div>