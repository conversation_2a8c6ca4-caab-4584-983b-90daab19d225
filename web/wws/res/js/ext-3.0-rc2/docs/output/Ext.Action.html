<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Action-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Action-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Action-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.Action-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Action"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Action.html#cls-Ext.Action">Ext.Action</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Action.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Action.html#cls-Ext.Action">Action</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>An Action is a piece of reusable functionality that can be abstracted out of any particular component so that it
can be usefully shared among multiple components.  Actions let you share handlers, configuration options and UI
updates across any components that support the Action interface (primarily <a href="output/Ext.Toolbar.html" ext:cls="Ext.Toolbar">Ext.Toolbar</a>, <a href="output/Ext.Button.html" ext:cls="Ext.Button">Ext.Button</a>
and <a href="output/Ext.menu.Menu.html" ext:cls="Ext.menu.Menu">Ext.menu.Menu</a> components).</p>
<p>Aside from supporting the config object interface, any component that needs to use Actions must also support
the following method list, as these will be called as needed by the Action class: setText(string), setIconCls(string),
setDisabled(boolean), setVisible(boolean) and setHandler(function).</p>
Example usage:<br>
<pre><code><i>// Define the shared action.  Each component below will have the same
</i>
<i>// display text and icon, and will display the same message on click.
</i>
<b>var</b> action = <b>new</b> Ext.Action({
    <a href="output/Ext.Action.html#Ext.Action-text" ext:member="text" ext:cls="Ext.Action">text</a>: <em>'Do something'</em>,
    <a href="output/Ext.Action.html#Ext.Action-handler" ext:member="handler" ext:cls="Ext.Action">handler</a>: <b>function</b>(){
        Ext.Msg.alert(<em>'Click'</em>, <em>'You did something.'</em>);
    },
    <a href="output/Ext.Action.html#Ext.Action-iconCls" ext:member="iconCls" ext:cls="Ext.Action">iconCls</a>: <em>'<b>do</b>-something'</em>,
    <a href="output/Ext.Action.html#Ext.Action-itemId" ext:member="itemId" ext:cls="Ext.Action">itemId</a>: <em>'myAction'</em>
});

<b>var</b> panel = <b>new</b> Ext.Panel({
    title: <em>'Actions'</em>,
    width: 500,
    height: 300,
    tbar: [
        <i>// Add the action directly to a toolbar as a menu button
</i>
        action,
        {
            text: <em>'Action Menu'</em>,
            <i>// Add the action to a menu as a text item
</i>
            menu: [action]
        }
    ],
    items: [
        <i>// Add the action to the panel body as a standard button
</i>
        <b>new</b> Ext.Button(action)
    ],
    renderTo: Ext.getBody()
});

<i>// Change the text <b>for</b> all components using the action
</i>
action.setText(<em>'Something <b>else</b>'</em>);

<i>// Reference an action through a container using the itemId
</i>
<b>var</b> btn = panel.getComponent(<em>'myAction'</em>);
<b>var</b> aRef = btn.baseAction;
aRef.setText(<em>'New text'</em>);</code></pre></div><div class="hr"></div><a id="Ext.Action-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-disabled"></a><b><a href="source/Action.html#cfg-Ext.Action-disabled">disabled</a></b> : Boolean<div class="mdesc">True to disable all components using this action, false to enable them (defaults to false).</div></td><td class="msource">Action</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-handler"></a><b><a href="source/Action.html#cfg-Ext.Action-handler">handler</a></b> : Function<div class="mdesc"><div class="short">The function that will be invoked by each component tied to this action&#13;
when the component's primary event is trigge...</div><div class="long">The function that will be invoked by each component tied to this action
when the component's primary event is triggered (defaults to undefined).</div></div></td><td class="msource">Action</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-hidden"></a><b><a href="source/Action.html#cfg-Ext.Action-hidden">hidden</a></b> : Boolean<div class="mdesc">True to hide all components using this action, false to show them (defaults to false).</div></td><td class="msource">Action</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-iconCls"></a><b><a href="source/Action.html#cfg-Ext.Action-iconCls">iconCls</a></b> : String<div class="mdesc"><div class="short">The CSS class selector that specifies a background image to be used as the header icon for&#13;
all components using this...</div><div class="long">The CSS class selector that specifies a background image to be used as the header icon for
all components using this action (defaults to '').
<p>An example of specifying a custom icon class would be something like:
</p><code><pre>
<i>// specify the property <b>in</b> the config <b>for</b> the class:
</i>
     ...
     iconCls: <em>'<b>do</b>-something'</em>

<i>// css class that specifies background image to be used as the icon image:
</i>
.<b>do</b>-something { background-image: url(../images/my-icon.gif) 0 6px no-repeat !important; }
</pre></code></div></div></td><td class="msource">Action</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-itemId"></a><b><a href="source/Action.html#cfg-Ext.Action-itemId">itemId</a></b> : String<div class="mdesc">See <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a>.</div></td><td class="msource">Action</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-scope"></a><b><a href="source/Action.html#cfg-Ext.Action-scope">scope</a></b> : Object<div class="mdesc">The scope in which the <a href="output/Ext.Action.html#Ext.Action-handler" ext:member="handler" ext:cls="Ext.Action">handler</a> function will execute.</div></td><td class="msource">Action</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-text"></a><b><a href="source/Action.html#cfg-Ext.Action-text">text</a></b> : String<div class="mdesc">The text to set for all components using this action (defaults to '').</div></td><td class="msource">Action</td></tr></tbody></table><a id="Ext.Action-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-isDisabled"></a><b><a href="source/Action.html#prop-Ext.Action-isDisabled">isDisabled</a></b> : Object<div class="mdesc">Returns true if the components using this action are currently disabled, else returns false.  Read-only.</div></td><td class="msource">Action</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-isHidden"></a><b><a href="source/Action.html#prop-Ext.Action-isHidden">isHidden</a></b> : Object<div class="mdesc">Returns true if the components using this action are currently hidden, else returns false.  Read-only.</div></td><td class="msource">Action</td></tr></tbody></table><a id="Ext.Action-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-Action"></a><b><a href="source/Action.html#cls-Ext.Action">Action</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The configuration options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-disable"></a><b><a href="source/Action.html#method-Ext.Action-disable">disable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Disables all components using this action.</div><div class="long">Disables all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-each"></a><b><a href="source/Action.html#method-Ext.Action-each">each</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes the specified function once for each component currently tied to this action.  The function passed&#13;
in shoul...</div><div class="long">Executes the specified function once for each component currently tied to this action.  The function passed
in should accept a single argument that will be an object that supports the basic Action config/method interface.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to execute for each component</div></li><li><code>scope</code> : Object<div class="sub-desc">The scope in which the function will execute</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-enable"></a><b><a href="source/Action.html#method-Ext.Action-enable">enable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Enables all components using this action.</div><div class="long">Enables all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-execute"></a><b><a href="source/Action.html#method-Ext.Action-execute">execute</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;arg1</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;arg2</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;etc...</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes this action manually using the handler function specified in the original config object&#13;
or the handler func...</div><div class="long">Executes this action manually using the handler function specified in the original config object
or the handler function set with <code><a href="output/Ext.Action.html#Ext.Action-setHandler" ext:member="setHandler" ext:cls="Ext.Action">setHandler</a></code>.  Any arguments passed to this
function will be passed on to the handler function.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arg1</code> : Mixed<div class="sub-desc">(optional) Variable number of arguments passed to the handler function</div></li><li><code>arg2</code> : Mixed<div class="sub-desc">(optional)</div></li><li><code>etc...</code> : Mixed<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-getIconClass"></a><b><a href="source/Action.html#method-Ext.Action-getIconClass">getIconClass</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Gets the icon CSS class currently used by all components using this action.</div><div class="long">Gets the icon CSS class currently used by all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-getText"></a><b><a href="source/Action.html#method-Ext.Action-getText">getText</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Gets the text currently displayed by all components using this action.</div><div class="long">Gets the text currently displayed by all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-hide"></a><b><a href="source/Action.html#method-Ext.Action-hide">hide</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides all components using this action.</div><div class="long">Hides all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-setDisabled"></a><b><a href="source/Action.html#method-Ext.Action-setDisabled">setDisabled</a></b>(&nbsp;<code>Boolean&nbsp;disabled</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the disabled state of all components using this action.  Shortcut method&#13;
for enable and disable.</div><div class="long">Sets the disabled state of all components using this action.  Shortcut method
for <a href="output/Ext.Action.html#Ext.Action-enable" ext:member="enable" ext:cls="Ext.Action">enable</a> and <a href="output/Ext.Action.html#Ext.Action-disable" ext:member="disable" ext:cls="Ext.Action">disable</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>disabled</code> : Boolean<div class="sub-desc">True to disable the component, false to enable it</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-setHandler"></a><b><a href="source/Action.html#method-Ext.Action-setHandler">setHandler</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the function that will be called by each component using this action when its primary event is triggered.</div><div class="long">Sets the function that will be called by each component using this action when its primary event is triggered.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function that will be invoked by the action's components.  The function
will be called with no arguments.</div></li><li><code>scope</code> : Object<div class="sub-desc">The scope in which the function will execute</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-setHidden"></a><b><a href="source/Action.html#method-Ext.Action-setHidden">setHidden</a></b>(&nbsp;<code>Boolean&nbsp;hidden</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the hidden state of all components using this action.  Shortcut method&#13;
for hide and show.</div><div class="long">Sets the hidden state of all components using this action.  Shortcut method
for <code><a href="output/Ext.Action.html#Ext.Action-hide" ext:member="hide" ext:cls="Ext.Action">hide</a></code> and <code><a href="output/Ext.Action.html#Ext.Action-show" ext:member="show" ext:cls="Ext.Action">show</a></code>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>hidden</code> : Boolean<div class="sub-desc">True to hide the component, false to show it</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-setIconClass"></a><b><a href="source/Action.html#method-Ext.Action-setIconClass">setIconClass</a></b>(&nbsp;<code>String&nbsp;cls</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the icon CSS class for all components using this action.  The class should supply&#13;
a background image that will ...</div><div class="long">Sets the icon CSS class for all components using this action.  The class should supply
a background image that will be used as the icon image.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : String<div class="sub-desc">The CSS class supplying the icon image</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-setText"></a><b><a href="source/Action.html#method-Ext.Action-setText">setText</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the text to be displayed by all components using this action.</div><div class="long">Sets the text to be displayed by all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">The text to display</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Action-show"></a><b><a href="source/Action.html#method-Ext.Action-show">show</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Shows all components using this action.</div><div class="long">Shows all components using this action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Action</td></tr></tbody></table><a id="Ext.Action-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>