<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#String-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#String-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#String-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=String"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Ext-more.html#cls-String">String</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Global</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Ext-more.js,&#13;Ext.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Ext-more.html#cls-String">String</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">These functions are available as static methods on the JavaScript String object.</div><div class="hr"></div><a id="String-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="String-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="String-escape"></a><b><a href="source/Ext-more.html#method-String-escape">escape</a></b>(&nbsp;<code>String&nbsp;string</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Escapes the passed string for ' and \</div><div class="long">&lt;static&gt;&nbsp;Escapes the passed string for ' and \<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>string</code> : String<div class="sub-desc">The string to escape</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The escaped string</div></li></ul></div></div></div></td><td class="msource">String</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="String-format"></a><b><a href="source/Ext.html#method-String-format">format</a></b>(&nbsp;<code>String&nbsp;string</code>,&nbsp;<code>String&nbsp;value1</code>,&nbsp;<code>String&nbsp;value2</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Allows you to define a tokenized string and pass an arbitrary number of arguments to replace the tokens.  Each
token ...</div><div class="long">&lt;static&gt;&nbsp;Allows you to define a tokenized string and pass an arbitrary number of arguments to replace the tokens.  Each
token must be unique, and must increment in the format {0}, {1}, etc.  Example usage:
<pre><code><b>var</b> cls = <em>'my-class'</em>, text = <em>'Some text'</em>;
<b>var</b> s = String.format(<em>'&lt;div class=<em>"{0}"</em>>{1}&lt;/div>'</em>, cls, text);
<i>// s now contains the string: <em>'&lt;div class=<em>"my-class"</em>>Some text&lt;/div>'</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>string</code> : String<div class="sub-desc">The tokenized string to be formatted</div></li><li><code>value1</code> : String<div class="sub-desc">The value to replace token {0}</div></li><li><code>value2</code> : String<div class="sub-desc">Etc...</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The formatted string</div></li></ul></div></div></div></td><td class="msource">String</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="String-leftPad"></a><b><a href="source/Ext-more.html#method-String-leftPad">leftPad</a></b>(&nbsp;<code>String&nbsp;string</code>,&nbsp;<code>Number&nbsp;size</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;char</code>]</span>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Pads the left side of a string with a specified character.  This is especially useful
for normalizing number and date...</div><div class="long">&lt;static&gt;&nbsp;Pads the left side of a string with a specified character.  This is especially useful
for normalizing number and date strings.  Example usage:
<pre><code><b>var</b> s = String.leftPad(<em>'123'</em>, 5, <em>'0'</em>);
<i>// s now contains the string: <em>'00123'</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>string</code> : String<div class="sub-desc">The original string</div></li><li><code>size</code> : Number<div class="sub-desc">The total length of the output string</div></li><li><code>char</code> : String<div class="sub-desc">(optional) The character with which to pad the original string (defaults to empty string " ")</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The padded string</div></li></ul></div></div></div></td><td class="msource">String</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="String-toggle"></a><b><a href="source/Ext-more.html#method-String-toggle">toggle</a></b>(&nbsp;<code>String&nbsp;value</code>,&nbsp;<code>String&nbsp;other</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Utility function that allows you to easily switch a string between two alternating values.  The passed value
is compa...</div><div class="long">Utility function that allows you to easily switch a string between two alternating values.  The passed value
is compared to the current string, and if they are equal, the other value that was passed in is returned.  If
they are already different, the first value passed in is returned.  Note that this method returns the new value
but does not change the current string.
<pre><code><i>// alternate sort directions</i>
sort = sort.toggle(<em>'ASC'</em>, <em>'DESC'</em>);

<i>// instead of conditional logic:</i>
sort = (sort == <em>'ASC'</em> ? <em>'DESC'</em> : <em>'ASC'</em>);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The value to compare to the current string</div></li><li><code>other</code> : String<div class="sub-desc">The new value to use if the string already equals the first value passed in</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The new value</div></li></ul></div></div></div></td><td class="msource">String</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="String-trim"></a><b><a href="source/Ext-more.html#method-String-trim">trim</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Trims whitespace from either end of a string, leaving spaces within the string intact.  Example:
var s = '  foo bar  ...</div><div class="long">Trims whitespace from either end of a string, leaving spaces within the string intact.  Example:
<pre><code><b>var</b> s = <em>'  foo bar  '</em>;
alert(<em>'-'</em> + s + <em>'-'</em>);         <i>//alerts <em>"- foo bar -"</em></i>
alert(<em>'-'</em> + s.trim() + <em>'-'</em>);  <i>//alerts <em>"-foo bar-"</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The trimmed string</div></li></ul></div></div></div></td><td class="msource">String</td></tr></tbody></table><a id="String-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>