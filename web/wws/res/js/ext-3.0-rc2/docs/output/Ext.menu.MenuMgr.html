<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.menu.MenuMgr-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.menu.MenuMgr-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.menu.MenuMgr-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.menu.MenuMgr"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/MenuMgr.html#cls-Ext.menu.MenuMgr">Ext.menu.MenuMgr</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.menu</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">MenuMgr.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/MenuMgr.html#cls-Ext.menu.MenuMgr">MenuMgr</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Provides a common registry of all menu items on a page so that they can be easily accessed by id.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.menu.MenuMgr-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.menu.MenuMgr-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.MenuMgr-get"></a><b><a href="source/MenuMgr.html#method-Ext.menu.MenuMgr-get">get</a></b>(&nbsp;<code>String/Object&nbsp;menu</code>&nbsp;)
    :
                                        Ext.menu.Menu<div class="mdesc"><div class="short">Returns a Ext.menu.Menu object</div><div class="long">Returns a <a href="output/Ext.menu.Menu.html" ext:cls="Ext.menu.Menu">Ext.menu.Menu</a> object<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>menu</code> : String/Object<div class="sub-desc">The string menu id, an existing menu object reference, or a Menu config that will
be used to generate and return a new Menu instance.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.menu.Menu</code><div class="sub-desc">The specified menu, or null if none are found</div></li></ul></div></div></div></td><td class="msource">MenuMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.menu.MenuMgr-hideAll"></a><b><a href="source/MenuMgr.html#method-Ext.menu.MenuMgr-hideAll">hideAll</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides all menus that are currently visible</div><div class="long">Hides all menus that are currently visible<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">MenuMgr</td></tr></tbody></table><a id="Ext.menu.MenuMgr-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>