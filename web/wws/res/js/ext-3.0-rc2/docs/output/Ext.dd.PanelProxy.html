<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.PanelProxy-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.PanelProxy-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.PanelProxy-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.dd.PanelProxy-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.PanelProxy"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/PanelDD.html#cls-Ext.dd.PanelProxy">Ext.dd.PanelProxy</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">PanelDD.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/PanelDD.html#cls-Ext.dd.PanelProxy">PanelProxy</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">A custom drag proxy implementation specific to <a href="output/Ext.Panel.html" ext:cls="Ext.Panel">Ext.Panel</a>s. This class is primarily used internally
for the Panel's drag drop implementation, and should never need to be created directly.</div><div class="hr"></div><a id="Ext.dd.PanelProxy-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-insertProxy"></a><b><a href="source/PanelDD.html#cfg-Ext.dd.PanelProxy-insertProxy">insertProxy</a></b> : Boolean<div class="mdesc">True to insert a placeholder proxy element while dragging the panel,
false to drag with no proxy (defaults to true).</div></td><td class="msource">PanelProxy</td></tr></tbody></table><a id="Ext.dd.PanelProxy-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.dd.PanelProxy-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-PanelProxy"></a><b><a href="source/PanelDD.html#cls-Ext.dd.PanelProxy">PanelProxy</a></b>(&nbsp;<code>panel&nbsp;The</code>,&nbsp;<code>config&nbsp;Configuration</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>The</code> : panel<div class="sub-desc"><a href="output/Ext.Panel.html" ext:cls="Ext.Panel">Ext.Panel</a> to proxy for</div></li><li><code>Configuration</code> : config<div class="sub-desc">options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-getEl"></a><b><a href="source/PanelDD.html#method-Ext.dd.PanelProxy-getEl">getEl</a></b>()
    :
                                        Element<div class="mdesc"><div class="short">Gets the proxy's element</div><div class="long">Gets the proxy's element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The proxy's element</div></li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-getGhost"></a><b><a href="source/PanelDD.html#method-Ext.dd.PanelProxy-getGhost">getGhost</a></b>()
    :
                                        Element<div class="mdesc"><div class="short">Gets the proxy's ghost element</div><div class="long">Gets the proxy's ghost element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The proxy's ghost element</div></li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-getProxy"></a><b><a href="source/PanelDD.html#method-Ext.dd.PanelProxy-getProxy">getProxy</a></b>()
    :
                                        Element<div class="mdesc"><div class="short">Gets the proxy's element</div><div class="long">Gets the proxy's element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The proxy's element</div></li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-hide"></a><b><a href="source/PanelDD.html#method-Ext.dd.PanelProxy-hide">hide</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides the proxy</div><div class="long">Hides the proxy<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-moveProxy"></a><b><a href="source/PanelDD.html#method-Ext.dd.PanelProxy-moveProxy">moveProxy</a></b>(&nbsp;<code>HTMLElement&nbsp;parentNode</code>,&nbsp;<span title="Optional" class="optional">[<code>HTMLElement&nbsp;before</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Moves the proxy to a different position in the DOM.  This is typically called while dragging the Panel&#13;
to keep the p...</div><div class="long">Moves the proxy to a different position in the DOM.  This is typically called while dragging the Panel
to keep the proxy sync'd to the Panel's location.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>parentNode</code> : HTMLElement<div class="sub-desc">The proxy's parent DOM node</div></li><li><code>before</code> : HTMLElement<div class="sub-desc">(optional) The sibling node before which the proxy should be inserted (defaults
to the parent's last child if not specified)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.PanelProxy-show"></a><b><a href="source/PanelDD.html#method-Ext.dd.PanelProxy-show">show</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Shows the proxy</div><div class="long">Shows the proxy<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PanelProxy</td></tr></tbody></table><a id="Ext.dd.PanelProxy-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>