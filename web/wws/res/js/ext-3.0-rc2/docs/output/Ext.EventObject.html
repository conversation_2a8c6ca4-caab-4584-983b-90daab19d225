<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.EventObject-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.EventObject-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.EventObject-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.EventObject"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/EventManager.html#cls-Ext.EventObject">Ext.EventObject</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">EventManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/EventManager.html#cls-Ext.EventObject">EventObject</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Just as <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> wraps around a native DOM node, Ext.EventObject 
wraps the browser's native event-object normalizing cross-browser differences,
such as which mouse button is clicked, keys pressed, mechanisms to stop
event-propagation along with a method to prevent default actions from taking place.
<p>For example:</p>
<pre><code><b>function</b> handleClick(e, t){ <i>// e is not a standard event object, it is a Ext.EventObject</i>
    e.preventDefault();
    <b>var</b> target = e.getTarget(); <i>// same as t (the target HTMLElement)</i>
    ...
}
<b>var</b> myDiv = <a href="output/Ext.html#Ext-get" ext:member="get" ext:cls="Ext">Ext.get</a>(<em>"myDiv"</em>);  <i>// get reference to an <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a></i>
myDiv.on(         <i>// <em>'on'</em> is shorthand <b>for</b> addListener</i>
    <em>"click"</em>,      <i>// perform an action on click of myDiv</i>
    handleClick   <i>// reference to the action handler</i>
);  
<i>// other methods to <b>do</b> the same:</i>
Ext.EventManager.on(<em>"myDiv"</em>, <em>'click'</em>, handleClick);
Ext.EventManager.addListener(<em>"myDiv"</em>, <em>'click'</em>, handleClick);</code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.EventObject-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.EventObject-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getCharCode"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getCharCode">getCharCode</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the character code for the event.</div><div class="long">Gets the character code for the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getKey"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getKey">getKey</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns a normalized keyCode for the event.</div><div class="long">Returns a normalized keyCode for the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The key code</div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getPageX"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getPageX">getPageX</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the x coordinate of the event.</div><div class="long">Gets the x coordinate of the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getPageY"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getPageY">getPageY</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the y coordinate of the event.</div><div class="long">Gets the y coordinate of the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getRelatedTarget"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getRelatedTarget">getRelatedTarget</a></b>()
    :
                                        HTMLElement<div class="mdesc"><div class="short">Gets the related target.</div><div class="long">Gets the related target.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getTarget"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getTarget">getTarget</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;selector</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number/Mixed&nbsp;maxDepth</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnEl</code>]</span>&nbsp;)
    :
                                        HTMLelement<div class="mdesc"><div class="short">Gets the target for the event.</div><div class="long">Gets the target for the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">(optional) A simple selector to filter the target or look for an ancestor of the target</div></li><li><code>maxDepth</code> : Number/Mixed<div class="sub-desc">(optional) The max depth to
                search as a number or element (defaults to 10 || document.body)</div></li><li><code>returnEl</code> : Boolean<div class="sub-desc">(optional) True to return a Ext.Element object instead of DOM node</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLelement</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getTime"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getTime">getTime</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">//          * Gets the time of the event.
//          *</div><div class="long">//          * Gets the time of the event.
//          *<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">//</div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getWheelDelta"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getWheelDelta">getWheelDelta</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Normalizes mouse wheel delta across browsers</div><div class="long">Normalizes mouse wheel delta across browsers<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The delta</div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-getXY"></a><b><a href="source/EventManager.html#method-Ext.EventObject-getXY">getXY</a></b>()
    :
                                        Array<div class="mdesc"><div class="short">Gets the page coordinates of the event.</div><div class="long">Gets the page coordinates of the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The xy values like [x, y]</div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-preventDefault"></a><b><a href="source/EventManager.html#method-Ext.EventObject-preventDefault">preventDefault</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Prevents the browsers default handling of the event.</div><div class="long">Prevents the browsers default handling of the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-stopEvent"></a><b><a href="source/EventManager.html#method-Ext.EventObject-stopEvent">stopEvent</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Stop the event (preventDefault and stopPropagation)</div><div class="long">Stop the event (preventDefault and stopPropagation)<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-stopPropagation"></a><b><a href="source/EventManager.html#method-Ext.EventObject-stopPropagation">stopPropagation</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Cancels bubbling of the event.</div><div class="long">Cancels bubbling of the event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">EventObject</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.EventObject-within"></a><b><a href="source/EventManager.html#method-Ext.EventObject-within">within</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;related</code>]</span>,&nbsp;<code>Boolean&nbsp;allowEl</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the target of this event is a child of el.  Unless the allowEl parameter is set, it will return false...</div><div class="long">Returns true if the target of this event is a child of el.  Unless the allowEl parameter is set, it will return false if if the target is el.
Example usage:<pre><code><i>// Handle click on any child of an element</i>
		Ext.getBody().on(<em>'click'</em>, <b>function</b>(e){
			<b>if</b>(e.within(<em>'some-el'</em>)){
				alert(<em>'Clicked on a child of some-el!'</em>);
			}
		});
		
		<i>// Handle click directly on an element, ignoring clicks on child nodes</i>
		Ext.getBody().on(<em>'click'</em>, <b>function</b>(e,t){
			<b>if</b>((t.id == <em>'some-el'</em>) && !e.within(t, true)){
				alert(<em>'Clicked directly on some-el!'</em>);
			}
		});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The id, DOM element or Ext.Element to check</div></li><li><code>related</code> : Boolean<div class="sub-desc">(optional) true to test if the related target is within el instead of the target</div></li><li><code>allowEl</code> : Boolean<div class="sub-desc">{optional} true to also check if the passed element is the target or related target</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">EventObject</td></tr></tbody></table><a id="Ext.EventObject-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>