<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Fx-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Fx-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Fx-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.Fx-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Fx"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Fx.html#cls-Ext.Fx">Ext.Fx</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Fx.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Fx.html#cls-Ext.Fx">Fx</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>A class to provide basic animation and visual effects support.  <b>Note:</b> This class is automatically applied
to the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> interface when included, so all effects calls should be performed via <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a>.
Conversely, since the effects are not actually defined in <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a>, Ext.Fx <b>must</b> be
<a href="output/Ext.html#Ext-enableFx" ext:member="enableFx" ext:cls="Ext">included</a> in order for the Element effects to work.</p><br/>
<p><b><u>Method Chaining</u></b></p>
<p>It is important to note that although the Fx methods and many non-Fx Element methods support "method chaining" in that
they return the Element object itself as the method return value, it is not always possible to mix the two in a single
method chain.  The Fx methods use an internal effects queue so that each effect can be properly timed and sequenced.
Non-Fx methods, on the other hand, have no such internal queueing and will always execute immediately.  For this reason,
while it may be possible to mix certain Fx and non-Fx method calls in a single chain, it may not always provide the
expected results and should be done with care.  Also see <tt><a href="output/Ext.Fx.html#Ext.Fx-callback" ext:member="callback" ext:cls="Ext.Fx">callback</a></tt>.</p><br/>
<p><b><u>Anchor Options for Motion Effects</u></b></p>
<p>Motion effects support 8-way anchoring, meaning that you can choose one of 8 different anchor points on the Element
that will serve as either the start or end point of the animation.  Following are all of the supported anchor positions:</p>
<pre>
Value  Description
-----  -----------------------------
tl     The top left corner
t      The center of the top edge
tr     The top right corner
l      The center of the left edge
r      The center of the right edge
bl     The bottom left corner
b      The center of the bottom edge
br     The bottom right corner
</pre>
<b>Note</b>: some Fx methods accept specific custom config parameters.  The options shown in the Config Options
section below are common options that can be passed to any Fx method unless otherwise noted.</b></div><div class="hr"></div><a id="Ext.Fx-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-afterCls"></a><b><a href="source/Fx.html#cfg-Ext.Fx-afterCls">afterCls</a></b> : String<div class="mdesc">A css class to apply after the effect</div></td><td class="msource">Fx</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-afterStyle"></a><b><a href="source/Fx.html#cfg-Ext.Fx-afterStyle">afterStyle</a></b> : String/Object/Function<div class="mdesc"><div class="short">A style specification string, e.g. "width:100px", or an object
in the form {width:"100px"}, or a function which retur...</div><div class="long">A style specification string, e.g. <tt>"width:100px"</tt>, or an object
in the form <tt>{width:"100px"}</tt>, or a function which returns such a specification that will be applied to the
Element after the effect finishes.</div></div></td><td class="msource">Fx</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-block"></a><b><a href="source/Fx.html#cfg-Ext.Fx-block">block</a></b> : Boolean<div class="mdesc">Whether the effect should block other effects from queueing while it runs</div></td><td class="msource">Fx</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-callback"></a><b><a href="source/Fx.html#cfg-Ext.Fx-callback">callback</a></b> : Function<div class="mdesc"><div class="short">A function called when the effect is finished.  Note that effects are queued internally by the
Fx class, so a callbac...</div><div class="long">A function called when the effect is finished.  Note that effects are queued internally by the
Fx class, so a callback is not required to specify another effect -- effects can simply be chained together
and called in sequence (see note for <b><u>Method Chaining</u></b> above), for example:<pre><code>el.slideIn().highlight();</code></pre>
The callback is intended for any additional code that should run once a particular effect has completed. The Element
being operated upon is passed as the first parameter.</div></div></td><td class="msource">Fx</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-concurrent"></a><b><a href="source/Fx.html#cfg-Ext.Fx-concurrent">concurrent</a></b> : Boolean<div class="mdesc"><div class="short">Whether to allow subsequently-queued effects to run at the same time as the current effect, or to ensure that they ru...</div><div class="long">Whether to allow subsequently-queued effects to run at the same time as the current effect, or to ensure that they run in sequence</div></div></td><td class="msource">Fx</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-duration"></a><b><a href="source/Fx.html#cfg-Ext.Fx-duration">duration</a></b> : Number<div class="mdesc">The length of time (in seconds) that the effect should last</div></td><td class="msource">Fx</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-easing"></a><b><a href="source/Fx.html#cfg-Ext.Fx-easing">easing</a></b> : String<div class="mdesc"><div class="short">A valid Ext.lib.Easing value for the effect:&lt;div class="mdetail-params"&gt;
backBoth
backIn
backOut
bounceBoth
bounceIn
...</div><div class="long">A valid Ext.lib.Easing value for the effect:</p><div class="mdetail-params"><ul>
<li><b><tt>backBoth</tt></b></li>
<li><b><tt>backIn</tt></b></li>
<li><b><tt>backOut</tt></b></li>
<li><b><tt>bounceBoth</tt></b></li>
<li><b><tt>bounceIn</tt></b></li>
<li><b><tt>bounceOut</tt></b></li>
<li><b><tt>easeBoth</tt></b></li>
<li><b><tt>easeBothStrong</tt></b></li>
<li><b><tt>easeIn</tt></b></li>
<li><b><tt>easeInStrong</tt></b></li>
<li><b><tt>easeNone</tt></b></li>
<li><b><tt>easeOut</tt></b></li>
<li><b><tt>easeOutStrong</tt></b></li>
<li><b><tt>elasticBoth</tt></b></li>
<li><b><tt>elasticIn</tt></b></li>
<li><b><tt>elasticOut</tt></b></li>
</ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-endOpacity"></a><b><a href="source/Fx.html#cfg-Ext.Fx-endOpacity">endOpacity</a></b> : Number<div class="mdesc">Only applicable for <a href="output/Ext.Fx.html#Ext.Fx-fadeIn" ext:member="fadeIn" ext:cls="Ext.Fx">fadeIn</a> or <a href="output/Ext.Fx.html#Ext.Fx-fadeOut" ext:member="fadeOut" ext:cls="Ext.Fx">fadeOut</a>, a number between
<tt>0</tt> and <tt>1</tt> inclusive to configure the ending opacity value.</div></td><td class="msource">Fx</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-remove"></a><b><a href="source/Fx.html#cfg-Ext.Fx-remove">remove</a></b> : Boolean<div class="mdesc">Whether the Element should be removed from the DOM and destroyed after the effect finishes</div></td><td class="msource">Fx</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-scope"></a><b><a href="source/Fx.html#cfg-Ext.Fx-scope">scope</a></b> : Object<div class="mdesc">The scope of the <tt><a href="output/Ext.Fx.html#Ext.Fx-callback" ext:member="callback" ext:cls="Ext.Fx">callback</a></tt> function</div></td><td class="msource">Fx</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-stopFx"></a><b><a href="source/Fx.html#cfg-Ext.Fx-stopFx">stopFx</a></b> : Boolean<div class="mdesc"><div class="short">Whether preceding effects should be stopped and removed before running current effect (only applies to non blocking e...</div><div class="long">Whether preceding effects should be stopped and removed before running current effect (only applies to non blocking effects)</div></div></td><td class="msource">Fx</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-useDisplay"></a><b><a href="source/Fx.html#cfg-Ext.Fx-useDisplay">useDisplay</a></b> : Boolean<div class="mdesc"><div class="short">Whether to use the display CSS property instead of visibility when hiding Elements (only applies to 
effects that end...</div><div class="long">Whether to use the <i>display</i> CSS property instead of <i>visibility</i> when hiding Elements (only applies to 
effects that end with the element being visually hidden, ignored otherwise)</div></div></td><td class="msource">Fx</td></tr></tbody></table><a id="Ext.Fx-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.Fx-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-fadeIn"></a><b><a href="source/Fx.html#method-Ext.Fx-fadeIn">fadeIn</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Fade an element in (from transparent to opaque).  The ending opacity can be specified
using the endOpacity config opt...</div><div class="long">Fade an element in (from transparent to opaque).  The ending opacity can be specified
using the <tt><a href="output/Ext.Fx.html#Ext.Fx-endOpacity" ext:member="endOpacity" ext:cls="Ext.Fx">endOpacity</a></tt> config option.
Usage:
<pre><code><i>// <b>default</b>: fade <b>in</b> from opacity 0 to 100%</i>
el.fadeIn();

<i>// custom: fade <b>in</b> from opacity 0 to 75% over 2 seconds</i>
el.fadeIn({ endOpacity: .75, duration: 2});

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.fadeIn({
    endOpacity: 1, <i>//can be any value between 0 and 1 (e.g. .5)</i>
    easing: <em>'easeOut'</em>,
    duration: .5
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-fadeOut"></a><b><a href="source/Fx.html#method-Ext.Fx-fadeOut">fadeOut</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Fade an element out (from opaque to transparent).  The ending opacity can be specified
using the endOpacity config op...</div><div class="long">Fade an element out (from opaque to transparent).  The ending opacity can be specified
using the <tt><a href="output/Ext.Fx.html#Ext.Fx-endOpacity" ext:member="endOpacity" ext:cls="Ext.Fx">endOpacity</a></tt> config option.  Note that IE may require
<tt><a href="output/Ext.Fx.html#Ext.Fx-useDisplay" ext:member="useDisplay" ext:cls="Ext.Fx">useDisplay</a>:true</tt> in order to redisplay correctly.
Usage:
<pre><code><i>// <b>default</b>: fade out from the element<em>'s current opacity to 0</i>
el.fadeOut();

<i>// custom: fade out from the element'</em>s current opacity to 25% over 2 seconds</i>
el.fadeOut({ endOpacity: .25, duration: 2});

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.fadeOut({
    endOpacity: 0, <i>//can be any value between 0 and 1 (e.g. .5)</i>
    easing: <em>'easeOut'</em>,
    duration: .5,
    remove: false,
    useDisplay: false
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-frame"></a><b><a href="source/Fx.html#method-Ext.Fx-frame">frame</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;color</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;count</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Shows a ripple of exploding, attenuating borders to draw attention to an Element.
Usage:
// default: a single light b...</div><div class="long">Shows a ripple of exploding, attenuating borders to draw attention to an Element.
Usage:
<pre><code><i>// <b>default</b>: a single light blue ripple</i>
el.frame();

<i>// custom: 3 red ripples lasting 3 seconds total</i>
el.frame(<em>"ff0000"</em>, 3, { duration: 3 });

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.frame(<em>"C3DAF9"</em>, 1, {
    duration: 1 <i>//duration of each individual ripple.</i>
    <i>// Note: Easing is not configurable and will be ignored <b>if</b> included</i>
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>color</code> : String<div class="sub-desc">(optional) The color of the border.  Should be a 6 char hex color without the leading # (defaults to light blue: 'C3DAF9').</div></li><li><code>count</code> : Number<div class="sub-desc">(optional) The number of ripples to display (defaults to 1)</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-ghost"></a><b><a href="source/Fx.html#method-Ext.Fx-ghost">ghost</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;anchor</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Slides the element while fading it out of view.  An anchor point can be optionally passed to set the 
ending point of...</div><div class="long">Slides the element while fading it out of view.  An anchor point can be optionally passed to set the 
ending point of the effect.
Usage:
<pre><code><i>// <b>default</b>: slide the element downward <b>while</b> fading out</i>
el.ghost();

<i>// custom: slide the element out to the right <b>with</b> a 2-second duration</i>
el.ghost(<em>'r'</em>, { duration: 2 });

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.ghost(<em>'b'</em>, {
    easing: <em>'easeOut'</em>,
    duration: .5,
    remove: false,
    useDisplay: false
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>anchor</code> : String<div class="sub-desc">(optional) One of the valid Fx anchor positions (defaults to bottom: 'b')</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-hasActiveFx"></a><b><a href="source/Fx.html#method-Ext.Fx-hasActiveFx">hasActiveFx</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the element has any effects actively running or queued, else returns false.</div><div class="long">Returns true if the element has any effects actively running or queued, else returns false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if element has active effects, else false</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-hasFxBlock"></a><b><a href="source/Fx.html#method-Ext.Fx-hasFxBlock">hasFxBlock</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the element is currently blocking so that no other effect can be queued
until this effect is finished...</div><div class="long">Returns true if the element is currently blocking so that no other effect can be queued
until this effect is finished, else returns false if blocking is not set.  This is commonly
used to ensure that an effect initiated by a user action runs to completion prior to the
same effect being restarted (e.g., firing only one effect even if the user clicks several times).<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if blocking, else false</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-highlight"></a><b><a href="source/Fx.html#method-Ext.Fx-highlight">highlight</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;color</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Highlights the Element by setting a color (applies to the background-color by default, but can be
changed using the "...</div><div class="long">Highlights the Element by setting a color (applies to the background-color by default, but can be
changed using the "attr" config option) and then fading back to the original color. If no original
color is available, you should provide the "endColor" config option which will be cleared after the animation.
Usage:
<pre><code><i>// <b>default</b>: highlight background to yellow</i>
el.highlight();

<i>// custom: highlight foreground text to blue <b>for</b> 2 seconds</i>
el.highlight(<em>"0000ff"</em>, { attr: <em>'color'</em>, duration: 2 });

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.highlight(<em>"ffff9c"</em>, {
    attr: <em>"background-color"</em>, <i>//can be any valid CSS property (attribute) that supports a color value</i>
    endColor: (current color) or <em>"ffffff"</em>,
    easing: <em>'easeIn'</em>,
    duration: 1
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>color</code> : String<div class="sub-desc">(optional) The highlight color. Should be a 6 char hex color without the leading # (defaults to yellow: 'ffff9c')</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-pause"></a><b><a href="source/Fx.html#method-Ext.Fx-pause">pause</a></b>(&nbsp;<code>Number&nbsp;seconds</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Creates a pause before any subsequent queued effects begin.  If there are
no effects queued after the pause it will h...</div><div class="long">Creates a pause before any subsequent queued effects begin.  If there are
no effects queued after the pause it will have no effect.
Usage:
<pre><code>el.pause(1);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>seconds</code> : Number<div class="sub-desc">The length of time to pause (in seconds)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-puff"></a><b><a href="source/Fx.html#method-Ext.Fx-puff">puff</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Fades the element out while slowly expanding it in all directions.  When the effect is completed, the 
element will b...</div><div class="long">Fades the element out while slowly expanding it in all directions.  When the effect is completed, the 
element will be hidden (visibility = 'hidden') but block elements will still take up space in the document. 
The element must be removed from the DOM using the 'remove' config option if desired.
Usage:
<pre><code><i>// <b>default</b></i>
el.puff();

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.puff({
    easing: <em>'easeOut'</em>,
    duration: .5,
    remove: false,
    useDisplay: false
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-scale"></a><b><a href="source/Fx.html#method-Ext.Fx-scale">scale</a></b>(&nbsp;<code>Number&nbsp;width</code>,&nbsp;<code>Number&nbsp;height</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Animates the transition of an element's dimensions from a starting height/width
to an ending height/width.  This meth...</div><div class="long">Animates the transition of an element's dimensions from a starting height/width
to an ending height/width.  This method is a convenience implementation of <a href="output/shift.html" ext:cls="shift">shift</a>.
Usage:
<pre><code><i>// change height and width to 100x100 pixels</i>
el.scale(100, 100);

<i>// common config options shown <b>with</b> <b>default</b> values.  The height and width will <b>default</b> to</i>
<i>// the element&#39;s existing values <b>if</b> passed as null.</i>
el.scale(
    [element&#39;s width],
    [element&#39;s height], {
	    easing: <em>'easeOut'</em>,
	    duration: .35
	}
);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Number<div class="sub-desc">The new width (pass undefined to keep the original width)</div></li><li><code>height</code> : Number<div class="sub-desc">The new height (pass undefined to keep the original height)</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-sequenceFx"></a><b><a href="source/Fx.html#method-Ext.Fx-sequenceFx">sequenceFx</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Ensures that all effects queued after sequenceFx is called on the element are
run in sequence.  This is the opposite ...</div><div class="long">Ensures that all effects queued after sequenceFx is called on the element are
run in sequence.  This is the opposite of <a href="output/Ext.Fx.html#Ext.Fx-syncFx" ext:member="syncFx" ext:cls="Ext.Fx">syncFx</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-shift"></a><b><a href="source/Fx.html#method-Ext.Fx-shift">shift</a></b>(&nbsp;<code>Object&nbsp;options</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Animates the transition of any combination of an element's dimensions, xy position and/or opacity.
Any of these prope...</div><div class="long">Animates the transition of any combination of an element's dimensions, xy position and/or opacity.
Any of these properties not specified in the config object will not be changed.  This effect 
requires that at least one new dimension, position or opacity setting must be passed in on
the config object in order for the function to have any effect.
Usage:
<pre><code><i>// slide the element horizontally to x position 200 <b>while</b> changing the height and opacity</i>
el.shift({ x: 200, height: 50, opacity: .8 });

<i>// common config options shown <b>with</b> <b>default</b> values.</i>
el.shift({
    width: [element&#39;s width],
    height: [element&#39;s height],
    x: [element&#39;s x position],
    y: [element&#39;s y position],
    opacity: [element&#39;s opacity],
    easing: <em>'easeOut'</em>,
    duration: .35
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-slideIn"></a><b><a href="source/Fx.html#method-Ext.Fx-slideIn">slideIn</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;anchor</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Slides the element into view.  An anchor point can be optionally passed to set the point of
origin for the slide effe...</div><div class="long">Slides the element into view.  An anchor point can be optionally passed to set the point of
origin for the slide effect.  This function automatically handles wrapping the element with
a fixed-size container if needed.  See the Fx class overview for valid anchor point options.
Usage:
<pre><code><i>// <b>default</b>: slide the element <b>in</b> from the top</i>
el.slideIn();

<i>// custom: slide the element <b>in</b> from the right <b>with</b> a 2-second duration</i>
el.slideIn(<em>'r'</em>, { duration: 2 });

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.slideIn(<em>'t'</em>, {
    easing: <em>'easeOut'</em>,
    duration: .5
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>anchor</code> : String<div class="sub-desc">(optional) One of the valid Fx anchor positions (defaults to top: 't')</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-slideOut"></a><b><a href="source/Fx.html#method-Ext.Fx-slideOut">slideOut</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;anchor</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Slides the element out of view.  An anchor point can be optionally passed to set the end point
for the slide effect. ...</div><div class="long">Slides the element out of view.  An anchor point can be optionally passed to set the end point
for the slide effect.  When the effect is completed, the element will be hidden (visibility = 
'hidden') but block elements will still take up space in the document.  The element must be removed
from the DOM using the 'remove' config option if desired.  This function automatically handles 
wrapping the element with a fixed-size container if needed.  See the Fx class overview for valid anchor point options.
Usage:
<pre><code><i>// <b>default</b>: slide the element out to the top</i>
el.slideOut();

<i>// custom: slide the element out to the right <b>with</b> a 2-second duration</i>
el.slideOut(<em>'r'</em>, { duration: 2 });

<i>// common config options shown <b>with</b> <b>default</b> values</i>
el.slideOut(<em>'t'</em>, {
    easing: <em>'easeOut'</em>,
    duration: .5,
    remove: false,
    useDisplay: false
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>anchor</code> : String<div class="sub-desc">(optional) One of the valid Fx anchor positions (defaults to top: 't')</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-stopFx"></a><b><a href="source/Fx.html#method-Ext.Fx-stopFx">stopFx</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Stops any running effects and clears the element's internal effects queue if it contains
any additional effects that ...</div><div class="long">Stops any running effects and clears the element's internal effects queue if it contains
any additional effects that haven't started yet.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-switchOff"></a><b><a href="source/Fx.html#method-Ext.Fx-switchOff">switchOff</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Blinks the element as if it was clicked and then collapses on its center (similar to switching off a television).
Whe...</div><div class="long">Blinks the element as if it was clicked and then collapses on its center (similar to switching off a television).
When the effect is completed, the element will be hidden (visibility = 'hidden') but block elements will still 
take up space in the document. The element must be removed from the DOM using the 'remove' config option if desired.
Usage:
<pre><code><i>// <b>default</b></i>
el.switchOff();

<i>// all config options shown <b>with</b> <b>default</b> values</i>
el.switchOff({
    easing: <em>'easeIn'</em>,
    duration: .3,
    remove: false,
    useDisplay: false
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">(optional) Object literal with any of the Fx config options</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Fx-syncFx"></a><b><a href="source/Fx.html#method-Ext.Fx-syncFx">syncFx</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Ensures that all effects queued after syncFx is called on the element are
run concurrently.  This is the opposite of ...</div><div class="long">Ensures that all effects queued after syncFx is called on the element are
run concurrently.  This is the opposite of <a href="output/Ext.Fx.html#Ext.Fx-sequenceFx" ext:member="sequenceFx" ext:cls="Ext.Fx">sequenceFx</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element</div></li></ul></div></div></div></td><td class="msource">Fx</td></tr></tbody></table><a id="Ext.Fx-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>