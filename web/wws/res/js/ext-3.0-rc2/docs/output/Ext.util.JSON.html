<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.util.JSON-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.util.JSON-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.util.JSON-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.util.JSON"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/JSON.html#cls-Ext.util.JSON">Ext.util.JSON</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.util</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">JSON.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/JSON.html#cls-Ext.util.JSON">JSON</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Modified version of Douglas Crockford"s json.js that doesn"t
mess with the Object prototype
http://www.json.org/js.html<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.util.JSON-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.util.JSON-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.JSON-decode"></a><b><a href="source/JSON.html#method-Ext.util.JSON-decode">decode</a></b>(&nbsp;<code>String&nbsp;json</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Decodes (parses) a JSON string to an object. If the JSON is invalid, this function throws a SyntaxError unless the sa...</div><div class="long">Decodes (parses) a JSON string to an object. If the JSON is invalid, this function throws a SyntaxError unless the safe option is set.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>json</code> : String<div class="sub-desc">The JSON string</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The resulting object</div></li></ul></div></div></div></td><td class="msource">JSON</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.JSON-encode"></a><b><a href="source/JSON.html#method-Ext.util.JSON-encode">encode</a></b>(&nbsp;<code>Mixed&nbsp;o</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Encodes an Object, Array or other value</div><div class="long">Encodes an Object, Array or other value<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Mixed<div class="sub-desc">The variable to encode</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The JSON string</div></li></ul></div></div></div></td><td class="msource">JSON</td></tr></tbody></table><a id="Ext.util.JSON-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>