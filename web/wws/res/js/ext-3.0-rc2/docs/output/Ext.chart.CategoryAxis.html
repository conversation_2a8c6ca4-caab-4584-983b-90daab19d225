<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.chart.CategoryAxis-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.chart.CategoryAxis-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.chart.CategoryAxis-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.chart.CategoryAxis"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.chart.Axis.html" ext:member="" ext:cls="Ext.chart.Axis">Axis</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">CategoryAxis</pre></div><h1>Class <a href="source/Chart.html#cls-Ext.chart.CategoryAxis">Ext.chart.CategoryAxis</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.chart</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Chart.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Chart.html#cls-Ext.chart.CategoryAxis">CategoryAxis</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.chart.Axis.html" ext:cls="Ext.chart.Axis" ext:member="">Axis</a></td></tr></table><div class="description">A type of axis that displays items in categories.</div><div class="hr"></div><a id="Ext.chart.CategoryAxis-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.CategoryAxis-categoryNames"></a><b><a href="source/Chart.html#prop-Ext.chart.CategoryAxis-categoryNames">categoryNames</a></b> : Array<div class="mdesc">A list of category names to display along this axis.</div></td><td class="msource">CategoryAxis</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-hideOverlappingLabels"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-hideOverlappingLabels">hideOverlappingLabels</a></b> : Boolean<div class="mdesc">If true, labels that overlap previously drawn labels on the axis will be hidden.</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#hideOverlappingLabels" ext:member="#hideOverlappingLabels" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-labelFunction"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-labelFunction">labelFunction</a></b> : String<div class="mdesc"><div class="short">A string reference to the globally-accessible function that may be called to&#13;
determine each of the label values for ...</div><div class="long">A string reference to the globally-accessible function that may be called to
determine each of the label values for this axis.</div></div></td><td class="msource"><a href="output/Ext.chart.Axis.html#labelFunction" ext:member="#labelFunction" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-orientation"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-orientation">orientation</a></b> : String<div class="mdesc">The direction in which the axis is drawn. May be "horizontal" or "vertical".</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#orientation" ext:member="#orientation" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-reverse"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-reverse">reverse</a></b> : Boolean<div class="mdesc">If true, the items on the axis will be drawn in opposite direction.</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#reverse" ext:member="#reverse" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-type"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-type">type</a></b> : String<div class="mdesc">The type of axis.</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#type" ext:member="#type" ext:cls="Ext.chart.Axis">Axis</a></td></tr></tbody></table><a id="Ext.chart.CategoryAxis-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.chart.CategoryAxis-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>