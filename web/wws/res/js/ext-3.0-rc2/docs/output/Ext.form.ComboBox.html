<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.form.ComboBox-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.form.ComboBox-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.form.ComboBox-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.form.ComboBox-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.form.ComboBox"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Component.html" ext:member="" ext:cls="Ext.Component">Component</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.BoxComponent.html" ext:member="" ext:cls="Ext.BoxComponent">BoxComponent</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.form.Field.html" ext:member="" ext:cls="Ext.form.Field">Field</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.form.TextField.html" ext:member="" ext:cls="Ext.form.TextField">TextField</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.form.TriggerField.html" ext:member="" ext:cls="Ext.form.TriggerField">TriggerField</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">ComboBox</pre></div><h1>Class <a href="source/Combo.html#cls-Ext.form.ComboBox">Ext.form.ComboBox</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.form</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Combo.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Combo.html#cls-Ext.form.ComboBox">ComboBox</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.form.TimeField.html" ext:cls="Ext.form.TimeField">TimeField</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.form.TriggerField.html" ext:cls="Ext.form.TriggerField" ext:member="">TriggerField</a></td></tr><tr><td class="label">xtype:</td><td class="hd-info">combo</td></tr></table><div class="description"><p>A combobox control with support for autocomplete, remote-loading, paging and many other features.</p>
<p>A ComboBox works in a similar manner to a traditional HTML &lt;select> field. The difference is
that to submit the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">valueField</a>, you must specify a <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a> to create a hidden input
field to hold the value of the valueField. The <i><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-displayField" ext:member="displayField" ext:cls="Ext.form.ComboBox">displayField</a></i> is shown in the text field
which is named according to the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-name" ext:member="name" ext:cls="Ext.form.ComboBox">name</a>.</p>
<p><b><u>Events</u></b></p>
<p>To do something when something in ComboBox is selected, configure the select event:<pre><code><b>var</b> cb = <b>new</b> Ext.form.ComboBox({
    <i>// all of your config options</i>
    listeners:{
         scope: yourScope,
         <em>'select'</em>: yourFunction
    }
});

<i>// Alternatively, you can assign events after the object is created:</i>
<b>var</b> cb = <b>new</b> Ext.form.ComboBox(yourOptions);
cb.on(<em>'select'</em>, yourFunction, yourScope);</code></pre></p>
<p><b><u>ComboBox in Grid</u></b></p>
<p>If using a ComboBox in an <a href="output/Ext.grid.EditorGridPanel.html" ext:cls="Ext.grid.EditorGridPanel">Editor Grid</a> a <a href="output/Ext.grid.Column.html#Ext.grid.Column-renderer" ext:member="renderer" ext:cls="Ext.grid.Column">renderer</a>
will be needed to show the displayField when the editor is not active.  Set up the renderer manually, or implement
a reusable render, for example:<pre><code><i>// create reusable renderer</i>
Ext.util.Format.comboRenderer = <b>function</b>(combo){
    <b>return</b> <b>function</b>(value){
        <b>var</b> record = combo.findRecord(combo.<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">valueField</a>, value);
        <b>return</b> record ? record.get(combo.<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-displayField" ext:member="displayField" ext:cls="Ext.form.ComboBox">displayField</a>) : combo.<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueNotFoundText" ext:member="valueNotFoundText" ext:cls="Ext.form.ComboBox">valueNotFoundText</a>;
    }
}

<i>// create the combo instance</i>
<b>var</b> combo = <b>new</b> Ext.form.ComboBox({
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-typeAhead" ext:member="typeAhead" ext:cls="Ext.form.ComboBox">typeAhead</a>: true,
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-triggerAction" ext:member="triggerAction" ext:cls="Ext.form.ComboBox">triggerAction</a>: <em>'all'</em>,
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-lazyRender" ext:member="lazyRender" ext:cls="Ext.form.ComboBox">lazyRender</a>:true,
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a>: <em>'local'</em>,
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-store" ext:member="store" ext:cls="Ext.form.ComboBox">store</a>: <b>new</b> Ext.data.ArrayStore({
        id: 0,
        fields: [
            <em>'myId'</em>,
            <em>'displayText'</em>
        ],
        data: [[1, <em>'item1'</em>], [2, <em>'item2'</em>]]
    }),
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">valueField</a>: <em>'myId'</em>,
    <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-displayField" ext:member="displayField" ext:cls="Ext.form.ComboBox">displayField</a>: <em>'displayText'</em>
});

<i>// snippet of column model used within grid</i>
<b>var</b> cm = <b>new</b> Ext.grid.ColumnModel([{
       ...
    },{
       header: <em>"Some Header"</em>,
       dataIndex: <em>'whatever'</em>,
       width: 130,
       editor: combo, <i>// specify reference to combo instance</i>
       renderer: Ext.util.Format.comboRenderer(combo) <i>// pass combo instance to reusable renderer</i>
    },
    ...
]);</code></pre></p>
<p><b><u>Filtering</u></b></p>
<p>A ComboBox <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-doQuery" ext:member="doQuery" ext:cls="Ext.form.ComboBox">uses filtering itself</a>, for information about filtering the ComboBox
store manually see <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-lastQuery" ext:member="lastQuery" ext:cls="Ext.form.ComboBox">lastQuery</a></tt>.</p></div><div class="hr"></div><a id="Ext.form.ComboBox-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-allQuery"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-allQuery">allQuery</a></b> : String<div class="mdesc">The text query to send to the server to return all records for the list
with no filtering (defaults to '')</div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-allowBlank"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-allowBlank">allowBlank</a></b> : Boolean<div class="mdesc">Specify <tt>false</tt> to validate that the value's length is > 0 (defaults to
<tt>true</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#allowBlank" ext:member="#allowBlank" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-allowDomMove"></a><b><a href="source/Component.html#cfg-Ext.Component-allowDomMove">allowDomMove</a></b> : Boolean<div class="mdesc">Whether the component can move the Dom node when rendering (defaults to true).</div></td><td class="msource"><a href="output/Ext.Component.html#allowDomMove" ext:member="#allowDomMove" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-anchor"></a><b><a href="source/Component.html#cfg-Ext.Component-anchor">anchor</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this Component is rendered
by a Container which has been configured to use the An...</div><div class="long"><p><b>Note</b>: this config is only used when this Component is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">AnchorLayout</a></b>
layout manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'anchor'</tt>).</p><br>
<p>See <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>.<a href="output/Ext.layout.AnchorLayout.html#Ext.layout.AnchorLayout-anchor" ext:member="anchor" ext:cls="Ext.layout.AnchorLayout">anchor</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#anchor" ext:member="#anchor" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-applyTo"></a><b><a href="source/Component.html#cfg-Ext.Component-applyTo">applyTo</a></b> : Mixed<div class="mdesc"><div class="short">Specify the id of the element, a DOM element or an existing Element corresponding to a DIV
that is already present in...</div><div class="long"><p>Specify the id of the element, a DOM element or an existing Element corresponding to a DIV
that is already present in the document that specifies some structural markup for this
component.</p><div><ul>
<li><b>Description</b> : <ul>
<div class="sub-desc">When <tt>applyTo</tt> is used, constituent parts of the component can also be specified
by id or CSS class name within the main element, and the component being created may attempt
to create its subcomponents from that markup if applicable.</div>
</ul></li>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When using this config, a call to render() is not required.</div>
<div class="sub-desc">If applyTo is specified, any value passed for <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a> will be ignored and the target
element's parent node will automatically be used as the component's container.</div>
</ul></li>
</ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#applyTo" ext:member="#applyTo" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-autoCreate"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-autoCreate">autoCreate</a></b> : String/Object<div class="mdesc"><div class="short">A DomHelper element spec, or true for a default
element spec. Used to create the Element which will encapsulate this ...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> element spec, or <tt>true</tt> for a default
element spec. Used to create the <a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">Element</a> which will encapsulate this Component.
See <tt><a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a></tt> for details.  Defaults to:</p>
<pre><code>{tag: <em>"input"</em>, type: <em>"text"</em>, size: <em>"24"</em>, autocomplete: <em>"off"</em>}</code></pre></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoEl"></a><b><a href="source/Component.html#cfg-Ext.Component-autoEl">autoEl</a></b> : Mixed<div class="mdesc"><div class="short">A tag name or DomHelper spec used to create the Element which will
encapsulate this Component.
You do not normally ne...</div><div class="long"><p>A tag name or <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> spec used to create the <a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">Element</a> which will
encapsulate this Component.</p>
<p>You do not normally need to specify this. For the base classes <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>, <a href="output/Ext.BoxComponent.html" ext:cls="Ext.BoxComponent">Ext.BoxComponent</a>,
and <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>, this defaults to <b><tt>'div'</tt></b>. The more complex Ext classes use a more complex
DOM structure created by their own onRender methods.</p>
<p>This is intended to allow the developer to create application-specific utility Components encapsulated by
different DOM elements. Example usage:</p><pre><code>{
    xtype: <em>'box'</em>,
    autoEl: {
        tag: <em>'img'</em>,
        src: <em>'http:<i>//www.example.com/example.jpg'</em></i>
    }
}, {
    xtype: <em>'box'</em>,
    autoEl: {
        tag: <em>'blockquote'</em>,
        html: <em>'autoEl is cool!'</em>
    }
}, {
    xtype: <em>'container'</em>,
    autoEl: <em>'ul'</em>,
    cls: <em>'ux-unordered-list'</em>,
    items: {
        xtype: <em>'box'</em>,
        autoEl: <em>'li'</em>,
        html: <em>'First list item'</em>
    }
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#autoEl" ext:member="#autoEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-autoHeight"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-autoHeight">autoHeight</a></b> : Boolean<div class="mdesc"><div class="short">True to use height:'auto', false to use fixed height (defaults to false). Note: Although many components 
inherit thi...</div><div class="long">True to use height:'auto', false to use fixed height (defaults to false). <b>Note</b>: Although many components 
inherit this config option, not all will function as expected with a height of 'auto'. Setting autoHeight:true 
means that the browser will manage height based on the element's contents, and that Ext will not manage it at all.</div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#autoHeight" ext:member="#autoHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoShow"></a><b><a href="source/Component.html#cfg-Ext.Component-autoShow">autoShow</a></b> : Boolean<div class="mdesc"><div class="short">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render...</div><div class="long">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render (defaults to false).</div></div></td><td class="msource"><a href="output/Ext.Component.html#autoShow" ext:member="#autoShow" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-autoWidth"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-autoWidth">autoWidth</a></b> : Boolean<div class="mdesc"><div class="short">True to use width:'auto', false to use fixed width (defaults to false). Note: Although many components 
inherit this ...</div><div class="long">True to use width:'auto', false to use fixed width (defaults to false). <b>Note</b>: Although many components 
inherit this config option, not all will function as expected with a width of 'auto'. Setting autoWidth:true 
means that the browser will manage width based on the element's contents, and that Ext will not manage it at all.</div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#autoWidth" ext:member="#autoWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-blankText"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-blankText">blankText</a></b> : String<div class="mdesc">The error text to display if the <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-allowBlank" ext:member="allowBlank" ext:cls="Ext.form.TextField">allowBlank</a></tt></b> validation
fails (defaults to <tt>"This field is required"</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#blankText" ext:member="#blankText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-clearCls"></a><b><a href="source/Component.html#cfg-Ext.Component-clearCls">clearCls</a></b> : String<div class="mdesc"><div class="short">The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide...</div><div class="long"><p>The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide field clearing (defaults to
<tt>'x-form-clear-left'</tt>).</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container
which has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout
manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>) and either a 
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> is specified or <tt>isFormField=true</tt> is specified.</p><br>
<p>See <a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#clearCls" ext:member="#clearCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-cls"></a><b><a href="source/Field.html#cfg-Ext.form.Field-cls">cls</a></b> : String<div class="mdesc">A custom CSS class to apply to the field's underlying element (defaults to "").</div></td><td class="msource"><a href="output/Ext.form.Field.html#cls" ext:member="#cls" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ctCls"></a><b><a href="source/Component.html#cfg-Ext.Component-ctCls">ctCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's container. This can be useful for
adding customize...</div><div class="long"><p>An optional extra CSS class that will be added to this component's container. This can be useful for
adding customized styles to the container or any of its children using standard CSS rules.  See
<a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a>.<a href="output/Ext.layout.ContainerLayout.html#Ext.layout.ContainerLayout-extraCls" ext:member="extraCls" ext:cls="Ext.layout.ContainerLayout">extraCls</a> also.</p>
<p><b>Note</b>: <tt>ctCls</tt> defaults to <tt>''</tt> except for the following class
which assigns a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-layout-ct'</tt></li>
</ul></div>
To configure the above Class with an extra CSS class append to the default.  For example,
for BoxLayout (Hbox and Vbox):<pre><code>ctCls: <em>'x-box-layout-ct custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ctCls" ext:member="#ctCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-disableKeyFilter"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-disableKeyFilter">disableKeyFilter</a></b> : Boolean<div class="mdesc">Specify <tt>true</tt> to disable input keystroke filtering (defaults
to <tt>false</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#disableKeyFilter" ext:member="#disableKeyFilter" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-disabled"></a><b><a href="source/Field.html#cfg-Ext.form.Field-disabled">disabled</a></b> : Boolean<div class="mdesc"><div class="short">True to disable the field (defaults to false).
Be aware that conformant with the &lt;a href="http://www.w3.org/TR/html40...</div><div class="long">True to disable the field (defaults to false).
<p>Be aware that conformant with the <a href="http://www.w3.org/TR/html401/interact/forms.html#h-17.12.1">HTML specification</a>,
disabled Fields will not be <a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-submit" ext:member="submit" ext:cls="Ext.form.BasicForm">submitted</a>.</p></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#disabled" ext:member="#disabled" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disabledClass"></a><b><a href="source/Component.html#cfg-Ext.Component-disabledClass">disabledClass</a></b> : String<div class="mdesc">CSS class added to the component when it is disabled (defaults to "x-item-disabled").</div></td><td class="msource"><a href="output/Ext.Component.html#disabledClass" ext:member="#disabledClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-displayField"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-displayField">displayField</a></b> : String<div class="mdesc"><div class="short">The underlying data field name to bind to this
ComboBox (defaults to undefined if mode = 'remote' or 'text' if
transf...</div><div class="long">The underlying <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">data field name</a> to bind to this
ComboBox (defaults to undefined if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'remote'</tt> or <tt>'text'</tt> if
<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-transform" ext:member="transform" ext:cls="Ext.form.ComboBox">transforming a select</a> a select).
<p>See also <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">valueField</a></tt>.</p>
<p><b>Note</b>: if using a ComboBox in an <a href="output/Ext.grid.EditorGridPanel.html" ext:cls="Ext.grid.EditorGridPanel">Editor Grid</a> a
<a href="output/Ext.grid.Column.html#Ext.grid.Column-renderer" ext:member="renderer" ext:cls="Ext.grid.Column">renderer</a> will be needed to show the displayField when the editor is not
active.</p></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TriggerField-editable"></a><b><a href="source/TriggerField.html#cfg-Ext.form.TriggerField-editable">editable</a></b> : Boolean<div class="mdesc"><div class="short">false to prevent the user from typing text directly into the field,
the field will only respond to a click on the tri...</div><div class="long"><tt>false</tt> to prevent the user from typing text directly into the field,
the field will only respond to a click on the trigger to set the value. (defaults to <tt>true</tt>)</div></div></td><td class="msource"><a href="output/Ext.form.TriggerField.html#editable" ext:member="#editable" ext:cls="Ext.form.TriggerField">TriggerField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-emptyClass"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-emptyClass">emptyClass</a></b> : String<div class="mdesc"><div class="short">The CSS class to apply to an empty field to style the emptyText
(defaults to 'x-form-empty-field').  This class is au...</div><div class="long">The CSS class to apply to an empty field to style the <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-emptyText" ext:member="emptyText" ext:cls="Ext.form.TextField">emptyText</a></tt></b>
(defaults to <tt>'x-form-empty-field'</tt>).  This class is automatically added and removed as needed
depending on the current field value.</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#emptyClass" ext:member="#emptyClass" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-emptyText"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-emptyText">emptyText</a></b> : String<div class="mdesc"><div class="short">The default text to place into an empty field (defaults to null).
Note: that this value will be submitted to the serv...</div><div class="long">The default text to place into an empty field (defaults to <tt>null</tt>).
<b>Note</b>: that this value will be submitted to the server if this field is enabled and configured
with a <a href="output/Ext.form.TextField.html#Ext.form.TextField-name" ext:member="name" ext:cls="Ext.form.TextField">name</a>.</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#emptyText" ext:member="#emptyText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-enableKeyEvents"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-enableKeyEvents">enableKeyEvents</a></b> : Boolean<div class="mdesc"><tt>true</tt> to enable the proxying of key events for the HTML input
field (defaults to <tt>false</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#enableKeyEvents" ext:member="#enableKeyEvents" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-fieldClass"></a><b><a href="source/Field.html#cfg-Ext.form.Field-fieldClass">fieldClass</a></b> : String<div class="mdesc">The default CSS class for the field (defaults to "x-form-field")</div></td><td class="msource"><a href="output/Ext.form.Field.html#fieldClass" ext:member="#fieldClass" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-fieldLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-fieldLabel">fieldLabel</a></b> : String<div class="mdesc"><div class="short">The label text to display next to this Component (defaults to '').
Note: this config is only used when this Component...</div><div class="long"><p>The label text to display next to this Component (defaults to '').</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container which
has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout manager (eg. 
<a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>).</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#fieldLabel" ext:member="#fieldLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-focusClass"></a><b><a href="source/Field.html#cfg-Ext.form.Field-focusClass">focusClass</a></b> : String<div class="mdesc">The CSS class to use when the field receives focus (defaults to "x-form-focus")</div></td><td class="msource"><a href="output/Ext.form.Field.html#focusClass" ext:member="#focusClass" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-forceSelection"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-forceSelection">forceSelection</a></b> : Boolean<div class="mdesc"><div class="short">true to restrict the selected value to one of the values in the list,
false to allow the user to set arbitrary text i...</div><div class="long"><tt>true</tt> to restrict the selected value to one of the values in the list,
<tt>false</tt> to allow the user to set arbitrary text into the field (defaults to <tt>false</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-handleHeight"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-handleHeight">handleHeight</a></b> : Number<div class="mdesc">The height in pixels of the dropdown list resize handle if
<tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-resizable" ext:member="resizable" ext:cls="Ext.form.ComboBox">resizable</a> = true</tt> (defaults to <tt>8</tt>)</div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-height"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-height">height</a></b> : Number<div class="mdesc">The height of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#height" ext:member="#height" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#cfg-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">Render this component hidden (default is false).</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-hiddenId"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-hiddenId">hiddenId</a></b> : String<div class="mdesc"><div class="short">If hiddenName is specified, hiddenId can also be provided
to give the hidden field a unique id (defaults to the hidde...</div><div class="long">If <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a></tt> is specified, <tt>hiddenId</tt> can also be provided
to give the hidden field a unique id (defaults to the <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a></tt>).  The <tt>hiddenId</tt>
and combo <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a> should be different, since no two DOM
nodes should share the same id.</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-hiddenName"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-hiddenName">hiddenName</a></b> : String<div class="mdesc"><div class="short">If specified, a hidden form field with this name is dynamically generated to store the
field's data value (defaults t...</div><div class="long">If specified, a hidden form field with this name is dynamically generated to store the
field's data value (defaults to the underlying DOM element's name). Required for the combo's value to automatically
post during a form submission.  See also <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">valueField</a>.
<p><b>Note</b>: the hidden field's id will also default to this name if <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenId" ext:member="hiddenId" ext:cls="Ext.form.ComboBox">hiddenId</a> is not specified.
The ComboBox <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a> and the <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenId" ext:member="hiddenId" ext:cls="Ext.form.ComboBox">hiddenId</a></tt> <b>should be different</b>, since
no two DOM nodes should share the same id.  So, if the ComboBox <tt><a href="output/Ext.form.Field.html#Ext.form.Field-name" ext:member="name" ext:cls="Ext.form.Field">name</a></tt> and
<tt>hiddenName</tt> are the same, you should specify a unique <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenId" ext:member="hiddenId" ext:cls="Ext.form.ComboBox">hiddenId</a></tt>.</p></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-hiddenValue"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-hiddenValue">hiddenValue</a></b> : String<div class="mdesc"><div class="short">Sets the initial value of the hidden field if hiddenName is
specified to contain the selected valueField, from the St...</div><div class="long">Sets the initial value of the hidden field if <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a> is
specified to contain the selected <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">valueField</a>, from the Store. Defaults to the configured
<tt><a href="output/Ext.form.Field.html#Ext.form.Field-value" ext:member="value" ext:cls="Ext.form.Field">value</a></tt>.</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-hideLabel">hideLabel</a></b> : Boolean<div class="mdesc"><div class="short">true to completely hide the label element
(label and separator). Defaults to false.
By default, even if you do not sp...</div><div class="long"><p><tt>true</tt> to completely hide the label element
(<a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">label</a> and <a href="output/Ext.Component.html#Ext.Component-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.Component">separator</a>). Defaults to <tt>false</tt>.
By default, even if you do not specify a <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> the space will still be
reserved so that the field will line up with other fields that do have labels.
Setting this to <tt>true</tt> will cause the field to not reserve that space.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>
        hideLabel: true
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideLabel" ext:member="#hideLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideMode"></a><b><a href="source/Component.html#cfg-Ext.Component-hideMode">hideMode</a></b> : String<div class="mdesc"><div class="short">How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset p...</div><div class="long"><p>How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset position) and "display" (css display) - defaults to "display".</p>
<p>For Containers which may be hidden and shown as part of a <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">card layout</a> Container such as a
<a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">TabPanel</a>, it is recommended that hideMode is configured as "offsets". This ensures
that hidden Components still have height and width so that layout managers can perform measurements when
calculating layouts.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideMode" ext:member="#hideMode" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideParent"></a><b><a href="source/Component.html#cfg-Ext.Component-hideParent">hideParent</a></b> : Boolean<div class="mdesc"><div class="short">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the...</div><div class="long">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the component itself (defaults to false).  For example, this can be used as a shortcut for a hide
button on a window by setting hide:true on the button when adding it to its parent container.</div></div></td><td class="msource"><a href="output/Ext.Component.html#hideParent" ext:member="#hideParent" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TriggerField-hideTrigger"></a><b><a href="source/TriggerField.html#cfg-Ext.form.TriggerField-hideTrigger">hideTrigger</a></b> : Boolean<div class="mdesc"><tt>true</tt> to hide the trigger element and display only the base
text field (defaults to <tt>false</tt>)</div></td><td class="msource"><a href="output/Ext.form.TriggerField.html#hideTrigger" ext:member="#hideTrigger" ext:cls="Ext.form.TriggerField">TriggerField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-id"></a><b><a href="source/Component.html#cfg-Ext.Component-id">id</a></b> : String<div class="mdesc"><div class="short">The unique id of this component (defaults to an auto-assigned id).
You should assign an id if you need to be able to ...</div><div class="long"><p>The <b>unique</b> id of this component (defaults to an <a href="output/Ext.Component.html#Ext.Component-getId" ext:member="getId" ext:cls="Ext.Component">auto-assigned id</a>).
You should assign an id if you need to be able to access the component later and you do
not have an object reference available (e.g., using <a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>).</p>
<p>Note that this id will also be used as the element id for the containing HTML element
that is rendered to the page for this component. This allows you to write id-based CSS
rules to style the specific instance of this component uniquely, and also to select
sub-elements using this component's id as the parent.</p>
<p><b>Note</b>: to avoid complications imposed by a unique <tt>id</tt> see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#id" ext:member="#id" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-inputType"></a><b><a href="source/Field.html#cfg-Ext.form.Field-inputType">inputType</a></b> : String<div class="mdesc"><div class="short">The type attribute for input fields -- e.g. radio, text, password, file (defaults
to "text"). The types "file" and "p...</div><div class="long">The type attribute for input fields -- e.g. radio, text, password, file (defaults
to "text"). The types "file" and "password" must be used to render those field types currently -- there are
no separate Ext components for those. Note that if you use <tt>inputType:'file'</tt>, <a href="output/Ext.form.Field.html#Ext.form.Field-emptyText" ext:member="emptyText" ext:cls="Ext.form.Field">emptyText</a>
is not supported and should be avoided.</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#inputType" ext:member="#inputType" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-invalidClass"></a><b><a href="source/Field.html#cfg-Ext.form.Field-invalidClass">invalidClass</a></b> : String<div class="mdesc">The CSS class to use when marking a field invalid (defaults to "x-form-invalid")</div></td><td class="msource"><a href="output/Ext.form.Field.html#invalidClass" ext:member="#invalidClass" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-invalidText"></a><b><a href="source/Field.html#cfg-Ext.form.Field-invalidText">invalidText</a></b> : String<div class="mdesc"><div class="short">The error text to use when marking a field invalid and no message is provided
(defaults to "The value in this field i...</div><div class="long">The error text to use when marking a field invalid and no message is provided
(defaults to "The value in this field is invalid")</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#invalidText" ext:member="#invalidText" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemCls"></a><b><a href="source/Component.html#cfg-Ext.Component-itemCls">itemCls</a></b> : String<div class="mdesc"><div class="short">An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, itemCls at th...</div><div class="long"><p>An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, <tt>itemCls</tt> at the <b>field</b> level will override
the default <tt>itemCls</tt> supplied at the <b>container</b> level. The value specified for 
<tt>itemCls</tt> will be added to the default class (<tt>'x-form-item'</tt>).</p>
<p>Since it is applied to the item wrapper (see
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>), it allows
you to write standard CSS rules that can apply to the field, the label (if specified), or
any other element within the markup for the field.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.</p><br>
Example use:<pre><code><i>// Apply a style to the field<em>'s <b>label</b>:</i>
&lt;style>
    .required .x-form-item-<b>label</b> {font-weight:bold;color:red;}
&lt;/style>

<b>new</b> Ext.FormPanel({
	height: 100,
	renderTo: Ext.getBody(),
	items: [{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Name<em>',
		itemCls: '</em>required<em>' <i>//this <b>label</b> will be styled</i>
	},{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Favorite Color<em>'
	}]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemCls" ext:member="#itemCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemId"></a><b><a href="source/Component.html#cfg-Ext.Component-itemId">itemId</a></b> : String<div class="mdesc"><div class="short">An itemId can be used as an alternative way to get a reference to a component
when no object reference is available. ...</div><div class="long"><p>An <tt>itemId</tt> can be used as an alternative way to get a reference to a component
when no object reference is available.  Instead of using an <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt> with
<a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>, use <tt>itemId</tt> with
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a> which will retrieve
<tt>itemId</tt>'s or <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>'s. Since <tt>itemId</tt>'s are an index to the
container's internal MixedCollection, the <tt>itemId</tt> is scoped locally to the container -- 
avoiding potential conflicts with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a> which requires a <b>unique</b>
<tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<pre><code><b>var</b> c = <b>new</b> Ext.Panel({ <i>//</i>
    <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 300,
    <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a>: document.body,
    <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'auto'</em>,
    <a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">items</a>: [
        {
            itemId: <em>'p1'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 1'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        },
        {
            itemId: <em>'p2'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 2'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        }
    ]
})
p1 = c.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p1'</em>); <i>// not the same as <a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">Ext.getCmp()</a></i>
p2 = p1.<a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p2'</em>); <i>// reference via a sibling</i></code></pre>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemId" ext:member="#itemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-itemSelector"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-itemSelector">itemSelector</a></b> : String<div class="mdesc"><div class="short">A simple CSS selector (e.g. div.some-class or span:first-child) that will be
used to determine what nodes the Ext.Dat...</div><div class="long"><p>A simple CSS selector (e.g. div.some-class or span:first-child) that will be
used to determine what nodes the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-view" ext:member="view" ext:cls="Ext.form.ComboBox">Ext.DataView</a> which handles the dropdown
display will be working with.</p>
<p><b>Note</b>: this setting is <b>required</b> if a custom XTemplate has been
specified in <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-tpl" ext:member="tpl" ext:cls="Ext.form.ComboBox">tpl</a> which assigns a class other than <pre>'x-combo-list-item'</pre>
to dropdown list items</b></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelSeparator"></a><b><a href="source/Component.html#cfg-Ext.Component-labelSeparator">labelSeparator</a></b> : String<div class="mdesc"><div class="short">The separator to display after the text of each
fieldLabel.  This property may be configured at various levels.
The o...</div><div class="long"><p>The separator to display after the text of each
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.  This property may be configured at various levels.
The order of precedence is:
<div class="mdetail-params"><ul>
<li>field / component level</li>
<li>container level</li>
<li><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">layout level</a> (defaults to colon <tt>':'</tt>)</li>
</ul></div>
To display no separator for this field's label specify empty string ''.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    layoutConfig: {
        labelSeparator: <em>'~'</em>   <i>// layout config has lowest priority (defaults to <em>':'</em>)</i>
    },
    <a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">labelSeparator</a>: <em>'>>'</em>,     <i>// config at container level </i>
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 1'</em>,
        labelSeparator: <em>'...'</em> <i>// field/component level config supersedes others</i>
    },{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 2'</em> <i>// labelSeparator will be <em>'='</em></i>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelSeparator" ext:member="#labelSeparator" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelStyle"></a><b><a href="source/Component.html#cfg-Ext.Component-labelStyle">labelStyle</a></b> : String<div class="mdesc"><div class="short">A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle val...</div><div class="long"><p>A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle value if set (eg,
<tt><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelStyle" ext:member="labelStyle" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.labelStyle</a></tt> , or '').</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>,
        labelStyle: <em>'font-weight:bold;'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelStyle" ext:member="#labelStyle" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-lazyInit"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-lazyInit">lazyInit</a></b> : Boolean<div class="mdesc"><tt>true</tt> to not initialize the list for this combo until the field is focused
(defaults to <tt>true</tt>)</div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-lazyRender"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-lazyRender">lazyRender</a></b> : Boolean<div class="mdesc"><div class="short">true to prevent the ComboBox from rendering until requested
(should always be used when rendering into an Ext.Editor ...</div><div class="long"><tt>true</tt> to prevent the ComboBox from rendering until requested
(should always be used when rendering into an <a href="output/Ext.Editor.html" ext:cls="Ext.Editor">Ext.Editor</a> (eg. <a href="output/Ext.grid.EditorGridPanel.html" ext:cls="Ext.grid.EditorGridPanel">Grids</a>),
defaults to <tt>false</tt>).</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-listAlign"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-listAlign">listAlign</a></b> : String<div class="mdesc"><div class="short">A valid anchor position value. See Ext.Element.alignTo for details
on supported anchor positions (defaults to 'tl-bl?...</div><div class="long">A valid anchor position value. See <tt><a href="output/Ext.Element.html#Ext.Element-alignTo" ext:member="alignTo" ext:cls="Ext.Element">Ext.Element.alignTo</a></tt> for details
on supported anchor positions (defaults to <tt>'tl-bl?'</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-listClass"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-listClass">listClass</a></b> : String<div class="mdesc">The CSS class to add to the predefined <tt>'x-combo-list'</tt> class
applied the dropdown list element (defaults to '').</div></td><td class="msource">ComboBox</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-listEmptyText"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-listEmptyText">listEmptyText</a></b> : String<div class="mdesc">The empty text to display in the data view if no items are found.
(defaults to '')</div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-listWidth"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-listWidth">listWidth</a></b> : Number<div class="mdesc"><div class="short">The width (used as a parameter to Ext.Element.setWidth) of the dropdown
list (defaults to the width of the ComboBox f...</div><div class="long">The width (used as a parameter to <a href="output/Ext.Element.html#Ext.Element-setWidth" ext:member="setWidth" ext:cls="Ext.Element">Ext.Element.setWidth</a>) of the dropdown
list (defaults to the width of the ComboBox field).  See also <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-minListWidth" ext:member="minListWidth" ext:cls="Ext.form.ComboBox">minListWidth</a></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-loadingText"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-loadingText">loadingText</a></b> : String<div class="mdesc"><div class="short">The text to display in the dropdown list while data is loading.  Only applies
when mode = 'remote' (defaults to 'Load...</div><div class="long">The text to display in the dropdown list while data is loading.  Only applies
when <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'remote'</tt> (defaults to <tt>'Loading...'</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-maskRe"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-maskRe">maskRe</a></b> : RegExp<div class="mdesc">An input mask regular expression that will be used to filter keystrokes that do
not match (defaults to <tt>null</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#maskRe" ext:member="#maskRe" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-maxHeight"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-maxHeight">maxHeight</a></b> : Number<div class="mdesc">The maximum height in pixels of the dropdown list before scrollbars are shown
(defaults to <tt>300</tt>)</div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-maxLength"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-maxLength">maxLength</a></b> : Number<div class="mdesc"><div class="short">Maximum input field length allowed by validation (defaults to Number.MAX_VALUE).
This behavior is intended to provide...</div><div class="long">Maximum input field length allowed by validation (defaults to Number.MAX_VALUE).
This behavior is intended to provide instant feedback to the user by improving usability to allow pasting
and editing or overtyping and back tracking. To restrict the maximum number of characters that can be
entered into the field use <tt><b><a href="output/Ext.form.Field.html#Ext.form.Field-autoCreate" ext:member="autoCreate" ext:cls="Ext.form.Field">autoCreate</a></b></tt> to add
any attributes you want to a field, for example:<pre><code><b>var</b> myField = <b>new</b> Ext.form.NumberField({
    id: <em>'mobile'</em>,
    anchor:<em>'90%'</em>,
    fieldLabel: <em>'Mobile'</em>,
    maxLength: 16, <i>// <b>for</b> validation</i>
    autoCreate: {tag: <em>"input"</em>, type: <em>"text"</em>, size: <em>"20"</em>, autocomplete: <em>"off"</em>, maxlength: <em>"10"</em>}
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#maxLength" ext:member="#maxLength" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-maxLengthText"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-maxLengthText">maxLengthText</a></b> : String<div class="mdesc"><div class="short">Error text to display if the maximum length
validation fails (defaults to "The maximum length for this field is {maxL...</div><div class="long">Error text to display if the <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-maxLength" ext:member="maxLength" ext:cls="Ext.form.TextField">maximum length</a></tt></b>
validation fails (defaults to <tt>"The maximum length for this field is {maxLength}"</tt>)</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#maxLengthText" ext:member="#maxLengthText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-minChars"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-minChars">minChars</a></b> : Number<div class="mdesc"><div class="short">The minimum number of characters the user must type before autocomplete and
typeAhead activate (defaults to 4 if mode...</div><div class="long">The minimum number of characters the user must type before autocomplete and
<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-typeAhead" ext:member="typeAhead" ext:cls="Ext.form.ComboBox">typeAhead</a> activate (defaults to <tt>4</tt> if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'remote'</tt> or <tt>0</tt> if
<tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'local'</tt>, does not apply if
<tt><a href="output/Ext.form.TriggerField.html#Ext.form.TriggerField-editable" ext:member="editable" ext:cls="Ext.form.TriggerField">editable</a> = false</tt>).</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-minHeight"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-minHeight">minHeight</a></b> : Number<div class="mdesc"><div class="short">The minimum height in pixels of the dropdown list when the list is constrained by its
distance to the viewport edges ...</div><div class="long">The minimum height in pixels of the dropdown list when the list is constrained by its
distance to the viewport edges (defaults to <tt>90</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-minLength"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-minLength">minLength</a></b> : Number<div class="mdesc">Minimum input field length required (defaults to <tt>0</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#minLength" ext:member="#minLength" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-minLengthText"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-minLengthText">minLengthText</a></b> : String<div class="mdesc"><div class="short">Error text to display if the minimum length
validation fails (defaults to "The minimum length for this field is {minL...</div><div class="long">Error text to display if the <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-minLength" ext:member="minLength" ext:cls="Ext.form.TextField">minimum length</a></tt></b>
validation fails (defaults to <tt>"The minimum length for this field is {minLength}"</tt>)</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#minLengthText" ext:member="#minLengthText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-minListWidth"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-minListWidth">minListWidth</a></b> : Number<div class="mdesc">The minimum width of the dropdown list in pixels (defaults to <tt>70</tt>, will
be ignored if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-listWidth" ext:member="listWidth" ext:cls="Ext.form.ComboBox">listWidth</a></tt> has a higher value)</div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-mode"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-mode">mode</a></b> : String<div class="mdesc"><div class="short">Acceptable values are:
&lt;div class="mdetail-params"&gt;
'remote' : Default
&lt;p class="sub-desc"&gt;Automatically loads the st...</div><div class="long">Acceptable values are:
<div class="mdetail-params"><ul>
<li><b><tt>'remote'</tt></b> : <b>Default</b>
<p class="sub-desc">Automatically loads the <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-store" ext:member="store" ext:cls="Ext.form.ComboBox">store</a></tt> the <b>first</b> time the trigger
is clicked. If you do not want the store to be automatically loaded the first time the trigger is
clicked, set to <tt>'local'</tt> and manually load the store.  To force a requery of the store
<b>every</b> time the trigger is clicked see <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-lastQuery" ext:member="lastQuery" ext:cls="Ext.form.ComboBox">lastQuery</a></tt>.</p></li>
<li><b><tt>'local'</tt></b> :
<p class="sub-desc">ComboBox loads local data</p>
<pre><code><b>var</b> combo = <b>new</b> Ext.form.ComboBox({
    renderTo: document.body,
    mode: <em>'local'</em>,
    store: <b>new</b> Ext.data.ArrayStore({
        id: 0,
        fields: [
            <em>'myId'</em>,  <i>// numeric value is the key</i>
            <em>'displayText'</em>
        ],
        data: [[1, <em>'item1'</em>], [2, <em>'item2'</em>]]  <i>// data is local</i>
    }),
    valueField: <em>'myId'</em>,
    displayField: <em>'displayText'</em>,
    triggerAction: <em>'all'</em>
});</code></pre></li>
</ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-msgFx"></a><b><a href="source/Field.html#cfg-Ext.form.Field-msgFx">msgFx</a></b> : String<div class="mdesc"><b>Experimental</b> The effect used when displaying a validation message under the field
(defaults to 'normal').</div></td><td class="msource"><a href="output/Ext.form.Field.html#msgFx" ext:member="#msgFx" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-msgTarget"></a><b><a href="source/Field.html#cfg-Ext.form.Field-msgTarget">msgTarget</a></b> : String<div class="mdesc"><div class="short">The location where error text should display.  Should be one of the following values
(defaults to 'qtip'):

Value    ...</div><div class="long">The location where error text should display.  Should be one of the following values
(defaults to 'qtip'):
<pre>
Value         Description
-----------   ----------------------------------------------------------------------
qtip          Display a quick tip when the user hovers over the field
title         Display a default browser title attribute popup
under         Add a block div beneath the field containing the error text
side          Add an error icon to the right of the field with a popup on hover
[element id]  Add the error text directly to the innerHTML of the specified element
</pre></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#msgTarget" ext:member="#msgTarget" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-name"></a><b><a href="source/Field.html#cfg-Ext.form.Field-name">name</a></b> : String<div class="mdesc"><div class="short">The field's HTML name attribute (defaults to "").
Note: this property must be set if this field is to be automaticall...</div><div class="long">The field's HTML name attribute (defaults to "").
<b>Note</b>: this property must be set if this field is to be automatically included with
<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-submit" ext:member="submit" ext:cls="Ext.form.BasicForm">form submit()</a>.</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#name" ext:member="#name" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-overCls"></a><b><a href="source/Component.html#cfg-Ext.Component-overCls">overCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and...</div><div class="long">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and removed when the mouse moves out. (defaults to '').  This can be
useful for adding customized "active" or "hover" styles to the component or any of its children using standard CSS rules.</div></div></td><td class="msource"><a href="output/Ext.Component.html#overCls" ext:member="#overCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-pageSize"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-pageSize">pageSize</a></b> : Number<div class="mdesc"><div class="short">If greater than 0, a Ext.PagingToolbar is displayed in the
footer of the dropdown list and the filter queries will ex...</div><div class="long">If greater than <tt>0</tt>, a <a href="output/Ext.PagingToolbar.html" ext:cls="Ext.PagingToolbar">Ext.PagingToolbar</a> is displayed in the
footer of the dropdown list and the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-doQuery" ext:member="doQuery" ext:cls="Ext.form.ComboBox">filter queries</a> will execute with page start and
<a href="output/Ext.PagingToolbar.html#Ext.PagingToolbar-pageSize" ext:member="pageSize" ext:cls="Ext.PagingToolbar">limit</a> parameters. Only applies when <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'remote'</tt>
(defaults to <tt>0</tt>).</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageX"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageX">pageX</a></b> : Number<div class="mdesc">The page level x coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageX" ext:member="#pageX" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageY"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageY">pageY</a></b> : Number<div class="mdesc">The page level y coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageY" ext:member="#pageY" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-plugins"></a><b><a href="source/Component.html#cfg-Ext.Component-plugins">plugins</a></b> : Object/Array<div class="mdesc"><div class="short">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a ...</div><div class="long">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a valid plugin is that it contain an init method that accepts a reference of type Ext.Component.
When a component is created, if any plugins are available, the component will call the init method on each
plugin, passing a reference to itself.  Each plugin can then call methods or respond to events on the
component as needed to provide its functionality.</div></div></td><td class="msource"><a href="output/Ext.Component.html#plugins" ext:member="#plugins" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ptype"></a><b><a href="source/Component.html#cfg-Ext.Component-ptype">ptype</a></b> : String<div class="mdesc"><div class="short">The registered ptype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>ptype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a Plugin is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>ptype</tt> will be looked up at render time up to determine what
type of Plugin to create.<br><br>
If you create your own Plugins, you may register them using
<a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerPlugin" ext:member="registerPlugin" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerPlugin</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#ptype" ext:member="#ptype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-queryDelay"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-queryDelay">queryDelay</a></b> : Number<div class="mdesc"><div class="short">The length of time in milliseconds to delay between the start of typing and
sending the query to filter the dropdown ...</div><div class="long">The length of time in milliseconds to delay between the start of typing and
sending the query to filter the dropdown list (defaults to <tt>500</tt> if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'remote'</tt>
or <tt>10</tt> if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'local'</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-queryParam"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-queryParam">queryParam</a></b> : String<div class="mdesc">Name of the query (<a href="output/Ext.data.Store.html#Ext.data.Store-baseParam" ext:member="baseParam" ext:cls="Ext.data.Store">baseParam</a> name for the store)
as it will be passed on the querystring (defaults to <tt>'query'</tt>)</div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-readOnly"></a><b><a href="source/Field.html#cfg-Ext.form.Field-readOnly">readOnly</a></b> : Boolean<div class="mdesc"><div class="short">True to mark the field as readOnly in HTML (defaults to false) -- Note: this only
sets the element's readOnly DOM att...</div><div class="long">True to mark the field as readOnly in HTML (defaults to false) -- Note: this only
sets the element's readOnly DOM attribute.</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#readOnly" ext:member="#readOnly" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ref"></a><b><a href="source/Component.html#cfg-Ext.Component-ref">ref</a></b> : String<div class="mdesc"><div class="short">A path specification, relative to the Component's ownerCt specifying into which
ancestor Container to place a named r...</div><div class="long"><p>A path specification, relative to the Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a> specifying into which
ancestor Container to place a named reference to this Component.</p>
<p>The ancestor axis can be traversed by using '/' characters in the path.
For example, to put a reference to a Toolbar Button into <i>the Panel which owns the Toolbar</i>:</p><pre><code><b>var</b> myGrid = <b>new</b> Ext.grid.EditorGridPanel({
    title: <em>'My EditorGridPanel'</em>,
    store: myStore,
    colModel: myColModel,
    tbar: [{
        text: <em>'Save'</em>,
        handler: saveChanges,
        disabled: true,
        ref: <em>'../saveButton'</em>
    }],
    listeners: {
        afteredit: <b>function</b>() {
<i>//          The button reference is <b>in</b> the GridPanel</i>
            myGrid.saveButton.enable();
        }
    }
});</code></pre>
<p>In the code above, if the ref had been <code><em>'saveButton'</em></code> the reference would
have been placed into the Toolbar. Each '/' in the ref moves up one level from the
Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ref" ext:member="#ref" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-regex"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-regex">regex</a></b> : RegExp<div class="mdesc"><div class="short">A JavaScript RegExp object to be tested against the field value during validation
(defaults to null). If available, t...</div><div class="long">A JavaScript RegExp object to be tested against the field value during validation
(defaults to <tt>null</tt>). If available, this regex will be evaluated only after the basic validators
all <tt>return true</tt>, and will be passed the current field value.  If the test fails, the field will
be marked invalid using <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-regexText" ext:member="regexText" ext:cls="Ext.form.TextField">regexText</a></tt></b>.</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#regex" ext:member="#regex" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-regexText"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-regexText">regexText</a></b> : String<div class="mdesc">The error text to display if <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-regex" ext:member="regex" ext:cls="Ext.form.TextField">regex</a></tt></b> is used and the
test fails during validation (defaults to <tt>""</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#regexText" ext:member="#regexText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-region"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-region">region</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the...</div><div class="long"><p><b>Note</b>: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a></b>
layout manager (eg. specifying <tt>layout:'border'</tt>).</p><br>
<p>See <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">Ext.layout.BorderLayout</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#region" ext:member="#region" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-renderTo"></a><b><a href="source/Component.html#cfg-Ext.Component-renderTo">renderTo</a></b> : Mixed<div class="mdesc"><div class="short">Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.
Notes ...</div><div class="long"><p>Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.</p><div><ul>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When using this config, a call to render() is not required.</div>
<div class="sub-desc">Do <u>not</u> use this option if the Component is to be a child item of
a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>. It is the responsibility of the
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>'s <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout manager</a>
to render its child items.</div>
</ul></li>
</ul></div>
<p>See <tt><a href="output/Ext.Component.html#Ext.Component-render" ext:member="render" ext:cls="Ext.Component">render</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#renderTo" ext:member="#renderTo" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-resizable"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-resizable">resizable</a></b> : Boolean<div class="mdesc"><div class="short">true to add a resize handle to the bottom of the dropdown list
(creates an Ext.Resizable with 'se' pinned handles).
D...</div><div class="long"><tt>true</tt> to add a resize handle to the bottom of the dropdown list
(creates an <a href="output/Ext.Resizable.html" ext:cls="Ext.Resizable">Ext.Resizable</a> with 'se' <a href="output/Ext.Resizable.html#Ext.Resizable-pinned" ext:member="pinned" ext:cls="Ext.Resizable">pinned</a> handles).
Defaults to <tt>false</tt>.</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-selectOnFocus"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-selectOnFocus">selectOnFocus</a></b> : Boolean<div class="mdesc"><div class="short">true to select any existing text in the field immediately on focus.
Only applies when editable = true (defaults to
fa...</div><div class="long"><tt>true</tt> to select any existing text in the field immediately on focus.
Only applies when <tt><a href="output/Ext.form.TriggerField.html#Ext.form.TriggerField-editable" ext:member="editable" ext:cls="Ext.form.TriggerField">editable</a> = true</tt> (defaults to
<tt>false</tt>).</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-selectedClass"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-selectedClass">selectedClass</a></b> : String<div class="mdesc">CSS class to apply to the selected item in the dropdown list
(defaults to <tt>'x-combo-selected'</tt>)</div></td><td class="msource">ComboBox</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-shadow"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-shadow">shadow</a></b> : Boolean/String<div class="mdesc"><tt>true</tt> or <tt>"sides"</tt> for the default effect, <tt>"frame"</tt> for
4-way shadow, and <tt>"drop"</tt> for bottom-right</div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateId"></a><b><a href="source/Component.html#cfg-Ext.Component-stateId">stateId</a></b> : String<div class="mdesc"><div class="short">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, o...</div><div class="long">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, otherwise null if the component is using a generated id).
<p>See <a href="output/Ext.Component.html#Ext.Component-stateful" ext:member="stateful" ext:cls="Ext.Component">stateful</a> for an explanation of saving and restoring Component state.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateId" ext:member="#stateId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateful"></a><b><a href="source/Component.html#cfg-Ext.Component-stateful">stateful</a></b> : Boolean<div class="mdesc"><div class="short">A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startu...</div><div class="long"><p>A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startup. The component must have either a <a href="output/Ext.Component.html#Ext.Component-stateId" ext:member="stateId" ext:cls="Ext.Component">stateId</a> or <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>
assigned for state to be managed.  Auto-generated ids are not guaranteed to be stable across page
loads and cannot be relied upon to save and restore the same state for a component.<p>
<p>For state saving to work, the state manager's provider must have been set to an implementation
of <a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> which overrides the <a href="output/Ext.state.Provider.html#Ext.state.Provider-set" ext:member="set" ext:cls="Ext.state.Provider">set</a>
and <a href="output/Ext.state.Provider.html#Ext.state.Provider-get" ext:member="get" ext:cls="Ext.state.Provider">get</a> methods to save and recall name/value pairs.
A built-in implementation, <a href="output/Ext.state.CookieProvider.html" ext:cls="Ext.state.CookieProvider">Ext.state.CookieProvider</a> is available.</p>
<p>To set the state provider for the current page:</p>
<pre><code>Ext.state.Manager.setProvider(<b>new</b> Ext.state.CookieProvider());</code></pre>
<p>A stateful Component attempts to save state when one of the events listed in the <a href="output/Ext.Component.html#Ext.Component-stateEvents" ext:member="stateEvents" ext:cls="Ext.Component">stateEvents</a>
configuration fires.</p>
To save state, A stateful Component first serializes its state by calling <b><tt>getState</tt></b>. By default,
this function does nothing. The developer must provide an implementation which returns an object hash
which represents the Component's restorable state.</p>
<p>The value yielded by getState is passed to <a href="output/Ext.state.Manager.html#Ext.state.Manager-set" ext:member="set" ext:cls="Ext.state.Manager">Ext.state.Manager.set</a> which uses the configured
<a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> to save the object keyed by the Component's <a href="output/stateId.html" ext:cls="stateId">stateId</a>, or,
if that is not specified, its <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>During construction, a stateful Component attempts to <i>restore</i> its state by calling
<a href="output/Ext.state.Manager.html#Ext.state.Manager-get" ext:member="get" ext:cls="Ext.state.Manager">Ext.state.Manager.get</a> passing the (@link #stateId}, or, if that is not specified, the <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>The resulting object is passed to <b><tt>applyState</tt></b>. The default implementation of applyState
simply copies properties into the object, but a developer may override this to support more behaviour.</p>
<p>You can perform extra processing on state save and restore by attaching handlers to the
<a href="output/Ext.Component.html#Ext.Component-beforestaterestore" ext:member="beforestaterestore" ext:cls="Ext.Component">beforestaterestore</a>, <a href="output/Ext.Component.html#Ext.Component-staterestore" ext:member="staterestore" ext:cls="Ext.Component">staterestore</a>, <a href="output/Ext.Component.html#Ext.Component-beforestatesave" ext:member="beforestatesave" ext:cls="Ext.Component">beforestatesave</a> and <a href="output/Ext.Component.html#Ext.Component-statesave" ext:member="statesave" ext:cls="Ext.Component">statesave</a> events</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateful" ext:member="#stateful" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-store"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-store">store</a></b> : Ext.data.Store/Array<div class="mdesc"><div class="short">The data source to which this combo is bound (defaults to undefined).
Acceptable values for this property are:
&lt;div c...</div><div class="long">The data source to which this combo is bound (defaults to <tt>undefined</tt>).
Acceptable values for this property are:
<div class="mdetail-params"><ul>
<li><b>any <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Store</a> subclass</b></li>
<li><b>an Array</b> : Arrays will be converted to a <a href="output/Ext.data.ArrayStore.html" ext:cls="Ext.data.ArrayStore">Ext.data.ArrayStore</a> internally.
<div class="mdetail-params"><ul>
<li><b>1-dimensional array</b> : (e.g., <tt>['Foo','Bar']</tt>)<div class="sub-desc">
A 1-dimensional array will automatically be expanded (each array item will be the combo
<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">value</a> and <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-displayField" ext:member="displayField" ext:cls="Ext.form.ComboBox">text</a>)</div></li>
<li><b>2-dimensional array</b> : (e.g., <tt>[['f','Foo'],['b','Bar']]</tt>)<div class="sub-desc">
For a multi-dimensional array, the value in index 0 of each item will be assumed to be the combo
<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-valueField" ext:member="valueField" ext:cls="Ext.form.ComboBox">value</a>, while the value at index 1 is assumed to be the combo <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-displayField" ext:member="displayField" ext:cls="Ext.form.ComboBox">text</a>.
</div></li></ul></div></li></ul></div>
<p>See also <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a></tt>.</p></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-stripCharsRe"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-stripCharsRe">stripCharsRe</a></b> : RegExp<div class="mdesc">A JavaScript RegExp object used to strip unwanted content from the value
before validation (defaults to <tt>null</tt>).</div></td><td class="msource"><a href="output/Ext.form.TextField.html#stripCharsRe" ext:member="#stripCharsRe" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-style"></a><b><a href="source/Component.html#cfg-Ext.Component-style">style</a></b> : String<div class="mdesc"><div class="short">A custom style specification to be applied to this component's Element.  Should be a valid argument to
Ext.Element.ap...</div><div class="long">A custom style specification to be applied to this component's Element.  Should be a valid argument to
<a href="output/Ext.Element.html#Ext.Element-applyStyles" ext:member="applyStyles" ext:cls="Ext.Element">Ext.Element.applyStyles</a>.
<pre><code><b>new</b> Ext.Panel({
    title: <em>'Some Title'</em>,
    renderTo: Ext.getBody(),
    width: 400, height: 300,
    layout: <em>'form'</em>,
    items: [{
        xtype: <em>'textarea'</em>,
        style: {
            width: <em>'95%'</em>,
            marginBottom: <em>'10px'</em>
        }
    },
        <b>new</b> Ext.Button({
            text: <em>'Send'</em>,
            minWidth: <em>'100'</em>,
            style: {
                marginBottom: <em>'10px'</em>
            }
        })
    ]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#style" ext:member="#style" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-tabIndex"></a><b><a href="source/Field.html#cfg-Ext.form.Field-tabIndex">tabIndex</a></b> : Number<div class="mdesc"><div class="short">The tabIndex for this field. Note this only applies to fields that are rendered,
not those which are built via applyT...</div><div class="long">The tabIndex for this field. Note this only applies to fields that are rendered,
not those which are built via applyTo (defaults to undefined).</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#tabIndex" ext:member="#tabIndex" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-title"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-title">title</a></b> : String<div class="mdesc"><div class="short">If supplied, a header element is created containing this text and added into the top of
the dropdown list (defaults t...</div><div class="long">If supplied, a header element is created containing this text and added into the top of
the dropdown list (defaults to undefined, with no header element)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-tpl"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-tpl">tpl</a></b> : String/Ext.XTemplate<div class="mdesc"><div class="short">The template string, or Ext.XTemplate instance to
use to display each item in the dropdown list. The dropdown list is...</div><div class="long"><p>The template string, or <a href="output/Ext.XTemplate.html" ext:cls="Ext.XTemplate">Ext.XTemplate</a> instance to
use to display each item in the dropdown list. The dropdown list is displayed in a
DataView. See <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-view" ext:member="view" ext:cls="Ext.form.ComboBox">view</a>.</p>
<p>The default template string is:</p><pre><code><em>'&lt;tpl <b>for</b>=<em>"."</em>>&lt;div class=<em>"x-combo-list-item"</em>>{'</em> + this.displayField + <em>'}&lt;/div>&lt;/tpl>'</em></code></pre>
<p>Override the default value to create custom UI layouts for items in the list.
For example:</p><pre><code><em>'&lt;tpl <b>for</b>=<em>"."</em>>&lt;div ext:qtip=<em>"{state}. {nick}"</em> class=<em>"x-combo-list-item"</em>>{state}&lt;/div>&lt;/tpl>'</em></code></pre>
<p>The template <b>must</b> contain one or more substitution parameters using field
names from the Combo's</b> <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-store" ext:member="store" ext:cls="Ext.form.ComboBox">Store</a>. In the example above an
<pre>ext:qtip</pre> attribute is added to display other fields from the Store.</p>
<p>To preserve the default visual look of list items, add the CSS class name
<pre>x-combo-list-item</pre> to the template's container element.</p>
<p>Also see <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-itemSelector" ext:member="itemSelector" ext:cls="Ext.form.ComboBox">itemSelector</a> for additional details.</p></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-transform"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-transform">transform</a></b> : Mixed<div class="mdesc"><div class="short">The id, DOM node or element of an existing HTML SELECT to convert to a ComboBox.
Note that if you specify this and th...</div><div class="long">The id, DOM node or element of an existing HTML SELECT to convert to a ComboBox.
Note that if you specify this and the combo is going to be in an <a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">Ext.form.BasicForm</a> or
<a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a>, you must also set <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-lazyRender" ext:member="lazyRender" ext:cls="Ext.form.ComboBox">lazyRender</a> = true</tt>.</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-triggerAction"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-triggerAction">triggerAction</a></b> : String<div class="mdesc"><div class="short">The action to execute when the trigger is clicked.
&lt;div class="mdetail-params"&gt;
'query' : Default
&lt;p class="sub-desc"...</div><div class="long">The action to execute when the trigger is clicked.
<div class="mdetail-params"><ul>
<li><b><tt>'query'</tt></b> : <b>Default</b>
<p class="sub-desc"><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-doQuery" ext:member="doQuery" ext:cls="Ext.form.ComboBox">run the query</a> using the <a href="output/Ext.form.Field.html#Ext.form.Field-getRawValue" ext:member="getRawValue" ext:cls="Ext.form.Field">raw value</a>.</p></li>
<li><b><tt>'all'</tt></b> :
<p class="sub-desc"><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-doQuery" ext:member="doQuery" ext:cls="Ext.form.ComboBox">run the query</a> specified by the <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-allQuery" ext:member="allQuery" ext:cls="Ext.form.ComboBox">allQuery</a></tt> config option</p></li>
</ul></div>
<p>See also <code><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-queryParam" ext:member="queryParam" ext:cls="Ext.form.ComboBox">queryParam</a></code>.</p></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-triggerClass"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-triggerClass">triggerClass</a></b> : String<div class="mdesc"><div class="short">An additional CSS class used to style the trigger button.  The trigger will always
get the class 'x-form-trigger' and...</div><div class="long">An additional CSS class used to style the trigger button.  The trigger will always
get the class <tt>'x-form-trigger'</tt> and <tt>triggerClass</tt> will be <b>appended</b> if specified
(defaults to <tt>'x-form-arrow-trigger'</tt> which displays a downward arrow icon).</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TriggerField-triggerConfig"></a><b><a href="source/TriggerField.html#cfg-Ext.form.TriggerField-triggerConfig">triggerConfig</a></b> : Mixed<div class="mdesc"><div class="short">A DomHelper config object specifying the structure of the
trigger element for this Field. (Optional).
Specify this wh...</div><div class="long"><p>A <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> config object specifying the structure of the
trigger element for this Field. (Optional).</p>
<p>Specify this when you need a customized element to act as the trigger button for a TriggerField.</p>
<p>Note that when using this option, it is the developer's responsibility to ensure correct sizing, positioning
and appearance of the trigger.  Defaults to:</p>
<pre><code>{tag: <em>"img"</em>, src: Ext.BLANK_IMAGE_URL, cls: <em>"x-form-trigger "</em> + this.triggerClass}</code></pre></div></div></td><td class="msource"><a href="output/Ext.form.TriggerField.html#triggerConfig" ext:member="#triggerConfig" ext:cls="Ext.form.TriggerField">TriggerField</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-typeAhead"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-typeAhead">typeAhead</a></b> : Boolean<div class="mdesc"><div class="short">true to populate and autoselect the remainder of the text being
typed after a configurable delay (typeAheadDelay) if ...</div><div class="long"><tt>true</tt> to populate and autoselect the remainder of the text being
typed after a configurable delay (<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-typeAheadDelay" ext:member="typeAheadDelay" ext:cls="Ext.form.ComboBox">typeAheadDelay</a>) if it matches a known value (defaults
to <tt>false</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-typeAheadDelay"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-typeAheadDelay">typeAheadDelay</a></b> : Number<div class="mdesc"><div class="short">The length of time in milliseconds to wait until the typeahead text is displayed
if typeAhead = true (defaults to 250...</div><div class="long">The length of time in milliseconds to wait until the typeahead text is displayed
if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-typeAhead" ext:member="typeAhead" ext:cls="Ext.form.ComboBox">typeAhead</a> = true</tt> (defaults to <tt>250</tt>)</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-validateOnBlur"></a><b><a href="source/Field.html#cfg-Ext.form.Field-validateOnBlur">validateOnBlur</a></b> : Boolean<div class="mdesc">Whether the field should validate when it loses focus (defaults to true).</div></td><td class="msource"><a href="output/Ext.form.Field.html#validateOnBlur" ext:member="#validateOnBlur" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-validationDelay"></a><b><a href="source/Field.html#cfg-Ext.form.Field-validationDelay">validationDelay</a></b> : Number<div class="mdesc">The length of time in milliseconds after user input begins until validation
is initiated (defaults to 250)</div></td><td class="msource"><a href="output/Ext.form.Field.html#validationDelay" ext:member="#validationDelay" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-validationEvent"></a><b><a href="source/Field.html#cfg-Ext.form.Field-validationEvent">validationEvent</a></b> : String/Boolean<div class="mdesc"><div class="short">The event that should initiate field validation. Set to false to disable
      automatic validation (defaults to "key...</div><div class="long">The event that should initiate field validation. Set to false to disable
      automatic validation (defaults to "keyup").</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#validationEvent" ext:member="#validationEvent" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-validator"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-validator">validator</a></b> : Function<div class="mdesc"><div class="short">A custom validation function to be called during field validation
(defaults to null). If specified, this function wil...</div><div class="long">A custom validation function to be called during field validation
(defaults to <tt>null</tt>). If specified, this function will be called <b>only after</b> the built-in
validations (<a href="output/Ext.form.TextField.html#Ext.form.TextField-allowBlank" ext:member="allowBlank" ext:cls="Ext.form.TextField">allowBlank</a>, <a href="output/Ext.form.TextField.html#Ext.form.TextField-minLength" ext:member="minLength" ext:cls="Ext.form.TextField">minLength</a>, <a href="output/Ext.form.TextField.html#Ext.form.TextField-maxLength" ext:member="maxLength" ext:cls="Ext.form.TextField">maxLength</a>) and any configured <a href="output/Ext.form.TextField.html#Ext.form.TextField-vtype" ext:member="vtype" ext:cls="Ext.form.TextField">vtype</a>
all <tt>return true</tt>. This function will be passed the current field value and expected to return
boolean <tt>true</tt> if the value is valid or a string error message if invalid.</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#validator" ext:member="#validator" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-value"></a><b><a href="source/Field.html#cfg-Ext.form.Field-value">value</a></b> : Mixed<div class="mdesc">A value to initialize this field with (defaults to undefined).</div></td><td class="msource"><a href="output/Ext.form.Field.html#value" ext:member="#value" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-valueField"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-valueField">valueField</a></b> : String<div class="mdesc"><div class="short">The underlying data value name to bind to this
ComboBox (defaults to undefined if mode = 'remote' or 'value' if
trans...</div><div class="long">The underlying <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">data value name</a> to bind to this
ComboBox (defaults to undefined if <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-mode" ext:member="mode" ext:cls="Ext.form.ComboBox">mode</a> = 'remote'</tt> or <tt>'value'</tt> if
<a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-transform" ext:member="transform" ext:cls="Ext.form.ComboBox">transforming a select</a>).
<p><b>Note</b>: use of a <tt>valueField</tt> requires the user to make a selection in order for a value to be
mapped.  See also <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a></tt>, <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenValue" ext:member="hiddenValue" ext:cls="Ext.form.ComboBox">hiddenValue</a></tt>, and <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-displayField" ext:member="displayField" ext:cls="Ext.form.ComboBox">displayField</a></tt>.</p></div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-valueNotFoundText"></a><b><a href="source/Combo.html#cfg-Ext.form.ComboBox-valueNotFoundText">valueNotFoundText</a></b> : String<div class="mdesc"><div class="short">When using a name/value combo, if the value passed to setValue is not found in
the store, valueNotFoundText will be d...</div><div class="long">When using a name/value combo, if the value passed to setValue is not found in
the store, valueNotFoundText will be displayed as the field text if defined (defaults to undefined). If this
default text is used, it means there is no value set and no validation will occur on this field.</div></div></td><td class="msource">ComboBox</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-vtype"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-vtype">vtype</a></b> : String<div class="mdesc">A validation type name as defined in <a href="output/Ext.form.VTypes.html" ext:cls="Ext.form.VTypes">Ext.form.VTypes</a> (defaults to <tt>null</tt>)</div></td><td class="msource"><a href="output/Ext.form.TextField.html#vtype" ext:member="#vtype" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-vtypeText"></a><b><a href="source/TextField.html#cfg-Ext.form.TextField-vtypeText">vtypeText</a></b> : String<div class="mdesc"><div class="short">A custom error message to display in place of the default message provided
for the vtype currently set for this field...</div><div class="long">A custom error message to display in place of the default message provided
for the <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-vtype" ext:member="vtype" ext:cls="Ext.form.TextField">vtype</a></tt></b> currently set for this field (defaults to <tt>''</tt>).  <b>Note</b>:
only applies if <b><tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-vtype" ext:member="vtype" ext:cls="Ext.form.TextField">vtype</a></tt></b> is set, else ignored.</div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#vtypeText" ext:member="#vtypeText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-width"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-width">width</a></b> : Number<div class="mdesc">The width of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#width" ext:member="#width" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-x"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-x">x</a></b> : Number<div class="mdesc">The local x (left) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#x" ext:member="#x" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-xtype"></a><b><a href="source/Component.html#cfg-Ext.Component-xtype">xtype</a></b> : String<div class="mdesc"><div class="short">The registered xtype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>xtype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a child item of a Container is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>xtype</tt> will be looked up at render time up to determine what
type of child Component to create.<br><br>
The predefined xtypes are listed <a href="output/Ext.Component.html" ext:cls="Ext.Component">here</a>.
<br><br>
If you subclass Components to create your own Components, you may register
them using <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerType" ext:member="registerType" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerType</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#xtype" ext:member="#xtype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-y"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-y">y</a></b> : Number<div class="mdesc">The local y (top) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#y" ext:member="#y" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr></tbody></table><a id="Ext.form.ComboBox-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disabled"></a><b><a href="source/Component.html#prop-Ext.Component-disabled">disabled</a></b> : Boolean<div class="mdesc">True if this component is disabled. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#disabled" ext:member="#disabled" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-el"></a><b><a href="source/Component.html#prop-Ext.Component-el">el</a></b> : Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will
usually be a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will
<i>usually</i> be a &lt;DIV> element created by the class's onRender method, but
that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<br><p><b>Note</b>: this element will not be available until this Component has been
rendered.</b></p><br>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners
for this Component's own Observable events), perform the adding of the listener in a
render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        }
    }
});</code></pre>
<p>See also <tt><a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">getEl</a></p></div></div></td><td class="msource"><a href="output/Ext.Component.html#el" ext:member="#el" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#prop-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">True if this component is hidden. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-initialConfig"></a><b><a href="source/Component.html#prop-Ext.Component-initialConfig">initialConfig</a></b> : Object<div class="mdesc">This Component's initial configuration specification. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#initialConfig" ext:member="#initialConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-lastQuery"></a><b><a href="source/Combo.html#prop-Ext.form.ComboBox-lastQuery">lastQuery</a></b> : String<div class="mdesc"><div class="short">The value of the match string used to filter the store. Delete this property to force a requery.
Example use:
var com...</div><div class="long">The value of the match string used to filter the store. Delete this property to force a requery.
Example use:
<pre><code><b>var</b> combo = <b>new</b> Ext.form.ComboBox({
    ...
    mode: <em>'remote'</em>,
    ...
    listeners: {
        <i>// <b>delete</b> the previous query <b>in</b> the beforequery event or set</i>
        <i>// combo.lastQuery = null (this will reload the store the next time it expands)</i>
        beforequery: <b>function</b>(qe){
            <b>delete</b> qe.combo.lastQuery;
        }
    }
});</code></pre>
To make sure the filter in the store is not cleared the first time the ComboBox trigger is used
configure the combo with <tt>lastQuery=''</tt>. Example use:
<pre><code><b>var</b> combo = <b>new</b> Ext.form.ComboBox({
    ...
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>,
    lastQuery: <em>''</em>
});</code></pre></div></div></td><td class="msource">ComboBox</td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ownerCt"></a><b><a href="source/Component.html#prop-Ext.Component-ownerCt">ownerCt</a></b> : Ext.Container<div class="mdesc"><div class="short">The component's owner Ext.Container (defaults to undefined, and is set automatically when
the component is added to a...</div><div class="long">The component's owner <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (defaults to undefined, and is set automatically when
the component is added to a container).  Read-only.
<p><b>Note</b>: to access items within the container see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ownerCt" ext:member="#ownerCt" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-rendered"></a><b><a href="source/Component.html#prop-Ext.Component-rendered">rendered</a></b> : Boolean<div class="mdesc">True if this component has been rendered. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#rendered" ext:member="#rendered" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-view"></a><b><a href="source/Combo.html#prop-Ext.form.ComboBox-view">view</a></b> : Ext.DataView<div class="mdesc">The <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a> used to display the ComboBox's options.</div></td><td class="msource">ComboBox</td></tr></tbody></table><a id="Ext.form.ComboBox-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-ComboBox"></a><b><a href="source/Combo.html#cls-Ext.form.ComboBox">ComboBox</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new ComboBox.</div><div class="long">Create a new ComboBox.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">Configuration options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div><table><tr><td class="label">xtype:</td><td class="hd-info">combo</td></tr></table></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-addClass"></a><b><a href="source/Component.html#method-Ext.Component-addClass">addClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Adds a CSS class to the component's underlying element.</div><div class="long">Adds a CSS class to the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to add</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#addClass" ext:member="#addClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-applyToMarkup"></a><b><a href="source/Component.html#method-Ext.Component-applyToMarkup">applyToMarkup</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Apply this component to existing markup that is valid. With this function, no call to render() is required.</div><div class="long">Apply this component to existing markup that is valid. With this function, no call to render() is required.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#applyToMarkup" ext:member="#applyToMarkup" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-clearInvalid"></a><b><a href="source/Field.html#method-Ext.form.Field-clearInvalid">clearInvalid</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clear any invalid styles/messages for this field</div><div class="long">Clear any invalid styles/messages for this field<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#clearInvalid" ext:member="#clearInvalid" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-clearValue"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-clearValue">clearValue</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears any text/value currently set in the field</div><div class="long">Clears any text/value currently set in the field<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-cloneConfig"></a><b><a href="source/Component.html#method-Ext.Component-cloneConfig">cloneConfig</a></b>(&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Clone the current component using the original config values passed into this instance by default.</div><div class="long">Clone the current component using the original config values passed into this instance by default.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>overrides</code> : Object<div class="sub-desc">A new config containing any properties to override in the cloned version.
An id property can be passed on this object, otherwise one will be generated to avoid duplicates.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">clone The cloned copy of this component</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#cloneConfig" ext:member="#cloneConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-collapse"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-collapse">collapse</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides the dropdown list if it is currently expanded. Fires the collapse event on completion.</div><div class="long">Hides the dropdown list if it is currently expanded. Fires the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-collapse" ext:member="collapse" ext:cls="Ext.form.ComboBox">collapse</a> event on completion.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#method-Ext.Component-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the c...</div><div class="long">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the component from its <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (if applicable) and unregistering it from
<a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>.  Destruction is generally handled automatically by the framework and this method
should usually not need to be called directly.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disable"></a><b><a href="source/Component.html#method-Ext.Component-disable">disable</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Disable this component.</div><div class="long">Disable this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#disable" ext:member="#disable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-doQuery"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-doQuery">doQuery</a></b>(&nbsp;<code>String&nbsp;query</code>,&nbsp;<code>Boolean&nbsp;forceAll</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Execute a query to filter the dropdown list.  Fires the beforequery event prior to performing the
query allowing the ...</div><div class="long">Execute a query to filter the dropdown list.  Fires the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-beforequery" ext:member="beforequery" ext:cls="Ext.form.ComboBox">beforequery</a> event prior to performing the
query allowing the query action to be canceled if needed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>query</code> : String<div class="sub-desc">The SQL query to execute</div></li><li><code>forceAll</code> : Boolean<div class="sub-desc"><tt>true</tt> to force the query to execute even if there are currently fewer
characters in the field than the minimum specified by the <tt><a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-minChars" ext:member="minChars" ext:cls="Ext.form.ComboBox">minChars</a></tt> config option.  It
also clears any filter previously saved in the current store (defaults to <tt>false</tt>)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-enable"></a><b><a href="source/Component.html#method-Ext.Component-enable">enable</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Enable this component.</div><div class="long">Enable this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#enable" ext:member="#enable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-expand"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-expand">expand</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Expands the dropdown list if it is currently hidden. Fires the expand event on completion.</div><div class="long">Expands the dropdown list if it is currently hidden. Fires the <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-expand" ext:member="expand" ext:cls="Ext.form.ComboBox">expand</a> event on completion.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentBy"></a><b><a href="source/Component.html#method-Ext.Component-findParentBy">findParentBy</a></b>(&nbsp;<code>Function&nbsp;fcn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by a custom function. If the passed function returns
true, the con...</div><div class="long">Find a container above this component at any level by a custom function. If the passed function returns
true, the container will be returned. The passed function is called with the arguments (container, this component).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fcn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container for which the custom function returns true</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentBy" ext:member="#findParentBy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentByType"></a><b><a href="source/Component.html#method-Ext.Component-findParentByType">findParentByType</a></b>(&nbsp;<code>String/Class&nbsp;xtype</code>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by xtype or class</div><div class="long">Find a container above this component at any level by xtype or class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String/Class<div class="sub-desc">The xtype string for a component, or the class of the component directly</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container which matches the given xtype or class</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentByType" ext:member="#findParentByType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-focus"></a><b><a href="source/Component.html#method-Ext.Component-focus">focus</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;selectText</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Number&nbsp;delay</code>]</span>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Try to focus this component.</div><div class="long">Try to focus this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selectText</code> : Boolean<div class="sub-desc">(optional) If applicable, true to also select the text in this component</div></li><li><code>delay</code> : Boolean/Number<div class="sub-desc">(optional) Delay the focus this number of milliseconds (true for 10 milliseconds)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#focus" ext:member="#focus" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getBox">getBox</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Gets the current box measurements of the component's underlying element.</div><div class="long">Gets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">box An object in the format {x, y, width, height}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getBox" ext:member="#getBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getBubbleTarget"></a><b><a href="source/Component.html#method-Ext.Component-getBubbleTarget">getBubbleTarget</a></b>()
    :
                                        Ext.Container<div class="mdesc"><div class="short">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.</div><div class="long">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">the Container which owns this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getBubbleTarget" ext:member="#getBubbleTarget" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getEl"></a><b><a href="source/Component.html#method-Ext.Component-getEl">getEl</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will usually be
a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will <i>usually</i> be
a &lt;DIV> element created by the class's onRender method, but that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<p><b>The Element will not be available until this Component has been rendered.</b></p>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners for this Component's
own Observable events), perform the adding of the listener in a one-off render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        },
        single: true  <i>// Remove the listener after first invocation</i>
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element which encapsulates this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getEl" ext:member="#getEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getHeight">getHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current height of the component's underlying element.</div><div class="long">Gets the current height of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getHeight" ext:member="#getHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getId"></a><b><a href="source/Component.html#method-Ext.Component-getId">getId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the id of this component or generates an id:"ext-comp-" + (++Ext.Component.AUTO_ID)</div><div class="long">Returns the id of this component or generates an id:<pre><code><em>"ext-comp-"</em> + (++Ext.Component.AUTO_ID)</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getId" ext:member="#getId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getItemId"></a><b><a href="source/Component.html#method-Ext.Component-getItemId">getItemId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the item id of this component.</div><div class="long">Returns the item id of this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getItemId" ext:member="#getItemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-getListParent"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-getListParent">getListParent</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns the element used to house this ComboBox's pop-up list. Defaults to the document body.
A custom implementation...</div><div class="long"><p>Returns the element used to house this ComboBox's pop-up list. Defaults to the document body.</p>
A custom implementation may be provided as a configuration option if the floating list needs to be rendered
to a different Element. An example might be rendering the list inside a Menu so that clicking
the list does not hide the Menu:<pre><code><b>var</b> store = <b>new</b> Ext.data.ArrayStore({
    autoDestroy: true,
    fields: [<em>'initials'</em>, <em>'fullname'</em>],
    data : [
        [<em>'FF'</em>, <em>'Fred Flintstone'</em>],
        [<em>'BR'</em>, <em>'Barney Rubble'</em>]
    ]
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({
    store: store,
    displayField: <em>'fullname'</em>,
    emptyText: <em>'Select a name...'</em>,
    forceSelection: true,
    getListParent: <b>function</b>() {
        <b>return</b> this.el.up(<em>'.x-menu'</em>);
    },
    iconCls: <em>'no-icon'</em>, <i>//use iconCls <b>if</b> placing within menu to shift to right side of menu</i>
    mode: <em>'local'</em>,
    selectOnFocus: true,
    triggerAction: <em>'all'</em>,
    typeAhead: true,
    width: 135
});

<b>var</b> menu = <b>new</b> Ext.menu.Menu({
    id: <em>'mainMenu'</em>,
    items: [
        combo <i>// A Field <b>in</b> a Menu</i>
    ]
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-getName"></a><b><a href="source/Field.html#method-Ext.form.Field-getName">getName</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the name or hiddenName
attribute of the field if available.</div><div class="long">Returns the <a href="output/Ext.form.Field.html#Ext.form.Field-name" ext:member="name" ext:cls="Ext.form.Field">name</a> or <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a>
attribute of the field if available.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">name The field {@link Ext.form.Field#name name} or {@link Ext.form.ComboBox#hiddenName hiddenName}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#getName" ext:member="#getName" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getOuterSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getOuterSize">getOuterSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element, including space taken by its margins.</div><div class="long">Gets the current size of the component's underlying element, including space taken by its margins.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width + left/right margins), height: (element height + top/bottom margins)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getOuterSize" ext:member="#getOuterSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getPosition">getPosition</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Gets the current XY position of the component's underlying element.</div><div class="long">Gets the current XY position of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The XY position of the element (e.g., [100, 200])</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getPosition" ext:member="#getPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-getRawValue"></a><b><a href="source/Field.html#method-Ext.form.Field-getRawValue">getRawValue</a></b>()
    :
                                        Mixed<div class="mdesc"><div class="short">Returns the raw data value which may or may not be a valid, defined value.  To return a normalized value see getValue...</div><div class="long">Returns the raw data value which may or may not be a valid, defined value.  To return a normalized value see <a href="output/Ext.form.Field.html#Ext.form.Field-getValue" ext:member="getValue" ext:cls="Ext.form.Field">getValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">value The field value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#getRawValue" ext:member="#getRawValue" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getSize">getSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element.</div><div class="long">Gets the current size of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width), height: (element height)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getSize" ext:member="#getSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-getStore"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-getStore">getStore</a></b>()
    :
                                        Ext.data.Store<div class="mdesc"><div class="short">Returns the store associated with this combo.</div><div class="long">Returns the store associated with this combo.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Store</code><div class="sub-desc">The store</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-getValue"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-getValue">getValue</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the currently selected field value or empty string if no value is set.</div><div class="long">Returns the currently selected field value or empty string if no value is set.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">value The selected value</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getWidth">getWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current width of the component's underlying element.</div><div class="long">Gets the current width of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getWidth" ext:member="#getWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXType"></a><b><a href="source/Component.html#method-Ext.Component-getXType">getXType</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Gets the xtype for this component as registered with Ext.ComponentMgr. For a list of all
available xtypes, see the Ex...</div><div class="long">Gets the xtype for this component as registered with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header. Example usage:
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXType());  <i>// alerts <em>'textfield'</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXType" ext:member="#getXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXTypes"></a><b><a href="source/Component.html#method-Ext.Component-getXTypes">getXTypes</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the Ext...</div><div class="long"><p>Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXTypes());  <i>// alerts <em>'component/box/field/textfield'</em></i>
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype hierarchy string</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXTypes" ext:member="#getXTypes" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hide"></a><b><a href="source/Component.html#method-Ext.Component-hide">hide</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Hide this component.</div><div class="long">Hide this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#hide" ext:member="#hide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-isDirty"></a><b><a href="source/Field.html#method-Ext.form.Field-isDirty">isDirty</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the value of this Field has been changed from its original value.
Will return false if the field is d...</div><div class="long"><p>Returns true if the value of this Field has been changed from its original value.
Will return false if the field is disabled or has not been rendered yet.</p>
<p>Note that if the owning <a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">form</a> was configured with
<a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">Ext.form.BasicForm</a>.<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-trackResetOnLoad" ext:member="trackResetOnLoad" ext:cls="Ext.form.BasicForm">trackResetOnLoad</a>
then the <i>original value</i> is updated when the values are loaded by
<a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">Ext.form.BasicForm</a>.<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-setValues" ext:member="setValues" ext:cls="Ext.form.BasicForm">setValues</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this field has been changed from its original value (and
is not disabled), false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#isDirty" ext:member="#isDirty" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-isExpanded"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-isExpanded">isExpanded</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns true if the dropdown list is expanded, else false.</div><div class="long">Returns true if the dropdown list is expanded, else false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-isValid"></a><b><a href="source/Field.html#method-Ext.form.Field-isValid">isValid</a></b>(&nbsp;<code>Boolean&nbsp;preventMark</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns whether or not the field value is currently valid</div><div class="long">Returns whether or not the field value is currently valid<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>preventMark</code> : Boolean<div class="sub-desc">True to disable marking the field invalid</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the value is valid, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#isValid" ext:member="#isValid" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isVisible"></a><b><a href="source/Component.html#method-Ext.Component-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this component is visible.</div><div class="long">Returns true if this component is visible.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component is visible, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isVisible" ext:member="#isVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isXType"></a><b><a href="source/Component.html#method-Ext.Component-isXType">isXType</a></b>(&nbsp;<code>String&nbsp;xtype</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;shallow</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from th...</div><div class="long"><p>Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from the xtype (default) or whether it is directly of the xtype specified (shallow = true).</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>For a list of all available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
<b>var</b> isText = t.isXType(<em>'textfield'</em>);        <i>// true</i>
<b>var</b> isBoxSubclass = t.isXType(<em>'box'</em>);       <i>// true, descended from BoxComponent</i>
<b>var</b> isBoxInstance = t.isXType(<em>'box'</em>, true); <i>// false, not a direct BoxComponent instance</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String<div class="sub-desc">The xtype to check for this Component</div></li><li><code>shallow</code> : Boolean<div class="sub-desc">(optional) False to check whether this Component is descended from the xtype (this is
the default), or true to check whether this Component is directly of the specified xtype.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component descends from the specified xtype, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isXType" ext:member="#isXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-markInvalid"></a><b><a href="source/Field.html#method-Ext.form.Field-markInvalid">markInvalid</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;msg</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Mark this field as invalid, using msgTarget to determine how to display the error and
applying invalidClass to the fi...</div><div class="long">Mark this field as invalid, using <a href="output/Ext.form.Field.html#Ext.form.Field-msgTarget" ext:member="msgTarget" ext:cls="Ext.form.Field">msgTarget</a> to determine how to display the error and
applying <a href="output/Ext.form.Field.html#Ext.form.Field-invalidClass" ext:member="invalidClass" ext:cls="Ext.form.Field">invalidClass</a> to the field's element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>msg</code> : String<div class="sub-desc">(optional) The validation message (defaults to <a href="output/Ext.form.Field.html#Ext.form.Field-invalidText" ext:member="invalidText" ext:cls="Ext.form.Field">invalidText</a>)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#markInvalid" ext:member="#markInvalid" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-nextSibling"></a><b><a href="source/Component.html#method-Ext.Component-nextSibling">nextSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the next component in the owning container</div><div class="long">Returns the next component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#nextSibling" ext:member="#nextSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-previousSibling"></a><b><a href="source/Component.html#method-Ext.Component-previousSibling">previousSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the previous component in the owning container</div><div class="long">Returns the previous component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#previousSibling" ext:member="#previousSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-removeClass"></a><b><a href="source/Component.html#method-Ext.Component-removeClass">removeClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Removes a CSS class from the component's underlying element.</div><div class="long">Removes a CSS class from the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#removeClass" ext:member="#removeClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#method-Ext.Component-render">render</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Element/HTMLElement/String&nbsp;container</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String/Number&nbsp;position</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Render this Component into the passed HTML element.
If you are using a Container object to house this Component, then...</div><div class="long"><p>Render this Component into the passed HTML element.</p>
<p><b>If you are using a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a> object to house this Component, then
do not use the render method.</b></p>
<p>A Container's child Components are rendered by that Container's
<a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager when the Container is first rendered.</p>
<p>Certain layout managers allow dynamic addition of child components. Those that do
include <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>, <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>,
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>, <a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">Ext.layout.TableLayout</a>.</p>
<p>If the Container is already rendered when a new child Component is added, you may need to call
the Container's <a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a> to refresh the view which causes any
unrendered child Components to be rendered. This is required so that you can add multiple
child components if needed while only refreshing the layout once.</p>
<p>When creating complex UIs, it is important to remember that sizing and positioning
of child items is the responsibility of the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager.
If you expect child items to be sized in response to user interactions, you must
configure the Container with a layout manager which creates and manages the type of layout you
have in mind.</p>
<p><b>Omitting the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> config means that a basic
layout manager is used which does nothing but render child components sequentially into the
Container. No sizing or positioning will be performed in this situation.</b></p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>container</code> : Element/HTMLElement/String<div class="sub-desc">(optional) The element this Component should be
rendered into. If it is being created from existing markup, this should be omitted.</div></li><li><code>position</code> : String/Number<div class="sub-desc">(optional) The element ID or DOM node index within the container <b>before</b>
which this component will be inserted (defaults to appending to the end of the container)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-reset"></a><b><a href="source/TextField.html#method-Ext.form.TextField-reset">reset</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resets the current field value to the originally-loaded value and clears any validation messages.
Also adds emptyText...</div><div class="long">Resets the current field value to the originally-loaded value and clears any validation messages.
Also adds <tt><b><a href="output/Ext.form.TextField.html#Ext.form.TextField-emptyText" ext:member="emptyText" ext:cls="Ext.form.TextField">emptyText</a></b></tt> and <tt><b><a href="output/Ext.form.TextField.html#Ext.form.TextField-emptyClass" ext:member="emptyClass" ext:cls="Ext.form.TextField">emptyClass</a></b></tt> if the
original value was blank.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#reset" ext:member="#reset" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-select"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-select">select</a></b>(&nbsp;<code>Number&nbsp;index</code>,&nbsp;<code>Boolean&nbsp;scrollIntoView</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Select an item in the dropdown list by its numeric index in the list. This function does NOT cause the select event t...</div><div class="long">Select an item in the dropdown list by its numeric index in the list. This function does NOT cause the select event to fire.
The store must be loaded and the list expanded for this function to work, otherwise use setValue.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The zero-based index of the list item to select</div></li><li><code>scrollIntoView</code> : Boolean<div class="sub-desc">False to prevent the dropdown list from autoscrolling to display the
selected item if it is not currently in view (defaults to true)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-selectByValue"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-selectByValue">selectByValue</a></b>(&nbsp;<code>String&nbsp;value</code>,&nbsp;<code>Boolean&nbsp;scrollIntoView</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Select an item in the dropdown list by its data value. This function does NOT cause the select event to fire.
The sto...</div><div class="long">Select an item in the dropdown list by its data value. This function does NOT cause the select event to fire.
The store must be loaded and the list expanded for this function to work, otherwise use setValue.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The data value of the item to select</div></li><li><code>scrollIntoView</code> : Boolean<div class="sub-desc">False to prevent the dropdown list from autoscrolling to display the
selected item if it is not currently in view (defaults to true)</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the value matched an item in the list, else false</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-selectText"></a><b><a href="source/TextField.html#method-Ext.form.TextField-selectText">selectText</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;start</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;end</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Selects text in this field</div><div class="long">Selects text in this field<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>start</code> : Number<div class="sub-desc">(optional) The index where the selection should start (defaults to 0)</div></li><li><code>end</code> : Number<div class="sub-desc">(optional) The index where the selection should end (defaults to the text length)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#selectText" ext:member="#selectText" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-setDisabled"></a><b><a href="source/Component.html#method-Ext.Component-setDisabled">setDisabled</a></b>(&nbsp;<code>Boolean&nbsp;disabled</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Convenience function for setting disabled/enabled by boolean.</div><div class="long">Convenience function for setting disabled/enabled by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>disabled</code> : Boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#setDisabled" ext:member="#setDisabled" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TriggerField-setEditable"></a><b><a href="source/TriggerField.html#method-Ext.form.TriggerField-setEditable">setEditable</a></b>(&nbsp;<code>Boolean&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allow or prevent the user from directly editing the field text.  If false is passed,
the user will only be able to mo...</div><div class="long">Allow or prevent the user from directly editing the field text.  If false is passed,
the user will only be able to modify the field using the trigger.  This method
is the runtime equivalent of setting the 'editable' config option at config time.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Boolean<div class="sub-desc">True to allow the user to directly edit the field text</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TriggerField.html#setEditable" ext:member="#setEditable" ext:cls="Ext.form.TriggerField">TriggerField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setHeight">setHeight</a></b>(&nbsp;<code>Number&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the height of the component.  This method fires the resize event.</div><div class="long">Sets the height of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>height</code> : Number<div class="sub-desc">The new height to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style.</li>
<li><i>undefined</i> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setHeight" ext:member="#setHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPagePosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPagePosition">setPagePosition</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the page XY position of the component.  To set the left and top instead, use setPosition.
This method fires the ...</div><div class="long">Sets the page XY position of the component.  To set the left and top instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPosition" ext:member="setPosition" ext:cls="Ext.BoxComponent">setPosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPagePosition" ext:member="#setPagePosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPosition">setPosition</a></b>(&nbsp;<code>Number&nbsp;left</code>,&nbsp;<code>Number&nbsp;top</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the left and top of the component.  To set the page XY position instead, use setPagePosition.
This method fires ...</div><div class="long">Sets the left and top of the component.  To set the page XY position instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPagePosition" ext:member="setPagePosition" ext:cls="Ext.BoxComponent">setPagePosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : Number<div class="sub-desc">The new left</div></li><li><code>top</code> : Number<div class="sub-desc">The new top</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPosition" ext:member="#setPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-setRawValue"></a><b><a href="source/Field.html#method-Ext.form.Field-setRawValue">setRawValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Sets the underlying DOM field's value directly, bypassing validation.  To set the value with validation see setValue.</div><div class="long">Sets the underlying DOM field's value directly, bypassing validation.  To set the value with validation see <a href="output/Ext.form.Field.html#Ext.form.Field-setValue" ext:member="setValue" ext:cls="Ext.form.Field">setValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to set</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">value The field value that is set</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#setRawValue" ext:member="#setRawValue" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setSize">setSize</a></b>(&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<code>Mixed&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width and height of this BoxComponent. This method fires the resize event. This method can accept
either wid...</div><div class="long">Sets the width and height of this BoxComponent. This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event. This method can accept
either width and height as separate arguments, or you can pass a size object like <code>{width:10, height:20}</code>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Mixed<div class="sub-desc">The new width to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
<li>A size object in the format <code>{width: widthValue, height: heightValue}</code>.</li>
<li><code>undefined</code> to leave the width unchanged.</li>
</ul></div></div></li><li><code>height</code> : Mixed<div class="sub-desc">The new height to set (not required if a size object is passed as the first arg).
This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
<li><code>undefined</code> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setSize" ext:member="#setSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-setValue"></a><b><a href="source/Combo.html#method-Ext.form.ComboBox-setValue">setValue</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        Ext.form.Field<div class="mdesc"><div class="short">Sets the specified value into the field.  If the value finds a match, the corresponding record text
will be displayed...</div><div class="long">Sets the specified value into the field.  If the value finds a match, the corresponding record text
will be displayed in the field.  If the value does not match the data value of an existing item,
and the valueNotFoundText config option is defined, it will be displayed as the default field text.
Otherwise the field will be blank (although the value will still be set).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The value to match</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.form.Field</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-setVisible"></a><b><a href="source/Component.html#method-Ext.Component-setVisible">setVisible</a></b>(&nbsp;<code>Boolean&nbsp;visible</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Convenience function to hide or show this component by boolean.</div><div class="long">Convenience function to hide or show this component by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>visible</code> : Boolean<div class="sub-desc">True to show, false to hide</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#setVisible" ext:member="#setVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setWidth">setWidth</a></b>(&nbsp;<code>Number&nbsp;width</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width of the component.  This method fires the resize event.</div><div class="long">Sets the width of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Number<div class="sub-desc">The new width to setThis may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setWidth" ext:member="#setWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-show"></a><b><a href="source/Component.html#method-Ext.Component-show">show</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Show this component.</div><div class="long">Show this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#show" ext:member="#show" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-syncSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-syncSize">syncSize</a></b>()
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Force the component's size to recalculate based on the underlying element's current height and width.</div><div class="long">Force the component's size to recalculate based on the underlying element's current height and width.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#syncSize" ext:member="#syncSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-updateBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-updateBox">updateBox</a></b>(&nbsp;<code>Object&nbsp;box</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the current box measurements of the component's underlying element.</div><div class="long">Sets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>box</code> : Object<div class="sub-desc">An object in the format {x, y, width, height}</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#updateBox" ext:member="#updateBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-validate"></a><b><a href="source/Field.html#method-Ext.form.Field-validate">validate</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Validates the field value</div><div class="long">Validates the field value<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the value is valid, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#validate" ext:member="#validate" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-validateValue"></a><b><a href="source/TextField.html#method-Ext.form.TextField-validateValue">validateValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Validates a value according to the field's validation rules and marks the field as invalid
if the validation fails</div><div class="long">Validates a value according to the field's validation rules and marks the field as invalid
if the validation fails<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to validate</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the value is valid, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#validateValue" ext:member="#validateValue" ext:cls="Ext.form.TextField">TextField</a></td></tr></tbody></table><a id="Ext.form.ComboBox-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-afterrender"></a><b><a href="source/Component.html#event-Ext.Component-afterrender">afterrender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component rendering is finished.</div><div class="long">Fires after the component rendering is finished.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#afterrender" ext:member="#afterrender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-autosize"></a><b><a href="source/TextField.html#event-Ext.form.TextField-autosize">autosize</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>,&nbsp;<code>Number&nbsp;width</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the autoSize function is triggered. The field may or
may not have actually changed size according to the d...</div><div class="long">Fires when the <tt><b><a href="output/Ext.form.TextField.html#Ext.form.TextField-autoSize" ext:member="autoSize" ext:cls="Ext.form.TextField">autoSize</a></b></tt> function is triggered. The field may or
may not have actually changed size according to the default logic, but this event provides
a hook for the developer to apply additional logic at runtime to resize the field if needed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc">This text field</div></li><li><code>width</code> : Number<div class="sub-desc">The new field width</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#autosize" ext:member="#autosize" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforedestroy"></a><b><a href="source/Component.html#event-Ext.Component-beforedestroy">beforedestroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is destroyed. Return false to stop the destroy.</div><div class="long">Fires before the component is destroyed. Return false to stop the destroy.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforedestroy" ext:member="#beforedestroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforehide"></a><b><a href="source/Component.html#event-Ext.Component-beforehide">beforehide</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is hidden. Return false to stop the hide.</div><div class="long">Fires before the component is hidden. Return false to stop the hide.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforehide" ext:member="#beforehide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-beforequery"></a><b><a href="source/Combo.html#event-Ext.form.ComboBox-beforequery">beforequery</a></b> :
                                      (&nbsp;<code>Object&nbsp;queryEvent</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before all queries are processed. Return false to cancel the query or set the queryEvent's
cancel property to t...</div><div class="long">Fires before all queries are processed. Return false to cancel the query or set the queryEvent's
cancel property to true.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>queryEvent</code> : Object<div class="sub-desc">An object that has these properties:<ul>
<li><code>combo</code> : Ext.form.ComboBox <div class="sub-desc">This combo box</div></li>
<li><code>query</code> : String <div class="sub-desc">The query</div></li>
<li><code>forceAll</code> : Boolean <div class="sub-desc">True to force "all" query</div></li>
<li><code>cancel</code> : Boolean <div class="sub-desc">Set to true to cancel the query</div></li>
</ul></div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforerender"></a><b><a href="source/Component.html#event-Ext.Component-beforerender">beforerender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is rendered. Return false to stop the render.</div><div class="long">Fires before the component is rendered. Return false to stop the render.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforerender" ext:member="#beforerender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-beforeselect"></a><b><a href="source/Combo.html#event-Ext.form.ComboBox-beforeselect">beforeselect</a></b> :
                                      (&nbsp;<code>Ext.form.ComboBox&nbsp;combo</code>,&nbsp;<code>Ext.data.Record&nbsp;record</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a list item is selected. Return false to cancel the selection.</div><div class="long">Fires before a list item is selected. Return false to cancel the selection.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>combo</code> : Ext.form.ComboBox<div class="sub-desc">This combo box</div></li><li><code>record</code> : Ext.data.Record<div class="sub-desc">The data record returned from the underlying store</div></li><li><code>index</code> : Number<div class="sub-desc">The index of the selected item in the dropdown list</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforeshow"></a><b><a href="source/Component.html#event-Ext.Component-beforeshow">beforeshow</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is shown. Return false to stop the show.</div><div class="long">Fires before the component is shown. Return false to stop the show.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforeshow" ext:member="#beforeshow" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestaterestore"></a><b><a href="source/Component.html#event-Ext.Component-beforestaterestore">beforestaterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is restored. Return false to stop the restore.</div><div class="long">Fires before the state of the component is restored. Return false to stop the restore.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. If this
event is not vetoed, then the state object is passed to <b><tt>applyState</tt></b>. By default,
that simply copies property values into this Component. The method maybe overriden to
provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestaterestore" ext:member="#beforestaterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestatesave"></a><b><a href="source/Component.html#event-Ext.Component-beforestatesave">beforestatesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.</div><div class="long">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestatesave" ext:member="#beforestatesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-blur"></a><b><a href="source/Field.html#event-Ext.form.Field-blur">blur</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this field loses input focus.</div><div class="long">Fires when this field loses input focus.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#blur" ext:member="#blur" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-change"></a><b><a href="source/Field.html#event-Ext.form.Field-change">change</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>,&nbsp;<code>Mixed&nbsp;newValue</code>,&nbsp;<code>Mixed&nbsp;oldValue</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires just before the field blurs if the field value has changed.</div><div class="long">Fires just before the field blurs if the field value has changed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li><li><code>newValue</code> : Mixed<div class="sub-desc">The new value</div></li><li><code>oldValue</code> : Mixed<div class="sub-desc">The original value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#change" ext:member="#change" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-collapse"></a><b><a href="source/Combo.html#event-Ext.form.ComboBox-collapse">collapse</a></b> :
                                      (&nbsp;<code>Ext.form.ComboBox&nbsp;combo</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the dropdown list is collapsed</div><div class="long">Fires when the dropdown list is collapsed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>combo</code> : Ext.form.ComboBox<div class="sub-desc">This combo box</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#event-Ext.Component-destroy">destroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is destroyed.</div><div class="long">Fires after the component is destroyed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disable"></a><b><a href="source/Component.html#event-Ext.Component-disable">disable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is disabled.</div><div class="long">Fires after the component is disabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#disable" ext:member="#disable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-enable"></a><b><a href="source/Component.html#event-Ext.Component-enable">enable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is enabled.</div><div class="long">Fires after the component is enabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#enable" ext:member="#enable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-expand"></a><b><a href="source/Combo.html#event-Ext.form.ComboBox-expand">expand</a></b> :
                                      (&nbsp;<code>Ext.form.ComboBox&nbsp;combo</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the dropdown list is expanded</div><div class="long">Fires when the dropdown list is expanded<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>combo</code> : Ext.form.ComboBox<div class="sub-desc">This combo box</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-focus"></a><b><a href="source/Field.html#event-Ext.form.Field-focus">focus</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this field receives input focus.</div><div class="long">Fires when this field receives input focus.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#focus" ext:member="#focus" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hide"></a><b><a href="source/Component.html#event-Ext.Component-hide">hide</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is hidden.</div><div class="long">Fires after the component is hidden.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#hide" ext:member="#hide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-invalid"></a><b><a href="source/Field.html#event-Ext.form.Field-invalid">invalid</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>,&nbsp;<code>String&nbsp;msg</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the field has been marked as invalid.</div><div class="long">Fires after the field has been marked as invalid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li><li><code>msg</code> : String<div class="sub-desc">The validation message</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#invalid" ext:member="#invalid" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-keydown"></a><b><a href="source/TextField.html#event-Ext.form.TextField-keydown">keydown</a></b> :
                                      (&nbsp;<code>Ext.form.TextField&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Keydown input field event. This event only fires if enableKeyEvents
is set to true.</div><div class="long">Keydown input field event. This event only fires if <tt><b><a href="output/Ext.form.TextField.html#Ext.form.TextField-enableKeyEvents" ext:member="enableKeyEvents" ext:cls="Ext.form.TextField">enableKeyEvents</a></b></tt>
is set to true.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.TextField<div class="sub-desc">This text field</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#keydown" ext:member="#keydown" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-keypress"></a><b><a href="source/TextField.html#event-Ext.form.TextField-keypress">keypress</a></b> :
                                      (&nbsp;<code>Ext.form.TextField&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Keypress input field event. This event only fires if enableKeyEvents
is set to true.</div><div class="long">Keypress input field event. This event only fires if <tt><b><a href="output/Ext.form.TextField.html#Ext.form.TextField-enableKeyEvents" ext:member="enableKeyEvents" ext:cls="Ext.form.TextField">enableKeyEvents</a></b></tt>
is set to true.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.TextField<div class="sub-desc">This text field</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#keypress" ext:member="#keypress" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.TextField-keyup"></a><b><a href="source/TextField.html#event-Ext.form.TextField-keyup">keyup</a></b> :
                                      (&nbsp;<code>Ext.form.TextField&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Keyup input field event. This event only fires if enableKeyEvents
is set to true.</div><div class="long">Keyup input field event. This event only fires if <tt><b><a href="output/Ext.form.TextField.html#Ext.form.TextField-enableKeyEvents" ext:member="enableKeyEvents" ext:cls="Ext.form.TextField">enableKeyEvents</a></b></tt>
is set to true.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.TextField<div class="sub-desc">This text field</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.TextField.html#keyup" ext:member="#keyup" ext:cls="Ext.form.TextField">TextField</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-move"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-move">move</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is moved.</div><div class="long">Fires after the component is moved.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#move" ext:member="#move" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#event-Ext.Component-render">render</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component markup is rendered.</div><div class="long">Fires after the component markup is rendered.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-resize"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-resize">resize</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;adjWidth</code>,&nbsp;<code>Number&nbsp;adjHeight</code>,&nbsp;<code>Number&nbsp;rawWidth</code>,&nbsp;<code>Number&nbsp;rawHeight</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is resized.</div><div class="long">Fires after the component is resized.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>adjWidth</code> : Number<div class="sub-desc">The box-adjusted width that was set</div></li><li><code>adjHeight</code> : Number<div class="sub-desc">The box-adjusted height that was set</div></li><li><code>rawWidth</code> : Number<div class="sub-desc">The width that was originally specified</div></li><li><code>rawHeight</code> : Number<div class="sub-desc">The height that was originally specified</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#resize" ext:member="#resize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.ComboBox-select"></a><b><a href="source/Combo.html#event-Ext.form.ComboBox-select">select</a></b> :
                                      (&nbsp;<code>Ext.form.ComboBox&nbsp;combo</code>,&nbsp;<code>Ext.data.Record&nbsp;record</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a list item is selected</div><div class="long">Fires when a list item is selected<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>combo</code> : Ext.form.ComboBox<div class="sub-desc">This combo box</div></li><li><code>record</code> : Ext.data.Record<div class="sub-desc">The data record returned from the underlying store</div></li><li><code>index</code> : Number<div class="sub-desc">The index of the selected item in the dropdown list</div></li></ul></div></div></div></td><td class="msource">ComboBox</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-show"></a><b><a href="source/Component.html#event-Ext.Component-show">show</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is shown.</div><div class="long">Fires after the component is shown.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#show" ext:member="#show" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-specialkey"></a><b><a href="source/Field.html#event-Ext.form.Field-specialkey">specialkey</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when any key related to navigation (arrows, tab, enter, esc, etc.) is pressed.
To handle other keys see Ext.Pan...</div><div class="long">Fires when any key related to navigation (arrows, tab, enter, esc, etc.) is pressed.
To handle other keys see <a href="output/Ext.Panel.html#Ext.Panel-keys" ext:member="keys" ext:cls="Ext.Panel">Ext.Panel.keys</a> or <a href="output/Ext.KeyMap.html" ext:cls="Ext.KeyMap">Ext.KeyMap</a>.
You can check <a href="output/Ext.EventObject.html#Ext.EventObject-getKey" ext:member="getKey" ext:cls="Ext.EventObject">Ext.EventObject.getKey</a> to determine which key was pressed.
For example: <pre><code><b>var</b> form = <b>new</b> Ext.form.FormPanel({
    ...
    items: [{
            fieldLabel: <em>'Field 1'</em>,
            name: <em>'field1'</em>,
            allowBlank: false
        },{
            fieldLabel: <em>'Field 2'</em>,
            name: <em>'field2'</em>,
            listeners: {
                specialkey: <b>function</b>(field, e){
                    <i>// e.HOME, e.END, e.PAGE_UP, e.PAGE_DOWN,</i>
                    <i>// e.TAB, e.ESC, arrow keys: e.LEFT, e.RIGHT, e.UP, e.DOWN</i>
                    <b>if</b> (e.<a href="output/Ext.EventObject.html#Ext.EventObject-getKey" ext:member="getKey" ext:cls="Ext.EventObject">getKey()</a> == e.ENTER) {
                        <b>var</b> form = field.ownerCt.getForm();
                        form.submit();
                    }
                }
            }
        }
    ],
    ...
});</code></pre><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc">The event object</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#specialkey" ext:member="#specialkey" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-staterestore"></a><b><a href="source/Component.html#event-Ext.Component-staterestore">staterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is restored.</div><div class="long">Fires after the state of the component is restored.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. This is passed
to <b><tt>applyState</tt></b>. By default, that simply copies property values into this
Component. The method maybe overriden to provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#staterestore" ext:member="#staterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-statesave"></a><b><a href="source/Component.html#event-Ext.Component-statesave">statesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is saved to the configured state provider.</div><div class="long">Fires after the state of the component is saved to the configured state provider.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#statesave" ext:member="#statesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-valid"></a><b><a href="source/Field.html#event-Ext.form.Field-valid">valid</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the field has been validated with no errors.</div><div class="long">Fires after the field has been validated with no errors.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#valid" ext:member="#valid" ext:cls="Ext.form.Field">Field</a></td></tr></tbody></table></div>