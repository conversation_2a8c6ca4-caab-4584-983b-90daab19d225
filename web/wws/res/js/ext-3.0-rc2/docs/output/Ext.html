<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Ext-more.html#cls-Ext">Ext</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Global</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Ext-more.js,&#13;Ext.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Ext-more.html#cls-Ext">Ext</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Ext core utilities and functions.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-BLANK_IMAGE_URL"></a><b><a href="source/Ext-more.html#prop-Ext-BLANK_IMAGE_URL">BLANK_IMAGE_URL</a></b> : String<div class="mdesc"><div class="short">URL to a 1x1 transparent gif image used by Ext to create inline icons with CSS background images. (Defaults to
"http:...</div><div class="long">URL to a 1x1 transparent gif image used by Ext to create inline icons with CSS background images. (Defaults to
"http://extjs.com/s.gif" and you should change this to a URL on your server).</div></div></td><td class="msource">Ext</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-SSL_SECURE_URL"></a><b><a href="source/Ext-more.html#prop-Ext-SSL_SECURE_URL">SSL_SECURE_URL</a></b> : String<div class="mdesc"><div class="short">URL to a blank file used by Ext when in secure mode for iframe src and onReady src to prevent
the IE insecure content...</div><div class="long">URL to a blank file used by Ext when in secure mode for iframe src and onReady src to prevent
the IE insecure content warning (defaults to javascript:false).</div></div></td><td class="msource">Ext</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-USE_NATIVE_JSON"></a><b><a href="source/Ext.html#prop-Ext-USE_NATIVE_JSON">USE_NATIVE_JSON</a></b> : Boolean<div class="mdesc"><div class="short">Indicates whether to use native browser parsing for JSON methods.
This option is ignored if the browser does not supp...</div><div class="long">Indicates whether to use native browser parsing for JSON methods.
This option is ignored if the browser does not support native JSON methods.
<b>Note: Native JSON methods will not work with objects that have functions.
Also, property names must be quoted, otherwise the data will not parse.</b> (Defaults to false)</div></div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-emptyFn"></a><b><a href="source/Ext-more.html#prop-Ext-emptyFn">emptyFn</a></b> : Function<div class="mdesc">A reusable empty function</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-enableFx"></a><b><a href="source/Ext.html#prop-Ext-enableFx">enableFx</a></b> : Boolean<div class="mdesc">True if the <a href="output/Ext.Fx.html" ext:cls="Ext.Fx">Ext.Fx</a> Class is available</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-enableGarbageCollector"></a><b><a href="source/Ext.html#prop-Ext-enableGarbageCollector">enableGarbageCollector</a></b> : Boolean<div class="mdesc">True to automatically uncache orphaned Ext.Elements periodically (defaults to true)</div></td><td class="msource">Ext</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-enableListenerCollection"></a><b><a href="source/Ext.html#prop-Ext-enableListenerCollection">enableListenerCollection</a></b> : Boolean<div class="mdesc"><div class="short">True to automatically purge event listeners after uncaching an element (defaults to false).
Note: this only happens i...</div><div class="long">True to automatically purge event listeners after uncaching an element (defaults to false).
Note: this only happens if <a href="output/Ext.html#Ext-enableGarbageCollector" ext:member="enableGarbageCollector" ext:cls="Ext">enableGarbageCollector</a> is true.</div></div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isAir"></a><b><a href="source/Ext.html#prop-Ext-isAir">isAir</a></b> : Boolean<div class="mdesc">True if the detected platform is Adobe Air.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isBorderBox"></a><b><a href="source/Ext.html#prop-Ext-isBorderBox">isBorderBox</a></b> : Boolean<div class="mdesc">True if the detected browser is Internet Explorer running in non-strict mode.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isChrome"></a><b><a href="source/Ext.html#prop-Ext-isChrome">isChrome</a></b> : Boolean<div class="mdesc">True if the detected browser is Chrome.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isGecko"></a><b><a href="source/Ext.html#prop-Ext-isGecko">isGecko</a></b> : Boolean<div class="mdesc">True if the detected browser uses the Gecko layout engine (e.g. Mozilla, Firefox).</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isGecko2"></a><b><a href="source/Ext.html#prop-Ext-isGecko2">isGecko2</a></b> : Boolean<div class="mdesc">True if the detected browser uses a pre-Gecko 1.9 layout engine (e.g. Firefox 2.x).</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isGecko3"></a><b><a href="source/Ext.html#prop-Ext-isGecko3">isGecko3</a></b> : Boolean<div class="mdesc">True if the detected browser uses a Gecko 1.9+ layout engine (e.g. Firefox 3.x).</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isIE"></a><b><a href="source/Ext.html#prop-Ext-isIE">isIE</a></b> : Boolean<div class="mdesc">True if the detected browser is Internet Explorer.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isIE6"></a><b><a href="source/Ext.html#prop-Ext-isIE6">isIE6</a></b> : Boolean<div class="mdesc">True if the detected browser is Internet Explorer 6.x.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isIE7"></a><b><a href="source/Ext.html#prop-Ext-isIE7">isIE7</a></b> : Boolean<div class="mdesc">True if the detected browser is Internet Explorer 7.x.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isIE8"></a><b><a href="source/Ext.html#prop-Ext-isIE8">isIE8</a></b> : Boolean<div class="mdesc">True if the detected browser is Internet Explorer 8.x.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isLinux"></a><b><a href="source/Ext.html#prop-Ext-isLinux">isLinux</a></b> : Boolean<div class="mdesc">True if the detected platform is Linux.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isMac"></a><b><a href="source/Ext.html#prop-Ext-isMac">isMac</a></b> : Boolean<div class="mdesc">True if the detected platform is Mac OS.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isOpera"></a><b><a href="source/Ext.html#prop-Ext-isOpera">isOpera</a></b> : Boolean<div class="mdesc">True if the detected browser is Opera.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isReady"></a><b><a href="source/Ext.html#prop-Ext-isReady">isReady</a></b> : Boolean<div class="mdesc">True when the document is fully initialized and ready for action</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isSafari"></a><b><a href="source/Ext.html#prop-Ext-isSafari">isSafari</a></b> : Boolean<div class="mdesc">True if the detected browser is Safari.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isSafari2"></a><b><a href="source/Ext.html#prop-Ext-isSafari2">isSafari2</a></b> : Boolean<div class="mdesc">True if the detected browser is Safari 2.x.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isSafari3"></a><b><a href="source/Ext.html#prop-Ext-isSafari3">isSafari3</a></b> : Boolean<div class="mdesc">True if the detected browser is Safari 3.x.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isSafari4"></a><b><a href="source/Ext.html#prop-Ext-isSafari4">isSafari4</a></b> : Boolean<div class="mdesc">True if the detected browser is Safari 4.x.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isSecure"></a><b><a href="source/Ext.html#prop-Ext-isSecure">isSecure</a></b> : Boolean<div class="mdesc">True if the page is running over SSL</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isStrict"></a><b><a href="source/Ext.html#prop-Ext-isStrict">isStrict</a></b> : Boolean<div class="mdesc">True if the browser is in strict (standards-compliant) mode, as opposed to quirks mode</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isWebKit"></a><b><a href="source/Ext.html#prop-Ext-isWebKit">isWebKit</a></b> : Boolean<div class="mdesc">True if the detected browser uses WebKit.</div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isWindows"></a><b><a href="source/Ext.html#prop-Ext-isWindows">isWindows</a></b> : Boolean<div class="mdesc">True if the detected platform is Windows.</div></td><td class="msource">Ext</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-useShims"></a><b><a href="source/Ext-more.html#prop-Ext-useShims">useShims</a></b> : Boolean<div class="mdesc"><div class="short">By default, Ext intelligently decides whether floating elements should be shimmed. If you are using flash,
you may wa...</div><div class="long">By default, Ext intelligently decides whether floating elements should be shimmed. If you are using flash,
you may want to set this to true.</div></div></td><td class="msource">Ext</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-version"></a><b><a href="source/Ext.html#prop-Ext-version">version</a></b> : String<div class="mdesc">The version of the framework</div></td><td class="msource">Ext</td></tr></tbody></table><a id="Ext-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-addBehaviors"></a><b><a href="source/Ext-more.html#method-Ext-addBehaviors">addBehaviors</a></b>(&nbsp;<code>Object&nbsp;obj</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Applies event listeners to elements by selectors when the document is ready.
The event name is specified with an &amp;#64...</div><div class="long">Applies event listeners to elements by selectors when the document is ready.
The event name is specified with an <tt>&#64;</tt> suffix.
<pre><code>Ext.addBehaviors({
    <i>// add a listener <b>for</b> click on all anchors <b>in</b> element <b>with</b> id foo</i>
    <em>'#foo a&#64;click'</em> : <b>function</b>(e, t){
        <i>// <b>do</b> something</i>
    },
    
    <i>// add the same listener to multiple selectors (separated by comma BEFORE the &#64;)</i>
    <em>'#foo a, #bar span.some-class&#64;mouseover'</em> : <b>function</b>(){
        <i>// <b>do</b> something</i>
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>obj</code> : Object<div class="sub-desc">The list of behaviors to apply</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-apply"></a><b><a href="source/Ext.html#method-Ext-apply">apply</a></b>(&nbsp;<code>Object&nbsp;obj</code>,&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>Object&nbsp;defaults</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Copies all the properties of config to obj.</div><div class="long">Copies all the properties of config to obj.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>obj</code> : Object<div class="sub-desc">The receiver of the properties</div></li><li><code>config</code> : Object<div class="sub-desc">The source of the properties</div></li><li><code>defaults</code> : Object<div class="sub-desc">A different object that will also be applied for default values</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">returns obj</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-applyIf"></a><b><a href="source/Ext.html#method-Ext-applyIf">applyIf</a></b>(&nbsp;<code>Object&nbsp;obj</code>,&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Copies all the properties of config to obj if they don't already exist.</div><div class="long">Copies all the properties of config to obj if they don't already exist.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>obj</code> : Object<div class="sub-desc">The receiver of the properties</div></li><li><code>config</code> : Object<div class="sub-desc">The source of the properties</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">returns obj</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-clean"></a><b><a href="source/Ext-more.html#method-Ext-clean">clean</a></b>(&nbsp;<code>Array/NodeList&nbsp;arr</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Creates a copy of the passed Array with falsy values removed.</div><div class="long">Creates a copy of the passed Array with falsy values removed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array/NodeList<div class="sub-desc">The Array from which to remove falsy values.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The new, compressed Array.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-copyTo"></a><b><a href="source/Ext-more.html#method-Ext-copyTo">copyTo</a></b>(&nbsp;<code>Object&nbsp;The</code>,&nbsp;<code>Object&nbsp;The</code>,&nbsp;<code>Array/String&nbsp;Either</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Copies a set of named properties fom the source object to the destination object.
example:ImageComponent = Ext.extend...</div><div class="long">Copies a set of named properties fom the source object to the destination object.
<p>example:<pre><code>ImageComponent = Ext.extend(Ext.BoxComponent, {
    initComponent: <b>function</b>() {
        this.autoEl = { tag: <em>'img'</em> };
        MyComponent.superclass.initComponent.apply(this, arguments);
        this.initialBox = Ext.copyTo({}, this.initialConfig, <em>'x,y,width,height'</em>);
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>The</code> : Object<div class="sub-desc">destination object.</div></li><li><code>The</code> : Object<div class="sub-desc">source object.</div></li><li><code>Either</code> : Array/String<div class="sub-desc">an Array of property names, or a comma-delimited list
of property names to copy.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The modified object.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-decode"></a><b><a href="source/JSON.html#method-Ext-decode">decode</a></b>(&nbsp;<code>String&nbsp;json</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;safe</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Shorthand for Ext.util.JSON.decode</div><div class="long">Shorthand for <a href="output/Ext.util.JSON.html#Ext.util.JSON-decode" ext:member="decode" ext:cls="Ext.util.JSON">Ext.util.JSON.decode</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>json</code> : String<div class="sub-desc">The JSON string</div></li><li><code>safe</code> : Boolean<div class="sub-desc">(optional) Whether to return null or throw an exception if the JSON is invalid.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The resulting object</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-destroy"></a><b><a href="source/Ext-more.html#method-Ext-destroy">destroy</a></b>(&nbsp;<code>Mixed&nbsp;arg1</code>,&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;arg2</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;etc...</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Attempts to destroy any objects passed to it by removing all event listeners, removing them from the
DOM (if applicab...</div><div class="long">Attempts to destroy any objects passed to it by removing all event listeners, removing them from the
DOM (if applicable) and calling their destroy functions (if available).  This method is primarily
intended for arguments of type <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> and <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>, but any subclass of
<a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable">Ext.util.Observable</a> can be passed in.  Any number of elements and/or components can be
passed into this function in a single call as separate arguments.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arg1</code> : Mixed<div class="sub-desc">An <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> or <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> to destroy</div></li><li><code>arg2</code> : Mixed<div class="sub-desc">(optional)</div></li><li><code>etc...</code> : Mixed<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-destroyMembers"></a><b><a href="source/Ext-more.html#method-Ext-destroyMembers">destroyMembers</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Mixed&nbsp;arg1</code>,&nbsp;<code>Mixed&nbsp;etc...</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Attempts to destroy and then remove a set of named properties of the passed object.</div><div class="long">Attempts to destroy and then remove a set of named properties of the passed object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The object (most likely a Component) who's properties you wish to destroy.</div></li><li><code>arg1</code> : Mixed<div class="sub-desc">The name of the property to destroy and remove from the object.</div></li><li><code>etc...</code> : Mixed<div class="sub-desc">More property names to destroy and remove.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-each"></a><b><a href="source/Ext.html#method-Ext-each">each</a></b>(&nbsp;<code>Array/NodeList/Mixed&nbsp;array</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Iterates an array calling the passed function with each item, stopping if your function returns false. If the
passed ...</div><div class="long">Iterates an array calling the passed function with each item, stopping if your function returns false. If the
passed array is not really an array, your function is called once with it.
The supplied function is called with (Object item, Number index, Array allItems).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>array</code> : Array/NodeList/Mixed<div class="sub-desc"></div></li><li><code>fn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-encode"></a><b><a href="source/JSON.html#method-Ext-encode">encode</a></b>(&nbsp;<code>Mixed&nbsp;o</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Shorthand for Ext.util.JSON.encode</div><div class="long">Shorthand for <a href="output/Ext.util.JSON.html#Ext.util.JSON-encode" ext:member="encode" ext:cls="Ext.util.JSON">Ext.util.JSON.encode</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Mixed<div class="sub-desc">The variable to encode</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The JSON string</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-escapeRe"></a><b><a href="source/Ext-more.html#method-Ext-escapeRe">escapeRe</a></b>(&nbsp;<code>String&nbsp;str</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Escapes the passed string for use in a regular expression</div><div class="long">Escapes the passed string for use in a regular expression<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>str</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-extend"></a><b><a href="source/Ext.html#method-Ext-extend">extend</a></b>(&nbsp;<code>Function&nbsp;subclass</code>,&nbsp;<code>Function&nbsp;superclass</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;overrides</code>]</span>&nbsp;)
    :
                                        Function<div class="mdesc"><div class="short">Extends one class with another class and optionally overrides members with the passed literal. This class
also adds t...</div><div class="long">Extends one class with another class and optionally overrides members with the passed literal. This class
also adds the function "override()" to the class that can be used to override
members on an instance.
<p>
This function also supports a 2-argument call in which the subclass's constructor is
not passed as an argument. In this form, the parameters are as follows:</p><p>
<div class="mdetail-params"><ul>
<li><code>superclass</code>
<div class="sub-desc">The class being extended</div></li>
<li><code>overrides</code>
<div class="sub-desc">A literal with members which are copied into the subclass's
prototype, and are therefore shared among all instances of the new class.<p>
This may contain a special member named <tt><b>constructor</b></tt>. This is used
to define the constructor of the new class, and is returned. If this property is
<i>not</i> specified, a constructor is generated and returned which just calls the
superclass's constructor passing on its parameters.</p></div></li>
</ul></div></p><p>
For example, to create a subclass of the Ext GridPanel:
<pre><code>MyGridPanel = Ext.extend(Ext.grid.GridPanel, {
    constructor: <b>function</b>(config) {
        <i>// Your preprocessing here</i>
        MyGridPanel.superclass.constructor.apply(this, arguments);
        <i>// Your postprocessing here</i>
    },

    yourMethod: <b>function</b>() {
        <i>// etc.</i>
    }
});</code></pre>
</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>subclass</code> : Function<div class="sub-desc">The class inheriting the functionality</div></li><li><code>superclass</code> : Function<div class="sub-desc">The class being extended</div></li><li><code>overrides</code> : Object<div class="sub-desc">(optional) A literal with members which are copied into the subclass's
prototype, and are therefore shared between all instances of the new class.</div></li></ul><strong>Returns:</strong><ul><li><code>Function</code><div class="sub-desc">The subclass constructor.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-flatten"></a><b><a href="source/Ext-more.html#method-Ext-flatten">flatten</a></b>(&nbsp;<code>Array&nbsp;arr</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Recursively flattens into 1-d Array. Injects Arrays inline.</div><div class="long">Recursively flattens into 1-d Array. Injects Arrays inline.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array<div class="sub-desc">The array to flatten</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The new, flattened array.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-fly"></a><b><a href="source/Element.html#method-Ext-fly">fly</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;named</code>]</span>&nbsp;)
    :
                                        Element<div class="mdesc"><div class="short">Gets the globally shared flyweight Element, with the passed node as the active element. Do not store a reference to t...</div><div class="long"><p>Gets the globally shared flyweight Element, with the passed node as the active element. Do not store a reference to this element -
the dom node can be overwritten by other code. Shorthand of <a href="output/Ext.Element.html#Ext.Element-fly" ext:member="fly" ext:cls="Ext.Element">Ext.Element.fly</a></p>
<p>Use this to make one-time references to DOM elements which are not going to be accessed again either by
application code, or by Ext's classes. If accessing an element which will be processed regularly, then <a href="output/Ext.html#Ext-get" ext:member="get" ext:cls="Ext">Ext.get</a>
will be more appropriate to take advantage of the caching provided by the Ext.Element class.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The dom node or id</div></li><li><code>named</code> : String<div class="sub-desc">(optional) Allows for creation of named reusable flyweights to prevent conflicts
(e.g. internally Ext uses "_global")</div></li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The shared Element object (or null if no matching element was found)</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-get"></a><b><a href="source/Element.html#method-Ext-get">get</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        Element<div class="mdesc"><div class="short">Retrieves Ext.Element objects.
This method does not retrieve Components. This method
retrieves Ext.Element objects wh...</div><div class="long">Retrieves Ext.Element objects.
<p><b>This method does not retrieve <a href="output/Ext.Component.html" ext:cls="Ext.Component">Component</a>s.</b> This method
retrieves Ext.Element objects which encapsulate DOM elements. To retrieve a Component by
its ID, use <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-get" ext:member="get" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.get</a>.</p>
<p>Uses simple caching to consistently return the same object. Automatically fixes if an
object was recreated with the same id via AJAX or DOM.</p>
Shorthand of <a href="output/Ext.Element.html#Ext.Element-get" ext:member="get" ext:cls="Ext.Element">Ext.Element.get</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The id of the node, a DOM Node or an existing Element.</div></li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The Element object (or null if no matching element was found)</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-getBody"></a><b><a href="source/Ext.html#method-Ext-getBody">getBody</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the current document body as an Ext.Element.</div><div class="long">Returns the current document body as an <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The document body</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-getCmp"></a><b><a href="source/Ext-more.html#method-Ext-getCmp">getCmp</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Looks up an existing Component by id</div><div class="long">Looks up an existing <a href="output/Ext.Component.html" ext:cls="Ext.Component">Component</a> by <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The component <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">The Component, or null if not found.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-getDoc"></a><b><a href="source/Ext-more.html#method-Ext-getDoc">getDoc</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the current HTML document object as an Ext.Element.</div><div class="long">Returns the current HTML document object as an <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The document</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-getDom"></a><b><a href="source/Ext.html#method-Ext-getDom">getDom</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        HTMLElement<div class="mdesc"><div class="short">Return the dom node for the passed String (id), dom node, or Ext.Element.
Here are some examples:
// gets dom node ba...</div><div class="long">Return the dom node for the passed String (id), dom node, or Ext.Element.
Here are some examples:
<pre><code><i>// gets dom node based on id</i>
<b>var</b> elDom = Ext.getDom(<em>'elId'</em>);
<i>// gets dom node based on the dom node</i>
<b>var</b> elDom1 = Ext.getDom(elDom);

<i>// If we don&#39;t know <b>if</b> we are working <b>with</b> an</i>
<i>// Ext.Element or a dom node use Ext.getDom</i>
<b>function</b>(el){
    <b>var</b> dom = Ext.getDom(el);
    <i>// <b>do</b> something <b>with</b> the dom node</i>
}</code></pre>
<b>Note</b>: the dom node to be found actually needs to exist (be rendered, etc)
when this method is called to be successful.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-id"></a><b><a href="source/Ext.html#method-Ext-id">id</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;el</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;prefix</code>]</span>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Generates unique ids. If the element already has an id, it is unchanged</div><div class="long">Generates unique ids. If the element already has an id, it is unchanged<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">(optional) The element to generate an id for</div></li><li><code>prefix</code> : String<div class="sub-desc">(optional) Id prefix (defaults "ext-gen")</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The generated Id.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-invoke"></a><b><a href="source/Ext-more.html#method-Ext-invoke">invoke</a></b>(&nbsp;<code>Array|NodeList(not&nbsp;in</code>,&nbsp;<code>String&nbsp;methodName</code>,&nbsp;<code>Anything&nbsp;...</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Invokes a method on each item in an Array.
// Example:
Ext.invoke(Ext.query("p"), "getAttribute", "id");
// [el1.getA...</div><div class="long">Invokes a method on each item in an Array.
<pre><code><i>// Example:</i>
Ext.invoke(Ext.query(<em>"p"</em>), <em>"getAttribute"</em>, <em>"id"</em>);
<i>// [el1.getAttribute(<em>"id"</em>), el2.getAttribute(<em>"id"</em>), ..., elN.getAttribute(<em>"id"</em>)]</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>in</code> : Array|NodeList(not<div class="sub-desc">IE)} arr The Array of items to invoke the method on.</div></li><li><code>methodName</code> : String<div class="sub-desc">The method name to invoke.</div></li><li><code>...</code> : Anything<div class="sub-desc">Arguments to send into the method invocation.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The results of invoking the method on each item in the array.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isArray"></a><b><a href="source/Ext.html#method-Ext-isArray">isArray</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed object is a JavaScript array, otherwise false.</div><div class="long">Returns true if the passed object is a JavaScript array, otherwise false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isDate"></a><b><a href="source/Ext-more.html#method-Ext-isDate">isDate</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed object is a JavaScript date object, otherwise false.</div><div class="long">Returns true if the passed object is a JavaScript date object, otherwise false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isEmpty"></a><b><a href="source/Ext.html#method-Ext-isEmpty">isEmpty</a></b>(&nbsp;<code>Mixed&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;allowBlank</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed value is empty.
The value is deemed to be empty if it is&lt;div class="mdetail-params"&gt;
null
...</div><div class="long"><p>Returns true if the passed value is empty.</p>
<p>The value is deemed to be empty if it is<div class="mdetail-params"><ul>
<li>null</li>
<li>undefined</li>
<li>an empty array</li>
<li>a zero length string (Unless the <tt>allowBlank</tt> parameter is <tt>true</tt>)</li>
</ul></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to test</div></li><li><code>allowBlank</code> : Boolean<div class="sub-desc">(optional) true to allow empty strings (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isFunction"></a><b><a href="source/Ext.html#method-Ext-isFunction">isFunction</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed object is a JavaScript Function, otherwise false.</div><div class="long">Returns true if the passed object is a JavaScript Function, otherwise false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isObject"></a><b><a href="source/Ext.html#method-Ext-isObject">isObject</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed object is a JavaScript Object, otherwise false.</div><div class="long">Returns true if the passed object is a JavaScript Object, otherwise false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-isPrimitive"></a><b><a href="source/Ext.html#method-Ext-isPrimitive">isPrimitive</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed object is a JavaScript 'primitive', a string, number or boolean.</div><div class="long">Returns true if the passed object is a JavaScript 'primitive', a string, number or boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-max"></a><b><a href="source/Ext-more.html#method-Ext-max">max</a></b>(&nbsp;<code>Array|NodeList&nbsp;arr</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;comp</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the maximum value in the Array</div><div class="long">Returns the maximum value in the Array<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array|NodeList<div class="sub-desc">The Array from which to select the maximum value.</div></li><li><code>comp</code> : Function<div class="sub-desc">(optional) a function to perform the comparision which determines maximization.
If omitted the ">" operator will be used. Note: gt = 1; eq = 0; lt = -1</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The maximum value in the Array.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-mean"></a><b><a href="source/Ext-more.html#method-Ext-mean">mean</a></b>(&nbsp;<code>Array&nbsp;arr</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Calculates the mean of the Array</div><div class="long">Calculates the mean of the Array<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array<div class="sub-desc">The Array to calculate the mean value of.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The mean.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-min"></a><b><a href="source/Ext-more.html#method-Ext-min">min</a></b>(&nbsp;<code>Array|NodeList&nbsp;arr</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;comp</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the minimum value in the Array.</div><div class="long">Returns the minimum value in the Array.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array|NodeList<div class="sub-desc">The Array from which to select the minimum value.</div></li><li><code>comp</code> : Function<div class="sub-desc">(optional) a function to perform the comparision which determines minimization.
If omitted the "<" operator will be used. Note: gt = 1; eq = 0; lt = -1</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The minimum value in the Array.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-namespace"></a><b><a href="source/Ext.html#method-Ext-namespace">namespace</a></b>(&nbsp;<code>String&nbsp;namespace1</code>,&nbsp;<code>String&nbsp;namespace2</code>,&nbsp;<code>String&nbsp;etc</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Creates namespaces to be used for scoping variables and classes so that they are not global.
Specifying the last node...</div><div class="long">Creates namespaces to be used for scoping variables and classes so that they are not global.
Specifying the last node of a namespace implicitly creates all other nodes. Usage:
<pre><code>Ext.namespace(<em>'Company'</em>, <em>'Company.data'</em>);
Ext.namespace(<em>'Company.data'</em>); <i>// equivalent and preferable to above syntax</i>
Company.Widget = <b>function</b>() { ... }
Company.data.CustomStore = <b>function</b>(config) { ... }</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>namespace1</code> : String<div class="sub-desc"></div></li><li><code>namespace2</code> : String<div class="sub-desc"></div></li><li><code>etc</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-num"></a><b><a href="source/Ext-more.html#method-Ext-num">num</a></b>(&nbsp;<code>Mixed&nbsp;value</code>,&nbsp;<code>Number&nbsp;defaultValue</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Utility method for validating that a value is numeric, returning the specified default value if it is not.</div><div class="long">Utility method for validating that a value is numeric, returning the specified default value if it is not.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">Should be a number, but any type will be handled appropriately</div></li><li><code>defaultValue</code> : Number<div class="sub-desc">The value to return if the original value is non-numeric</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">Value, if numeric, else defaultValue</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-onReady"></a><b><a href="source/EventManager.html#method-Ext-onReady">onReady</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<code>Object&nbsp;scope</code>,&nbsp;<span title="Optional" class="optional">[<code>boolean&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Fires when the document is ready (before onload and before images are loaded).  Shorthand of Ext.EventManager.onDocum...</div><div class="long">Fires when the document is ready (before onload and before images are loaded).  Shorthand of <a href="output/Ext.EventManager.html#Ext.EventManager-onDocumentReady" ext:member="onDocumentReady" ext:cls="Ext.EventManager">Ext.EventManager.onDocumentReady</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">An object that becomes the scope of the handler</div></li><li><code>options</code> : boolean<div class="sub-desc">(optional) An object containing standard <a href="output/Ext.EventManager.html#Ext.EventManager-addListener" ext:member="addListener" ext:cls="Ext.EventManager">addListener</a> options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-override"></a><b><a href="source/Ext.html#method-Ext-override">override</a></b>(&nbsp;<code>Object&nbsp;origclass</code>,&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Adds a list of functions to the prototype of an existing class, overwriting any existing methods with the same name.
...</div><div class="long">Adds a list of functions to the prototype of an existing class, overwriting any existing methods with the same name.
Usage:<pre><code>Ext.override(MyClass, {
    newMethod1: <b>function</b>(){
        <i>// etc.</i>
    },
    newMethod2: <b>function</b>(foo){
        <i>// etc.</i>
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>origclass</code> : Object<div class="sub-desc">The class to override</div></li><li><code>overrides</code> : Object<div class="sub-desc">The list of functions to add to origClass.  This should be specified as an object literal
containing one or more methods.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-partition"></a><b><a href="source/Ext-more.html#method-Ext-partition">partition</a></b>(&nbsp;<code>Array|NodeList&nbsp;arr</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;truth</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Partitions the set into two sets: a true set and a false set.
Example: 
Example2: 
// Example 1:
Ext.partition([true,...</div><div class="long">Partitions the set into two sets: a true set and a false set.
Example: 
Example2: 
<pre><code><i>// Example 1:</i>
Ext.partition([true, false, true, true, false]); <i>// [[true, true, true], [false, false]]</i>

<i>// Example 2:</i>
Ext.partition(
    Ext.query(<em>"p"</em>),
    <b>function</b>(val){
        <b>return</b> val.className == <em>"class1"</em>
    }
);
<i>// true are those paragraph elements <b>with</b> a className of <em>"class1"</em>,</i>
<i>// false set are those that <b>do</b> not have that className.</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array|NodeList<div class="sub-desc">The array to partition</div></li><li><code>truth</code> : Function<div class="sub-desc">(optional) a function to determine truth.  If this is omitted the element
itself must be able to be evaluated for its truthfulness.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">[true&lt;Array&gt;,false&lt;Array&gt;]</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-pluck"></a><b><a href="source/Ext-more.html#method-Ext-pluck">pluck</a></b>(&nbsp;<code>Array|NodeList&nbsp;arr</code>,&nbsp;<code>String&nbsp;prop</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Plucks the value of a property from each item in the Array
// Example:
Ext.pluck(Ext.query("p"), "className"); // [el...</div><div class="long">Plucks the value of a property from each item in the Array
<pre><code><i>// Example:</i>
Ext.pluck(Ext.query(<em>"p"</em>), <em>"className"</em>); <i>// [el1.className, el2.className, ..., elN.className]</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array|NodeList<div class="sub-desc">The Array of items to pluck the value from.</div></li><li><code>prop</code> : String<div class="sub-desc">The property name to pluck from each element.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The value from each item in the Array.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-query"></a><b><a href="source/DomQuery.html#method-Ext-query">query</a></b>(&nbsp;<code>String&nbsp;path</code>,&nbsp;<span title="Optional" class="optional">[<code>Node&nbsp;root</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Selects an array of DOM nodes by CSS/XPath selector. Shorthand of Ext.DomQuery.select</div><div class="long">Selects an array of DOM nodes by CSS/XPath selector. Shorthand of <a href="output/Ext.DomQuery.html#Ext.DomQuery-select" ext:member="select" ext:cls="Ext.DomQuery">Ext.DomQuery.select</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>path</code> : String<div class="sub-desc">The selector/xpath query</div></li><li><code>root</code> : Node<div class="sub-desc">(optional) The start of the query (defaults to document).</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-reg"></a><b><a href="source/ComponentMgr.html#method-Ext-reg">reg</a></b>(&nbsp;<code>String&nbsp;ptype</code>,&nbsp;<code>Constructor&nbsp;cls</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Shorthand for Ext.ComponentMgr.registerPlugin</div><div class="long">Shorthand for <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerPlugin" ext:member="registerPlugin" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerPlugin</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>ptype</code> : String<div class="sub-desc">The <a href="output/Ext.component.html#Ext.component-ptype" ext:member="ptype" ext:cls="Ext.component">mnemonic string</a> by which the Plugin class
may be looked up.</div></li><li><code>cls</code> : Constructor<div class="sub-desc">The new Plugin class.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-removeNode"></a><b><a href="source/Ext.html#method-Ext-removeNode">removeNode</a></b>(&nbsp;<code>HTMLElement&nbsp;node</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes a DOM node from the document.  The body node will be ignored if passed in.</div><div class="long">Removes a DOM node from the document.  The body node will be ignored if passed in.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : HTMLElement<div class="sub-desc">The node to remove</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-select"></a><b><a href="source/CompositeElementLite.html#method-Ext-select">select</a></b>(&nbsp;<code>String/Array&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;unique</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>HTMLElement/String&nbsp;root</code>]</span>&nbsp;)
    :
                                        CompositeElementLite/CompositeElement<div class="mdesc"><div class="short">Selects elements based on the passed CSS selector to enable Element methods&#13;
to be applied to many related elements i...</div><div class="long">Selects elements based on the passed CSS selector to enable <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> methods
to be applied to many related elements in one statement through the returned <a href="output/Ext.CompositeElement.html" ext:cls="Ext.CompositeElement">CompositeElement</a> or
<a href="output/Ext.CompositeElementLite.html" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a> object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String/Array<div class="sub-desc">The CSS selector or an array of elements</div></li><li><code>unique</code> : Boolean<div class="sub-desc">(optional) true to create a unique Ext.Element for each element (defaults to a shared flyweight object)</div></li><li><code>root</code> : HTMLElement/String<div class="sub-desc">(optional) The root element of the query or id of the root</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElementLite/CompositeElement</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-sum"></a><b><a href="source/Ext-more.html#method-Ext-sum">sum</a></b>(&nbsp;<code>Array&nbsp;arr</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Calculates the sum of the Array</div><div class="long">Calculates the sum of the Array<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array<div class="sub-desc">The Array to calculate the sum value of.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The sum.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-toArray"></a><b><a href="source/Ext.html#method-Ext-toArray">toArray</a></b>(&nbsp;<code>Iterable&nbsp;the</code>&nbsp;)
    :
                                        (Array)<div class="mdesc"><div class="short">Converts any iterable (numeric indices and a length property) into a true array
Don't use this on strings. IE doesn't...</div><div class="long">Converts any iterable (numeric indices and a length property) into a true array
Don't use this on strings. IE doesn't support "abc"[0] which this implementation depends on.
For strings, use this instead: "abc".match(/./g) => [a,b,c];<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : Iterable<div class="sub-desc">iterable object to be turned into a true Array.</div></li></ul><strong>Returns:</strong><ul><li><code>(Array)</code><div class="sub-desc">array</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-type"></a><b><a href="source/Ext-more.html#method-Ext-type">type</a></b>(&nbsp;<code>Mixed&nbsp;object</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the type of object that is passed in. If the object passed in is null or undefined it
return false otherwise ...</div><div class="long">Returns the type of object that is passed in. If the object passed in is null or undefined it
return false otherwise it returns one of the following values:<div class="mdetail-params"><ul>
<li><b>string</b>: If the object passed is a string</li>
<li><b>number</b>: If the object passed is a number</li>
<li><b>boolean</b>: If the object passed is a boolean value</li>
<li><b>date</b>: If the object passed is a Date object</li>
<li><b>function</b>: If the object passed is a function reference</li>
<li><b>object</b>: If the object passed is an object</li>
<li><b>array</b>: If the object passed is an array</li>
<li><b>regexp</b>: If the object passed is a regular expression</li>
<li><b>element</b>: If the object passed is a DOM Element</li>
<li><b>nodelist</b>: If the object passed is a DOM NodeList</li>
<li><b>textnode</b>: If the object passed is a DOM text node and contains something other than whitespace</li>
<li><b>whitespace</b>: If the object passed is a DOM text node and contains only whitespace</li>
</ul></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Mixed<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-unique"></a><b><a href="source/Ext-more.html#method-Ext-unique">unique</a></b>(&nbsp;<code>Array&nbsp;arr</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Creates a copy of the passed Array, filtered to contain only unique values.</div><div class="long">Creates a copy of the passed Array, filtered to contain only unique values.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Array<div class="sub-desc">The Array to filter</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The new Array containing unique values.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-urlDecode"></a><b><a href="source/Ext.html#method-Ext-urlDecode">urlDecode</a></b>(&nbsp;<code>String&nbsp;string</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;overwrite</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Takes an encoded URL and and converts it to an object. Example: Ext.urlDecode("foo=1&amp;bar=2"); // returns {foo: "1", b...</div><div class="long">Takes an encoded URL and and converts it to an object. Example: <pre><code>Ext.urlDecode(<em>"foo=1&bar=2"</em>); <i>// returns {foo: <em>"1"</em>, bar: <em>"2"</em>}</i>
Ext.urlDecode(<em>"foo=1&bar=2&bar=3&bar=4"</em>, false); <i>// returns {foo: <em>"1"</em>, bar: [<em>"2"</em>, <em>"3"</em>, <em>"4"</em>]}</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>string</code> : String<div class="sub-desc"></div></li><li><code>overwrite</code> : Boolean<div class="sub-desc">(optional) Items of the same name will overwrite previous values instead of creating an an array (Defaults to false).</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">A literal with members</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-urlEncode"></a><b><a href="source/Ext.html#method-Ext-urlEncode">urlEncode</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;pre</code>]</span>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Takes an object and converts it to an encoded URL. e.g. Ext.urlEncode({foo: 1, bar: 2}); would return "foo=1&amp;bar=2". ...</div><div class="long">Takes an object and converts it to an encoded URL. e.g. Ext.urlEncode({foo: 1, bar: 2}); would return "foo=1&bar=2".  Optionally, property values can be arrays, instead of keys and the resulting string that's returned will contain a name/value pair for each array value.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc"></div></li><li><code>pre</code> : String<div class="sub-desc">(optional) A prefix to add to the url encoded string</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-value"></a><b><a href="source/Ext-more.html#method-Ext-value">value</a></b>(&nbsp;<code>Mixed&nbsp;value</code>,&nbsp;<code>Mixed&nbsp;defaultValue</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;allowBlank</code>]</span>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Utility method for returning a default value if the passed value is empty.
The value is deemed to be empty if it is&lt;d...</div><div class="long"><p>Utility method for returning a default value if the passed value is empty.</p>
<p>The value is deemed to be empty if it is<div class="mdetail-params"><ul>
<li>null</li>
<li>undefined</li>
<li>an empty array</li>
<li>a zero length string (Unless the <tt>allowBlank</tt> parameter is <tt>true</tt>)</li>
</ul></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to test</div></li><li><code>defaultValue</code> : Mixed<div class="sub-desc">The value to return if the original value is empty</div></li><li><code>allowBlank</code> : Boolean<div class="sub-desc">(optional) true to allow zero length strings to qualify as non-empty (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">value, if non-empty, else defaultValue</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext-zip"></a><b><a href="source/Ext-more.html#method-Ext-zip">zip</a></b>(&nbsp;<code>Arrays|NodeLists&nbsp;arr</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;zipper</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Zips N sets together.
// Example 1:
Ext.zip([1,2,3],[4,5,6]); // [[1,4],[2,5],[3,6]]
// Example 2:
Ext.zip(
    [ "+"...</div><div class="long"><p>Zips N sets together.</p>
<pre><code><i>// Example 1:</i>
Ext.zip([1,2,3],[4,5,6]); <i>// [[1,4],[2,5],[3,6]]</i>
<i>// Example 2:</i>
Ext.zip(
    [ <em>"+"</em>, <em>"-"</em>, <em>"+"</em>],
    [  12,  10,  22],
    [  43,  15,  96],
    <b>function</b>(a, b, c){
        <b>return</b> <em>"$"</em> + a + <em>""</em> + b + <em>"."</em> + c
    }
); <i>// [<em>"$+12.43"</em>, <em>"$-10.15"</em>, <em>"$+22.96"</em>]</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>arr</code> : Arrays|NodeLists<div class="sub-desc">This argument may be repeated. Array(s) to contribute values.</div></li><li><code>zipper</code> : Function<div class="sub-desc">(optional) The last item in the argument list. This will drive how the items are zipped together.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The zipped set.</div></li></ul></div></div></div></td><td class="msource">Ext</td></tr></tbody></table><a id="Ext-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>