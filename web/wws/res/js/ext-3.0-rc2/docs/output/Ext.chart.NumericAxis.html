<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.chart.NumericAxis-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.chart.NumericAxis-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.chart.NumericAxis-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.chart.NumericAxis"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.chart.Axis.html" ext:member="" ext:cls="Ext.chart.Axis">Axis</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">NumericAxis</pre></div><h1>Class <a href="source/Chart.html#cls-Ext.chart.NumericAxis">Ext.chart.NumericAxis</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.chart</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Chart.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Chart.html#cls-Ext.chart.NumericAxis">NumericAxis</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.chart.Axis.html" ext:cls="Ext.chart.Axis" ext:member="">Axis</a></td></tr></table><div class="description">A type of axis whose units are measured in numeric values.</div><div class="hr"></div><a id="Ext.chart.NumericAxis-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-alwaysShowZero"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-alwaysShowZero">alwaysShowZero</a></b> : Boolean<div class="mdesc">If true, and the bounds are calculated automatically, either the minimum or
maximum will be set to zero.</div></td><td class="msource">NumericAxis</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-hideOverlappingLabels"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-hideOverlappingLabels">hideOverlappingLabels</a></b> : Boolean<div class="mdesc">If true, labels that overlap previously drawn labels on the axis will be hidden.</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#hideOverlappingLabels" ext:member="#hideOverlappingLabels" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-labelFunction"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-labelFunction">labelFunction</a></b> : String<div class="mdesc"><div class="short">A string reference to the globally-accessible function that may be called to&#13;
determine each of the label values for ...</div><div class="long">A string reference to the globally-accessible function that may be called to
determine each of the label values for this axis.</div></div></td><td class="msource"><a href="output/Ext.chart.Axis.html#labelFunction" ext:member="#labelFunction" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-majorUnit"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-majorUnit">majorUnit</a></b> : Number<div class="mdesc">The spacing between major intervals on this axis.</div></td><td class="msource">NumericAxis</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-maximum"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-maximum">maximum</a></b> : Number<div class="mdesc">The maximum value drawn by the axis. If not set explicitly, the axis maximum
will be calculated automatically.</div></td><td class="msource">NumericAxis</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-minimum"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-minimum">minimum</a></b> : Number<div class="mdesc">The minimum value drawn by the axis. If not set explicitly, the axis minimum
will be calculated automatically.</div></td><td class="msource">NumericAxis</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-minorUnit"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-minorUnit">minorUnit</a></b> : Number<div class="mdesc">The spacing between minor intervals on this axis.</div></td><td class="msource">NumericAxis</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-orientation"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-orientation">orientation</a></b> : String<div class="mdesc">The direction in which the axis is drawn. May be "horizontal" or "vertical".</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#orientation" ext:member="#orientation" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-reverse"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-reverse">reverse</a></b> : Boolean<div class="mdesc">If true, the items on the axis will be drawn in opposite direction.</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#reverse" ext:member="#reverse" ext:cls="Ext.chart.Axis">Axis</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-scale"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-scale">scale</a></b> : String<div class="mdesc">The scaling algorithm to use on this axis. May be "linear" or "logarithmic".</div></td><td class="msource">NumericAxis</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.NumericAxis-snapToUnits"></a><b><a href="source/Chart.html#prop-Ext.chart.NumericAxis-snapToUnits">snapToUnits</a></b> : Boolean<div class="mdesc"><div class="short">If true, the labels, ticks, gridlines, and other objects will snap to&#13;
the nearest major or minor unit. If false, the...</div><div class="long">If true, the labels, ticks, gridlines, and other objects will snap to
the nearest major or minor unit. If false, their position will be based
on the minimum value.</div></div></td><td class="msource">NumericAxis</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-type"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-type">type</a></b> : String<div class="mdesc">The type of axis.</div></td><td class="msource"><a href="output/Ext.chart.Axis.html#type" ext:member="#type" ext:cls="Ext.chart.Axis">Axis</a></td></tr></tbody></table><a id="Ext.chart.NumericAxis-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.chart.NumericAxis-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>