<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.direct.PollingProvider-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.direct.PollingProvider-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.direct.PollingProvider-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.direct.PollingProvider-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.direct.PollingProvider"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.direct.Provider.html" ext:member="" ext:cls="Ext.direct.Provider">Provider</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.direct.JsonProvider.html" ext:member="" ext:cls="Ext.direct.JsonProvider">JsonProvider</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">PollingProvider</pre></div><h1>Class <a href="source/PollingProvider.html#cls-Ext.direct.PollingProvider">Ext.direct.PollingProvider</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.direct</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">PollingProvider.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/PollingProvider.html#cls-Ext.direct.PollingProvider">PollingProvider</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.direct.JsonProvider.html" ext:cls="Ext.direct.JsonProvider" ext:member="">JsonProvider</a></td></tr></table><div class="description"><p>Provides for repetitive polling of the server at distinct <a href="output/Ext.direct.PollingProvider.html#Ext.direct.PollingProvider-interval" ext:member="interval" ext:cls="Ext.direct.PollingProvider">intervals</a>.
The initial request for data originates from the client, and then is responded to by the
server.</p>
<p>All configurations for the PollingProvider should be generated by the server-side
API portion of the Ext.Direct stack.</p>
<p>An instance of PollingProvider may be created directly via the new keyword or by simply
specifying <tt>type = 'polling'</tt>.  For example:</p>
<pre><code><b>var</b> pollA = <b>new</b> Ext.direct.PollingProvider({
    type:<em>'polling'</em>,
    url: <em>'php/pollA.php'</em>,
});
Ext.Direct.addProvider(pollA);
pollA.disconnect();

Ext.Direct.addProvider(
    {
        type:<em>'polling'</em>,
        url: <em>'php/pollB.php'</em>,
        id: <em>'pollB-provider'</em>
    }
);
<b>var</b> pollB = Ext.Direct.getProvider(<em>'pollB-provider'</em>);</code></pre></div><div class="hr"></div><a id="Ext.direct.PollingProvider-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-baseParams"></a><b><a href="source/PollingProvider.html#cfg-Ext.direct.PollingProvider-baseParams">baseParams</a></b> : Object<div class="mdesc">An object containing properties which are to be sent as parameters
on every polling request</div></td><td class="msource">PollingProvider</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-id"></a><b><a href="source/Provider.html#cfg-Ext.direct.Provider-id">id</a></b> : String<div class="mdesc"><div class="short">The unique id of the provider (defaults to an auto-assigned id).&#13;
You should assign an id if you need to be able to a...</div><div class="long">The unique id of the provider (defaults to an <a href="output/Ext.html#Ext-id" ext:member="id" ext:cls="Ext">auto-assigned id</a>).
You should assign an id if you need to be able to access the provider later and you do
not have an object reference available, for example:
<pre><code>Ext.Direct.addProvider(
    {
        type: <em>'polling'</em>,
        url:  <em>'php/poll.php'</em>,
        id:   <em>'poll-provider'</em>
    }
);
     
<b>var</b> p = <a href="output/Ext.Direct.html" ext:cls="Ext.Direct">Ext.Direct</a>.<a href="output/Ext.Direct.html#Ext.Direct-getProvider" ext:member="getProvider" ext:cls="Ext.Direct">getProvider</a>(<em>'poll-provider'</em>);
p.disconnect();</code></pre></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#id" ext:member="#id" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-interval"></a><b><a href="source/PollingProvider.html#cfg-Ext.direct.PollingProvider-interval">interval</a></b> : Number<div class="mdesc">How often to poll the server-side in milliseconds (defaults to <tt>3000</tt> - every
3 seconds).</div></td><td class="msource">PollingProvider</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-priority"></a><b><a href="source/PollingProvider.html#cfg-Ext.direct.PollingProvider-priority">priority</a></b> : Number<div class="mdesc">Priority of the request (defaults to <tt>3</tt>). See <a href="output/Ext.direct.Provider.html#Ext.direct.Provider-priority" ext:member="priority" ext:cls="Ext.direct.Provider">Ext.direct.Provider.priority</a>.</div></td><td class="msource">PollingProvider</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-type"></a><b><a href="source/Provider.html#cfg-Ext.direct.Provider-type">type</a></b> : String<div class="mdesc"><div class="short">Required, undefined by default.  The type of provider specified&#13;
to Ext.Direct.addProvider to create a&#13;
new Provider....</div><div class="long"><b>Required</b>, <tt>undefined</tt> by default.  The <tt>type</tt> of provider specified
to <a href="output/Ext.Direct.html" ext:cls="Ext.Direct">Ext.Direct</a>.<a href="output/Ext.Direct.html#Ext.Direct-addProvider" ext:member="addProvider" ext:cls="Ext.Direct">addProvider</a> to create a
new Provider. Acceptable values by default are:<div class="mdetail-params"><ul>
<li><b><tt>polling</tt></b> : <a href="output/Ext.direct.PollingProvider.html" ext:cls="Ext.direct.PollingProvider">PollingProvider</a></li>
<li><b><tt>remoting</tt></b> : <a href="output/Ext.direct.RemotingProvider.html" ext:cls="Ext.direct.RemotingProvider">RemotingProvider</a></li>
</ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#type" ext:member="#type" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-url"></a><b><a href="source/PollingProvider.html#cfg-Ext.direct.PollingProvider-url">url</a></b> : String/Function<div class="mdesc"><div class="short">The url which the PollingProvider should contact with each request. This can also be&#13;
an imported Ext.Direct method w...</div><div class="long">The url which the PollingProvider should contact with each request. This can also be
an imported Ext.Direct method which will accept the baseParams as its only argument.</div></div></td><td class="msource">PollingProvider</td></tr></tbody></table><a id="Ext.direct.PollingProvider-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-"></a><b><a href="source/ext-lang-vn.html#prop-Ext.direct.PollingProvider-"></a></b> : Object<div class="mdesc">List compiled by mystix on the extjs.com forums.
Thank you Mystix!</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-Slovak"></a><b><a href="source/ext-lang-sk.html#prop-Ext.direct.PollingProvider-Slovak">Slovak</a></b> : Object<div class="mdesc">List compiled by mystix on the extjs.com forums.
Thank you Mystix!</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-connect"></a><b><a href="source/Provider.html#prop-Ext.direct.Provider-connect">connect</a></b> : Object<div class="mdesc">Abstract methods for subclasses to implement.</div></td><td class="msource"><a href="output/Ext.direct.Provider.html#connect" ext:member="#connect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-disconnect"></a><b><a href="source/Provider.html#prop-Ext.direct.Provider-disconnect">disconnect</a></b> : Object<div class="mdesc">Abstract methods for subclasses to implement.</div></td><td class="msource"><a href="output/Ext.direct.Provider.html#disconnect" ext:member="#disconnect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-el_GR.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Greek translation
By thesilentman (utf8 encoding)
27 Apr 2008
Changes since previous (second) Version:
+ added Date.s...</div><div class="long">Greek translation
By thesilentman (utf8 encoding)
27 Apr 2008
Changes since previous (second) Version:
+ added Date.shortMonthNames 
+ added Date.getShortMonthName 
+ added Date.monthNumbers
+ added Ext.grid.GroupingView</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-tr.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
Turkish translation by Alper YAZGAN
2008-01-24, 10...</div><div class="long">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
Turkish translation by Alper YAZGAN
2008-01-24, 10:29 AM 
Updated to 2.2 by YargicX
2008-10-05, 06:22 PM</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-vn.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Vietnamese translation
By bpmtri
12-April-2007 04:06PM</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-ko.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Korean Translations By nicetip
05 September 2007
Modify by techbug / 25 February 2008</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-th.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">List compiled by KillerNay on the extjs.com forums.
Thank you KillerNay!
Thailand Translations</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-bg.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Bulgarian Translation
By Георги Костадинов, Калгари, Канада
10 October 2007
By Nedko Penev 
26 October 2007
(utf-8 en...</div><div class="long">Bulgarian Translation
By Георги Костадинов, Калгари, Канада
10 October 2007
By Nedko Penev 
26 October 2007
(utf-8 encoding)</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-pt_PT.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Ext 2.2.0 - Portuguese/Portugal (pt_PT) Translation
by Nuno Franco da Costa - francodacosta.com
translated from ext-l...</div><div class="long">Ext 2.2.0 - Portuguese/Portugal (pt_PT) Translation
by Nuno Franco da Costa - francodacosta.com
translated from ext-lang-en.js
11 Nov 2008</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-ca.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Catalonian Translation by halkon_polako 6-12-2007
December correction halkon_polako 11-12-2007
Synchronized with 2.2 ...</div><div class="long">Catalonian Translation by halkon_polako 6-12-2007
December correction halkon_polako 11-12-2007
Synchronized with 2.2 version of ext-lang-en.js (provided by Condor 8 aug 2008) 
by halkon_polako 14-aug-2008</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-pl.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Polish Translations
By vbert 17-April-2007
Updated by mmar 16-November-2007
Encoding: utf-8</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-hu.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
Hungarian Translations (utf-8 encoded)
by Amon  (2...</div><div class="long">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
Hungarian Translations (utf-8 encoded)
by Amon <<EMAIL>> (27 Apr 2008)
encoding fixed by Vili (17 Feb 2009)</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-no_NB.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Norwegian translation (Bokmål: no-NB)
By Tore Kjørsvik 21-January-2008</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-lv.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Latvian Translations
By salix 17 April 2007</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-sl.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Slovenian translation by Matjaž (UTF-8 encoding)
25 April 2007</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-gr.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Greek (Old Version) Translations by Vagelis
03-June-2007</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-cs.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Czech Translations
Translated by Tomáš Korčák (72)
2008/02/08 18:02, Ext-2.0.1</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-id.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Pedoman translasi:
http://id.wikisource.org/wiki/Panduan_Pembakuan_Istilah,_Pelaksanaan_Instruksi_Presiden_Nomor_2_Ta...</div><div class="long">Pedoman translasi:
http://id.wikisource.org/wiki/Panduan_Pembakuan_Istilah,_Pelaksanaan_Instruksi_Presiden_Nomor_2_Tahun_2001_Tentang_Penggunaan_Komputer_Dengan_Aplikasi_Komputer_Berbahasa_Indonesia
Original source: http://vlsm.org/etc/baku-0.txt
by Farid GS
farid [at] pulen.net
10:13 04 Desember 2007
Indonesian Translations</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-lt.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Lithuanian Translations (UTF-8)
By Vladas Saulis, October 18, 2007</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-ro.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Romanian translations for ExtJS 2.1
First released by Lucian Lature on 2007-04-24
Changed locale for Romania (date fo...</div><div class="long">Romanian translations for ExtJS 2.1
First released by Lucian Lature on 2007-04-24
Changed locale for Romania (date formats) as suggested by keypoint
on ExtJS forums: http://www.extjs.com/forum/showthread.php?p=129524#post129524
Removed some useless parts
Changed by: Emil Cazamir, 2008-04-24
Fixed some errors left behind
Changed by: Emil Cazamir, 2008-09-01</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-sv_SE.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Swedish translation (utf8-encoding)
By Erik Andersson, Monator Technologies
24 April 2007
Changed by Cariad, 29 July ...</div><div class="long">Swedish translation (utf8-encoding)
By Erik Andersson, Monator Technologies
24 April 2007
Changed by Cariad, 29 July 2007</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-pt_BR.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">Portuguese/Brazil Translation by Weber Souza
08 April 2007
Updated by Allan Brazute Alves (EthraZa)
06 September 2007...</div><div class="long">Portuguese/Brazil Translation by Weber Souza
08 April 2007
Updated by Allan Brazute Alves (EthraZa)
06 September 2007
Updated by Leonardo Lima
05 March 2008
Updated by Juliano Tarini (jtarini)
22 April 2008</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-en_GB.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
English (UK) Translations
updated to 2.2 by Condor...</div><div class="long">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
English (UK) Translations
updated to 2.2 by Condor (8 Aug 2008)</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-fi.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Finnish Translations
<tuomas.salo (at) iki.fi>
'ä' should read as lowercase 'a' with two dots on top (&auml;)</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-nl.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
Dutch Translations
by Ido Sebastiaan Bas van Oostv...</div><div class="long">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
Dutch Translations
by Ido Sebastiaan Bas van Oostveen (12 Oct 2007)
updated to 2.2 by Condor (8 Aug 2008)</div></div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-he.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Hebrew Translations
By spartacus (from forums) 06-12-2007</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-no_NN.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc">Norwegian translation (Nynorsk: no-NN)
By Tore Kjørsvik 21-January-2008</div></td><td class="msource">PollingProvider</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-indicatorText"></a><b><a href="source/ext-lang-en.html#prop-Ext.direct.PollingProvider-indicatorText">indicatorText</a></b> : Object<div class="mdesc"><div class="short">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
English Translations
updated to 2.2 by Condor (8 A...</div><div class="long">List compiled by mystix on the extjs.com forums.
Thank you Mystix!
English Translations
updated to 2.2 by Condor (8 Aug 2008)</div></div></td><td class="msource">PollingProvider</td></tr></tbody></table><a id="Ext.direct.PollingProvider-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-connect"></a><b><a href="source/PollingProvider.html#method-Ext.direct.PollingProvider-connect">connect</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Connect to the server-side and begin the polling process. To handle each&#13;
response subscribe to the data event.</div><div class="long">Connect to the server-side and begin the polling process. To handle each
response subscribe to the data event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PollingProvider</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-disconnect"></a><b><a href="source/PollingProvider.html#method-Ext.direct.PollingProvider-disconnect">disconnect</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Disconnect from the server-side and stop the polling process. The disconnect&#13;
event will be fired on a successful dis...</div><div class="long">Disconnect from the server-side and stop the polling process. The disconnect
event will be fired on a successful disconnect.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">PollingProvider</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-isConnected"></a><b><a href="source/Provider.html#method-Ext.direct.Provider-isConnected">isConnected</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns whether or not the server-side is currently connected.&#13;
Abstract method for subclasses to implement.</div><div class="long">Returns whether or not the server-side is currently connected.
Abstract method for subclasses to implement.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#isConnected" ext:member="#isConnected" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.direct.PollingProvider-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-beforepoll"></a><b><a href="source/PollingProvider.html#event-Ext.direct.PollingProvider-beforepoll">beforepoll</a></b> :
                                      (&nbsp;<code>Ext.direct.PollingProvider&nbsp;</code>&nbsp;)
    <div class="mdesc"><div class="short">Fired immediately before a poll takes place, an event handler can return false&#13;
in order to cancel the poll.</div><div class="long">Fired immediately before a poll takes place, an event handler can return false
in order to cancel the poll.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code></code> : Ext.direct.PollingProvider<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">PollingProvider</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-connect"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-connect">connect</a></b> :
                                      (&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the Provider connects to the server-side</div><div class="long">Fires when the Provider connects to the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#connect" ext:member="#connect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-data"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-data">data</a></b> :
                                      (&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>,&nbsp;<code>event&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the Provider receives data from the server-side</div><div class="long">Fires when the Provider receives data from the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li><li><code>e</code> : event<div class="sub-desc">The <a href="output/Ext.Direct.html#Ext.Direct-eventTypes" ext:member="eventTypes" ext:cls="Ext.Direct">Ext.Direct.Event type</a> that occurred.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#data" ext:member="#data" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-disconnect"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-disconnect">disconnect</a></b> :
                                      (&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the Provider disconnects from the server-side</div><div class="long">Fires when the Provider disconnects from the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#disconnect" ext:member="#disconnect" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.Provider-exception"></a><b><a href="source/Provider.html#event-Ext.direct.Provider-exception">exception</a></b> :
                                      ()
    <div class="mdesc"><div class="short">Fires when the Provider receives an exception from the server-side</div><div class="long">Fires when the Provider receives an exception from the server-side<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li>None.</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.direct.Provider.html#exception" ext:member="#exception" ext:cls="Ext.direct.Provider">Provider</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.direct.PollingProvider-poll"></a><b><a href="source/PollingProvider.html#event-Ext.direct.PollingProvider-poll">poll</a></b> :
                                      (&nbsp;<code>Ext.direct.PollingProvider&nbsp;</code>&nbsp;)
    <div class="mdesc"><div class="short">This event has not yet been implemented.</div><div class="long">This event has not yet been implemented.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code></code> : Ext.direct.PollingProvider<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">PollingProvider</td></tr></tbody></table></div>