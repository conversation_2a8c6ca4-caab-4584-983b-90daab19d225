<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.form.HtmlEditor-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.form.HtmlEditor-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.form.HtmlEditor-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.form.HtmlEditor-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.form.HtmlEditor"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.Component.html" ext:member="" ext:cls="Ext.Component">Component</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.BoxComponent.html" ext:member="" ext:cls="Ext.BoxComponent">BoxComponent</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.form.Field.html" ext:member="" ext:cls="Ext.form.Field">Field</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">HtmlEditor</pre></div><h1>Class <a href="source/HtmlEditor.html#cls-Ext.form.HtmlEditor">Ext.form.HtmlEditor</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.form</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">HtmlEditor.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/HtmlEditor.html#cls-Ext.form.HtmlEditor">HtmlEditor</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.form.Field.html" ext:cls="Ext.form.Field" ext:member="">Field</a></td></tr><tr><td class="label">xtype:</td><td class="hd-info">htmleditor</td></tr></table><div class="description">Provides a lightweight HTML Editor component. Some toolbar features are not supported by Safari and will be 
automatically hidden when needed.  These are noted in the config options where appropriate.
<br><br>The editor's toolbar buttons have tooltips defined in the <a href="output/Ext.form.HtmlEditor.html#Ext.form.HtmlEditor-buttonTips" ext:member="buttonTips" ext:cls="Ext.form.HtmlEditor">buttonTips</a> property, but they are not 
enabled by default unless the global <a href="output/Ext.QuickTips.html" ext:cls="Ext.QuickTips">Ext.QuickTips</a> singleton is <a href="output/Ext.QuickTips.html#Ext.QuickTips-init" ext:member="init" ext:cls="Ext.QuickTips">initialized</a>.
<br><br><b>Note: The focus/blur and validation marking functionality inherited from Ext.form.Field is NOT
supported by this editor.</b>
<br><br>An Editor is a sensitive component that can't be used in all spots standard fields can be used. Putting an Editor within
any element that has display set to 'none' can cause problems in Safari and Firefox due to their default iframe reloading bugs.
<br><br>Example usage:
<pre><code><i>// Simple example rendered <b>with</b> <b>default</b> options:
</i>
Ext.QuickTips.init();  <i>// enable tooltips
</i>
<b>new</b> Ext.form.HtmlEditor({
    renderTo: Ext.getBody(),
    width: 800,
    height: 300
});

<i>// Passed via xtype into a container and <b>with</b> custom options:
</i>
Ext.QuickTips.init();  <i>// enable tooltips
</i>
<b>new</b> Ext.Panel({
    title: <em>'HTML Editor'</em>,
    renderTo: Ext.getBody(),
    width: 600,
    height: 300,
    frame: true,
    layout: <em>'fit'</em>,
    items: {
        xtype: <em>'htmleditor'</em>,
        enableColors: false,
        enableAlignments: false
    }
});</code></pre></div><div class="hr"></div><a id="Ext.form.HtmlEditor-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-anchor"></a><b><a href="source/Component.html#cfg-Ext.Component-anchor">anchor</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this Component is rendered
by a Container which has been configured to use the An...</div><div class="long"><p><b>Note</b>: this config is only used when this Component is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">AnchorLayout</a></b>
layout manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'anchor'</tt>).</p><br>
<p>See <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>.<a href="output/Ext.layout.AnchorLayout.html#Ext.layout.AnchorLayout-anchor" ext:member="anchor" ext:cls="Ext.layout.AnchorLayout">anchor</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#anchor" ext:member="#anchor" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoEl"></a><b><a href="source/Component.html#cfg-Ext.Component-autoEl">autoEl</a></b> : Mixed<div class="mdesc"><div class="short">A tag name or DomHelper spec used to create the Element which will
encapsulate this Component.
You do not normally ne...</div><div class="long"><p>A tag name or <a href="output/Ext.DomHelper.html" ext:cls="Ext.DomHelper">DomHelper</a> spec used to create the <a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">Element</a> which will
encapsulate this Component.</p>
<p>You do not normally need to specify this. For the base classes <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>, <a href="output/Ext.BoxComponent.html" ext:cls="Ext.BoxComponent">Ext.BoxComponent</a>,
and <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>, this defaults to <b><tt>'div'</tt></b>. The more complex Ext classes use a more complex
DOM structure created by their own onRender methods.</p>
<p>This is intended to allow the developer to create application-specific utility Components encapsulated by
different DOM elements. Example usage:</p><pre><code>{
    xtype: <em>'box'</em>,
    autoEl: {
        tag: <em>'img'</em>,
        src: <em>'http:<i>//www.example.com/example.jpg'</em></i>
    }
}, {
    xtype: <em>'box'</em>,
    autoEl: {
        tag: <em>'blockquote'</em>,
        html: <em>'autoEl is cool!'</em>
    }
}, {
    xtype: <em>'container'</em>,
    autoEl: <em>'ul'</em>,
    cls: <em>'ux-unordered-list'</em>,
    items: {
        xtype: <em>'box'</em>,
        autoEl: <em>'li'</em>,
        html: <em>'First list item'</em>
    }
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#autoEl" ext:member="#autoEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-autoShow"></a><b><a href="source/Component.html#cfg-Ext.Component-autoShow">autoShow</a></b> : Boolean<div class="mdesc"><div class="short">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render...</div><div class="long">True if the component should check for hidden classes (e.g. 'x-hidden' or 'x-hide-display') and remove
them on render (defaults to false).</div></div></td><td class="msource"><a href="output/Ext.Component.html#autoShow" ext:member="#autoShow" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-clearCls"></a><b><a href="source/Component.html#cfg-Ext.Component-clearCls">clearCls</a></b> : String<div class="mdesc"><div class="short">The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide...</div><div class="long"><p>The CSS class used to to apply to the special clearing div rendered
directly after each form field wrapper to provide field clearing (defaults to
<tt>'x-form-clear-left'</tt>).</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container
which has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout
manager (eg. <a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>) and either a 
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> is specified or <tt>isFormField=true</tt> is specified.</p><br>
<p>See <a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#clearCls" ext:member="#clearCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-createLinkText"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-createLinkText">createLinkText</a></b> : String<div class="mdesc">The default text for the create link prompt</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ctCls"></a><b><a href="source/Component.html#cfg-Ext.Component-ctCls">ctCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's container. This can be useful for
adding customize...</div><div class="long"><p>An optional extra CSS class that will be added to this component's container. This can be useful for
adding customized styles to the container or any of its children using standard CSS rules.  See
<a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a>.<a href="output/Ext.layout.ContainerLayout.html#Ext.layout.ContainerLayout-extraCls" ext:member="extraCls" ext:cls="Ext.layout.ContainerLayout">extraCls</a> also.</p>
<p><b>Note</b>: <tt>ctCls</tt> defaults to <tt>''</tt> except for the following class
which assigns a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-layout-ct'</tt></li>
</ul></div>
To configure the above Class with an extra CSS class append to the default.  For example,
for BoxLayout (Hbox and Vbox):<pre><code>ctCls: <em>'x-box-layout-ct custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ctCls" ext:member="#ctCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-defaultLinkValue"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-defaultLinkValue">defaultLinkValue</a></b> : String<div class="mdesc">The default value for the create link prompt (defaults to http:/ /)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-defaultValue"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-defaultValue">defaultValue</a></b> : String<div class="mdesc">A default value to be put into the editor to resolve focus issues (defaults to &#8203;, &nbsp; in Opera).</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableAlignments"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableAlignments">enableAlignments</a></b> : Boolean<div class="mdesc">Enable the left, center, right alignment buttons (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableColors"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableColors">enableColors</a></b> : Boolean<div class="mdesc">Enable the fore/highlight color buttons (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableFont"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableFont">enableFont</a></b> : Boolean<div class="mdesc">Enable font selection. Not available in Safari. (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableFontSize"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableFontSize">enableFontSize</a></b> : Boolean<div class="mdesc">Enable the increase/decrease font size buttons (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableFormat"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableFormat">enableFormat</a></b> : Boolean<div class="mdesc">Enable the bold, italic and underline buttons (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableLinks"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableLinks">enableLinks</a></b> : Boolean<div class="mdesc">Enable the create link button. Not available in Safari. (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableLists"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableLists">enableLists</a></b> : Boolean<div class="mdesc">Enable the bullet and numbered list buttons. Not available in Safari. (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-enableSourceEdit"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-enableSourceEdit">enableSourceEdit</a></b> : Boolean<div class="mdesc">Enable the switch to source edit button. Not available in Safari. (defaults to true)</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-fieldLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-fieldLabel">fieldLabel</a></b> : String<div class="mdesc"><div class="short">The label text to display next to this Component (defaults to '').
Note: this config is only used when this Component...</div><div class="long"><p>The label text to display next to this Component (defaults to '').</p>
<br><p><b>Note</b>: this config is only used when this Component is rendered by a Container which
has been configured to use the <b><a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">FormLayout</a></b> layout manager (eg. 
<a href="output/Ext.form.FormPanel.html" ext:cls="Ext.form.FormPanel">Ext.form.FormPanel</a> or specifying <tt>layout:'form'</tt>).</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#fieldLabel" ext:member="#fieldLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-fontFamilies"></a><b><a href="source/HtmlEditor.html#cfg-Ext.form.HtmlEditor-fontFamilies">fontFamilies</a></b> : Array<div class="mdesc">An array of available font families</div></td><td class="msource">HtmlEditor</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-height"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-height">height</a></b> : Number<div class="mdesc">The height of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#height" ext:member="#height" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#cfg-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">Render this component hidden (default is false).</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideLabel"></a><b><a href="source/Component.html#cfg-Ext.Component-hideLabel">hideLabel</a></b> : Boolean<div class="mdesc"><div class="short">true to completely hide the label element
(label and separator). Defaults to false.
By default, even if you do not sp...</div><div class="long"><p><tt>true</tt> to completely hide the label element
(<a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">label</a> and <a href="output/Ext.Component.html#Ext.Component-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.Component">separator</a>). Defaults to <tt>false</tt>.
By default, even if you do not specify a <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt> the space will still be
reserved so that the field will line up with other fields that do have labels.
Setting this to <tt>true</tt> will cause the field to not reserve that space.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>
        hideLabel: true
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideLabel" ext:member="#hideLabel" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideMode"></a><b><a href="source/Component.html#cfg-Ext.Component-hideMode">hideMode</a></b> : String<div class="mdesc"><div class="short">How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset p...</div><div class="long"><p>How this component should be hidden. Supported values are "visibility" (css visibility), "offsets" (negative
offset position) and "display" (css display) - defaults to "display".</p>
<p>For Containers which may be hidden and shown as part of a <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">card layout</a> Container such as a
<a href="output/Ext.TabPanel.html" ext:cls="Ext.TabPanel">TabPanel</a>, it is recommended that hideMode is configured as "offsets". This ensures
that hidden Components still have height and width so that layout managers can perform measurements when
calculating layouts.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#hideMode" ext:member="#hideMode" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hideParent"></a><b><a href="source/Component.html#cfg-Ext.Component-hideParent">hideParent</a></b> : Boolean<div class="mdesc"><div class="short">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the...</div><div class="long">True to hide and show the component's container when hide/show is called on the component, false to hide
and show the component itself (defaults to false).  For example, this can be used as a shortcut for a hide
button on a window by setting hide:true on the button when adding it to its parent container.</div></div></td><td class="msource"><a href="output/Ext.Component.html#hideParent" ext:member="#hideParent" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-id"></a><b><a href="source/Component.html#cfg-Ext.Component-id">id</a></b> : String<div class="mdesc"><div class="short">The unique id of this component (defaults to an auto-assigned id).
You should assign an id if you need to be able to ...</div><div class="long"><p>The <b>unique</b> id of this component (defaults to an <a href="output/Ext.Component.html#Ext.Component-getId" ext:member="getId" ext:cls="Ext.Component">auto-assigned id</a>).
You should assign an id if you need to be able to access the component later and you do
not have an object reference available (e.g., using <a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>).</p>
<p>Note that this id will also be used as the element id for the containing HTML element
that is rendered to the page for this component. This allows you to write id-based CSS
rules to style the specific instance of this component uniquely, and also to select
sub-elements using this component's id as the parent.</p>
<p><b>Note</b>: to avoid complications imposed by a unique <tt>id</tt> see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#id" ext:member="#id" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemCls"></a><b><a href="source/Component.html#cfg-Ext.Component-itemCls">itemCls</a></b> : String<div class="mdesc"><div class="short">An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, itemCls at th...</div><div class="long"><p>An additional CSS class to apply to the div wrapping the form item
element of this field.  If supplied, <tt>itemCls</tt> at the <b>field</b> level will override
the default <tt>itemCls</tt> supplied at the <b>container</b> level. The value specified for 
<tt>itemCls</tt> will be added to the default class (<tt>'x-form-item'</tt>).</p>
<p>Since it is applied to the item wrapper (see
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>), it allows
you to write standard CSS rules that can apply to the field, the label (if specified), or
any other element within the markup for the field.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.</p><br>
Example use:<pre><code><i>// Apply a style to the field<em>'s <b>label</b>:</i>
&lt;style>
    .required .x-form-item-<b>label</b> {font-weight:bold;color:red;}
&lt;/style>

<b>new</b> Ext.FormPanel({
	height: 100,
	renderTo: Ext.getBody(),
	items: [{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Name<em>',
		itemCls: '</em>required<em>' <i>//this <b>label</b> will be styled</i>
	},{
		xtype: '</em>textfield<em>',
		fieldLabel: '</em>Favorite Color<em>'
	}]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemCls" ext:member="#itemCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-itemId"></a><b><a href="source/Component.html#cfg-Ext.Component-itemId">itemId</a></b> : String<div class="mdesc"><div class="short">An itemId can be used as an alternative way to get a reference to a component
when no object reference is available. ...</div><div class="long"><p>An <tt>itemId</tt> can be used as an alternative way to get a reference to a component
when no object reference is available.  Instead of using an <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt> with
<a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">getCmp</a>, use <tt>itemId</tt> with
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a> which will retrieve
<tt>itemId</tt>'s or <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>'s. Since <tt>itemId</tt>'s are an index to the
container's internal MixedCollection, the <tt>itemId</tt> is scoped locally to the container -- 
avoiding potential conflicts with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a> which requires a <b>unique</b>
<tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<pre><code><b>var</b> c = <b>new</b> Ext.Panel({ <i>//</i>
    <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 300,
    <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a>: document.body,
    <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'auto'</em>,
    <a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">items</a>: [
        {
            itemId: <em>'p1'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 1'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        },
        {
            itemId: <em>'p2'</em>,
            <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Panel 2'</em>,
            <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 150
        }
    ]
})
p1 = c.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p1'</em>); <i>// not the same as <a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">Ext.getCmp()</a></i>
p2 = p1.<a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.<a href="output/Ext.Container.html#Ext.Container-getComponent" ext:member="getComponent" ext:cls="Ext.Container">getComponent</a>(<em>'p2'</em>); <i>// reference via a sibling</i></code></pre>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a></tt>.</p>
<p><b>Note</b>: to access the container of an item see <tt><a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#itemId" ext:member="#itemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelSeparator"></a><b><a href="source/Component.html#cfg-Ext.Component-labelSeparator">labelSeparator</a></b> : String<div class="mdesc"><div class="short">The separator to display after the text of each
fieldLabel.  This property may be configured at various levels.
The o...</div><div class="long"><p>The separator to display after the text of each
<tt><a href="output/Ext.Component.html#Ext.Component-fieldLabel" ext:member="fieldLabel" ext:cls="Ext.Component">fieldLabel</a></tt>.  This property may be configured at various levels.
The order of precedence is:
<div class="mdetail-params"><ul>
<li>field / component level</li>
<li>container level</li>
<li><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">layout level</a> (defaults to colon <tt>':'</tt>)</li>
</ul></div>
To display no separator for this field's label specify empty string ''.</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a></tt> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    layoutConfig: {
        labelSeparator: <em>'~'</em>   <i>// layout config has lowest priority (defaults to <em>':'</em>)</i>
    },
    <a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelSeparator" ext:member="labelSeparator" ext:cls="Ext.layout.FormLayout">labelSeparator</a>: <em>'>>'</em>,     <i>// config at container level </i>
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 1'</em>,
        labelSeparator: <em>'...'</em> <i>// field/component level config supersedes others</i>
    },{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Field 2'</em> <i>// labelSeparator will be <em>'='</em></i>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelSeparator" ext:member="#labelSeparator" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-labelStyle"></a><b><a href="source/Component.html#cfg-Ext.Component-labelStyle">labelStyle</a></b> : String<div class="mdesc"><div class="short">A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle val...</div><div class="long"><p>A CSS style specification string to apply directly to this field's
label.  Defaults to the container's labelStyle value if set (eg,
<tt><a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-labelStyle" ext:member="labelStyle" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.labelStyle</a></tt> , or '').</p>
<br><p><b>Note</b>: see the note for <tt><a href="output/Ext.Component.html#Ext.Component-clearCls" ext:member="clearCls" ext:cls="Ext.Component">clearCls</a></tt>.</p><br>
<p>Also see <tt><a href="output/Ext.Component.html#Ext.Component-hideLabel" ext:member="hideLabel" ext:cls="Ext.Component">hideLabel</a> and
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>.<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-fieldTpl" ext:member="fieldTpl" ext:cls="Ext.layout.FormLayout">fieldTpl</a>.</p>
Example use:<pre><code><b>new</b> Ext.FormPanel({
    height: 100,
    renderTo: Ext.getBody(),
    items: [{
        xtype: <em>'textfield'</em>,
        fieldLabel: <em>'Name'</em>,
        labelStyle: <em>'font-weight:bold;'</em>
    }]
});</code></pre></div></div></td><td class="msource"><a href="output/Ext.Component.html#labelStyle" ext:member="#labelStyle" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-name"></a><b><a href="source/Field.html#cfg-Ext.form.Field-name">name</a></b> : String<div class="mdesc"><div class="short">The field's HTML name attribute (defaults to "").
Note: this property must be set if this field is to be automaticall...</div><div class="long">The field's HTML name attribute (defaults to "").
<b>Note</b>: this property must be set if this field is to be automatically included with
<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-submit" ext:member="submit" ext:cls="Ext.form.BasicForm">form submit()</a>.</div></div></td><td class="msource"><a href="output/Ext.form.Field.html#name" ext:member="#name" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-overCls"></a><b><a href="source/Component.html#cfg-Ext.Component-overCls">overCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and...</div><div class="long">An optional extra CSS class that will be added to this component's Element when the mouse moves
over the Element, and removed when the mouse moves out. (defaults to '').  This can be
useful for adding customized "active" or "hover" styles to the component or any of its children using standard CSS rules.</div></div></td><td class="msource"><a href="output/Ext.Component.html#overCls" ext:member="#overCls" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageX"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageX">pageX</a></b> : Number<div class="mdesc">The page level x coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageX" ext:member="#pageX" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-pageY"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-pageY">pageY</a></b> : Number<div class="mdesc">The page level y coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#pageY" ext:member="#pageY" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-plugins"></a><b><a href="source/Component.html#cfg-Ext.Component-plugins">plugins</a></b> : Object/Array<div class="mdesc"><div class="short">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a ...</div><div class="long">An object or array of objects that will provide custom functionality for this component.  The only
requirement for a valid plugin is that it contain an init method that accepts a reference of type Ext.Component.
When a component is created, if any plugins are available, the component will call the init method on each
plugin, passing a reference to itself.  Each plugin can then call methods or respond to events on the
component as needed to provide its functionality.</div></div></td><td class="msource"><a href="output/Ext.Component.html#plugins" ext:member="#plugins" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ptype"></a><b><a href="source/Component.html#cfg-Ext.Component-ptype">ptype</a></b> : String<div class="mdesc"><div class="short">The registered ptype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>ptype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a Plugin is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>ptype</tt> will be looked up at render time up to determine what
type of Plugin to create.<br><br>
If you create your own Plugins, you may register them using
<a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerPlugin" ext:member="registerPlugin" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerPlugin</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#ptype" ext:member="#ptype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ref"></a><b><a href="source/Component.html#cfg-Ext.Component-ref">ref</a></b> : String<div class="mdesc"><div class="short">A path specification, relative to the Component's ownerCt specifying into which
ancestor Container to place a named r...</div><div class="long"><p>A path specification, relative to the Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a> specifying into which
ancestor Container to place a named reference to this Component.</p>
<p>The ancestor axis can be traversed by using '/' characters in the path.
For example, to put a reference to a Toolbar Button into <i>the Panel which owns the Toolbar</i>:</p><pre><code><b>var</b> myGrid = <b>new</b> Ext.grid.EditorGridPanel({
    title: <em>'My EditorGridPanel'</em>,
    store: myStore,
    colModel: myColModel,
    tbar: [{
        text: <em>'Save'</em>,
        handler: saveChanges,
        disabled: true,
        ref: <em>'../saveButton'</em>
    }],
    listeners: {
        afteredit: <b>function</b>() {
<i>//          The button reference is <b>in</b> the GridPanel</i>
            myGrid.saveButton.enable();
        }
    }
});</code></pre>
<p>In the code above, if the ref had been <code><em>'saveButton'</em></code> the reference would
have been placed into the Toolbar. Each '/' in the ref moves up one level from the
Component's <a href="output/Ext.Component.html#Ext.Component-ownerCt" ext:member="ownerCt" ext:cls="Ext.Component">ownerCt</a>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ref" ext:member="#ref" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-region"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-region">region</a></b> : String<div class="mdesc"><div class="short">Note: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the...</div><div class="long"><p><b>Note</b>: this config is only used when this BoxComponent is rendered
by a Container which has been configured to use the <b><a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a></b>
layout manager (eg. specifying <tt>layout:'border'</tt>).</p><br>
<p>See <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">Ext.layout.BorderLayout</a> also.</p></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#region" ext:member="#region" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-renderTo"></a><b><a href="source/Component.html#cfg-Ext.Component-renderTo">renderTo</a></b> : Mixed<div class="mdesc"><div class="short">Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.
Notes ...</div><div class="long"><p>Specify the id of the element, a DOM element or an existing Element that this component
will be rendered into.</p><div><ul>
<li><b>Notes</b> : <ul>
<div class="sub-desc">When using this config, a call to render() is not required.</div>
<div class="sub-desc">Do <u>not</u> use this option if the Component is to be a child item of
a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>. It is the responsibility of the
<a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>'s <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout manager</a>
to render its child items.</div>
</ul></li>
</ul></div>
<p>See <tt><a href="output/Ext.Component.html#Ext.Component-render" ext:member="render" ext:cls="Ext.Component">render</a></tt> also.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#renderTo" ext:member="#renderTo" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateId"></a><b><a href="source/Component.html#cfg-Ext.Component-stateId">stateId</a></b> : String<div class="mdesc"><div class="short">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, o...</div><div class="long">The unique id for this component to use for state management purposes (defaults to the component id if one was
set, otherwise null if the component is using a generated id).
<p>See <a href="output/Ext.Component.html#Ext.Component-stateful" ext:member="stateful" ext:cls="Ext.Component">stateful</a> for an explanation of saving and restoring Component state.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateId" ext:member="#stateId" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-stateful"></a><b><a href="source/Component.html#cfg-Ext.Component-stateful">stateful</a></b> : Boolean<div class="mdesc"><div class="short">A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startu...</div><div class="long"><p>A flag which causes the Component to attempt to restore the state of internal properties
from a saved state on startup. The component must have either a <a href="output/Ext.Component.html#Ext.Component-stateId" ext:member="stateId" ext:cls="Ext.Component">stateId</a> or <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>
assigned for state to be managed.  Auto-generated ids are not guaranteed to be stable across page
loads and cannot be relied upon to save and restore the same state for a component.<p>
<p>For state saving to work, the state manager's provider must have been set to an implementation
of <a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> which overrides the <a href="output/Ext.state.Provider.html#Ext.state.Provider-set" ext:member="set" ext:cls="Ext.state.Provider">set</a>
and <a href="output/Ext.state.Provider.html#Ext.state.Provider-get" ext:member="get" ext:cls="Ext.state.Provider">get</a> methods to save and recall name/value pairs.
A built-in implementation, <a href="output/Ext.state.CookieProvider.html" ext:cls="Ext.state.CookieProvider">Ext.state.CookieProvider</a> is available.</p>
<p>To set the state provider for the current page:</p>
<pre><code>Ext.state.Manager.setProvider(<b>new</b> Ext.state.CookieProvider());</code></pre>
<p>A stateful Component attempts to save state when one of the events listed in the <a href="output/Ext.Component.html#Ext.Component-stateEvents" ext:member="stateEvents" ext:cls="Ext.Component">stateEvents</a>
configuration fires.</p>
To save state, A stateful Component first serializes its state by calling <b><tt>getState</tt></b>. By default,
this function does nothing. The developer must provide an implementation which returns an object hash
which represents the Component's restorable state.</p>
<p>The value yielded by getState is passed to <a href="output/Ext.state.Manager.html#Ext.state.Manager-set" ext:member="set" ext:cls="Ext.state.Manager">Ext.state.Manager.set</a> which uses the configured
<a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider">Ext.state.Provider</a> to save the object keyed by the Component's <a href="output/stateId.html" ext:cls="stateId">stateId</a>, or,
if that is not specified, its <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>During construction, a stateful Component attempts to <i>restore</i> its state by calling
<a href="output/Ext.state.Manager.html#Ext.state.Manager-get" ext:member="get" ext:cls="Ext.state.Manager">Ext.state.Manager.get</a> passing the (@link #stateId}, or, if that is not specified, the <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>.</p>
<p>The resulting object is passed to <b><tt>applyState</tt></b>. The default implementation of applyState
simply copies properties into the object, but a developer may override this to support more behaviour.</p>
<p>You can perform extra processing on state save and restore by attaching handlers to the
<a href="output/Ext.Component.html#Ext.Component-beforestaterestore" ext:member="beforestaterestore" ext:cls="Ext.Component">beforestaterestore</a>, <a href="output/Ext.Component.html#Ext.Component-staterestore" ext:member="staterestore" ext:cls="Ext.Component">staterestore</a>, <a href="output/Ext.Component.html#Ext.Component-beforestatesave" ext:member="beforestatesave" ext:cls="Ext.Component">beforestatesave</a> and <a href="output/Ext.Component.html#Ext.Component-statesave" ext:member="statesave" ext:cls="Ext.Component">statesave</a> events</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#stateful" ext:member="#stateful" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-value"></a><b><a href="source/Field.html#cfg-Ext.form.Field-value">value</a></b> : Mixed<div class="mdesc">A value to initialize this field with (defaults to undefined).</div></td><td class="msource"><a href="output/Ext.form.Field.html#value" ext:member="#value" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-width"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-width">width</a></b> : Number<div class="mdesc">The width of this component in pixels (defaults to auto).</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#width" ext:member="#width" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-x"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-x">x</a></b> : Number<div class="mdesc">The local x (left) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#x" ext:member="#x" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-xtype"></a><b><a href="source/Component.html#cfg-Ext.Component-xtype">xtype</a></b> : String<div class="mdesc"><div class="short">The registered xtype to create. This config option is not used when passing
a config object into a constructor. This ...</div><div class="long">The registered <tt>xtype</tt> to create. This config option is not used when passing
a config object into a constructor. This config option is used only when
lazy instantiation is being used, and a child item of a Container is being
specified not as a fully instantiated Component, but as a <i>Component config
object</i>. The <tt>xtype</tt> will be looked up at render time up to determine what
type of child Component to create.<br><br>
The predefined xtypes are listed <a href="output/Ext.Component.html" ext:cls="Ext.Component">here</a>.
<br><br>
If you subclass Components to create your own Components, you may register
them using <a href="output/Ext.ComponentMgr.html#Ext.ComponentMgr-registerType" ext:member="registerType" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr.registerType</a> in order to be able to
take advantage of lazy instantiation and rendering.</div></div></td><td class="msource"><a href="output/Ext.Component.html#xtype" ext:member="#xtype" ext:cls="Ext.Component">Component</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-y"></a><b><a href="source/BoxComponent.html#cfg-Ext.BoxComponent-y">y</a></b> : Number<div class="mdesc">The local y (top) coordinate for this component if contained within a positioning container.</div></td><td class="msource"><a href="output/Ext.BoxComponent.html#y" ext:member="#y" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr></tbody></table><a id="Ext.form.HtmlEditor-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-buttonTips"></a><b><a href="source/HtmlEditor.html#prop-Ext.form.HtmlEditor-buttonTips">buttonTips</a></b> : Object<div class="mdesc"><div class="short">Object collection of toolbar tooltips for the buttons in the editor. The key&#13;
is the command id associated with that ...</div><div class="long">Object collection of toolbar tooltips for the buttons in the editor. The key
is the command id associated with that button and the value is a valid QuickTips object.
For example:
<pre><code>{
    bold : {
        title: <em>'Bold (Ctrl+B)'</em>,
        text: <em>'Make the selected text bold.'</em>,
        cls: <em>'x-html-editor-tip'</em>
    },
    italic : {
        title: <em>'Italic (Ctrl+I)'</em>,
        text: <em>'Make the selected text italic.'</em>,
        cls: <em>'x-html-editor-tip'</em>
    },
    ...</code></pre></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-el"></a><b><a href="source/Component.html#prop-Ext.Component-el">el</a></b> : Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will
usually be a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will
<i>usually</i> be a &lt;DIV> element created by the class's onRender method, but
that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<br><p><b>Note</b>: this element will not be available until this Component has been
rendered.</b></p><br>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners
for this Component's own Observable events), perform the adding of the listener in a
render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        }
    }
});</code></pre>
<p>See also <tt><a href="output/Ext.Component.html#Ext.Component-getEl" ext:member="getEl" ext:cls="Ext.Component">getEl</a></p></div></div></td><td class="msource"><a href="output/Ext.Component.html#el" ext:member="#el" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hidden"></a><b><a href="source/Component.html#prop-Ext.Component-hidden">hidden</a></b> : Boolean<div class="mdesc">True if this component is hidden. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#hidden" ext:member="#hidden" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-initialConfig"></a><b><a href="source/Component.html#prop-Ext.Component-initialConfig">initialConfig</a></b> : Object<div class="mdesc">This Component's initial configuration specification. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#initialConfig" ext:member="#initialConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-ownerCt"></a><b><a href="source/Component.html#prop-Ext.Component-ownerCt">ownerCt</a></b> : Ext.Container<div class="mdesc"><div class="short">The component's owner Ext.Container (defaults to undefined, and is set automatically when
the component is added to a...</div><div class="long">The component's owner <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (defaults to undefined, and is set automatically when
the component is added to a container).  Read-only.
<p><b>Note</b>: to access items within the container see <tt><a href="output/Ext.Component.html#Ext.Component-itemId" ext:member="itemId" ext:cls="Ext.Component">itemId</a></tt>.</p></div></div></td><td class="msource"><a href="output/Ext.Component.html#ownerCt" ext:member="#ownerCt" ext:cls="Ext.Component">Component</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-rendered"></a><b><a href="source/Component.html#prop-Ext.Component-rendered">rendered</a></b> : Boolean<div class="mdesc">True if this component has been rendered. Read-only.</div></td><td class="msource"><a href="output/Ext.Component.html#rendered" ext:member="#rendered" ext:cls="Ext.Component">Component</a></td></tr></tbody></table><a id="Ext.form.HtmlEditor-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-HtmlEditor"></a><b><a href="source/HtmlEditor.html#cls-Ext.form.HtmlEditor">HtmlEditor</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new HtmlEditor</div><div class="long">Create a new HtmlEditor<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div><table><tr><td class="label">xtype:</td><td class="hd-info">htmleditor</td></tr></table></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-addClass"></a><b><a href="source/Component.html#method-Ext.Component-addClass">addClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Adds a CSS class to the component's underlying element.</div><div class="long">Adds a CSS class to the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to add</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#addClass" ext:member="#addClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-cleanHtml"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-cleanHtml">cleanHtml</a></b>(&nbsp;<code>String&nbsp;html</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Protected method that will not generally be called directly. If you need/want&#13;
custom HTML cleanup, this is the metho...</div><div class="long">Protected method that will not generally be called directly. If you need/want
custom HTML cleanup, this is the method you should override.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>html</code> : String<div class="sub-desc">The HTML to be cleaned</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The cleaned HTML</div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-cloneConfig"></a><b><a href="source/Component.html#method-Ext.Component-cloneConfig">cloneConfig</a></b>(&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Clone the current component using the original config values passed into this instance by default.</div><div class="long">Clone the current component using the original config values passed into this instance by default.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>overrides</code> : Object<div class="sub-desc">A new config containing any properties to override in the cloned version.
An id property can be passed on this object, otherwise one will be generated to avoid duplicates.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">clone The cloned copy of this component</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#cloneConfig" ext:member="#cloneConfig" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#method-Ext.Component-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the c...</div><div class="long">Destroys this component by purging any event listeners, removing the component's element from the DOM,
removing the component from its <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> (if applicable) and unregistering it from
<a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>.  Destruction is generally handled automatically by the framework and this method
should usually not need to be called directly.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-execCmd"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-execCmd">execCmd</a></b>(&nbsp;<code>String&nbsp;cmd</code>,&nbsp;<span title="Optional" class="optional">[<code>String/Boolean&nbsp;value</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes a Midas editor command directly on the editor document.&#13;
For visual commands, you should use relayCmd instea...</div><div class="long">Executes a Midas editor command directly on the editor document.
For visual commands, you should use <a href="output/Ext.form.HtmlEditor.html#Ext.form.HtmlEditor-relayCmd" ext:member="relayCmd" ext:cls="Ext.form.HtmlEditor">relayCmd</a> instead.
<b>This should only be called after the editor is initialized.</b><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cmd</code> : String<div class="sub-desc">The Midas command</div></li><li><code>value</code> : String/Boolean<div class="sub-desc">(optional) The value to pass to the command (defaults to null)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentBy"></a><b><a href="source/Component.html#method-Ext.Component-findParentBy">findParentBy</a></b>(&nbsp;<code>Function&nbsp;fcn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by a custom function. If the passed function returns
true, the con...</div><div class="long">Find a container above this component at any level by a custom function. If the passed function returns
true, the container will be returned. The passed function is called with the arguments (container, this component).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fcn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container for which the custom function returns true</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentBy" ext:member="#findParentBy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-findParentByType"></a><b><a href="source/Component.html#method-Ext.Component-findParentByType">findParentByType</a></b>(&nbsp;<code>String/Class&nbsp;xtype</code>&nbsp;)
    :
                                        Ext.Container<div class="mdesc"><div class="short">Find a container above this component at any level by xtype or class</div><div class="long">Find a container above this component at any level by xtype or class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String/Class<div class="sub-desc">The xtype string for a component, or the class of the component directly</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">The first Container which matches the given xtype or class</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#findParentByType" ext:member="#findParentByType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-focus"></a><b><a href="source/Component.html#method-Ext.Component-focus">focus</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;selectText</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Number&nbsp;delay</code>]</span>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Try to focus this component.</div><div class="long">Try to focus this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selectText</code> : Boolean<div class="sub-desc">(optional) If applicable, true to also select the text in this component</div></li><li><code>delay</code> : Boolean/Number<div class="sub-desc">(optional) Delay the focus this number of milliseconds (true for 10 milliseconds)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#focus" ext:member="#focus" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getBox">getBox</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Gets the current box measurements of the component's underlying element.</div><div class="long">Gets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">box An object in the format {x, y, width, height}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getBox" ext:member="#getBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getBubbleTarget"></a><b><a href="source/Component.html#method-Ext.Component-getBubbleTarget">getBubbleTarget</a></b>()
    :
                                        Ext.Container<div class="mdesc"><div class="short">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.</div><div class="long">Provides the link for Observable's fireEvent method to bubble up the ownership hierarchy.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Container</code><div class="sub-desc">the Container which owns this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getBubbleTarget" ext:member="#getBubbleTarget" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-getDocMarkup"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-getDocMarkup">getDocMarkup</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Protected method that will not generally be called directly. It&#13;
is called when the editor initializes the iframe wit...</div><div class="long">Protected method that will not generally be called directly. It
is called when the editor initializes the iframe with HTML contents. Override this method if you
want to change the initialization markup of the iframe (e.g. to add stylesheets).<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getEl"></a><b><a href="source/Component.html#method-Ext.Component-getEl">getEl</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Ext.Element which encapsulates this Component. This will usually be
a &amp;lt;DIV&gt; element created by the cla...</div><div class="long"><p>Returns the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> which encapsulates this Component. This will <i>usually</i> be
a &lt;DIV> element created by the class's onRender method, but that may be overridden using the <a href="output/Ext.Component.html#Ext.Component-autoEl" ext:member="autoEl" ext:cls="Ext.Component">autoEl</a> config.</p>
<p><b>The Element will not be available until this Component has been rendered.</b></p>
<p>To add listeners for <b>DOM events</b> to this Component (as opposed to listeners for this Component's
own Observable events), perform the adding of the listener in a one-off render event listener:</p><pre><code><b>new</b> Ext.Panel({
    title: <em>'The Clickable Panel'</em>,
    listeners: {
        render: <b>function</b>(p) {
            <i>// Append the Panel to the click handler&#39;s argument list.</i>
            p.getEl().on(<em>'click'</em>, handlePanelClick.createDelegate(null, [p], true));
        },
        single: true  <i>// Remove the listener after first invocation</i>
    }
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The Element which encapsulates this Component.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getEl" ext:member="#getEl" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getHeight">getHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current height of the component's underlying element.</div><div class="long">Gets the current height of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getHeight" ext:member="#getHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getId"></a><b><a href="source/Component.html#method-Ext.Component-getId">getId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the id of this component or generates an id:"ext-comp-" + (++Ext.Component.AUTO_ID)</div><div class="long">Returns the id of this component or generates an id:<pre><code><em>"ext-comp-"</em> + (++Ext.Component.AUTO_ID)</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getId" ext:member="#getId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getItemId"></a><b><a href="source/Component.html#method-Ext.Component-getItemId">getItemId</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the item id of this component.</div><div class="long">Returns the item id of this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getItemId" ext:member="#getItemId" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-getName"></a><b><a href="source/Field.html#method-Ext.form.Field-getName">getName</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns the name or hiddenName
attribute of the field if available.</div><div class="long">Returns the <a href="output/Ext.form.Field.html#Ext.form.Field-name" ext:member="name" ext:cls="Ext.form.Field">name</a> or <a href="output/Ext.form.ComboBox.html#Ext.form.ComboBox-hiddenName" ext:member="hiddenName" ext:cls="Ext.form.ComboBox">hiddenName</a>
attribute of the field if available.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">name The field {@link Ext.form.Field#name name} or {@link Ext.form.ComboBox#hiddenName hiddenName}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#getName" ext:member="#getName" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getOuterSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getOuterSize">getOuterSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element, including space taken by its margins.</div><div class="long">Gets the current size of the component's underlying element, including space taken by its margins.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width + left/right margins), height: (element height + top/bottom margins)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getOuterSize" ext:member="#getOuterSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getPosition">getPosition</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Gets the current XY position of the component's underlying element.</div><div class="long">Gets the current XY position of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page XY (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The XY position of the element (e.g., [100, 200])</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getPosition" ext:member="#getPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-getRawValue"></a><b><a href="source/Field.html#method-Ext.form.Field-getRawValue">getRawValue</a></b>()
    :
                                        Mixed<div class="mdesc"><div class="short">Returns the raw data value which may or may not be a valid, defined value.  To return a normalized value see getValue...</div><div class="long">Returns the raw data value which may or may not be a valid, defined value.  To return a normalized value see <a href="output/Ext.form.Field.html#Ext.form.Field-getValue" ext:member="getValue" ext:cls="Ext.form.Field">getValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">value The field value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#getRawValue" ext:member="#getRawValue" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getSize">getSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets the current size of the component's underlying element.</div><div class="long">Gets the current size of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width), height: (element height)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getSize" ext:member="#getSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-getToolbar"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-getToolbar">getToolbar</a></b>()
    :
                                        Ext.Toolbar<div class="mdesc"><div class="short">Returns the editor's toolbar. This is only available after the editor has been rendered.</div><div class="long">Returns the editor's toolbar. <b>This is only available after the editor has been rendered.</b><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Toolbar</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-getValue"></a><b><a href="source/Field.html#method-Ext.form.Field-getValue">getValue</a></b>()
    :
                                        Mixed<div class="mdesc"><div class="short">Returns the normalized data value (undefined or emptyText will be returned as '').  To return the raw value see getRa...</div><div class="long">Returns the normalized data value (undefined or emptyText will be returned as '').  To return the raw value see <a href="output/Ext.form.Field.html#Ext.form.Field-getRawValue" ext:member="getRawValue" ext:cls="Ext.form.Field">getRawValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">value The field value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#getValue" ext:member="#getValue" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-getWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-getWidth">getWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current width of the component's underlying element.</div><div class="long">Gets the current width of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#getWidth" ext:member="#getWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXType"></a><b><a href="source/Component.html#method-Ext.Component-getXType">getXType</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Gets the xtype for this component as registered with Ext.ComponentMgr. For a list of all
available xtypes, see the Ex...</div><div class="long">Gets the xtype for this component as registered with <a href="output/Ext.ComponentMgr.html" ext:cls="Ext.ComponentMgr">Ext.ComponentMgr</a>. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header. Example usage:
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXType());  <i>// alerts <em>'textfield'</em></i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXType" ext:member="#getXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-getXTypes"></a><b><a href="source/Component.html#method-Ext.Component-getXTypes">getXTypes</a></b>()
    :
                                        String<div class="mdesc"><div class="short">Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the Ext...</div><div class="long"><p>Returns this Component's xtype hierarchy as a slash-delimited string. For a list of all
available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
alert(t.getXTypes());  <i>// alerts <em>'component/box/field/textfield'</em></i>
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The xtype hierarchy string</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#getXTypes" ext:member="#getXTypes" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hide"></a><b><a href="source/Component.html#method-Ext.Component-hide">hide</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Hide this component.</div><div class="long">Hide this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#hide" ext:member="#hide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-insertAtCursor"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-insertAtCursor">insertAtCursor</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Inserts the passed text at the current cursor position. Note: the editor must be initialized and activated&#13;
to insert...</div><div class="long">Inserts the passed text at the current cursor position. Note: the editor must be initialized and activated
to insert text.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-isDirty"></a><b><a href="source/Field.html#method-Ext.form.Field-isDirty">isDirty</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the value of this Field has been changed from its original value.
Will return false if the field is d...</div><div class="long"><p>Returns true if the value of this Field has been changed from its original value.
Will return false if the field is disabled or has not been rendered yet.</p>
<p>Note that if the owning <a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">form</a> was configured with
<a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">Ext.form.BasicForm</a>.<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-trackResetOnLoad" ext:member="trackResetOnLoad" ext:cls="Ext.form.BasicForm">trackResetOnLoad</a>
then the <i>original value</i> is updated when the values are loaded by
<a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">Ext.form.BasicForm</a>.<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-setValues" ext:member="setValues" ext:cls="Ext.form.BasicForm">setValues</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this field has been changed from its original value (and
is not disabled), false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#isDirty" ext:member="#isDirty" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-isValid"></a><b><a href="source/Field.html#method-Ext.form.Field-isValid">isValid</a></b>(&nbsp;<code>Boolean&nbsp;preventMark</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns whether or not the field value is currently valid</div><div class="long">Returns whether or not the field value is currently valid<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>preventMark</code> : Boolean<div class="sub-desc">True to disable marking the field invalid</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the value is valid, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#isValid" ext:member="#isValid" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isVisible"></a><b><a href="source/Component.html#method-Ext.Component-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this component is visible.</div><div class="long">Returns true if this component is visible.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component is visible, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isVisible" ext:member="#isVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-isXType"></a><b><a href="source/Component.html#method-Ext.Component-isXType">isXType</a></b>(&nbsp;<code>String&nbsp;xtype</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;shallow</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from th...</div><div class="long"><p>Tests whether or not this Component is of a specific xtype. This can test whether this Component is descended
from the xtype (default) or whether it is directly of the xtype specified (shallow = true).</p>
<p><b>If using your own subclasses, be aware that a Component must register its own xtype
to participate in determination of inherited xtypes.</b></p>
<p>For a list of all available xtypes, see the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> header.</p>
<p>Example usage:</p>
<pre><code><b>var</b> t = <b>new</b> Ext.form.TextField();
<b>var</b> isText = t.isXType(<em>'textfield'</em>);        <i>// true</i>
<b>var</b> isBoxSubclass = t.isXType(<em>'box'</em>);       <i>// true, descended from BoxComponent</i>
<b>var</b> isBoxInstance = t.isXType(<em>'box'</em>, true); <i>// false, not a direct BoxComponent instance</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xtype</code> : String<div class="sub-desc">The xtype to check for this Component</div></li><li><code>shallow</code> : Boolean<div class="sub-desc">(optional) False to check whether this Component is descended from the xtype (this is
the default), or true to check whether this Component is directly of the specified xtype.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this component descends from the specified xtype, false otherwise.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#isXType" ext:member="#isXType" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-nextSibling"></a><b><a href="source/Component.html#method-Ext.Component-nextSibling">nextSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the next component in the owning container</div><div class="long">Returns the next component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#nextSibling" ext:member="#nextSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-previousSibling"></a><b><a href="source/Component.html#method-Ext.Component-previousSibling">previousSibling</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Returns the previous component in the owning container</div><div class="long">Returns the previous component in the owning container<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#previousSibling" ext:member="#previousSibling" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-pushValue"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-pushValue">pushValue</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Protected method that will not generally be called directly. Pushes the value of the textarea&#13;
into the iframe editor...</div><div class="long">Protected method that will not generally be called directly. Pushes the value of the textarea
into the iframe editor.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-relayCmd"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-relayCmd">relayCmd</a></b>(&nbsp;<code>String&nbsp;cmd</code>,&nbsp;<span title="Optional" class="optional">[<code>String/Boolean&nbsp;value</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes a Midas editor command on the editor document and performs necessary focus and&#13;
toolbar updates. This should...</div><div class="long">Executes a Midas editor command on the editor document and performs necessary focus and
toolbar updates. <b>This should only be called after the editor is initialized.</b><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cmd</code> : String<div class="sub-desc">The Midas command</div></li><li><code>value</code> : String/Boolean<div class="sub-desc">(optional) The value to pass to the command (defaults to null)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-removeClass"></a><b><a href="source/Component.html#method-Ext.Component-removeClass">removeClass</a></b>(&nbsp;<code>string&nbsp;cls</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Removes a CSS class from the component's underlying element.</div><div class="long">Removes a CSS class from the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cls</code> : string<div class="sub-desc">The CSS class name to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#removeClass" ext:member="#removeClass" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#method-Ext.Component-render">render</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Element/HTMLElement/String&nbsp;container</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String/Number&nbsp;position</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Render this Component into the passed HTML element.
If you are using a Container object to house this Component, then...</div><div class="long"><p>Render this Component into the passed HTML element.</p>
<p><b>If you are using a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a> object to house this Component, then
do not use the render method.</b></p>
<p>A Container's child Components are rendered by that Container's
<a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager when the Container is first rendered.</p>
<p>Certain layout managers allow dynamic addition of child components. Those that do
include <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>, <a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">Ext.layout.AnchorLayout</a>,
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>, <a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">Ext.layout.TableLayout</a>.</p>
<p>If the Container is already rendered when a new child Component is added, you may need to call
the Container's <a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a> to refresh the view which causes any
unrendered child Components to be rendered. This is required so that you can add multiple
child components if needed while only refreshing the layout once.</p>
<p>When creating complex UIs, it is important to remember that sizing and positioning
of child items is the responsibility of the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> manager.
If you expect child items to be sized in response to user interactions, you must
configure the Container with a layout manager which creates and manages the type of layout you
have in mind.</p>
<p><b>Omitting the Container's <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a> config means that a basic
layout manager is used which does nothing but render child components sequentially into the
Container. No sizing or positioning will be performed in this situation.</b></p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>container</code> : Element/HTMLElement/String<div class="sub-desc">(optional) The element this Component should be
rendered into. If it is being created from existing markup, this should be omitted.</div></li><li><code>position</code> : String/Number<div class="sub-desc">(optional) The element ID or DOM node index within the container <b>before</b>
which this component will be inserted (defaults to appending to the end of the container)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-reset"></a><b><a href="source/Field.html#method-Ext.form.Field-reset">reset</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resets the current field value to the originally loaded value and clears any validation messages.
See Ext.form.BasicF...</div><div class="long">Resets the current field value to the originally loaded value and clears any validation messages.
See <a href="output/Ext.form.BasicForm.html" ext:cls="Ext.form.BasicForm">Ext.form.BasicForm</a>.<a href="output/Ext.form.BasicForm.html#Ext.form.BasicForm-trackResetOnLoad" ext:member="trackResetOnLoad" ext:cls="Ext.form.BasicForm">trackResetOnLoad</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#reset" ext:member="#reset" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setHeight"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setHeight">setHeight</a></b>(&nbsp;<code>Number&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the height of the component.  This method fires the resize event.</div><div class="long">Sets the height of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>height</code> : Number<div class="sub-desc">The new height to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style.</li>
<li><i>undefined</i> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setHeight" ext:member="#setHeight" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPagePosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPagePosition">setPagePosition</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the page XY position of the component.  To set the left and top instead, use setPosition.
This method fires the ...</div><div class="long">Sets the page XY position of the component.  To set the left and top instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPosition" ext:member="setPosition" ext:cls="Ext.BoxComponent">setPosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPagePosition" ext:member="#setPagePosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setPosition"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setPosition">setPosition</a></b>(&nbsp;<code>Number&nbsp;left</code>,&nbsp;<code>Number&nbsp;top</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the left and top of the component.  To set the page XY position instead, use setPagePosition.
This method fires ...</div><div class="long">Sets the left and top of the component.  To set the page XY position instead, use <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-setPagePosition" ext:member="setPagePosition" ext:cls="Ext.BoxComponent">setPagePosition</a>.
This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-move" ext:member="move" ext:cls="Ext.BoxComponent">move</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : Number<div class="sub-desc">The new left</div></li><li><code>top</code> : Number<div class="sub-desc">The new top</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setPosition" ext:member="#setPosition" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-setRawValue"></a><b><a href="source/Field.html#method-Ext.form.Field-setRawValue">setRawValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Sets the underlying DOM field's value directly, bypassing validation.  To set the value with validation see setValue.</div><div class="long">Sets the underlying DOM field's value directly, bypassing validation.  To set the value with validation see <a href="output/Ext.form.Field.html#Ext.form.Field-setValue" ext:member="setValue" ext:cls="Ext.form.Field">setValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to set</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">value The field value that is set</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#setRawValue" ext:member="#setRawValue" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setSize">setSize</a></b>(&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<code>Mixed&nbsp;height</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width and height of this BoxComponent. This method fires the resize event. This method can accept
either wid...</div><div class="long">Sets the width and height of this BoxComponent. This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event. This method can accept
either width and height as separate arguments, or you can pass a size object like <code>{width:10, height:20}</code>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Mixed<div class="sub-desc">The new width to set. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
<li>A size object in the format <code>{width: widthValue, height: heightValue}</code>.</li>
<li><code>undefined</code> to leave the width unchanged.</li>
</ul></div></div></li><li><code>height</code> : Mixed<div class="sub-desc">The new height to set (not required if a size object is passed as the first arg).
This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
<li><code>undefined</code> to leave the height unchanged.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setSize" ext:member="#setSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-setValue"></a><b><a href="source/Field.html#method-Ext.form.Field-setValue">setValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Ext.form.Field<div class="mdesc"><div class="short">Sets a data value into the field and validates it.  To set the value directly without validation see setRawValue.</div><div class="long">Sets a data value into the field and validates it.  To set the value directly without validation see <a href="output/Ext.form.Field.html#Ext.form.Field-setRawValue" ext:member="setRawValue" ext:cls="Ext.form.Field">setRawValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to set</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.form.Field</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#setValue" ext:member="#setValue" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-setVisible"></a><b><a href="source/Component.html#method-Ext.Component-setVisible">setVisible</a></b>(&nbsp;<code>Boolean&nbsp;visible</code>&nbsp;)
    :
                                        Ext.Component<div class="mdesc"><div class="short">Convenience function to hide or show this component by boolean.</div><div class="long">Convenience function to hide or show this component by boolean.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>visible</code> : Boolean<div class="sub-desc">True to show, false to hide</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#setVisible" ext:member="#setVisible" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-setWidth"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-setWidth">setWidth</a></b>(&nbsp;<code>Number&nbsp;width</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the width of the component.  This method fires the resize event.</div><div class="long">Sets the width of the component.  This method fires the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-resize" ext:member="resize" ext:cls="Ext.BoxComponent">resize</a> event.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Number<div class="sub-desc">The new width to setThis may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in the <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-getEl" ext:member="getEl" ext:cls="Ext.BoxComponent">Element</a>'s <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">Ext.Element.defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style.</li>
</ul></div></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#setWidth" ext:member="#setWidth" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-show"></a><b><a href="source/Component.html#method-Ext.Component-show">show</a></b>()
    :
                                        Ext.Component<div class="mdesc"><div class="short">Show this component.</div><div class="long">Show this component.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Component</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#show" ext:member="#show" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-syncSize"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-syncSize">syncSize</a></b>()
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Force the component's size to recalculate based on the underlying element's current height and width.</div><div class="long">Force the component's size to recalculate based on the underlying element's current height and width.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#syncSize" ext:member="#syncSize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-syncValue"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-syncValue">syncValue</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Protected method that will not generally be called directly. Syncs the contents&#13;
of the editor iframe with the textar...</div><div class="long">Protected method that will not generally be called directly. Syncs the contents
of the editor iframe with the textarea.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-toggleSourceEdit"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-toggleSourceEdit">toggleSourceEdit</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;sourceEdit</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Toggles the editor between standard and source edit mode.</div><div class="long">Toggles the editor between standard and source edit mode.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>sourceEdit</code> : Boolean<div class="sub-desc">(optional) True for source edit, false for standard</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-updateBox"></a><b><a href="source/BoxComponent.html#method-Ext.BoxComponent-updateBox">updateBox</a></b>(&nbsp;<code>Object&nbsp;box</code>&nbsp;)
    :
                                        Ext.BoxComponent<div class="mdesc"><div class="short">Sets the current box measurements of the component's underlying element.</div><div class="long">Sets the current box measurements of the component's underlying element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>box</code> : Object<div class="sub-desc">An object in the format {x, y, width, height}</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.BoxComponent</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#updateBox" ext:member="#updateBox" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-updateToolbar"></a><b><a href="source/HtmlEditor.html#method-Ext.form.HtmlEditor-updateToolbar">updateToolbar</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Protected method that will not generally be called directly. It triggers&#13;
a toolbar update by reading the markup stat...</div><div class="long">Protected method that will not generally be called directly. It triggers
a toolbar update by reading the markup state of the current selection in the editor.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr></tbody></table><a id="Ext.form.HtmlEditor-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-activate"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-activate">activate</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the editor is first receives the focus. Any insertion must wait&#13;
until after this event.</div><div class="long">Fires when the editor is first receives the focus. Any insertion must wait
until after this event.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-afterrender"></a><b><a href="source/Component.html#event-Ext.Component-afterrender">afterrender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component rendering is finished.</div><div class="long">Fires after the component rendering is finished.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#afterrender" ext:member="#afterrender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforedestroy"></a><b><a href="source/Component.html#event-Ext.Component-beforedestroy">beforedestroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is destroyed. Return false to stop the destroy.</div><div class="long">Fires before the component is destroyed. Return false to stop the destroy.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforedestroy" ext:member="#beforedestroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforehide"></a><b><a href="source/Component.html#event-Ext.Component-beforehide">beforehide</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is hidden. Return false to stop the hide.</div><div class="long">Fires before the component is hidden. Return false to stop the hide.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforehide" ext:member="#beforehide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-beforepush"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-beforepush">beforepush</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>,&nbsp;<code>String&nbsp;html</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the iframe editor is updated with content from the textarea. Return false&#13;
to cancel the push.</div><div class="long">Fires before the iframe editor is updated with content from the textarea. Return false
to cancel the push.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li><li><code>html</code> : String<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforerender"></a><b><a href="source/Component.html#event-Ext.Component-beforerender">beforerender</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is rendered. Return false to stop the render.</div><div class="long">Fires before the component is rendered. Return false to stop the render.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforerender" ext:member="#beforerender" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforeshow"></a><b><a href="source/Component.html#event-Ext.Component-beforeshow">beforeshow</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the component is shown. Return false to stop the show.</div><div class="long">Fires before the component is shown. Return false to stop the show.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforeshow" ext:member="#beforeshow" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestaterestore"></a><b><a href="source/Component.html#event-Ext.Component-beforestaterestore">beforestaterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is restored. Return false to stop the restore.</div><div class="long">Fires before the state of the component is restored. Return false to stop the restore.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. If this
event is not vetoed, then the state object is passed to <b><tt>applyState</tt></b>. By default,
that simply copies property values into this Component. The method maybe overriden to
provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestaterestore" ext:member="#beforestaterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-beforestatesave"></a><b><a href="source/Component.html#event-Ext.Component-beforestatesave">beforestatesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.</div><div class="long">Fires before the state of the component is saved to the configured state provider. Return false to stop the save.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#beforestatesave" ext:member="#beforestatesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-beforesync"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-beforesync">beforesync</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>,&nbsp;<code>String&nbsp;html</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the textarea is updated with content from the editor iframe. Return false&#13;
to cancel the sync.</div><div class="long">Fires before the textarea is updated with content from the editor iframe. Return false
to cancel the sync.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li><li><code>html</code> : String<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-destroy"></a><b><a href="source/Component.html#event-Ext.Component-destroy">destroy</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is destroyed.</div><div class="long">Fires after the component is destroyed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#destroy" ext:member="#destroy" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-disable"></a><b><a href="source/Component.html#event-Ext.Component-disable">disable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is disabled.</div><div class="long">Fires after the component is disabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#disable" ext:member="#disable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-editmodechange"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-editmodechange">editmodechange</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>,&nbsp;<code>Boolean&nbsp;sourceEdit</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the editor switches edit modes</div><div class="long">Fires when the editor switches edit modes<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li><li><code>sourceEdit</code> : Boolean<div class="sub-desc">True if source edit, false if standard editing.</div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-enable"></a><b><a href="source/Component.html#event-Ext.Component-enable">enable</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is enabled.</div><div class="long">Fires after the component is enabled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#enable" ext:member="#enable" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-hide"></a><b><a href="source/Component.html#event-Ext.Component-hide">hide</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is hidden.</div><div class="long">Fires after the component is hidden.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#hide" ext:member="#hide" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-initialize"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-initialize">initialize</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the editor is fully initialized (including the iframe)</div><div class="long">Fires when the editor is fully initialized (including the iframe)<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.Field-invalid"></a><b><a href="source/Field.html#event-Ext.form.Field-invalid">invalid</a></b> :
                                      (&nbsp;<code>Ext.form.Field&nbsp;this</code>,&nbsp;<code>String&nbsp;msg</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the field has been marked as invalid.</div><div class="long">Fires after the field has been marked as invalid.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.form.Field<div class="sub-desc"></div></li><li><code>msg</code> : String<div class="sub-desc">The validation message</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.form.Field.html#invalid" ext:member="#invalid" ext:cls="Ext.form.Field">Field</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-move"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-move">move</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is moved.</div><div class="long">Fires after the component is moved.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>x</code> : Number<div class="sub-desc">The new x position</div></li><li><code>y</code> : Number<div class="sub-desc">The new y position</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#move" ext:member="#move" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-push"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-push">push</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>,&nbsp;<code>String&nbsp;html</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the iframe editor is updated with content from the textarea.</div><div class="long">Fires when the iframe editor is updated with content from the textarea.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li><li><code>html</code> : String<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-render"></a><b><a href="source/Component.html#event-Ext.Component-render">render</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component markup is rendered.</div><div class="long">Fires after the component markup is rendered.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#render" ext:member="#render" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.BoxComponent-resize"></a><b><a href="source/BoxComponent.html#event-Ext.BoxComponent-resize">resize</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Number&nbsp;adjWidth</code>,&nbsp;<code>Number&nbsp;adjHeight</code>,&nbsp;<code>Number&nbsp;rawWidth</code>,&nbsp;<code>Number&nbsp;rawHeight</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is resized.</div><div class="long">Fires after the component is resized.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>adjWidth</code> : Number<div class="sub-desc">The box-adjusted width that was set</div></li><li><code>adjHeight</code> : Number<div class="sub-desc">The box-adjusted height that was set</div></li><li><code>rawWidth</code> : Number<div class="sub-desc">The width that was originally specified</div></li><li><code>rawHeight</code> : Number<div class="sub-desc">The height that was originally specified</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.BoxComponent.html#resize" ext:member="#resize" ext:cls="Ext.BoxComponent">BoxComponent</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-show"></a><b><a href="source/Component.html#event-Ext.Component-show">show</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the component is shown.</div><div class="long">Fires after the component is shown.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#show" ext:member="#show" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-staterestore"></a><b><a href="source/Component.html#event-Ext.Component-staterestore">staterestore</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is restored.</div><div class="long">Fires after the state of the component is restored.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values returned from the StateProvider. This is passed
to <b><tt>applyState</tt></b>. By default, that simply copies property values into this
Component. The method maybe overriden to provide custom state restoration.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#staterestore" ext:member="#staterestore" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Component-statesave"></a><b><a href="source/Component.html#event-Ext.Component-statesave">statesave</a></b> :
                                      (&nbsp;<code>Ext.Component&nbsp;this</code>,&nbsp;<code>Object&nbsp;state</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after the state of the component is saved to the configured state provider.</div><div class="long">Fires after the state of the component is saved to the configured state provider.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Ext.Component<div class="sub-desc"></div></li><li><code>state</code> : Object<div class="sub-desc">The hash of state values. This is determined by calling
<b><tt>getState()</tt></b> on the Component. This method must be provided by the
developer to return whetever representation of state is required, by default, Ext.Component
has a null implementation.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Component.html#statesave" ext:member="#statesave" ext:cls="Ext.Component">Component</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.HtmlEditor-sync"></a><b><a href="source/HtmlEditor.html#event-Ext.form.HtmlEditor-sync">sync</a></b> :
                                      (&nbsp;<code>HtmlEditor&nbsp;this</code>,&nbsp;<code>String&nbsp;html</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the textarea is updated with content from the editor iframe.</div><div class="long">Fires when the textarea is updated with content from the editor iframe.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : HtmlEditor<div class="sub-desc"></div></li><li><code>html</code> : String<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">HtmlEditor</td></tr></tbody></table></div>