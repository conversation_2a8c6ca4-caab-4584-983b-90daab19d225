<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.tree.TreeLoader-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.tree.TreeLoader-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.tree.TreeLoader-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.tree.TreeLoader-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.tree.TreeLoader"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">TreeLoader</pre></div><h1>Class <a href="source/TreeLoader.html#cls-Ext.tree.TreeLoader">Ext.tree.TreeLoader</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.tree</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TreeLoader.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TreeLoader.html#cls-Ext.tree.TreeLoader">TreeLoader</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable" ext:member="">Observable</a></td></tr></table><div class="description">A TreeLoader provides for lazy loading of an <a href="output/Ext.tree.TreeNode.html" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode</a>'s child
nodes from a specified URL. The response must be a JavaScript Array definition
whose elements are node definition objects. eg:
<pre><code>[{
        id: 1,
        text: <em>'A leaf Node'</em>,
        leaf: true
    },{
        id: 2,
        text: <em>'A folder Node'</em>,
        children: [{
            id: 3,
            text: <em>'A child Node'</em>,
            leaf: true
        }]
   }]</code></pre>
<br><br>
A server request is sent, and child nodes are loaded only when a node is expanded.
The loading node's id is passed to the server under the parameter name "node" to
enable the server to produce the correct child nodes.
<br><br>
To pass extra parameters, an event handler may be attached to the "beforeload"
event, and the parameters specified in the TreeLoader's baseParams property:
<pre><code>myTreeLoader.on(<em>"beforeload"</em>, <b>function</b>(treeLoader, node) {
        this.baseParams.category = node.attributes.category;
    }, this);</code></pre>
This would pass an HTTP parameter called "category" to the server containing
the value of the Node's "category" attribute.</div><div class="hr"></div><a id="Ext.tree.TreeLoader-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-baseAttrs"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-baseAttrs">baseAttrs</a></b> : Object<div class="mdesc"><div class="short">An object containing attributes to be added to all nodes&#13;
created by this loader. If the attributes sent by the serve...</div><div class="long">An object containing attributes to be added to all nodes
created by this loader. If the attributes sent by the server have an attribute in this object,
they take priority.</div></div></td><td class="msource">TreeLoader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-baseParams"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-baseParams">baseParams</a></b> : Object<div class="mdesc">An object containing properties which
specify HTTP parameters to be passed to each request for child nodes.</div></td><td class="msource">TreeLoader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-clearOnLoad"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-clearOnLoad">clearOnLoad</a></b> : Boolean<div class="mdesc">Default to true. Remove previously existing
child nodes before loading.</div></td><td class="msource">TreeLoader</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-dataUrl"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-dataUrl">dataUrl</a></b> : String<div class="mdesc"><div class="short">The URL from which to request a Json string which&#13;
specifies an array of node definition objects representing the chi...</div><div class="long">The URL from which to request a Json string which
specifies an array of node definition objects representing the child nodes
to be loaded.</div></div></td><td class="msource">TreeLoader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-directFn"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-directFn">directFn</a></b> : Function<div class="mdesc">Function to call when executing a request.</div></td><td class="msource">TreeLoader</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-paramOrder"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-paramOrder">paramOrder</a></b> : Array/String<div class="mdesc"><div class="short">Defaults to undefined. Only used when using directFn.&#13;
A list of params to be executed&#13;
server side.  Specify the par...</div><div class="long">Defaults to <tt>undefined</tt>. Only used when using directFn.
A list of params to be executed
server side.  Specify the params in the order in which they must be executed on the server-side
as either (1) an Array of String values, or (2) a String of params delimited by either whitespace,
comma, or pipe. For example,
any of the following would be acceptable:<pre><code>paramOrder: [<em>'param1'</em>,<em>'param2'</em>,<em>'param3'</em>]
paramOrder: <em>'param1 param2 param3'</em>
paramOrder: <em>'param1,param2,param3'</em>
paramOrder: <em>'param1|param2|param'</em></code></pre></div></div></td><td class="msource">TreeLoader</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-paramsAsHash"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-paramsAsHash">paramsAsHash</a></b> : Boolean<div class="mdesc"><div class="short">Only used when using directFn.&#13;
Send parameters as a collection of named arguments (defaults to false). Providing a&#13;
...</div><div class="long">Only used when using directFn.
Send parameters as a collection of named arguments (defaults to <tt>false</tt>). Providing a
<tt><a href="output/Ext.tree.TreeLoader.html#Ext.tree.TreeLoader-paramOrder" ext:member="paramOrder" ext:cls="Ext.tree.TreeLoader">paramOrder</a></tt> nullifies this configuration.</div></div></td><td class="msource">TreeLoader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-preloadChildren"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-preloadChildren">preloadChildren</a></b> : Boolean<div class="mdesc">If set to true, the loader recursively loads "children" attributes when doing the first load on nodes.</div></td><td class="msource">TreeLoader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-requestMethod"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-requestMethod">requestMethod</a></b> : String<div class="mdesc">The HTTP request method for loading data (defaults to the value of <a href="output/Ext.Ajax.html#Ext.Ajax-method" ext:member="method" ext:cls="Ext.Ajax">Ext.Ajax.method</a>).</div></td><td class="msource">TreeLoader</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-uiProviders"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-uiProviders">uiProviders</a></b> : Object<div class="mdesc"><div class="short">An object containing properties which&#13;
specify custom Ext.tree.TreeNodeUI implementations. If the optional&#13;
uiProvide...</div><div class="long">An object containing properties which
specify custom <a href="output/Ext.tree.TreeNodeUI.html" ext:cls="Ext.tree.TreeNodeUI">Ext.tree.TreeNodeUI</a> implementations. If the optional
<i>uiProvider</i> attribute of a returned child node is a string rather
than a reference to a TreeNodeUI implementation, then that string value
is used as a property name in the uiProviders object.</div></div></td><td class="msource">TreeLoader</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-url"></a><b><a href="source/TreeLoader.html#cfg-Ext.tree.TreeLoader-url">url</a></b> : String<div class="mdesc">Equivalent to <a href="output/Ext.tree.TreeLoader.html#Ext.tree.TreeLoader-dataUrl" ext:member="dataUrl" ext:cls="Ext.tree.TreeLoader">dataUrl</a>.</div></td><td class="msource">TreeLoader</td></tr></tbody></table><a id="Ext.tree.TreeLoader-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.tree.TreeLoader-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-TreeLoader"></a><b><a href="source/TreeLoader.html#cls-Ext.tree.TreeLoader">TreeLoader</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short">Creates a new Treeloader.</div><div class="long">Creates a new Treeloader.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">A config object containing config properties.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeLoader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-createNode"></a><b><a href="source/TreeLoader.html#method-Ext.tree.TreeLoader-createNode">createNode</a></b>(&nbsp;<code>attr&nbsp;{Object}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Override this function for custom TreeNode node implementation, or to&#13;
modify the attributes at creation time.&#13;
Examp...</div><div class="long"><p>Override this function for custom TreeNode node implementation, or to
modify the attributes at creation time.</p>
Example:<code><pre>
<b>new</b> Ext.tree.TreePanel({
    ...
    <b>new</b> Ext.tree.TreeLoader({
        url: <em>'dataUrl'</em>,
        createNode: <b>function</b>(attr) {
<i>//          Allow consolidation consignments to have
</i>
<i>//          consignments dropped into them.
</i>
            <b>if</b> (attr.isConsolidation) {
                attr.iconCls = <em>'x-consol'</em>,
                attr.allowDrop = true;
            }
            <b>return</b> Ext.tree.TreeLoader.prototype.call(this, attr);
        }
    }),
    ...
});
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{Object}</code> : attr<div class="sub-desc">The attributes from which to create the new node.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeLoader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-load"></a><b><a href="source/TreeLoader.html#method-Ext.tree.TreeLoader-load">load</a></b>(&nbsp;<code>Ext.tree.TreeNode&nbsp;node</code>,&nbsp;<code>Function&nbsp;callback</code>,&nbsp;<code>(Object)&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Load an Ext.tree.TreeNode from the URL specified in the constructor.&#13;
This is called automatically when a node is exp...</div><div class="long">Load an <a href="output/Ext.tree.TreeNode.html" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode</a> from the URL specified in the constructor.
This is called automatically when a node is expanded, but may be used to reload
a node (or append new children if the <a href="output/Ext.tree.TreeLoader.html#Ext.tree.TreeLoader-clearOnLoad" ext:member="clearOnLoad" ext:cls="Ext.tree.TreeLoader">clearOnLoad</a> option is false.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Ext.tree.TreeNode<div class="sub-desc"></div></li><li><code>callback</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : (Object)<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeLoader</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.tree.TreeLoader-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-beforeload"></a><b><a href="source/TreeLoader.html#event-Ext.tree.TreeLoader-beforeload">beforeload</a></b> :
                                      (&nbsp;<code>Object&nbsp;This</code>,&nbsp;<code>Object&nbsp;node</code>,&nbsp;<code>Object&nbsp;callback</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a network request is made to retrieve the Json text which specifies a node's children.</div><div class="long">Fires before a network request is made to retrieve the Json text which specifies a node's children.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>This</code> : Object<div class="sub-desc">TreeLoader object.</div></li><li><code>node</code> : Object<div class="sub-desc">The <a href="output/Ext.tree.TreeNode.html" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode</a> object being loaded.</div></li><li><code>callback</code> : Object<div class="sub-desc">The callback function specified in the <a href="output/Ext.tree.TreeLoader.html#Ext.tree.TreeLoader-load" ext:member="load" ext:cls="Ext.tree.TreeLoader">load</a> call.</div></li></ul></div></div></div></td><td class="msource">TreeLoader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-load"></a><b><a href="source/TreeLoader.html#event-Ext.tree.TreeLoader-load">load</a></b> :
                                      (&nbsp;<code>Object&nbsp;This</code>,&nbsp;<code>Object&nbsp;node</code>,&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the node has been successfuly loaded.</div><div class="long">Fires when the node has been successfuly loaded.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>This</code> : Object<div class="sub-desc">TreeLoader object.</div></li><li><code>node</code> : Object<div class="sub-desc">The <a href="output/Ext.tree.TreeNode.html" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode</a> object being loaded.</div></li><li><code>response</code> : Object<div class="sub-desc">The response object containing the data from the server.</div></li></ul></div></div></div></td><td class="msource">TreeLoader</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeLoader-loadexception"></a><b><a href="source/TreeLoader.html#event-Ext.tree.TreeLoader-loadexception">loadexception</a></b> :
                                      (&nbsp;<code>Object&nbsp;This</code>,&nbsp;<code>Object&nbsp;node</code>,&nbsp;<code>Object&nbsp;response</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires if the network request failed.</div><div class="long">Fires if the network request failed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>This</code> : Object<div class="sub-desc">TreeLoader object.</div></li><li><code>node</code> : Object<div class="sub-desc">The <a href="output/Ext.tree.TreeNode.html" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode</a> object being loaded.</div></li><li><code>response</code> : Object<div class="sub-desc">The response object containing the data from the server.</div></li></ul></div></div></div></td><td class="msource">TreeLoader</td></tr></tbody></table></div>