<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Updater.BasicRenderer-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Updater.BasicRenderer-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Updater.BasicRenderer-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Updater.BasicRenderer"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/UpdateManager.html#cls-Ext.Updater.BasicRenderer">Ext.Updater.BasicRenderer</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">UpdateManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/UpdateManager.html#cls-Ext.Updater.BasicRenderer">Updater.BasicRenderer</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Default Content renderer. Updates the elements innerHTML with the responseText.</div><div class="hr"></div><a id="Ext.Updater.BasicRenderer-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.Updater.BasicRenderer-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.BasicRenderer-render"></a><b><a href="source/UpdateManager.html#method-Ext.Updater.BasicRenderer-render">render</a></b>(&nbsp;<code>Ext.Element&nbsp;el</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Updater&nbsp;updateManager</code>,&nbsp;<code>Function&nbsp;callback</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">This is called when the transaction is completed and it's time to update the element - The BasicRenderer
updates the ...</div><div class="long">This is called when the transaction is completed and it's time to update the element - The BasicRenderer
updates the elements innerHTML with the responseText - To perform a custom render (i.e. XML or JSON processing),
create an object with a "render(el, response)" method and pass it to setRenderer on the Updater.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Ext.Element<div class="sub-desc">The element being rendered</div></li><li><code>response</code> : Object<div class="sub-desc">The XMLHttpRequest object</div></li><li><code>updateManager</code> : Updater<div class="sub-desc">The calling update manager</div></li><li><code>callback</code> : Function<div class="sub-desc">A callback that will need to be called if loadScripts is true on the Updater</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater.BasicRenderer</td></tr></tbody></table><a id="Ext.Updater.BasicRenderer-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>