<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.LoadMask-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.LoadMask-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.LoadMask-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.LoadMask-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.LoadMask"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/LoadMask.html#cls-Ext.LoadMask">Ext.LoadMask</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">LoadMask.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/LoadMask.html#cls-Ext.LoadMask">LoadMask</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">A simple utility class for generically masking elements while loading data.  If the <a href="output/Ext.LoadMask.html#Ext.LoadMask-store" ext:member="store" ext:cls="Ext.LoadMask">store</a>
config option is specified, the masking will be automatically synchronized with the store's loading
process and the mask element will be cached for reuse.  For all other elements, this mask will replace the
element's Updater load indicator and will be destroyed after the initial load.
<p>Example usage:</p>
<pre><code><i>// Basic mask:</i>
<b>var</b> myMask = <b>new</b> Ext.LoadMask(Ext.getBody(), {msg:<em>"Please wait..."</em>});
myMask.show();</code></pre></div><div class="hr"></div><a id="Ext.LoadMask-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-msg"></a><b><a href="source/LoadMask.html#cfg-Ext.LoadMask-msg">msg</a></b> : String<div class="mdesc">The text to display in a centered loading message box (defaults to 'Loading...')</div></td><td class="msource">LoadMask</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-msgCls"></a><b><a href="source/LoadMask.html#cfg-Ext.LoadMask-msgCls">msgCls</a></b> : String<div class="mdesc">The CSS class to apply to the loading message element (defaults to "x-mask-loading")</div></td><td class="msource">LoadMask</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-removeMask"></a><b><a href="source/LoadMask.html#cfg-Ext.LoadMask-removeMask">removeMask</a></b> : Boolean<div class="mdesc"><div class="short">True to create a single-use mask that is automatically destroyed after loading (useful for page loads),
False to pers...</div><div class="long">True to create a single-use mask that is automatically destroyed after loading (useful for page loads),
False to persist the mask element reference for multiple uses (e.g., for paged data widgets).  Defaults to false.</div></div></td><td class="msource">LoadMask</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-store"></a><b><a href="source/LoadMask.html#cfg-Ext.LoadMask-store">store</a></b> : Ext.data.Store<div class="mdesc"><div class="short">Optional Store to which the mask is bound. The mask is displayed when a load request is issued, and
hidden on either ...</div><div class="long">Optional Store to which the mask is bound. The mask is displayed when a load request is issued, and
hidden on either load sucess, or load fail.</div></div></td><td class="msource">LoadMask</td></tr></tbody></table><a id="Ext.LoadMask-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-disabled"></a><b><a href="source/LoadMask.html#prop-Ext.LoadMask-disabled">disabled</a></b> : Boolean<div class="mdesc">Read-only. True if the mask is currently disabled so that it will not be displayed (defaults to false)</div></td><td class="msource">LoadMask</td></tr></tbody></table><a id="Ext.LoadMask-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-LoadMask"></a><b><a href="source/LoadMask.html#cls-Ext.LoadMask">LoadMask</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new LoadMask</div><div class="long">Create a new LoadMask<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element or DOM node, or its id</div></li><li><code>config</code> : Object<div class="sub-desc">The config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">LoadMask</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-disable"></a><b><a href="source/LoadMask.html#method-Ext.LoadMask-disable">disable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Disables the mask to prevent it from being displayed</div><div class="long">Disables the mask to prevent it from being displayed<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">LoadMask</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-enable"></a><b><a href="source/LoadMask.html#method-Ext.LoadMask-enable">enable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Enables the mask so that it can be displayed</div><div class="long">Enables the mask so that it can be displayed<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">LoadMask</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-hide"></a><b><a href="source/LoadMask.html#method-Ext.LoadMask-hide">hide</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hide this LoadMask.</div><div class="long">Hide this LoadMask.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">LoadMask</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.LoadMask-show"></a><b><a href="source/LoadMask.html#method-Ext.LoadMask-show">show</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Show this LoadMask over the configured Element.</div><div class="long">Show this LoadMask over the configured Element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">LoadMask</td></tr></tbody></table><a id="Ext.LoadMask-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>