<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.SortTypes-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.SortTypes-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.SortTypes-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.SortTypes"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/SortTypes.html#cls-Ext.data.SortTypes">Ext.data.SortTypes</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">SortTypes.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/SortTypes.html#cls-Ext.data.SortTypes">SortTypes</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.data.SortTypes-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-stripTagsRE"></a><b><a href="source/SortTypes.html#prop-Ext.data.SortTypes-stripTagsRE">stripTagsRE</a></b> : RegExp<div class="mdesc">The regular expression used to strip tags</div></td><td class="msource">SortTypes</td></tr></tbody></table><a id="Ext.data.SortTypes-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-asDate"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-asDate">asDate</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Date sorting</div><div class="long">Date sorting<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-asFloat"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-asFloat">asFloat</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        Float<div class="mdesc"><div class="short">Float sorting</div><div class="long">Float sorting<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>Float</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-asInt"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-asInt">asInt</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Integer sorting</div><div class="long">Integer sorting<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-asText"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-asText">asText</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Strips all HTML tags to sort on text only</div><div class="long">Strips all HTML tags to sort on text only<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-asUCString"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-asUCString">asUCString</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Case insensitive string</div><div class="long">Case insensitive string<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-asUCText"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-asUCText">asUCText</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Strips all HTML tags to sort on text only - Case insensitive</div><div class="long">Strips all HTML tags to sort on text only - Case insensitive<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.SortTypes-none"></a><b><a href="source/SortTypes.html#method-Ext.data.SortTypes-none">none</a></b>(&nbsp;<code>Mixed&nbsp;s</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Default sort that does nothing</div><div class="long">Default sort that does nothing<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>s</code> : Mixed<div class="sub-desc">The value being converted</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">The comparison value</div></li></ul></div></div></div></td><td class="msource">SortTypes</td></tr></tbody></table><a id="Ext.data.SortTypes-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>