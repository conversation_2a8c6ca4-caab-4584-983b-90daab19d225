<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.CompositeElement-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.CompositeElement-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.CompositeElement-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.CompositeElement"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.CompositeElementLite.html" ext:member="" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">CompositeElement</pre></div><h1>Class <a href="source/CompositeElement.html#cls-Ext.CompositeElement">Ext.CompositeElement</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">CompositeElement.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/CompositeElement.html#cls-Ext.CompositeElement">CompositeElement</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.CompositeElementLite.html" ext:cls="Ext.CompositeElementLite" ext:member="">CompositeElementLite</a></td></tr></table><div class="description">Standard composite class. Creates a Ext.Element for every element in the collection.
<br><br>
<b>NOTE: Although they are not listed, this class supports all of the set/update methods of Ext.Element. All Ext.Element
actions will be performed on all the elements in this collection.</b>
<br><br>
All methods return <i>this</i> and can be chained.
 <pre><code><b>var</b> els = Ext.select(<em>"#some-el div.some-class"</em>, true);
 <i>// or select directly from an existing element
</i>
 <b>var</b> el = Ext.get(<em>'some-el'</em>);
 el.select(<em>'div.some-class'</em>, true);

 els.setWidth(100); <i>// all elements become 100 width
</i>
 els.hide(true); <i>// all elements fade out and hide
</i>
 <i>// or
</i>
 els.setWidth(100).hide(true);</code></pre></div><div class="hr"></div><a id="Ext.CompositeElement-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.CompositeElement-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElement-add"></a><b><a href="source/CompositeElement.html#method-Ext.CompositeElement-add">add</a></b>(&nbsp;<code>String/Array&nbsp;els</code>&nbsp;)
    :
                                        CompositeElement<div class="mdesc"><div class="short">Adds elements to this composite.</div><div class="long">Adds elements to this composite.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>els</code> : String/Array<div class="sub-desc">A string CSS selector, an array of elements or an element</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElement</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">CompositeElement</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-clear"></a><b><a href="source/CompositeElementLite.html#method-Ext.CompositeElementLite-clear">clear</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all elements.</div><div class="long">Removes all elements.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#clear" ext:member="#clear" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-contains"></a><b><a href="source/CompositeElementLite-more.html#method-Ext.CompositeElementLite-contains">contains</a></b>(&nbsp;<code>el&nbsp;{Mixed}</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this composite contains the passed element</div><div class="long">Returns true if this composite contains the passed element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{Mixed}</code> : el<div class="sub-desc">The id of an element, or an Ext.Element, or an HtmlElement to find within the composite collection.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#contains" ext:member="#contains" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElement-each"></a><b><a href="source/CompositeElement.html#method-Ext.CompositeElement-each">each</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        CompositeElement<div class="mdesc"><div class="short">Calls the passed function passing (el, this, index) for each element in this composite.</div><div class="long">Calls the passed function passing (el, this, index) for each element in this composite.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The <i>this</i> object (defaults to the element)</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElement</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">CompositeElement</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-fill"></a><b><a href="source/CompositeElementLite-more.html#method-Ext.CompositeElementLite-fill">fill</a></b>(&nbsp;<code>String/Array&nbsp;els</code>&nbsp;)
    :
                                        CompositeElement<div class="mdesc"><div class="short">Clears this composite and adds the elements returned by the passed selector.</div><div class="long">Clears this composite and adds the elements returned by the passed selector.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>els</code> : String/Array<div class="sub-desc">A string CSS selector, an array of elements or an element</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElement</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#fill" ext:member="#fill" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-filter"></a><b><a href="source/CompositeElementLite-more.html#method-Ext.CompositeElementLite-filter">filter</a></b>(&nbsp;<code>String&nbsp;selector</code>&nbsp;)
    :
                                        CompositeElement<div class="mdesc"><div class="short">Filters this composite to only elements that match the passed selector.</div><div class="long">Filters this composite to only elements that match the passed selector.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">A string CSS selector</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElement</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#filter" ext:member="#filter" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-first"></a><b><a href="source/CompositeElementLite-more.html#method-Ext.CompositeElementLite-first">first</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the first Element</div><div class="long">Returns the first Element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#first" ext:member="#first" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-getCount"></a><b><a href="source/CompositeElementLite.html#method-Ext.CompositeElementLite-getCount">getCount</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the number of elements in this composite</div><div class="long">Returns the number of elements in this composite<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#getCount" ext:member="#getCount" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-handleError"></a><b><a href="source/Error.html#method-Ext.CompositeElementLite-handleError">handleError</a></b>(&nbsp;<code>Object/Error&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Framework-wide error-handler.  Developers can override this method to provide custom exception-handling.  Framework
e...</div><div class="long">Framework-wide error-handler.  Developers can override this method to provide custom exception-handling.  Framework
errors will often extend from the base Ext.Error class.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Object/Error<div class="sub-desc">The thrown exception object.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#handleError" ext:member="#handleError" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-indexOf"></a><b><a href="source/CompositeElementLite.html#method-Ext.CompositeElementLite-indexOf">indexOf</a></b>(&nbsp;<code>el&nbsp;{Mixed}</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Find the index of the passed element within the composite collection.</div><div class="long">Find the index of the passed element within the composite collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{Mixed}</code> : el<div class="sub-desc">The id of an element, or an Ext.Element, or an HtmlElement to find within the composite collection.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The index of the passed Ext.Element in the composite collection, or -1 if not found.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#indexOf" ext:member="#indexOf" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElement-item"></a><b><a href="source/CompositeElement.html#method-Ext.CompositeElement-item">item</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the Element object at the specified index</div><div class="long">Returns the Element object at the specified index<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">CompositeElement</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-last"></a><b><a href="source/CompositeElementLite-more.html#method-Ext.CompositeElementLite-last">last</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the last Element</div><div class="long">Returns the last Element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#last" ext:member="#last" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-removeElement"></a><b><a href="source/CompositeElementLite-more.html#method-Ext.CompositeElementLite-removeElement">removeElement</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;removeDom</code>]</span>&nbsp;)
    :
                                        CompositeElement<div class="mdesc"><div class="short">Removes the specified element(s).</div><div class="long">Removes the specified element(s).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The id of an element, the Element itself, the index of the element in this composite
or an array of any of those.</div></li><li><code>removeDom</code> : Boolean<div class="sub-desc">(optional) True to also remove the element from the document</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElement</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#removeElement" ext:member="#removeElement" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.CompositeElementLite-replaceElement"></a><b><a href="source/CompositeElementLite.html#method-Ext.CompositeElementLite-replaceElement">replaceElement</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Mixed&nbsp;replacement</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;domReplace</code>]</span>&nbsp;)
    :
                                        CompositeElement<div class="mdesc"><div class="short">Replaces the specified element with the passed element.</div><div class="long">Replaces the specified element with the passed element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The id of an element, the Element itself, the index of the element in this composite
to replace.</div></li><li><code>replacement</code> : Mixed<div class="sub-desc">The id of an element or the Element itself.</div></li><li><code>domReplace</code> : Boolean<div class="sub-desc">(Optional) True to remove and replace the element in the document too.</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElement</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.CompositeElementLite.html#replaceElement" ext:member="#replaceElement" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a></td></tr></tbody></table><a id="Ext.CompositeElement-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>