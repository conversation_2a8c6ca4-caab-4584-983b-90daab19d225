<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Layer-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Layer-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Layer-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.Layer-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Layer"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.Element.html" ext:member="" ext:cls="Ext.Element">Element</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">Layer</pre></div><h1>Class <a href="source/Layer.html#cls-Ext.Layer">Ext.Layer</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Layer.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Layer.html#cls-Ext.Layer">Layer</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.Element.html" ext:cls="Ext.Element" ext:member="">Element</a></td></tr></table><div class="description">An extended <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> object that supports a shadow and shim, constrain to viewport and
automatic maintaining of shadow/shim positions.</div><div class="hr"></div><a id="Ext.Layer-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-cls"></a><b><a href="source/Layer.html#cfg-Ext.Layer-cls">cls</a></b> : String<div class="mdesc">CSS class to add to the element</div></td><td class="msource">Layer</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-constrain"></a><b><a href="source/Layer.html#cfg-Ext.Layer-constrain">constrain</a></b> : Boolean<div class="mdesc">False to disable constrain to viewport (defaults to true)</div></td><td class="msource">Layer</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-dh"></a><b><a href="source/Layer.html#cfg-Ext.Layer-dh">dh</a></b> : Object<div class="mdesc">DomHelper object config to create element with (defaults to {tag: "div", cls: "x-layer"}).</div></td><td class="msource">Layer</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-shadow"></a><b><a href="source/Layer.html#cfg-Ext.Layer-shadow">shadow</a></b> : String/Boolean<div class="mdesc"><div class="short">True to automatically create an Ext.Shadow, or a string indicating the
shadow's display Ext.Shadow.mode. False to dis...</div><div class="long">True to automatically create an <a href="output/Ext.Shadow.html" ext:cls="Ext.Shadow">Ext.Shadow</a>, or a string indicating the
shadow's display <a href="output/Ext.Shadow.html#Ext.Shadow-mode" ext:member="mode" ext:cls="Ext.Shadow">Ext.Shadow.mode</a>. False to disable the shadow. (defaults to false)</div></div></td><td class="msource">Layer</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-shadowOffset"></a><b><a href="source/Layer.html#cfg-Ext.Layer-shadowOffset">shadowOffset</a></b> : Number<div class="mdesc">Number of pixels to offset the shadow (defaults to 4)</div></td><td class="msource">Layer</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-shim"></a><b><a href="source/Layer.html#cfg-Ext.Layer-shim">shim</a></b> : Boolean<div class="mdesc">False to disable the iframe shim in browsers which need one (defaults to true)</div></td><td class="msource">Layer</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-zindex"></a><b><a href="source/Layer.html#cfg-Ext.Layer-zindex">zindex</a></b> : Number<div class="mdesc">Starting z-index (defaults to 11000)</div></td><td class="msource">Layer</td></tr></tbody></table><a id="Ext.Layer-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DISPLAY"></a><b><a href="source/Element.fx.html#prop-Ext.Element-DISPLAY">DISPLAY</a></b> : Number<div class="mdesc">Visibility mode constant for use with <a href="output/Ext.Element.html#Ext.Element-setVisibilityMode" ext:member="setVisibilityMode" ext:cls="Ext.Element">setVisibilityMode</a>. Use display to hide element</div></td><td class="msource"><a href="output/Ext.Element.html#DISPLAY" ext:member="#DISPLAY" ext:cls="Ext.Element">Element</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-VISIBILITY"></a><b><a href="source/Element.fx.html#prop-Ext.Element-VISIBILITY">VISIBILITY</a></b> : Number<div class="mdesc">Visibility mode constant for use with <a href="output/Ext.Element.html#Ext.Element-setVisibilityMode" ext:member="setVisibilityMode" ext:cls="Ext.Element">setVisibilityMode</a>. Use visibility to hide element</div></td><td class="msource"><a href="output/Ext.Element.html#VISIBILITY" ext:member="#VISIBILITY" ext:cls="Ext.Element">Element</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-autoBoxAdjust"></a><b><a href="source/Element.html#prop-Ext.Element-autoBoxAdjust">autoBoxAdjust</a></b> : Object<div class="mdesc">true to automatically adjust width and height settings for box-model issues (default to true)</div></td><td class="msource"><a href="output/Ext.Element.html#autoBoxAdjust" ext:member="#autoBoxAdjust" ext:cls="Ext.Element">Element</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-defaultUnit"></a><b><a href="source/Element.html#prop-Ext.Element-defaultUnit">defaultUnit</a></b> : String<div class="mdesc">The default unit to append to CSS values where a unit isn't provided (defaults to px).</div></td><td class="msource"><a href="output/Ext.Element.html#defaultUnit" ext:member="#defaultUnit" ext:cls="Ext.Element">Element</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-dom"></a><b><a href="source/Element.html#prop-Ext.Element-dom">dom</a></b> : HTMLElement<div class="mdesc">The DOM element</div></td><td class="msource"><a href="output/Ext.Element.html#dom" ext:member="#dom" ext:cls="Ext.Element">Element</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-id"></a><b><a href="source/Element.html#prop-Ext.Element-id">id</a></b> : String<div class="mdesc">The DOM element ID</div></td><td class="msource"><a href="output/Ext.Element.html#id" ext:member="#id" ext:cls="Ext.Element">Element</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-originalDisplay"></a><b><a href="source/Element.fx.html#prop-Ext.Element-originalDisplay">originalDisplay</a></b> : String<div class="mdesc">The element's default display mode  (defaults to "")</div></td><td class="msource"><a href="output/Ext.Element.html#originalDisplay" ext:member="#originalDisplay" ext:cls="Ext.Element">Element</a></td></tr></tbody></table><a id="Ext.Layer-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-Layer"></a><b><a href="source/Layer.html#cls-Ext.Layer">Layer</a></b>(&nbsp;<code>Object&nbsp;config</code>,&nbsp;<span title="Optional" class="optional">[<code>String/HTMLElement&nbsp;existingEl</code>]</span>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">An object with config options.</div></li><li><code>existingEl</code> : String/HTMLElement<div class="sub-desc">(optional) Uses an existing DOM element. If the element is not found it creates it.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Layer</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addClass"></a><b><a href="source/Element.style.html#method-Ext.Element-addClass">addClass</a></b>(&nbsp;<code>String/Array&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Adds one or more CSS classes to the element. Duplicate classes are automatically filtered out.</div><div class="long">Adds one or more CSS classes to the element. Duplicate classes are automatically filtered out.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String/Array<div class="sub-desc">The CSS class to add, or an array of classes</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addClass" ext:member="#addClass" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addClassOnClick"></a><b><a href="source/Element.style-more.html#method-Ext.Element-addClassOnClick">addClassOnClick</a></b>(&nbsp;<code>String&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets up event handlers to add and remove a css class when the mouse is down and then up on this element (a click effe...</div><div class="long">Sets up event handlers to add and remove a css class when the mouse is down and then up on this element (a click effect)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addClassOnClick" ext:member="#addClassOnClick" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addClassOnFocus"></a><b><a href="source/Element.style-more.html#method-Ext.Element-addClassOnFocus">addClassOnFocus</a></b>(&nbsp;<code>String&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets up event handlers to add and remove a css class when this element has the focus</div><div class="long">Sets up event handlers to add and remove a css class when this element has the focus<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addClassOnFocus" ext:member="#addClassOnFocus" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addClassOnOver"></a><b><a href="source/Element.style-more.html#method-Ext.Element-addClassOnOver">addClassOnOver</a></b>(&nbsp;<code>String&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets up event handlers to add and remove a css class when the mouse is over this element</div><div class="long">Sets up event handlers to add and remove a css class when the mouse is over this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addClassOnOver" ext:member="#addClassOnOver" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addKeyListener"></a><b><a href="source/Element.keys.html#method-Ext.Element-addKeyListener">addKeyListener</a></b>(&nbsp;<code>Number/Array/Object/String&nbsp;key</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.KeyMap<div class="mdesc"><div class="short">Convenience method for constructing a KeyMap</div><div class="long">Convenience method for constructing a KeyMap<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : Number/Array/Object/String<div class="sub-desc">Either a string with the keys to listen for, the numeric key code, array of key codes or an object with the following options:
{key: (number or array), shift: (true/false), ctrl: (true/false), alt: (true/false)}</div></li><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.KeyMap</code><div class="sub-desc">The KeyMap created</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addKeyListener" ext:member="#addKeyListener" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addKeyMap"></a><b><a href="source/Element.keys.html#method-Ext.Element-addKeyMap">addKeyMap</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        Ext.KeyMap<div class="mdesc"><div class="short">Creates a KeyMap for this element</div><div class="long">Creates a KeyMap for this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The KeyMap config. See <a href="output/Ext.KeyMap.html" ext:cls="Ext.KeyMap">Ext.KeyMap</a> for more details</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.KeyMap</code><div class="sub-desc">The KeyMap created</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addKeyMap" ext:member="#addKeyMap" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-addListener"></a><b><a href="source/Element.html#method-Ext.Element-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Appends an event handler to this element.  The shorthand version on is equivalent.</div><div class="long">Appends an event handler to this element.  The shorthand version <a href="output/Ext.Element.html#Ext.Element-on" ext:member="on" ext:cls="Ext.Element">on</a> is equivalent.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to handle</div></li><li><code>fn</code> : Function<div class="sub-desc">The handler function the event invokes. This function is passed
the following parameters:<ul>
<li><b>evt</b> : EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">EventObject</a> describing the event.</div></li>
<li><b>el</b> : Element<div class="sub-desc">The <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> which was the target of the event.
Note that this may be filtered by using the <tt>delegate</tt> option.</div></li>
<li><b>o</b> : Object<div class="sub-desc">The options object from the addListener call.</div></li>
</ul></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to this Element.</b>.</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration properties.
This may contain any of the following properties:<ul>
<li><b>scope</b> Object : <div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to this Element.</b></div></li>
<li><b>delegate</b> String: <div class="sub-desc">A simple selector to filter the target or look for a descendant of the target. See below for additional details.</div></li>
<li><b>stopEvent</b> Boolean: <div class="sub-desc">True to stop the event. That is stop propagation, and prevent the default action.</div></li>
<li><b>preventDefault</b> Boolean: <div class="sub-desc">True to prevent the default action</div></li>
<li><b>stopPropagation</b> Boolean: <div class="sub-desc">True to prevent event propagation</div></li>
<li><b>normalized</b> Boolean: <div class="sub-desc">False to pass a browser event to the handler function instead of an Ext.EventObject</div></li>
<li><b>target</b> Ext.Element: <div class="sub-desc">The event will be passed to handler only when it has bubbled up to the specified target element</div></li>
<li><b>delay</b> Number: <div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> Boolean: <div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> Number: <div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
In the following examples, the shorthand form <a href="output/Ext.Element.html#Ext.Element-on" ext:member="on" ext:cls="Ext.Element">on</a> is used rather than the more verbose
addListener.  The two are equivalent.  Using the options argument, it is possible to combine different
types of listeners:<br>
<br>
A delayed, one-time listener that auto stops the event and adds a custom argument (forumId) to the
options object. The options object is available as the third parameter in the handler function.<div style="margin: 5px 20px 20px;">
Code:<pre><code>el.on(<em>'click'</em>, this.onClick, this, {
    single: true,
    delay: 100,
    stopEvent : true,
    forumId: 4
});</code></pre></p>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.</p>
<p>
Code:<pre><code>el.on({
    <em>'click'</em> : {
        fn: this.onClick,
        scope: this,
        delay: 100
    },
    <em>'mouseover'</em> : {
        fn: this.onMouseOver,
        scope: this
    },
    <em>'mouseout'</em> : {
        fn: this.onMouseOut,
        scope: this
    }
});</code></pre>
<p>
Or a shorthand syntax:<br>
Code:<pre><code></p>
el.on({
    <em>'click'</em> : this.onClick,
    <em>'mouseover'</em> : this.onMouseOver,
    <em>'mouseout'</em> : this.onMouseOut,
    scope: this
});</code></pre></p>
<p><b>delegate</b></p>
<p>This is a configuration option that you can pass along when registering a handler for
an event to assist with event delegation. Event delegation is a technique that is used to
reduce memory consumption and prevent exposure to memory-leaks. By registering an event
for a container element as opposed to each element within a container. By setting this
configuration option to a simple selector, the target element will be filtered to look for
a descendant of the target.
For example:<pre><code><i>// using this markup:</i>
&lt;div id=<em>'elId'</em>>
    &lt;p id=<em>'p1'</em>>paragraph one&lt;/p>
    &lt;p id=<em>'p2'</em> class=<em>'clickable'</em>>paragraph two&lt;/p>
    &lt;p id=<em>'p3'</em>>paragraph three&lt;/p>
&lt;/div>
<i>// utilize event delegation to registering just one handler on the container element: </i>
el = Ext.get(<em>'elId'</em>);
el.on(
    <em>'click'</em>,
    <b>function</b>(e,t) {
        <i>// handle click</i>
        console.info(t.id); <i>// <em>'p2'</em></i>
    },
    this,
    {
        <i>// filter the target element to be a descendant <b>with</b> the class <em>'clickable'</em></i>
        delegate: <em>'.clickable'</em> 
    }
);</code></pre></p></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#addListener" ext:member="#addListener" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-alignTo"></a><b><a href="source/Element.alignment.html#method-Ext.Element-alignTo">alignTo</a></b>(&nbsp;<code>Mixed&nbsp;element</code>,&nbsp;<code>String&nbsp;position</code>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;offsets</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Aligns this element with another element relative to the specified anchor points. If the other element is the&#13;
docume...</div><div class="long">Aligns this element with another element relative to the specified anchor points. If the other element is the
document it aligns it to the viewport.
The position parameter is optional, and can be specified in any one of the following formats:
<ul>
<li><b>Blank</b>: Defaults to aligning the element's top-left corner to the target's bottom-left corner ("tl-bl").</li>
<li><b>One anchor (deprecated)</b>: The passed anchor position is used as the target element's anchor point.
The element being aligned will position its top-left corner (tl) to that point.  <i>This method has been
deprecated in favor of the newer two anchor syntax below</i>.</li>
<li><b>Two anchors</b>: If two values from the table below are passed separated by a dash, the first value is used as the
element's anchor point, and the second value is used as the target's anchor point.</li>
</ul>
In addition to the anchor points, the position parameter also supports the "?" character.  If "?" is passed at the end of
the position string, the element will attempt to align as specified, but the position will be adjusted to constrain to
the viewport if necessary.  Note that the element being aligned might be swapped to align to a different position than
that specified in order to enforce the viewport constraints.
Following are all of the supported anchor positions:
<pre>
Value  Description
-----  -----------------------------
tl     The top left corner (default)
t      The center of the top edge
tr     The top right corner
l      The center of the left edge
c      In the center of the element
r      The center of the right edge
bl     The bottom left corner
b      The center of the bottom edge
br     The bottom right corner
</pre>
Example Usage:
<pre><code><i>// align el to other-el using the <b>default</b> positioning (<em>"tl-bl"</em>, non-constrained)
</i>
el.alignTo(<em>"other-el"</em>);

<i>// align the top left corner of el <b>with</b> the top right corner of other-el (constrained to viewport)
</i>
el.alignTo(<em>"other-el"</em>, <em>"tr?"</em>);

<i>// align the bottom right corner of el <b>with</b> the center left edge of other-el
</i>
el.alignTo(<em>"other-el"</em>, <em>"br-l?"</em>);

<i>// align the center of el <b>with</b> the bottom left corner of other-el and
</i>
<i>// adjust the x position by -6 pixels (and the y position by 0)
</i>
el.alignTo(<em>"other-el"</em>, <em>"c-bl"</em>, [-6, 0]);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : Mixed<div class="sub-desc">The element to align to.</div></li><li><code>position</code> : String<div class="sub-desc">The position to align to.</div></li><li><code>offsets</code> : Array<div class="sub-desc">(optional) Offset the positioning by [x, y]</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#alignTo" ext:member="#alignTo" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-anchorTo"></a><b><a href="source/Element.alignment.html#method-Ext.Element-anchorTo">anchorTo</a></b>(&nbsp;<code>Mixed&nbsp;element</code>,&nbsp;<code>String&nbsp;position</code>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;offsets</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Number&nbsp;monitorScroll</code>]</span>,&nbsp;<code>Function&nbsp;callback</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Anchors an element to another element and realigns it when the window is resized.</div><div class="long">Anchors an element to another element and realigns it when the window is resized.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : Mixed<div class="sub-desc">The element to align to.</div></li><li><code>position</code> : String<div class="sub-desc">The position to align to.</div></li><li><code>offsets</code> : Array<div class="sub-desc">(optional) Offset the positioning by [x, y]</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation or a standard Element animation config object</div></li><li><code>monitorScroll</code> : Boolean/Number<div class="sub-desc">(optional) True to monitor body scroll and reposition. If this parameter
is a number, it is used as the buffer delay (defaults to 50ms).</div></li><li><code>callback</code> : Function<div class="sub-desc">The function to call after the animation finishes</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#anchorTo" ext:member="#anchorTo" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-animate"></a><b><a href="source/Element.fx.html#method-Ext.Element-animate">animate</a></b>(&nbsp;<code>Object&nbsp;args</code>,&nbsp;<span title="Optional" class="optional">[<code>Float&nbsp;duration</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;onComplete</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;easing</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;animType</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Perform custom animation on this element.&#13;
&lt;ul class="mdetail-params"&gt;&#13;
Animation Properties&#13;
The Animation Control O...</div><div class="long">Perform custom animation on this element.
<div><ul class="mdetail-params">
<li><u>Animation Properties</u></li>
<p>The Animation Control Object enables gradual transitions for any member of an
element's style object that takes a numeric value including but not limited to
these properties:</p><div><ul class="mdetail-params">
<li><tt>bottom, top, left, right</tt></li>
<li><tt>height, width</tt></li>
<li><tt>margin, padding</tt></li>
<li><tt>borderWidth</tt></li>
<li><tt>opacity</tt></li>
<li><tt>fontSize</tt></li>
<li><tt>lineHeight</tt></li>
</ul></div>
<li><u>Animation Property Attributes</u></li>
<p>Each Animation Property is a config object with optional properties:</p>
<div><ul class="mdetail-params">
<li><tt>by</tt>*  : relative change - start at current value, change by this value</li>
<li><tt>from</tt> : ignore current value, start from this value</li>
<li><tt>to</tt>*  : start at current value, go to this value</li>
<li><tt>unit</tt> : any allowable unit specification</li>
<p>* do not specify both <tt>to</tt> and <tt>by</tt> for an animation property</p>
</ul></div>
<li><u>Animation Types</u></li>
<p>The supported animation types:</p><div><ul class="mdetail-params">
<li><tt>'run'</tt> : Default
<pre><code><b>var</b> el = Ext.get(<em>'complexEl'</em>);
el.animate(
    <i>// animation control object
</i>
    {
    	borderWidth: {to: 3, from: 0},
    	opacity: {to: .3, from: 1},
    	height: {to: 50, from: el.getHeight()},
    	width: {to: 300, from: el.getWidth()},
    	top  : {by: - 100, unit: <em>'px'</em>},
    },
    0.35,      <i>// animation duration
</i>
    null,      <i>// callback
</i>
    <em>'easeOut'</em>, <i>// easing method
</i>
    <em>'run'</em>      <i>// animation type (<em>'run'</em>,<em>'color'</em>,<em>'motion'</em>,<em>'scroll'</em>)    
</i>
);</code></pre>
</li>
<li><tt>'color'</tt>
<p>Animates transition of background, text, or border colors.</p>
<pre><code>el.animate(
    <i>// animation control object
</i>
    {
        color: { to: <em>'#06e'</em> },
        backgroundColor: { to: <em>'#e06'</em> }
    },
    0.35,      <i>// animation duration
</i>
    null,      <i>// callback
</i>
    <em>'easeOut'</em>, <i>// easing method
</i>
    <em>'color'</em>    <i>// animation type (<em>'run'</em>,<em>'color'</em>,<em>'motion'</em>,<em>'scroll'</em>)    
</i>
);</code></pre> 
</li>
<li><tt>'motion'</tt>
<p>Animates the motion of an element to/from specific points using optional bezier
way points during transit.</p>
<pre><code>el.animate(
    <i>// animation control object
</i>
    {
    	borderWidth: {to: 3, from: 0},
    	opacity: {to: .3, from: 1},
    	height: {to: 50, from: el.getHeight()},
    	width: {to: 300, from: el.getWidth()},
    	top  : {by: - 100, unit: <em>'px'</em>},
    	points: {
    	    to: [50, 100],  <i>// go to this point
</i>
    	    control: [      <i>// optional bezier way points
</i>
    	        [ 600, 800],
    	        [-100, 200]
    	    ]
    	}
    },
    3000,      <i>// animation duration (milliseconds!)
</i>
    null,      <i>// callback
</i>
    <em>'easeOut'</em>, <i>// easing method
</i>
    <em>'motion'</em>   <i>// animation type (<em>'run'</em>,<em>'color'</em>,<em>'motion'</em>,<em>'scroll'</em>)    
</i>
);</code></pre> 
</li>
<li><tt>'scroll'</tt>
<p>Animate horizontal or vertical scrolling of an overflowing page element.</p>
<pre><code>el.animate(
    <i>// animation control object
</i>
    {
    	scroll: {to: [400, 300]}
    },
    0.35,      <i>// animation duration
</i>
    null,      <i>// callback
</i>
    <em>'easeOut'</em>, <i>// easing method
</i>
    <em>'scroll'</em>   <i>// animation type (<em>'run'</em>,<em>'color'</em>,<em>'motion'</em>,<em>'scroll'</em>)    
</i>
);</code></pre> 
</li>
</ul></div>
</ul></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>args</code> : Object<div class="sub-desc">The animation control args</div></li><li><code>duration</code> : Float<div class="sub-desc">(optional) How long the animation lasts in seconds (defaults to <tt>.35</tt>)</div></li><li><code>onComplete</code> : Function<div class="sub-desc">(optional) Function to call when animation completes</div></li><li><code>easing</code> : String<div class="sub-desc">(optional) <a href="output/Ext.Fx.html#Ext.Fx-easing" ext:member="easing" ext:cls="Ext.Fx">Ext.Fx.easing</a> method to use (defaults to <tt>'easeOut'</tt>)</div></li><li><code>animType</code> : String<div class="sub-desc">(optional) <tt>'run'</tt> is the default. Can also be <tt>'color'</tt>,
<tt>'motion'</tt>, or <tt>'scroll'</tt></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#animate" ext:member="#animate" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-appendChild"></a><b><a href="source/Element.insertion.html#method-Ext.Element-appendChild">appendChild</a></b>(&nbsp;<code>String/HTMLElement/Array/Element/CompositeElement&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Appends the passed element(s) to this element</div><div class="long">Appends the passed element(s) to this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement/Array/Element/CompositeElement<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#appendChild" ext:member="#appendChild" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-appendTo"></a><b><a href="source/Element.insertion.html#method-Ext.Element-appendTo">appendTo</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Appends this element to the passed element</div><div class="long">Appends this element to the passed element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The new parent element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#appendTo" ext:member="#appendTo" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-applyStyles"></a><b><a href="source/Element.style-more.html#method-Ext.Element-applyStyles">applyStyles</a></b>(&nbsp;<code>String/Object/Function&nbsp;styles</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">More flexible version of setStyle for setting style properties.</div><div class="long">More flexible version of <a href="output/Ext.Element.html#Ext.Element-setStyle" ext:member="setStyle" ext:cls="Ext.Element">setStyle</a> for setting style properties.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>styles</code> : String/Object/Function<div class="sub-desc">A style specification string, e.g. "width:100px", or object in the form {width:"100px"}, or
a function which returns such a specification.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#applyStyles" ext:member="#applyStyles" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-autoHeight"></a><b><a href="source/Element.legacy.html#method-Ext.Element-autoHeight">autoHeight</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;animate</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Float&nbsp;duration</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;onComplete</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;easing</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Measures the element's content height and updates height to match. Note: this function uses setTimeout so&#13;
the new he...</div><div class="long">Measures the element's content height and updates height to match. Note: this function uses setTimeout so
the new height may not be available immediately.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean<div class="sub-desc">(optional) Animate the transition (defaults to false)</div></li><li><code>duration</code> : Float<div class="sub-desc">(optional) Length of the animation in seconds (defaults to .35)</div></li><li><code>onComplete</code> : Function<div class="sub-desc">(optional) Function to call when animation completes</div></li><li><code>easing</code> : String<div class="sub-desc">(optional) Easing method to use (defaults to easeOut)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#autoHeight" ext:member="#autoHeight" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-blur"></a><b><a href="source/Element.html#method-Ext.Element-blur">blur</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Tries to blur the element. Any exceptions are caught and ignored.</div><div class="long">Tries to blur the element. Any exceptions are caught and ignored.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#blur" ext:member="#blur" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-boxWrap"></a><b><a href="source/Element.style-more.html#method-Ext.Element-boxWrap">boxWrap</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;class</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Wraps the specified element with a special 9 element markup/CSS block that renders by default as&#13;
a gray container wi...</div><div class="long"><p>Wraps the specified element with a special 9 element markup/CSS block that renders by default as
a gray container with a gradient background, rounded corners and a 4-way shadow.</p>
<p>This special markup is used throughout Ext when box wrapping elements (<a href="output/Ext.Button.html" ext:cls="Ext.Button">Ext.Button</a>,
<a href="output/Ext.Panel.html" ext:cls="Ext.Panel">Ext.Panel</a> when <tt><a href="output/Ext.Panel.html#Ext.Panel-frame" ext:member="frame" ext:cls="Ext.Panel">frame=true</a></tt>, <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a>).  The markup
is of this form:</p>
<pre><code>Ext.Element.boxMarkup =
    &#39;&lt;div class=<em>"{0}-tl"</em>>&lt;div class=<em>"{0}-tr"</em>>&lt;div class=<em>"{0}-tc"</em>>&lt;/div>&lt;/div>&lt;/div>
     &lt;div class=<em>"{0}-ml"</em>>&lt;div class=<em>"{0}-mr"</em>>&lt;div class=<em>"{0}-mc"</em>>&lt;/div>&lt;/div>&lt;/div>
     &lt;div class=<em>"{0}-bl"</em>>&lt;div class=<em>"{0}-br"</em>>&lt;div class=<em>"{0}-bc"</em>>&lt;/div>&lt;/div>&lt;/div>&#39;;
</pre></code>
<p>Example usage:</p>
<pre><code><i>// Basic box wrap
</i>
Ext.get(<em>"foo"</em>).boxWrap();

<i>// You can also add a custom class and use CSS inheritance rules to customize the box look.
</i>
<i>// <em>'x-box-blue'</em> is a built-<b>in</b> alternative -- look at the related CSS definitions as an example
</i>
<i>// <b>for</b> how to create a custom box wrap style.
</i>
Ext.get(<em>"foo"</em>).boxWrap().addClass(<em>"x-box-blue"</em>);
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>class</code> : String<div class="sub-desc">(optional) A base CSS class to apply to the containing wrapper element
(defaults to <tt>'x-box'</tt>). Note that there are a number of CSS rules that are dependent on
this name to make the overall effect work, so if you supply an alternate base class, make sure you
also supply all of the necessary rules.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#boxWrap" ext:member="#boxWrap" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-center"></a><b><a href="source/Element.alignment.html#method-Ext.Element-center">center</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;centerIn</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Centers the Element in either the viewport, or another Element.</div><div class="long">Centers the Element in either the viewport, or another Element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>centerIn</code> : Mixed<div class="sub-desc">(optional) The element in which to center the element.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#center" ext:member="#center" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-child"></a><b><a href="source/Element.traversal.html#method-Ext.Element-child">child</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Selects a single child at any depth below this element based on the passed CSS selector (the selector should not cont...</div><div class="long">Selects a single child at any depth below this element based on the passed CSS selector (the selector should not contain an id).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The CSS selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return the DOM node instead of Ext.Element (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The child Ext.Element (or DOM node if returnDom = true)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#child" ext:member="#child" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-clean"></a><b><a href="source/Element-more.html#method-Ext.Element-clean">clean</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;forceReclean</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes worthless text nodes</div><div class="long">Removes worthless text nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>forceReclean</code> : Boolean<div class="sub-desc">(optional) By default the element
keeps track if it has been cleaned already so
you can call this over and over. However, if you update the element and
need to force a reclean, you can pass true.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#clean" ext:member="#clean" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-clearOpacity"></a><b><a href="source/Element.style.html#method-Ext.Element-clearOpacity">clearOpacity</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Clears any opacity settings from this element. Required in some cases for IE.</div><div class="long">Clears any opacity settings from this element. Required in some cases for IE.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#clearOpacity" ext:member="#clearOpacity" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-clearPositioning"></a><b><a href="source/Element.position.html#method-Ext.Element-clearPositioning">clearPositioning</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;value</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Clear positioning back to the default when the document was loaded</div><div class="long">Clear positioning back to the default when the document was loaded<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">(optional) The value to use for the left,right,top,bottom, defaults to '' (empty string). You could use 'auto'.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#clearPositioning" ext:member="#clearPositioning" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-clip"></a><b><a href="source/Element.style.html#method-Ext.Element-clip">clip</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Store the current overflow setting and clip overflow on the element - use unclip to remove</div><div class="long">Store the current overflow setting and clip overflow on the element - use <tt><a href="output/Ext.Element.html#Ext.Element-unclip" ext:member="unclip" ext:cls="Ext.Element">unclip</a></tt> to remove<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#clip" ext:member="#clip" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-contains"></a><b><a href="source/Element.html#method-Ext.Element-contains">contains</a></b>(&nbsp;<code>HTMLElement/String&nbsp;el</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this element is an ancestor of the passed element</div><div class="long">Returns true if this element is an ancestor of the passed element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : HTMLElement/String<div class="sub-desc">The element to check</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this element is an ancestor of el, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#contains" ext:member="#contains" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-createChild"></a><b><a href="source/Element.insertion.html#method-Ext.Element-createChild">createChild</a></b>(&nbsp;<code>Object&nbsp;config</code>,&nbsp;<span title="Optional" class="optional">[<code>HTMLElement&nbsp;insertBefore</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Creates the passed DomHelper config and appends it to this element or optionally inserts it before the passed child e...</div><div class="long">Creates the passed DomHelper config and appends it to this element or optionally inserts it before the passed child element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">DomHelper element config object.  If no tag is specified (e.g., {tag:'input'}) then a div will be
automatically generated with the specified attributes.</div></li><li><code>insertBefore</code> : HTMLElement<div class="sub-desc">(optional) a child element of this element</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) true to return the dom node instead of creating an Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The new child element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#createChild" ext:member="#createChild" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-createProxy"></a><b><a href="source/Element-more.html#method-Ext.Element-createProxy">createProxy</a></b>(&nbsp;<code>String/Object&nbsp;config</code>,&nbsp;<span title="Optional" class="optional">[<code>String/HTMLElement&nbsp;renderTo</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;matchBox</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Creates a proxy element of this element</div><div class="long">Creates a proxy element of this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : String/Object<div class="sub-desc">The class name of the proxy element or a DomHelper config object</div></li><li><code>renderTo</code> : String/HTMLElement<div class="sub-desc">(optional) The element or element id to render the proxy to (defaults to document.body)</div></li><li><code>matchBox</code> : Boolean<div class="sub-desc">(optional) True to align and size the proxy to this element now (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The new proxy element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#createProxy" ext:member="#createProxy" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-createShim"></a><b><a href="source/Element.fx-more.html#method-Ext.Element-createShim">createShim</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Creates an iframe shim for this element to keep selects and other windowed objects from&#13;
showing through.</div><div class="long">Creates an iframe shim for this element to keep selects and other windowed objects from
showing through.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The new shim element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#createShim" ext:member="#createShim" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-down"></a><b><a href="source/Element.traversal.html#method-Ext.Element-down">down</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Selects a single *direct* child based on the passed CSS selector (the selector should not contain an id).</div><div class="long">Selects a single *direct* child based on the passed CSS selector (the selector should not contain an id).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The CSS selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return the DOM node instead of Ext.Element (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The child Ext.Element (or DOM node if returnDom = true)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#down" ext:member="#down" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-enableDisplayMode"></a><b><a href="source/Element.fx-more.html#method-Ext.Element-enableDisplayMode">enableDisplayMode</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;display</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Convenience method for setVisibilityMode(Element.DISPLAY)</div><div class="long">Convenience method for setVisibilityMode(Element.DISPLAY)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>display</code> : String<div class="sub-desc">(optional) What to set display to when visible</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#enableDisplayMode" ext:member="#enableDisplayMode" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-findParent"></a><b><a href="source/Element.traversal.html#method-Ext.Element-findParent">findParent</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Number/Mixed&nbsp;maxDepth</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnEl</code>]</span>&nbsp;)
    :
                                        HTMLElement<div class="mdesc"><div class="short">Looks at this node and then at parent nodes for a match of the passed simple selector (e.g. div.some-class or span:fi...</div><div class="long">Looks at this node and then at parent nodes for a match of the passed simple selector (e.g. div.some-class or span:first-child)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The simple selector to test</div></li><li><code>maxDepth</code> : Number/Mixed<div class="sub-desc">(optional) The max depth to
	            search as a number or element (defaults to 10 || document.body)</div></li><li><code>returnEl</code> : Boolean<div class="sub-desc">(optional) True to return a Ext.Element object instead of DOM node</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc">The matching DOM node (or null if no match was found)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#findParent" ext:member="#findParent" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-findParentNode"></a><b><a href="source/Element.traversal.html#method-Ext.Element-findParentNode">findParentNode</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Number/Mixed&nbsp;maxDepth</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnEl</code>]</span>&nbsp;)
    :
                                        HTMLElement<div class="mdesc"><div class="short">Looks at parent nodes for a match of the passed simple selector (e.g. div.some-class or span:first-child)</div><div class="long">Looks at parent nodes for a match of the passed simple selector (e.g. div.some-class or span:first-child)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The simple selector to test</div></li><li><code>maxDepth</code> : Number/Mixed<div class="sub-desc">(optional) The max depth to
	            search as a number or element (defaults to 10 || document.body)</div></li><li><code>returnEl</code> : Boolean<div class="sub-desc">(optional) True to return a Ext.Element object instead of DOM node</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc">The matching DOM node (or null if no match was found)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#findParentNode" ext:member="#findParentNode" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-first"></a><b><a href="source/Element.traversal.html#method-Ext.Element-first">first</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;selector</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element/HTMLElement<div class="mdesc"><div class="short">Gets the first child, skipping text nodes</div><div class="long">Gets the first child, skipping text nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">(optional) Find the next sibling that matches the passed simple selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return a raw dom node instead of an Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element/HTMLElement</code><div class="sub-desc">The first child or null</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#first" ext:member="#first" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-fly"></a><b><a href="source/Element.html#method-Ext.Element-fly">fly</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;named</code>]</span>&nbsp;)
    :
                                        Element<div class="mdesc"><div class="short">Gets the globally shared flyweight Element, with the passed node as the active element. Do not store a reference to t...</div><div class="long"><p>Gets the globally shared flyweight Element, with the passed node as the active element. Do not store a reference to this element -
the dom node can be overwritten by other code. Shorthand of <a href="output/Ext.Element.html#Ext.Element-fly" ext:member="fly" ext:cls="Ext.Element">Ext.Element.fly</a></p>
<p>Use this to make one-time references to DOM elements which are not going to be accessed again either by
application code, or by Ext's classes. If accessing an element which will be processed regularly, then <a href="output/Ext.html#Ext-get" ext:member="get" ext:cls="Ext">Ext.get</a>
will be more appropriate to take advantage of the caching provided by the Ext.Element class.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The dom node or id</div></li><li><code>named</code> : String<div class="sub-desc">(optional) Allows for creation of named reusable flyweights to prevent conflicts
(e.g. internally Ext uses "_global")</div></li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The shared Element object (or null if no matching element was found)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#fly" ext:member="#fly" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-focus"></a><b><a href="source/Element.html#method-Ext.Element-focus">focus</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;defer</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Tries to focus the element. Any exceptions are caught and ignored.</div><div class="long">Tries to focus the element. Any exceptions are caught and ignored.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>defer</code> : Number<div class="sub-desc">(optional) Milliseconds to defer the focus</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#focus" ext:member="#focus" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getAlignToXY"></a><b><a href="source/Element.alignment.html#method-Ext.Element-getAlignToXY">getAlignToXY</a></b>(&nbsp;<code>Mixed&nbsp;element</code>,&nbsp;<code>String&nbsp;position</code>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;offsets</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Gets the x,y coordinates to align this element with another element. See alignTo for more info on the&#13;
supported posi...</div><div class="long">Gets the x,y coordinates to align this element with another element. See <a href="output/Ext.Element.html#Ext.Element-alignTo" ext:member="alignTo" ext:cls="Ext.Element">alignTo</a> for more info on the
supported position values.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : Mixed<div class="sub-desc">The element to align to.</div></li><li><code>position</code> : String<div class="sub-desc">The position to align to.</div></li><li><code>offsets</code> : Array<div class="sub-desc">(optional) Offset the positioning by [x, y]</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">[x, y]</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getAlignToXY" ext:member="#getAlignToXY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getAnchorXY"></a><b><a href="source/Element.alignment.html#method-Ext.Element-getAnchorXY">getAnchorXY</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;anchor</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;size</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Gets the x,y coordinates specified by the anchor position on the element.</div><div class="long">Gets the x,y coordinates specified by the anchor position on the element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>anchor</code> : String<div class="sub-desc">(optional) The specified anchor position (defaults to "c").  See <a href="output/Ext.Element.html#Ext.Element-alignTo" ext:member="alignTo" ext:cls="Ext.Element">alignTo</a>
for details on supported anchor positions.</div></li><li><code>local</code> : Boolean<div class="sub-desc">(optional) True to get the local (element top/left-relative) anchor position instead
of page coordinates</div></li><li><code>size</code> : Object<div class="sub-desc">(optional) An object containing the size to use for calculating anchor position
{width: (target width), height: (target height)} (defaults to the element's current size)</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">[x, y] An array containing the element's x and y coordinates</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getAnchorXY" ext:member="#getAnchorXY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getAttribute"></a><b><a href="source/Element.html#method-Ext.Element-getAttribute">getAttribute</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;namespace</code>]</span>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the value of an attribute from the element's underlying DOM node.</div><div class="long">Returns the value of an attribute from the element's underlying DOM node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The attribute name</div></li><li><code>namespace</code> : String<div class="sub-desc">(optional) The namespace in which to look for the attribute</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The attribute value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getAttribute" ext:member="#getAttribute" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getAttributeNS"></a><b><a href="source/Element.html#method-Ext.Element-getAttributeNS">getAttributeNS</a></b>(&nbsp;<code>String&nbsp;namespace</code>,&nbsp;<code>String&nbsp;name</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the value of a namespaced attribute from the element's underlying DOM node.</div><div class="long">Returns the value of a namespaced attribute from the element's underlying DOM node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>namespace</code> : String<div class="sub-desc">The namespace in which to look for the attribute</div></li><li><code>name</code> : String<div class="sub-desc">The attribute name</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The attribute value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getAttributeNS" ext:member="#getAttributeNS" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getBorderWidth"></a><b><a href="source/Element.style.html#method-Ext.Element-getBorderWidth">getBorderWidth</a></b>(&nbsp;<code>String&nbsp;side</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Gets the width of the border(s) for the specified side(s)</div><div class="long">Gets the width of the border(s) for the specified side(s)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>side</code> : String<div class="sub-desc">Can be t, l, r, b or any combination of those to add multiple values. For example,
passing <tt>'lr'</tt> would get the border <b><u>l</u></b>eft width + the border <b><u>r</u></b>ight width.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The width of the sides passed added together</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getBorderWidth" ext:member="#getBorderWidth" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getBottom"></a><b><a href="source/Element.position.html#method-Ext.Element-getBottom">getBottom</a></b>(&nbsp;<code>Boolean&nbsp;local</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Gets the bottom Y coordinate of the element (element Y position + element height)</div><div class="long">Gets the bottom Y coordinate of the element (element Y position + element height)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">True to get the local css position instead of page coordinate</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getBottom" ext:member="#getBottom" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getBox"></a><b><a href="source/Element.position-more.html#method-Ext.Element-getBox">getBox</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;contentBox</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;local</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Return a box {x, y, width, height} that can be used to set another elements&#13;
size/location to match this element.</div><div class="long">Return a box {x, y, width, height} that can be used to set another elements
size/location to match this element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>contentBox</code> : Boolean<div class="sub-desc">(optional) If true a box for the content of the element is returned.</div></li><li><code>local</code> : Boolean<div class="sub-desc">(optional) If true the element's left and top are returned instead of page x/y.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">box An object in the format {x, y, width, height}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getBox" ext:member="#getBox" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getCenterXY"></a><b><a href="source/Element.alignment.html#method-Ext.Element-getCenterXY">getCenterXY</a></b>()
    :
                                        Array<div class="mdesc"><div class="short">Calculates the x, y to center this element on the screen</div><div class="long">Calculates the x, y to center this element on the screen<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The x, y values [x, y]</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getCenterXY" ext:member="#getCenterXY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getColor"></a><b><a href="source/Element.style.html#method-Ext.Element-getColor">getColor</a></b>(&nbsp;<code>String&nbsp;attr</code>,&nbsp;<code>String&nbsp;defaultValue</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;prefix</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Return the CSS color for the specified CSS attribute. rgb, 3 digit (like #fff) and valid values&#13;
are convert to stand...</div><div class="long">Return the CSS color for the specified CSS attribute. rgb, 3 digit (like #fff) and valid values
are convert to standard 6 digit hex color.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>attr</code> : String<div class="sub-desc">The css attribute</div></li><li><code>defaultValue</code> : String<div class="sub-desc">The default value to use when a valid color isn't found</div></li><li><code>prefix</code> : String<div class="sub-desc">(optional) defaults to #. Use an empty string when working with
color anims.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getColor" ext:member="#getColor" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getComputedHeight"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getComputedHeight">getComputedHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns either the offsetHeight or the height of this element based on CSS height adjusted by padding or borders&#13;
whe...</div><div class="long">Returns either the offsetHeight or the height of this element based on CSS height adjusted by padding or borders
when needed to simulate offsetHeight when offsets aren't available. This may not work on display:none elements
if a height has not been set using CSS.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getComputedHeight" ext:member="#getComputedHeight" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getComputedWidth"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getComputedWidth">getComputedWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns either the offsetWidth or the width of this element based on CSS width adjusted by padding or borders&#13;
when n...</div><div class="long">Returns either the offsetWidth or the width of this element based on CSS width adjusted by padding or borders
when needed to simulate offsetWidth when offsets aren't available. This may not work on display:none elements
if a width has not been set using CSS.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getComputedWidth" ext:member="#getComputedWidth" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getFrameWidth"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getFrameWidth">getFrameWidth</a></b>(&nbsp;<code>String&nbsp;sides</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the sum width of the padding and borders for the passed "sides". See getBorderWidth()&#13;
	     for more informa...</div><div class="long">Returns the sum width of the padding and borders for the passed "sides". See getBorderWidth()
	     for more information about the sides.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>sides</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getFrameWidth" ext:member="#getFrameWidth" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getHeight"></a><b><a href="source/Element.style.html#method-Ext.Element-getHeight">getHeight</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;contentHeight</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the offset height of the element</div><div class="long">Returns the offset height of the element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>contentHeight</code> : Boolean<div class="sub-desc">(optional) true to get the height minus borders and padding</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The element's height</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getHeight" ext:member="#getHeight" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getLeft"></a><b><a href="source/Element.position.html#method-Ext.Element-getLeft">getLeft</a></b>(&nbsp;<code>Boolean&nbsp;local</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Gets the left X coordinate</div><div class="long">Gets the left X coordinate<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">True to get the local css position instead of page coordinate</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getLeft" ext:member="#getLeft" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getMargins"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getMargins">getMargins</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;sides</code>]</span>&nbsp;)
    :
                                        Object/Number<div class="mdesc"><div class="short">Returns an object with properties top, left, right and bottom representing the margins of this element unless sides i...</div><div class="long">Returns an object with properties top, left, right and bottom representing the margins of this element unless sides is passed,
then it returns the calculated width of the sides (see getPadding)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>sides</code> : String<div class="sub-desc">(optional) Any combination of l, r, t, b to get the sum of those sides</div></li></ul><strong>Returns:</strong><ul><li><code>Object/Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getMargins" ext:member="#getMargins" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getOffsetsTo"></a><b><a href="source/Element.position.html#method-Ext.Element-getOffsetsTo">getOffsetsTo</a></b>(&nbsp;<code>Mixed&nbsp;element</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Returns the offsets of this element from the passed element. Both element must be part of the DOM tree and not have d...</div><div class="long">Returns the offsets of this element from the passed element. Both element must be part of the DOM tree and not have display:none to have page coordinates.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : Mixed<div class="sub-desc">The element to get the offsets from.</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The XY page offsets (e.g. [100, -200])</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getOffsetsTo" ext:member="#getOffsetsTo" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getPadding"></a><b><a href="source/Element.style.html#method-Ext.Element-getPadding">getPadding</a></b>(&nbsp;<code>String&nbsp;side</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Gets the width of the padding(s) for the specified side(s)</div><div class="long">Gets the width of the padding(s) for the specified side(s)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>side</code> : String<div class="sub-desc">Can be t, l, r, b or any combination of those to add multiple values. For example,
passing <tt>'lr'</tt> would get the padding <b><u>l</u></b>eft + the padding <b><u>r</u></b>ight.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The padding of the sides passed added together</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getPadding" ext:member="#getPadding" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getPositioning"></a><b><a href="source/Element.position.html#method-Ext.Element-getPositioning">getPositioning</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets an object with all CSS positioning properties. Useful along with setPostioning to get&#13;
snapshot before performin...</div><div class="long">Gets an object with all CSS positioning properties. Useful along with setPostioning to get
snapshot before performing an update and then restoring the element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getPositioning" ext:member="#getPositioning" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getRegion"></a><b><a href="source/Element.position-more.html#method-Ext.Element-getRegion">getRegion</a></b>()
    :
                                        Region<div class="mdesc"><div class="short">Returns the region of the given element.&#13;
The element must be part of the DOM tree to have a region (display:none or ...</div><div class="long">Returns the region of the given element.
The element must be part of the DOM tree to have a region (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Region</code><div class="sub-desc">A Ext.lib.Region containing "top, left, bottom, right" member data.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getRegion" ext:member="#getRegion" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getRight"></a><b><a href="source/Element.position.html#method-Ext.Element-getRight">getRight</a></b>(&nbsp;<code>Boolean&nbsp;local</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Gets the right X coordinate of the element (element X position + element width)</div><div class="long">Gets the right X coordinate of the element (element X position + element width)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">True to get the local css position instead of page coordinate</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getRight" ext:member="#getRight" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getScroll"></a><b><a href="source/Element.scroll.html#method-Ext.Element-getScroll">getScroll</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the current scroll position of the element.</div><div class="long">Returns the current scroll position of the element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the scroll position in the format {left: (scrollLeft), top: (scrollTop)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getScroll" ext:member="#getScroll" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getSize"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getSize">getSize</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;contentSize</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the size of the element.</div><div class="long">Returns the size of the element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>contentSize</code> : Boolean<div class="sub-desc">(optional) true to get the width/size minus borders and padding</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size {width: (element width), height: (element height)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getSize" ext:member="#getSize" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getStyle"></a><b><a href="source/Element.style.html#method-Ext.Element-getStyle">getStyle</a></b>(&nbsp;<code>String&nbsp;property</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Normalizes currentStyle and computedStyle.</div><div class="long">Normalizes currentStyle and computedStyle.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>property</code> : String<div class="sub-desc">The style property whose value is returned.</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The current value of the style property for this element.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getStyle" ext:member="#getStyle" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getStyles"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getStyles">getStyles</a></b>(&nbsp;<code>String&nbsp;style1</code>,&nbsp;<code>String&nbsp;style2</code>,&nbsp;<code>String&nbsp;etc.</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns an object with properties matching the styles requested.&#13;
For example, el.getStyles('color', 'font-size', 'wi...</div><div class="long">Returns an object with properties matching the styles requested.
For example, el.getStyles('color', 'font-size', 'width') might return
{'color': '#FFFFFF', 'font-size': '13px', 'width': '100px'}.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>style1</code> : String<div class="sub-desc">A style name</div></li><li><code>style2</code> : String<div class="sub-desc">A style name</div></li><li><code>etc.</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The style object</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getStyles" ext:member="#getStyles" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getTextWidth"></a><b><a href="source/TextMetrics.html#method-Ext.Element-getTextWidth">getTextWidth</a></b>(&nbsp;<code>String&nbsp;text</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;min</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;max</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the width in pixels of the passed text, or the width of the text in this Element.</div><div class="long">Returns the width in pixels of the passed text, or the width of the text in this Element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">The text to measure. Defaults to the innerHTML of the element.</div></li><li><code>min</code> : Number<div class="sub-desc">(Optional) The minumum value to return.</div></li><li><code>max</code> : Number<div class="sub-desc">(Optional) The maximum value to return.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The text width in pixels.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getTextWidth" ext:member="#getTextWidth" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getTop"></a><b><a href="source/Element.position.html#method-Ext.Element-getTop">getTop</a></b>(&nbsp;<code>Boolean&nbsp;local</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Gets the top Y coordinate</div><div class="long">Gets the top Y coordinate<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>local</code> : Boolean<div class="sub-desc">True to get the local css position instead of page coordinate</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getTop" ext:member="#getTop" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getUpdater"></a><b><a href="source/Element-more.html#method-Ext.Element-getUpdater">getUpdater</a></b>()
    :
                                        Ext.Updater<div class="mdesc"><div class="short">Gets this element's Updater</div><div class="long">Gets this element's <a href="output/Ext.Updater.html" ext:cls="Ext.Updater">Updater</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Updater</code><div class="sub-desc">The Updater</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getUpdater" ext:member="#getUpdater" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getValue"></a><b><a href="source/Element.html#method-Ext.Element-getValue">getValue</a></b>(&nbsp;<code>Boolean&nbsp;asNumber</code>&nbsp;)
    :
                                        String/Number<div class="mdesc"><div class="short">Returns the value of the "value" attribute</div><div class="long">Returns the value of the "value" attribute<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>asNumber</code> : Boolean<div class="sub-desc">true to parse the value as a number</div></li></ul><strong>Returns:</strong><ul><li><code>String/Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getValue" ext:member="#getValue" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getViewSize"></a><b><a href="source/Element.style-more.html#method-Ext.Element-getViewSize">getViewSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the width and height of the viewport.&#13;
var vpSize = Ext.getBody().getViewSize();&#13;
&#13;
        // all Windows cr...</div><div class="long">Returns the width and height of the viewport.
<pre><code><b>var</b> vpSize = Ext.getBody().getViewSize();

        <i>// all Windows created afterwards will have a <b>default</b> value of 90% height and 95% width
</i>
        Ext.Window.override({
            width: vpSize.width * 0.9,
            height: vpSize.height * 0.95
        });
        <i>// To handle window resizing you would have to hook onto onWindowResize. 
</i>
        </pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the viewport's size {width: (viewport width), height: (viewport height)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getViewSize" ext:member="#getViewSize" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getWidth"></a><b><a href="source/Element.style.html#method-Ext.Element-getWidth">getWidth</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;contentWidth</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the offset width of the element</div><div class="long">Returns the offset width of the element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>contentWidth</code> : Boolean<div class="sub-desc">(optional) true to get the width minus borders and padding</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The element's width</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getWidth" ext:member="#getWidth" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getX"></a><b><a href="source/Element.position.html#method-Ext.Element-getX">getX</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current X position of the element based on page coordinates.  Element must be part of the DOM tree to have p...</div><div class="long">Gets the current X position of the element based on page coordinates.  Element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The X position of the element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getX" ext:member="#getX" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getXY"></a><b><a href="source/Element.position.html#method-Ext.Element-getXY">getXY</a></b>()
    :
                                        Array<div class="mdesc"><div class="short">Gets the current position of the element based on page coordinates.  Element must be part of the DOM tree to have pag...</div><div class="long">Gets the current position of the element based on page coordinates.  Element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">The XY position of the element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getXY" ext:member="#getXY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-getY"></a><b><a href="source/Element.position.html#method-Ext.Element-getY">getY</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Gets the current Y position of the element based on page coordinates.  Element must be part of the DOM tree to have p...</div><div class="long">Gets the current Y position of the element based on page coordinates.  Element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The Y position of the element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#getY" ext:member="#getY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-hasClass"></a><b><a href="source/Element.style.html#method-Ext.Element-hasClass">hasClass</a></b>(&nbsp;<code>String&nbsp;className</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks if the specified CSS class exists on this element's DOM node.</div><div class="long">Checks if the specified CSS class exists on this element's DOM node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String<div class="sub-desc">The CSS class to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the class exists, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#hasClass" ext:member="#hasClass" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-hide"></a><b><a href="source/Element.fx.html#method-Ext.Element-hide">hide</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Hide this element - Uses display mode to determine whether to use "display" or "visibility". See setVisible.</div><div class="long">Hide this element - Uses display mode to determine whether to use "display" or "visibility". See <a href="output/Ext.Element.html#Ext.Element-setVisible" ext:member="setVisible" ext:cls="Ext.Element">setVisible</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#hide" ext:member="#hide" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-hover"></a><b><a href="source/Element.html#method-Ext.Element-hover">hover</a></b>(&nbsp;<code>Function&nbsp;overFn</code>,&nbsp;<code>Function&nbsp;outFn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets up event handlers to call the passed functions when the mouse is moved into and out of the Element.</div><div class="long">Sets up event handlers to call the passed functions when the mouse is moved into and out of the Element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>overFn</code> : Function<div class="sub-desc">The function to call when the mouse enters the Element.</div></li><li><code>outFn</code> : Function<div class="sub-desc">The function to call when the mouse leaves the Element.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<tt>this</tt> reference) in which the functions are executed. Defaults to the Element's DOM element.</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) Options for the listener. See <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">the <tt>options</tt> parameter</a>.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#hover" ext:member="#hover" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-initDD"></a><b><a href="source/Element.dd.html#method-Ext.Element-initDD">initDD</a></b>(&nbsp;<code>String&nbsp;group</code>,&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.dd.DD<div class="mdesc"><div class="short">Initializes a Ext.dd.DD drag drop object for this element.</div><div class="long">Initializes a <a href="output/Ext.dd.DD.html" ext:cls="Ext.dd.DD">Ext.dd.DD</a> drag drop object for this element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>group</code> : String<div class="sub-desc">The group the DD object is member of</div></li><li><code>config</code> : Object<div class="sub-desc">The DD config object</div></li><li><code>overrides</code> : Object<div class="sub-desc">An object containing methods to override/implement on the DD object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.dd.DD</code><div class="sub-desc">The DD object</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#initDD" ext:member="#initDD" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-initDDProxy"></a><b><a href="source/Element.dd.html#method-Ext.Element-initDDProxy">initDDProxy</a></b>(&nbsp;<code>String&nbsp;group</code>,&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.dd.DDProxy<div class="mdesc"><div class="short">Initializes a Ext.dd.DDProxy object for this element.</div><div class="long">Initializes a <a href="output/Ext.dd.DDProxy.html" ext:cls="Ext.dd.DDProxy">Ext.dd.DDProxy</a> object for this element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>group</code> : String<div class="sub-desc">The group the DDProxy object is member of</div></li><li><code>config</code> : Object<div class="sub-desc">The DDProxy config object</div></li><li><code>overrides</code> : Object<div class="sub-desc">An object containing methods to override/implement on the DDProxy object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.dd.DDProxy</code><div class="sub-desc">The DDProxy object</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#initDDProxy" ext:member="#initDDProxy" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-initDDTarget"></a><b><a href="source/Element.dd.html#method-Ext.Element-initDDTarget">initDDTarget</a></b>(&nbsp;<code>String&nbsp;group</code>,&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>Object&nbsp;overrides</code>&nbsp;)
    :
                                        Ext.dd.DDTarget<div class="mdesc"><div class="short">Initializes a Ext.dd.DDTarget object for this element.</div><div class="long">Initializes a <a href="output/Ext.dd.DDTarget.html" ext:cls="Ext.dd.DDTarget">Ext.dd.DDTarget</a> object for this element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>group</code> : String<div class="sub-desc">The group the DDTarget object is member of</div></li><li><code>config</code> : Object<div class="sub-desc">The DDTarget config object</div></li><li><code>overrides</code> : Object<div class="sub-desc">An object containing methods to override/implement on the DDTarget object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.dd.DDTarget</code><div class="sub-desc">The DDTarget object</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#initDDTarget" ext:member="#initDDTarget" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-insertAfter"></a><b><a href="source/Element.insertion.html#method-Ext.Element-insertAfter">insertAfter</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Inserts this element after the passed element in the DOM</div><div class="long">Inserts this element after the passed element in the DOM<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element to insert after</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#insertAfter" ext:member="#insertAfter" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-insertBefore"></a><b><a href="source/Element.insertion.html#method-Ext.Element-insertBefore">insertBefore</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Inserts this element before the passed element in the DOM</div><div class="long">Inserts this element before the passed element in the DOM<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element before which this element will be inserted</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#insertBefore" ext:member="#insertBefore" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-insertFirst"></a><b><a href="source/Element.insertion.html#method-Ext.Element-insertFirst">insertFirst</a></b>(&nbsp;<code>Mixed/Object&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Inserts (or creates) an element (or DomHelper config) as the first child of this element</div><div class="long">Inserts (or creates) an element (or DomHelper config) as the first child of this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed/Object<div class="sub-desc">The id or element to insert or a DomHelper config to create and insert</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The new child</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#insertFirst" ext:member="#insertFirst" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-insertHtml"></a><b><a href="source/Element.insertion.html#method-Ext.Element-insertHtml">insertHtml</a></b>(&nbsp;<code>String&nbsp;where</code>,&nbsp;<code>String&nbsp;html</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnEl</code>]</span>&nbsp;)
    :
                                        HTMLElement/Ext.Element<div class="mdesc"><div class="short">Inserts an html fragment into this element</div><div class="long">Inserts an html fragment into this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>where</code> : String<div class="sub-desc">Where to insert the html in relation to this element - beforeBegin, afterBegin, beforeEnd, afterEnd.</div></li><li><code>html</code> : String<div class="sub-desc">The HTML fragment</div></li><li><code>returnEl</code> : Boolean<div class="sub-desc">(optional) True to return an Ext.Element (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Ext.Element</code><div class="sub-desc">The inserted node (or nearest related if more than 1 inserted)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#insertHtml" ext:member="#insertHtml" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-insertSibling"></a><b><a href="source/Element.insertion-more.html#method-Ext.Element-insertSibling">insertSibling</a></b>(&nbsp;<code>Mixed/Object/Array&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;where</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Inserts (or creates) the passed element (or DomHelper config) as a sibling of this element</div><div class="long">Inserts (or creates) the passed element (or DomHelper config) as a sibling of this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed/Object/Array<div class="sub-desc">The id, element to insert or a DomHelper config to create and insert *or* an array of any of those.</div></li><li><code>where</code> : String<div class="sub-desc">(optional) 'before' or 'after' defaults to before</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return the raw DOM element instead of Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">the inserted Element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#insertSibling" ext:member="#insertSibling" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-is"></a><b><a href="source/Element.html#method-Ext.Element-is">is</a></b>(&nbsp;<code>String&nbsp;selector</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this element matches the passed simple selector (e.g. div.some-class or span:first-child)</div><div class="long">Returns true if this element matches the passed simple selector (e.g. div.some-class or span:first-child)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The simple selector to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if this element matches the selector, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#is" ext:member="#is" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-isBorderBox"></a><b><a href="source/Element.html#method-Ext.Element-isBorderBox">isBorderBox</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Tests various css rules/browsers to determine if this element uses a border box</div><div class="long">Tests various css rules/browsers to determine if this element uses a border box<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#isBorderBox" ext:member="#isBorderBox" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-isDisplayed"></a><b><a href="source/Element.fx-more.html#method-Ext.Element-isDisplayed">isDisplayed</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if display is not "none"</div><div class="long">Returns true if display is not "none"<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#isDisplayed" ext:member="#isDisplayed" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-isMasked"></a><b><a href="source/Element.fx-more.html#method-Ext.Element-isMasked">isMasked</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this element is masked</div><div class="long">Returns true if this element is masked<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#isMasked" ext:member="#isMasked" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-isScrollable"></a><b><a href="source/Element.scroll.html#method-Ext.Element-isScrollable">isScrollable</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this element is scrollable.</div><div class="long">Returns true if this element is scrollable.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#isScrollable" ext:member="#isScrollable" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-isVisible"></a><b><a href="source/Element.fx.html#method-Ext.Element-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Checks whether the element is currently visible using both visibility and display properties.</div><div class="long">Checks whether the element is currently visible using both visibility and display properties.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the element is currently visible, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#isVisible" ext:member="#isVisible" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-last"></a><b><a href="source/Element.traversal.html#method-Ext.Element-last">last</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;selector</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element/HTMLElement<div class="mdesc"><div class="short">Gets the last child, skipping text nodes</div><div class="long">Gets the last child, skipping text nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">(optional) Find the previous sibling that matches the passed simple selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return a raw dom node instead of an Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element/HTMLElement</code><div class="sub-desc">The last child or null</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#last" ext:member="#last" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-load"></a><b><a href="source/Element.html#method-Ext.Element-load">load</a></b>(&nbsp;<code>Mixed&nbsp;options.</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Updates the &lt;a href="http://developer.mozilla.org/en/DOM/element.innerHTML"&gt;innerHTML of this Element
from a specifie...</div><div class="long"><p>Updates the <a href="http://developer.mozilla.org/en/DOM/element.innerHTML">innerHTML</a> of this Element
from a specified URL. Note that this is subject to the <a href="http://en.wikipedia.org/wiki/Same_origin_policy">Same Origin Policy</a></p>
<p>Updating innerHTML of an element will <b>not</b> execute embedded <tt>&lt;script></tt> elements. This is a browser restriction.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options.</code> : Mixed<div class="sub-desc">Either a sring containing the URL from which to load the HTML, or an <a href="output/Ext.Ajax.html#Ext.Ajax-request" ext:member="request" ext:cls="Ext.Ajax">Ext.Ajax.request</a> options object specifying
exactly how to request the HTML.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#load" ext:member="#load" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mask"></a><b><a href="source/Element.fx-more.html#method-Ext.Element-mask">mask</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;msg</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;msgCls</code>]</span>&nbsp;)
    :
                                        Element<div class="mdesc"><div class="short">Puts a mask over this element to disable user interaction. Requires core.css.&#13;
This method can only be applied to ele...</div><div class="long">Puts a mask over this element to disable user interaction. Requires core.css.
This method can only be applied to elements which accept child nodes.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>msg</code> : String<div class="sub-desc">(optional) A message to display in the mask</div></li><li><code>msgCls</code> : String<div class="sub-desc">(optional) A css class to apply to the msg element</div></li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The mask element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mask" ext:member="#mask" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-move"></a><b><a href="source/Element.position-more.html#method-Ext.Element-move">move</a></b>(&nbsp;<code>String&nbsp;direction</code>,&nbsp;<code>Number&nbsp;distance</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Move this element relative to its current position.</div><div class="long">Move this element relative to its current position.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>direction</code> : String<div class="sub-desc">Possible values are: "l" (or "left"), "r" (or "right"), "t" (or "top", or "up"), "b" (or "bottom", or "down").</div></li><li><code>distance</code> : Number<div class="sub-desc">How far to move the element in pixels</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#move" ext:member="#move" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-moveTo"></a><b><a href="source/Element.position.html#method-Ext.Element-moveTo">moveTo</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the position of the element in page coordinates, regardless of how the element is positioned.&#13;
The element must ...</div><div class="long">Sets the position of the element in page coordinates, regardless of how the element is positioned.
The element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">X value for new position (coordinates are page-based)</div></li><li><code>y</code> : Number<div class="sub-desc">Y value for new position (coordinates are page-based)</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#moveTo" ext:member="#moveTo" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-next"></a><b><a href="source/Element.traversal.html#method-Ext.Element-next">next</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;selector</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element/HTMLElement<div class="mdesc"><div class="short">Gets the next sibling, skipping text nodes</div><div class="long">Gets the next sibling, skipping text nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">(optional) Find the next sibling that matches the passed simple selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return a raw dom node instead of an Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element/HTMLElement</code><div class="sub-desc">The next sibling or null</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#next" ext:member="#next" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-on"></a><b><a href="source/Element.html#method-Ext.Element-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler (shorthand for addListener).</div><div class="long">Appends an event handler (shorthand for <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a>).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to handle</div></li><li><code>fn</code> : Function<div class="sub-desc">The handler function the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (this element) of the handler function</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing standard <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> options</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#on" ext:member="#on" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-parent"></a><b><a href="source/Element.traversal.html#method-Ext.Element-parent">parent</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;selector</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element/HTMLElement<div class="mdesc"><div class="short">Gets the parent node for this element, optionally chaining up trying to match a selector</div><div class="long">Gets the parent node for this element, optionally chaining up trying to match a selector<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">(optional) Find a parent node that matches the passed simple selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return a raw dom node instead of an Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element/HTMLElement</code><div class="sub-desc">The parent node or null</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#parent" ext:member="#parent" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-position"></a><b><a href="source/Element.position.html#method-Ext.Element-position">position</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;pos</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;zIndex</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;x</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;y</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Initializes positioning on this element. If a desired position is not passed, it will make the&#13;
the element positione...</div><div class="long">Initializes positioning on this element. If a desired position is not passed, it will make the
the element positioned relative IF it is not already positioned.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>pos</code> : String<div class="sub-desc">(optional) Positioning to use "relative", "absolute" or "fixed"</div></li><li><code>zIndex</code> : Number<div class="sub-desc">(optional) The zIndex to apply</div></li><li><code>x</code> : Number<div class="sub-desc">(optional) Set the page X position</div></li><li><code>y</code> : Number<div class="sub-desc">(optional) Set the page Y position</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#position" ext:member="#position" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-prev"></a><b><a href="source/Element.traversal.html#method-Ext.Element-prev">prev</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;selector</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        Ext.Element/HTMLElement<div class="mdesc"><div class="short">Gets the previous sibling, skipping text nodes</div><div class="long">Gets the previous sibling, skipping text nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">(optional) Find the previous sibling that matches the passed simple selector</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return a raw dom node instead of an Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element/HTMLElement</code><div class="sub-desc">The previous sibling or null</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#prev" ext:member="#prev" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-query"></a><b><a href="source/Element.traversal.html#method-Ext.Element-query">query</a></b>(&nbsp;<code>String&nbsp;selector</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Selects child nodes based on the passed CSS selector (the selector should not contain an id).</div><div class="long">Selects child nodes based on the passed CSS selector (the selector should not contain an id).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The CSS selector</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">An array of the matched nodes</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#query" ext:member="#query" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-radioClass"></a><b><a href="source/Element.style.html#method-Ext.Element-radioClass">radioClass</a></b>(&nbsp;<code>String/Array&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Adds one or more CSS classes to this element and removes the same class(es) from all siblings.</div><div class="long">Adds one or more CSS classes to this element and removes the same class(es) from all siblings.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String/Array<div class="sub-desc">The CSS class to add, or an array of classes</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#radioClass" ext:member="#radioClass" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-relayEvent"></a><b><a href="source/Element-more.html#method-Ext.Element-relayEvent">relayEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Create an event handler on this element such that when the event fires and is handled by this element,&#13;
it will be re...</div><div class="long">Create an event handler on this element such that when the event fires and is handled by this element,
it will be relayed to another object (i.e., fired again as if it originated from that object instead).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to relay</div></li><li><code>object</code> : Object<div class="sub-desc">Any object that extends <a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable">Ext.util.Observable</a> that will provide the context
for firing the relayed event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#relayEvent" ext:member="#relayEvent" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-remove"></a><b><a href="source/Element.html#method-Ext.Element-remove">remove</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes this element from the DOM and deletes it from the cache</div><div class="long">Removes this element from the DOM and deletes it from the cache<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#remove" ext:member="#remove" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-removeAllListeners"></a><b><a href="source/Element.html#method-Ext.Element-removeAllListeners">removeAllListeners</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Removes all previous added listeners from this element</div><div class="long">Removes all previous added listeners from this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#removeAllListeners" ext:member="#removeAllListeners" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-removeClass"></a><b><a href="source/Element.style.html#method-Ext.Element-removeClass">removeClass</a></b>(&nbsp;<code>String/Array&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Removes one or more CSS classes from the element.</div><div class="long">Removes one or more CSS classes from the element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String/Array<div class="sub-desc">The CSS class to remove, or an array of classes</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#removeClass" ext:member="#removeClass" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-removeListener"></a><b><a href="source/Element.html#method-Ext.Element-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Removes an event handler from this element.  The shorthand version un is equivalent.
Note: if a scope was explicitly ...</div><div class="long">Removes an event handler from this element.  The shorthand version <a href="output/Ext.Element.html#Ext.Element-un" ext:member="un" ext:cls="Ext.Element">un</a> is equivalent.
<b>Note</b>: if a <i>scope</i> was explicitly specified when <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">adding</a> the
listener, the same scope must be specified here.
Example:
<pre><code>el.removeListener(<em>'click'</em>, this.handlerFn);
<i>// or</i>
el.un(<em>'click'</em>, this.handlerFn);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">the type of event to remove</div></li><li><code>fn</code> : Function<div class="sub-desc">the method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (The <tt>this</tt> reference) of the handler function. Defaults
to this Element.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#removeListener" ext:member="#removeListener" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-repaint"></a><b><a href="source/Element.style-more.html#method-Ext.Element-repaint">repaint</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Forces the browser to repaint this element</div><div class="long">Forces the browser to repaint this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#repaint" ext:member="#repaint" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-replace"></a><b><a href="source/Element.insertion.html#method-Ext.Element-replace">replace</a></b>(&nbsp;<code>Mixed&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Replaces the passed element with this element</div><div class="long">Replaces the passed element with this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element to replace</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#replace" ext:member="#replace" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-replaceClass"></a><b><a href="source/Element.style.html#method-Ext.Element-replaceClass">replaceClass</a></b>(&nbsp;<code>String&nbsp;oldClassName</code>,&nbsp;<code>String&nbsp;newClassName</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Replaces a CSS class on the element with another.  If the old name does not exist, the new name will simply be added.</div><div class="long">Replaces a CSS class on the element with another.  If the old name does not exist, the new name will simply be added.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>oldClassName</code> : String<div class="sub-desc">The CSS class to replace</div></li><li><code>newClassName</code> : String<div class="sub-desc">The replacement CSS class</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#replaceClass" ext:member="#replaceClass" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-replaceWith"></a><b><a href="source/Element.insertion.html#method-Ext.Element-replaceWith">replaceWith</a></b>(&nbsp;<code>Mixed/Object&nbsp;el</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Replaces this element with the passed element</div><div class="long">Replaces this element with the passed element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed/Object<div class="sub-desc">The new element or a DomHelper config of an element to create</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#replaceWith" ext:member="#replaceWith" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-scroll"></a><b><a href="source/Element.scroll-more.html#method-Ext.Element-scroll">scroll</a></b>(&nbsp;<code>String&nbsp;direction</code>,&nbsp;<code>Number&nbsp;distance</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Scrolls this element the specified direction. Does bounds checking to make sure the scroll is&#13;
within this element's ...</div><div class="long">Scrolls this element the specified direction. Does bounds checking to make sure the scroll is
within this element's scrollable range.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>direction</code> : String<div class="sub-desc">Possible values are: "l" (or "left"), "r" (or "right"), "t" (or "top", or "up"), "b" (or "bottom", or "down").</div></li><li><code>distance</code> : Number<div class="sub-desc">How far to scroll the element in pixels</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">Returns true if a scroll was triggered or false if the element&#13;
was scrolled as far as it could go.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#scroll" ext:member="#scroll" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-scrollIntoView"></a><b><a href="source/Element.scroll-more.html#method-Ext.Element-scrollIntoView">scrollIntoView</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Mixed&nbsp;container</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;hscroll</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Scrolls this element into view within the passed container.</div><div class="long">Scrolls this element into view within the passed container.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>container</code> : Mixed<div class="sub-desc">(optional) The container element to scroll (defaults to document.body).  Should be a
string (id), dom node, or Ext.Element.</div></li><li><code>hscroll</code> : Boolean<div class="sub-desc">(optional) False to disable horizontal scroll (defaults to true)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#scrollIntoView" ext:member="#scrollIntoView" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-scrollTo"></a><b><a href="source/Element.scroll-more.html#method-Ext.Element-scrollTo">scrollTo</a></b>(&nbsp;<code>String&nbsp;side</code>,&nbsp;<code>Number&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Element<div class="mdesc"><div class="short">Scrolls this element the specified scroll point. It does NOT do bounds checking so if you scroll to a weird value it ...</div><div class="long">Scrolls this element the specified scroll point. It does NOT do bounds checking so if you scroll to a weird value it will try to do it. For auto bounds checking, use scroll().<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>side</code> : String<div class="sub-desc">Either "left" for scrollLeft values or "top" for scrollTop values.</div></li><li><code>value</code> : Number<div class="sub-desc">The new scroll value</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#scrollTo" ext:member="#scrollTo" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-select"></a><b><a href="source/CompositeElement.html#method-Ext.Element-select">select</a></b>(&nbsp;<code>String/Array&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;unique</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>HTMLElement/String&nbsp;root</code>]</span>&nbsp;)
    :
                                        CompositeElementLite/CompositeElement<div class="mdesc"><div class="short">Selects elements based on the passed CSS selector to enable Element methods&#13;
to be applied to many related elements i...</div><div class="long">Selects elements based on the passed CSS selector to enable <a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> methods
to be applied to many related elements in one statement through the returned <a href="output/Ext.CompositeElement.html" ext:cls="Ext.CompositeElement">CompositeElement</a> or
<a href="output/Ext.CompositeElementLite.html" ext:cls="Ext.CompositeElementLite">CompositeElementLite</a> object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String/Array<div class="sub-desc">The CSS selector or an array of elements</div></li><li><code>unique</code> : Boolean<div class="sub-desc">(optional) true to create a unique Ext.Element for each element (defaults to a shared flyweight object)</div></li><li><code>root</code> : HTMLElement/String<div class="sub-desc">(optional) The root element of the query or id of the root</div></li></ul><strong>Returns:</strong><ul><li><code>CompositeElementLite/CompositeElement</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#select" ext:member="#select" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-set"></a><b><a href="source/Element.html#method-Ext.Element-set">set</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;useSet</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the passed attributes as attributes of this element (a style attribute can be a string, object or function)</div><div class="long">Sets the passed attributes as attributes of this element (a style attribute can be a string, object or function)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The object with the attributes</div></li><li><code>useSet</code> : Boolean<div class="sub-desc">(optional) false to override the default setAttribute to use expandos.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#set" ext:member="#set" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setBottom"></a><b><a href="source/Element.position.html#method-Ext.Element-setBottom">setBottom</a></b>(&nbsp;<code>String&nbsp;bottom</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's CSS bottom style.</div><div class="long">Sets the element's CSS bottom style.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>bottom</code> : String<div class="sub-desc">The bottom CSS property value</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setBottom" ext:member="#setBottom" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setBounds"></a><b><a href="source/Element.position-more.html#method-Ext.Element-setBounds">setBounds</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>,&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<code>Mixed&nbsp;height</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's position and size in one shot. If animation is true then width, height, x and y will be animated c...</div><div class="long">Sets the element's position and size in one shot. If animation is true then width, height, x and y will be animated concurrently.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">X value for new position (coordinates are page-based)</div></li><li><code>y</code> : Number<div class="sub-desc">Y value for new position (coordinates are page-based)</div></li><li><code>width</code> : Mixed<div class="sub-desc">The new width. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in this Element's <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">defaultUnit</a>s (by default, pixels)</li>
<li>A String used to set the CSS width style. Animation may <b>not</b> be used.
</ul></div></div></li><li><code>height</code> : Mixed<div class="sub-desc">The new height. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in this Element's <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">defaultUnit</a>s (by default, pixels)</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
</ul></div></div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setBounds" ext:member="#setBounds" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setBox"></a><b><a href="source/Element.position-more.html#method-Ext.Element-setBox">setBox</a></b>(&nbsp;<code>Object&nbsp;box</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;adjust</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's box. Use getBox() on another element to get a box obj. If animate is true then width, height, x an...</div><div class="long">Sets the element's box. Use getBox() on another element to get a box obj. If animate is true then width, height, x and y will be animated concurrently.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>box</code> : Object<div class="sub-desc">The box to fill {x, y, width, height}</div></li><li><code>adjust</code> : Boolean<div class="sub-desc">(optional) Whether to adjust for box-model issues automatically</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setBox" ext:member="#setBox" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setDisplayed"></a><b><a href="source/Element.fx.html#method-Ext.Element-setDisplayed">setDisplayed</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the CSS display property. Uses originalDisplay if the specified value is a boolean true.</div><div class="long">Sets the CSS display property. Uses originalDisplay if the specified value is a boolean true.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">Boolean value to display the element using its default display, or a string to set the display directly.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setDisplayed" ext:member="#setDisplayed" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setHeight"></a><b><a href="source/Element.style.html#method-Ext.Element-setHeight">setHeight</a></b>(&nbsp;<code>Mixed&nbsp;height</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Set the height of this Element.&#13;
// change the height to 200px and animate with default configuration&#13;
Ext.fly('eleme...</div><div class="long">Set the height of this Element.
<pre><code><i>// change the height to 200px and animate <b>with</b> <b>default</b> configuration
</i>
Ext.fly(<em>'elementId'</em>).setHeight(200, true);

<i>// change the height to 150px and animate <b>with</b> a custom configuration
</i>
Ext.fly(<em>'elId'</em>).setHeight(150, {
    duration : .5, <i>// animation will have a duration of .5 seconds
</i>
    <i>// will change the content to <em>"finished"</em>
</i>
    callback: <b>function</b>(){ this.<a href="output/Ext.Element.html#Ext.Element-update" ext:member="update" ext:cls="Ext.Element">update</a>(<em>"finished"</em>); } 
});</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>height</code> : Mixed<div class="sub-desc">The new height. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in this Element's <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">defaultUnit</a>s (by default, pixels.)</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
</ul></div></div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setHeight" ext:member="#setHeight" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setLeft"></a><b><a href="source/Element.position.html#method-Ext.Element-setLeft">setLeft</a></b>(&nbsp;<code>String&nbsp;left</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's left position directly using CSS style (instead of setX).</div><div class="long">Sets the element's left position directly using CSS style (instead of <a href="output/Ext.Element.html#Ext.Element-setX" ext:member="setX" ext:cls="Ext.Element">setX</a>).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : String<div class="sub-desc">The left CSS property value</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setLeft" ext:member="#setLeft" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setLeftTop"></a><b><a href="source/Element.position-more.html#method-Ext.Element-setLeftTop">setLeftTop</a></b>(&nbsp;<code>String&nbsp;left</code>,&nbsp;<code>String&nbsp;top</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Quick set left and top adding default units</div><div class="long">Quick set left and top adding default units<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : String<div class="sub-desc">The left CSS property value</div></li><li><code>top</code> : String<div class="sub-desc">The top CSS property value</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setLeftTop" ext:member="#setLeftTop" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setLocation"></a><b><a href="source/Element.position.html#method-Ext.Element-setLocation">setLocation</a></b>(&nbsp;<code>Number&nbsp;x</code>,&nbsp;<code>Number&nbsp;y</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the position of the element in page coordinates, regardless of how the element is positioned.&#13;
The element must ...</div><div class="long">Sets the position of the element in page coordinates, regardless of how the element is positioned.
The element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number<div class="sub-desc">X value for new position (coordinates are page-based)</div></li><li><code>y</code> : Number<div class="sub-desc">Y value for new position (coordinates are page-based)</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setLocation" ext:member="#setLocation" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setOpacity"></a><b><a href="source/Element.style.html#method-Ext.Element-setOpacity">setOpacity</a></b>(&nbsp;<code>Float&nbsp;opacity</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Set the opacity of the element</div><div class="long">Set the opacity of the element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>opacity</code> : Float<div class="sub-desc">The new opacity. 0 = transparent, .5 = 50% visibile, 1 = fully visible, etc</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) a standard Element animation config object or <tt>true</tt> for
the default animation (<tt>{duration: .35, easing: 'easeIn'}</tt>)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setOpacity" ext:member="#setOpacity" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setPositioning"></a><b><a href="source/Element.position.html#method-Ext.Element-setPositioning">setPositioning</a></b>(&nbsp;<code>Object&nbsp;posCfg</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Set positioning with an object returned by getPositioning().</div><div class="long">Set positioning with an object returned by getPositioning().<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>posCfg</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setPositioning" ext:member="#setPositioning" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setRegion"></a><b><a href="source/Element.position-more.html#method-Ext.Element-setRegion">setRegion</a></b>(&nbsp;<code>Ext.lib.Region&nbsp;region</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's position and size the specified region. If animation is true then width, height, x and y will be a...</div><div class="long">Sets the element's position and size the specified region. If animation is true then width, height, x and y will be animated concurrently.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>region</code> : Ext.lib.Region<div class="sub-desc">The region to fill</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setRegion" ext:member="#setRegion" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setRight"></a><b><a href="source/Element.position.html#method-Ext.Element-setRight">setRight</a></b>(&nbsp;<code>String&nbsp;right</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's CSS right style.</div><div class="long">Sets the element's CSS right style.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>right</code> : String<div class="sub-desc">The right CSS property value</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setRight" ext:member="#setRight" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setSize"></a><b><a href="source/Element.style-more.html#method-Ext.Element-setSize">setSize</a></b>(&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<code>Mixed&nbsp;height</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Set the size of this Element. If animation is true, both width and height will be animated concurrently.</div><div class="long">Set the size of this Element. If animation is true, both width and height will be animated concurrently.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Mixed<div class="sub-desc">The new width. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in this Element's <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style. Animation may <b>not</b> be used.
<li>A size object in the format <code>{width: widthValue, height: heightValue}</code>.</li>
</ul></div></div></li><li><code>height</code> : Mixed<div class="sub-desc">The new height. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new height in this Element's <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS height style. Animation may <b>not</b> be used.</li>
</ul></div></div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setSize" ext:member="#setSize" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setStyle"></a><b><a href="source/Element.style.html#method-Ext.Element-setStyle">setStyle</a></b>(&nbsp;<code>String/Object&nbsp;property</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;value</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Wrapper for setting style properties, also takes single object parameter of multiple styles.</div><div class="long">Wrapper for setting style properties, also takes single object parameter of multiple styles.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>property</code> : String/Object<div class="sub-desc">The style property to be set, or an object of multiple styles.</div></li><li><code>value</code> : String<div class="sub-desc">(optional) The value to apply to the given property, or null if an object was passed.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setStyle" ext:member="#setStyle" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setTop"></a><b><a href="source/Element.position.html#method-Ext.Element-setTop">setTop</a></b>(&nbsp;<code>String&nbsp;top</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's top position directly using CSS style (instead of setY).</div><div class="long">Sets the element's top position directly using CSS style (instead of <a href="output/Ext.Element.html#Ext.Element-setY" ext:member="setY" ext:cls="Ext.Element">setY</a>).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>top</code> : String<div class="sub-desc">The top CSS property value</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setTop" ext:member="#setTop" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setVisibilityMode"></a><b><a href="source/Element.fx.html#method-Ext.Element-setVisibilityMode">setVisibilityMode</a></b>(&nbsp;<code>visMode&nbsp;Ext.Element.VISIBILITY</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the element's visibility mode. When setVisible() is called it&#13;
will use this to determine whether to set the vis...</div><div class="long">Sets the element's visibility mode. When setVisible() is called it
will use this to determine whether to set the visibility or the display property.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>Ext.Element.VISIBILITY</code> : visMode<div class="sub-desc">or Ext.Element.DISPLAY</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setVisibilityMode" ext:member="#setVisibilityMode" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setVisible"></a><b><a href="source/Element.fx.html#method-Ext.Element-setVisible">setVisible</a></b>(&nbsp;<code>Boolean&nbsp;visible</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the visibility of the element (see details). If the visibilityMode is set to Element.DISPLAY, it will use&#13;
the d...</div><div class="long">Sets the visibility of the element (see details). If the visibilityMode is set to Element.DISPLAY, it will use
the display property to hide the element, otherwise it uses visibility. The default is to hide and show using the visibility property.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>visible</code> : Boolean<div class="sub-desc">Whether the element is visible</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setVisible" ext:member="#setVisible" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setWidth"></a><b><a href="source/Element.style.html#method-Ext.Element-setWidth">setWidth</a></b>(&nbsp;<code>Mixed&nbsp;width</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Set the width of this Element.</div><div class="long">Set the width of this Element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Mixed<div class="sub-desc">The new width. This may be one of:<div class="mdetail-params"><ul>
<li>A Number specifying the new width in this Element's <a href="output/Ext.Element.html#Ext.Element-defaultUnit" ext:member="defaultUnit" ext:cls="Ext.Element">defaultUnit</a>s (by default, pixels).</li>
<li>A String used to set the CSS width style. Animation may <b>not</b> be used.
</ul></div></div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setWidth" ext:member="#setWidth" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setX"></a><b><a href="source/Element.position.html#method-Ext.Element-setX">setX</a></b>(&nbsp;<code>Number&nbsp;The</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the X position of the element based on page coordinates.  Element must be part of the DOM tree to have page coor...</div><div class="long">Sets the X position of the element based on page coordinates.  Element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>The</code> : Number<div class="sub-desc">X position of the element</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setX" ext:member="#setX" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setXY"></a><b><a href="source/Element.position.html#method-Ext.Element-setXY">setXY</a></b>(&nbsp;<code>Array&nbsp;pos</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the position of the element in page coordinates, regardless of how the element is positioned.&#13;
The element must ...</div><div class="long">Sets the position of the element in page coordinates, regardless of how the element is positioned.
The element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>pos</code> : Array<div class="sub-desc">Contains X & Y [x, y] values for new position (coordinates are page-based)</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setXY" ext:member="#setXY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-setY"></a><b><a href="source/Element.position.html#method-Ext.Element-setY">setY</a></b>(&nbsp;<code>Number&nbsp;The</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Sets the Y position of the element based on page coordinates.  Element must be part of the DOM tree to have page coor...</div><div class="long">Sets the Y position of the element based on page coordinates.  Element must be part of the DOM tree to have page coordinates (display:none or elements not appended return false).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>The</code> : Number<div class="sub-desc">Y position of the element</div></li><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#setY" ext:member="#setY" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Layer-setZIndex"></a><b><a href="source/Layer.html#method-Ext.Layer-setZIndex">setZIndex</a></b>(&nbsp;<code>Number&nbsp;zindex</code>&nbsp;)
    :
                                        this<div class="mdesc"><div class="short">Sets the z-index of this layer and adjusts any shadow and shim z-indexes. The layer z-index is automatically
incremen...</div><div class="long">Sets the z-index of this layer and adjusts any shadow and shim z-indexes. The layer z-index is automatically
incremented by two more than the value passed in so that it always shows above any shadow or shim (the shadow
element, if any, will be assigned z-index + 1, and the shim element, if any, will be assigned the unmodified z-index).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>zindex</code> : Number<div class="sub-desc">The new z-index to set</div></li></ul><strong>Returns:</strong><ul><li><code>this</code><div class="sub-desc">The Layer</div></li></ul></div></div></div></td><td class="msource">Layer</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-show"></a><b><a href="source/Element.fx.html#method-Ext.Element-show">show</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Show this element - Uses display mode to determine whether to use "display" or "visibility". See setVisible.</div><div class="long">Show this element - Uses display mode to determine whether to use "display" or "visibility". See <a href="output/Ext.Element.html#Ext.Element-setVisible" ext:member="setVisible" ext:cls="Ext.Element">setVisible</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) true for the default animation or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#show" ext:member="#show" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-swallowEvent"></a><b><a href="source/Element-more.html#method-Ext.Element-swallowEvent">swallowEvent</a></b>(&nbsp;<code>String/Array&nbsp;eventName</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;preventDefault</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Stops the specified event(s) from bubbling and optionally prevents the default action</div><div class="long">Stops the specified event(s) from bubbling and optionally prevents the default action<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String/Array<div class="sub-desc">an event / array of events to stop from bubbling</div></li><li><code>preventDefault</code> : Boolean<div class="sub-desc">(optional) true to prevent the default action too</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#swallowEvent" ext:member="#swallowEvent" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-toggle"></a><b><a href="source/Element.fx.html#method-Ext.Element-toggle">toggle</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean/Object&nbsp;animate</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Toggles the element's visibility or display, depending on visibility mode.</div><div class="long">Toggles the element's visibility or display, depending on visibility mode.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>animate</code> : Boolean/Object<div class="sub-desc">(optional) True for the default animation, or a standard Element animation config object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#toggle" ext:member="#toggle" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-toggleClass"></a><b><a href="source/Element.style.html#method-Ext.Element-toggleClass">toggleClass</a></b>(&nbsp;<code>String&nbsp;className</code>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Toggles the specified CSS class on this element (removes it if it already exists, otherwise adds it).</div><div class="long">Toggles the specified CSS class on this element (removes it if it already exists, otherwise adds it).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String<div class="sub-desc">The CSS class to toggle</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#toggleClass" ext:member="#toggleClass" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-translatePoints"></a><b><a href="source/Element.position.html#method-Ext.Element-translatePoints">translatePoints</a></b>(&nbsp;<code>Number/Array&nbsp;x</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;y</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Translates the passed page coordinates into left/top css values for this element</div><div class="long">Translates the passed page coordinates into left/top css values for this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>x</code> : Number/Array<div class="sub-desc">The page x or an array containing [x, y]</div></li><li><code>y</code> : Number<div class="sub-desc">(optional) The page y, required if x is not an array</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object with left and top properties. e.g. {left: (value), top: (value)}</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#translatePoints" ext:member="#translatePoints" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-un"></a><b><a href="source/Element.html#method-Ext.Element-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Removes an event handler from this element (see removeListener for additional notes).</div><div class="long">Removes an event handler from this element (see <a href="output/Ext.Element.html#Ext.Element-removeListener" ext:member="removeListener" ext:cls="Ext.Element">removeListener</a> for additional notes).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">the type of event to remove</div></li><li><code>fn</code> : Function<div class="sub-desc">the method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (The <tt>this</tt> reference) of the handler function. Defaults
to this Element.</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#un" ext:member="#un" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-unclip"></a><b><a href="source/Element.style.html#method-Ext.Element-unclip">unclip</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Return clipping (overflow) to original clipping before clip was called</div><div class="long">Return clipping (overflow) to original clipping before <tt><a href="output/Ext.Element.html#Ext.Element-clip" ext:member="clip" ext:cls="Ext.Element">clip</a></tt> was called<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#unclip" ext:member="#unclip" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-unmask"></a><b><a href="source/Element.fx-more.html#method-Ext.Element-unmask">unmask</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes a previously applied mask.</div><div class="long">Removes a previously applied mask.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#unmask" ext:member="#unmask" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-unselectable"></a><b><a href="source/Element.style-more.html#method-Ext.Element-unselectable">unselectable</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Disables text selection for this element (normalized across browsers)</div><div class="long">Disables text selection for this element (normalized across browsers)<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#unselectable" ext:member="#unselectable" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-up"></a><b><a href="source/Element.traversal.html#method-Ext.Element-up">up</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Number/Mixed&nbsp;maxDepth</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Walks up the dom looking for a parent node that matches the passed simple selector (e.g. div.some-class or span:first...</div><div class="long">Walks up the dom looking for a parent node that matches the passed simple selector (e.g. div.some-class or span:first-child).
This is a shortcut for findParentNode() that always returns an Ext.Element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The simple selector to test</div></li><li><code>maxDepth</code> : Number/Mixed<div class="sub-desc">(optional) The max depth to
	            search as a number or element (defaults to 10 || document.body)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The matching DOM node (or null if no match was found)</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#up" ext:member="#up" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-update"></a><b><a href="source/Element-more.html#method-Ext.Element-update">update</a></b>(&nbsp;<code>String&nbsp;html</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;loadScripts</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>&nbsp;)
    :
                                        Ext.Element<div class="mdesc"><div class="short">Update the innerHTML of this element, optionally searching for and processing scripts</div><div class="long">Update the innerHTML of this element, optionally searching for and processing scripts<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>html</code> : String<div class="sub-desc">The new HTML</div></li><li><code>loadScripts</code> : Boolean<div class="sub-desc">(optional) True to look for and process scripts (defaults to false)</div></li><li><code>callback</code> : Function<div class="sub-desc">(optional) For async script loading you can be notified when the update completes</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#update" ext:member="#update" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-wrap"></a><b><a href="source/Element.insertion.html#method-Ext.Element-wrap">wrap</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;config</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;returnDom</code>]</span>&nbsp;)
    :
                                        HTMLElement/Element<div class="mdesc"><div class="short">Creates and wraps this element with another element</div><div class="long">Creates and wraps this element with another element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">(optional) DomHelper element config object for the wrapper element or null for an empty div</div></li><li><code>returnDom</code> : Boolean<div class="sub-desc">(optional) True to return the raw DOM element instead of Ext.Element</div></li></ul><strong>Returns:</strong><ul><li><code>HTMLElement/Element</code><div class="sub-desc">The newly created wrapper element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#wrap" ext:member="#wrap" ext:cls="Ext.Element">Element</a></td></tr></tbody></table><a id="Ext.Layer-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMActivate"></a><b><a href="source/Element.html#event-Ext.Element-DOMActivate">DOMActivate</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when an element is activated, for instance, through a mouse click or a keypress.</div><div class="long">Where supported. Fires when an element is activated, for instance, through a mouse click or a keypress.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMActivate" ext:member="#DOMActivate" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMAttrModified"></a><b><a href="source/Element.html#event-Ext.Element-DOMAttrModified">DOMAttrModified</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when an attribute has been modified.</div><div class="long">Where supported. Fires when an attribute has been modified.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMAttrModified" ext:member="#DOMAttrModified" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMCharacterDataModified"></a><b><a href="source/Element.html#event-Ext.Element-DOMCharacterDataModified">DOMCharacterDataModified</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when the character data has been modified.</div><div class="long">Where supported. Fires when the character data has been modified.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMCharacterDataModified" ext:member="#DOMCharacterDataModified" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMFocusIn"></a><b><a href="source/Element.html#event-Ext.Element-DOMFocusIn">DOMFocusIn</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Similar to HTML focus event, but can be applied to any focusable element.</div><div class="long">Where supported. Similar to HTML focus event, but can be applied to any focusable element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMFocusIn" ext:member="#DOMFocusIn" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMFocusOut"></a><b><a href="source/Element.html#event-Ext.Element-DOMFocusOut">DOMFocusOut</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Similar to HTML blur event, but can be applied to any focusable element.</div><div class="long">Where supported. Similar to HTML blur event, but can be applied to any focusable element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMFocusOut" ext:member="#DOMFocusOut" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMNodeInserted"></a><b><a href="source/Element.html#event-Ext.Element-DOMNodeInserted">DOMNodeInserted</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when a node has been added as a child of another node.</div><div class="long">Where supported. Fires when a node has been added as a child of another node.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMNodeInserted" ext:member="#DOMNodeInserted" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMNodeInsertedIntoDocument"></a><b><a href="source/Element.html#event-Ext.Element-DOMNodeInsertedIntoDocument">DOMNodeInsertedIntoDocument</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when a node is being inserted into a document.</div><div class="long">Where supported. Fires when a node is being inserted into a document.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMNodeInsertedIntoDocument" ext:member="#DOMNodeInsertedIntoDocument" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMNodeRemoved"></a><b><a href="source/Element.html#event-Ext.Element-DOMNodeRemoved">DOMNodeRemoved</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when a descendant node of the element is removed.</div><div class="long">Where supported. Fires when a descendant node of the element is removed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMNodeRemoved" ext:member="#DOMNodeRemoved" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMNodeRemovedFromDocument"></a><b><a href="source/Element.html#event-Ext.Element-DOMNodeRemovedFromDocument">DOMNodeRemovedFromDocument</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when a node is being removed from a document.</div><div class="long">Where supported. Fires when a node is being removed from a document.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMNodeRemovedFromDocument" ext:member="#DOMNodeRemovedFromDocument" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-DOMSubtreeModified"></a><b><a href="source/Element.html#event-Ext.Element-DOMSubtreeModified">DOMSubtreeModified</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Where supported. Fires when the subtree is modified.</div><div class="long">Where supported. Fires when the subtree is modified.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#DOMSubtreeModified" ext:member="#DOMSubtreeModified" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-abort"></a><b><a href="source/Element.html#event-Ext.Element-abort">abort</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an object/image is stopped from loading before completely loaded.</div><div class="long">Fires when an object/image is stopped from loading before completely loaded.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#abort" ext:member="#abort" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-blur"></a><b><a href="source/Element.html#event-Ext.Element-blur">blur</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an element loses focus either via the pointing device or by tabbing navigation.</div><div class="long">Fires when an element loses focus either via the pointing device or by tabbing navigation.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#blur" ext:member="#blur" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-change"></a><b><a href="source/Element.html#event-Ext.Element-change">change</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a control loses the input focus and its value has been modified since gaining focus.</div><div class="long">Fires when a control loses the input focus and its value has been modified since gaining focus.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#change" ext:member="#change" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-click"></a><b><a href="source/Element.html#event-Ext.Element-click">click</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mouse click is detected within the element.</div><div class="long">Fires when a mouse click is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#click" ext:member="#click" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-dblclick"></a><b><a href="source/Element.html#event-Ext.Element-dblclick">dblclick</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mouse double click is detected within the element.</div><div class="long">Fires when a mouse double click is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#dblclick" ext:member="#dblclick" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-error"></a><b><a href="source/Element.html#event-Ext.Element-error">error</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an object/image/frame cannot be loaded properly.</div><div class="long">Fires when an object/image/frame cannot be loaded properly.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#error" ext:member="#error" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-focus"></a><b><a href="source/Element.html#event-Ext.Element-focus">focus</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an element receives focus either via the pointing device or by tab navigation.</div><div class="long">Fires when an element receives focus either via the pointing device or by tab navigation.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#focus" ext:member="#focus" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-keydown"></a><b><a href="source/Element.html#event-Ext.Element-keydown">keydown</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a keydown is detected within the element.</div><div class="long">Fires when a keydown is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#keydown" ext:member="#keydown" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-keypress"></a><b><a href="source/Element.html#event-Ext.Element-keypress">keypress</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a keypress is detected within the element.</div><div class="long">Fires when a keypress is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#keypress" ext:member="#keypress" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-keyup"></a><b><a href="source/Element.html#event-Ext.Element-keyup">keyup</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a keyup is detected within the element.</div><div class="long">Fires when a keyup is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#keyup" ext:member="#keyup" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-load"></a><b><a href="source/Element.html#event-Ext.Element-load">load</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the user agent finishes loading all content within the element. Only supported by window, frames, objects ...</div><div class="long">Fires when the user agent finishes loading all content within the element. Only supported by window, frames, objects and images.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#load" ext:member="#load" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mousedown"></a><b><a href="source/Element.html#event-Ext.Element-mousedown">mousedown</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mousedown is detected within the element.</div><div class="long">Fires when a mousedown is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mousedown" ext:member="#mousedown" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mouseenter"></a><b><a href="source/Element.html#event-Ext.Element-mouseenter">mouseenter</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the mouse enters the element.</div><div class="long">Fires when the mouse enters the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mouseenter" ext:member="#mouseenter" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mouseleave"></a><b><a href="source/Element.html#event-Ext.Element-mouseleave">mouseleave</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the mouse leaves the element.</div><div class="long">Fires when the mouse leaves the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mouseleave" ext:member="#mouseleave" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mousemove"></a><b><a href="source/Element.html#event-Ext.Element-mousemove">mousemove</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mousemove is detected with the element.</div><div class="long">Fires when a mousemove is detected with the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mousemove" ext:member="#mousemove" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mouseout"></a><b><a href="source/Element.html#event-Ext.Element-mouseout">mouseout</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mouseout is detected with the element.</div><div class="long">Fires when a mouseout is detected with the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mouseout" ext:member="#mouseout" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mouseover"></a><b><a href="source/Element.html#event-Ext.Element-mouseover">mouseover</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mouseover is detected within the element.</div><div class="long">Fires when a mouseover is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mouseover" ext:member="#mouseover" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-mouseup"></a><b><a href="source/Element.html#event-Ext.Element-mouseup">mouseup</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a mouseup is detected within the element.</div><div class="long">Fires when a mouseup is detected within the element.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#mouseup" ext:member="#mouseup" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-reset"></a><b><a href="source/Element.html#event-Ext.Element-reset">reset</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a form is reset.</div><div class="long">Fires when a form is reset.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#reset" ext:member="#reset" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-resize"></a><b><a href="source/Element.html#event-Ext.Element-resize">resize</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a document view is resized.</div><div class="long">Fires when a document view is resized.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#resize" ext:member="#resize" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-scroll"></a><b><a href="source/Element.html#event-Ext.Element-scroll">scroll</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a document view is scrolled.</div><div class="long">Fires when a document view is scrolled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#scroll" ext:member="#scroll" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-select"></a><b><a href="source/Element.html#event-Ext.Element-select">select</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a user selects some text in a text field, including input and textarea.</div><div class="long">Fires when a user selects some text in a text field, including input and textarea.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#select" ext:member="#select" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-submit"></a><b><a href="source/Element.html#event-Ext.Element-submit">submit</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a form is submitted.</div><div class="long">Fires when a form is submitted.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#submit" ext:member="#submit" ext:cls="Ext.Element">Element</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Element-unload"></a><b><a href="source/Element.html#event-Ext.Element-unload">unload</a></b> :
                                      (&nbsp;<code>Ext.EventObject&nbsp;e</code>,&nbsp;<code>HtmlElement&nbsp;t</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the user agent removes all content from a window or frame. For elements, it fires when the target element ...</div><div class="long">Fires when the user agent removes all content from a window or frame. For elements, it fires when the target element or any of its content has been removed.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : Ext.EventObject<div class="sub-desc">The <a href="output/Ext.EventObject.html" ext:cls="Ext.EventObject">Ext.EventObject</a> encapsulating the DOM event.</div></li><li><code>t</code> : HtmlElement<div class="sub-desc">The target of the event.</div></li><li><code>o</code> : Object<div class="sub-desc">The options configuration passed to the <a href="output/Ext.Element.html#Ext.Element-addListener" ext:member="addListener" ext:cls="Ext.Element">addListener</a> call.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.Element.html#unload" ext:member="#unload" ext:cls="Ext.Element">Element</a></td></tr></tbody></table></div>