<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.DropZone-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.DropZone-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.DropZone-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.dd.DropZone-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.DropZone"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.dd.DragDrop.html" ext:member="" ext:cls="Ext.dd.DragDrop">DragDrop</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.dd.DDTarget.html" ext:member="" ext:cls="Ext.dd.DDTarget">DDTarget</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.dd.DropTarget.html" ext:member="" ext:cls="Ext.dd.DropTarget">DropTarget</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">DropZone</pre></div><h1>Class <a href="source/DropZone.html#cls-Ext.dd.DropZone">Ext.dd.DropZone</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DropZone.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DropZone.html#cls-Ext.dd.DropZone">DropZone</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.tree.TreeDropZone.html" ext:cls="Ext.tree.TreeDropZone">TreeDropZone</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.dd.DropTarget.html" ext:cls="Ext.dd.DropTarget" ext:member="">DropTarget</a></td></tr></table><div class="description"><p>This class provides a container DD instance that allows dropping on multiple child target nodes.</p>
<p>By default, this class requires that child nodes accepting drop are registered with <a href="output/Ext.dd.Registry.html" ext:cls="Ext.dd.Registry">Ext.dd.Registry</a>.
However a simpler way to allow a DropZone to manage any number of target elements is to configure the
DropZone with an implementation of <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a> which interrogates the passed
mouse event to see if it has taken place within an element, or class of elements. This is easily done
by using the event's <a href="output/Ext.EventObject.html#Ext.EventObject-getTarget" ext:member="getTarget" ext:cls="Ext.EventObject">getTarget</a> method to identify a node based on a
<a href="output/Ext.DomQuery.html" ext:cls="Ext.DomQuery">Ext.DomQuery</a> selector.</p>
<p>Once the DropZone has detected through calling getTargetFromEvent, that the mouse is over
a drop target, that target is passed as the first parameter to <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeEnter" ext:member="onNodeEnter" ext:cls="Ext.dd.DropZone">onNodeEnter</a>, <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOver" ext:member="onNodeOver" ext:cls="Ext.dd.DropZone">onNodeOver</a>,
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOut" ext:member="onNodeOut" ext:cls="Ext.dd.DropZone">onNodeOut</a>, <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeDrop" ext:member="onNodeDrop" ext:cls="Ext.dd.DropZone">onNodeDrop</a>. You may configure the instance of DropZone with implementations
of these methods to provide application-specific behaviour for these events to update both
application state, and UI state.</p>
<p>For example to make a GridPanel a cooperating target with the example illustrated in
<a href="output/Ext.dd.DragZone.html" ext:cls="Ext.dd.DragZone">DragZone</a>, the following technique might be used:</p><pre><code>myGridPanel.on(<em>'render'</em>, <b>function</b>() {
    myGridPanel.dropZone = <b>new</b> Ext.dd.DropZone(myGridPanel.getView().scroller, {

<i>//      If the mouse is over a grid row, <b>return</b> that node. This is
</i>
<i>//      provided as the <em>"target"</em> parameter <b>in</b> all <em>"onNodeXXXX"</em> node event handling functions
</i>
        getTargetFromEvent: <b>function</b>(e) {
            <b>return</b> e.getTarget(myGridPanel.getView().rowSelector);
        },

<i>//      On entry into a target node, highlight that node.
</i>
        onNodeEnter : <b>function</b>(target, dd, e, data){ 
            Ext.fly(target).addClass(<em>'my-row-highlight-class'</em>);
        },

<i>//      On exit from a target node, unhighlight that node.
</i>
        onNodeOut : <b>function</b>(target, dd, e, data){ 
            Ext.fly(target).removeClass(<em>'my-row-highlight-class'</em>);
        },

<i>//      While over a target node, <b>return</b> the <b>default</b> drop allowed class which
</i>
<i>//      places a <em>"tick"</em> icon into the drag proxy.
</i>
        onNodeOver : <b>function</b>(target, dd, e, data){ 
            <b>return</b> Ext.dd.DropZone.prototype.dropAllowed;
        },

<i>//      On node drop we can interrogate the target to find the underlying
</i>
<i>//      application object that is the real target of the dragged data.
</i>
<i>//      In this <b>case</b>, it is a Record <b>in</b> the GridPanel<em>'s Store.
</i>
<i>//      We can use the data set up by the DragZone'</em>s getDragData method to read
</i>
<i>//      any data we decided to attach <b>in</b> the DragZone<em>'s getDragData method.
</i>
        onNodeDrop : <b>function</b>(target, dd, e, data){
            <b>var</b> rowIndex = myGridPanel.getView().findRowIndex(target);
            <b>var</b> r = myGridPanel.getStore().getAt(rowIndex);
            Ext.Msg.alert('</em>Drop gesture<em>', '</em>Dropped Record id <em>' + data.draggedRecord.id +
                '</em> on Record id <em>' + r.id);
            <b>return</b> true;
        }
    });
}</code></pre>
See the <a href="output/Ext.dd.DragZone.html" ext:cls="Ext.dd.DragZone">DragZone</a> documentation for details about building a DragZone which
cooperates with this DropZone.</div><div class="hr"></div><a id="Ext.dd.DropZone-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropTarget-ddGroup"></a><b><a href="source/DropTarget.html#cfg-Ext.dd.DropTarget-ddGroup">ddGroup</a></b> : String<div class="mdesc"><div class="short">A named drag drop group to which this object belongs.  If a group is specified, then this object will only&#13;
interact ...</div><div class="long">A named drag drop group to which this object belongs.  If a group is specified, then this object will only
interact with other drag drop objects in the same group (defaults to undefined).</div></div></td><td class="msource"><a href="output/Ext.dd.DropTarget.html#ddGroup" ext:member="#ddGroup" ext:cls="Ext.dd.DropTarget">DropTarget</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropTarget-dropAllowed"></a><b><a href="source/DropTarget.html#cfg-Ext.dd.DropTarget-dropAllowed">dropAllowed</a></b> : String<div class="mdesc">The CSS class returned to the drag source when drop is allowed (defaults to "x-dd-drop-ok").</div></td><td class="msource"><a href="output/Ext.dd.DropTarget.html#dropAllowed" ext:member="#dropAllowed" ext:cls="Ext.dd.DropTarget">DropTarget</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropTarget-dropNotAllowed"></a><b><a href="source/DropTarget.html#cfg-Ext.dd.DropTarget-dropNotAllowed">dropNotAllowed</a></b> : String<div class="mdesc">The CSS class returned to the drag source when drop is not allowed (defaults to "x-dd-drop-nodrop").</div></td><td class="msource"><a href="output/Ext.dd.DropTarget.html#dropNotAllowed" ext:member="#dropNotAllowed" ext:cls="Ext.dd.DropTarget">DropTarget</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropTarget-overClass"></a><b><a href="source/DropTarget.html#cfg-Ext.dd.DropTarget-overClass">overClass</a></b> : String<div class="mdesc">The CSS class applied to the drop target element while the drag source is over it (defaults to "").</div></td><td class="msource"><a href="output/Ext.dd.DropTarget.html#overClass" ext:member="#overClass" ext:cls="Ext.dd.DropTarget">DropTarget</a></td></tr></tbody></table><a id="Ext.dd.DropZone-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-Only"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-Only">Only</a></b> : Object<div class="mdesc"><div class="short">The drag and drop utility provides a framework for building drag and drop
applications.  In addition to enabling drag...</div><div class="long">The drag and drop utility provides a framework for building drag and drop
applications.  In addition to enabling drag and drop for specific elements,
the drag and drop elements are tracked by the manager class, and the
interactions between the various elements are tracked during the drag and
the implementing code is notified about these important moments.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#Only" ext:member="#Only" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-available"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-available">available</a></b> : boolean<div class="mdesc">The availabe property is false until the linked dom element is accessible.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#available" ext:member="#available" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-config"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-config">config</a></b> : object<div class="mdesc">Configuration attributes passed into the constructor</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#config" ext:member="#config" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-defaultPadding"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-defaultPadding">defaultPadding</a></b> : Object<div class="mdesc">Provides default constraint padding to "constrainTo" elements (defaults to {left: 0, right:0, top:0, bottom:0}).</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#defaultPadding" ext:member="#defaultPadding" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-groups"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-groups">groups</a></b> : object An object in the format {'group1':true, 'group2':true<div class="mdesc"><div class="short">The group defines a logical collection of DragDrop objects that are
related.  Instances only get events when interact...</div><div class="long">The group defines a logical collection of DragDrop objects that are
related.  Instances only get events when interacting with other
DragDrop object in the same group.  This lets us define multiple
groups using a single DragDrop subclass if we want.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#groups" ext:member="#groups" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-hasOuterHandles"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-hasOuterHandles">hasOuterHandles</a></b> : boolean<div class="mdesc"><div class="short">By default, drags can only be initiated if the mousedown occurs in the
region the linked element is.  This is done in...</div><div class="long">By default, drags can only be initiated if the mousedown occurs in the
region the linked element is.  This is done in part to work around a
bug in some browsers that mis-report the mousedown if the previous
mouseup happened outside of the window.  This property is set to true
if outer handles are defined.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#hasOuterHandles" ext:member="#hasOuterHandles" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-id"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-id">id</a></b> : String<div class="mdesc"><div class="short">The id of the element associated with this object.  This is what we
refer to as the "linked element" because the size...</div><div class="long">The id of the element associated with this object.  This is what we
refer to as the "linked element" because the size and position of
this element is used to determine when the drag and drop objects have
interacted.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#id" ext:member="#id" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-ignoreSelf"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-ignoreSelf">ignoreSelf</a></b> : Boolean<div class="mdesc"><div class="short">Set to false to enable a DragDrop object to fire drag events while dragging
over its own Element. Defaults to true - ...</div><div class="long">Set to false to enable a DragDrop object to fire drag events while dragging
over its own Element. Defaults to true - DragDrop objects do not by default
fire drag events to themselves.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#ignoreSelf" ext:member="#ignoreSelf" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-invalidHandleClasses"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-invalidHandleClasses">invalidHandleClasses</a></b> : Array<div class="mdesc">An Array of CSS class names for elements to be considered in valid as drag handles.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#invalidHandleClasses" ext:member="#invalidHandleClasses" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-invalidHandleIds"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-invalidHandleIds">invalidHandleIds</a></b> : Object<div class="mdesc"><div class="short">An object who's property names identify the IDs of elements to be considered invalid as drag handles.
A non-null prop...</div><div class="long">An object who's property names identify the IDs of elements to be considered invalid as drag handles.
A non-null property value identifies the ID as invalid. For example, to prevent
dragging from being initiated on element ID "foo", use:<pre><code>{
    foo: true
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#invalidHandleIds" ext:member="#invalidHandleIds" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-invalidHandleTypes"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-invalidHandleTypes">invalidHandleTypes</a></b> : Object<div class="mdesc"><div class="short">An object who's property names identify HTML tags to be considered invalid as drag handles.
A non-null property value...</div><div class="long">An object who's property names identify HTML tags to be considered invalid as drag handles.
A non-null property value identifies the tag as invalid. Defaults to the 
following value which prevents drag operations from being initiated by &lt;a> elements:<pre><code>{
    A: <em>"A"</em>
}</code></pre></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#invalidHandleTypes" ext:member="#invalidHandleTypes" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-isTarget"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-isTarget">isTarget</a></b> : boolean<div class="mdesc">By default, all instances can be a drop target.  This can be disabled by
setting isTarget to false.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#isTarget" ext:member="#isTarget" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-maintainOffset"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-maintainOffset">maintainOffset</a></b> : boolean<div class="mdesc"><div class="short">Maintain offsets when we resetconstraints.  Set to true when you want
the position of the element relative to its par...</div><div class="long">Maintain offsets when we resetconstraints.  Set to true when you want
the position of the element relative to its parent to stay the same
when the page changes</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#maintainOffset" ext:member="#maintainOffset" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-moveOnly"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-moveOnly">moveOnly</a></b> : boolean<div class="mdesc"><div class="short">When set to true, other DD objects in cooperating DDGroups do not receive
notification events when this DD object is ...</div><div class="long">When set to true, other DD objects in cooperating DDGroups do not receive
notification events when this DD object is dragged over them. Defaults to false.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#moveOnly" ext:member="#moveOnly" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-padding"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-padding">padding</a></b> : int[] An array containing the 4 padding values: [top, right, bottom, left]<div class="mdesc">The padding configured for this drag and drop object for calculating
the drop zone intersection with this object.</div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#padding" ext:member="#padding" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-primaryButtonOnly"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-primaryButtonOnly">primaryButtonOnly</a></b> : boolean<div class="mdesc"><div class="short">By default the drag and drop instance will only respond to the primary
button click (left button for a right-handed m...</div><div class="long">By default the drag and drop instance will only respond to the primary
button click (left button for a right-handed mouse).  Set to true to
allow drag and drop to start with any mouse click that is propogated
by the browser</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#primaryButtonOnly" ext:member="#primaryButtonOnly" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-xTicks"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-xTicks">xTicks</a></b> : int[]<div class="mdesc"><div class="short">Array of pixel locations the element will snap to if we specified a
horizontal graduation/interval.  This array is ge...</div><div class="long">Array of pixel locations the element will snap to if we specified a
horizontal graduation/interval.  This array is generated automatically
when you define a tick interval.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#xTicks" ext:member="#xTicks" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-yTicks"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDrop-yTicks">yTicks</a></b> : int[]<div class="mdesc"><div class="short">Array of pixel locations the element will snap to if we specified a
vertical graduation/interval.  This array is gene...</div><div class="long">Array of pixel locations the element will snap to if we specified a
vertical graduation/interval.  This array is generated automatically
when you define a tick interval.</div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#yTicks" ext:member="#yTicks" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr></tbody></table><a id="Ext.dd.DropZone-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-DropZone"></a><b><a href="source/DropZone.html#cls-Ext.dd.DropZone">DropZone</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The container element</div></li><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addInvalidHandleClass"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addInvalidHandleClass">addInvalidHandleClass</a></b>(&nbsp;<code>string&nbsp;cssClass</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Lets you specify a css class of elements that will not initiate a drag</div><div class="long">Lets you specify a css class of elements that will not initiate a drag<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cssClass</code> : string<div class="sub-desc">the class of the elements you wish to ignore</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addInvalidHandleClass" ext:member="#addInvalidHandleClass" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addInvalidHandleId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addInvalidHandleId">addInvalidHandleId</a></b>(&nbsp;<code>string&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Lets you to specify an element id for a child of a drag handle
that should not initiate a drag</div><div class="long">Lets you to specify an element id for a child of a drag handle
that should not initiate a drag<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : string<div class="sub-desc">the element id of the element you wish to ignore</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addInvalidHandleId" ext:member="#addInvalidHandleId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addInvalidHandleType"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addInvalidHandleType">addInvalidHandleType</a></b>(&nbsp;<code>string&nbsp;tagName</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to specify a tag name that should not start a drag operation
when clicked.  This is designed to facilitate...</div><div class="long">Allows you to specify a tag name that should not start a drag operation
when clicked.  This is designed to facilitate embedding links within a
drag handle that do something other than start the drag.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>tagName</code> : string<div class="sub-desc">the type of element to exclude</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addInvalidHandleType" ext:member="#addInvalidHandleType" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-addToGroup"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-addToGroup">addToGroup</a></b>(&nbsp;<code>sGroup&nbsp;{string}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Add this instance to a group of related drag/drop objects.  All
instances belong to at least one group, and can belon...</div><div class="long">Add this instance to a group of related drag/drop objects.  All
instances belong to at least one group, and can belong to as many
groups as needed.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{string}</code> : sGroup<div class="sub-desc">the name of the group</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#addToGroup" ext:member="#addToGroup" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-applyConfig"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-applyConfig">applyConfig</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Applies the configuration parameters that were passed into the constructor.
This is supposed to happen at each level ...</div><div class="long">Applies the configuration parameters that were passed into the constructor.
This is supposed to happen at each level through the inheritance chain.  So
a DDProxy implentation will execute apply config on DDProxy, DD, and
DragDrop in order to get all of the parameters that are available in
each object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#applyConfig" ext:member="#applyConfig" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-clearConstraints"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-clearConstraints">clearConstraints</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears any constraints applied to this instance.  Also clears ticks
since they can't exist independent of a constrain...</div><div class="long">Clears any constraints applied to this instance.  Also clears ticks
since they can't exist independent of a constraint at this time.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#clearConstraints" ext:member="#clearConstraints" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-clearTicks"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-clearTicks">clearTicks</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears any tick interval defined for this instance</div><div class="long">Clears any tick interval defined for this instance<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#clearTicks" ext:member="#clearTicks" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-constrainTo"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-constrainTo">constrainTo</a></b>(&nbsp;<code>Mixed&nbsp;constrainTo</code>,&nbsp;<span title="Optional" class="optional">[<code>Object/Number&nbsp;pad</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;inContent</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Initializes the drag drop object's constraints to restrict movement to a certain element.
Usage:
 var dd = new Ext.dd...</div><div class="long">Initializes the drag drop object's constraints to restrict movement to a certain element.
Usage:
 <pre><code><b>var</b> dd = <b>new</b> Ext.dd.DDProxy(<em>"dragDiv1"</em>, <em>"proxytest"</em>,
                { dragElId: <em>"existingProxyDiv"</em> });
 dd.startDrag = <b>function</b>(){
     this.constrainTo(<em>"parent-id"</em>);
 };</code></pre>
Or you can initalize it using the <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> object:
 <pre><code>Ext.get(<em>"dragDiv1"</em>).initDDProxy(<em>"proxytest"</em>, {dragElId: <em>"existingProxyDiv"</em>}, {
     startDrag : <b>function</b>(){
         this.constrainTo(<em>"parent-id"</em>);
     }
 });</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>constrainTo</code> : Mixed<div class="sub-desc">The element to constrain to.</div></li><li><code>pad</code> : Object/Number<div class="sub-desc">(optional) Pad provides a way to specify "padding" of the constraints,
and can be either a number for symmetrical padding (4 would be equal to {left:4, right:4, top:4, bottom:4}) or
an object containing the sides to pad. For example: {right:10, bottom:10}</div></li><li><code>inContent</code> : Boolean<div class="sub-desc">(optional) Constrain the draggable in the content box of the element (inside padding and borders)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#constrainTo" ext:member="#constrainTo" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-endDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-endDrag">endDrag</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Fired when we are done dragging the object</div><div class="long">Fired when we are done dragging the object<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#endDrag" ext:member="#endDrag" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-getDragEl"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-getDragEl">getDragEl</a></b>()
    :
                                        HTMLElement<div class="mdesc"><div class="short">Returns a reference to the actual element to drag.  By default this is
the same as the html element, but it can be as...</div><div class="long">Returns a reference to the actual element to drag.  By default this is
the same as the html element, but it can be assigned to another
element. An example of this can be found in Ext.dd.DDProxy<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc">the html element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#getDragEl" ext:member="#getDragEl" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-getEl"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-getEl">getEl</a></b>()
    :
                                        HTMLElement<div class="mdesc"><div class="short">Returns a reference to the linked element</div><div class="long">Returns a reference to the linked element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HTMLElement</code><div class="sub-desc">the html element</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#getEl" ext:member="#getEl" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-getTargetFromEvent"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-getTargetFromEvent">getTargetFromEvent</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns a custom data object associated with the DOM node that is the target of the event.  By default&#13;
this looks up...</div><div class="long">Returns a custom data object associated with the DOM node that is the target of the event.  By default
this looks up the event target in the <a href="output/Ext.dd.Registry.html" ext:cls="Ext.dd.Registry">Ext.dd.Registry</a>, although you can override this method to
provide your own custom lookup.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">The event</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data The custom data</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-init"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-init">init</a></b>(&nbsp;<code>id&nbsp;the</code>,&nbsp;<code>String&nbsp;sGroup</code>,&nbsp;<code>object&nbsp;config</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets up the DragDrop object.  Must be called in the constructor of any
Ext.dd.DragDrop subclass</div><div class="long">Sets up the DragDrop object.  Must be called in the constructor of any
Ext.dd.DragDrop subclass<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : id<div class="sub-desc">id of the linked element</div></li><li><code>sGroup</code> : String<div class="sub-desc">the group of related items</div></li><li><code>config</code> : object<div class="sub-desc">configuration attributes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#init" ext:member="#init" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-initTarget"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-initTarget">initTarget</a></b>(&nbsp;<code>id&nbsp;the</code>,&nbsp;<code>String&nbsp;sGroup</code>,&nbsp;<code>object&nbsp;config</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Initializes Targeting functionality only... the object does not
get a mousedown handler.</div><div class="long">Initializes Targeting functionality only... the object does not
get a mousedown handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : id<div class="sub-desc">id of the linked element</div></li><li><code>sGroup</code> : String<div class="sub-desc">the group of related items</div></li><li><code>config</code> : object<div class="sub-desc">configuration attributes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#initTarget" ext:member="#initTarget" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-isLocked"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-isLocked">isLocked</a></b>()
    :
                                        boolean<div class="mdesc"><div class="short">Returns true if this instance is locked, or the drag drop mgr is locked
(meaning that all drag/drop is disabled on th...</div><div class="long">Returns true if this instance is locked, or the drag drop mgr is locked
(meaning that all drag/drop is disabled on the page.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if this obj or all drag/drop is locked, else
false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#isLocked" ext:member="#isLocked" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-isValidHandleChild"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-isValidHandleChild">isValidHandleChild</a></b>(&nbsp;<code>HTMLElement&nbsp;node</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">Checks the tag exclusion list to see if this click should be ignored</div><div class="long">Checks the tag exclusion list to see if this click should be ignored<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : HTMLElement<div class="sub-desc">the HTMLElement to evaluate</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if this is a valid tag type, false if not</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#isValidHandleChild" ext:member="#isValidHandleChild" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-lock"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-lock">lock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Lock this instance</div><div class="long">Lock this instance<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#lock" ext:member="#lock" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-notifyDrop"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-notifyDrop">notifyDrop</a></b>(&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">The function a Ext.dd.DragSource calls once to notify this drop zone that the dragged item has&#13;
been dropped on it.  ...</div><div class="long">The function a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> calls once to notify this drop zone that the dragged item has
been dropped on it.  The drag zone will look up the target node based on the event passed in, and if there
is a node registered for that event, it will delegate to <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeDrop" ext:member="onNodeDrop" ext:cls="Ext.dd.DropZone">onNodeDrop</a> for node-specific handling,
otherwise it will call <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onContainerDrop" ext:member="onContainerDrop" ext:cls="Ext.dd.DropZone">onContainerDrop</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the drop was valid, else false</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-notifyEnter"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-notifyEnter">notifyEnter</a></b>(&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">The function a Ext.dd.DragSource calls once to notify this drop zone that the source is now over&#13;
the zone.  The defa...</div><div class="long">The function a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> calls once to notify this drop zone that the source is now over
the zone.  The default implementation returns this.dropNotAllowed and expects that only registered drop
nodes can process drag drop operations, so if you need the drop zone itself to be able to process drops
you should override this method and provide a custom implementation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">status The CSS class that communicates the drop status back to the source so that the&#13;
underlying {@link Ext.dd.StatusProxy} can be updated</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-notifyOut"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-notifyOut">notifyOut</a></b>(&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">The function a Ext.dd.DragSource calls once to notify this drop zone that the source has been dragged&#13;
out of the zon...</div><div class="long">The function a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> calls once to notify this drop zone that the source has been dragged
out of the zone without dropping.  If the drag source is currently over a registered node, the notification
will be delegated to <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOut" ext:member="onNodeOut" ext:cls="Ext.dd.DropZone">onNodeOut</a> for node-specific handling, otherwise it will be ignored.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop target</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag zone</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-notifyOver"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-notifyOver">notifyOver</a></b>(&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">The function a Ext.dd.DragSource calls continuously while it is being dragged over the drop zone.&#13;
This method will b...</div><div class="long">The function a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> calls continuously while it is being dragged over the drop zone.
This method will be called on every mouse movement while the drag source is over the drop zone.
It will call <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOver" ext:member="onNodeOver" ext:cls="Ext.dd.DropZone">onNodeOver</a> while the drag source is over a registered node, and will also automatically
delegate to the appropriate node-specific methods as necessary when the drag source enters and exits
registered nodes (<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeEnter" ext:member="onNodeEnter" ext:cls="Ext.dd.DropZone">onNodeEnter</a>, <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onNodeOut" ext:member="onNodeOut" ext:cls="Ext.dd.DropZone">onNodeOut</a>). If the drag source is not currently over a
registered node, it will call <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-onContainerOver" ext:member="onContainerOver" ext:cls="Ext.dd.DropZone">onContainerOver</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">status The CSS class that communicates the drop status back to the source so that the&#13;
underlying {@link Ext.dd.StatusProxy} can be updated</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onAvailable"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onAvailable">onAvailable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Override the onAvailable method to do what is needed after the initial
position was determined.</div><div class="long">Override the onAvailable method to do what is needed after the initial
position was determined.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onAvailable" ext:member="#onAvailable" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-onContainerDrop"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-onContainerDrop">onContainerDrop</a></b>(&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Called when the DropZone determines that a Ext.dd.DragSource has been dropped on it,&#13;
but not on any of its registere...</div><div class="long">Called when the DropZone determines that a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> has been dropped on it,
but not on any of its registered drop nodes.  The default implementation returns false, so it should be
overridden to provide the appropriate processing of the drop event if you need the drop zone itself to
be able to accept drops.  It should return true when valid so that the drag source's repair action does not run.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the drop was valid, else false</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-onContainerOver"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-onContainerOver">onContainerOver</a></b>(&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Called while the DropZone determines that a Ext.dd.DragSource is being dragged over it,&#13;
but not over any of its regi...</div><div class="long">Called while the DropZone determines that a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> is being dragged over it,
but not over any of its registered drop nodes.  The default implementation returns this.dropNotAllowed, so
it should be overridden to provide the proper feedback if necessary.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">status The CSS class that communicates the drop status back to the source so that the&#13;
underlying {@link Ext.dd.StatusProxy} can be updated</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDrag">onDrag</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called during the onMouseMove event while dragging an
object.</div><div class="long">Abstract method called during the onMouseMove event while dragging an
object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDrag" ext:member="#onDrag" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragDrop"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragDrop">onDragDrop</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this item is dropped on another DragDrop
obj</div><div class="long">Abstract method called when this item is dropped on another DragDrop
obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this was dropped on.  In INTERSECT mode, an array of dd items this
was dropped on.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragDrop" ext:member="#onDragDrop" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragEnter"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragEnter">onDragEnter</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this element fist begins hovering over
another DragDrop obj</div><div class="long">Abstract method called when this element fist begins hovering over
another DragDrop obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this is hovering over.  In INTERSECT mode, an array of one or more
dragdrop items being hovered over.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragEnter" ext:member="#onDragEnter" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragOut"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragOut">onDragOut</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when we are no longer hovering over an element</div><div class="long">Abstract method called when we are no longer hovering over an element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this was hovering over.  In INTERSECT mode, an array of dd items
that the mouse is no longer over.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragOut" ext:member="#onDragOut" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onDragOver"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onDragOver">onDragOver</a></b>(&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>String|DragDrop[]&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this element is hovering over another
DragDrop obj</div><div class="long">Abstract method called when this element is hovering over another
DragDrop obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousemove event</div></li><li><code>id</code> : String|DragDrop[]<div class="sub-desc">In POINT mode, the element
id this is hovering over.  In INTERSECT mode, an array of dd items
being hovered over.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onDragOver" ext:member="#onDragOver" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onInvalidDrop"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onInvalidDrop">onInvalidDrop</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called when this item is dropped on an area with no
drop target</div><div class="long">Abstract method called when this item is dropped on an area with no
drop target<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onInvalidDrop" ext:member="#onInvalidDrop" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onMouseDown"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onMouseDown">onMouseDown</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Event handler that fires when a drag/drop obj gets a mousedown</div><div class="long">Event handler that fires when a drag/drop obj gets a mousedown<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mousedown event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onMouseDown" ext:member="#onMouseDown" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-onMouseUp"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-onMouseUp">onMouseUp</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Event handler that fires when a drag/drop obj gets a mouseup</div><div class="long">Event handler that fires when a drag/drop obj gets a mouseup<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the mouseup event</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#onMouseUp" ext:member="#onMouseUp" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-onNodeDrop"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-onNodeDrop">onNodeDrop</a></b>(&nbsp;<code>Object&nbsp;nodeData</code>,&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Called when the DropZone determines that a Ext.dd.DragSource has been dropped onto&#13;
the drop node.  The default imple...</div><div class="long">Called when the DropZone determines that a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> has been dropped onto
the drop node.  The default implementation returns false, so it should be overridden to provide the
appropriate processing of the drop event and return true so that the drag source's repair action does not run.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>nodeData</code> : Object<div class="sub-desc">The custom data associated with the drop node (this is the same value returned from
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a> for this node)</div></li><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the drop was valid, else false</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-onNodeEnter"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-onNodeEnter">onNodeEnter</a></b>(&nbsp;<code>Object&nbsp;nodeData</code>,&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Called when the DropZone determines that a Ext.dd.DragSource has entered a drop node&#13;
that has either been registered...</div><div class="long">Called when the DropZone determines that a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> has entered a drop node
that has either been registered or detected by a configured implementation of <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a>.
This method has no default implementation and should be overridden to provide
node-specific processing if necessary.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>nodeData</code> : Object<div class="sub-desc">The custom data associated with the drop node (this is the same value returned from 
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a> for this node)</div></li><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-onNodeOut"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-onNodeOut">onNodeOut</a></b>(&nbsp;<code>Object&nbsp;nodeData</code>,&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Called when the DropZone determines that a Ext.dd.DragSource has been dragged out of&#13;
the drop node without dropping....</div><div class="long">Called when the DropZone determines that a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> has been dragged out of
the drop node without dropping.  This method has no default implementation and should be overridden to provide
node-specific processing if necessary.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>nodeData</code> : Object<div class="sub-desc">The custom data associated with the drop node (this is the same value returned from
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a> for this node)</div></li><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DropZone-onNodeOver"></a><b><a href="source/DropZone.html#method-Ext.dd.DropZone-onNodeOver">onNodeOver</a></b>(&nbsp;<code>Object&nbsp;nodeData</code>,&nbsp;<code>Ext.dd.DragSource&nbsp;source</code>,&nbsp;<code>Event&nbsp;e</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Called while the DropZone determines that a Ext.dd.DragSource is over a drop node&#13;
that has either been registered or...</div><div class="long">Called while the DropZone determines that a <a href="output/Ext.dd.DragSource.html" ext:cls="Ext.dd.DragSource">Ext.dd.DragSource</a> is over a drop node
that has either been registered or detected by a configured implementation of <a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a>.
The default implementation returns this.dropNotAllowed, so it should be
overridden to provide the proper feedback.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>nodeData</code> : Object<div class="sub-desc">The custom data associated with the drop node (this is the same value returned from
<a href="output/Ext.dd.DropZone.html#Ext.dd.DropZone-getTargetFromEvent" ext:member="getTargetFromEvent" ext:cls="Ext.dd.DropZone">getTargetFromEvent</a> for this node)</div></li><li><code>source</code> : Ext.dd.DragSource<div class="sub-desc">The drag source that was dragged over this drop zone</div></li><li><code>e</code> : Event<div class="sub-desc">The event</div></li><li><code>data</code> : Object<div class="sub-desc">An object containing arbitrary data supplied by the drag source</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">status The CSS class that communicates the drop status back to the source so that the&#13;
underlying {@link Ext.dd.StatusProxy} can be updated</div></li></ul></div></div></div></td><td class="msource">DropZone</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeFromGroup"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeFromGroup">removeFromGroup</a></b>(&nbsp;<code>string&nbsp;sGroup</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Remove's this instance from the supplied interaction group</div><div class="long">Remove's this instance from the supplied interaction group<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>sGroup</code> : string<div class="sub-desc">The group to drop</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeFromGroup" ext:member="#removeFromGroup" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeInvalidHandleClass"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeInvalidHandleClass">removeInvalidHandleClass</a></b>(&nbsp;<code>string&nbsp;cssClass</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unsets an invalid css class</div><div class="long">Unsets an invalid css class<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cssClass</code> : string<div class="sub-desc">the class of the element(s) you wish to
re-enable</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeInvalidHandleClass" ext:member="#removeInvalidHandleClass" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeInvalidHandleId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeInvalidHandleId">removeInvalidHandleId</a></b>(&nbsp;<code>string&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unsets an invalid handle id</div><div class="long">Unsets an invalid handle id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : string<div class="sub-desc">the id of the element to re-enable</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeInvalidHandleId" ext:member="#removeInvalidHandleId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-removeInvalidHandleType"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-removeInvalidHandleType">removeInvalidHandleType</a></b>(&nbsp;<code>string&nbsp;tagName</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unsets an excluded tag name set by addInvalidHandleType</div><div class="long">Unsets an excluded tag name set by addInvalidHandleType<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>tagName</code> : string<div class="sub-desc">the type of element to unexclude</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#removeInvalidHandleType" ext:member="#removeInvalidHandleType" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-resetConstraints"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-resetConstraints">resetConstraints</a></b>(&nbsp;<code>boolean&nbsp;maintainOffset</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">resetConstraints must be called if you manually reposition a dd element.</div><div class="long">resetConstraints must be called if you manually reposition a dd element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>maintainOffset</code> : boolean<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#resetConstraints" ext:member="#resetConstraints" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setDragElId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setDragElId">setDragElId</a></b>(&nbsp;<code>id&nbsp;{string}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to specify that an element other than the linked element
will be moved with the cursor during a drag</div><div class="long">Allows you to specify that an element other than the linked element
will be moved with the cursor during a drag<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{string}</code> : id<div class="sub-desc">the id of the element that will be used to initiate the drag</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setDragElId" ext:member="#setDragElId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setHandleElId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setHandleElId">setHandleElId</a></b>(&nbsp;<code>id&nbsp;{string}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to specify a child of the linked element that should be
used to initiate the drag operation.  An example o...</div><div class="long">Allows you to specify a child of the linked element that should be
used to initiate the drag operation.  An example of this would be if
you have a content div with text and links.  Clicking anywhere in the
content area would normally start the drag operation.  Use this method
to specify that an element inside of the content div is the element
that starts the drag operation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{string}</code> : id<div class="sub-desc">the id of the element that will be used to
initiate the drag.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setHandleElId" ext:member="#setHandleElId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setInitPosition"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setInitPosition">setInitPosition</a></b>(&nbsp;<code>int&nbsp;diffX</code>,&nbsp;<code>int&nbsp;diffY</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Stores the initial placement of the linked element.</div><div class="long">Stores the initial placement of the linked element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>diffX</code> : int<div class="sub-desc">the X offset, default 0</div></li><li><code>diffY</code> : int<div class="sub-desc">the Y offset, default 0</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setInitPosition" ext:member="#setInitPosition" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setOuterHandleElId"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setOuterHandleElId">setOuterHandleElId</a></b>(&nbsp;<code>id&nbsp;the</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Allows you to set an element outside of the linked element as a drag
handle</div><div class="long">Allows you to set an element outside of the linked element as a drag
handle<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : id<div class="sub-desc">id of the element that will be used to initiate the drag</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setOuterHandleElId" ext:member="#setOuterHandleElId" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setPadding"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setPadding">setPadding</a></b>(&nbsp;<code>int&nbsp;iTop</code>,&nbsp;<code>int&nbsp;iRight</code>,&nbsp;<code>int&nbsp;iBot</code>,&nbsp;<code>int&nbsp;iLeft</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Configures the padding for the target zone in px.  Effectively expands
(or reduces) the virtual object size for targe...</div><div class="long">Configures the padding for the target zone in px.  Effectively expands
(or reduces) the virtual object size for targeting calculations.
Supports css-style shorthand; if only one parameter is passed, all sides
will have that padding, and if only two are passed, the top and bottom
will have the first param, the left and right the second.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iTop</code> : int<div class="sub-desc">Top pad</div></li><li><code>iRight</code> : int<div class="sub-desc">Right pad</div></li><li><code>iBot</code> : int<div class="sub-desc">Bot pad</div></li><li><code>iLeft</code> : int<div class="sub-desc">Left pad</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setPadding" ext:member="#setPadding" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setXConstraint"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setXConstraint">setXConstraint</a></b>(&nbsp;<code>int&nbsp;iLeft</code>,&nbsp;<code>int&nbsp;iRight</code>,&nbsp;<code>int&nbsp;iTickSize</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">By default, the element can be dragged any place on the screen.  Use
this method to limit the horizontal travel of th...</div><div class="long">By default, the element can be dragged any place on the screen.  Use
this method to limit the horizontal travel of the element.  Pass in
0,0 for the parameters if you want to lock the drag to the y axis.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iLeft</code> : int<div class="sub-desc">the number of pixels the element can move to the left</div></li><li><code>iRight</code> : int<div class="sub-desc">the number of pixels the element can move to the
right</div></li><li><code>iTickSize</code> : int<div class="sub-desc">optional parameter for specifying that the
element
should move iTickSize pixels at a time.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setXConstraint" ext:member="#setXConstraint" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-setYConstraint"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-setYConstraint">setYConstraint</a></b>(&nbsp;<code>int&nbsp;iUp</code>,&nbsp;<code>int&nbsp;iDown</code>,&nbsp;<code>int&nbsp;iTickSize</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">By default, the element can be dragged any place on the screen.  Set
this to limit the vertical travel of the element...</div><div class="long">By default, the element can be dragged any place on the screen.  Set
this to limit the vertical travel of the element.  Pass in 0,0 for the
parameters if you want to lock the drag to the x axis.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>iUp</code> : int<div class="sub-desc">the number of pixels the element can move up</div></li><li><code>iDown</code> : int<div class="sub-desc">the number of pixels the element can move down</div></li><li><code>iTickSize</code> : int<div class="sub-desc">optional parameter for specifying that the
element should move iTickSize pixels at a time.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#setYConstraint" ext:member="#setYConstraint" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-startDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-startDrag">startDrag</a></b>(&nbsp;<code>int&nbsp;X</code>,&nbsp;<code>int&nbsp;Y</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Abstract method called after a drag/drop object is clicked
and the drag or mousedown time thresholds have beeen met.</div><div class="long">Abstract method called after a drag/drop object is clicked
and the drag or mousedown time thresholds have beeen met.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>X</code> : int<div class="sub-desc">click location</div></li><li><code>Y</code> : int<div class="sub-desc">click location</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#startDrag" ext:member="#startDrag" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-toString"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-toString">toString</a></b>()
    :
                                        string<div class="mdesc"><div class="short">toString method</div><div class="long">toString method<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>string</code><div class="sub-desc">string representation of the dd obj</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#toString" ext:member="#toString" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-unlock"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-unlock">unlock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Unlock this instace</div><div class="long">Unlock this instace<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#unlock" ext:member="#unlock" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDrop-unreg"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDrop-unreg">unreg</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Remove all drag and drop hooks for this element</div><div class="long">Remove all drag and drop hooks for this element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.dd.DragDrop.html#unreg" ext:member="#unreg" ext:cls="Ext.dd.DragDrop">DragDrop</a></td></tr></tbody></table><a id="Ext.dd.DropZone-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>