<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.util.TextMetrics-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.util.TextMetrics-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.util.TextMetrics-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.util.TextMetrics"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/TextMetrics.html#cls-Ext.util.TextMetrics">Ext.util.TextMetrics</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.util</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TextMetrics.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TextMetrics.html#cls-Ext.util.TextMetrics">TextMetrics</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Provides precise pixel measurements for blocks of text so that you can determine exactly how high and
wide, in pixels, a given block of text will be.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.util.TextMetrics-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.util.TextMetrics-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-bind"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-bind">bind</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Binds this TextMetrics instance to an element from which to copy existing CSS styles
that can affect the size of the ...</div><div class="long">Binds this TextMetrics instance to an element from which to copy existing CSS styles
that can affect the size of the rendered text<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The element, dom node or id</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-createInstance"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-createInstance">createInstance</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;fixedWidth</code>]</span>&nbsp;)
    :
                                        Ext.util.TextMetrics.Instance<div class="mdesc"><div class="short">Return a unique TextMetrics instance that can be bound directly to an element and reused.  This reduces
the overhead ...</div><div class="long">Return a unique TextMetrics instance that can be bound directly to an element and reused.  This reduces
the overhead of multiple calls to initialize the style properties on each measurement.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The element, dom node or id that the instance will be bound to</div></li><li><code>fixedWidth</code> : Number<div class="sub-desc">(optional) If the text will be multiline, you have to set a fixed width
in order to accurately measure the text height</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.util.TextMetrics.Instance</code><div class="sub-desc">instance The new instance</div></li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-getHeight"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-getHeight">getHeight</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the measured height of the specified text.  For multiline text, be sure to call
setFixedWidth if necessary.</div><div class="long">Returns the measured height of the specified text.  For multiline text, be sure to call
<a href="output/Ext.util.TextMetrics.html#Ext.util.TextMetrics-setFixedWidth" ext:member="setFixedWidth" ext:cls="Ext.util.TextMetrics">setFixedWidth</a> if necessary.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">The text to measure</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">height The height in pixels</div></li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-getSize"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-getSize">getSize</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the size of the specified text based on the internal element's style and width properties</div><div class="long">Returns the size of the specified text based on the internal element's style and width properties<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">The text to measure</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the text's size {width: (width), height: (height)}</div></li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-getWidth"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-getWidth">getWidth</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the measured width of the specified text</div><div class="long">Returns the measured width of the specified text<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">The text to measure</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">width The width in pixels</div></li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-measure"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-measure">measure</a></b>(&nbsp;<code>String/HTMLElement&nbsp;el</code>,&nbsp;<code>String&nbsp;text</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;fixedWidth</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Measures the size of the specified text</div><div class="long">Measures the size of the specified text<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement<div class="sub-desc">The element, dom node or id from which to copy existing CSS styles
that can affect the size of the rendered text</div></li><li><code>text</code> : String<div class="sub-desc">The text to measure</div></li><li><code>fixedWidth</code> : Number<div class="sub-desc">(optional) If the text will be multiline, you have to set a fixed width
in order to accurately measure the text height</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the text's size {width: (width), height: (height)}</div></li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TextMetrics-setFixedWidth"></a><b><a href="source/TextMetrics.html#method-Ext.util.TextMetrics-setFixedWidth">setFixedWidth</a></b>(&nbsp;<code>Number&nbsp;width</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets a fixed width on the internal measurement element.  If the text will be multiline, you have
to set a fixed width...</div><div class="long">Sets a fixed width on the internal measurement element.  If the text will be multiline, you have
to set a fixed width in order to accurately measure the text height.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>width</code> : Number<div class="sub-desc">The width to set on the element</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TextMetrics</td></tr></tbody></table><a id="Ext.util.TextMetrics-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>