<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Updater.defaults-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Updater.defaults-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Updater.defaults-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Updater.defaults"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/UpdateManager.html#cls-Ext.Updater.defaults">Ext.Updater.defaults</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">UpdateManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/UpdateManager.html#cls-Ext.Updater.defaults">Updater.defaults</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">The defaults collection enables customizing the default properties of Updater</div><div class="hr"></div><a id="Ext.Updater.defaults-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.defaults-disableCaching"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater.defaults-disableCaching">disableCaching</a></b> : Boolean<div class="mdesc">True to append a unique parameter to GET requests to disable caching (defaults to false).</div></td><td class="msource">Updater.defaults</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.defaults-indicatorText"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater.defaults-indicatorText">indicatorText</a></b> : String<div class="mdesc">Text for loading indicator (defaults to '&lt;div class="loading-indicator"&gt;Loading...&lt;/div&gt;').</div></td><td class="msource">Updater.defaults</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.defaults-loadScripts"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater.defaults-loadScripts">loadScripts</a></b> : Boolean<div class="mdesc">True to process scripts by default (defaults to false).</div></td><td class="msource">Updater.defaults</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.defaults-showLoadIndicator"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater.defaults-showLoadIndicator">showLoadIndicator</a></b> : Boolean<div class="mdesc">Whether or not to show <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-indicatorText" ext:member="indicatorText" ext:cls="Ext.Updater.defaults">indicatorText</a> during loading (defaults to true).</div></td><td class="msource">Updater.defaults</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.defaults-sslBlankUrl"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater.defaults-sslBlankUrl">sslBlankUrl</a></b> : String<div class="mdesc">Blank page URL to use with SSL file uploads (defaults to <a href="output/Ext.html#Ext-SSL_SECURE_URL" ext:member="SSL_SECURE_URL" ext:cls="Ext">Ext.SSL_SECURE_URL</a> if set, or "javascript:false").</div></td><td class="msource">Updater.defaults</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater.defaults-timeout"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater.defaults-timeout">timeout</a></b> : Number<div class="mdesc">Timeout for requests or form posts in seconds (defaults to 30 seconds).</div></td><td class="msource">Updater.defaults</td></tr></tbody></table><a id="Ext.Updater.defaults-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.Updater.defaults-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>