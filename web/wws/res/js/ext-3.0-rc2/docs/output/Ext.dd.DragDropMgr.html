<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.DragDropMgr-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.DragDropMgr-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.DragDropMgr-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.DragDropMgr"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/DDCore.html#cls-Ext.dd.DragDropMgr">Ext.dd.DragDropMgr</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DDCore.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DDCore.html#cls-Ext.dd.DragDropMgr">DragDropMgr</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">DragDropMgr is a singleton that tracks the element interaction for
all DragDrop items in the window.  Generally, you will not call
this class directly, but it does have helper methods that could
be useful in your DragDrop implementations.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.dd.DragDropMgr-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-INTERSECT"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-INTERSECT">INTERSECT</a></b> : int<div class="mdesc">In intersect mode, drag and drop interaction is defined by the
overlap of two or more drag and drop objects.</div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-POINT"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-POINT">POINT</a></b> : int<div class="mdesc">In point mode, drag and drop interaction is defined by the
location of the cursor during the drag/drop</div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-clickPixelThresh"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-clickPixelThresh">clickPixelThresh</a></b> : int<div class="mdesc">The number of pixels that the mouse needs to move after the
mousedown before the drag is initiated.  Default=3;</div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-clickTimeThresh"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-clickTimeThresh">clickTimeThresh</a></b> : int<div class="mdesc"><div class="short">The number of milliseconds after the mousedown event to initiate the
drag if we don't get a mouseup event. Default=10...</div><div class="long">The number of milliseconds after the mousedown event to initiate the
drag if we don't get a mouseup event. Default=1000</div></div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-mode"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-mode">mode</a></b> : int<div class="mdesc">The current drag and drop mode.  Default: POINT</div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-preventDefault"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-preventDefault">preventDefault</a></b> : boolean<div class="mdesc"><div class="short">Flag to determine if we should prevent the default behavior of the
events we define. By default this is true, but thi...</div><div class="long">Flag to determine if we should prevent the default behavior of the
events we define. By default this is true, but this can be set to
false if you need the default behavior (not recommended)</div></div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-stopPropagation"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-stopPropagation">stopPropagation</a></b> : boolean<div class="mdesc"><div class="short">Flag to determine if we should stop the propagation of the events
we generate. This is true by default but you may wa...</div><div class="long">Flag to determine if we should stop the propagation of the events
we generate. This is true by default but you may want to set it to
false if the html element contains other features that require the
mouse click.</div></div></td><td class="msource">DragDropMgr</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-useCache"></a><b><a href="source/DDCore.html#prop-Ext.dd.DragDropMgr-useCache">useCache</a></b> : boolean<div class="mdesc"><div class="short">Set useCache to false if you want to force object the lookup of each
drag and drop linked element constantly during a...</div><div class="long">Set useCache to false if you want to force object the lookup of each
drag and drop linked element constantly during a drag.</div></div></td><td class="msource">DragDropMgr</td></tr></tbody></table><a id="Ext.dd.DragDropMgr-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-getBestMatch"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-getBestMatch">getBestMatch</a></b>(&nbsp;<code>DragDrop[]&nbsp;dds</code>&nbsp;)
    :
                                        DragDrop<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Helper function for getting the best match from the list of drag
and drop objects returned by the drag and drop event...</div><div class="long">&lt;static&gt;&nbsp;Helper function for getting the best match from the list of drag
and drop objects returned by the drag and drop events when we are
in INTERSECT mode.  It returns either the first object that the
cursor is over, or the object that has the greatest overlap with
the dragged element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>dds</code> : DragDrop[]<div class="sub-desc">The array of drag and drop objects
targeted</div></li></ul><strong>Returns:</strong><ul><li><code>DragDrop</code><div class="sub-desc">The best single match</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-getCss"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-getCss">getCss</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Returns the style property for the DOM element (i.e.,
document.getElById(id).style)</div><div class="long">&lt;static&gt;&nbsp;Returns the style property for the DOM element (i.e.,
document.getElById(id).style)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">the id of the elment to get</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The style property of the element</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-getDDById"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-getDDById">getDDById</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        DragDrop<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Returns the DragDrop instance for a given id</div><div class="long">&lt;static&gt;&nbsp;Returns the DragDrop instance for a given id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">the id of the DragDrop object</div></li></ul><strong>Returns:</strong><ul><li><code>DragDrop</code><div class="sub-desc">the drag drop object, null if it is not found</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-getElement"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-getElement">getElement</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Returns the actual DOM element</div><div class="long">&lt;static&gt;&nbsp;Returns the actual DOM element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">the id of the elment to get</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The element</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-getLocation"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-getLocation">getLocation</a></b>(&nbsp;<code>DragDrop&nbsp;oDD</code>&nbsp;)
    :
                                        Ext.lib.Region<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Returns a Region object containing the drag and drop element's position
and size, including the padding configured fo...</div><div class="long">&lt;static&gt;&nbsp;Returns a Region object containing the drag and drop element's position
and size, including the padding configured for it<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>oDD</code> : DragDrop<div class="sub-desc">the drag and drop object to get the
location for</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.lib.Region</code><div class="sub-desc">a Region object representing the total area
the element occupies, including any padding
the instance is configured for.</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-getRelated"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-getRelated">getRelated</a></b>(&nbsp;<code>DragDrop&nbsp;p_oDD</code>,&nbsp;<code>boolean&nbsp;bTargetsOnly</code>&nbsp;)
    :
                                        DragDrop[]<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Returns the drag and drop instances that are in all groups the
passed in instance belongs to.</div><div class="long">&lt;static&gt;&nbsp;Returns the drag and drop instances that are in all groups the
passed in instance belongs to.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>p_oDD</code> : DragDrop<div class="sub-desc">the obj to get related data for</div></li><li><code>bTargetsOnly</code> : boolean<div class="sub-desc">if true, only return targetable objs</div></li></ul><strong>Returns:</strong><ul><li><code>DragDrop[]</code><div class="sub-desc">the related instances</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-isDragDrop"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-isDragDrop">isDragDrop</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Utility function to determine if a given element has been
registered as a drag drop item.</div><div class="long">&lt;static&gt;&nbsp;Utility function to determine if a given element has been
registered as a drag drop item.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">the element id to check</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if this element is a DragDrop item,
false otherwise</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-isHandle"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-isHandle">isHandle</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Utility function to determine if a given element has been
registered as a drag drop handle for the given Drag Drop ob...</div><div class="long">&lt;static&gt;&nbsp;Utility function to determine if a given element has been
registered as a drag drop handle for the given Drag Drop object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">the element id to check</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if this element is a DragDrop handle, false
otherwise</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-isLegalTarget"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-isLegalTarget">isLegalTarget</a></b>(&nbsp;<code>DragDrop&nbsp;the</code>,&nbsp;<code>DragDrop&nbsp;the</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Returns true if the specified dd target is a legal target for
the specifice drag obj</div><div class="long">&lt;static&gt;&nbsp;Returns true if the specified dd target is a legal target for
the specifice drag obj<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : DragDrop<div class="sub-desc">drag obj</div></li><li><code>the</code> : DragDrop<div class="sub-desc">target</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if the target is a legal target for the
dd obj</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-isLocked"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-isLocked">isLocked</a></b>()
    :
                                        boolean<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Is drag and drop locked?</div><div class="long">&lt;static&gt;&nbsp;Is drag and drop locked?<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">True if drag and drop is locked, false otherwise.</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-isTypeOfDD"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-isTypeOfDD">isTypeOfDD</a></b>(&nbsp;<code>Object&nbsp;the</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;My goal is to be able to transparently determine if an object is
typeof DragDrop, and the exact subclass of DragDrop....</div><div class="long">&lt;static&gt;&nbsp;My goal is to be able to transparently determine if an object is
typeof DragDrop, and the exact subclass of DragDrop.  typeof
returns "object", oDD.constructor.toString() always returns
"DragDrop" and not the name of the subclass.  So for now it just
evaluates a well-known variable in DragDrop.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>the</code> : Object<div class="sub-desc">object to evaluate</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if typeof oDD = DragDrop</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-lock"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-lock">lock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Lock all drag and drop functionality</div><div class="long">&lt;static&gt;&nbsp;Lock all drag and drop functionality<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-refreshCache"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-refreshCache">refreshCache</a></b>(&nbsp;<code>Object&nbsp;groups</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Refreshes the cache of the top-left and bottom-right points of the
drag and drop objects in the specified group(s).  ...</div><div class="long">&lt;static&gt;&nbsp;Refreshes the cache of the top-left and bottom-right points of the
drag and drop objects in the specified group(s).  This is in the
format that is stored in the drag and drop instance, so typical
usage is:
<code>Ext.dd.DragDropMgr.refreshCache(ddinstance.groups);</code>
Alternatively:
<code>Ext.dd.DragDropMgr.refreshCache({group1:true, group2:true});
</code><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>groups</code> : Object<div class="sub-desc">an associative array of groups to refresh</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-regDragDrop"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-regDragDrop">regDragDrop</a></b>(&nbsp;<code>DragDrop&nbsp;oDD</code>,&nbsp;<code>String&nbsp;sGroup</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Each DragDrop instance must be registered with the DragDropMgr.
This is executed in DragDrop.init()</div><div class="long">&lt;static&gt;&nbsp;Each DragDrop instance must be registered with the DragDropMgr.
This is executed in DragDrop.init()<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>oDD</code> : DragDrop<div class="sub-desc">the DragDrop object to register</div></li><li><code>sGroup</code> : String<div class="sub-desc">the name of the group this element belongs to</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-regHandle"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-regHandle">regHandle</a></b>(&nbsp;<code>String&nbsp;sDDId</code>,&nbsp;<code>String&nbsp;sHandleId</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Each DragDrop handle element must be registered.  This is done
automatically when executing DragDrop.setHandleElId()</div><div class="long">&lt;static&gt;&nbsp;Each DragDrop handle element must be registered.  This is done
automatically when executing DragDrop.setHandleElId()<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>sDDId</code> : String<div class="sub-desc">the DragDrop id this element is a handle for</div></li><li><code>sHandleId</code> : String<div class="sub-desc">the id of the element that is the drag
handle</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-startDrag"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-startDrag">startDrag</a></b>(&nbsp;<code>x&nbsp;{int}</code>,&nbsp;<code>y&nbsp;{int}</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Fired when either the drag pixel threshol or the mousedown hold
time threshold has been met.</div><div class="long">&lt;static&gt;&nbsp;Fired when either the drag pixel threshol or the mousedown hold
time threshold has been met.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>{int}</code> : x<div class="sub-desc">the X position of the original mousedown</div></li><li><code>{int}</code> : y<div class="sub-desc">the Y position of the original mousedown</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-stopEvent"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-stopEvent">stopEvent</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Utility to stop event propagation and event default, if these
features are turned on.</div><div class="long">&lt;static&gt;&nbsp;Utility to stop event propagation and event default, if these
features are turned on.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">the event as returned by this.getEvent()</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-unlock"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-unlock">unlock</a></b>()
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Unlock all drag and drop functionality</div><div class="long">&lt;static&gt;&nbsp;Unlock all drag and drop functionality<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.DragDropMgr-verifyEl"></a><b><a href="source/DDCore.html#method-Ext.dd.DragDropMgr-verifyEl">verifyEl</a></b>(&nbsp;<code>HTMLElement&nbsp;el</code>&nbsp;)
    :
                                        boolean<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;This checks to make sure an element exists and is in the DOM.  The
main purpose is to handle cases where innerHTML is...</div><div class="long">&lt;static&gt;&nbsp;This checks to make sure an element exists and is in the DOM.  The
main purpose is to handle cases where innerHTML is used to remove
drag and drop objects from the DOM.  IE provides an 'unspecified
error' when trying to access the offsetParent of such an element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : HTMLElement<div class="sub-desc">the element to check</div></li></ul><strong>Returns:</strong><ul><li><code>boolean</code><div class="sub-desc">true if the element looks usable</div></li></ul></div></div></div></td><td class="msource">DragDropMgr</td></tr></tbody></table><a id="Ext.dd.DragDropMgr-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>