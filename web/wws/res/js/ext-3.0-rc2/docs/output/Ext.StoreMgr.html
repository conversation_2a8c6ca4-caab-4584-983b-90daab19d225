<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.StoreMgr-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.StoreMgr-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.StoreMgr-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.StoreMgr"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.util.MixedCollection.html" ext:member="" ext:cls="Ext.util.MixedCollection">MixedCollection</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">StoreMgr</pre></div><h1>Class <a href="source/StoreMgr.html#cls-Ext.StoreMgr">Ext.StoreMgr</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">StoreMgr.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/StoreMgr.html#cls-Ext.StoreMgr">StoreMgr</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.MixedCollection.html" ext:cls="Ext.util.MixedCollection" ext:member="">MixedCollection</a></td></tr></table><div class="description">The default global group of stores.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.StoreMgr-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.StoreMgr-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-add"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-add">add</a></b>(&nbsp;<code>String&nbsp;key</code>,&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Adds an item to the collection. Fires the add event when complete.</div><div class="long">Adds an item to the collection. Fires the <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-add" ext:member="add" ext:cls="Ext.util.MixedCollection">add</a> event when complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String<div class="sub-desc"><p>The key to associate with the item, or the new item.</p>
<p>If you supplied a <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-getKey" ext:member="getKey" ext:cls="Ext.util.MixedCollection">getKey</a> implementation for this MixedCollection, or if the key
of your stored items is in a property called <tt><b>id</b></tt>, then the MixedCollection
will be able to <i>derive</i> the key for the new item. In this case just pass the new item in
this parameter.</p></div></li><li><code>o</code> : Object<div class="sub-desc">The item to add.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item added.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#add" ext:member="#add" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-addAll"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-addAll">addAll</a></b>(&nbsp;<code>Object/Array&nbsp;objs</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Adds all elements of an Array or an Object to the collection.</div><div class="long">Adds all elements of an Array or an Object to the collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>objs</code> : Object/Array<div class="sub-desc">An Object containing properties which will be added to the collection, or
an Array of values, each of which are added to the collection.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#addAll" ext:member="#addAll" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-clear"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-clear">clear</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all items from the collection.  Fires the clear event when complete.</div><div class="long">Removes all items from the collection.  Fires the <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-clear" ext:member="clear" ext:cls="Ext.util.MixedCollection">clear</a> event when complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#clear" ext:member="#clear" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-clone"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-clone">clone</a></b>()
    :
                                        MixedCollection<div class="mdesc"><div class="short">Creates a shallow copy of this collection</div><div class="long">Creates a shallow copy of this collection<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>MixedCollection</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#clone" ext:member="#clone" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-contains"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-contains">contains</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the collection contains the passed Object as an item.</div><div class="long">Returns true if the collection contains the passed Object as an item.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Object to look for in the collection.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the collection contains the Object as an item.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#contains" ext:member="#contains" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-containsKey"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-containsKey">containsKey</a></b>(&nbsp;<code>String&nbsp;key</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the collection contains the passed Object as a key.</div><div class="long">Returns true if the collection contains the passed Object as a key.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String<div class="sub-desc">The key to look for in the collection.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the collection contains the Object as a key.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#containsKey" ext:member="#containsKey" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-each"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-each">each</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes the specified function once for every item in the collection, passing the following arguments:&#13;
&lt;div class="...</div><div class="long">Executes the specified function once for every item in the collection, passing the following arguments:
<div class="mdetail-params"><ul>
<li><b>item</b> : Mixed<p class="sub-desc">The collection item</p></li>
<li><b>index</b> : Number<p class="sub-desc">The item's index</p></li>
<li><b>length</b> : Number<p class="sub-desc">The total number of items in the collection</p></li>
</ul></div>
The function should return a boolean value. Returning false from the function will stop the iteration.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to execute for each item.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the function.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#each" ext:member="#each" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-eachKey"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-eachKey">eachKey</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Executes the specified function once for every key in the collection, passing each&#13;
key, and its associated item as t...</div><div class="long">Executes the specified function once for every key in the collection, passing each
key, and its associated item as the first two parameters.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to execute for each item.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the function.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#eachKey" ext:member="#eachKey" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-filter"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-filter">filter</a></b>(&nbsp;<code>String&nbsp;property</code>,&nbsp;<code>String/RegExp&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anyMatch</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;caseSensitive</code>]</span>&nbsp;)
    :
                                        MixedCollection<div class="mdesc"><div class="short">Filter the objects in this collection by a specific property.&#13;
Returns a new collection that has been filtered.</div><div class="long">Filter the <i>objects</i> in this collection by a specific property.
Returns a new collection that has been filtered.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>property</code> : String<div class="sub-desc">A property on your objects</div></li><li><code>value</code> : String/RegExp<div class="sub-desc">Either string that the property values
should start with or a RegExp to test against the property</div></li><li><code>anyMatch</code> : Boolean<div class="sub-desc">(optional) True to match any part of the string, not just the beginning</div></li><li><code>caseSensitive</code> : Boolean<div class="sub-desc">(optional) True for case sensitive comparison (defaults to False).</div></li></ul><strong>Returns:</strong><ul><li><code>MixedCollection</code><div class="sub-desc">The new filtered collection</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#filter" ext:member="#filter" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-filterBy"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-filterBy">filterBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        MixedCollection<div class="mdesc"><div class="short">Filter by a function. Returns a new collection that has been filtered.&#13;
The passed function will be called with each ...</div><div class="long">Filter by a function. Returns a <i>new</i> collection that has been filtered.
The passed function will be called with each object in the collection.
If the function returns true, the value is included otherwise it is filtered.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to be called, it will receive the args o (the object), k (the key)</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to this)</div></li></ul><strong>Returns:</strong><ul><li><code>MixedCollection</code><div class="sub-desc">The new filtered collection</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#filterBy" ext:member="#filterBy" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-find"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-find">find</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the first item in the collection which elicits a true return value from the&#13;
passed selection function.</div><div class="long">Returns the first item in the collection which elicits a true return value from the
passed selection function.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The selection function to execute for each item.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the function.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The first item in the collection which returned true from the selection function.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#find" ext:member="#find" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-findIndex"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-findIndex">findIndex</a></b>(&nbsp;<code>String&nbsp;property</code>,&nbsp;<code>String/RegExp&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;start</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anyMatch</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;caseSensitive</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Finds the index of the first matching object in this collection by a specific property/value.</div><div class="long">Finds the index of the first matching object in this collection by a specific property/value.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>property</code> : String<div class="sub-desc">The name of a property on your objects.</div></li><li><code>value</code> : String/RegExp<div class="sub-desc">A string that the property values
should start with or a RegExp to test against the property.</div></li><li><code>start</code> : Number<div class="sub-desc">(optional) The index to start searching at (defaults to 0).</div></li><li><code>anyMatch</code> : Boolean<div class="sub-desc">(optional) True to match any part of the string, not just the beginning.</div></li><li><code>caseSensitive</code> : Boolean<div class="sub-desc">(optional) True for case sensitive comparison.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The matched index or -1</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#findIndex" ext:member="#findIndex" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-findIndexBy"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-findIndexBy">findIndexBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;start</code>]</span>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Find the index of the first matching object in this collection by a function.&#13;
If the function returns true it is con...</div><div class="long">Find the index of the first matching object in this collection by a function.
If the function returns <i>true</i> it is considered a match.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to be called, it will receive the args o (the object), k (the key).</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to this).</div></li><li><code>start</code> : Number<div class="sub-desc">(optional) The index to start searching at (defaults to 0).</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The matched index or -1</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#findIndexBy" ext:member="#findIndexBy" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-first"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-first">first</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the first item in the collection.</div><div class="long">Returns the first item in the collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">the first item in the collection..</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#first" ext:member="#first" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-get"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-get">get</a></b>(&nbsp;<code>String/Number&nbsp;key</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the item associated with the passed key or index.</div><div class="long">Returns the item associated with the passed key or index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String/Number<div class="sub-desc">The key or index of the item.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item associated with the passed key.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#get" ext:member="#get" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-getCount"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-getCount">getCount</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the number of items in the collection.</div><div class="long">Returns the number of items in the collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">the number of items in the collection.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#getCount" ext:member="#getCount" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-getKey"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-getKey">getKey</a></b>(&nbsp;<code>Object&nbsp;item</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">MixedCollection has a generic way to fetch keys if you implement getKey.  The default implementation&#13;
simply returns ...</div><div class="long">MixedCollection has a generic way to fetch keys if you implement getKey.  The default implementation
simply returns <tt style="font-weight:bold;">item.id</tt> but you can provide your own implementation
to return a different value as in the following examples:
<pre><code><i>// normal way
</i>
<b>var</b> mc = <b>new</b> Ext.util.MixedCollection();
mc.add(someEl.dom.id, someEl);
mc.add(otherEl.dom.id, otherEl);
<i>//and so on
</i>

<i>// using getKey
</i>
<b>var</b> mc = <b>new</b> Ext.util.MixedCollection();
mc.getKey = <b>function</b>(el){
   <b>return</b> el.dom.id;
};
mc.add(someEl);
mc.add(otherEl);

<i>// or via the constructor
</i>
<b>var</b> mc = <b>new</b> Ext.util.MixedCollection(false, <b>function</b>(el){
   <b>return</b> el.dom.id;
});
mc.add(someEl);
mc.add(otherEl);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>item</code> : Object<div class="sub-desc">The item for which to find the key.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The key for the passed item.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#getKey" ext:member="#getKey" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-getRange"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-getRange">getRange</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;startIndex</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Number&nbsp;endIndex</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Returns a range of items in this collection</div><div class="long">Returns a range of items in this collection<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>startIndex</code> : Number<div class="sub-desc">(optional) defaults to 0</div></li><li><code>endIndex</code> : Number<div class="sub-desc">(optional) default to the last item</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">An array of items</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#getRange" ext:member="#getRange" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-indexOf"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-indexOf">indexOf</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns index within the collection of the passed Object.</div><div class="long">Returns index within the collection of the passed Object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The item to find the index of.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">index of the item. Returns -1 if not found.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#indexOf" ext:member="#indexOf" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-indexOfKey"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-indexOfKey">indexOfKey</a></b>(&nbsp;<code>String&nbsp;key</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns index within the collection of the passed key.</div><div class="long">Returns index within the collection of the passed key.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String<div class="sub-desc">The key to find the index of.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">index of the key.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#indexOfKey" ext:member="#indexOfKey" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-insert"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-insert">insert</a></b>(&nbsp;<code>Number&nbsp;index</code>,&nbsp;<code>String&nbsp;key</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;o</code>]</span>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Inserts an item at the specified index in the collection. Fires the add event when complete.</div><div class="long">Inserts an item at the specified index in the collection. Fires the <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-add" ext:member="add" ext:cls="Ext.util.MixedCollection">add</a> event when complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index to insert the item at.</div></li><li><code>key</code> : String<div class="sub-desc">The key to associate with the new item, or the item itself.</div></li><li><code>o</code> : Object<div class="sub-desc">(optional) If the second parameter was a key, the new item.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item inserted.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#insert" ext:member="#insert" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-item"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-item">item</a></b>(&nbsp;<code>String/Number&nbsp;key</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the item associated with the passed key OR index. Key has priority over index.  This is the equivalent&#13;
of ca...</div><div class="long">Returns the item associated with the passed key OR index. Key has priority over index.  This is the equivalent
of calling <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-key" ext:member="key" ext:cls="Ext.util.MixedCollection">key</a> first, then if nothing matched calling <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-itemAt" ext:member="itemAt" ext:cls="Ext.util.MixedCollection">itemAt</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String/Number<div class="sub-desc">The key or index of the item.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item associated with the passed key.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#item" ext:member="#item" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-itemAt"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-itemAt">itemAt</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the item at the specified index.</div><div class="long">Returns the item at the specified index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index of the item.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item at the specified index.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#itemAt" ext:member="#itemAt" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-key"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-key">key</a></b>(&nbsp;<code>String/Number&nbsp;key</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the item associated with the passed key.</div><div class="long">Returns the item associated with the passed key.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String/Number<div class="sub-desc">The key of the item.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item associated with the passed key.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#key" ext:member="#key" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-keySort"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-keySort">keySort</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;direction</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;fn</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sorts this collection by keys</div><div class="long">Sorts this collection by keys<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>direction</code> : String<div class="sub-desc">(optional) "ASC" or "DESC"</div></li><li><code>fn</code> : Function<div class="sub-desc">(optional) a comparison function (defaults to case insensitive string)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#keySort" ext:member="#keySort" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-last"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-last">last</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the last item in the collection.</div><div class="long">Returns the last item in the collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">the last item in the collection..</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#last" ext:member="#last" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.StoreMgr-lookup"></a><b><a href="source/StoreMgr.html#method-Ext.StoreMgr-lookup">lookup</a></b>(&nbsp;<code>String/Object&nbsp;id</code>&nbsp;)
    :
                                        Ext.data.Store<div class="mdesc"><div class="short">Gets a registered Store by id</div><div class="long">Gets a registered Store by id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String/Object<div class="sub-desc">The id of the Store, or a Store instance</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.data.Store</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">StoreMgr</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.StoreMgr-register"></a><b><a href="source/StoreMgr.html#method-Ext.StoreMgr-register">register</a></b>(&nbsp;<code>Ext.data.Store&nbsp;store1</code>,&nbsp;<span title="Optional" class="optional">[<code>Ext.data.Store&nbsp;store2</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Ext.data.Store&nbsp;etc...</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Registers one or more Stores with the StoreMgr. You do not normally need to register stores
manually.  Any store init...</div><div class="long">Registers one or more Stores with the StoreMgr. You do not normally need to register stores
manually.  Any store initialized with a <a href="output/Ext.data.Store.html#Ext.data.Store-storeId" ext:member="storeId" ext:cls="Ext.data.Store">Ext.data.Store.storeId</a> will be auto-registered.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>store1</code> : Ext.data.Store<div class="sub-desc">A Store instance</div></li><li><code>store2</code> : Ext.data.Store<div class="sub-desc">(optional)</div></li><li><code>etc...</code> : Ext.data.Store<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StoreMgr</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-remove"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-remove">remove</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Remove an item from the collection.</div><div class="long">Remove an item from the collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The item to remove.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item removed or false if no item was removed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#remove" ext:member="#remove" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-removeAt"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-removeAt">removeAt</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Remove an item from a specified index in the collection. Fires the remove event when complete.</div><div class="long">Remove an item from a specified index in the collection. Fires the <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-remove" ext:member="remove" ext:cls="Ext.util.MixedCollection">remove</a> event when complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index within the collection of the item to remove.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item removed or false if no item was removed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#removeAt" ext:member="#removeAt" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-removeKey"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-removeKey">removeKey</a></b>(&nbsp;<code>String&nbsp;key</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Removed an item associated with the passed key fom the collection.</div><div class="long">Removed an item associated with the passed key fom the collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String<div class="sub-desc">The key of the item to remove.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The item removed or false if no item was removed.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#removeKey" ext:member="#removeKey" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-replace"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-replace">replace</a></b>(&nbsp;<code>String&nbsp;key</code>,&nbsp;<code>o&nbsp;{Object}</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Replaces an item in the collection. Fires the replace event when complete.</div><div class="long">Replaces an item in the collection. Fires the <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-replace" ext:member="replace" ext:cls="Ext.util.MixedCollection">replace</a> event when complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>key</code> : String<div class="sub-desc"><p>The key associated with the item to replace, or the replacement item.</p>
<p>If you supplied a <a href="output/Ext.util.MixedCollection.html#Ext.util.MixedCollection-getKey" ext:member="getKey" ext:cls="Ext.util.MixedCollection">getKey</a> implementation for this MixedCollection, or if the key
of your stored items is in a property called <tt><b>id</b></tt>, then the MixedCollection
will be able to <i>derive</i> the key of the replacement item. If you want to replace an item
with one having the same key value, then just pass the replacement item in this parameter.</p></div></li><li><code>{Object}</code> : o<div class="sub-desc">o (optional) If the first parameter passed was a key, the item to associate
with that key.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The new item.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#replace" ext:member="#replace" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-sort"></a><b><a href="source/MixedCollection.html#method-Ext.util.MixedCollection-sort">sort</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;direction</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;fn</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sorts this collection with the passed comparison function</div><div class="long">Sorts this collection with the passed comparison function<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>direction</code> : String<div class="sub-desc">(optional) "ASC" or "DESC"</div></li><li><code>fn</code> : Function<div class="sub-desc">(optional) comparison function</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#sort" ext:member="#sort" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.StoreMgr-unregister"></a><b><a href="source/StoreMgr.html#method-Ext.StoreMgr-unregister">unregister</a></b>(&nbsp;<code>String/Object&nbsp;id1</code>,&nbsp;<span title="Optional" class="optional">[<code>String/Object&nbsp;id2</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String/Object&nbsp;etc...</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unregisters one or more Stores with the StoreMgr</div><div class="long">Unregisters one or more Stores with the StoreMgr<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id1</code> : String/Object<div class="sub-desc">The id of the Store, or a Store instance</div></li><li><code>id2</code> : String/Object<div class="sub-desc">(optional)</div></li><li><code>etc...</code> : String/Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StoreMgr</td></tr></tbody></table><a id="Ext.StoreMgr-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-add"></a><b><a href="source/MixedCollection.html#event-Ext.util.MixedCollection-add">add</a></b> :
                                      (&nbsp;<code>Number&nbsp;index</code>,&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>String&nbsp;key</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an item is added to the collection.</div><div class="long">Fires when an item is added to the collection.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>index</code> : Number<div class="sub-desc">The index at which the item was added.</div></li><li><code>o</code> : Object<div class="sub-desc">The item added.</div></li><li><code>key</code> : String<div class="sub-desc">The key associated with the added item.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#add" ext:member="#add" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-clear"></a><b><a href="source/MixedCollection.html#event-Ext.util.MixedCollection-clear">clear</a></b> :
                                      ()
    <div class="mdesc"><div class="short">Fires when the collection is cleared.</div><div class="long">Fires when the collection is cleared.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li>None.</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#clear" ext:member="#clear" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-remove"></a><b><a href="source/MixedCollection.html#event-Ext.util.MixedCollection-remove">remove</a></b> :
                                      (&nbsp;<code>Object&nbsp;o</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;key</code>]</span>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an item is removed from the collection.</div><div class="long">Fires when an item is removed from the collection.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The item being removed.</div></li><li><code>key</code> : String<div class="sub-desc">(optional) The key associated with the removed item.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#remove" ext:member="#remove" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.MixedCollection-replace"></a><b><a href="source/MixedCollection.html#event-Ext.util.MixedCollection-replace">replace</a></b> :
                                      (&nbsp;<code>String&nbsp;key</code>,&nbsp;<code>Object&nbsp;old</code>,&nbsp;<code>Object&nbsp;new</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when an item is replaced in the collection.</div><div class="long">Fires when an item is replaced in the collection.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>key</code> : String<div class="sub-desc">he key associated with the new added.</div></li><li><code>old</code> : Object<div class="sub-desc">The item being replaced.</div></li><li><code>new</code> : Object<div class="sub-desc">The new item.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.MixedCollection.html#replace" ext:member="#replace" ext:cls="Ext.util.MixedCollection">MixedCollection</a></td></tr></tbody></table></div>