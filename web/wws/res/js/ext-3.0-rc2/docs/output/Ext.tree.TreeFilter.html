<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.tree.TreeFilter-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.tree.TreeFilter-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.tree.TreeFilter-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.tree.TreeFilter"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/TreeFilter.html#cls-Ext.tree.TreeFilter">Ext.tree.TreeFilter</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.tree</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TreeFilter.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TreeFilter.html#cls-Ext.tree.TreeFilter">TreeFilter</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Note this class is experimental and doesn't update the indent (lines) or expand collapse icons of the nodes</div><div class="hr"></div><a id="Ext.tree.TreeFilter-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.tree.TreeFilter-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeFilter-clear"></a><b><a href="source/TreeFilter.html#method-Ext.tree.TreeFilter-clear">clear</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Clears the current filter. Note: with the "remove" option
set a filter cannot be cleared.</div><div class="long">Clears the current filter. Note: with the "remove" option
set a filter cannot be cleared.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeFilter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeFilter-filter"></a><b><a href="source/TreeFilter.html#method-Ext.tree.TreeFilter-filter">filter</a></b>(&nbsp;<code>String/RegExp&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;attr</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>TreeNode&nbsp;startNode</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Filter the data by a specific attribute.</div><div class="long">Filter the data by a specific attribute.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String/RegExp<div class="sub-desc">Either string that the attribute value
should start with or a RegExp to test against the attribute</div></li><li><code>attr</code> : String<div class="sub-desc">(optional) The attribute passed in your node's attributes collection. Defaults to "text".</div></li><li><code>startNode</code> : TreeNode<div class="sub-desc">(optional) The node to start the filter at.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeFilter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeFilter-filterBy"></a><b><a href="source/TreeFilter.html#method-Ext.tree.TreeFilter-filterBy">filterBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Filter by a function. The passed function will be called with each
node in the tree (or from the startNode). If the f...</div><div class="long">Filter by a function. The passed function will be called with each
node in the tree (or from the startNode). If the function returns true, the node is kept
otherwise it is filtered. If a node is filtered, its children are also filtered.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The filter function</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to the current node)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeFilter</td></tr></tbody></table><a id="Ext.tree.TreeFilter-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>