<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.util.Format-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.util.Format-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.util.Format-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.util.Format"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Format.html#cls-Ext.util.Format">Ext.util.Format</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.util</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Format.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Format.html#cls-Ext.util.Format">Format</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Reusable data formatting functions<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.util.Format-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.util.Format-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-capitalize"></a><b><a href="source/Format.html#method-Ext.util.Format-capitalize">capitalize</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Converts the first character only of a string to upper case</div><div class="long">Converts the first character only of a string to upper case<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The text to convert</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The converted text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-date"></a><b><a href="source/Format.html#method-Ext.util.Format-date">date</a></b>(&nbsp;<code>String/Date&nbsp;value</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;format</code>]</span>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Parse a value into a formatted date using the specified format pattern.</div><div class="long">Parse a value into a formatted date using the specified format pattern.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String/Date<div class="sub-desc">The value to format (Strings must conform to the format expected by the javascript Date object's <a href="http://www.w3schools.com/jsref/jsref_parse.asp">parse()</a> method)</div></li><li><code>format</code> : String<div class="sub-desc">(optional) Any valid date format string (defaults to 'm/d/Y')</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The formatted date string</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-dateRenderer"></a><b><a href="source/Format.html#method-Ext.util.Format-dateRenderer">dateRenderer</a></b>(&nbsp;<code>String&nbsp;format</code>&nbsp;)
    :
                                        Function<div class="mdesc"><div class="short">Returns a date rendering function that can be reused to apply a date format multiple times efficiently</div><div class="long">Returns a date rendering function that can be reused to apply a date format multiple times efficiently<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>format</code> : String<div class="sub-desc">Any valid date format string</div></li></ul><strong>Returns:</strong><ul><li><code>Function</code><div class="sub-desc">The date formatting function</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-defaultValue"></a><b><a href="source/Format.html#method-Ext.util.Format-defaultValue">defaultValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>,&nbsp;<code>String&nbsp;defaultValue</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Checks a reference and converts it to the default value if it's empty</div><div class="long">Checks a reference and converts it to the default value if it's empty<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">Reference to check</div></li><li><code>defaultValue</code> : String<div class="sub-desc">The value to insert of it's undefined (defaults to "")</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-ellipsis"></a><b><a href="source/Format.html#method-Ext.util.Format-ellipsis">ellipsis</a></b>(&nbsp;<code>String&nbsp;value</code>,&nbsp;<code>Number&nbsp;length</code>,&nbsp;<code>Boolean&nbsp;word</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Truncate a string and add an ellipsis ('...') to the end if it exceeds the specified length</div><div class="long">Truncate a string and add an ellipsis ('...') to the end if it exceeds the specified length<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The string to truncate</div></li><li><code>length</code> : Number<div class="sub-desc">The maximum length to allow before truncating</div></li><li><code>word</code> : Boolean<div class="sub-desc">True to try to find a common work break</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The converted text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-fileSize"></a><b><a href="source/Format.html#method-Ext.util.Format-fileSize">fileSize</a></b>(&nbsp;<code>Number/String&nbsp;size</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Simple format for a file size (xxx bytes, xxx KB, xxx MB)</div><div class="long">Simple format for a file size (xxx bytes, xxx KB, xxx MB)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>size</code> : Number/String<div class="sub-desc">The numeric value to format</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The formatted file size</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-htmlDecode"></a><b><a href="source/Format.html#method-Ext.util.Format-htmlDecode">htmlDecode</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Convert certain characters (&amp;, &lt;, &gt;, and ') from their HTML character equivalents.</div><div class="long">Convert certain characters (&, <, >, and ') from their HTML character equivalents.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The string to decode</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The decoded text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-htmlEncode"></a><b><a href="source/Format.html#method-Ext.util.Format-htmlEncode">htmlEncode</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Convert certain characters (&amp;, &lt;, &gt;, and ') to their HTML character equivalents for literal display in web pages.</div><div class="long">Convert certain characters (&, <, >, and ') to their HTML character equivalents for literal display in web pages.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The string to encode</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The encoded text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-lowercase"></a><b><a href="source/Format.html#method-Ext.util.Format-lowercase">lowercase</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Converts a string to all lower case letters</div><div class="long">Converts a string to all lower case letters<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The text to convert</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The converted text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-nl2br"></a><b><a href="source/Format.html#method-Ext.util.Format-nl2br">nl2br</a></b>(&nbsp;<code>String&nbsp;The</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Converts newline characters to the HTML tag &amp;lt;br/&gt;</div><div class="long">Converts newline characters to the HTML tag &lt;br/><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>The</code> : String<div class="sub-desc">string value to format.</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The string with embedded &amp;lt;br/&gt; tags in place of newlines.</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-number"></a><b><a href="source/Format.html#method-Ext.util.Format-number">number</a></b>(&nbsp;<code>Number&nbsp;v</code>,&nbsp;<code>String&nbsp;format</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Formats the number according to the format string.&#13;
&lt;div style="margin-left:40px"&gt;examples (123456.789):&#13;
&lt;div style=...</div><div class="long">Formats the number according to the format string.
<div style="margin-left:40px">examples (123456.789):
<div style="margin-left:10px">
0 - (123456) show only digits, no precision<br>
0.00 - (123456.78) show only digits, 2 precision<br>
0.0000 - (123456.7890) show only digits, 4 precision<br>
0,000 - (123,456) show comma and digits, no precision<br>
0,000.00 - (123,456.78) show comma and digits, 2 precision<br>
0,0.00 - (123,456.78) shortcut method, show comma and digits, 2 precision<br>
To reverse the grouping (,) and decimal (.) for international numbers, add /i to the end.
For example: 0.000,00/i
</div></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>v</code> : Number<div class="sub-desc">The number to format.</div></li><li><code>format</code> : String<div class="sub-desc">The way you would like to format this text.</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The formatted number.</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-numberRenderer"></a><b><a href="source/Format.html#method-Ext.util.Format-numberRenderer">numberRenderer</a></b>(&nbsp;<code>String&nbsp;format</code>&nbsp;)
    :
                                        Function<div class="mdesc"><div class="short">Returns a number rendering function that can be reused to apply a number format multiple times efficiently</div><div class="long">Returns a number rendering function that can be reused to apply a number format multiple times efficiently<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>format</code> : String<div class="sub-desc">Any valid number format string for <a href="output/Ext.util.Format.html#Ext.util.Format-number" ext:member="number" ext:cls="Ext.util.Format">number</a></div></li></ul><strong>Returns:</strong><ul><li><code>Function</code><div class="sub-desc">The number formatting function</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-plural"></a><b><a href="source/Format.html#method-Ext.util.Format-plural">plural</a></b>(&nbsp;<code>Number&nbsp;value</code>,&nbsp;<code>String&nbsp;singular</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;plural</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Selectively do a plural form of a word based on a numeric value. For example, in a template,&#13;
{commentCount:plural("C...</div><div class="long">Selectively do a plural form of a word based on a numeric value. For example, in a template,
{commentCount:plural("Comment")}  would result in "1 Comment" if commentCount was 1 or would be "x Comments"
if the value is 0 or greater than 1.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Number<div class="sub-desc">The value to compare against</div></li><li><code>singular</code> : String<div class="sub-desc">The singular form of the word</div></li><li><code>plural</code> : String<div class="sub-desc">(optional) The plural form of the word (defaults to the singular with an "s")</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-round"></a><b><a href="source/Format.html#method-Ext.util.Format-round">round</a></b>(&nbsp;<code>Number/String&nbsp;value</code>,&nbsp;<code>Number&nbsp;precision</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Rounds the passed number to the required decimal precision.</div><div class="long">Rounds the passed number to the required decimal precision.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Number/String<div class="sub-desc">The numeric value to round.</div></li><li><code>precision</code> : Number<div class="sub-desc">The number of decimal places to which to round the first parameter's value.</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The rounded value.</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-stripScripts"></a><b><a href="source/Format.html#method-Ext.util.Format-stripScripts">stripScripts</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Strips all script tags</div><div class="long">Strips all script tags<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The text from which to strip script tags</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The stripped text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-stripTags"></a><b><a href="source/Format.html#method-Ext.util.Format-stripTags">stripTags</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Strips all HTML tags</div><div class="long">Strips all HTML tags<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The text from which to strip tags</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The stripped text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-substr"></a><b><a href="source/Format.html#method-Ext.util.Format-substr">substr</a></b>(&nbsp;<code>String&nbsp;value</code>,&nbsp;<code>Number&nbsp;start</code>,&nbsp;<code>Number&nbsp;length</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns a substring from within an original string</div><div class="long">Returns a substring from within an original string<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The original text</div></li><li><code>start</code> : Number<div class="sub-desc">The start index of the substring</div></li><li><code>length</code> : Number<div class="sub-desc">The length of the substring</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The substring</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-trim"></a><b><a href="source/Format.html#method-Ext.util.Format-trim">trim</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Trims any whitespace from either side of a string</div><div class="long">Trims any whitespace from either side of a string<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The text to trim</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The trimmed text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-undef"></a><b><a href="source/Format.html#method-Ext.util.Format-undef">undef</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Checks a reference and converts it to empty string if it is undefined</div><div class="long">Checks a reference and converts it to empty string if it is undefined<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">Reference to check</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">Empty string if converted, otherwise the original value</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-uppercase"></a><b><a href="source/Format.html#method-Ext.util.Format-uppercase">uppercase</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Converts a string to all upper case letters</div><div class="long">Converts a string to all upper case letters<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The text to convert</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The converted text</div></li></ul></div></div></div></td><td class="msource">Format</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Format-usMoney"></a><b><a href="source/Format.html#method-Ext.util.Format-usMoney">usMoney</a></b>(&nbsp;<code>Number/String&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Format a number as US currency</div><div class="long">Format a number as US currency<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Number/String<div class="sub-desc">The numeric value to format</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The formatted currency string</div></li></ul></div></div></div></td><td class="msource">Format</td></tr></tbody></table><a id="Ext.util.Format-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>