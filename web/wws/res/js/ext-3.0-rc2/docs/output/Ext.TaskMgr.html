<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.TaskMgr-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.TaskMgr-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.TaskMgr-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.TaskMgr"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.TaskRunner.html" ext:member="" ext:cls="Ext.util.TaskRunner">TaskRunner</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">TaskMgr</pre></div><h1>Class <a href="source/TaskMgr.html#cls-Ext.TaskMgr">Ext.TaskMgr</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TaskMgr.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TaskMgr.html#cls-Ext.TaskMgr">TaskMgr</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.TaskRunner.html" ext:cls="Ext.util.TaskRunner" ext:member="">TaskRunner</a></td></tr></table><div class="description">A static <a href="output/Ext.util.TaskRunner.html" ext:cls="Ext.util.TaskRunner">Ext.util.TaskRunner</a> instance that can be used to start and stop arbitrary tasks.  See
<a href="output/Ext.util.TaskRunner.html" ext:cls="Ext.util.TaskRunner">Ext.util.TaskRunner</a> for supported methods and task config properties.
<pre><code><i>// Start a simple clock task that updates a div once per second</i>
<b>var</b> task = {
    run: <b>function</b>(){
        Ext.fly(<em>'clock'</em>).update(<b>new</b> Date().format(<em>'g:i:s A'</em>));
    },
    interval: 1000 <i>//1 second</i>
}
Ext.TaskMgr.start(task);</code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.TaskMgr-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.TaskMgr-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TaskRunner-start"></a><b><a href="source/TaskMgr.html#method-Ext.util.TaskRunner-start">start</a></b>(&nbsp;<code>Object&nbsp;task</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Starts a new task.</div><div class="long">Starts a new task.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>task</code> : Object<div class="sub-desc">A config object that supports the following properties:<ul>
<li><code>run</code> : Function<div class="sub-desc">The function to execute each time the task is run. The
function will be called at each interval and passed the <code>args</code> argument if specified.  If a
particular scope is required, be sure to specify it using the <code>scope</code> argument.</div></li>
<li><code>interval</code> : Number<div class="sub-desc">The frequency in milliseconds with which the task
should be executed.</div></li>
<li><code>args</code> : Array<div class="sub-desc">(optional) An array of arguments to be passed to the function
specified by <code>run</code>.</div></li>
<li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<tt>this</tt> reference) in which to execute the
<code>run</code> function. Defaults to the task config object.</div></li>
<li><code>duration</code> : Number<div class="sub-desc">(optional) The length of time in milliseconds to execute
the task before stopping automatically (defaults to indefinite).</div></li>
<li><code>repeat</code> : Number<div class="sub-desc">(optional) The number of times to execute the task before
stopping automatically (defaults to indefinite).</div></li>
</ul></div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The task</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.TaskRunner.html#start" ext:member="#start" ext:cls="Ext.util.TaskRunner">TaskRunner</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TaskRunner-stop"></a><b><a href="source/TaskMgr.html#method-Ext.util.TaskRunner-stop">stop</a></b>(&nbsp;<code>Object&nbsp;task</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Stops an existing running task.</div><div class="long">Stops an existing running task.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>task</code> : Object<div class="sub-desc">The task to stop</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The task</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.TaskRunner.html#stop" ext:member="#stop" ext:cls="Ext.util.TaskRunner">TaskRunner</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.TaskRunner-stopAll"></a><b><a href="source/TaskMgr.html#method-Ext.util.TaskRunner-stopAll">stopAll</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Stops all tasks that are currently running.</div><div class="long">Stops all tasks that are currently running.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.TaskRunner.html#stopAll" ext:member="#stopAll" ext:cls="Ext.util.TaskRunner">TaskRunner</a></td></tr></tbody></table><a id="Ext.TaskMgr-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>