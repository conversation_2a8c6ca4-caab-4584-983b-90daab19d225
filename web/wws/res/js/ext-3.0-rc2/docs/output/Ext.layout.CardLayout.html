<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.CardLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.CardLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.CardLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.CardLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.CardLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.layout.ContainerLayout.html" ext:member="" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.layout.FitLayout.html" ext:member="" ext:cls="Ext.layout.FitLayout">FitLayout</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">CardLayout</pre></div><h1>Class <a href="source/CardLayout.html#cls-Ext.layout.CardLayout">Ext.layout.CardLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">CardLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/CardLayout.html#cls-Ext.layout.CardLayout">CardLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout" ext:member="">FitLayout</a></td></tr></table><div class="description"><p>This layout manages multiple child Components, each fitted to the Container, where only a single child Component can be
visible at any given time.  This layout style is most commonly used for wizards, tab implementations, etc.
This class is intended to be extended or created via the layout:'card' <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">Ext.Container.layout</a> config,
and should generally not need to be created directly via the new keyword.</p>
<p>The CardLayout's focal method is <a href="output/Ext.layout.CardLayout.html#Ext.layout.CardLayout-setActiveItem" ext:member="setActiveItem" ext:cls="Ext.layout.CardLayout">setActiveItem</a>.  Since only one panel is displayed at a time,
the only way to move from one Component to the next is by calling setActiveItem, passing the id or index of
the next panel to display.  The layout itself does not provide a user interface for handling this navigation,
so that functionality must be provided by the developer.</p>
<p>In the following example, a simplistic wizard setup is demonstrated.  A button bar is added
to the footer of the containing panel to provide navigation buttons.  The buttons will be handled by a
common navigation routine -- for this example, the implementation of that routine has been ommitted since
it can be any type of custom logic.  Note that other uses of a CardLayout (like a tab control) would require a
completely different implementation.  For serious implementations, a better approach would be to extend
CardLayout to provide the custom functionality needed.  Example usage:</p>
<pre><code><b>var</b> navHandler = <b>function</b>(direction){
    <i>// This routine could contain business logic required to manage the navigation steps.
</i>
    <i>// It would call setActiveItem as needed, manage navigation button state, handle any
</i>
    <i>// branching logic that might be required, handle alternate actions like cancellation
</i>
    <i>// or finalization, etc.  A complete wizard implementation could get pretty
</i>
    <i>// sophisticated depending on the complexity required, and should probably be
</i>
    <i>// done as a subclass of CardLayout <b>in</b> a real-world implementation.
</i>
};

<b>var</b> card = <b>new</b> Ext.Panel({
    title: <em>'Example Wizard'</em>,
    layout:<em>'card'</em>,
    activeItem: 0, <i>// make sure the active item is set on the container config!
</i>
    bodyStyle: <em>'padding:15px'</em>,
    defaults: {
        <i>// applied to each contained panel
</i>
        border:false
    },
    <i>// just an example of one possible navigation scheme, using buttons
</i>
    bbar: [
        {
            id: <em>'move-prev'</em>,
            text: <em>'Back'</em>,
            handler: navHandler.createDelegate(this, [-1]),
            disabled: true
        },
        <em>'->'</em>, <i>// greedy spacer so that the buttons are aligned to each side
</i>
        {
            id: <em>'move-next'</em>,
            text: <em>'Next'</em>,
            handler: navHandler.createDelegate(this, [1])
        }
    ],
    <i>// the panels (or <em>"cards"</em>) within the layout
</i>
    items: [{
        id: <em>'card-0'</em>,
        html: <em>'&lt;h1&gt;Welcome to the Wizard!&lt;/h1&gt;&lt;p&gt;Step 1 of 3&lt;/p&gt;'</em>
    },{
        id: <em>'card-1'</em>,
        html: <em>'&lt;p&gt;Step 2 of 3&lt;/p&gt;'</em>
    },{
        id: <em>'card-2'</em>,
        html: <em>'&lt;h1&gt;Congratulations!&lt;/h1&gt;&lt;p&gt;Step 3 of 3 - Complete&lt;/p&gt;'</em>
    }]
});</code></pre></div><div class="hr"></div><a id="Ext.layout.CardLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.CardLayout-deferredRender"></a><b><a href="source/CardLayout.html#cfg-Ext.layout.CardLayout-deferredRender">deferredRender</a></b> : Boolean<div class="mdesc"><div class="short">True to render each contained item at the time it becomes active, false to render all contained items&#13;
as soon as the...</div><div class="long">True to render each contained item at the time it becomes active, false to render all contained items
as soon as the layout is rendered (defaults to false).  If there is a significant amount of content or
a lot of heavy controls being rendered into panels that are not displayed by default, setting this to
true might improve performance.</div></div></td><td class="msource">CardLayout</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#extraCls" ext:member="#extraCls" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.CardLayout-layoutOnCardChange"></a><b><a href="source/CardLayout.html#cfg-Ext.layout.CardLayout-layoutOnCardChange">layoutOnCardChange</a></b> : Boolean<div class="mdesc">True to force a layout of the active item when the active card is changed. Defaults to false.</div></td><td class="msource">CardLayout</td></tr></tbody></table><a id="Ext.layout.CardLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-activeItem"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-activeItem">activeItem</a></b> : Ext.Component<div class="mdesc"><div class="short">A reference to the Ext.Component that is active.  For example, if(myPanel.layout.activeItem.id == 'item-1') { ... }
a...</div><div class="long">A reference to the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> that is active.  For example, <pre><code><b>if</b>(myPanel.layout.activeItem.id == <em>'item-1'</em>) { ... }</code></pre>
<tt>activeItem</tt> only applies to layout styles that can display items one at a time
(like <a href="output/Ext.layout.AccordionLayout.html" ext:cls="Ext.layout.AccordionLayout">Ext.layout.AccordionLayout</a>, <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>
and <a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout">Ext.layout.FitLayout</a>).  Read-only.  Related to <a href="output/Ext.Container.html#Ext.Container-activeItem" ext:member="activeItem" ext:cls="Ext.Container">Ext.Container.activeItem</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#activeItem" ext:member="#activeItem" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#fieldTpl" ext:member="#fieldTpl" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.CardLayout-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.CardLayout-setActiveItem"></a><b><a href="source/CardLayout.html#method-Ext.layout.CardLayout-setActiveItem">setActiveItem</a></b>(&nbsp;<code>String/Number&nbsp;item</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the active (visible) item in the layout.</div><div class="long">Sets the active (visible) item in the layout.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>item</code> : String/Number<div class="sub-desc">The string component id or numeric index of the item to activate</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">CardLayout</td></tr></tbody></table><a id="Ext.layout.CardLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>