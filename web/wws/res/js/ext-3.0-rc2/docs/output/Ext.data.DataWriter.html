<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.DataWriter-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.DataWriter-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.DataWriter-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.DataWriter-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.DataWriter"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/DataWriter.html#cls-Ext.data.DataWriter">Ext.data.DataWriter</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DataWriter.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DataWriter.html#cls-Ext.data.DataWriter">DataWriter</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.data.JsonWriter.html" ext:cls="Ext.data.JsonWriter">JsonWriter</a>,&#13;<a href="output/Ext.data.XmlWriter.html" ext:cls="Ext.data.XmlWriter">XmlWriter</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Abstract base class for writing structured data from a data source and converting
it into a JSON-string containing <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> objects and metadata for use
by an <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>.  This class is intended to be extended and should not
be created directly. For existing implementations, see <a href="output/Ext.data.JsonWriter.html" ext:cls="Ext.data.JsonWriter">Ext.data.JsonWriter</a>,</div><div class="hr"></div><a id="Ext.data.DataWriter-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-createRecord"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-createRecord">createRecord</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.JsonWriter.html#Ext.data.JsonWriter-createRecord" ext:member="createRecord" ext:cls="Ext.data.JsonWriter">JsonWriter.createRecord</a></div></td><td class="msource">DataWriter</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-destroyRecord"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-destroyRecord">destroyRecord</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.JsonWriter.html#Ext.data.JsonWriter-destroyRecord" ext:member="destroyRecord" ext:cls="Ext.data.JsonWriter">JsonWriter.destroyRecord</a></div></td><td class="msource">DataWriter</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-listful"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-listful">listful</a></b> : Boolean<div class="mdesc"><div class="short">false by default.  Set true to have the DataWriter always write HTTP params as a list,
even when acting upon a single...</div><div class="long"><tt>false</tt> by default.  Set <tt>true</tt> to have the DataWriter <b>always</b> write HTTP params as a list,
even when acting upon a single record.</div></div></td><td class="msource">DataWriter</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-saveRecord"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-saveRecord">saveRecord</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.JsonWriter.html#Ext.data.JsonWriter-saveRecord" ext:member="saveRecord" ext:cls="Ext.data.JsonWriter">JsonWriter.saveRecord</a></div></td><td class="msource">DataWriter</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-writeAllFields"></a><b><a href="source/DataWriter.html#cfg-Ext.data.DataWriter-writeAllFields">writeAllFields</a></b> : Boolean<div class="mdesc"><div class="short">false by default.  Set true to have DataWriter return ALL fields of a modified
record -- not just those that changed....</div><div class="long"><tt>false</tt> by default.  Set <tt>true</tt> to have DataWriter return ALL fields of a modified
record -- not just those that changed.
<tt>false</tt> to have DataWriter only request modified fields from a record.</div></div></td><td class="msource">DataWriter</td></tr></tbody></table><a id="Ext.data.DataWriter-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-meta"></a><b><a href="source/DataWriter.html#prop-Ext.data.DataWriter-meta">meta</a></b> : Mixed<div class="mdesc">This DataWriter's configured metadata as passed to the constructor.</div></td><td class="msource">DataWriter</td></tr></tbody></table><a id="Ext.data.DataWriter-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-DataWriter"></a><b><a href="source/DataWriter.html#cls-Ext.data.DataWriter">DataWriter</a></b>(&nbsp;<code>Object&nbsp;meta</code>,&nbsp;<code>Object&nbsp;recordType</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new DataWriter</div><div class="long">Create a new DataWriter<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>meta</code> : Object<div class="sub-desc">Metadata configuration options (implementation-specific)</div></li><li><code>recordType</code> : Object<div class="sub-desc">Either an Array of field definition objects as specified
in <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>, or an <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> object created
using <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">Ext.data.Record.create</a>.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataWriter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-render"></a><b><a href="source/DataWriter.html#method-Ext.data.DataWriter-render">render</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Record[]&nbsp;rs</code>,&nbsp;<code>Object&nbsp;params</code>,&nbsp;<code>Object&nbsp;data</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">abstract method meant to be overridden by all DataWriter extensions.  It's the extension's job to apply the "data" to...</div><div class="long">abstract method meant to be overridden by all DataWriter extensions.  It's the extension's job to apply the "data" to the "params".
The data-object provided to render is populated with data according to the meta-info defined in the user's DataReader config,<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>rs</code> : Record[]<div class="sub-desc">Store recordset</div></li><li><code>params</code> : Object<div class="sub-desc">Http params to be sent to server.</div></li><li><code>data</code> : Object<div class="sub-desc">object populated according to DataReader meta-data.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataWriter</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataWriter-write"></a><b><a href="source/DataWriter.html#method-Ext.data.DataWriter-write">write</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;params</code>,&nbsp;<code>Record/Record[]&nbsp;rs</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Writes data in preparation for server-write action.  Simply proxies to DataWriter#update, DataWriter#create
DataWrite...</div><div class="long">Writes data in preparation for server-write action.  Simply proxies to DataWriter#update, DataWriter#create
DataWriter#destroy.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc">[CREATE|UPDATE|DESTROY]</div></li><li><code>params</code> : Object<div class="sub-desc">The params-hash to write-to</div></li><li><code>rs</code> : Record/Record[]<div class="sub-desc">The recordset write.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataWriter</td></tr></tbody></table><a id="Ext.data.DataWriter-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>