<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.StatusProxy-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.StatusProxy-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.StatusProxy-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.dd.StatusProxy-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.StatusProxy"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/StatusProxy.html#cls-Ext.dd.StatusProxy">Ext.dd.StatusProxy</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">StatusProxy.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/StatusProxy.html#cls-Ext.dd.StatusProxy">StatusProxy</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">A specialized drag proxy that supports a drop status icon, <a href="output/Ext.Layer.html" ext:cls="Ext.Layer">Ext.Layer</a> styles and auto-repair.  This is the
default drag proxy used by all Ext.dd components.</div><div class="hr"></div><a id="Ext.dd.StatusProxy-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-dropAllowed"></a><b><a href="source/StatusProxy.html#cfg-Ext.dd.StatusProxy-dropAllowed">dropAllowed</a></b> : String<div class="mdesc">The CSS class to apply to the status element when drop is allowed (defaults to "x-dd-drop-ok").</div></td><td class="msource">StatusProxy</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-dropNotAllowed"></a><b><a href="source/StatusProxy.html#cfg-Ext.dd.StatusProxy-dropNotAllowed">dropNotAllowed</a></b> : String<div class="mdesc">The CSS class to apply to the status element when drop is not allowed (defaults to "x-dd-drop-nodrop").</div></td><td class="msource">StatusProxy</td></tr></tbody></table><a id="Ext.dd.StatusProxy-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.dd.StatusProxy-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-StatusProxy"></a><b><a href="source/StatusProxy.html#cls-Ext.dd.StatusProxy">StatusProxy</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-getEl"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-getEl">getEl</a></b>()
    :
                                        Ext.Layer<div class="mdesc"><div class="short">Returns the underlying proxy Ext.Layer</div><div class="long">Returns the underlying proxy <a href="output/Ext.Layer.html" ext:cls="Ext.Layer">Ext.Layer</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Layer</code><div class="sub-desc">el</div></li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-getGhost"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-getGhost">getGhost</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Returns the ghost element</div><div class="long">Returns the ghost element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">el</div></li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-hide"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-hide">hide</a></b>(&nbsp;<code>Boolean&nbsp;clear</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Hides the proxy</div><div class="long">Hides the proxy<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>clear</code> : Boolean<div class="sub-desc">True to reset the status and clear the ghost contents, false to preserve them</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-repair"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-repair">repair</a></b>(&nbsp;<code>Array&nbsp;xy</code>,&nbsp;<code>Function&nbsp;callback</code>,&nbsp;<code>Object&nbsp;scope</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Causes the proxy to return to its position of origin via an animation.  Should be called after an&#13;
invalid drop opera...</div><div class="long">Causes the proxy to return to its position of origin via an animation.  Should be called after an
invalid drop operation by the item being dragged.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>xy</code> : Array<div class="sub-desc">The XY position of the element ([x, y])</div></li><li><code>callback</code> : Function<div class="sub-desc">The function to call after the repair is complete</div></li><li><code>scope</code> : Object<div class="sub-desc">The scope in which to execute the callback</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-reset"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-reset">reset</a></b>(&nbsp;<code>Boolean&nbsp;clearGhost</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Resets the status indicator to the default dropNotAllowed value</div><div class="long">Resets the status indicator to the default dropNotAllowed value<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>clearGhost</code> : Boolean<div class="sub-desc">True to also remove all content from the ghost, false to preserve it</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-setStatus"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-setStatus">setStatus</a></b>(&nbsp;<code>String&nbsp;cssClass</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Updates the proxy's visual element to indicate the status of whether or not drop is allowed&#13;
over the current target ...</div><div class="long">Updates the proxy's visual element to indicate the status of whether or not drop is allowed
over the current target element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>cssClass</code> : String<div class="sub-desc">The css class for the new drop status indicator image</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-show"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-show">show</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Displays this proxy</div><div class="long">Displays this proxy<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-stop"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-stop">stop</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Stops the repair animation if it's currently running</div><div class="long">Stops the repair animation if it's currently running<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-sync"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-sync">sync</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Force the Layer to sync its shadow and shim positions to the element</div><div class="long">Force the Layer to sync its shadow and shim positions to the element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.StatusProxy-update"></a><b><a href="source/StatusProxy.html#method-Ext.dd.StatusProxy-update">update</a></b>(&nbsp;<code>String/HTMLElement&nbsp;html</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Updates the contents of the ghost element</div><div class="long">Updates the contents of the ghost element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>html</code> : String/HTMLElement<div class="sub-desc">The html that will replace the current innerHTML of the ghost element, or a
DOM node to append as the child of the ghost element (in which case the innerHTML will be cleared first).</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">StatusProxy</td></tr></tbody></table><a id="Ext.dd.StatusProxy-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>