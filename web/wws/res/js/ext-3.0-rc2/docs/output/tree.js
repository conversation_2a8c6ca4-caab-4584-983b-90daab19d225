
		Docs.classData ={"id":"apidocs","iconCls":"icon-docs","text":"API Documentation","singleClickExpand":true,"children":[
                {"id":"pkg-Ext","text":"Ext","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"id":"pkg-Ext.chart","text":"chart","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.chart.Axis.html","text":"Axis","id":"Ext.chart.Axis","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.BarChart.html","text":"BarChart","id":"Ext.chart.BarChart","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.BarSeries.html","text":"BarSeries","id":"Ext.chart.BarSeries","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.CartesianChart.html","text":"CartesianChart","id":"Ext.chart.CartesianChart","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.CartesianSeries.html","text":"CartesianSeries","id":"Ext.chart.CartesianSeries","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.CategoryAxis.html","text":"CategoryAxis","id":"Ext.chart.CategoryAxis","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.Chart.html","text":"Chart","id":"Ext.chart.Chart","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.ColumnChart.html","text":"ColumnChart","id":"Ext.chart.ColumnChart","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.ColumnSeries.html","text":"ColumnSeries","id":"Ext.chart.ColumnSeries","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.LineChart.html","text":"LineChart","id":"Ext.chart.LineChart","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.LineSeries.html","text":"LineSeries","id":"Ext.chart.LineSeries","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.NumericAxis.html","text":"NumericAxis","id":"Ext.chart.NumericAxis","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.PieChart.html","text":"PieChart","id":"Ext.chart.PieChart","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.PieSeries.html","text":"PieSeries","id":"Ext.chart.PieSeries","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.Series.html","text":"Series","id":"Ext.chart.Series","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.chart.TimeAxis.html","text":"TimeAxis","id":"Ext.chart.TimeAxis","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.data","text":"data","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.data.Api.html","text":"Api","id":"Ext.data.Api","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.ArrayReader.html","text":"ArrayReader","id":"Ext.data.ArrayReader","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.ArrayStore.html","text":"ArrayStore","id":"Ext.data.ArrayStore","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.Connection.html","text":"Connection","id":"Ext.data.Connection","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.DataProxy.html","text":"DataProxy","id":"Ext.data.DataProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.DataReader.html","text":"DataReader","id":"Ext.data.DataReader","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.DataWriter.html","text":"DataWriter","id":"Ext.data.DataWriter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.DirectProxy.html","text":"DirectProxy","id":"Ext.data.DirectProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.DirectStore.html","text":"DirectStore","id":"Ext.data.DirectStore","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.Field.html","text":"Field","id":"Ext.data.Field","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.GroupingStore.html","text":"GroupingStore","id":"Ext.data.GroupingStore","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.HttpProxy.html","text":"HttpProxy","id":"Ext.data.HttpProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.JsonReader.html","text":"JsonReader","id":"Ext.data.JsonReader","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.JsonStore.html","text":"JsonStore","id":"Ext.data.JsonStore","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.JsonWriter.html","text":"JsonWriter","id":"Ext.data.JsonWriter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.MemoryProxy.html","text":"MemoryProxy","id":"Ext.data.MemoryProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.Node.html","text":"Node","id":"Ext.data.Node","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.Record.html","text":"Record","id":"Ext.data.Record","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.ScriptTagProxy.html","text":"ScriptTagProxy","id":"Ext.data.ScriptTagProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.SortTypes.html","text":"SortTypes","id":"Ext.data.SortTypes","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.Store.html","text":"Store","id":"Ext.data.Store","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.Tree.html","text":"Tree","id":"Ext.data.Tree","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.XmlReader.html","text":"XmlReader","id":"Ext.data.XmlReader","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.XmlStore.html","text":"XmlStore","id":"Ext.data.XmlStore","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.data.XmlWriter.html","text":"XmlWriter","id":"Ext.data.XmlWriter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.dd","text":"dd","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.dd.DD.html","text":"DD","id":"Ext.dd.DD","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DDProxy.html","text":"DDProxy","id":"Ext.dd.DDProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DDTarget.html","text":"DDTarget","id":"Ext.dd.DDTarget","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DragDrop.html","text":"DragDrop","id":"Ext.dd.DragDrop","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DragDropMgr.html","text":"DragDropMgr","id":"Ext.dd.DragDropMgr","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DragSource.html","text":"DragSource","id":"Ext.dd.DragSource","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DragTracker.html","text":"DragTracker","id":"Ext.dd.DragTracker","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DragZone.html","text":"DragZone","id":"Ext.dd.DragZone","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DropTarget.html","text":"DropTarget","id":"Ext.dd.DropTarget","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.DropZone.html","text":"DropZone","id":"Ext.dd.DropZone","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.PanelProxy.html","text":"PanelProxy","id":"Ext.dd.PanelProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.Registry.html","text":"Registry","id":"Ext.dd.Registry","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.ScrollManager.html","text":"ScrollManager","id":"Ext.dd.ScrollManager","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.dd.StatusProxy.html","text":"StatusProxy","id":"Ext.dd.StatusProxy","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.direct","text":"direct","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.direct.JsonProvider.html","text":"JsonProvider","id":"Ext.direct.JsonProvider","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.direct.PollingProvider.html","text":"PollingProvider","id":"Ext.direct.PollingProvider","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.direct.Provider.html","text":"Provider","id":"Ext.direct.Provider","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.direct.RemotingProvider.html","text":"RemotingProvider","id":"Ext.direct.RemotingProvider","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.form","text":"form","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.form.Action.html","text":"Action","id":"Ext.form.Action","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Action.DirectLoad.html","text":"Action.DirectLoad","id":"Ext.form.Action.DirectLoad","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Action.DirectSubmit.html","text":"Action.DirectSubmit","id":"Ext.form.Action.DirectSubmit","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Action.Load.html","text":"Action.Load","id":"Ext.form.Action.Load","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Action.Submit.html","text":"Action.Submit","id":"Ext.form.Action.Submit","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.BasicForm.html","text":"BasicForm","id":"Ext.form.BasicForm","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Checkbox.html","text":"Checkbox","id":"Ext.form.Checkbox","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.CheckboxGroup.html","text":"CheckboxGroup","id":"Ext.form.CheckboxGroup","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.ComboBox.html","text":"ComboBox","id":"Ext.form.ComboBox","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.DateField.html","text":"DateField","id":"Ext.form.DateField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.DisplayField.html","text":"DisplayField","id":"Ext.form.DisplayField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Field.html","text":"Field","id":"Ext.form.Field","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.FieldSet.html","text":"FieldSet","id":"Ext.form.FieldSet","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.FormPanel.html","text":"FormPanel","id":"Ext.form.FormPanel","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Hidden.html","text":"Hidden","id":"Ext.form.Hidden","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.HtmlEditor.html","text":"HtmlEditor","id":"Ext.form.HtmlEditor","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Label.html","text":"Label","id":"Ext.form.Label","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.NumberField.html","text":"NumberField","id":"Ext.form.NumberField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.Radio.html","text":"Radio","id":"Ext.form.Radio","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.RadioGroup.html","text":"RadioGroup","id":"Ext.form.RadioGroup","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.TextArea.html","text":"TextArea","id":"Ext.form.TextArea","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.TextField.html","text":"TextField","id":"Ext.form.TextField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.TimeField.html","text":"TimeField","id":"Ext.form.TimeField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.TriggerField.html","text":"TriggerField","id":"Ext.form.TriggerField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.TwinTriggerField.html","text":"TwinTriggerField","id":"Ext.form.TwinTriggerField","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.form.VTypes.html","text":"VTypes","id":"Ext.form.VTypes","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.grid","text":"grid","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.grid.AbstractSelectionModel.html","text":"AbstractSelectionModel","id":"Ext.grid.AbstractSelectionModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.BooleanColumn.html","text":"BooleanColumn","id":"Ext.grid.BooleanColumn","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.CellSelectionModel.html","text":"CellSelectionModel","id":"Ext.grid.CellSelectionModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.CheckboxSelectionModel.html","text":"CheckboxSelectionModel","id":"Ext.grid.CheckboxSelectionModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.Column.html","text":"Column","id":"Ext.grid.Column","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.ColumnModel.html","text":"ColumnModel","id":"Ext.grid.ColumnModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.DateColumn.html","text":"DateColumn","id":"Ext.grid.DateColumn","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.EditorGridPanel.html","text":"EditorGridPanel","id":"Ext.grid.EditorGridPanel","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.GridDragZone.html","text":"GridDragZone","id":"Ext.grid.GridDragZone","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.GridPanel.html","text":"GridPanel","id":"Ext.grid.GridPanel","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.GridView.html","text":"GridView","id":"Ext.grid.GridView","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.GroupingView.html","text":"GroupingView","id":"Ext.grid.GroupingView","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.NumberColumn.html","text":"NumberColumn","id":"Ext.grid.NumberColumn","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.PropertyColumnModel.html","text":"PropertyColumnModel","id":"Ext.grid.PropertyColumnModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.PropertyGrid.html","text":"PropertyGrid","id":"Ext.grid.PropertyGrid","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.PropertyRecord.html","text":"PropertyRecord","id":"Ext.grid.PropertyRecord","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.PropertyStore.html","text":"PropertyStore","id":"Ext.grid.PropertyStore","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.RowNumberer.html","text":"RowNumberer","id":"Ext.grid.RowNumberer","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.RowSelectionModel.html","text":"RowSelectionModel","id":"Ext.grid.RowSelectionModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.grid.TemplateColumn.html","text":"TemplateColumn","id":"Ext.grid.TemplateColumn","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.layout","text":"layout","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.layout.AbsoluteLayout.html","text":"AbsoluteLayout","id":"Ext.layout.AbsoluteLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.AccordionLayout.html","text":"AccordionLayout","id":"Ext.layout.AccordionLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.AnchorLayout.html","text":"AnchorLayout","id":"Ext.layout.AnchorLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.BorderLayout.html","text":"BorderLayout","id":"Ext.layout.BorderLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.BorderLayout.Region.html","text":"BorderLayout.Region","id":"Ext.layout.BorderLayout.Region","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.BorderLayout.SplitRegion.html","text":"BorderLayout.SplitRegion","id":"Ext.layout.BorderLayout.SplitRegion","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.BoxLayout.html","text":"BoxLayout","id":"Ext.layout.BoxLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.CardLayout.html","text":"CardLayout","id":"Ext.layout.CardLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.ColumnLayout.html","text":"ColumnLayout","id":"Ext.layout.ColumnLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.ContainerLayout.html","text":"ContainerLayout","id":"Ext.layout.ContainerLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.FitLayout.html","text":"FitLayout","id":"Ext.layout.FitLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.FormLayout.html","text":"FormLayout","id":"Ext.layout.FormLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.HBoxLayout.html","text":"HBoxLayout","id":"Ext.layout.HBoxLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.MenuLayout.html","text":"MenuLayout","id":"Ext.layout.MenuLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.TableLayout.html","text":"TableLayout","id":"Ext.layout.TableLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.ToolbarLayout.html","text":"ToolbarLayout","id":"Ext.layout.ToolbarLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.layout.VBoxLayout.html","text":"VBoxLayout","id":"Ext.layout.VBoxLayout","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.menu","text":"menu","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.menu.BaseItem.html","text":"BaseItem","id":"Ext.menu.BaseItem","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.CheckItem.html","text":"CheckItem","id":"Ext.menu.CheckItem","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.ColorMenu.html","text":"ColorMenu","id":"Ext.menu.ColorMenu","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.DateMenu.html","text":"DateMenu","id":"Ext.menu.DateMenu","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.Item.html","text":"Item","id":"Ext.menu.Item","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.Menu.html","text":"Menu","id":"Ext.menu.Menu","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.MenuMgr.html","text":"MenuMgr","id":"Ext.menu.MenuMgr","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.Separator.html","text":"Separator","id":"Ext.menu.Separator","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.menu.TextItem.html","text":"TextItem","id":"Ext.menu.TextItem","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.state","text":"state","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.state.CookieProvider.html","text":"CookieProvider","id":"Ext.state.CookieProvider","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.state.Manager.html","text":"Manager","id":"Ext.state.Manager","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.state.Provider.html","text":"Provider","id":"Ext.state.Provider","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.tree","text":"tree","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.tree.AsyncTreeNode.html","text":"AsyncTreeNode","id":"Ext.tree.AsyncTreeNode","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.DefaultSelectionModel.html","text":"DefaultSelectionModel","id":"Ext.tree.DefaultSelectionModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.MultiSelectionModel.html","text":"MultiSelectionModel","id":"Ext.tree.MultiSelectionModel","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.RootTreeNodeUI.html","text":"RootTreeNodeUI","id":"Ext.tree.RootTreeNodeUI","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeDragZone.html","text":"TreeDragZone","id":"Ext.tree.TreeDragZone","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeDropZone.html","text":"TreeDropZone","id":"Ext.tree.TreeDropZone","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeEditor.html","text":"TreeEditor","id":"Ext.tree.TreeEditor","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeFilter.html","text":"TreeFilter","id":"Ext.tree.TreeFilter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeLoader.html","text":"TreeLoader","id":"Ext.tree.TreeLoader","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeNode.html","text":"TreeNode","id":"Ext.tree.TreeNode","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeNodeUI.html","text":"TreeNodeUI","id":"Ext.tree.TreeNodeUI","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreePanel.html","text":"TreePanel","id":"Ext.tree.TreePanel","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.tree.TreeSorter.html","text":"TreeSorter","id":"Ext.tree.TreeSorter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"id":"pkg-Ext.util","text":"util","iconCls":"icon-pkg","cls":"package","singleClickExpand":true, children:[
                {"href":"output/Ext.util.CSS.html","text":"CSS","id":"Ext.util.CSS","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.ClickRepeater.html","text":"ClickRepeater","id":"Ext.util.ClickRepeater","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.DelayedTask.html","text":"DelayedTask","id":"Ext.util.DelayedTask","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.Format.html","text":"Format","id":"Ext.util.Format","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.JSON.html","text":"JSON","id":"Ext.util.JSON","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.MixedCollection.html","text":"MixedCollection","id":"Ext.util.MixedCollection","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.Observable.html","text":"Observable","id":"Ext.util.Observable","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.TaskRunner.html","text":"TaskRunner","id":"Ext.util.TaskRunner","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.util.TextMetrics.html","text":"TextMetrics","id":"Ext.util.TextMetrics","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				]}
				,
                {"href":"output/Ext.Action.html","text":"Action","id":"Ext.Action","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Ajax.html","text":"Ajax","id":"Ext.Ajax","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.BoxComponent.html","text":"BoxComponent","id":"Ext.BoxComponent","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Button.html","text":"Button","id":"Ext.Button","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ButtonGroup.html","text":"ButtonGroup","id":"Ext.ButtonGroup","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ColorPalette.html","text":"ColorPalette","id":"Ext.ColorPalette","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Component.html","text":"Component","id":"Ext.Component","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ComponentMgr.html","text":"ComponentMgr","id":"Ext.ComponentMgr","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.CompositeElement.html","text":"CompositeElement","id":"Ext.CompositeElement","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.CompositeElementLite.html","text":"CompositeElementLite","id":"Ext.CompositeElementLite","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Container.html","text":"Container","id":"Ext.Container","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.CycleButton.html","text":"CycleButton","id":"Ext.CycleButton","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.DataView.html","text":"DataView","id":"Ext.DataView","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.DatePicker.html","text":"DatePicker","id":"Ext.DatePicker","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Direct.html","text":"Direct","id":"Ext.Direct","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.DomHelper.html","text":"DomHelper","id":"Ext.DomHelper","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.DomQuery.html","text":"DomQuery","id":"Ext.DomQuery","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Editor.html","text":"Editor","id":"Ext.Editor","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Element.html","text":"Element","id":"Ext.Element","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Error.html","text":"Error","id":"Ext.Error","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.EventManager.html","text":"EventManager","id":"Ext.EventManager","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.EventObject.html","text":"EventObject","id":"Ext.EventObject","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.FlashComponent.html","text":"FlashComponent","id":"Ext.FlashComponent","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.FlashProxy.html","text":"FlashProxy","id":"Ext.FlashProxy","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Fx.html","text":"Fx","id":"Ext.Fx","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.History.html","text":"History","id":"Ext.History","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.KeyMap.html","text":"KeyMap","id":"Ext.KeyMap","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.KeyNav.html","text":"KeyNav","id":"Ext.KeyNav","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Layer.html","text":"Layer","id":"Ext.Layer","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ListView.html","text":"ListView","id":"Ext.ListView","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ListView.ColumnResizer.html","text":"ListView.ColumnResizer","id":"Ext.ListView.ColumnResizer","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ListView.Sorter.html","text":"ListView.Sorter","id":"Ext.ListView.Sorter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.LoadMask.html","text":"LoadMask","id":"Ext.LoadMask","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.MessageBox.html","text":"MessageBox","id":"Ext.MessageBox","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.PagingToolbar.html","text":"PagingToolbar","id":"Ext.PagingToolbar","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Panel.html","text":"Panel","id":"Ext.Panel","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ProgressBar.html","text":"ProgressBar","id":"Ext.ProgressBar","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.QuickTip.html","text":"QuickTip","id":"Ext.QuickTip","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.QuickTips.html","text":"QuickTips","id":"Ext.QuickTips","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Resizable.html","text":"Resizable","id":"Ext.Resizable","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Shadow.html","text":"Shadow","id":"Ext.Shadow","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Slider.html","text":"Slider","id":"Ext.Slider","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Spacer.html","text":"Spacer","id":"Ext.Spacer","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.SplitBar.html","text":"SplitBar","id":"Ext.SplitBar","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.SplitBar.AbsoluteLayoutAdapter.html","text":"SplitBar.AbsoluteLayoutAdapter","id":"Ext.SplitBar.AbsoluteLayoutAdapter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.SplitBar.BasicLayoutAdapter.html","text":"SplitBar.BasicLayoutAdapter","id":"Ext.SplitBar.BasicLayoutAdapter","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.SplitButton.html","text":"SplitButton","id":"Ext.SplitButton","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.StoreMgr.html","text":"StoreMgr","id":"Ext.StoreMgr","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.TabPanel.html","text":"TabPanel","id":"Ext.TabPanel","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.TaskMgr.html","text":"TaskMgr","id":"Ext.TaskMgr","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Template.html","text":"Template","id":"Ext.Template","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Tip.html","text":"Tip","id":"Ext.Tip","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.ToolTip.html","text":"ToolTip","id":"Ext.ToolTip","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Toolbar.html","text":"Toolbar","id":"Ext.Toolbar","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Toolbar.Fill.html","text":"Toolbar.Fill","id":"Ext.Toolbar.Fill","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Toolbar.Item.html","text":"Toolbar.Item","id":"Ext.Toolbar.Item","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Toolbar.Separator.html","text":"Toolbar.Separator","id":"Ext.Toolbar.Separator","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Toolbar.Spacer.html","text":"Toolbar.Spacer","id":"Ext.Toolbar.Spacer","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Toolbar.TextItem.html","text":"Toolbar.TextItem","id":"Ext.Toolbar.TextItem","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Updater.html","text":"Updater","id":"Ext.Updater","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Updater.BasicRenderer.html","text":"Updater.BasicRenderer","id":"Ext.Updater.BasicRenderer","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Updater.defaults.html","text":"Updater.defaults","id":"Ext.Updater.defaults","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Viewport.html","text":"Viewport","id":"Ext.Viewport","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.Window.html","text":"Window","id":"Ext.Window","isClass":true,"iconCls":"icon-cmp","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.WindowGroup.html","text":"WindowGroup","id":"Ext.WindowGroup","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.WindowMgr.html","text":"WindowMgr","id":"Ext.WindowMgr","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.XTemplate.html","text":"XTemplate","id":"Ext.XTemplate","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]}
				,
                {"href":"output/Array.html","text":"Array","id":"Array","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Date.html","text":"Date","id":"Date","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Ext.html","text":"Ext","id":"Ext","isClass":true,"iconCls":"icon-static","cls":"cls","leaf":true}
				,
                {"href":"output/Function.html","text":"Function","id":"Function","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/Number.html","text":"Number","id":"Number","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				,
                {"href":"output/String.html","text":"String","id":"String","isClass":true,"iconCls":"icon-cls","cls":"cls","leaf":true}
				]};
        Docs.icons = {
        
			"Ext.chart.Axis":"icon-cls"
			,
			"Ext.chart.BarChart":"icon-cmp"
			,
			"Ext.chart.BarSeries":"icon-cls"
			,
			"Ext.chart.CartesianChart":"icon-cmp"
			,
			"Ext.chart.CartesianSeries":"icon-cls"
			,
			"Ext.chart.CategoryAxis":"icon-cls"
			,
			"Ext.chart.Chart":"icon-cmp"
			,
			"Ext.chart.ColumnChart":"icon-cmp"
			,
			"Ext.chart.ColumnSeries":"icon-cls"
			,
			"Ext.chart.LineChart":"icon-cmp"
			,
			"Ext.chart.LineSeries":"icon-cls"
			,
			"Ext.chart.NumericAxis":"icon-cls"
			,
			"Ext.chart.PieChart":"icon-cmp"
			,
			"Ext.chart.PieSeries":"icon-cls"
			,
			"Ext.chart.Series":"icon-cls"
			,
			"Ext.chart.TimeAxis":"icon-cls"
			,
			"Ext.data.Api":"icon-static"
			,
			"Ext.data.ArrayReader":"icon-cls"
			,
			"Ext.data.ArrayStore":"icon-cls"
			,
			"Ext.data.Connection":"icon-cls"
			,
			"Ext.data.DataProxy":"icon-cls"
			,
			"Ext.data.DataReader":"icon-cls"
			,
			"Ext.data.DataWriter":"icon-cls"
			,
			"Ext.data.DirectProxy":"icon-cls"
			,
			"Ext.data.DirectStore":"icon-cls"
			,
			"Ext.data.Field":"icon-cls"
			,
			"Ext.data.GroupingStore":"icon-cls"
			,
			"Ext.data.HttpProxy":"icon-cls"
			,
			"Ext.data.JsonReader":"icon-cls"
			,
			"Ext.data.JsonStore":"icon-cls"
			,
			"Ext.data.JsonWriter":"icon-cls"
			,
			"Ext.data.MemoryProxy":"icon-cls"
			,
			"Ext.data.Node":"icon-cls"
			,
			"Ext.data.Record":"icon-cls"
			,
			"Ext.data.ScriptTagProxy":"icon-cls"
			,
			"Ext.data.SortTypes":"icon-static"
			,
			"Ext.data.Store":"icon-cls"
			,
			"Ext.data.Tree":"icon-cls"
			,
			"Ext.data.XmlReader":"icon-cls"
			,
			"Ext.data.XmlStore":"icon-cls"
			,
			"Ext.data.XmlWriter":"icon-cls"
			,
			"Ext.dd.DD":"icon-cls"
			,
			"Ext.dd.DDProxy":"icon-cls"
			,
			"Ext.dd.DDTarget":"icon-cls"
			,
			"Ext.dd.DragDrop":"icon-cls"
			,
			"Ext.dd.DragDropMgr":"icon-static"
			,
			"Ext.dd.DragSource":"icon-cls"
			,
			"Ext.dd.DragTracker":"icon-cls"
			,
			"Ext.dd.DragZone":"icon-cls"
			,
			"Ext.dd.DropTarget":"icon-cls"
			,
			"Ext.dd.DropZone":"icon-cls"
			,
			"Ext.dd.PanelProxy":"icon-cls"
			,
			"Ext.dd.Registry":"icon-static"
			,
			"Ext.dd.ScrollManager":"icon-static"
			,
			"Ext.dd.StatusProxy":"icon-cls"
			,
			"Ext.direct.JsonProvider":"icon-cls"
			,
			"Ext.direct.PollingProvider":"icon-cls"
			,
			"Ext.direct.Provider":"icon-cls"
			,
			"Ext.direct.RemotingProvider":"icon-cls"
			,
			"Ext.form.Action":"icon-cls"
			,
			"Ext.form.Action.DirectLoad":"icon-cls"
			,
			"Ext.form.Action.DirectSubmit":"icon-cls"
			,
			"Ext.form.Action.Load":"icon-cls"
			,
			"Ext.form.Action.Submit":"icon-cls"
			,
			"Ext.form.BasicForm":"icon-cls"
			,
			"Ext.form.Checkbox":"icon-cmp"
			,
			"Ext.form.CheckboxGroup":"icon-cmp"
			,
			"Ext.form.ComboBox":"icon-cmp"
			,
			"Ext.form.DateField":"icon-cmp"
			,
			"Ext.form.DisplayField":"icon-cmp"
			,
			"Ext.form.Field":"icon-cmp"
			,
			"Ext.form.FieldSet":"icon-cmp"
			,
			"Ext.form.FormPanel":"icon-cmp"
			,
			"Ext.form.Hidden":"icon-cmp"
			,
			"Ext.form.HtmlEditor":"icon-cmp"
			,
			"Ext.form.Label":"icon-cmp"
			,
			"Ext.form.NumberField":"icon-cmp"
			,
			"Ext.form.Radio":"icon-cmp"
			,
			"Ext.form.RadioGroup":"icon-cmp"
			,
			"Ext.form.TextArea":"icon-cmp"
			,
			"Ext.form.TextField":"icon-cmp"
			,
			"Ext.form.TimeField":"icon-cmp"
			,
			"Ext.form.TriggerField":"icon-cmp"
			,
			"Ext.form.TwinTriggerField":"icon-cmp"
			,
			"Ext.form.VTypes":"icon-static"
			,
			"Ext.grid.AbstractSelectionModel":"icon-cls"
			,
			"Ext.grid.BooleanColumn":"icon-cls"
			,
			"Ext.grid.CellSelectionModel":"icon-cls"
			,
			"Ext.grid.CheckboxSelectionModel":"icon-cls"
			,
			"Ext.grid.Column":"icon-cls"
			,
			"Ext.grid.ColumnModel":"icon-cls"
			,
			"Ext.grid.DateColumn":"icon-cls"
			,
			"Ext.grid.EditorGridPanel":"icon-cmp"
			,
			"Ext.grid.GridDragZone":"icon-cls"
			,
			"Ext.grid.GridPanel":"icon-cmp"
			,
			"Ext.grid.GridView":"icon-cls"
			,
			"Ext.grid.GroupingView":"icon-cls"
			,
			"Ext.grid.NumberColumn":"icon-cls"
			,
			"Ext.grid.PropertyColumnModel":"icon-cls"
			,
			"Ext.grid.PropertyGrid":"icon-cmp"
			,
			"Ext.grid.PropertyRecord":"icon-cls"
			,
			"Ext.grid.PropertyStore":"icon-cls"
			,
			"Ext.grid.RowNumberer":"icon-cls"
			,
			"Ext.grid.RowSelectionModel":"icon-cls"
			,
			"Ext.grid.TemplateColumn":"icon-cls"
			,
			"Ext.layout.AbsoluteLayout":"icon-cls"
			,
			"Ext.layout.AccordionLayout":"icon-cls"
			,
			"Ext.layout.AnchorLayout":"icon-cls"
			,
			"Ext.layout.BorderLayout":"icon-cls"
			,
			"Ext.layout.BorderLayout.Region":"icon-cls"
			,
			"Ext.layout.BorderLayout.SplitRegion":"icon-cls"
			,
			"Ext.layout.BoxLayout":"icon-cls"
			,
			"Ext.layout.CardLayout":"icon-cls"
			,
			"Ext.layout.ColumnLayout":"icon-cls"
			,
			"Ext.layout.ContainerLayout":"icon-cls"
			,
			"Ext.layout.FitLayout":"icon-cls"
			,
			"Ext.layout.FormLayout":"icon-cls"
			,
			"Ext.layout.HBoxLayout":"icon-cls"
			,
			"Ext.layout.MenuLayout":"icon-cls"
			,
			"Ext.layout.TableLayout":"icon-cls"
			,
			"Ext.layout.ToolbarLayout":"icon-cls"
			,
			"Ext.layout.VBoxLayout":"icon-cls"
			,
			"Ext.menu.BaseItem":"icon-cmp"
			,
			"Ext.menu.CheckItem":"icon-cmp"
			,
			"Ext.menu.ColorMenu":"icon-cmp"
			,
			"Ext.menu.DateMenu":"icon-cmp"
			,
			"Ext.menu.Item":"icon-cmp"
			,
			"Ext.menu.Menu":"icon-cmp"
			,
			"Ext.menu.MenuMgr":"icon-static"
			,
			"Ext.menu.Separator":"icon-cmp"
			,
			"Ext.menu.TextItem":"icon-cmp"
			,
			"Ext.state.CookieProvider":"icon-cls"
			,
			"Ext.state.Manager":"icon-static"
			,
			"Ext.state.Provider":"icon-cls"
			,
			"Ext.tree.AsyncTreeNode":"icon-cls"
			,
			"Ext.tree.DefaultSelectionModel":"icon-cls"
			,
			"Ext.tree.MultiSelectionModel":"icon-cls"
			,
			"Ext.tree.RootTreeNodeUI":"icon-cls"
			,
			"Ext.tree.TreeDragZone":"icon-cls"
			,
			"Ext.tree.TreeDropZone":"icon-cls"
			,
			"Ext.tree.TreeEditor":"icon-cmp"
			,
			"Ext.tree.TreeFilter":"icon-cls"
			,
			"Ext.tree.TreeLoader":"icon-cls"
			,
			"Ext.tree.TreeNode":"icon-cls"
			,
			"Ext.tree.TreeNodeUI":"icon-cls"
			,
			"Ext.tree.TreePanel":"icon-cmp"
			,
			"Ext.tree.TreeSorter":"icon-cls"
			,
			"Ext.util.CSS":"icon-static"
			,
			"Ext.util.ClickRepeater":"icon-cls"
			,
			"Ext.util.DelayedTask":"icon-cls"
			,
			"Ext.util.Format":"icon-static"
			,
			"Ext.util.JSON":"icon-static"
			,
			"Ext.util.MixedCollection":"icon-cls"
			,
			"Ext.util.Observable":"icon-cls"
			,
			"Ext.util.TaskRunner":"icon-cls"
			,
			"Ext.util.TextMetrics":"icon-static"
			,
			"Ext.Action":"icon-cls"
			,
			"Ext.Ajax":"icon-static"
			,
			"Ext.BoxComponent":"icon-cmp"
			,
			"Ext.Button":"icon-cmp"
			,
			"Ext.ButtonGroup":"icon-cmp"
			,
			"Ext.ColorPalette":"icon-cmp"
			,
			"Ext.Component":"icon-cls"
			,
			"Ext.ComponentMgr":"icon-static"
			,
			"Ext.CompositeElement":"icon-cls"
			,
			"Ext.CompositeElementLite":"icon-cls"
			,
			"Ext.Container":"icon-cmp"
			,
			"Ext.CycleButton":"icon-cmp"
			,
			"Ext.DataView":"icon-cmp"
			,
			"Ext.DatePicker":"icon-cmp"
			,
			"Ext.Direct":"icon-static"
			,
			"Ext.DomHelper":"icon-static"
			,
			"Ext.DomQuery":"icon-static"
			,
			"Ext.Editor":"icon-cmp"
			,
			"Ext.Element":"icon-cls"
			,
			"Ext.Error":"icon-cls"
			,
			"Ext.EventManager":"icon-static"
			,
			"Ext.EventObject":"icon-static"
			,
			"Ext.FlashComponent":"icon-cmp"
			,
			"Ext.FlashProxy":"icon-static"
			,
			"Ext.Fx":"icon-cls"
			,
			"Ext.History":"icon-static"
			,
			"Ext.KeyMap":"icon-cls"
			,
			"Ext.KeyNav":"icon-cls"
			,
			"Ext.Layer":"icon-cls"
			,
			"Ext.ListView":"icon-cmp"
			,
			"Ext.ListView.ColumnResizer":"icon-cls"
			,
			"Ext.ListView.Sorter":"icon-cls"
			,
			"Ext.LoadMask":"icon-cls"
			,
			"Ext.MessageBox":"icon-static"
			,
			"Ext.PagingToolbar":"icon-cmp"
			,
			"Ext.Panel":"icon-cmp"
			,
			"Ext.ProgressBar":"icon-cmp"
			,
			"Ext.QuickTip":"icon-cmp"
			,
			"Ext.QuickTips":"icon-static"
			,
			"Ext.Resizable":"icon-cls"
			,
			"Ext.Shadow":"icon-cls"
			,
			"Ext.Slider":"icon-cmp"
			,
			"Ext.Spacer":"icon-cmp"
			,
			"Ext.SplitBar":"icon-cls"
			,
			"Ext.SplitBar.AbsoluteLayoutAdapter":"icon-cls"
			,
			"Ext.SplitBar.BasicLayoutAdapter":"icon-cls"
			,
			"Ext.SplitButton":"icon-cmp"
			,
			"Ext.StoreMgr":"icon-static"
			,
			"Ext.TabPanel":"icon-cmp"
			,
			"Ext.TaskMgr":"icon-static"
			,
			"Ext.Template":"icon-cls"
			,
			"Ext.Tip":"icon-cmp"
			,
			"Ext.ToolTip":"icon-cmp"
			,
			"Ext.Toolbar":"icon-cmp"
			,
			"Ext.Toolbar.Fill":"icon-cmp"
			,
			"Ext.Toolbar.Item":"icon-cmp"
			,
			"Ext.Toolbar.Separator":"icon-cmp"
			,
			"Ext.Toolbar.Spacer":"icon-cmp"
			,
			"Ext.Toolbar.TextItem":"icon-cmp"
			,
			"Ext.Updater":"icon-cls"
			,
			"Ext.Updater.BasicRenderer":"icon-cls"
			,
			"Ext.Updater.defaults":"icon-cls"
			,
			"Ext.Viewport":"icon-cmp"
			,
			"Ext.Window":"icon-cmp"
			,
			"Ext.WindowGroup":"icon-cls"
			,
			"Ext.WindowMgr":"icon-static"
			,
			"Ext.XTemplate":"icon-cls"
			,
			"Array":"icon-cls"
			,
			"Date":"icon-cls"
			,
			"Ext":"icon-static"
			,
			"Function":"icon-cls"
			,
			"Number":"icon-cls"
			,
			"String":"icon-cls"
			};
    