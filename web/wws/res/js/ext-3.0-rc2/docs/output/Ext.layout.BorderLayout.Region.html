<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.BorderLayout.Region-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.BorderLayout.Region-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.BorderLayout.Region-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.BorderLayout.Region-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.BorderLayout.Region"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/BorderLayout.html#cls-Ext.layout.BorderLayout.Region">Ext.layout.BorderLayout.Region</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">BorderLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/BorderLayout.html#cls-Ext.layout.BorderLayout.Region">BorderLayout.Region</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.layout.BorderLayout.SplitRegion.html" ext:cls="Ext.layout.BorderLayout.SplitRegion">BorderLayout.SplitRegion</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>This is a region of a <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a> that acts as a subcontainer
within the layout.  Each region has its own <a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">layout</a> that is
independent of other regions and the containing BorderLayout, and can be any of the
<a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout">valid Ext layout types</a>.</p>
<p>Region size is managed automatically and cannot be changed by the user -- for
<a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-split" ext:member="split" ext:cls="Ext.layout.BorderLayout.Region">resizable regions</a>, see <a href="output/Ext.layout.BorderLayout.SplitRegion.html" ext:cls="Ext.layout.BorderLayout.SplitRegion">Ext.layout.BorderLayout.SplitRegion</a>.</p></div><div class="hr"></div><a id="Ext.layout.BorderLayout.Region-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-animFloat"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-animFloat">animFloat</a></b> : Boolean<div class="mdesc"><div class="short">When a collapsed region's bar is clicked, the region's panel will be displayed as a floated
panel that will close aga...</div><div class="long">When a collapsed region's bar is clicked, the region's panel will be displayed as a floated
panel that will close again once the user mouses out of that panel (or clicks out if
<tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-autoHide" ext:member="autoHide" ext:cls="Ext.layout.BorderLayout.Region">autoHide</a> = false</tt>).  Setting <tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-animFloat" ext:member="animFloat" ext:cls="Ext.layout.BorderLayout.Region">animFloat</a> = false</tt> will
prevent the open and close of these floated panels from being animated (defaults to <tt>true</tt>).</div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-autoHide"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-autoHide">autoHide</a></b> : Boolean<div class="mdesc"><div class="short">When a collapsed region's bar is clicked, the region's panel will be displayed as a floated
panel.  If autoHide = tru...</div><div class="long">When a collapsed region's bar is clicked, the region's panel will be displayed as a floated
panel.  If <tt>autoHide = true</tt>, the panel will automatically hide after the user mouses
out of the panel.  If <tt>autoHide = false</tt>, the panel will continue to display until the
user clicks outside of the panel (defaults to <tt>true</tt>).</div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-cmargins"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-cmargins">cmargins</a></b> : Object<div class="mdesc"><div class="short">An object containing margins to apply to the region when in the collapsed state in the
format:{
    top: (top margin)...</div><div class="long">An object containing margins to apply to the region when in the collapsed state in the
format:<pre><code>{
    top: (top margin),
    right: (right margin),
    bottom: (bottom margin),
    left: (left margin)
}</code></pre>
<p>May also be a string containing space-separated, numeric margin values. The order of the
sides associated with each value matches the way CSS processes margin values.</p>
<p><ul>
<li>If there is only one value, it applies to all sides.</li>
<li>If there are two values, the top and bottom borders are set to the first value and the
right and left are set to the second.</li>
<li>If there are three values, the top is set to the first value, the left and right are set
to the second, and the bottom is set to the third.</li>
<li>If there are four values, they apply to the top, right, bottom, and left, respectively.</li>
</ul></p></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-collapseMode"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-collapseMode">collapseMode</a></b> : String<div class="mdesc"><div class="short">collapseMode supports two configuration values:&lt;div class="mdetail-params"&gt;
undefined (default)&lt;div class="sub-desc"&gt;...</div><div class="long"><tt>collapseMode</tt> supports two configuration values:<div class="mdetail-params"><ul>
<li><b><tt>undefined</tt></b> (default)<div class="sub-desc">By default, <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-collapsible" ext:member="collapsible" ext:cls="Ext.layout.BorderLayout.Region">collapsible</a>
regions are collapsed by clicking the expand/collapse tool button that renders into the region's
title bar.</div></li>
<li><b><tt>'mini'</tt></b><div class="sub-desc">Optionally, when <tt>collapseMode</tt> is set to
<tt>'mini'</tt> the region's split bar will also display a small collapse button in the center of
the bar. In <tt>'mini'</tt> mode the region will collapse to a thinner bar than in normal mode.
</div></li>
</ul></div></p>
<p><b>Note</b>: if a collapsible region does not have a title bar, then set <tt>collapseMode =
'mini'</tt> and <tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-split" ext:member="split" ext:cls="Ext.layout.BorderLayout.Region">split</a> = true</tt> in order for the region to be <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-collapsible" ext:member="collapsible" ext:cls="Ext.layout.BorderLayout.Region">collapsible</a>
by the user as the expand/collapse tool button (that would go in the title bar) will not be rendered.</p>
<p>See also <tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-cmargins" ext:member="cmargins" ext:cls="Ext.layout.BorderLayout.Region">cmargins</a></tt>.</p></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-collapsible"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-collapsible">collapsible</a></b> : Boolean<div class="mdesc"><div class="short">true to allow the user to collapse this region (defaults to false).  If
true, an expand/collapse tool button will aut...</div><div class="long"><p><tt>true</tt> to allow the user to collapse this region (defaults to <tt>false</tt>).  If
<tt>true</tt>, an expand/collapse tool button will automatically be rendered into the title
bar of the region, otherwise the button will not be shown.</p>
<p><b>Note</b>: that a title bar is required to display the collapse/expand toggle button -- if
no <tt>title</tt> is specified for the region's panel, the region will only be collapsible if
<tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-collapseMode" ext:member="collapseMode" ext:cls="Ext.layout.BorderLayout.Region">collapseMode</a> = 'mini'</tt> and <tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-split" ext:member="split" ext:cls="Ext.layout.BorderLayout.Region">split</a> = true</tt>.</div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-floatable"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-floatable">floatable</a></b> : Boolean<div class="mdesc"><div class="short">true to allow clicking a collapsed region's bar to display the region's panel floated
above the layout, false to forc...</div><div class="long"><tt>true</tt> to allow clicking a collapsed region's bar to display the region's panel floated
above the layout, <tt>false</tt> to force the user to fully expand a collapsed region by
clicking the expand button to see it again (defaults to <tt>true</tt>).</div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-margins"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-margins">margins</a></b> : Object<div class="mdesc"><div class="short">An object containing margins to apply to the region when in the expanded state in the
format:{
    top: (top margin),...</div><div class="long">An object containing margins to apply to the region when in the expanded state in the
format:<pre><code>{
    top: (top margin),
    right: (right margin),
    bottom: (bottom margin),
    left: (left margin)
}</code></pre>
<p>May also be a string containing space-separated, numeric margin values. The order of the
sides associated with each value matches the way CSS processes margin values:</p>
<p><ul>
<li>If there is only one value, it applies to all sides.</li>
<li>If there are two values, the top and bottom borders are set to the first value and the
right and left are set to the second.</li>
<li>If there are three values, the top is set to the first value, the left and right are set
to the second, and the bottom is set to the third.</li>
<li>If there are four values, they apply to the top, right, bottom, and left, respectively.</li>
</ul></p>
<p>Defaults to:</p><pre><code>{top:0, right:0, bottom:0, left:0}</code></pre></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-minHeight"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-minHeight">minHeight</a></b> : Number<div class="mdesc"><div class="short">The minimum allowable height in pixels for this region (defaults to 50)
maxHeight may also be specified.
Note: settin...</div><div class="long">The minimum allowable height in pixels for this region (defaults to <tt>50</tt>)
<tt>maxHeight</tt> may also be specified.</p><br>
<p><b>Note</b>: setting the <tt><a href="output/Ext.SplitBar.html#Ext.SplitBar-minSize" ext:member="minSize" ext:cls="Ext.SplitBar">minSize</a></tt> / 
<tt><a href="output/Ext.SplitBar.html#Ext.SplitBar-maxSize" ext:member="maxSize" ext:cls="Ext.SplitBar">maxSize</a></tt> supersedes any specified 
<tt>minHeight</tt> / <tt>maxHeight</tt>.</p></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-minWidth"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-minWidth">minWidth</a></b> : Number<div class="mdesc"><div class="short">The minimum allowable width in pixels for this region (defaults to 50).
maxWidth may also be specified.
Note: setting...</div><div class="long"><p>The minimum allowable width in pixels for this region (defaults to <tt>50</tt>).
<tt>maxWidth</tt> may also be specified.</p><br>
<p><b>Note</b>: setting the <tt><a href="output/Ext.SplitBar.html#Ext.SplitBar-minSize" ext:member="minSize" ext:cls="Ext.SplitBar">minSize</a></tt> / 
<tt><a href="output/Ext.SplitBar.html#Ext.SplitBar-maxSize" ext:member="maxSize" ext:cls="Ext.SplitBar">maxSize</a></tt> supersedes any specified 
<tt>minWidth</tt> / <tt>maxWidth</tt>.</p></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-split"></a><b><a href="source/BorderLayout.html#cfg-Ext.layout.BorderLayout.Region-split">split</a></b> : Boolean<div class="mdesc"><div class="short">true to create a SplitRegion and 
display a 5px wide Ext.SplitBar between this region and its neighbor, allowing the ...</div><div class="long"><p><tt>true</tt> to create a <a href="output/Ext.layout.BorderLayout.SplitRegion.html" ext:cls="Ext.layout.BorderLayout.SplitRegion">SplitRegion</a> and 
display a 5px wide <a href="output/Ext.SplitBar.html" ext:cls="Ext.SplitBar">Ext.SplitBar</a> between this region and its neighbor, allowing the user to
resize the regions dynamically.  Defaults to <tt>false</tt> creating a
<a href="output/Ext.layout.BorderLayout.Region.html" ext:cls="Ext.layout.BorderLayout.Region">Region</a>.</p><br>
<p><b>Notes</b>:</p><div class="mdetail-params"><ul>
<li>this configuration option is ignored if <tt>region='center'</tt></li> 
<li>when <tt>split == true</tt>, it is common to specify a
<tt><a href="output/Ext.SplitBar.html#Ext.SplitBar-minSize" ext:member="minSize" ext:cls="Ext.SplitBar">minSize</a></tt> and <tt><a href="output/Ext.SplitBar.html#Ext.SplitBar-maxSize" ext:member="maxSize" ext:cls="Ext.SplitBar">maxSize</a></tt>
for the <a href="output/Ext.BoxComponent.html" ext:cls="Ext.BoxComponent">BoxComponent</a> representing the region. These are not native
configs of <a href="output/Ext.BoxComponent.html" ext:cls="Ext.BoxComponent">BoxComponent</a>, and are used only by this class.</li>
<li>if <tt><a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-collapseMode" ext:member="collapseMode" ext:cls="Ext.layout.BorderLayout.Region">collapseMode</a> = 'mini'</tt> requires <tt>split = true</tt> to reserve space
for the collapse tool</tt></li> 
</ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr></tbody></table><a id="Ext.layout.BorderLayout.Region-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-isCollapsed"></a><b><a href="source/BorderLayout.html#prop-Ext.layout.BorderLayout.Region-isCollapsed">isCollapsed</a></b> : Boolean<div class="mdesc">True if this region is collapsed. Read-only.</div></td><td class="msource">BorderLayout.Region</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-layout"></a><b><a href="source/BorderLayout.html#prop-Ext.layout.BorderLayout.Region-layout">layout</a></b> : Layout<div class="mdesc">This region's layout.  Read-only.</div></td><td class="msource">BorderLayout.Region</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-panel"></a><b><a href="source/BorderLayout.html#prop-Ext.layout.BorderLayout.Region-panel">panel</a></b> : Ext.Panel<div class="mdesc">This region's panel.  Read-only.</div></td><td class="msource">BorderLayout.Region</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-position"></a><b><a href="source/BorderLayout.html#prop-Ext.layout.BorderLayout.Region-position">position</a></b> : String<div class="mdesc">This region's layout position (north, south, east, west or center).  Read-only.</div></td><td class="msource">BorderLayout.Region</td></tr></tbody></table><a id="Ext.layout.BorderLayout.Region-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-BorderLayout.Region"></a><b><a href="source/BorderLayout.html#cls-Ext.layout.BorderLayout.Region">BorderLayout.Region</a></b>(&nbsp;<code>Layout&nbsp;layout</code>,&nbsp;<code>Object&nbsp;config</code>,&nbsp;<code>String&nbsp;position</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new Region.</div><div class="long">Create a new Region.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>layout</code> : Layout<div class="sub-desc">The <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a> instance that is managing this Region.</div></li><li><code>config</code> : Object<div class="sub-desc">The configuration options</div></li><li><code>position</code> : String<div class="sub-desc">The region position.  Valid values are: <tt>north</tt>, <tt>south</tt>,
<tt>east</tt>, <tt>west</tt> and <tt>center</tt>.  Every <a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a>
<b>must have a center region</b> for the primary content -- all other regions are optional.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-getMargins"></a><b><a href="source/BorderLayout.html#method-Ext.layout.BorderLayout.Region-getMargins">getMargins</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the current margins for this region.  If the region is collapsed, the
cmargins (collapsed margins) value will...</div><div class="long">Returns the current margins for this region.  If the region is collapsed, the
<a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-cmargins" ext:member="cmargins" ext:cls="Ext.layout.BorderLayout.Region">cmargins</a> (collapsed margins) value will be returned, otherwise the
<a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-margins" ext:member="margins" ext:cls="Ext.layout.BorderLayout.Region">margins</a> value will be returned.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's margins: &lt;tt&gt;{left: (left
margin), top: (top margin), right: (right margin), bottom: (bottom margin)}&lt;/tt&gt;</div></li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-getMinHeight"></a><b><a href="source/BorderLayout.html#method-Ext.layout.BorderLayout.Region-getMinHeight">getMinHeight</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the minimum allowable height for this region.</div><div class="long">Returns the minimum allowable height for this region.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The minimum height</div></li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-getMinWidth"></a><b><a href="source/BorderLayout.html#method-Ext.layout.BorderLayout.Region-getMinWidth">getMinWidth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns the minimum allowable width for this region.</div><div class="long">Returns the minimum allowable width for this region.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The minimum width</div></li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-getSize"></a><b><a href="source/BorderLayout.html#method-Ext.layout.BorderLayout.Region-getSize">getSize</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the current size of this region.  If the region is collapsed, the size of the
collapsedEl will be returned, o...</div><div class="long">Returns the current size of this region.  If the region is collapsed, the size of the
collapsedEl will be returned, otherwise the size of the region's panel will be returned.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">An object containing the element's size: &lt;tt&gt;{width: (element width),
height: (element height)}&lt;/tt&gt;</div></li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-isVisible"></a><b><a href="source/BorderLayout.html#method-Ext.layout.BorderLayout.Region-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">True if this region is currently visible, else false.</div><div class="long">True if this region is currently visible, else false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BorderLayout.Region-setPanel"></a><b><a href="source/BorderLayout.html#method-Ext.layout.BorderLayout.Region-setPanel">setPanel</a></b>(&nbsp;<code>Ext.Panel&nbsp;panel</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the specified panel as the container element for this region.</div><div class="long">Sets the specified panel as the container element for this region.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>panel</code> : Ext.Panel<div class="sub-desc">The new panel</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">BorderLayout.Region</td></tr></tbody></table><a id="Ext.layout.BorderLayout.Region-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>