<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.RowNumberer-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.RowNumberer-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.RowNumberer-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.grid.RowNumberer-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.RowNumberer"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/RowNumberer.html#cls-Ext.grid.RowNumberer">Ext.grid.RowNumberer</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">RowNumberer.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/RowNumberer.html#cls-Ext.grid.RowNumberer">RowNumberer</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">This is a utility class that can be passed into a <a href="output/Ext.grid.ColumnModel.html" ext:cls="Ext.grid.ColumnModel">Ext.grid.ColumnModel</a> as a column config that provides
an automatic row numbering column.
<br>Usage:<br>
 <pre><code><i>// This is a typical column config <b>with</b> the first column providing row numbers</i>
 <b>var</b> colModel = <b>new</b> Ext.grid.ColumnModel([
    <b>new</b> Ext.grid.RowNumberer(),
    {header: <em>"Name"</em>, width: 80, sortable: true},
    {header: <em>"Code"</em>, width: 50, sortable: true},
    {header: <em>"Description"</em>, width: 200, sortable: true}
 ]);</code></pre></div><div class="hr"></div><a id="Ext.grid.RowNumberer-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowNumberer-header"></a><b><a href="source/RowNumberer.html#cfg-Ext.grid.RowNumberer-header">header</a></b> : String<div class="mdesc">Any valid text or HTML fragment to display in the header cell for the row
number column (defaults to '').</div></td><td class="msource">RowNumberer</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.grid.RowNumberer-width"></a><b><a href="source/RowNumberer.html#cfg-Ext.grid.RowNumberer-width">width</a></b> : Number<div class="mdesc">The default width in pixels of the row number column (defaults to 23).</div></td><td class="msource">RowNumberer</td></tr></tbody></table><a id="Ext.grid.RowNumberer-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.grid.RowNumberer-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.grid.RowNumberer-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>