<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.tree.TreeNodeUI-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.tree.TreeNodeUI-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.tree.TreeNodeUI-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.tree.TreeNodeUI"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/TreeNodeUI.html#cls-Ext.tree.TreeNodeUI">Ext.tree.TreeNodeUI</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.tree</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TreeNodeUI.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TreeNodeUI.html#cls-Ext.tree.TreeNodeUI">TreeNodeUI</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">This class provides the default UI implementation for Ext TreeNodes.
The TreeNode UI implementation is separate from the
tree implementation, and allows customizing of the appearance of
tree nodes.<br>
<p>
If you are customizing the Tree's user interface, you
may need to extend this class, but you should never need to instantiate this class.<br>
<p>
This class provides access to the user interface components of an Ext TreeNode, through
<a href="output/Ext.tree.TreeNode.html#Ext.tree.TreeNode-getUI" ext:member="getUI" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode.getUI</a></div><div class="hr"></div><a id="Ext.tree.TreeNodeUI-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.tree.TreeNodeUI-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-addClass"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-addClass">addClass</a></b>(&nbsp;<code>String/Array&nbsp;className</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Adds one or more CSS classes to the node's UI element.&#13;
Duplicate classes are automatically filtered out.</div><div class="long">Adds one or more CSS classes to the node's UI element.
Duplicate classes are automatically filtered out.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String/Array<div class="sub-desc">The CSS class to add, or an array of classes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-getAnchor"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-getAnchor">getAnchor</a></b>()
    :
                                        HtmlElement<div class="mdesc"><div class="short">Returns the &amp;lt;a&gt; element that provides focus for the node's UI.</div><div class="long">Returns the &lt;a> element that provides focus for the node's UI.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HtmlElement</code><div class="sub-desc">The DOM anchor element.</div></li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-getIconEl"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-getIconEl">getIconEl</a></b>()
    :
                                        HtmlElement<div class="mdesc"><div class="short">Returns the icon &amp;lt;img&gt; element.</div><div class="long">Returns the icon &lt;img> element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HtmlElement</code><div class="sub-desc">The DOM image element.</div></li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-getTextEl"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-getTextEl">getTextEl</a></b>()
    :
                                        HtmlNode<div class="mdesc"><div class="short">Returns the text node.</div><div class="long">Returns the text node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>HtmlNode</code><div class="sub-desc">The DOM text node.</div></li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-hide"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-hide">hide</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides this node.</div><div class="long">Hides this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-isChecked"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-isChecked">isChecked</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns the checked status of the node. If the node was rendered with no&#13;
checkbox, it returns false.</div><div class="long">Returns the checked status of the node. If the node was rendered with no
checkbox, it returns false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">The checked flag.</div></li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-removeClass"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-removeClass">removeClass</a></b>(&nbsp;<code>String/Array&nbsp;className</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes one or more CSS classes from the node's UI element.</div><div class="long">Removes one or more CSS classes from the node's UI element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>className</code> : String/Array<div class="sub-desc">The CSS class to remove, or an array of classes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-show"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-show">show</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Shows this node.</div><div class="long">Shows this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNodeUI-toggleCheck"></a><b><a href="source/TreeNodeUI.html#method-Ext.tree.TreeNodeUI-toggleCheck">toggleCheck</a></b>(&nbsp;<code>Boolean&nbsp;(optional)</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the checked status of the tree node to the passed value, or, if no value was passed,&#13;
toggles the checked status...</div><div class="long">Sets the checked status of the tree node to the passed value, or, if no value was passed,
toggles the checked status. If the node was rendered with no checkbox, this has no effect.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>(optional)</code> : Boolean<div class="sub-desc">The new checked status.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNodeUI</td></tr></tbody></table><a id="Ext.tree.TreeNodeUI-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>