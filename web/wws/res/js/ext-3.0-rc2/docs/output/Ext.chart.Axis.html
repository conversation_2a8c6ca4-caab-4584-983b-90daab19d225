<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.chart.Axis-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.chart.Axis-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.chart.Axis-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.chart.Axis"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Chart.html#cls-Ext.chart.Axis">Ext.chart.Axis</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.chart</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Chart.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Chart.html#cls-Ext.chart.Axis">Axis</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.chart.CategoryAxis.html" ext:cls="Ext.chart.CategoryAxis">CategoryAxis</a>,&#13;<a href="output/Ext.chart.NumericAxis.html" ext:cls="Ext.chart.NumericAxis">NumericAxis</a>,&#13;<a href="output/Ext.chart.TimeAxis.html" ext:cls="Ext.chart.TimeAxis">TimeAxis</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Defines a CartesianChart's vertical or horizontal axis.</div><div class="hr"></div><a id="Ext.chart.Axis-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-hideOverlappingLabels"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-hideOverlappingLabels">hideOverlappingLabels</a></b> : Boolean<div class="mdesc">If true, labels that overlap previously drawn labels on the axis will be hidden.</div></td><td class="msource">Axis</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-labelFunction"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-labelFunction">labelFunction</a></b> : String<div class="mdesc"><div class="short">A string reference to the globally-accessible function that may be called to&#13;
determine each of the label values for ...</div><div class="long">A string reference to the globally-accessible function that may be called to
determine each of the label values for this axis.</div></div></td><td class="msource">Axis</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-orientation"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-orientation">orientation</a></b> : String<div class="mdesc">The direction in which the axis is drawn. May be "horizontal" or "vertical".</div></td><td class="msource">Axis</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-reverse"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-reverse">reverse</a></b> : Boolean<div class="mdesc">If true, the items on the axis will be drawn in opposite direction.</div></td><td class="msource">Axis</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Axis-type"></a><b><a href="source/Chart.html#prop-Ext.chart.Axis-type">type</a></b> : String<div class="mdesc">The type of axis.</div></td><td class="msource">Axis</td></tr></tbody></table><a id="Ext.chart.Axis-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.chart.Axis-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>