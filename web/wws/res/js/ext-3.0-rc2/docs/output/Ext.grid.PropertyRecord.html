<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.grid.PropertyRecord-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.grid.PropertyRecord-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.grid.PropertyRecord-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.grid.PropertyRecord"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/PropertyGrid.html#cls-Ext.grid.PropertyRecord">Ext.grid.PropertyRecord</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.grid</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">PropertyGrid.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/PropertyGrid.html#cls-Ext.grid.PropertyRecord">PropertyRecord</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">A specific <a href="output/Ext.data.Record.html" ext:cls="Ext.data.Record">Ext.data.Record</a> type that represents a name/value pair and is made to work with the
<a href="output/Ext.grid.PropertyGrid.html" ext:cls="Ext.grid.PropertyGrid">Ext.grid.PropertyGrid</a>.  Typically, PropertyRecords do not need to be created directly as they can be
created implicitly by simply using the appropriate data configs either via the <a href="output/Ext.grid.PropertyGrid.html#Ext.grid.PropertyGrid-source" ext:member="source" ext:cls="Ext.grid.PropertyGrid">Ext.grid.PropertyGrid.source</a>
config property or by calling <a href="output/Ext.grid.PropertyGrid.html#Ext.grid.PropertyGrid-setSource" ext:member="setSource" ext:cls="Ext.grid.PropertyGrid">Ext.grid.PropertyGrid.setSource</a>.  However, if the need arises, these records
can also be created explicitly as shwon below.  Example usage:
<pre><code><b>var</b> rec = <b>new</b> Ext.grid.PropertyRecord({
    name: <em>'Birthday'</em>,
    value: <b>new</b> Date(Date.parse(<em>'05/26/1972'</em>))
});
<i>// Add record to an already populated grid</i>
grid.store.addSorted(rec);</code></pre></div><div class="hr"></div><a id="Ext.grid.PropertyRecord-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.grid.PropertyRecord-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.grid.PropertyRecord-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>