<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.util.DelayedTask-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.util.DelayedTask-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.util.DelayedTask-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.util.DelayedTask"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/DelayedTask.html#cls-Ext.util.DelayedTask">Ext.util.DelayedTask</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.util</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DelayedTask.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DelayedTask.html#cls-Ext.util.DelayedTask">DelayedTask</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p> The DelayedTask class provides a convenient way to "buffer" the execution of a method,
performing setTimeout where a new timeout cancels the old timeout. When called, the
task will wait the specified time period before executing. If durng that time period,
the task is called again, the original call will be cancelled. This continues so that
the function is only called a single time for each iteration.</p>
<p>This method is especially useful for things like detecting whether a user has finished
typing in a text field. An example would be performing validation on a keypress. You can
use this class to buffer the keypress events for a certain number of milliseconds, and
perform only if they stop for that amount of time.  Usage:</p><pre><code><b>var</b> task = <b>new</b> Ext.util.DelayedTask(<b>function</b>(){
    alert(Ext.getDom(<em>'myInputField'</em>).value.length);
});
<i>// Wait 500ms before calling our <b>function</b>. If the user presses another key </i>
<i>// during that 500ms, it will be cancelled and we<em>'ll wait another 500ms.</i>
Ext.get('</em>myInputField<em>').on('</em>keypress<em>', <b>function</b>(){
    task.<a href="output/Ext.util.DelayedTask.html#Ext.util.DelayedTask-delay" ext:member="delay" ext:cls="Ext.util.DelayedTask">delay</a>(500); 
});</code></pre> 
<p>Note that we are using a DelayedTask here to illustrate a point. The configuration
option <tt>buffer</tt> for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener/on</a> will
also setup a delayed task for you to buffer events.</p></div><div class="hr"></div><a id="Ext.util.DelayedTask-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.util.DelayedTask-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.DelayedTask-DelayedTask"></a><b><a href="source/DelayedTask.html#cls-Ext.util.DelayedTask">DelayedTask</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;fn</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    <div class="mdesc"><div class="short">The parameters to this constructor serve as defaults and are not required.</div><div class="long">The parameters to this constructor serve as defaults and are not required.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">(optional) The default function to timeout</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The default scope of that timeout</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The default Array of arguments</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DelayedTask</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.DelayedTask-cancel"></a><b><a href="source/DelayedTask.html#method-Ext.util.DelayedTask-cancel">cancel</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Cancel the last queued timeout</div><div class="long">Cancel the last queued timeout<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DelayedTask</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.DelayedTask-delay"></a><b><a href="source/DelayedTask.html#method-Ext.util.DelayedTask-delay">delay</a></b>(&nbsp;<code>Number&nbsp;delay</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;newFn</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;newScope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;newArgs</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Cancels any pending timeout and queues a new one</div><div class="long">Cancels any pending timeout and queues a new one<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>delay</code> : Number<div class="sub-desc">The milliseconds to delay</div></li><li><code>newFn</code> : Function<div class="sub-desc">(optional) Overrides function passed to constructor</div></li><li><code>newScope</code> : Object<div class="sub-desc">(optional) Overrides scope passed to constructor</div></li><li><code>newArgs</code> : Array<div class="sub-desc">(optional) Overrides args passed to constructor</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DelayedTask</td></tr></tbody></table><a id="Ext.util.DelayedTask-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>