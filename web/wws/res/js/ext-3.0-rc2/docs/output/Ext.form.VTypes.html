<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.form.VTypes-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.form.VTypes-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.form.VTypes-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.form.VTypes"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/VTypes.html#cls-Ext.form.VTypes">Ext.form.VTypes</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.form</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">VTypes.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/VTypes.html#cls-Ext.form.VTypes">VTypes</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>This is a singleton object which contains a set of commonly used field validation functions.
The validations provided are basic and intended to be easily customizable and extended.</p>
<p>To add custom VTypes specify the <tt><a href="output/Ext.form.TextField.html#Ext.form.TextField-vtype" ext:member="vtype" ext:cls="Ext.form.TextField">vtype</a></tt> validation
test function, and optionally specify any corresponding error text to display and any keystroke
filtering mask to apply. For example:</p>
<pre><code><i>// custom Vtype <b>for</b> vtype:<em>'time'</em></i>
<b>var</b> timeTest = /^([1-9]|1[0-9]):([0-5][0-9])(\s[a|p]m)$/i;
Ext.apply(Ext.form.VTypes, {
    <i>//  vtype validation <b>function</b></i>
    time: <b>function</b>(val, field) {
        <b>return</b> timeTest.test(val);
    },
    <i>// vtype Text property: The error text to display when the validation <b>function</b> returns false</i>
    timeText: <em>'Not a valid time.  Must be <b>in</b> the format <em>"12:34 PM"</em>.'</em>,
    <i>// vtype Mask property: The keystroke filter mask</i>
    timeMask: /[\d\s:amp]/i
});</code></pre>
Another example: 
<pre><code><i>// custom Vtype <b>for</b> vtype:<em>'IPAddress'</em></i>
Ext.apply(Ext.form.VTypes, {
    IPAddress:  <b>function</b>(v) {
        <b>return</b> /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(v);
    },
    IPAddressText: <em>'Must be a numeric IP address'</em>,
    IPAddressMask: /[\d\.]/i
});</code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.form.VTypes-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-alphaMask"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-alphaMask">alphaMask</a></b> : RegExp<div class="mdesc">The keystroke filter mask to be applied on alpha input.  Defaults to:
<tt>/[a-z_]/i</tt></div></td><td class="msource">VTypes</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-alphaText"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-alphaText">alphaText</a></b> : String<div class="mdesc"><div class="short">The error text to display when the alpha validation function returns false.  Defaults to:
'This field should only con...</div><div class="long">The error text to display when the alpha validation function returns false.  Defaults to:
<tt>'This field should only contain letters and _'</tt></div></div></td><td class="msource">VTypes</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-alphanumMask"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-alphanumMask">alphanumMask</a></b> : RegExp<div class="mdesc">The keystroke filter mask to be applied on alphanumeric input.  Defaults to:
<tt>/[a-z0-9_]/i</tt></div></td><td class="msource">VTypes</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-alphanumText"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-alphanumText">alphanumText</a></b> : String<div class="mdesc"><div class="short">The error text to display when the alphanumeric validation function returns false.  Defaults to:
'This field should o...</div><div class="long">The error text to display when the alphanumeric validation function returns false.  Defaults to:
<tt>'This field should only contain letters, numbers and _'</tt></div></div></td><td class="msource">VTypes</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-emailMask"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-emailMask">emailMask</a></b> : RegExp<div class="mdesc"><div class="short">The keystroke filter mask to be applied on email input.  See the email method for 
information about more complex ema...</div><div class="long">The keystroke filter mask to be applied on email input.  See the <a href="output/Ext.form.VTypes.html#Ext.form.VTypes-email" ext:member="email" ext:cls="Ext.form.VTypes">email</a> method for 
information about more complex email validation. Defaults to:
<tt>/[a-z0-9_\.\-@]/i</tt></div></div></td><td class="msource">VTypes</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-emailText"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-emailText">emailText</a></b> : String<div class="mdesc"><div class="short">The error text to display when the email validation function returns false.  Defaults to:
'This field should be an e-...</div><div class="long">The error text to display when the email validation function returns false.  Defaults to:
<tt>'This field should be an e-mail address in the format "<EMAIL>"'</tt></div></div></td><td class="msource">VTypes</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-urlText"></a><b><a href="source/VTypes.html#prop-Ext.form.VTypes-urlText">urlText</a></b> : String<div class="mdesc"><div class="short">The error text to display when the url validation function returns false.  Defaults to:
'This field should be a URL i...</div><div class="long">The error text to display when the url validation function returns false.  Defaults to:
<tt>'This field should be a URL in the format "http:/'+'/www.domain.com"'</tt></div></div></td><td class="msource">VTypes</td></tr></tbody></table><a id="Ext.form.VTypes-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-alpha"></a><b><a href="source/VTypes.html#method-Ext.form.VTypes-alpha">alpha</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">The function used to validate alpha values</div><div class="long">The function used to validate alpha values<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The value</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">VTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-alphanum"></a><b><a href="source/VTypes.html#method-Ext.form.VTypes-alphanum">alphanum</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">The function used to validate alphanumeric values</div><div class="long">The function used to validate alphanumeric values<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The value</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">VTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-email"></a><b><a href="source/VTypes.html#method-Ext.form.VTypes-email">email</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">The function used to validate email addresses.  Note that this is a very basic validation -- complete
validation per ...</div><div class="long">The function used to validate email addresses.  Note that this is a very basic validation -- complete
validation per the email RFC specifications is very complex and beyond the scope of this class, although
this function can be overridden if a more comprehensive validation scheme is desired.  See the validation
section of the <a href="http://en.wikipedia.org/wiki/E-mail_address">Wikipedia article on email addresses</a> 
for additional information.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The email address</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">VTypes</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.form.VTypes-url"></a><b><a href="source/VTypes.html#method-Ext.form.VTypes-url">url</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">The function used to validate URLs</div><div class="long">The function used to validate URLs<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The URL</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">VTypes</td></tr></tbody></table><a id="Ext.form.VTypes-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>