<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.chart.Series-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.chart.Series-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.chart.Series-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.chart.Series"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Chart.html#cls-Ext.chart.Series">Ext.chart.Series</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.chart</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Chart.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Chart.html#cls-Ext.chart.Series">Series</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.chart.CartesianSeries.html" ext:cls="Ext.chart.CartesianSeries">CartesianSeries</a>,&#13;<a href="output/Ext.chart.PieSeries.html" ext:cls="Ext.chart.PieSeries">PieSeries</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Series class for the charts widget.</div><div class="hr"></div><a id="Ext.chart.Series-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Series-displayName"></a><b><a href="source/Chart.html#prop-Ext.chart.Series-displayName">displayName</a></b> : String<div class="mdesc">The human-readable name of the series.</div></td><td class="msource">Series</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Series-type"></a><b><a href="source/Chart.html#prop-Ext.chart.Series-type">type</a></b> : String<div class="mdesc">The type of series.</div></td><td class="msource">Series</td></tr></tbody></table><a id="Ext.chart.Series-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.chart.Series-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>