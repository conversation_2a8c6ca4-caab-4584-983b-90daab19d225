<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.state.CookieProvider-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.state.CookieProvider-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.state.CookieProvider-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.state.CookieProvider-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.state.CookieProvider"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.state.Provider.html" ext:member="" ext:cls="Ext.state.Provider">Provider</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">CookieProvider</pre></div><h1>Class <a href="source/CookieProvider.html#cls-Ext.state.CookieProvider">Ext.state.CookieProvider</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.state</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">CookieProvider.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/CookieProvider.html#cls-Ext.state.CookieProvider">CookieProvider</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.state.Provider.html" ext:cls="Ext.state.Provider" ext:member="">Provider</a></td></tr></table><div class="description">The default Provider implementation which saves state via cookies.
<br />Usage:
 <pre><code><b>var</b> cp = <b>new</b> Ext.state.CookieProvider({
       path: <em>"/cgi-bin/"</em>,
       expires: <b>new</b> Date(<b>new</b> Date().getTime()+(1000*60*60*24*30)), <i>//30 days
</i>
       domain: <em>"extjs.com"</em>
   });
   Ext.state.Manager.setProvider(cp);</code></pre></div><div class="hr"></div><a id="Ext.state.CookieProvider-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.CookieProvider-domain"></a><b><a href="source/CookieProvider.html#cfg-Ext.state.CookieProvider-domain">domain</a></b> : String<div class="mdesc"><div class="short">The domain to save the cookie for.  Note that you cannot specify a different domain than&#13;
your page is on, but you ca...</div><div class="long">The domain to save the cookie for.  Note that you cannot specify a different domain than
your page is on, but you can specify a sub-domain, or simply the domain itself like 'extjs.com' to include
all sub-domains if you need to access cookies across different sub-domains (defaults to null which uses the same
domain the page is running on including the 'www' like 'www.extjs.com')</div></div></td><td class="msource">CookieProvider</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.CookieProvider-expires"></a><b><a href="source/CookieProvider.html#cfg-Ext.state.CookieProvider-expires">expires</a></b> : Date<div class="mdesc">The cookie expiration date (defaults to 7 days from now)</div></td><td class="msource">CookieProvider</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.CookieProvider-path"></a><b><a href="source/CookieProvider.html#cfg-Ext.state.CookieProvider-path">path</a></b> : String<div class="mdesc">The path for which the cookie is active (defaults to root '/' which makes it active for all pages in the site)</div></td><td class="msource">CookieProvider</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.CookieProvider-secure"></a><b><a href="source/CookieProvider.html#cfg-Ext.state.CookieProvider-secure">secure</a></b> : Boolean<div class="mdesc">True if the site is using SSL (defaults to false)</div></td><td class="msource">CookieProvider</td></tr></tbody></table><a id="Ext.state.CookieProvider-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.state.CookieProvider-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.CookieProvider-CookieProvider"></a><b><a href="source/CookieProvider.html#cls-Ext.state.CookieProvider">CookieProvider</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new CookieProvider</div><div class="long">Create a new CookieProvider<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The configuration object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">CookieProvider</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-clear"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-clear">clear</a></b>(&nbsp;<code>String&nbsp;name</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Clears a value from the state</div><div class="long">Clears a value from the state<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.state.Provider.html#clear" ext:member="#clear" ext:cls="Ext.state.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-decodeValue"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-decodeValue">decodeValue</a></b>(&nbsp;<code>String&nbsp;value</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Decodes a string previously encoded with encodeValue.</div><div class="long">Decodes a string previously encoded with <a href="output/Ext.state.Provider.html#Ext.state.Provider-encodeValue" ext:member="encodeValue" ext:cls="Ext.state.Provider">encodeValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : String<div class="sub-desc">The value to decode</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">The decoded value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.state.Provider.html#decodeValue" ext:member="#decodeValue" ext:cls="Ext.state.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-encodeValue"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-encodeValue">encodeValue</a></b>(&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Encodes a value including type information.  Decode with decodeValue.</div><div class="long">Encodes a value including type information.  Decode with <a href="output/Ext.state.Provider.html#Ext.state.Provider-decodeValue" ext:member="decodeValue" ext:cls="Ext.state.Provider">decodeValue</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Mixed<div class="sub-desc">The value to encode</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The encoded value</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.state.Provider.html#encodeValue" ext:member="#encodeValue" ext:cls="Ext.state.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-get"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-get">get</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;defaultValue</code>&nbsp;)
    :
                                        Mixed<div class="mdesc"><div class="short">Returns the current value for a key</div><div class="long">Returns the current value for a key<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li><li><code>defaultValue</code> : Mixed<div class="sub-desc">A default value to return if the key's value is not found</div></li></ul><strong>Returns:</strong><ul><li><code>Mixed</code><div class="sub-desc">The state data</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.state.Provider.html#get" ext:member="#get" ext:cls="Ext.state.Provider">Provider</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-set"></a><b><a href="source/Provider1.html#method-Ext.state.Provider-set">set</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the value for a key</div><div class="long">Sets the value for a key<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The key name</div></li><li><code>value</code> : Mixed<div class="sub-desc">The value to set</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.state.Provider.html#set" ext:member="#set" ext:cls="Ext.state.Provider">Provider</a></td></tr></tbody></table><a id="Ext.state.CookieProvider-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.state.Provider-statechange"></a><b><a href="source/Provider1.html#event-Ext.state.Provider-statechange">statechange</a></b> :
                                      (&nbsp;<code>Provider&nbsp;this</code>,&nbsp;<code>String&nbsp;key</code>,&nbsp;<code>String&nbsp;value</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a state change occurs.</div><div class="long">Fires when a state change occurs.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Provider<div class="sub-desc">This state provider</div></li><li><code>key</code> : String<div class="sub-desc">The state key which was changed</div></li><li><code>value</code> : String<div class="sub-desc">The encoded value for the state</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.state.Provider.html#statechange" ext:member="#statechange" ext:cls="Ext.state.Provider">Provider</a></td></tr></tbody></table></div>