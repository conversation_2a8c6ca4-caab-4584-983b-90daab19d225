<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.MessageBox-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.MessageBox-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.MessageBox-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.MessageBox"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/MessageBox.html#cls-Ext.MessageBox">Ext.MessageBox</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">MessageBox.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/MessageBox.html#cls-Ext.MessageBox">MessageBox</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>Utility class for generating different styles of message boxes.  The alias Ext.Msg can also be used.<p/>
<p>Note that the MessageBox is asynchronous.  Unlike a regular JavaScript <code>alert</code> (which will halt
browser execution), showing a MessageBox will not cause the code to stop.  For this reason, if you have code
that should only run <em>after</em> some user feedback from the MessageBox, you must use a callback function
(see the <code><b>function</b></code> parameter for <a href="output/Ext.MessageBox.html#Ext.MessageBox-show" ext:member="show" ext:cls="Ext.MessageBox">show</a> for more details).</p>
<p>Example usage:</p>
<pre><code><i>// Basic alert:</i>
Ext.Msg.alert(<em>'Status'</em>, <em>'Changes saved successfully.'</em>);

<i>// Prompt <b>for</b> user data and process the result using a callback:</i>
Ext.Msg.prompt(<em>'Name'</em>, <em>'Please enter your name:'</em>, <b>function</b>(btn, text){
    <b>if</b> (btn == <em>'ok'</em>){
        <i>// process text value and close...</i>
    }
});

<i>// Show a dialog using config options:</i>
Ext.Msg.show({
   title:<em>'Save Changes?'</em>,
   msg: <em>'You are closing a tab that has unsaved changes. Would you like to save your changes?'</em>,
   buttons: Ext.Msg.YESNOCANCEL,
   fn: processResult,
   animEl: <em>'elId'</em>,
   icon: Ext.MessageBox.QUESTION
});</code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.MessageBox-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-CANCEL"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-CANCEL">CANCEL</a></b> : Object<div class="mdesc">Button config that displays a single Cancel button</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-ERROR"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-ERROR">ERROR</a></b> : String<div class="mdesc">The CSS class that provides the ERROR icon image</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-INFO"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-INFO">INFO</a></b> : String<div class="mdesc">The CSS class that provides the INFO icon image</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-Msg"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-Msg">Msg</a></b> : Object<div class="mdesc">Shorthand for <a href="output/Ext.MessageBox.html" ext:cls="Ext.MessageBox">Ext.MessageBox</a></div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-OK"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-OK">OK</a></b> : Object<div class="mdesc">Button config that displays a single OK button</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-OKCANCEL"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-OKCANCEL">OKCANCEL</a></b> : Object<div class="mdesc">Button config that displays OK and Cancel buttons</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-QUESTION"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-QUESTION">QUESTION</a></b> : String<div class="mdesc">The CSS class that provides the QUESTION icon image</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-WARNING"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-WARNING">WARNING</a></b> : String<div class="mdesc">The CSS class that provides the WARNING icon image</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-YESNO"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-YESNO">YESNO</a></b> : Object<div class="mdesc">Button config that displays Yes and No buttons</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-YESNOCANCEL"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-YESNOCANCEL">YESNOCANCEL</a></b> : Object<div class="mdesc">Button config that displays Yes, No and Cancel buttons</div></td><td class="msource">MessageBox</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-buttonText"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-buttonText">buttonText</a></b> : Object<div class="mdesc"><div class="short">An object containing the default button text strings that can be overriden for localized language support.
Supported ...</div><div class="long">An object containing the default button text strings that can be overriden for localized language support.
Supported properties are: ok, cancel, yes and no.  Generally you should include a locale-specific
resource file for handling language support across the framework.
Customize the default text like so: Ext.MessageBox.buttonText.yes = "oui"; //french</div></div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-defaultTextHeight"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-defaultTextHeight">defaultTextHeight</a></b> : Number<div class="mdesc">The default height in pixels of the message box's multiline textarea if displayed (defaults to 75)</div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-maxWidth"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-maxWidth">maxWidth</a></b> : Number<div class="mdesc">The maximum width in pixels of the message box (defaults to 600)</div></td><td class="msource">MessageBox</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-minProgressWidth"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-minProgressWidth">minProgressWidth</a></b> : Number<div class="mdesc"><div class="short">The minimum width in pixels of the message box if it is a progress-style dialog.  This is useful
for setting a differ...</div><div class="long">The minimum width in pixels of the message box if it is a progress-style dialog.  This is useful
for setting a different minimum width than text-only dialogs may need (defaults to 250)</div></div></td><td class="msource">MessageBox</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-minWidth"></a><b><a href="source/MessageBox.html#prop-Ext.MessageBox-minWidth">minWidth</a></b> : Number<div class="mdesc">The minimum width in pixels of the message box (defaults to 100)</div></td><td class="msource">MessageBox</td></tr></tbody></table><a id="Ext.MessageBox-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-alert"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-alert">alert</a></b>(&nbsp;<code>String&nbsp;title</code>,&nbsp;<code>String&nbsp;msg</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;fn</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Displays a standard read-only message box with an OK button (comparable to the basic JavaScript alert prompt).
If a c...</div><div class="long">Displays a standard read-only message box with an OK button (comparable to the basic JavaScript alert prompt).
If a callback function is passed it will be called after the user clicks the button, and the
id of the button that was clicked will be passed as the only parameter to the callback
(could also be the top-right close button).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>title</code> : String<div class="sub-desc">The title bar text</div></li><li><code>msg</code> : String<div class="sub-desc">The message box body text</div></li><li><code>fn</code> : Function<div class="sub-desc">(optional) The callback function invoked after the message box is closed</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the callback function</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-confirm"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-confirm">confirm</a></b>(&nbsp;<code>String&nbsp;title</code>,&nbsp;<code>String&nbsp;msg</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;fn</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Displays a confirmation message box with Yes and No buttons (comparable to JavaScript's confirm).
If a callback funct...</div><div class="long">Displays a confirmation message box with Yes and No buttons (comparable to JavaScript's confirm).
If a callback function is passed it will be called after the user clicks either button,
and the id of the button that was clicked will be passed as the only parameter to the callback
(could also be the top-right close button).<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>title</code> : String<div class="sub-desc">The title bar text</div></li><li><code>msg</code> : String<div class="sub-desc">The message box body text</div></li><li><code>fn</code> : Function<div class="sub-desc">(optional) The callback function invoked after the message box is closed</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the callback function</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-getDialog"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-getDialog">getDialog</a></b>()
    :
                                        Ext.Window<div class="mdesc"><div class="short">Returns a reference to the underlying Ext.Window element</div><div class="long">Returns a reference to the underlying <a href="output/Ext.Window.html" ext:cls="Ext.Window">Ext.Window</a> element<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Window</code><div class="sub-desc">The window</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-hide"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-hide">hide</a></b>()
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Hides the message box if it is displayed</div><div class="long">Hides the message box if it is displayed<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-isVisible"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-isVisible">isVisible</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the message box is currently displayed</div><div class="long">Returns true if the message box is currently displayed<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the message box is visible, else false</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-progress"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-progress">progress</a></b>(&nbsp;<code>String&nbsp;title</code>,&nbsp;<code>String&nbsp;msg</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;progressText</code>]</span>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Displays a message box with a progress bar.  This message box has no buttons and is not closeable by
the user.  You a...</div><div class="long">Displays a message box with a progress bar.  This message box has no buttons and is not closeable by
the user.  You are responsible for updating the progress bar as needed via <a href="output/Ext.MessageBox.html#Ext.MessageBox-updateProgress" ext:member="updateProgress" ext:cls="Ext.MessageBox">Ext.MessageBox.updateProgress</a>
and closing the message box when the process is complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>title</code> : String<div class="sub-desc">The title bar text</div></li><li><code>msg</code> : String<div class="sub-desc">The message box body text</div></li><li><code>progressText</code> : String<div class="sub-desc">(optional) The text to display inside the progress bar (defaults to '')</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-prompt"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-prompt">prompt</a></b>(&nbsp;<code>String&nbsp;title</code>,&nbsp;<code>String&nbsp;msg</code>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;fn</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean/Number&nbsp;multiline</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;value</code>]</span>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Displays a message box with OK and Cancel buttons prompting the user to enter some text (comparable to JavaScript's p...</div><div class="long">Displays a message box with OK and Cancel buttons prompting the user to enter some text (comparable to JavaScript's prompt).
The prompt can be a single-line or multi-line textbox.  If a callback function is passed it will be called after the user
clicks either button, and the id of the button that was clicked (could also be the top-right
close button) and the text that was entered will be passed as the two parameters to the callback.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>title</code> : String<div class="sub-desc">The title bar text</div></li><li><code>msg</code> : String<div class="sub-desc">The message box body text</div></li><li><code>fn</code> : Function<div class="sub-desc">(optional) The callback function invoked after the message box is closed</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the callback function</div></li><li><code>multiline</code> : Boolean/Number<div class="sub-desc">(optional) True to create a multiline textbox using the defaultTextHeight
property, or the height in pixels to create the textbox (defaults to false / single-line)</div></li><li><code>value</code> : String<div class="sub-desc">(optional) Default value of the text input element (defaults to '')</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-setIcon"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-setIcon">setIcon</a></b>(&nbsp;<code>String&nbsp;icon</code>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Adds the specified icon to the dialog.  By default, the class 'ext-mb-icon' is applied for default
styling, and the c...</div><div class="long">Adds the specified icon to the dialog.  By default, the class 'ext-mb-icon' is applied for default
styling, and the class passed in is expected to supply the background image url. Pass in empty string ('')
to clear any existing icon.  The following built-in icon classes are supported, but you can also pass
in a custom class name:
<pre>
Ext.MessageBox.INFO
Ext.MessageBox.WARNING
Ext.MessageBox.QUESTION
Ext.MessageBox.ERROR
</pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>icon</code> : String<div class="sub-desc">A CSS classname specifying the icon's background image url, or empty string to clear the icon</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-show"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-show">show</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Displays a new message box, or reinitializes an existing message box, based on the config options
passed in. All disp...</div><div class="long">Displays a new message box, or reinitializes an existing message box, based on the config options
passed in. All display functions (e.g. prompt, alert, etc.) on MessageBox call this function internally,
although those calls are basic shortcuts and do not support all of the config options allowed here.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The following config options are supported: <ul>
<li><b>animEl</b> : String/Element<div class="sub-desc">An id or Element from which the message box should animate as it
opens and closes (defaults to undefined)</div></li>
<li><b>buttons</b> : Object/Boolean<div class="sub-desc">A button config object (e.g., Ext.MessageBox.OKCANCEL or {ok:'Foo',
cancel:'Bar'}), or false to not show any buttons (defaults to false)</div></li>
<li><b>closable</b> : Boolean<div class="sub-desc">False to hide the top-right close button (defaults to true). Note that
progress and wait dialogs will ignore this property and always hide the close button as they can only
be closed programmatically.</div></li>
<li><b>cls</b> : String<div class="sub-desc">A custom CSS class to apply to the message box's container element</div></li>
<li><b>defaultTextHeight</b> : Number<div class="sub-desc">The default height in pixels of the message box's multiline textarea
if displayed (defaults to 75)</div></li>
<li><b>fn</b> : Function<div class="sub-desc">A callback function which is called when the dialog is dismissed either
by clicking on the configured buttons, or on the dialog close button, or by pressing
the return button to enter input.
<p>Progress and wait dialogs will ignore this option since they do not respond to user
actions and can only be closed programmatically, so any required function should be called
by the same code after it closes the dialog. Parameters passed:<ul>
<li><b>buttonId</b> : String<div class="sub-desc">The ID of the button pressed, one of:<div class="sub-desc"><ul>
<li><tt>ok</tt></li>
<li><tt>yes</tt></li>
<li><tt>no</tt></li>
<li><tt>cancel</tt></li>
</ul></div></div></li>
<li><b>text</b> : String<div class="sub-desc">Value of the input field if either <tt><a href="#show-option-prompt" ext:member="show-option-prompt" ext:cls="Ext.MessageBox">prompt</a></tt>
or <tt><a href="#show-option-multiline" ext:member="show-option-multiline" ext:cls="Ext.MessageBox">multiline</a></tt> is true</div></li>
<li><b>opt</b> : Object<div class="sub-desc">The config object passed to show.</div></li>
</ul></p></div></li>
<li><b>scope</b> : Object<div class="sub-desc">The scope of the callback function</div></li>
<li><b>icon</b> : String<div class="sub-desc">A CSS class that provides a background image to be used as the body icon for the
dialog (e.g. Ext.MessageBox.WARNING or 'custom-class') (defaults to '')</div></li>
<li><b>iconCls</b> : String<div class="sub-desc">The standard <a href="output/Ext.Window.html#Ext.Window-iconCls" ext:member="iconCls" ext:cls="Ext.Window">Ext.Window.iconCls</a> to
add an optional header icon (defaults to '')</div></li>
<li><b>maxWidth</b> : Number<div class="sub-desc">The maximum width in pixels of the message box (defaults to 600)</div></li>
<li><b>minWidth</b> : Number<div class="sub-desc">The minimum width in pixels of the message box (defaults to 100)</div></li>
<li><b>modal</b> : Boolean<div class="sub-desc">False to allow user interaction with the page while the message box is
displayed (defaults to true)</div></li>
<li><b>msg</b> : String<div class="sub-desc">A string that will replace the existing message box body text (defaults to the
XHTML-compliant non-breaking space character '&amp;#160;')</div></li>
<li><a id="show-option-multiline"></a><b>multiline</b> : Boolean<div class="sub-desc">
True to prompt the user to enter multi-line text (defaults to false)</div></li>
<li><b>progress</b> : Boolean<div class="sub-desc">True to display a progress bar (defaults to false)</div></li>
<li><b>progressText</b> : String<div class="sub-desc">The text to display inside the progress bar if progress = true (defaults to '')</div></li>
<li><a id="show-option-prompt"></a><b>prompt</b> : Boolean<div class="sub-desc">True to prompt the user to enter single-line text (defaults to false)</div></li>
<li><b>proxyDrag</b> : Boolean<div class="sub-desc">True to display a lightweight proxy while dragging (defaults to false)</div></li>
<li><b>title</b> : String<div class="sub-desc">The title text</div></li>
<li><b>value</b> : String<div class="sub-desc">The string value to set into the active textbox element if displayed</div></li>
<li><b>wait</b> : Boolean<div class="sub-desc">True to display a progress bar (defaults to false)</div></li>
<li><b>waitConfig</b> : Object<div class="sub-desc">A <a href="output/Ext.ProgressBar.html#Ext.ProgressBar-waitConfig" ext:member="waitConfig" ext:cls="Ext.ProgressBar">Ext.ProgressBar.waitConfig</a> object (applies only if wait = true)</div></li>
<li><b>width</b> : Number<div class="sub-desc">The width of the dialog in pixels</div></li>
</ul>
Example usage:
<pre><code>Ext.Msg.show({
   title: <em>'Address'</em>,
   msg: <em>'Please enter your address:'</em>,
   width: 300,
   buttons: Ext.MessageBox.OKCANCEL,
   multiline: true,
   fn: saveAddress,
   animEl: <em>'addAddressBtn'</em>,
   icon: Ext.MessageBox.INFO
});</code></pre></div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-updateProgress"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-updateProgress">updateProgress</a></b>(&nbsp;<code>Number&nbsp;value</code>,&nbsp;<code>String&nbsp;progressText</code>,&nbsp;<code>String&nbsp;msg</code>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Updates a progress-style message box's text and progress bar. Only relevant on message boxes
initiated via Ext.Messag...</div><div class="long">Updates a progress-style message box's text and progress bar. Only relevant on message boxes
initiated via <a href="output/Ext.MessageBox.html#Ext.MessageBox-progress" ext:member="progress" ext:cls="Ext.MessageBox">Ext.MessageBox.progress</a> or <a href="output/Ext.MessageBox.html#Ext.MessageBox-wait" ext:member="wait" ext:cls="Ext.MessageBox">Ext.MessageBox.wait</a>,
or by calling <a href="output/Ext.MessageBox.html#Ext.MessageBox-show" ext:member="show" ext:cls="Ext.MessageBox">Ext.MessageBox.show</a> with progress: true.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>value</code> : Number<div class="sub-desc">Any number between 0 and 1 (e.g., .5, defaults to 0)</div></li><li><code>progressText</code> : String<div class="sub-desc">The progress text to display inside the progress bar (defaults to '')</div></li><li><code>msg</code> : String<div class="sub-desc">The message box's body text is replaced with the specified string (defaults to undefined
so that any existing body text will not get overwritten by default unless a new value is passed in)</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-updateText"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-updateText">updateText</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;text</code>]</span>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Updates the message box body text</div><div class="long">Updates the message box body text<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc">(optional) Replaces the message box element's innerHTML with the specified string (defaults to
the XHTML-compliant non-breaking space character '&amp;#160;')</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.MessageBox-wait"></a><b><a href="source/MessageBox.html#method-Ext.MessageBox-wait">wait</a></b>(&nbsp;<code>String&nbsp;msg</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;title</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;config</code>]</span>&nbsp;)
    :
                                        Ext.MessageBox<div class="mdesc"><div class="short">Displays a message box with an infinitely auto-updating progress bar.  This can be used to block user
interaction whi...</div><div class="long">Displays a message box with an infinitely auto-updating progress bar.  This can be used to block user
interaction while waiting for a long-running process to complete that does not have defined intervals.
You are responsible for closing the message box when the process is complete.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>msg</code> : String<div class="sub-desc">The message box body text</div></li><li><code>title</code> : String<div class="sub-desc">(optional) The title bar text</div></li><li><code>config</code> : Object<div class="sub-desc">(optional) A <a href="output/Ext.ProgressBar.html#Ext.ProgressBar-waitConfig" ext:member="waitConfig" ext:cls="Ext.ProgressBar">Ext.ProgressBar.waitConfig</a> object</div></li></ul><strong>Returns:</strong><ul><li><code>Ext.MessageBox</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource">MessageBox</td></tr></tbody></table><a id="Ext.MessageBox-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>