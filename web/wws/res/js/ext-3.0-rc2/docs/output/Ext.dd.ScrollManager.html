<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.ScrollManager-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.ScrollManager-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.ScrollManager-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.ScrollManager"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/ScrollManager.html#cls-Ext.dd.ScrollManager">Ext.dd.ScrollManager</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">ScrollManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/ScrollManager.html#cls-Ext.dd.ScrollManager">ScrollManager</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>Provides automatic scrolling of overflow regions in the page during drag operations.</p>
<p>The ScrollManager configs will be used as the defaults for any scroll container registered with it,
but you can also override most of the configs per scroll container by adding a 
<tt>ddScrollConfig</tt> object to the target element that contains these properties: <a href="output/Ext.dd.ScrollManager.html#Ext.dd.ScrollManager-hthresh" ext:member="hthresh" ext:cls="Ext.dd.ScrollManager">hthresh</a>,
<a href="output/Ext.dd.ScrollManager.html#Ext.dd.ScrollManager-vthresh" ext:member="vthresh" ext:cls="Ext.dd.ScrollManager">vthresh</a>, <a href="output/Ext.dd.ScrollManager.html#Ext.dd.ScrollManager-increment" ext:member="increment" ext:cls="Ext.dd.ScrollManager">increment</a> and <a href="output/Ext.dd.ScrollManager.html#Ext.dd.ScrollManager-frequency" ext:member="frequency" ext:cls="Ext.dd.ScrollManager">frequency</a>.  Example usage:
<pre><code><b>var</b> el = Ext.get(<em>'scroll-ct'</em>);
el.ddScrollConfig = {
    vthresh: 50,
    hthresh: -1,
    frequency: 100,
    increment: 200
};
Ext.dd.ScrollManager.register(el);</code></pre>
<b>Note: This class uses "Point Mode" and is untested in "Intersect Mode".</b><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.dd.ScrollManager-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-animDuration"></a><b><a href="source/ScrollManager.html#prop-Ext.dd.ScrollManager-animDuration">animDuration</a></b> : Number<div class="mdesc">The animation duration in seconds - 
MUST BE less than Ext.dd.ScrollManager.frequency! (defaults to .4)</div></td><td class="msource">ScrollManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-animate"></a><b><a href="source/ScrollManager.html#prop-Ext.dd.ScrollManager-animate">animate</a></b> : Boolean<div class="mdesc">True to animate the scroll (defaults to true)</div></td><td class="msource">ScrollManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-frequency"></a><b><a href="source/ScrollManager.html#prop-Ext.dd.ScrollManager-frequency">frequency</a></b> : Number<div class="mdesc">The frequency of scrolls in milliseconds (defaults to 500)</div></td><td class="msource">ScrollManager</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-hthresh"></a><b><a href="source/ScrollManager.html#prop-Ext.dd.ScrollManager-hthresh">hthresh</a></b> : Number<div class="mdesc"><div class="short">The number of pixels from the right or left edge of a container the pointer needs to be to&#13;
trigger scrolling (defaul...</div><div class="long">The number of pixels from the right or left edge of a container the pointer needs to be to
trigger scrolling (defaults to 25)</div></div></td><td class="msource">ScrollManager</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-increment"></a><b><a href="source/ScrollManager.html#prop-Ext.dd.ScrollManager-increment">increment</a></b> : Number<div class="mdesc">The number of pixels to scroll in each scroll increment (defaults to 50)</div></td><td class="msource">ScrollManager</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-vthresh"></a><b><a href="source/ScrollManager.html#prop-Ext.dd.ScrollManager-vthresh">vthresh</a></b> : Number<div class="mdesc"><div class="short">The number of pixels from the top or bottom edge of a container the pointer needs to be to&#13;
trigger scrolling (defaul...</div><div class="long">The number of pixels from the top or bottom edge of a container the pointer needs to be to
trigger scrolling (defaults to 25)</div></div></td><td class="msource">ScrollManager</td></tr></tbody></table><a id="Ext.dd.ScrollManager-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-refreshCache"></a><b><a href="source/ScrollManager.html#method-Ext.dd.ScrollManager-refreshCache">refreshCache</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Manually trigger a cache refresh.</div><div class="long">Manually trigger a cache refresh.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ScrollManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-register"></a><b><a href="source/ScrollManager.html#method-Ext.dd.ScrollManager-register">register</a></b>(&nbsp;<code>Mixed/Array&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Registers new overflow element(s) to auto scroll</div><div class="long">Registers new overflow element(s) to auto scroll<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed/Array<div class="sub-desc">The id of or the element to be scrolled or an array of either</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ScrollManager</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.ScrollManager-unregister"></a><b><a href="source/ScrollManager.html#method-Ext.dd.ScrollManager-unregister">unregister</a></b>(&nbsp;<code>Mixed/Array&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unregisters overflow element(s) so they are no longer scrolled</div><div class="long">Unregisters overflow element(s) so they are no longer scrolled<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed/Array<div class="sub-desc">The id of or the element to be removed or an array of either</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">ScrollManager</td></tr></tbody></table><a id="Ext.dd.ScrollManager-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>