<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.VBoxLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.VBoxLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.VBoxLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.VBoxLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.VBoxLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.layout.ContainerLayout.html" ext:member="" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.layout.BoxLayout.html" ext:member="" ext:cls="Ext.layout.BoxLayout">BoxLayout</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">VBoxLayout</pre></div><h1>Class <a href="source/BoxLayout.html#cls-Ext.layout.VBoxLayout">Ext.layout.VBoxLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">BoxLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/BoxLayout.html#cls-Ext.layout.VBoxLayout">VBoxLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.layout.BoxLayout.html" ext:cls="Ext.layout.BoxLayout" ext:member="">BoxLayout</a></td></tr></table><div class="description">A layout that arranges items vertically</div><div class="hr"></div><a id="Ext.layout.VBoxLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.VBoxLayout-align"></a><b><a href="source/BoxLayout.html#cfg-Ext.layout.VBoxLayout-align">align</a></b> : String<div class="mdesc"><div class="short">Controls how the child items of the container are aligned. Acceptable configuration values for this&#13;
property are:&#13;
&lt;...</div><div class="long">Controls how the child items of the container are aligned. Acceptable configuration values for this
property are:
<div class="mdetail-params"><ul>
<li><b><tt>left</tt></b> : <b>Default</b><div class="sub-desc">child items are aligned horizontally
at the <b>left</b> side of the container</div></li>
<li><b><tt>center</tt></b> : <div class="sub-desc">child items are aligned horizontally at the
<b>mid-width</b> of the container</div></li>
<li><b><tt>stretch</tt></b> : <div class="sub-desc">child items are stretched horizontally to fill
the width of the container</div></li>
<li><b><tt>strechmax</tt></b> : <div class="sub-desc"> </div></li>
</ul></div></div></div></td><td class="msource">VBoxLayout</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BoxLayout-defaultMargins"></a><b><a href="source/BoxLayout.html#cfg-Ext.layout.BoxLayout-defaultMargins">defaultMargins</a></b> : Object<div class="mdesc"><div class="short">If the individual contained items do not have a margins property specified, the margins&#13;
from this object literal rep...</div><div class="long">If the individual contained items do not have a <tt>margins</tt> property specified, the margins
from this object literal representing the default margins will be applied to each item. Defaults
to <tt>{left:0,top:0,right:0,bottom:0}</tt>.</div></div></td><td class="msource"><a href="output/Ext.layout.BoxLayout.html#defaultMargins" ext:member="#defaultMargins" ext:cls="Ext.layout.BoxLayout">BoxLayout</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#extraCls" ext:member="#extraCls" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.VBoxLayout-flex"></a><b><a href="source/BoxLayout.html#cfg-Ext.layout.VBoxLayout-flex">flex</a></b> : Number<div class="mdesc"><div class="short">This configuation option is to be applied to child items of the container managed&#13;
by this layout. Each child item wi...</div><div class="long">This configuation option is to be applied to <b>child <tt>items</tt></b> of the container managed
by this layout. Each child item with a <tt>flex</tt> property will be flexed <b>vertically</b>
according to each item's <b>relative</b> <tt>flex</tt> value compared to the sum of all items with
a <tt>flex</tt> value specified.  Any child items that have either a <tt>flex = 0</tt> or
<tt>flex = undefined</tt> will not be 'flexed' (the initial size will not be changed).</div></div></td><td class="msource">VBoxLayout</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.VBoxLayout-pack"></a><b><a href="source/BoxLayout.html#cfg-Ext.layout.VBoxLayout-pack">pack</a></b> : String<div class="mdesc"><div class="short">Controls how the child items of the container are packed together. Acceptable configuration values&#13;
for this property...</div><div class="long">Controls how the child items of the container are packed together. Acceptable configuration values
for this property are:
<div class="mdetail-params"><ul>
<li><b><tt>start</tt></b> : <b>Default</b><div class="sub-desc">child items are packed together at
<b>top</b> side of container</div></li>
<li><b><tt>center</tt></b> : <div class="sub-desc">child items are packed together at
<b>mid-height</b> of container</div></li>
<li><b><tt>end</tt></b> : <div class="sub-desc">child items are packed together at <b>bottom</b>
side of container</div></li>
</ul></div></div></div></td><td class="msource">VBoxLayout</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.BoxLayout-padding"></a><b><a href="source/BoxLayout.html#cfg-Ext.layout.BoxLayout-padding">padding</a></b> : String<div class="mdesc">Defaults to <tt>'0'</tt>. Sets the padding to be applied to all child items managed by this
container's layout.</div></td><td class="msource"><a href="output/Ext.layout.BoxLayout.html#padding" ext:member="#padding" ext:cls="Ext.layout.BoxLayout">BoxLayout</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-renderHidden"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-renderHidden">renderHidden</a></b> : Boolean<div class="mdesc">True to hide each contained item on render (defaults to false).</div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#renderHidden" ext:member="#renderHidden" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.VBoxLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#fieldTpl" ext:member="#fieldTpl" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.VBoxLayout-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.layout.VBoxLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>