<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.DataProxy-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.DataProxy-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.DataProxy-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.data.DataProxy-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.DataProxy"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">DataProxy</pre></div><h1>Class <a href="source/DataProxy.html#cls-Ext.data.DataProxy">Ext.data.DataProxy</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DataProxy.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DataProxy.html#cls-Ext.data.DataProxy">DataProxy</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.data.DirectProxy.html" ext:cls="Ext.data.DirectProxy">DirectProxy</a>,&#13;<a href="output/Ext.data.HttpProxy.html" ext:cls="Ext.data.HttpProxy">HttpProxy</a>,&#13;<a href="output/Ext.data.MemoryProxy.html" ext:cls="Ext.data.MemoryProxy">MemoryProxy</a>,&#13;<a href="output/Ext.data.ScriptTagProxy.html" ext:cls="Ext.data.ScriptTagProxy">ScriptTagProxy</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable" ext:member="">Observable</a></td></tr></table><div class="description"><p>Abstract base class for implementations which provide retrieval of unformatted data objects.
This class is intended to be extended and should not be created directly. For existing implementations,
see <a href="output/Ext.data.DirectProxy.html" ext:cls="Ext.data.DirectProxy">Ext.data.DirectProxy</a>, <a href="output/Ext.data.HttpProxy.html" ext:cls="Ext.data.HttpProxy">Ext.data.HttpProxy</a>, <a href="output/Ext.data.ScriptTagProxy.html" ext:cls="Ext.data.ScriptTagProxy">Ext.data.ScriptTagProxy</a> and
<a href="output/Ext.data.MemoryProxy.html" ext:cls="Ext.data.MemoryProxy">Ext.data.MemoryProxy</a>.</p>
<p>DataProxy implementations are usually used in conjunction with an implementation of <a href="output/Ext.data.DataReader.html" ext:cls="Ext.data.DataReader">Ext.data.DataReader</a>
(of the appropriate type which knows how to parse the data object) to provide a block of
<a href="output/Ext.data.Records.html" ext:cls="Ext.data.Records">Ext.data.Records</a> to an <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>.</p>
<p>The parameter to a DataProxy constructor may be an <a href="output/Ext.data.Connection.html" ext:cls="Ext.data.Connection">Ext.data.Connection</a> or can also be the
config object to an <a href="output/Ext.data.Connection.html" ext:cls="Ext.data.Connection">Ext.data.Connection</a>.</p>
<p>Custom implementations must implement either the <code><b>doRequest</b></code> method (preferred) or the
<code>load</code> method (deprecated). See
<a href="output/Ext.data.HttpProxy.html" ext:cls="Ext.data.HttpProxy">Ext.data.HttpProxy</a>.<a href="output/Ext.data.HttpProxy.html#Ext.data.HttpProxy-doRequest" ext:member="doRequest" ext:cls="Ext.data.HttpProxy">doRequest</a> or
<a href="output/Ext.data.HttpProxy.html" ext:cls="Ext.data.HttpProxy">Ext.data.HttpProxy</a>.<a href="output/Ext.data.HttpProxy.html#Ext.data.HttpProxy-load" ext:member="load" ext:cls="Ext.data.HttpProxy">load</a> for additional details.</p>
<p><b><u>Example 1</u></b></p>
<pre><code>proxy: <b>new</b> Ext.data.ScriptTagProxy({
    <a href="output/Ext.data.Connection.html#Ext.data.Connection-url" ext:member="url" ext:cls="Ext.data.Connection">url</a>: <em>'http:<i>//extjs.com/forum/topics-remote.php'</em>
</i>
}),</code></pre>
<p><b><u>Example 2</u></b></p>
<pre><code>proxy : <b>new</b> Ext.data.HttpProxy({
    <a href="output/Ext.data.Connection.html#Ext.data.Connection-method" ext:member="method" ext:cls="Ext.data.Connection">method</a>: <em>'GET'</em>,
    <a href="output/Ext.data.HttpProxy.html#Ext.data.HttpProxy-prettyUrls" ext:member="prettyUrls" ext:cls="Ext.data.HttpProxy">prettyUrls</a>: false,
    <a href="output/Ext.data.Connection.html#Ext.data.Connection-url" ext:member="url" ext:cls="Ext.data.Connection">url</a>: <em>'local/<b>default</b>.php'</em>, <i>// see options parameter <b>for</b> <a href="output/Ext.Ajax.html#Ext.Ajax-request" ext:member="request" ext:cls="Ext.Ajax">Ext.Ajax.request</a>
</i>
    <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-api" ext:member="api" ext:cls="Ext.data.DataProxy">api</a>: {
        <i>// all actions except the following will use above url
</i>
        create  : <em>'local/<b>new</b>.php'</em>,
        update  : <em>'local/update.php'</em>
    }
}),</code></pre></div><div class="hr"></div><a id="Ext.data.DataProxy-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-api"></a><b><a href="source/DataProxy.html#cfg-Ext.data.DataProxy-api">api</a></b> : Object<div class="mdesc"><div class="short">Specific urls to call on CRUD action methods "read", "create", "update" and "destroy".&#13;
Defaults to:api: {&#13;
    read ...</div><div class="long">Specific urls to call on CRUD action methods "read", "create", "update" and "destroy".
Defaults to:<pre><code>api: {
    read    : undefined,
    create  : undefined,
    update  : undefined,
    destroy : undefined
}</code></pre>
<p>If the specific URL for a given CRUD action is undefined, the CRUD action request
will be directed to the configured <tt><a href="output/Ext.data.Connection.html#Ext.data.Connection-url" ext:member="url" ext:cls="Ext.data.Connection">url</a></tt>.</p>
<br><p><b>Note</b>: To modify the URL for an action dynamically the appropriate API
property should be modified before the action is requested using the corresponding before
action event.  For example to modify the URL associated with the load action:
<pre><code><i>// modify the url <b>for</b> the action
</i>
myStore.on({
    beforeload: {
        fn: <b>function</b> (store, options) {
            <i>// use <tt><a href="output/Ext.data.HttpProxy.html#Ext.data.HttpProxy-setUrl" ext:member="setUrl" ext:cls="Ext.data.HttpProxy">setUrl</a></tt> to change the URL <b>for</b> *just* this request.
</i>
            store.proxy.setUrl(<em>'changed1.php'</em>);

            <i>// set optional second parameter to true to make this URL change permanent, applying this URL <b>for</b> all subsequent requests.
</i>
            store.proxy.setUrl(<em>'changed1.php'</em>, true);

            <i>// manually set the <b>private</b> connection URL.  <b>Warning:</b>  Accessing the private URL property like should be avoided.  Please use the public
</i>
            <i>// method <tt><a href="output/Ext.data.HttpProxy.html#Ext.data.HttpProxy-setUrl" ext:member="setUrl" ext:cls="Ext.data.HttpProxy">setUrl</a></tt> instead, shown above.  It should be noted that changing the URL like
</i>
            <i>// this will affect the URL <b>for</b> just this request.  Subsequent requests will use the API or URL defined <b>in</b> your initial
</i>
            <i>// proxy configuration.
</i>
            store.proxy.conn.url = <em>'changed1.php'</em>;

            <i>// proxy URL will be superseded by API (only <b>if</b> proxy created to use ajax):
</i>
            <i>// It should be noted that proxy API changes are permanent and will be used <b>for</b> all subsequent requests.
</i>
            store.proxy.api.load = <em>'changed2.php'</em>;

            <i>// However, altering the proxy API should be done using the public method <tt><a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-setApi" ext:member="setApi" ext:cls="Ext.data.DataProxy">setApi</a></tt> instead.
</i>
            store.proxy.setApi(<em>'load'</em>, <em>'changed2.php'</em>);

            <i>// Or set the entire API <b>with</b> a config-object.  When using the config-object option, you must redefine the <b>entire</b> API --
</i>
            <i>// not just a specific action of it.
</i>
            store.proxy.setApi({
                read    : <em>'changed_read.php'</em>,
                create  : <em>'changed_create.php'</em>,
                update  : <em>'changed_update.php'</em>,
                destroy : <em>'changed_destroy.php'</em>
            });
        }
    }
});</code></pre>
</p></div></div></td><td class="msource">DataProxy</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-doRequest"></a><b><a href="source/DataProxy.html#cfg-Ext.data.DataProxy-doRequest">doRequest</a></b> : Function<div class="mdesc">Abstract method that should be implemented in all subclasses
(eg: <a href="output/Ext.data.HttpProxy.html#Ext.data.HttpProxy-doRequest" ext:member="doRequest" ext:cls="Ext.data.HttpProxy">HttpProxy.doRequest</a>,
<a href="output/Ext.data.DirectProxy.html#Ext.data.DirectProxy-doRequest" ext:member="doRequest" ext:cls="Ext.data.DirectProxy">DirectProxy.doRequest</a>).</div></td><td class="msource">DataProxy</td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-restful"></a><b><a href="source/DataProxy.html#cfg-Ext.data.DataProxy-restful">restful</a></b> : Boolean<div class="mdesc"><div class="short">Defaults to false.  Set to true to operate in a RESTful manner.&#13;
 Note: this parameter will automatically be set to t...</div><div class="long"><p>Defaults to <tt>false</tt>.  Set to <tt>true</tt> to operate in a RESTful manner.</p>
<br><p> Note: this parameter will automatically be set to <tt>true</tt> if the
<a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> it is plugged into is set to <code>restful: true</code>. If the
Store is RESTful, there is no need to set this option on the proxy.</p>
<br><p>RESTful implementations enable the serverside framework to automatically route
actions sent to one url based upon the HTTP method, for example:
<pre><code>store: <b>new</b> Ext.data.Store({
    restful: true,
    proxy: <b>new</b> Ext.data.HttpProxy({url:<em>'/users'</em>}); <i>// all requests sent to /users
</i>
    ...
)}</code></pre>
There is no <code><a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-api" ext:member="api" ext:cls="Ext.data.DataProxy">api</a></code> specified in the configuration of the proxy,
all requests will be marshalled to a single RESTful url (/users) so the serverside
framework can inspect the HTTP Method and act accordingly:
<pre>
<u>Method</u>   <u>url</u>        <u>action</u>
POST     /users     create
GET      /users     read
PUT      /users/23  update
DESTROY  /users/23  delete
</pre></p></div></div></td><td class="msource">DataProxy</td></tr></tbody></table><a id="Ext.data.DataProxy-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.data.DataProxy-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-DataProxy.Error"></a><b><a href="source/DataProxy.html#method-Ext.data.DataProxy-DataProxy.Error">DataProxy.Error</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Record/Array[Record]/Array&nbsp;</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">DataProxy Error extension.&#13;
constructor</div><div class="long">DataProxy Error extension.
constructor<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc"></div></li><li><code></code> : Record/Array[Record]/Array<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-destroy"></a><b><a href="source/DataProxy.html#method-Ext.data.DataProxy-destroy">destroy</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Destroys the proxy by purging any event listeners and cancelling any active requests.</div><div class="long">Destroys the proxy by purging any event listeners and cancelling any active requests.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-isApiAction"></a><b><a href="source/DataProxy.html#method-Ext.data.DataProxy-isApiAction">isApiAction</a></b>(&nbsp;<code>String&nbsp;[Ext.data.Api.CREATE|READ|UPDATE|DESTROY]}</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the specified action is defined as a unique action in the api-config.&#13;
request.  If all API-actions a...</div><div class="long">Returns true if the specified action is defined as a unique action in the api-config.
request.  If all API-actions are routed to unique urls, the xaction parameter is unecessary.  However, if no api is defined
and all Proxy actions are routed to DataProxy#url, the server-side will require the xaction parameter to perform a switch to
the corresponding code for CRUD action.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>[Ext.data.Api.CREATE|READ|UPDATE|DESTROY]}</code> : String<div class="sub-desc">action</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-load"></a><b><a href="source/DataProxy.html#method-Ext.data.DataProxy-load">load</a></b>(&nbsp;<code>Object&nbsp;params</code>,&nbsp;<code>Object&nbsp;reader</code>,&nbsp;<code>Object&nbsp;callback</code>,&nbsp;<code>Object&nbsp;scope</code>,&nbsp;<code>Object&nbsp;arg</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Deprecated load method using old method signature. See {@doRequest} for preferred method.</div><div class="long"><b>Deprecated</b> load method using old method signature. See {@doRequest} for preferred method.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>params</code> : Object<div class="sub-desc"></div></li><li><code>reader</code> : Object<div class="sub-desc"></div></li><li><code>callback</code> : Object<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc"></div></li><li><code>arg</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-request"></a><b><a href="source/DataProxy.html#method-Ext.data.DataProxy-request">request</a></b>(&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Ext.data.Record/Ext.data.Record[]/null&nbsp;rs</code>,&nbsp;<code>Object&nbsp;params</code>,&nbsp;<code>Ext.data.DataReader&nbsp;reader</code>,&nbsp;<code>Function&nbsp;callback</code>,&nbsp;<code>Object&nbsp;scope</code>,&nbsp;<code>Object&nbsp;options</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">All proxy actions are executed through this method.  Automatically fires the "before" + action event</div><div class="long">All proxy actions are executed through this method.  Automatically fires the "before" + action event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>action</code> : String<div class="sub-desc"></div></li><li><code>rs</code> : Ext.data.Record/Ext.data.Record[]/null<div class="sub-desc">Will be null when action is 'load'</div></li><li><code>params</code> : Object<div class="sub-desc"></div></li><li><code>reader</code> : Ext.data.DataReader<div class="sub-desc"></div></li><li><code>callback</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">Scope with which to call the callback (defaults to the Proxy object)</div></li><li><code>options</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-setApi"></a><b><a href="source/DataProxy.html#method-Ext.data.DataProxy-setApi">setApi</a></b>(&nbsp;<code>String/Object&nbsp;api</code>,&nbsp;<code>String/Function&nbsp;url</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Redefines the Proxy's API or a single action of an API. Can be called with two method signatures.&#13;
If called with an ...</div><div class="long"><p>Redefines the Proxy's API or a single action of an API. Can be called with two method signatures.</p>
<p>If called with an object as the only parameter, the object should redefine the <b>entire</b> API, eg:</p><code><pre>
proxy.setApi({
    read    : <em>'/users/read'</em>,
    create  : <em>'/users/create'</em>,
    update  : <em>'/users/update'</em>,
    destroy : <em>'/users/destroy'</em>
});
</pre></code>
<p>If called with two parameters, the first parameter should be a string specifying the API action to
redefine and the second parameter should be the URL (or function if using DirectProxy) to call for that action, eg:</p><code><pre>
proxy.setApi(Ext.data.Api.actions.read, <em>'/users/<b>new</b>_load_url'</em>);
</pre></code><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>api</code> : String/Object<div class="sub-desc">An API specification object, or the name of an action.</div></li><li><code>url</code> : String/Function<div class="sub-desc">The URL (or function if using DirectProxy) to call for the action.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.data.DataProxy-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-beforeload"></a><b><a href="source/DataProxy.html#event-Ext.data.DataProxy-beforeload">beforeload</a></b> :
                                      (&nbsp;<code>Object&nbsp;this</code>,&nbsp;<code>Object&nbsp;params</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a network request is made to retrieve a data object.</div><div class="long">Fires before a network request is made to retrieve a data object.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Object<div class="sub-desc"></div></li><li><code>params</code> : Object<div class="sub-desc">The params object passed to the <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-request" ext:member="request" ext:cls="Ext.data.DataProxy">request</a> function</div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-beforewrite"></a><b><a href="source/DataProxy.html#event-Ext.data.DataProxy-beforewrite">beforewrite</a></b> :
                                      (&nbsp;<code>DataProxy&nbsp;this</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Record/Array[Record]&nbsp;rs</code>,&nbsp;<code>Object&nbsp;params</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a network request is generated for one of the actions Ext.data.Api.actions.create|update|destroy</div><div class="long">Fires before a network request is generated for one of the actions Ext.data.Api.actions.create|update|destroy<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : DataProxy<div class="sub-desc"></div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|update|destroy]</div></li><li><code>rs</code> : Record/Array[Record]<div class="sub-desc"></div></li><li><code>params</code> : Object<div class="sub-desc">HTTP request-params object.  Edit <code>params</code> to add Http parameters to the request.</div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-exception"></a><b><a href="source/DataProxy.html#event-Ext.data.DataProxy-exception">exception</a></b> :
                                      (&nbsp;<code>DataProxy&nbsp;sender</code>,&nbsp;<code>String&nbsp;type</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;options</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Error&nbsp;e</code>,&nbsp;<code>DataProxy&nbsp;sender</code>,&nbsp;<code>String&nbsp;type</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;options</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Record/Record[]&nbsp;rs</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires if an exception occurs in the Proxy during a remote request.  This event can be fired for one of two reasons:&#13;
...</div><div class="long">Fires if an exception occurs in the Proxy during a remote request.  This event can be fired for one of two reasons:
<ul><li><b>The remote-request failed and the server did not return status === 200</b></li>
<li><b>The remote-request succeeded but the reader could not read the response.</b>  This means the server returned
data, but the configured Reader threw an error while reading the response.  In this case, this event will be
raised and the caught error will be passed along into this event.</li></ul>
This event fires with two different contexts based upon the 2nd parameter <tt>type [remote|response]</tt>.  Note that the
first four parameters are identical between the two contexts -- only the final two parameters differ.
<b>response</b>
If the type of exception is "response", an <b>invalid response</b> from the server was returned, either 404, 500 or the response
meta-data does not match that defined in your DataReader (eg: root, idProperty, successProperty).
The event parameters for this context are:<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>sender</code> : DataProxy<div class="sub-desc"></div></li><li><code>type</code> : String<div class="sub-desc">[response]</div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified (see <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-load" ext:member="load" ext:cls="Ext.data.DataProxy">load</a> for details)</div></li><li><code>response</code> : Object<div class="sub-desc">The raw browser response object (eg: XMLHttpRequest)</div></li><li><code>e</code> : Error<div class="sub-desc">The JavaScript Error object caught if the configured Reader could not read the data.
If the load call returned success: false, this parameter will be null.
<b>remote</b>
If the type of exception is "remote", a <b>valid response</b> was returned from the server having successProperty === false.  This
response might contain an error-message sent from the server.  For example, the user may have failed
authentication/authorization or a database validation error occurred.</div></li><li><code>sender</code> : DataProxy<div class="sub-desc"></div></li><li><code>type</code> : String<div class="sub-desc">[remote]</div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|update|destroy]</div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified (see <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-load" ext:member="load" ext:cls="Ext.data.DataProxy">load</a> for details)</div></li><li><code>response</code> : Object<div class="sub-desc">The decoded response object sent from the server.</div></li><li><code>rs</code> : Record/Record[]<div class="sub-desc">Records from the Store.  This parameter will only exist if the <tt>action</tt> was a <b>write</b> action
(Ext.data.Api.actions.create|update|destroy)
Note that this event is also relayed through <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>, so you can listen for it directly
on any Store instance.</div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-load"></a><b><a href="source/DataProxy.html#event-Ext.data.DataProxy-load">load</a></b> :
                                      (&nbsp;<code>Object&nbsp;this</code>,&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Object&nbsp;arg</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the load method's callback is called.</div><div class="long">Fires before the load method's callback is called.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Object<div class="sub-desc"></div></li><li><code>o</code> : Object<div class="sub-desc">The data object</div></li><li><code>arg</code> : Object<div class="sub-desc">The callback's arg object passed to the <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-request" ext:member="request" ext:cls="Ext.data.DataProxy">request</a> function</div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-loadexception"></a><b><a href="source/DataProxy.html#event-Ext.data.DataProxy-loadexception">loadexception</a></b> :
                                      (&nbsp;<code>Object&nbsp;this</code>,&nbsp;<code>Object&nbsp;options</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Error&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Object<div class="sub-desc"></div></li><li><code>options</code> : Object<div class="sub-desc">The loading options that were specified (see <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-load" ext:member="load" ext:cls="Ext.data.DataProxy">load</a> for details)</div></li><li><code>response</code> : Object<div class="sub-desc">The raw response object (eg: XMLHttpRequest) containing the response data</div></li><li><code>e</code> : Error<div class="sub-desc">The JavaScript Error object caught if the configured Reader could not read the data.
If the load call returned success: false, this parameter will be null.
Note that this event is also relayed through <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a>, so you can listen for it directly</div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.DataProxy-write"></a><b><a href="source/DataProxy.html#event-Ext.data.DataProxy-write">write</a></b> :
                                      (&nbsp;<code>Object&nbsp;this</code>,&nbsp;<code>String&nbsp;action</code>,&nbsp;<code>Object&nbsp;data</code>,&nbsp;<code>Object&nbsp;response</code>,&nbsp;<code>Record/Record{}&nbsp;rs</code>,&nbsp;<code>Object&nbsp;arg</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before the request-callback is called</div><div class="long">Fires before the request-callback is called<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Object<div class="sub-desc"></div></li><li><code>action</code> : String<div class="sub-desc">[Ext.data.Api.actions.create|read|upate|destroy]</div></li><li><code>data</code> : Object<div class="sub-desc">The data object extracted from the server-response</div></li><li><code>response</code> : Object<div class="sub-desc">The decoded response from server</div></li><li><code>rs</code> : Record/Record{}<div class="sub-desc">The records from Store</div></li><li><code>arg</code> : Object<div class="sub-desc">The callback's arg object passed to the <a href="output/Ext.data.DataProxy.html#Ext.data.DataProxy-request" ext:member="request" ext:cls="Ext.data.DataProxy">request</a> function</div></li></ul></div></div></div></td><td class="msource">DataProxy</td></tr></tbody></table></div>