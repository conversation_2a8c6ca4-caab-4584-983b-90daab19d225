<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.chart.CartesianSeries-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.chart.CartesianSeries-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.chart.CartesianSeries-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.chart.CartesianSeries"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.chart.Series.html" ext:member="" ext:cls="Ext.chart.Series">Series</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">CartesianSeries</pre></div><h1>Class <a href="source/Chart.html#cls-Ext.chart.CartesianSeries">Ext.chart.CartesianSeries</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.chart</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Chart.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Chart.html#cls-Ext.chart.CartesianSeries">CartesianSeries</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.chart.BarSeries.html" ext:cls="Ext.chart.BarSeries">BarSeries</a>,&#13;<a href="output/Ext.chart.ColumnSeries.html" ext:cls="Ext.chart.ColumnSeries">ColumnSeries</a>,&#13;<a href="output/Ext.chart.LineSeries.html" ext:cls="Ext.chart.LineSeries">LineSeries</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.chart.Series.html" ext:cls="Ext.chart.Series" ext:member="">Series</a></td></tr></table><div class="description">CartesianSeries class for the charts widget.</div><div class="hr"></div><a id="Ext.chart.CartesianSeries-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Series-displayName"></a><b><a href="source/Chart.html#prop-Ext.chart.Series-displayName">displayName</a></b> : String<div class="mdesc">The human-readable name of the series.</div></td><td class="msource"><a href="output/Ext.chart.Series.html#displayName" ext:member="#displayName" ext:cls="Ext.chart.Series">Series</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.Series-type"></a><b><a href="source/Chart.html#prop-Ext.chart.Series-type">type</a></b> : String<div class="mdesc">The type of series.</div></td><td class="msource"><a href="output/Ext.chart.Series.html#type" ext:member="#type" ext:cls="Ext.chart.Series">Series</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.CartesianSeries-xField"></a><b><a href="source/Chart.html#prop-Ext.chart.CartesianSeries-xField">xField</a></b> : String<div class="mdesc">The field used to access the x-axis value from the items from the data source.</div></td><td class="msource">CartesianSeries</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.chart.CartesianSeries-yField"></a><b><a href="source/Chart.html#prop-Ext.chart.CartesianSeries-yField">yField</a></b> : String<div class="mdesc">The field used to access the y-axis value from the items from the data source.</div></td><td class="msource">CartesianSeries</td></tr></tbody></table><a id="Ext.chart.CartesianSeries-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.chart.CartesianSeries-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>