<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.data.Record-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.data.Record-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.data.Record-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.data.Record"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Record.html#cls-Ext.data.Record">Ext.data.Record</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.data</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Record.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Record.html#cls-Ext.data.Record">Record</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>Instances of this class encapsulate both Record <em>definition</em> information, and Record
<em>value</em> information for use in <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> objects, or any code which needs
to access Records cached in an <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> object.</p>
<p>Constructors for this class are generated by passing an Array of field definition objects to <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">create</a>.
Instances are usually only created by <a href="output/Ext.data.Reader.html" ext:cls="Ext.data.Reader">Ext.data.Reader</a> implementations when processing unformatted data
objects.</p>
<p>Note that an instance of a Record class may only belong to one <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Store</a> at a time.
In order to copy data from one Store to another, use the <a href="output/Ext.data.Record.html#Ext.data.Record-copy" ext:member="copy" ext:cls="Ext.data.Record">copy</a> method to create an exact
copy of the Record, and insert the new instance into the other Store.</p>
<p>When serializing a Record for submission to the server, be aware that it contains many private
properties, and also a reference to its owning Store which in turn holds references to its Records.
This means that a whole Record may not be encoded using <a href="output/Ext.util.JSON.encode.html" ext:cls="Ext.util.JSON.encode">Ext.util.JSON.encode</a>. Instead, use the
<code><a href="output/Ext.data.Record.html#Ext.data.Record-data" ext:member="data" ext:cls="Ext.data.Record">data</a></code> and <code><a href="output/Ext.data.Record.html#Ext.data.Record-id" ext:member="id" ext:cls="Ext.data.Record">id</a></code> properties.</p>
<p>Record objects generated by this constructor inherit all the methods of Ext.data.Record listed below.</p></div><div class="hr"></div><a id="Ext.data.Record-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-data"></a><b><a href="source/Record.html#prop-Ext.data.Record-data">data</a></b> : Object<div class="mdesc"><div class="short">An object hash representing the data for this Record. Every field name in the Record definition
is represented by a p...</div><div class="long">An object hash representing the data for this Record. Every field name in the Record definition
is represented by a property of that name in this object. Note that unless you specified a field
with <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">name</a> "id" in the Record definition, this will <b>not</b> contain
an <tt>id</tt> property.</div></div></td><td class="msource">Record</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-dirty"></a><b><a href="source/Record.html#prop-Ext.data.Record-dirty">dirty</a></b> : Boolean<div class="mdesc">Readonly flag - true if this Record has been modified.</div></td><td class="msource">Record</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-fields"></a><b><a href="source/Record.html#prop-Ext.data.Record-fields">fields</a></b> : Ext.util.MixedCollection<div class="mdesc"><div class="short">This property is stored in the Record definition's prototype
A MixedCollection containing the defined Fields for this...</div><div class="long"><p><b>This property is stored in the Record definition's <u>prototype</u></b></p>
A MixedCollection containing the defined <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a>s for this Record.  Read-only.</div></div></td><td class="msource">Record</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-id"></a><b><a href="source/Record.html#prop-Ext.data.Record-id">id</a></b> : Object<div class="mdesc">The unique ID of the Record <a href="output/Ext.data.Record.html#Ext.data.Record-Record" ext:member="Record" ext:cls="Ext.data.Record">as specified at construction time</a>.</div></td><td class="msource">Record</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-modified"></a><b><a href="source/Record.html#prop-Ext.data.Record-modified">modified</a></b> : Object<div class="mdesc"><div class="short">This object contains a key and value storing the original values of all modified
fields or is null if no fields have ...</div><div class="long">This object contains a key and value storing the original values of all modified
fields or is null if no fields have been modified.</div></div></td><td class="msource">Record</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-phantom"></a><b><a href="source/Record.html#prop-Ext.data.Record-phantom">phantom</a></b> : Boolean<div class="mdesc"><div class="short">false when the record does not yet exist in a server-side database (see
markDirty).  Any record which has a real data...</div><div class="long"><tt>false</tt> when the record does not yet exist in a server-side database (see
<a href="output/Ext.data.Record.html#Ext.data.Record-markDirty" ext:member="markDirty" ext:cls="Ext.data.Record">markDirty</a>).  Any record which has a real database pk set as its id property
is NOT a phantom -- it's real.</div></div></td><td class="msource">Record</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-store"></a><b><a href="source/Record.html#prop-Ext.data.Record-store">store</a></b> : Ext.data.Store<div class="mdesc">The <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> to which this Record belongs.</div></td><td class="msource">Record</td></tr></tbody></table><a id="Ext.data.Record-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-Record"></a><b><a href="source/Record.html#cls-Ext.data.Record">Record</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;data</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;id</code>]</span>&nbsp;)
    <div class="mdesc"><div class="short">This constructor should not be used to create Record objects. Instead, use create to
generate a subclass of Ext.data....</div><div class="long">This constructor should not be used to create Record objects. Instead, use <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">create</a> to
generate a subclass of Ext.data.Record configured with information about its constituent fields.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>data</code> : Object<div class="sub-desc">(Optional) An object, the properties of which provide values for the new Record's
fields. If not specified the <code><a href="output/Ext.data.Field.html#Ext.data.Field-defaultValue" ext:member="defaultValue" ext:cls="Ext.data.Field">defaultValue</a></code>
for each field will be assigned.</div></li><li><code>id</code> : Object<div class="sub-desc">(Optional) The id of the Record. This id should be unique, and is used by the
<a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> object which owns the Record to index its collection of Records. If
an <code>id</code> is not specified a <b><code><a href="output/Ext.data.Record.html#Ext.data.Record-phantom" ext:member="phantom" ext:cls="Ext.data.Record">phantom</a></code></b> Record will be created
with an <a href="output/Ext.data.Record.html#Ext.data.Record-Record.id" ext:member="Record.id" ext:cls="Ext.data.Record">automatically generated id</a>.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-Record.id"></a><b><a href="source/Record.html#method-Ext.data.Record-Record.id">Record.id</a></b>(&nbsp;<code>Record&nbsp;rec</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Generates a sequential id. This method is typically called when a record is created
and no id has been specified. The...</div><div class="long">Generates a sequential id. This method is typically called when a record is <a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">create</a>d
and <a href="output/Ext.data.Record.html#Ext.data.Record-Record" ext:member="Record" ext:cls="Ext.data.Record">no id has been specified</a>. The returned id takes the form:
<tt>&#123;PREFIX}-&#123;AUTO_ID}</tt>.<div class="mdetail-params"><ul>
<li><b><tt>PREFIX</tt></b> : String<p class="sub-desc"><tt>Ext.data.Record.PREFIX</tt>
(defaults to <tt>'ext-record'</tt>)</p></li>
<li><b><tt>AUTO_ID</tt></b> : String<p class="sub-desc"><tt>Ext.data.Record.AUTO_ID</tt>
(defaults to <tt>1</tt> initially)</p></li>
</ul></div><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>rec</code> : Record<div class="sub-desc">The record being created.  The record does not exist, it's a <a href="output/Ext.data.Record.html#Ext.data.Record-phantom" ext:member="phantom" ext:cls="Ext.data.Record">phantom</a>.</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">auto-generated string id, &lt;tt&gt;"ext-record-i++'&lt;/tt&gt;;</div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-beginEdit"></a><b><a href="source/Record.html#method-Ext.data.Record-beginEdit">beginEdit</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Begin an edit. While in edit mode, no events are relayed to the containing store.</div><div class="long">Begin an edit. While in edit mode, no events are relayed to the containing store.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-cancelEdit"></a><b><a href="source/Record.html#method-Ext.data.Record-cancelEdit">cancelEdit</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Cancels all changes made in the current edit operation.</div><div class="long">Cancels all changes made in the current edit operation.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-commit"></a><b><a href="source/Record.html#method-Ext.data.Record-commit">commit</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;silent</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Usually called by the Ext.data.Store which owns the Record.
Commits all changes made to the Record since either creat...</div><div class="long">Usually called by the <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> which owns the Record.
Commits all changes made to the Record since either creation, or the last commit operation.
<p>
Developers should subscribe to the <a href="output/Ext.data.Store.html#Ext.data.Store-update" ext:member="update" ext:cls="Ext.data.Store">Ext.data.Store.update</a> event to have their code notified
of commit operations.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>silent</code> : Boolean<div class="sub-desc">(optional) True to skip notification of the owning store of the change (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-copy"></a><b><a href="source/Record.html#method-Ext.data.Record-copy">copy</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;id</code>]</span>&nbsp;)
    :
                                        Record<div class="mdesc"><div class="short">Creates a copy of this Record.</div><div class="long">Creates a copy of this Record.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">(optional) A new Record id, defaults to <a href="output/Ext.data.Record.html#Ext.data.Record-Record.id" ext:member="Record.id" ext:cls="Ext.data.Record">autogenerating an id</a></div></li></ul><strong>Returns:</strong><ul><li><code>Record</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-create"></a><b><a href="source/Record.html#method-Ext.data.Record-create">create</a></b>(&nbsp;<code>Array&nbsp;o</code>&nbsp;)
    :
                                        function<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Generate a constructor for a specific Record layout.</div><div class="long">&lt;static&gt;&nbsp;Generate a constructor for a specific Record layout.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Array<div class="sub-desc">An Array of <b><a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">Field</a></b> definition objects.
The constructor generated by this method may be used to create new Record instances. The data
object must contain properties named after the <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">field</a>
<b><tt><a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">Ext.data.Field.name</a>s</tt></b>.  Example usage:<pre><code><i>// create a Record constructor from a description of the fields</i>
<b>var</b> TopicRecord = Ext.data.Record.create([ <i>// creates a subclass of Ext.data.Record</i>
    {<a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">name</a>: <em>'title'</em>, <a href="output/Ext.data.Field.html#Ext.data.Field-mapping" ext:member="mapping" ext:cls="Ext.data.Field">mapping</a>: <em>'topic_title'</em>},
    {name: <em>'author'</em>, mapping: <em>'username'</em>, allowBlank: false},
    {name: <em>'totalPosts'</em>, mapping: <em>'topic_replies'</em>, type: <em>'int'</em>},
    {name: <em>'lastPost'</em>, mapping: <em>'post_time'</em>, type: <em>'date'</em>},
    {name: <em>'lastPoster'</em>, mapping: <em>'user2'</em>},
    {name: <em>'excerpt'</em>, mapping: <em>'post_text'</em>, allowBlank: false},
    <i>// In the simplest <b>case</b>, <b>if</b> no properties other than <tt>name</tt> are required,</i>
    <i>// a field definition may consist of just a String <b>for</b> the field name.</i>
    <em>'signature'</em>
]);

<i>// create Record instance</i>
<b>var</b> myNewRecord = <b>new</b> TopicRecord(
    {
        title: <em>'Do my job please'</em>,
        author: <em>'noobie'</em>,
        totalPosts: 1,
        lastPost: <b>new</b> Date(),
        lastPoster: <em>'Animal'</em>,
        excerpt: <em>'No way dude!'</em>,
        signature: <em>''</em>
    },
    id <i>// optionally specify the id of the record otherwise <a href="output/Ext.data.Record.html#Ext.data.Record-Record.id" ext:member="Record.id" ext:cls="Ext.data.Record">one is auto-assigned</a></i>
);
myStore.<a href="output/Ext.data.Store.html#Ext.data.Store-add" ext:member="add" ext:cls="Ext.data.Store">add</a>(myNewRecord);</code></pre></div></li></ul><strong>Returns:</strong><ul><li><code>function</code><div class="sub-desc">A constructor which is used to create new Records according
to the definition. The constructor has the same signature as {@link #Ext.data.Record}.</div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-endEdit"></a><b><a href="source/Record.html#method-Ext.data.Record-endEdit">endEdit</a></b>()
    :
                                        void<div class="mdesc"><div class="short">End an edit. If any data was modified, the containing store is notified.</div><div class="long">End an edit. If any data was modified, the containing store is notified.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-get"></a><b><a href="source/Record.html#method-Ext.data.Record-get">get</a></b>(&nbsp;<code>String&nbsp;name</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Get the value of the named field.</div><div class="long">Get the value of the <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">named field</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">name of the field</a> to get the value of.</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">The value of the field.</div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-getChanges"></a><b><a href="source/Record.html#method-Ext.data.Record-getChanges">getChanges</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Gets a hash of only the fields that have been modified since this Record was created or commited.</div><div class="long">Gets a hash of only the fields that have been modified since this Record was created or commited.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-isModified"></a><b><a href="source/Record.html#method-Ext.data.Record-isModified">isModified</a></b>(&nbsp;<code>String&nbsp;fieldName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed field name has been modified since the load or last commit.</div><div class="long">Returns <tt>true</tt> if the passed field name has been <a href="output/Ext.data.Record.html#Ext.data.Record-modified" ext:member="modified" ext:cls="Ext.data.Record">modified</a> since the load or last commit.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fieldName</code> : String<div class="sub-desc"><a href="output/Ext.data.Field.{@link.html" ext:cls="Ext.data.Field.{@link">Ext.data.Field#name</a></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-isValid"></a><b><a href="source/Record.html#method-Ext.data.Record-isValid">isValid</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">isValid
By default returns false if any field within the
record configured with Ext.data.Field.allowBlank = false ret...</div><div class="long">isValid
By default returns <tt>false</tt> if any <a href="output/Ext.data.Field.html" ext:cls="Ext.data.Field">field</a> within the
record configured with <tt><a href="output/Ext.data.Field.html#Ext.data.Field-allowBlank" ext:member="allowBlank" ext:cls="Ext.data.Field">Ext.data.Field.allowBlank</a> = false</tt> returns
<tt>true</tt> from an <a href="output/Ext.html" ext:cls="Ext">Ext</a>.<a href="output/Ext.html#Ext-isEmpty" ext:member="isEmpty" ext:cls="Ext">isempty</a> test.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-markDirty"></a><b><a href="source/Record.html#method-Ext.data.Record-markDirty">markDirty</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Marks all fields as dirty.  Useful when adding phantom
records to a grid which have not yet been inserted on the serv...</div><div class="long">Marks all fields as <tt><a href="output/Ext.data.Record.html#Ext.data.Record-dirty" ext:member="dirty" ext:cls="Ext.data.Record">dirty</a></tt>.  Useful when adding <tt><a href="output/Ext.data.Record.html#Ext.data.Record-phantom" ext:member="phantom" ext:cls="Ext.data.Record">phantom</a></tt>
records to a grid which have not yet been inserted on the serverside.  Marking a new record
<tt><a href="output/Ext.data.Record.html#Ext.data.Record-dirty" ext:member="dirty" ext:cls="Ext.data.Record">dirty</a></tt> causes the phantom to be returned by <a href="output/Ext.data.Store.html#Ext.data.Store-getModifiedRecords" ext:member="getModifiedRecords" ext:cls="Ext.data.Store">Ext.data.Store.getModifiedRecords</a>
where it will have a create action composed for it.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-reject"></a><b><a href="source/Record.html#method-Ext.data.Record-reject">reject</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;silent</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Usually called by the Ext.data.Store which owns the Record.
Rejects all changes made to the Record since either creat...</div><div class="long">Usually called by the <a href="output/Ext.data.Store.html" ext:cls="Ext.data.Store">Ext.data.Store</a> which owns the Record.
Rejects all changes made to the Record since either creation, or the last commit operation.
Modified fields are reverted to their original values.
<p>
Developers should subscribe to the <a href="output/Ext.data.Store.html#Ext.data.Store-update" ext:member="update" ext:cls="Ext.data.Store">Ext.data.Store.update</a> event to have their code notified
of reject operations.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>silent</code> : Boolean<div class="sub-desc">(optional) True to skip notification of the owning store of the change (defaults to false)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Record-set"></a><b><a href="source/Record.html#method-Ext.data.Record-set">set</a></b>(&nbsp;<code>String&nbsp;name</code>,&nbsp;<code>Object&nbsp;value</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Set the named field to the specified value.  For example:
// record has a field named 'firstname'
var Employee = Ext....</div><div class="long">Set the <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">named field</a> to the specified value.  For example:
<pre><code><i>// record has a field named <em>'firstname'</em></i>
<b>var</b> Employee = Ext.data.Record.<a href="output/Ext.data.Record.html#Ext.data.Record-create" ext:member="create" ext:cls="Ext.data.Record">create</a>([
    {name: <em>'firstname'</em>},
    ...
]);

<i>// update the 2nd record <b>in</b> the store:</i>
<b>var</b> rec = myStore.<a href="output/Ext.data.Store.html#Ext.data.Store-getAt" ext:member="getAt" ext:cls="Ext.data.Store">getAt</a>(1);

<i>// set the value (shows dirty flag):</i>
rec.set(<em>'firstname'</em>, <em>'Betty'</em>);

<i>// commit the change (removes dirty flag):</i>
rec.<a href="output/Ext.data.Record.html#Ext.data.Record-commit" ext:member="commit" ext:cls="Ext.data.Record">commit</a>();

<i>// update the record <b>in</b> the store, bypass setting dirty flag,</i>
<i>// and <b>do</b> not store the change <b>in</b> the <a href="output/Ext.data.Store.html#Ext.data.Store-getModifiedRecords" ext:member="getModifiedRecords" ext:cls="Ext.data.Store">modified records</a></i>
rec.<a href="output/Ext.data.Record.html#Ext.data.Record-data" ext:member="data" ext:cls="Ext.data.Record">data</a>[<em>'firstname'</em>] = <em>'Wilma'</em>); <i>// updates record, but not the view</i>
rec.<a href="output/Ext.data.Record.html#Ext.data.Record-commit" ext:member="commit" ext:cls="Ext.data.Record">commit</a>(); <i>// updates the view</i></code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>name</code> : String<div class="sub-desc">The <a href="output/Ext.data.Field.html#Ext.data.Field-name" ext:member="name" ext:cls="Ext.data.Field">name of the field</a> to set.</div></li><li><code>value</code> : Object<div class="sub-desc">The value to set the field to.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Record</td></tr></tbody></table><a id="Ext.data.Record-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>