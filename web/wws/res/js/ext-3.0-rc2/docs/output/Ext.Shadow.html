<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Shadow-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Shadow-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Shadow-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.Shadow-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Shadow"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Shadow.html#cls-Ext.Shadow">Ext.Shadow</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Shadow.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Shadow.html#cls-Ext.Shadow">Shadow</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Simple class that can provide a shadow effect for any element.  Note that the element MUST be absolutely positioned,
and the shadow does not provide any shimming.  This should be used only in simple cases -- for more advanced
functionality that can also provide the same shadow effect, see the <a href="output/Ext.Layer.html" ext:cls="Ext.Layer">Ext.Layer</a> class.</div><div class="hr"></div><a id="Ext.Shadow-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-mode"></a><b><a href="source/Shadow.html#cfg-Ext.Shadow-mode">mode</a></b> : String<div class="mdesc"><div class="short">The shadow display mode.  Supports the following options:&lt;div class="mdetail-params"&gt;
sides : Shadow displays on both...</div><div class="long">The shadow display mode.  Supports the following options:<div class="mdetail-params"><ul>
<li><b><tt>sides</tt></b> : Shadow displays on both sides and bottom only</li>
<li><b><tt>frame</tt></b> : Shadow displays equally on all four sides</li>
<li><b><tt>drop</tt></b> : Traditional bottom-right drop shadow</li>
</ul></div></div></div></td><td class="msource">Shadow</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-offset"></a><b><a href="source/Shadow.html#cfg-Ext.Shadow-offset">offset</a></b> : String<div class="mdesc">The number of pixels to offset the shadow from the element (defaults to <tt>4</tt>)</div></td><td class="msource">Shadow</td></tr></tbody></table><a id="Ext.Shadow-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.Shadow-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-Shadow"></a><b><a href="source/Shadow.html#cls-Ext.Shadow">Shadow</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    <div class="mdesc"><div class="short">Create a new Shadow</div><div class="long">Create a new Shadow<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Shadow</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-hide"></a><b><a href="source/Shadow.html#method-Ext.Shadow-hide">hide</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Hides this shadow</div><div class="long">Hides this shadow<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Shadow</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-isVisible"></a><b><a href="source/Shadow.html#method-Ext.Shadow-isVisible">isVisible</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns true if the shadow is visible, else false</div><div class="long">Returns true if the shadow is visible, else false<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Shadow</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-realign"></a><b><a href="source/Shadow.html#method-Ext.Shadow-realign">realign</a></b>(&nbsp;<code>Number&nbsp;left</code>,&nbsp;<code>Number&nbsp;top</code>,&nbsp;<code>Number&nbsp;width</code>,&nbsp;<code>Number&nbsp;height</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Direct alignment when values are already available. Show must be called at least once before
calling this method to e...</div><div class="long">Direct alignment when values are already available. Show must be called at least once before
calling this method to ensure it is initialized.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>left</code> : Number<div class="sub-desc">The target element left position</div></li><li><code>top</code> : Number<div class="sub-desc">The target element top position</div></li><li><code>width</code> : Number<div class="sub-desc">The target element width</div></li><li><code>height</code> : Number<div class="sub-desc">The target element height</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Shadow</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-setZIndex"></a><b><a href="source/Shadow.html#method-Ext.Shadow-setZIndex">setZIndex</a></b>(&nbsp;<code>Number&nbsp;zindex</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Adjust the z-index of this shadow</div><div class="long">Adjust the z-index of this shadow<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>zindex</code> : Number<div class="sub-desc">The new z-index</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Shadow</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Shadow-show"></a><b><a href="source/Shadow.html#method-Ext.Shadow-show">show</a></b>(&nbsp;<code>Mixed&nbsp;targetEl</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Displays the shadow under the target element</div><div class="long">Displays the shadow under the target element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>targetEl</code> : Mixed<div class="sub-desc">The id or element under which the shadow should display</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Shadow</td></tr></tbody></table><a id="Ext.Shadow-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>