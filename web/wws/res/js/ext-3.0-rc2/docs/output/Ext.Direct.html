<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Direct-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Direct-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Direct-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.Direct-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Direct"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">Direct</pre></div><h1>Class <a href="source/Direct.html#cls-Ext.Direct">Ext.Direct</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Direct.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Direct.html#cls-Ext.Direct">Direct</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable" ext:member="">Observable</a></td></tr></table><div class="description"><p>Provides a single interface to facilitate data communication between the client and server.</p>
<p>Custom events from the server may be handled by the client by adding listeners, for example:</p>
<pre><code>{<em>"type"</em>:<em>"event"</em>,<em>"name"</em>:<em>"message"</em>,<em>"data"</em>:<em>"Successfully polled at: 11:19:30 am"</em>}

<i>// add a handler <b>for</b> a <em>'message'</em> event sent by the server 
</i>
Ext.Direct.on(<em>'message'</em>, <b>function</b>(e){
    out.append(String.format(<em>'<p><i>{0}</i></p>'</em>, e.data));
            out.el.scrollTo(<em>'t'</em>, 100000, true);
});</code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.Direct-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.Direct-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Direct-eventTypes"></a><b><a href="source/Direct.html#prop-Ext.Direct-eventTypes">eventTypes</a></b> : Object&#13;
Each event type implements a getData() method. The default event types are:&#13;
&lt;div class="mdetail-params"&gt;&lt;ul&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;event&lt;/tt&gt;&lt;/b&gt; : Ext.Direct.Event&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;exception&lt;/tt&gt;&lt;/b&gt; : Ext.Direct.ExceptionEvent&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;rpc&lt;/tt&gt;&lt;/b&gt; : Ext.Direct.RemotingEvent&lt;/li&gt;&#13;
&lt;/ul&gt;&lt;/div&gt;<div class="mdesc"></div></td><td class="msource">Direct</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Direct-exceptions"></a><b><a href="source/Direct.html#prop-Ext.Direct-exceptions">exceptions</a></b> : Object&#13;
Four types of possible exceptions which can occur:&#13;
&lt;div class="mdetail-params"&gt;&lt;ul&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;Ext.Direct.exceptions.TRANSPORT&lt;/tt&gt;&lt;/b&gt; : 'xhr'&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;Ext.Direct.exceptions.PARSE&lt;/tt&gt;&lt;/b&gt; : 'parse'&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;Ext.Direct.exceptions.LOGIN&lt;/tt&gt;&lt;/b&gt; : 'login'&lt;/li&gt;&#13;
&lt;li&gt;&lt;b&gt;&lt;tt&gt;Ext.Direct.exceptions.SERVER&lt;/tt&gt;&lt;/b&gt; : 'exception'&lt;/li&gt;&#13;
&lt;/ul&gt;&lt;/div&gt;<div class="mdesc"></div></td><td class="msource">Direct</td></tr></tbody></table><a id="Ext.Direct-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Direct-addProvider"></a><b><a href="source/Direct.html#method-Ext.Direct-addProvider">addProvider</a></b>(&nbsp;<code>Object/Array&nbsp;provider</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Adds an Ext.Direct Provider and creates the proxy or stub methods to execute server-side methods.&#13;
If the provider is...</div><div class="long">Adds an Ext.Direct Provider and creates the proxy or stub methods to execute server-side methods.
If the provider is not already connected, it will auto-connect.
<pre><code><b>var</b> pollProv = <b>new</b> Ext.direct.PollingProvider({
    url: <em>'php/poll2.php'</em>
}); 

Ext.Direct.addProvider(
    {
        <em>"type"</em>:<em>"remoting"</em>,       <i>// create a <a href="output/Ext.direct.RemotingProvider.html" ext:cls="Ext.direct.RemotingProvider">Ext.direct.RemotingProvider</a> 
</i>
        <em>"url"</em>:<em>"php\/router.php"</em>, <i>// url to connect to the Ext.Direct server-side router.
</i>
        <em>"actions"</em>:{              <i>// each property within the actions object represents a Class 
</i>
            <em>"TestAction"</em>:[       <i>// array of methods within each server side Class   
</i>
            {
                <em>"name"</em>:<em>"doEcho"</em>, <i>// name of method
</i>
                <em>"len"</em>:1
            },{
                <em>"name"</em>:<em>"multiply"</em>,
                <em>"len"</em>:1
            },{
                <em>"name"</em>:<em>"doForm"</em>,
                <em>"formHandler"</em>:true, <i>// handle form on server <b>with</b> Ext.Direct.Transaction 
</i>
                <em>"len"</em>:1
            }]
        },
        <em>"namespace"</em>:<em>"myApplication"</em>,<i>// namespace to create the Remoting Provider <b>in</b>
</i>
    },{
        type: <em>'polling'</em>, <i>// create a <a href="output/Ext.direct.PollingProvider.html" ext:cls="Ext.direct.PollingProvider">Ext.direct.PollingProvider</a> 
</i>
        url:  <em>'php/poll.php'</em>
    },
    pollProv <i>// reference to previously created instance
</i>
);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>provider</code> : Object/Array<div class="sub-desc">Accepts either an Array of Provider descriptions (an instance
or config object for a Provider) or any number of Provider descriptions as arguments.  Each
Provider description instructs Ext.Direct how to create client-side stub methods.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Direct</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Direct-getProvider"></a><b><a href="source/Direct.html#method-Ext.Direct-getProvider">getProvider</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Retrieve a provider by the&#13;
id specified when the provider is&#13;
added.</div><div class="long">Retrieve a <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">provider</a> by the
<b><tt><a href="output/Ext.direct.Provider.html#Ext.direct.Provider-id" ext:member="id" ext:cls="Ext.direct.Provider">id</a></tt></b> specified when the provider is
<a href="output/Ext.Direct.html#Ext.Direct-addProvider" ext:member="addProvider" ext:cls="Ext.Direct">added</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">Unique identifier assigned to the provider when calling <a href="output/Ext.Direct.html#Ext.Direct-addProvider" ext:member="addProvider" ext:cls="Ext.Direct">addProvider</a></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Direct</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.Direct-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Direct-event"></a><b><a href="source/Direct.html#event-Ext.Direct-event">event</a></b> :
                                      (&nbsp;<code>event&nbsp;e</code>,&nbsp;<code>Ext.direct.Provider&nbsp;provider</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after an event.</div><div class="long">Fires after an event.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : event<div class="sub-desc">The <a href="output/Ext.Direct.html#Ext.Direct-eventTypes" ext:member="eventTypes" ext:cls="Ext.Direct">Ext.Direct.Event type</a> that occurred.</div></li><li><code>provider</code> : Ext.direct.Provider<div class="sub-desc">The <a href="output/Ext.direct.Provider.html" ext:cls="Ext.direct.Provider">Provider</a>.</div></li></ul></div></div></div></td><td class="msource">Direct</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Direct-exception"></a><b><a href="source/Direct.html#event-Ext.Direct-exception">exception</a></b> :
                                      (&nbsp;<code>event&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires after an event exception.</div><div class="long">Fires after an event exception.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>e</code> : event<div class="sub-desc">The <a href="output/Ext.Direct.html#Ext.Direct-eventTypes" ext:member="eventTypes" ext:cls="Ext.Direct">Ext.Direct.Event type</a> that occurred.</div></li></ul></div></div></div></td><td class="msource">Direct</td></tr></tbody></table></div>