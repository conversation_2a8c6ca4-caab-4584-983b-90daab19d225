<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.Updater-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.Updater-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.Updater-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.Updater-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.Updater"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">Updater</pre></div><h1>Class <a href="source/UpdateManager.html#cls-Ext.Updater">Ext.Updater</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">UpdateManager.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/UpdateManager.html#cls-Ext.Updater">Updater</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.util.Observable.html" ext:cls="Ext.util.Observable" ext:member="">Observable</a></td></tr></table><div class="description">Provides AJAX-style update capabilities for Element objects.  Updater can be used to <a href="output/Ext.Updater.html#Ext.Updater-update" ext:member="update" ext:cls="Ext.Updater">update</a>
an <a href="output/Ext.Element.html" ext:cls="Ext.Element">Ext.Element</a> once, or you can use <a href="output/Ext.Updater.html#Ext.Updater-startAutoRefresh" ext:member="startAutoRefresh" ext:cls="Ext.Updater">startAutoRefresh</a> to set up an auto-updating
<a href="output/Ext.Element.html" ext:cls="Ext.Element">Element</a> on a specific interval.<br><br>
Usage:<br>
<pre><code><b>var</b> el = Ext.get(<em>"foo"</em>); <i>// Get Ext.Element object</i>
<b>var</b> mgr = el.getUpdater();
mgr.update({
        url: <em>"http:<i>//myserver.com/index.php"</em>,</i>
        params: {
            param1: <em>"foo"</em>,
            param2: <em>"bar"</em>
        }
});
...
mgr.formUpdate(<em>"myFormId"</em>, <em>"http:<i>//myserver.com/index.php"</em>);</i>
<br>
<i>// or directly (returns the same Updater instance)</i>
<b>var</b> mgr = <b>new</b> Ext.Updater(<em>"myElementId"</em>);
mgr.startAutoRefresh(60, <em>"http:<i>//myserver.com/index.php"</em>);</i>
mgr.on(<em>"update"</em>, myFcnNeedsToKnow);
<br>
<i>// short handed call directly from the element object</i>
Ext.get(<em>"foo"</em>).load({
        url: <em>"bar.php"</em>,
        scripts: true,
        params: <em>"param1=foo&amp;param2=bar"</em>,
        text: <em>"Loading Foo..."</em>
});</code></pre></div><div class="hr"></div><a id="Ext.Updater-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr></tbody></table><a id="Ext.Updater-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-defaultUrl"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-defaultUrl">defaultUrl</a></b> : String<div class="mdesc">Cached url to use for refreshes. Overwritten every time update() is called unless "discardUrl" param is set to true.</div></td><td class="msource">Updater</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-disableCaching"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-disableCaching">disableCaching</a></b> : Boolean<div class="mdesc"><div class="short">Whether to append unique parameter on get request to disable caching (defaults to Ext.Updater.defaults.disableCaching...</div><div class="long">Whether to append unique parameter on get request to disable caching (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-disableCaching" ext:member="disableCaching" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.disableCaching</a>).</div></div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-el"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-el">el</a></b> : Ext.Element<div class="mdesc">The Element object</div></td><td class="msource">Updater</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-formUpdateDelegate"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-formUpdateDelegate">formUpdateDelegate</a></b> : Function<div class="mdesc"><div class="short">Delegate for formUpdate() prebound to "this", use myUpdater.formUpdateDelegate.createCallback(arg1, arg2) to bind arg...</div><div class="long">Delegate for formUpdate() prebound to "this", use myUpdater.formUpdateDelegate.createCallback(arg1, arg2) to bind arguments</div></div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-indicatorText"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-indicatorText">indicatorText</a></b> : String<div class="mdesc">Text for loading indicator (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-indicatorText" ext:member="indicatorText" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.indicatorText</a>).</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-loadScripts"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-loadScripts">loadScripts</a></b> : Boolean<div class="mdesc">True to process scripts in the output (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-loadScripts" ext:member="loadScripts" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.loadScripts</a>).</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-refreshDelegate"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-refreshDelegate">refreshDelegate</a></b> : Function<div class="mdesc">Delegate for refresh() prebound to "this", use myUpdater.refreshDelegate.createCallback(arg1, arg2) to bind arguments</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-renderer"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-renderer">renderer</a></b> : Object<div class="mdesc">The renderer for this Updater (defaults to <a href="output/Ext.Updater.BasicRenderer.html" ext:cls="Ext.Updater.BasicRenderer">Ext.Updater.BasicRenderer</a>).</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-showLoadIndicator"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-showLoadIndicator">showLoadIndicator</a></b> : String<div class="mdesc">Whether to show indicatorText when loading (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-showLoadIndicator" ext:member="showLoadIndicator" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.showLoadIndicator</a>).</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-sslBlankUrl"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-sslBlankUrl">sslBlankUrl</a></b> : String<div class="mdesc">Blank page URL to use with SSL file uploads (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-sslBlankUrl" ext:member="sslBlankUrl" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.sslBlankUrl</a>).</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-timeout"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-timeout">timeout</a></b> : Number<div class="mdesc">Timeout for requests or form posts in seconds (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-timeout" ext:member="timeout" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.timeout</a>).</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-transaction"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-transaction">transaction</a></b> : Object<div class="mdesc">Transaction object of the current executing transaction, or null if there is no active transaction.</div></td><td class="msource">Updater</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-updateDelegate"></a><b><a href="source/UpdateManager.html#prop-Ext.Updater-updateDelegate">updateDelegate</a></b> : Function<div class="mdesc">Delegate for update() prebound to "this", use myUpdater.updateDelegate.createCallback(arg1, arg2) to bind arguments</div></td><td class="msource">Updater</td></tr></tbody></table><a id="Ext.Updater-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-Updater"></a><b><a href="source/UpdateManager.html#cls-Ext.Updater">Updater</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;forceNew</code>]</span>&nbsp;)
    <div class="mdesc"><div class="short">Create new Updater directly.</div><div class="long">Create new Updater directly.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element to update</div></li><li><code>forceNew</code> : Boolean<div class="sub-desc">(optional) By default the constructor checks to see if the passed element already
has an Updater and if it does it returns the same instance. This will skip that check (useful for extending this class).</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-Updater.updateElement"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-Updater.updateElement">Updater.updateElement</a></b>(&nbsp;<code>Mixed&nbsp;el</code>,&nbsp;<code>String&nbsp;url</code>,&nbsp;<span title="Optional" class="optional">[<code>String/Object&nbsp;params</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">&lt;static&gt;&nbsp;Static convenience method. This method is deprecated in favor of el.load({url:'foo.php', ...}).
Usage:
Ext.Updater.up...</div><div class="long">&lt;static&gt;&nbsp;Static convenience method. <b>This method is deprecated in favor of el.load({url:'foo.php', ...})</b>.
Usage:
<pre><code>Ext.Updater.updateElement(<em>"my-div"</em>, <em>"stuff.php"</em>);</code></pre><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Mixed<div class="sub-desc">The element to update</div></li><li><code>url</code> : String<div class="sub-desc">The url</div></li><li><code>params</code> : String/Object<div class="sub-desc">(optional) Url encoded param string or an object of name/value pairs</div></li><li><code>options</code> : Object<div class="sub-desc">(optional) A config object with any of the Updater properties you want to set - for
example: {disableCaching:true, indicatorText: "Loading data..."}</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-abort"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-abort">abort</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Aborts the currently executing transaction, if any.</div><div class="long">Aborts the currently executing transaction, if any.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-formUpdate"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-formUpdate">formUpdate</a></b>(&nbsp;<code>String/HTMLElement&nbsp;form</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;url</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;reset</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Performs an async form post, updating this element with the response. If the form has the attribute
enctype="&lt;a href=...</div><div class="long"><p>Performs an async form post, updating this element with the response. If the form has the attribute
enctype="<a href="http://www.faqs.org/rfcs/rfc2388.html">multipart/form-data</a>", it assumes it's a file upload.
Uses this.sslBlankUrl for SSL file uploads to prevent IE security warning.</p>
<p>File uploads are not performed using normal "Ajax" techniques, that is they are <b>not</b>
performed using XMLHttpRequests. Instead the form is submitted in the standard manner with the
DOM <tt>&lt;form></tt> element temporarily modified to have its
<a href="http://www.w3.org/TR/REC-html40/present/frames.html#adef-target">target</a> set to refer
to a dynamically generated, hidden <tt>&lt;iframe></tt> which is inserted into the document
but removed after the return data has been gathered.</p>
<p>Be aware that file upload packets, sent with the content type <a href="http://www.faqs.org/rfcs/rfc2388.html">multipart/form-data</a>
and some server technologies (notably JEE) may require some custom processing in order to
retrieve parameter names and parameter values from the packet content.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>form</code> : String/HTMLElement<div class="sub-desc">The form Id or form element</div></li><li><code>url</code> : String<div class="sub-desc">(optional) The url to pass the form to. If omitted the action attribute on the form will be used.</div></li><li><code>reset</code> : Boolean<div class="sub-desc">(optional) Whether to try to reset the form after the update</div></li><li><code>callback</code> : Function<div class="sub-desc">(optional) Callback when transaction is complete. The following
parameters are passed:<ul>
<li><b>el</b> : Ext.Element<p class="sub-desc">The Element being updated.</p></li>
<li><b>success</b> : Boolean<p class="sub-desc">True for success, false for failure.</p></li>
<li><b>response</b> : XMLHttpRequest<p class="sub-desc">The XMLHttpRequest which processed the update.</p></li></ul></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-getDefaultRenderer"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-getDefaultRenderer">getDefaultRenderer</a></b>()
    :
                                        void<div class="mdesc"><div class="short">This is an overrideable method which returns a reference to a default
renderer class if none is specified when creati...</div><div class="long">This is an overrideable method which returns a reference to a default
renderer class if none is specified when creating the Ext.Updater.
Defaults to <a href="output/Ext.Updater.BasicRenderer.html" ext:cls="Ext.Updater.BasicRenderer">Ext.Updater.BasicRenderer</a><div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-getEl"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-getEl">getEl</a></b>()
    :
                                        Ext.Element<div class="mdesc"><div class="short">Get the Element this Updater is bound to</div><div class="long">Get the Element this Updater is bound to<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Ext.Element</code><div class="sub-desc">The element</div></li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-getRenderer"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-getRenderer">getRenderer</a></b>()
    :
                                        Object<div class="mdesc"><div class="short">Returns the current content renderer for this Updater. See Ext.Updater.BasicRenderer.render for more details.</div><div class="long">Returns the current content renderer for this Updater. See <a href="output/Ext.Updater.BasicRenderer.html#Ext.Updater.BasicRenderer-render" ext:member="render" ext:cls="Ext.Updater.BasicRenderer">Ext.Updater.BasicRenderer.render</a> for more details.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-isAutoRefreshing"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-isAutoRefreshing">isAutoRefreshing</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Returns true if the Updater is currently set to auto refresh its content (see startAutoRefresh), otherwise false.</div><div class="long">Returns true if the Updater is currently set to auto refresh its content (see <a href="output/Ext.Updater.html#Ext.Updater-startAutoRefresh" ext:member="startAutoRefresh" ext:cls="Ext.Updater">startAutoRefresh</a>), otherwise false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-isUpdating"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-isUpdating">isUpdating</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if an update is in progress, otherwise false.</div><div class="long">Returns true if an update is in progress, otherwise false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-refresh"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-refresh">refresh</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Refresh the element with the last used url or defaultUrl. If there is no url, it returns immediately</div><div class="long">Refresh the element with the last used url or defaultUrl. If there is no url, it returns immediately<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>callback</code> : Function<div class="sub-desc">(optional) Callback when transaction is complete - called with signature (oElement, bSuccess)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-setDefaultUrl"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-setDefaultUrl">setDefaultUrl</a></b>(&nbsp;<code>String/Function&nbsp;defaultUrl</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the default URL used for updates.</div><div class="long">Sets the default URL used for updates.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>defaultUrl</code> : String/Function<div class="sub-desc">The url or a function to call to get the url</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-setRenderer"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-setRenderer">setRenderer</a></b>(&nbsp;<code>Object&nbsp;renderer</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the content renderer for this Updater. See Ext.Updater.BasicRenderer.render for more details.</div><div class="long">Sets the content renderer for this Updater. See <a href="output/Ext.Updater.BasicRenderer.html#Ext.Updater.BasicRenderer-render" ext:member="render" ext:cls="Ext.Updater.BasicRenderer">Ext.Updater.BasicRenderer.render</a> for more details.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>renderer</code> : Object<div class="sub-desc">The object implementing the render() method</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-showLoading"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-showLoading">showLoading</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Display the element's "loading" state. By default, the element is updated with indicatorText. This
method may be over...</div><div class="long">Display the element's "loading" state. By default, the element is updated with <a href="output/Ext.Updater.html#Ext.Updater-indicatorText" ext:member="indicatorText" ext:cls="Ext.Updater">indicatorText</a>. This
method may be overridden to perform a custom action while this Updater is actively updating its contents.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-startAutoRefresh"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-startAutoRefresh">startAutoRefresh</a></b>(&nbsp;<code>Number&nbsp;interval</code>,&nbsp;<span title="Optional" class="optional">[<code>String/Object/Function&nbsp;url</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>String/Object&nbsp;params</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;refreshNow</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Set this element to auto refresh.  Can be canceled by calling stopAutoRefresh.</div><div class="long">Set this element to auto refresh.  Can be canceled by calling <a href="output/Ext.Updater.html#Ext.Updater-stopAutoRefresh" ext:member="stopAutoRefresh" ext:cls="Ext.Updater">stopAutoRefresh</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>interval</code> : Number<div class="sub-desc">How often to update (in seconds).</div></li><li><code>url</code> : String/Object/Function<div class="sub-desc">(optional) The url for this request, a config object in the same format
supported by <a href="output/Ext.Updater.html#Ext.Updater-load" ext:member="load" ext:cls="Ext.Updater">load</a>, or a function to call to get the url (defaults to the last used url).  Note that while
the url used in a load call can be reused by this method, other load config options will not be reused and must be
sepcified as part of a config object passed as this paramter if needed.</div></li><li><code>params</code> : String/Object<div class="sub-desc">(optional) The parameters to pass as either a url encoded string
"&param1=1&param2=2" or as an object {param1: 1, param2: 2}</div></li><li><code>callback</code> : Function<div class="sub-desc">(optional) Callback when transaction is complete - called with signature (oElement, bSuccess)</div></li><li><code>refreshNow</code> : Boolean<div class="sub-desc">(optional) Whether to execute the refresh now, or wait the interval</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-stopAutoRefresh"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-stopAutoRefresh">stopAutoRefresh</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Stop auto refresh on this element.</div><div class="long">Stop auto refresh on this element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-update"></a><b><a href="source/UpdateManager.html#method-Ext.Updater-update">update</a></b>(&nbsp;<code>Object&nbsp;options</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Performs an asynchronous request, updating this element with the response.
If params are specified it uses POST, othe...</div><div class="long">Performs an <b>asynchronous</b> request, updating this element with the response.
If params are specified it uses POST, otherwise it uses GET.<br><br>
<b>Note:</b> Due to the asynchronous nature of remote server requests, the Element
will not have been fully updated when the function returns. To post-process the returned
data, use the callback option, or an <b><tt>update</tt></b> event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>options</code> : Object<div class="sub-desc">A config object containing any of the following options:<ul>
<li>url : <b>String/Function</b><p class="sub-desc">The URL to request or a function which
<i>returns</i> the URL (defaults to the value of <a href="output/Ext.Ajax.html#Ext.Ajax-url" ext:member="url" ext:cls="Ext.Ajax">Ext.Ajax.url</a> if not specified).</p></li>
<li>method : <b>String</b><p class="sub-desc">The HTTP method to
use. Defaults to POST if the <tt>params</tt> argument is present, otherwise GET.</p></li>
<li>params : <b>String/Object/Function</b><p class="sub-desc">The
parameters to pass to the server (defaults to none). These may be specified as a url-encoded
string, or as an object containing properties which represent parameters,
or as a function, which returns such an object.</p></li>
<li>scripts : <b>Boolean</b><p class="sub-desc">If <tt>true</tt>
any &lt;script&gt; tags embedded in the response text will be extracted
and executed (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-loadScripts" ext:member="loadScripts" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.loadScripts</a>). If this option is specified,
the callback will be called <i>after</i> the execution of the scripts.</p></li>
<li>callback : <b>Function</b><p class="sub-desc">A function to
be called when the response from the server arrives. The following
parameters are passed:<ul>
<li><b>el</b> : Ext.Element<p class="sub-desc">The Element being updated.</p></li>
<li><b>success</b> : Boolean<p class="sub-desc">True for success, false for failure.</p></li>
<li><b>response</b> : XMLHttpRequest<p class="sub-desc">The XMLHttpRequest which processed the update.</p></li>
<li><b>options</b> : Object<p class="sub-desc">The config object passed to the update call.</p></li></ul>
</p></li>
<li>scope : <b>Object</b><p class="sub-desc">The scope in which
to execute the callback (The callback's <tt>this</tt> reference.) If the
<tt>params</tt> argument is a function, this scope is used for that function also.</p></li>
<li>discardUrl : <b>Boolean</b><p class="sub-desc">By default, the URL of this request becomes
the default URL for this Updater object, and will be subsequently used in <a href="output/Ext.Updater.html#Ext.Updater-refresh" ext:member="refresh" ext:cls="Ext.Updater">refresh</a>
calls.  To bypass this behavior, pass <tt>discardUrl:true</tt> (defaults to false).</p></li>
<li>timeout : <b>Number</b><p class="sub-desc">The number of seconds to wait for a response before
timing out (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-timeout" ext:member="timeout" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.timeout</a>).</p></li>
<li>text : <b>String</b><p class="sub-desc">The text to use as the innerHTML of the
<a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-indicatorText" ext:member="indicatorText" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.indicatorText</a> div (defaults to 'Loading...').  To replace the entire div, not
just the text, override <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-indicatorText" ext:member="indicatorText" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.indicatorText</a> directly.</p></li>
<li>nocache : <b>Boolean</b><p class="sub-desc">Only needed for GET
requests, this option causes an extra, auto-generated parameter to be appended to the request
to defeat caching (defaults to <a href="output/Ext.Updater.defaults.html#Ext.Updater.defaults-disableCaching" ext:member="disableCaching" ext:cls="Ext.Updater.defaults">Ext.Updater.defaults.disableCaching</a>).</p></li></ul>
<p>
For example:
	<pre><code>um.update({
	    url: <em>"your-url.php"</em>,
	    params: {param1: <em>"foo"</em>, param2: <em>"bar"</em>}, <i>// or a URL encoded string</i>
	    callback: yourFunction,
	    scope: yourObject, <i>//(optional scope)</i>
	    discardUrl: true,
	    nocache: true,
	    text: <em>"Loading..."</em>,
	    timeout: 60,
	    scripts: false <i>// Save time by avoiding RegExp execution.</i>
	});</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Updater</td></tr></tbody></table><a id="Ext.Updater-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-beforeupdate"></a><b><a href="source/UpdateManager.html#event-Ext.Updater-beforeupdate">beforeupdate</a></b> :
                                      (&nbsp;<code>Ext.Element&nbsp;el</code>,&nbsp;<code>String/Object/Function&nbsp;url</code>,&nbsp;<code>String/Object&nbsp;params</code>&nbsp;)
    <div class="mdesc"><div class="short">Fired before an update is made, return false from your handler and the update is cancelled.</div><div class="long">Fired before an update is made, return false from your handler and the update is cancelled.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>el</code> : Ext.Element<div class="sub-desc"></div></li><li><code>url</code> : String/Object/Function<div class="sub-desc"></div></li><li><code>params</code> : String/Object<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-failure"></a><b><a href="source/UpdateManager.html#event-Ext.Updater-failure">failure</a></b> :
                                      (&nbsp;<code>Ext.Element&nbsp;el</code>,&nbsp;<code>Object&nbsp;oResponseObject</code>&nbsp;)
    <div class="mdesc"><div class="short">Fired on update failure.</div><div class="long">Fired on update failure.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>el</code> : Ext.Element<div class="sub-desc"></div></li><li><code>oResponseObject</code> : Object<div class="sub-desc">The response Object</div></li></ul></div></div></div></td><td class="msource">Updater</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.Updater-update"></a><b><a href="source/UpdateManager.html#event-Ext.Updater-update">update</a></b> :
                                      (&nbsp;<code>Ext.Element&nbsp;el</code>,&nbsp;<code>Object&nbsp;oResponseObject</code>&nbsp;)
    <div class="mdesc"><div class="short">Fired after successful update is made.</div><div class="long">Fired after successful update is made.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>el</code> : Ext.Element<div class="sub-desc"></div></li><li><code>oResponseObject</code> : Object<div class="sub-desc">The response Object</div></li></ul></div></div></div></td><td class="msource">Updater</td></tr></tbody></table></div>