<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.dd.Registry-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.dd.Registry-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.dd.Registry-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.dd.Registry"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Registry.html#cls-Ext.dd.Registry">Ext.dd.Registry</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.dd</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Registry.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Registry.html#cls-Ext.dd.Registry">Registry</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Provides easy access to all drag drop components that are registered on a page.  Items can be retrieved either
directly by DOM node id, or by passing in the drag drop event that occurred and looking up the event target.<br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.dd.Registry-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.dd.Registry-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.Registry-getHandle"></a><b><a href="source/Registry.html#method-Ext.dd.Registry-getHandle">getHandle</a></b>(&nbsp;<code>String/HTMLElement&nbsp;id</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the handle registered for a DOM Node by id</div><div class="long">Returns the handle registered for a DOM Node by id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String/HTMLElement<div class="sub-desc">The DOM node or id to look up</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">handle The custom handle data</div></li></ul></div></div></div></td><td class="msource">Registry</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.Registry-getHandleFromEvent"></a><b><a href="source/Registry.html#method-Ext.dd.Registry-getHandleFromEvent">getHandleFromEvent</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns the handle that is registered for the DOM node that is the target of the event</div><div class="long">Returns the handle that is registered for the DOM node that is the target of the event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">The event</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">handle The custom handle data</div></li></ul></div></div></div></td><td class="msource">Registry</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.Registry-getTarget"></a><b><a href="source/Registry.html#method-Ext.dd.Registry-getTarget">getTarget</a></b>(&nbsp;<code>String/HTMLElement&nbsp;id</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns a custom data object that is registered for a DOM node by id</div><div class="long">Returns a custom data object that is registered for a DOM node by id<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String/HTMLElement<div class="sub-desc">The DOM node or id to look up</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data The custom data</div></li></ul></div></div></div></td><td class="msource">Registry</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.Registry-getTargetFromEvent"></a><b><a href="source/Registry.html#method-Ext.dd.Registry-getTargetFromEvent">getTargetFromEvent</a></b>(&nbsp;<code>Event&nbsp;e</code>&nbsp;)
    :
                                        Object<div class="mdesc"><div class="short">Returns a custom data object that is registered for the DOM node that is the target of the event</div><div class="long">Returns a custom data object that is registered for the DOM node that is the target of the event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>e</code> : Event<div class="sub-desc">The event</div></li></ul><strong>Returns:</strong><ul><li><code>Object</code><div class="sub-desc">data The custom data</div></li></ul></div></div></div></td><td class="msource">Registry</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.Registry-register"></a><b><a href="source/Registry.html#method-Ext.dd.Registry-register">register</a></b>(&nbsp;<code>String/HTMLElement)&nbsp;element</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;data</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Resgister a drag drop element</div><div class="long">Resgister a drag drop element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : String/HTMLElement)<div class="sub-desc">The id or DOM node to register</div></li><li><code>data</code> : Object<div class="sub-desc">(optional) An custom data object that will be passed between the elements that are involved
in drag drop operations.  You can populate this object with any arbitrary properties that your own code
knows how to interpret, plus there are some specific properties known to the Registry that should be
populated in the data object (if applicable):
<pre>
Value      Description<br />
---------  ------------------------------------------<br />
handles    Array of DOM nodes that trigger dragging<br />
           for the element being registered<br />
isHandle   True if the element passed in triggers<br />
           dragging itself, else false
</pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Registry</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.dd.Registry-unregister"></a><b><a href="source/Registry.html#method-Ext.dd.Registry-unregister">unregister</a></b>(&nbsp;<code>String/HTMLElement)&nbsp;element</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Unregister a drag drop element</div><div class="long">Unregister a drag drop element<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>element</code> : String/HTMLElement)<div class="sub-desc">The id or DOM node to unregister</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">Registry</td></tr></tbody></table><a id="Ext.dd.Registry-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>