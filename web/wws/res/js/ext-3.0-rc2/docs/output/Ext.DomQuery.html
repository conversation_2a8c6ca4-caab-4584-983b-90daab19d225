<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.DomQuery-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.DomQuery-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.DomQuery-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.DomQuery"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/DomQuery.html#cls-Ext.DomQuery">Ext.DomQuery</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">DomQuery.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/DomQuery.html#cls-Ext.DomQuery">DomQuery</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Provides high performance selector/xpath processing by compiling queries into reusable functions. New pseudo classes and matchers can be plugged. It works on HTML and XML documents (if a content node is passed in).
<p>
DomQuery supports most of the <a href="http://www.w3.org/TR/2005/WD-css3-selectors-20051215/#selectors">CSS3 selectors spec</a>, along with some custom selectors and basic XPath.</p>

<p>
All selectors, attribute filters and pseudos below can be combined infinitely in any order. For example "div.foo:nth-child(odd)[@foo=bar].bar:first" would be a perfectly valid selector. Node filters are processed in the order in which they appear, which allows you to optimize your queries for your document structure.
</p>
<h4>Element Selectors:</h4>
<ul class="list">
    <li> <b>*</b> any element</li>
    <li> <b>E</b> an element with the tag E</li>
    <li> <b>E F</b> All descendent elements of E that have the tag F</li>
    <li> <b>E > F</b> or <b>E/F</b> all direct children elements of E that have the tag F</li>
    <li> <b>E + F</b> all elements with the tag F that are immediately preceded by an element with the tag E</li>
    <li> <b>E ~ F</b> all elements with the tag F that are preceded by a sibling element with the tag E</li>
</ul>
<h4>Attribute Selectors:</h4>
<p>The use of &#64; and quotes are optional. For example, div[&#64;foo='bar'] is also a valid attribute selector.</p>
<ul class="list">
    <li> <b>E[foo]</b> has an attribute "foo"</li>
    <li> <b>E[foo=bar]</b> has an attribute "foo" that equals "bar"</li>
    <li> <b>E[foo^=bar]</b> has an attribute "foo" that starts with "bar"</li>
    <li> <b>E[foo$=bar]</b> has an attribute "foo" that ends with "bar"</li>
    <li> <b>E[foo*=bar]</b> has an attribute "foo" that contains the substring "bar"</li>
    <li> <b>E[foo%=2]</b> has an attribute "foo" that is evenly divisible by 2</li>
    <li> <b>E[foo!=bar]</b> has an attribute "foo" that does not equal "bar"</li>
</ul>
<h4>Pseudo Classes:</h4>
<ul class="list">
    <li> <b>E:first-child</b> E is the first child of its parent</li>
    <li> <b>E:last-child</b> E is the last child of its parent</li>
    <li> <b>E:nth-child(<i>n</i>)</b> E is the <i>n</i>th child of its parent (1 based as per the spec)</li>
    <li> <b>E:nth-child(odd)</b> E is an odd child of its parent</li>
    <li> <b>E:nth-child(even)</b> E is an even child of its parent</li>
    <li> <b>E:only-child</b> E is the only child of its parent</li>
    <li> <b>E:checked</b> E is an element that is has a checked attribute that is true (e.g. a radio or checkbox) </li>
    <li> <b>E:first</b> the first E in the resultset</li>
    <li> <b>E:last</b> the last E in the resultset</li>
    <li> <b>E:nth(<i>n</i>)</b> the <i>n</i>th E in the resultset (1 based)</li>
    <li> <b>E:odd</b> shortcut for :nth-child(odd)</li>
    <li> <b>E:even</b> shortcut for :nth-child(even)</li>
    <li> <b>E:contains(foo)</b> E's innerHTML contains the substring "foo"</li>
    <li> <b>E:nodeValue(foo)</b> E contains a textNode with a nodeValue that equals "foo"</li>
    <li> <b>E:not(S)</b> an E element that does not match simple selector S</li>
    <li> <b>E:has(S)</b> an E element that has a descendent that matches simple selector S</li>
    <li> <b>E:next(S)</b> an E element whose next sibling matches simple selector S</li>
    <li> <b>E:prev(S)</b> an E element whose previous sibling matches simple selector S</li>
</ul>
<h4>CSS Value Selectors:</h4>
<ul class="list">
    <li> <b>E{display=none}</b> css value "display" that equals "none"</li>
    <li> <b>E{display^=none}</b> css value "display" that starts with "none"</li>
    <li> <b>E{display$=none}</b> css value "display" that ends with "none"</li>
    <li> <b>E{display*=none}</b> css value "display" that contains the substring "none"</li>
    <li> <b>E{display%=2}</b> css value "display" that is evenly divisible by 2</li>
    <li> <b>E{display!=none}</b> css value "display" that does not equal "none"</li>
</ul><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.DomQuery-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-matchers"></a><b><a href="source/DomQuery.html#prop-Ext.DomQuery-matchers">matchers</a></b> : Object<div class="mdesc">Collection of matching regular expressions and code snippets.</div></td><td class="msource">DomQuery</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-pseudos"></a><b><a href="source/DomQuery.html#prop-Ext.DomQuery-pseudos">pseudos</a></b> : Object<div class="mdesc"><div class="short">Collection of "pseudo class" processors. Each processor is passed the current nodeset (array)&#13;
and the argument (if a...</div><div class="long">Collection of "pseudo class" processors. Each processor is passed the current nodeset (array)
and the argument (if any) supplied in the selector.</div></div></td><td class="msource">DomQuery</td></tr></tbody></table><a id="Ext.DomQuery-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-compile"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-compile">compile</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;type</code>]</span>&nbsp;)
    :
                                        Function<div class="mdesc"><div class="short">Compiles a selector/xpath query into a reusable function. The returned function&#13;
takes one parameter "root" (optional...</div><div class="long">Compiles a selector/xpath query into a reusable function. The returned function
takes one parameter "root" (optional), which is the context node from where the query should start.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The selector/xpath query</div></li><li><code>type</code> : String<div class="sub-desc">(optional) Either "select" (the default) or "simple" for a simple selector match</div></li></ul><strong>Returns:</strong><ul><li><code>Function</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-filter"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-filter">filter</a></b>(&nbsp;<code>Array&nbsp;el</code>,&nbsp;<code>String&nbsp;selector</code>,&nbsp;<code>Boolean&nbsp;nonMatches</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Filters an array of elements to only include matches of a simple selector (e.g. div.some-class or span:first-child)</div><div class="long">Filters an array of elements to only include matches of a simple selector (e.g. div.some-class or span:first-child)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : Array<div class="sub-desc">An array of elements to filter</div></li><li><code>selector</code> : String<div class="sub-desc">The simple selector to test</div></li><li><code>nonMatches</code> : Boolean<div class="sub-desc">If true, it returns the elements that DON'T match
the selector instead of the ones that match</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">An Array of DOM elements which match the selector. If there are&#13;
no matches, and empty Array is returned.</div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-is"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-is">is</a></b>(&nbsp;<code>String/HTMLElement/Array&nbsp;el</code>,&nbsp;<code>String&nbsp;selector</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed element(s) match the passed simple selector (e.g. div.some-class or span:first-child)</div><div class="long">Returns true if the passed element(s) match the passed simple selector (e.g. div.some-class or span:first-child)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement/Array<div class="sub-desc">An element id, element or array of elements</div></li><li><code>selector</code> : String<div class="sub-desc">The simple selector to test</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-operators"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-operators">operators</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Collection of operator comparison functions. The default operators are =, !=, ^=, $=, *=, %=, |= and ~=.&#13;
New operato...</div><div class="long">Collection of operator comparison functions. The default operators are =, !=, ^=, $=, *=, %=, |= and ~=.
New operators can be added as long as the match the format <i>c</i>= where <i>c</i> is any character other than space, &gt; &lt;.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-select"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-select">select</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Node&nbsp;root</code>]</span>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Selects a group of elements.</div><div class="long">Selects a group of elements.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The selector/xpath query (can be a comma separated list of selectors)</div></li><li><code>root</code> : Node<div class="sub-desc">(optional) The start of the query (defaults to document).</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">An Array of DOM elements which match the selector. If there are&#13;
no matches, and empty Array is returned.</div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-selectNode"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-selectNode">selectNode</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Node&nbsp;root</code>]</span>&nbsp;)
    :
                                        Element<div class="mdesc"><div class="short">Selects a single element.</div><div class="long">Selects a single element.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The selector/xpath query</div></li><li><code>root</code> : Node<div class="sub-desc">(optional) The start of the query (defaults to document).</div></li></ul><strong>Returns:</strong><ul><li><code>Element</code><div class="sub-desc">The DOM element which matched the selector.</div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-selectNumber"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-selectNumber">selectNumber</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Node&nbsp;root</code>]</span>,&nbsp;<code>Number&nbsp;defaultValue</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Selects the value of a node, parsing integers and floats. Returns the defaultValue, or 0 if none is specified.</div><div class="long">Selects the value of a node, parsing integers and floats. Returns the defaultValue, or 0 if none is specified.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The selector/xpath query</div></li><li><code>root</code> : Node<div class="sub-desc">(optional) The start of the query (defaults to document).</div></li><li><code>defaultValue</code> : Number<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.DomQuery-selectValue"></a><b><a href="source/DomQuery.html#method-Ext.DomQuery-selectValue">selectValue</a></b>(&nbsp;<code>String&nbsp;selector</code>,&nbsp;<span title="Optional" class="optional">[<code>Node&nbsp;root</code>]</span>,&nbsp;<code>String&nbsp;defaultValue</code>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Selects the value of a node, optionally replacing null with the defaultValue.</div><div class="long">Selects the value of a node, optionally replacing null with the defaultValue.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>selector</code> : String<div class="sub-desc">The selector/xpath query</div></li><li><code>root</code> : Node<div class="sub-desc">(optional) The start of the query (defaults to document).</div></li><li><code>defaultValue</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">DomQuery</td></tr></tbody></table><a id="Ext.DomQuery-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>