<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Array-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Array-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Array-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Array"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/Ext.html#cls-Array">Array</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Global</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">Ext.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/Ext.html#cls-Array">Array</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"></div><div class="hr"></div><a id="Array-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Array-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Array-indexOf"></a><b><a href="source/Ext.html#method-Array-indexOf">indexOf</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Checks whether or not the specified object exists in the array.</div><div class="long">Checks whether or not the specified object exists in the array.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The object to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The index of o in the array (or -1 if it is not found)</div></li></ul></div></div></div></td><td class="msource">Array</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Array-remove"></a><b><a href="source/Ext.html#method-Array-remove">remove</a></b>(&nbsp;<code>Object&nbsp;o</code>&nbsp;)
    :
                                        Array<div class="mdesc"><div class="short">Removes the specified object from the array.  If the object is not found nothing happens.</div><div class="long">Removes the specified object from the array.  If the object is not found nothing happens.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The object to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Array</code><div class="sub-desc">this array</div></li></ul></div></div></div></td><td class="msource">Array</td></tr></tbody></table><a id="Array-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>