<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.ContainerLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.ContainerLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.ContainerLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.ContainerLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.ContainerLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/ContainerLayout.html#cls-Ext.layout.ContainerLayout">Ext.layout.ContainerLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">ContainerLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/ContainerLayout.html#cls-Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.layout.AnchorLayout.html" ext:cls="Ext.layout.AnchorLayout">AnchorLayout</a>,&#13;<a href="output/Ext.layout.BorderLayout.html" ext:cls="Ext.layout.BorderLayout">BorderLayout</a>,&#13;<a href="output/Ext.layout.BoxLayout.html" ext:cls="Ext.layout.BoxLayout">BoxLayout</a>,&#13;<a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">ColumnLayout</a>,&#13;<a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout">FitLayout</a>,&#13;<a href="output/Ext.layout.MenuLayout.html" ext:cls="Ext.layout.MenuLayout">MenuLayout</a>,&#13;<a href="output/Ext.layout.TableLayout.html" ext:cls="Ext.layout.TableLayout">TableLayout</a>,&#13;<a href="output/Ext.layout.ToolbarLayout.html" ext:cls="Ext.layout.ToolbarLayout">ToolbarLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>The ContainerLayout class is the default layout manager delegated by <a href="output/Ext.Container.html" ext:cls="Ext.Container">Ext.Container</a> to
render any child Components when no <tt><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></tt> is configured into
a <a href="output/Ext.Container.html" ext:cls="Ext.Container">Container</a>. ContainerLayout provides the basic foundation for all other layout
classes in Ext. It simply renders all child Components into the Container, performing no sizing or
positioning services. To utilize a layout that provides sizing and positioning of child Components,
specify an appropriate <tt><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></tt>.</p>
<p>This class is intended to be extended or created via the <tt><b><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a></b></tt>
configuration property.  See <tt><b><a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">Ext.Container.layout</a></b></tt> for additional details.</p></div><div class="hr"></div><a id="Ext.layout.ContainerLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource">ContainerLayout</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-renderHidden"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-renderHidden">renderHidden</a></b> : Boolean<div class="mdesc">True to hide each contained item on render (defaults to false).</div></td><td class="msource">ContainerLayout</td></tr></tbody></table><a id="Ext.layout.ContainerLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-activeItem"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-activeItem">activeItem</a></b> : Ext.Component<div class="mdesc"><div class="short">A reference to the Ext.Component that is active.  For example, if(myPanel.layout.activeItem.id == 'item-1') { ... }
a...</div><div class="long">A reference to the <a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a> that is active.  For example, <pre><code><b>if</b>(myPanel.layout.activeItem.id == <em>'item-1'</em>) { ... }</code></pre>
<tt>activeItem</tt> only applies to layout styles that can display items one at a time
(like <a href="output/Ext.layout.AccordionLayout.html" ext:cls="Ext.layout.AccordionLayout">Ext.layout.AccordionLayout</a>, <a href="output/Ext.layout.CardLayout.html" ext:cls="Ext.layout.CardLayout">Ext.layout.CardLayout</a>
and <a href="output/Ext.layout.FitLayout.html" ext:cls="Ext.layout.FitLayout">Ext.layout.FitLayout</a>).  Read-only.  Related to <a href="output/Ext.Container.html#Ext.Container-activeItem" ext:member="activeItem" ext:cls="Ext.Container">Ext.Container.activeItem</a>.</div></div></td><td class="msource">ContainerLayout</td></tr><tr class="property-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource">ContainerLayout</td></tr></tbody></table><a id="Ext.layout.ContainerLayout-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.layout.ContainerLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>