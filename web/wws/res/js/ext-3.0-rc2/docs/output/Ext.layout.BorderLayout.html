<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.layout.BorderLayout-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.layout.BorderLayout-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.layout.BorderLayout-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.layout.BorderLayout-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.layout.BorderLayout"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.layout.ContainerLayout.html" ext:member="" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif">BorderLayout</pre></div><h1>Class <a href="source/BorderLayout.html#cls-Ext.layout.BorderLayout">Ext.layout.BorderLayout</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.layout</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">BorderLayout.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/BorderLayout.html#cls-Ext.layout.BorderLayout">BorderLayout</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.layout.ContainerLayout.html" ext:cls="Ext.layout.ContainerLayout" ext:member="">ContainerLayout</a></td></tr></table><div class="description"><p>This is a multi-pane, application-oriented UI layout style that supports multiple
nested panels, automatic <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-split" ext:member="split" ext:cls="Ext.layout.BorderLayout.Region">split</a> bars between
<a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-BorderLayout.Region" ext:member="BorderLayout.Region" ext:cls="Ext.layout.BorderLayout.Region">regions</a> and built-in
<a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-collapsible" ext:member="collapsible" ext:cls="Ext.layout.BorderLayout.Region">expanding and collapsing</a> of regions.</p>
<p>This class is intended to be extended or created via the <tt>layout:'border'</tt>
<a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">Ext.Container.layout</a> config, and should generally not need to be created directly
via the new keyword.</p>
<p>BorderLayout does not have any direct config options (other than inherited ones).
All configuration options available for customizing the BorderLayout are at the
<a href="output/Ext.layout.BorderLayout.Region.html" ext:cls="Ext.layout.BorderLayout.Region">Ext.layout.BorderLayout.Region</a> and <a href="output/Ext.layout.BorderLayout.SplitRegion.html" ext:cls="Ext.layout.BorderLayout.SplitRegion">Ext.layout.BorderLayout.SplitRegion</a>
levels.</p>
<p>Example usage:</p>
<pre><code><b>var</b> myBorderPanel = <b>new</b> Ext.Panel({
    <a href="output/Ext.Component.html#Ext.Component-renderTo" ext:member="renderTo" ext:cls="Ext.Component">renderTo</a>: document.body,
    <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-width" ext:member="width" ext:cls="Ext.BoxComponent">width</a>: 700,
    <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 500,
    <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Border Layout'</em>,
    <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'border'</em>,
    <a href="output/Ext.Container.html#Ext.Container-items" ext:member="items" ext:cls="Ext.Container">items</a>: [{
        <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'South Region is resizable'</em>,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-BorderLayout.Region" ext:member="BorderLayout.Region" ext:cls="Ext.layout.BorderLayout.Region">region</a>: <em>'south'</em>,     <i>// position <b>for</b> region</i>
        <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-height" ext:member="height" ext:cls="Ext.BoxComponent">height</a>: 100,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-split" ext:member="split" ext:cls="Ext.layout.BorderLayout.Region">split</a>: true,         <i>// enable resizing</i>
        <a href="output/Ext.SplitBar.html#Ext.SplitBar-minSize" ext:member="minSize" ext:cls="Ext.SplitBar">minSize</a>: 75,         <i>// defaults to <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-minHeight" ext:member="minHeight" ext:cls="Ext.layout.BorderLayout.Region">50</a> </i>
        <a href="output/Ext.SplitBar.html#Ext.SplitBar-maxSize" ext:member="maxSize" ext:cls="Ext.SplitBar">maxSize</a>: 150,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-margins" ext:member="margins" ext:cls="Ext.layout.BorderLayout.Region">margins</a>: <em>'0 5 5 5'</em>
    },{
        <i>// xtype: <em>'panel'</em> implied by <b>default</b></i>
        <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'West Region is collapsible'</em>,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-BorderLayout.Region" ext:member="BorderLayout.Region" ext:cls="Ext.layout.BorderLayout.Region">region</a>:<em>'west'</em>,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-margins" ext:member="margins" ext:cls="Ext.layout.BorderLayout.Region">margins</a>: <em>'5 0 0 5'</em>,
        <a href="output/Ext.BoxComponent.html#Ext.BoxComponent-width" ext:member="width" ext:cls="Ext.BoxComponent">width</a>: 200,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-collapsible" ext:member="collapsible" ext:cls="Ext.layout.BorderLayout.Region">collapsible</a>: true,   <i>// make collapsible</i>
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-cmargins" ext:member="cmargins" ext:cls="Ext.layout.BorderLayout.Region">cmargins</a>: <em>'5 5 0 5'</em>, <i>// adjust top margin when collapsed</i>
        <a href="output/Ext.Component.html#Ext.Component-id" ext:member="id" ext:cls="Ext.Component">id</a>: <em>'west-region-container'</em>,
        <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'fit'</em>,
        <a href="output/Ext.Panel.html#Ext.Panel-unstyled" ext:member="unstyled" ext:cls="Ext.Panel">unstyled</a>: true
    },{
        <a href="output/Ext.Panel.html#Ext.Panel-title" ext:member="title" ext:cls="Ext.Panel">title</a>: <em>'Center Region'</em>,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-BorderLayout.Region" ext:member="BorderLayout.Region" ext:cls="Ext.layout.BorderLayout.Region">region</a>: <em>'center'</em>,     <i>// center region is required, no width/height specified</i>
        <a href="output/Ext.Component.html#Ext.Component-xtype" ext:member="xtype" ext:cls="Ext.Component">xtype</a>: <em>'container'</em>,
        <a href="output/Ext.Container.html#Ext.Container-layout" ext:member="layout" ext:cls="Ext.Container">layout</a>: <em>'fit'</em>,
        <a href="output/Ext.layout.BorderLayout.Region.html#Ext.layout.BorderLayout.Region-margins" ext:member="margins" ext:cls="Ext.layout.BorderLayout.Region">margins</a>: <em>'5 5 0 0'</em>
    }]
});</code></pre>
<p><b><u>Notes</u></b>:</p><div class="mdetail-params"><ul>
<li>Any container using the BorderLayout <b>must</b> have a child item with <tt>region:'center'</tt>.
The child item in the center region will always be resized to fill the remaining space not used by
the other regions in the layout.</li>
<li>Any child items with a region of <tt>west</tt> or <tt>east</tt> must have <tt>width</tt> defined
(an integer representing the number of pixels that the region should take up).</li>
<li>Any child items with a region of <tt>north</tt> or <tt>south</tt> must have <tt>height</tt> defined.</li>
<li>The regions of a BorderLayout are <b>fixed at render time</b> and thereafter, its child Components may not be removed or added</b>.  To add/remove
Components within a BorderLayout, have them wrapped by an additional Container which is directly
managed by the BorderLayout.  If the region is to be collapsible, the Container used directly
by the BorderLayout manager should be a Panel.  In the following example a Container (an Ext.Panel)
is added to the west region:
<div style="margin-left:16px"><pre><code>wrc = <a href="output/Ext.html#Ext-getCmp" ext:member="getCmp" ext:cls="Ext">Ext.getCmp</a>(<em>'west-region-container'</em>);
wrc.<a href="output/Ext.Panel.html#Ext.Panel-removeAll" ext:member="removeAll" ext:cls="Ext.Panel">removeAll</a>();
wrc.<a href="output/Ext.Container.html#Ext.Container-add" ext:member="add" ext:cls="Ext.Container">add</a>({
    title: <em>'Added Panel'</em>,
    html: <em>'Some content'</em>
});
wrc.<a href="output/Ext.Container.html#Ext.Container-doLayout" ext:member="doLayout" ext:cls="Ext.Container">doLayout</a>();</code></pre></div>
</li>
<li> To reference a <a href="output/Ext.layout.BorderLayout.Region.html" ext:cls="Ext.layout.BorderLayout.Region">Region</a>:
<div style="margin-left:16px"><pre><code>wr = myBorderPanel.layout.west;</code></pre></div>
</li>
</ul></div></div><div class="hr"></div><a id="Ext.layout.BorderLayout-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-extraCls"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-extraCls">extraCls</a></b> : String<div class="mdesc"><div class="short">An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to t...</div><div class="long"><p>An optional extra CSS class that will be added to the container. This can be useful for adding
customized styles to the container or any of its children using standard CSS rules. See
<a href="output/Ext.Component.html" ext:cls="Ext.Component">Ext.Component</a>.<a href="output/Ext.Component.html#Ext.Component-ctCls" ext:member="ctCls" ext:cls="Ext.Component">ctCls</a> also.</p>
<p><b>Note</b>: <tt>extraCls</tt> defaults to <tt>''</tt> except for the following classes
which assign a value by default:
<div class="mdetail-params"><ul>
<li><a href="output/Ext.layout.AbsoluteLayout.html" ext:cls="Ext.layout.AbsoluteLayout">Absolute Layout</a> : <tt>'x-abs-layout-item'</tt></li>
<li><a href="output/Ext.layout.Box.html" ext:cls="Ext.layout.Box">Box Layout</a> : <tt>'x-box-item'</tt></li>
<li><a href="output/Ext.layout.ColumnLayout.html" ext:cls="Ext.layout.ColumnLayout">Column Layout</a> : <tt>'x-column'</tt></li>
</ul></div>
To configure the above Classes with an extra CSS class append to the default.  For example,
for ColumnLayout:<pre><code>extraCls: <em>'x-column custom-class'</em></code></pre>
</p></div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#extraCls" ext:member="#extraCls" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-renderHidden"></a><b><a href="source/ContainerLayout.html#cfg-Ext.layout.ContainerLayout-renderHidden">renderHidden</a></b> : Boolean<div class="mdesc">True to hide each contained item on render (defaults to false).</div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#renderHidden" ext:member="#renderHidden" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.BorderLayout-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.layout.ContainerLayout-fieldTpl"></a><b><a href="source/ContainerLayout.html#prop-Ext.layout.ContainerLayout-fieldTpl">fieldTpl</a></b> : Ext.Template<div class="mdesc"><div class="short">The Ext.Template used by Field rendering layout classes (such as
Ext.layout.FormLayout) to create the DOM structure o...</div><div class="long">The <a href="output/Template.html" ext:cls="Template">Ext.Template</a> used by Field rendering layout classes (such as
<a href="output/Ext.layout.FormLayout.html" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout</a>) to create the DOM structure of a fully wrapped,
labeled and styled form Field. A default Template is supplied, but this may be
overriden to create custom field structures. The template processes values returned from
<a href="output/Ext.layout.FormLayout.html#Ext.layout.FormLayout-getTemplateArgs" ext:member="getTemplateArgs" ext:cls="Ext.layout.FormLayout">Ext.layout.FormLayout.getTemplateArgs</a>.</div></div></td><td class="msource"><a href="output/Ext.layout.ContainerLayout.html#fieldTpl" ext:member="#fieldTpl" ext:cls="Ext.layout.ContainerLayout">ContainerLayout</a></td></tr></tbody></table><a id="Ext.layout.BorderLayout-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.layout.BorderLayout-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>