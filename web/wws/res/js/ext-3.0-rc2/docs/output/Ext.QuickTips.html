<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.QuickTips-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.QuickTips-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.QuickTips-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="bookmark" href="../docs/?class=Ext.QuickTips"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/QuickTips.html#cls-Ext.QuickTips">Ext.QuickTips</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">QuickTips.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/QuickTips.html#cls-Ext.QuickTips">QuickTips</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description"><p>Provides attractive and customizable tooltips for any element. The QuickTips
singleton is used to configure and manage tooltips globally for multiple elements
in a generic manner.  To create individual tooltips with maximum customizability,
you should consider either <a href="output/Ext.Tip.html" ext:cls="Ext.Tip">Ext.Tip</a> or <a href="output/Ext.ToolTip.html" ext:cls="Ext.ToolTip">Ext.ToolTip</a>.</p>
<p>Quicktips can be configured via tag attributes directly in markup, or by
registering quick tips programmatically via the <a href="output/Ext.QuickTips.html#Ext.QuickTips-register" ext:member="register" ext:cls="Ext.QuickTips">register</a> method.</p>
<p>The singleton's instance of <a href="output/Ext.QuickTip.html" ext:cls="Ext.QuickTip">Ext.QuickTip</a> is available via
<a href="output/Ext.QuickTips.html#Ext.QuickTips-getQuickTip" ext:member="getQuickTip" ext:cls="Ext.QuickTips">getQuickTip</a>, and supports all the methods, and all the all the
configuration properties of Ext.QuickTip. These settings will apply to all
tooltips shown by the singleton.</p>
<p>Below is the summary of the configuration properties which can be used.
For detailed descriptions see <a href="output/Ext.QuickTips.html#Ext.QuickTips-getQuickTip" ext:member="getQuickTip" ext:cls="Ext.QuickTips">getQuickTip</a></p>
<p><b>QuickTips singleton configs (all are optional)</b></p>
<div class="mdetail-params"><ul><li>dismissDelay</li>
<li>hideDelay</li>
<li>maxWidth</li>
<li>minWidth</li>
<li>showDelay</li>
<li>trackMouse</li></ul></div>
<p><b>Target element configs (optional unless otherwise noted)</b></p>
<div class="mdetail-params"><ul><li>autoHide</li>
<li>cls</li>
<li>dismissDelay (overrides singleton value)</li>
<li>target (required)</li>
<li>text (required)</li>
<li>title</li>
<li>width</li></ul></div>
<p>Here is an example showing how some of these config options could be used:</p>
<pre><code><i>// Init the singleton.  Any tag-based quick tips will start working.
</i>
Ext.QuickTips.init();

<i>// Apply a set of config properties to the singleton
</i>
Ext.apply(Ext.QuickTips.getQuickTip(), {
    maxWidth: 200,
    minWidth: 100,
    showDelay: 50,
    trackMouse: true
});

<i>// Manually register a quick tip <b>for</b> a specific element
</i>
Ext.QuickTips.register({
    target: <em>'my-div'</em>,
    title: <em>'My Tooltip'</em>,
    text: <em>'This tooltip was added <b>in</b> code'</em>,
    width: 100,
    dismissDelay: 20
});</code></pre>
<p>To register a quick tip in markup, you simply add one or more of the valid QuickTip attributes prefixed with
the <b>ext:</b> namespace.  The HTML element itself is automatically set as the quick tip target. Here is the summary
of supported attributes (optional unless otherwise noted):</p>
<ul><li><b>hide</b>: Specifying "user" is equivalent to setting autoHide = false.  Any other value will be the
same as autoHide = true.</li>
<li><b>qclass</b>: A CSS class to be applied to the quick tip (equivalent to the 'cls' target element config).</li>
<li><b>qtip (required)</b>: The quick tip text (equivalent to the 'text' target element config).</li>
<li><b>qtitle</b>: The quick tip title (equivalent to the 'title' target element config).</li>
<li><b>qwidth</b>: The quick tip width (equivalent to the 'width' target element config).</li></ul>
<p>Here is an example of configuring an HTML element to display a tooltip from markup:</p>
<pre><code><i>// Add a quick tip to an HTML button
</i>
&lt;input type=<em>"button"</em> value=<em>"OK"</em> ext:qtitle=<em>"OK Button"</em> ext:qwidth=<em>"100"</em>
     ext:qtip=<em>"This is a quick tip from markup!"</em>>&lt;/input></code></pre><br><br><i>This class is a singleton and cannot be created directly.</i></div><div class="hr"></div><a id="Ext.QuickTips-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.QuickTips-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-disable"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-disable">disable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Disable quick tips globally.</div><div class="long">Disable quick tips globally.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-enable"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-enable">enable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Enable quick tips globally.</div><div class="long">Enable quick tips globally.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-getQuickTip"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-getQuickTip">getQuickTip</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Gets the global QuickTips instance.</div><div class="long">Gets the global QuickTips instance.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-init"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-init">init</a></b>(&nbsp;<code>Boolean&nbsp;autoRender</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Initialize the global QuickTips instance and prepare any quick tips.</div><div class="long">Initialize the global QuickTips instance and prepare any quick tips.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>autoRender</code> : Boolean<div class="sub-desc">True to render the QuickTips container immediately to preload images. (Defaults to true)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-isEnabled"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-isEnabled">isEnabled</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if quick tips are enabled, else false.</div><div class="long">Returns true if quick tips are enabled, else false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-register"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-register">register</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Configures a new quick tip instance and assigns it to a target element.  See&#13;
Ext.QuickTip.register for details.</div><div class="long">Configures a new quick tip instance and assigns it to a target element.  See
<a href="output/Ext.QuickTip.html#Ext.QuickTip-register" ext:member="register" ext:cls="Ext.QuickTip">Ext.QuickTip.register</a> for details.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-tips"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-tips">tips</a></b>(&nbsp;<code>Object&nbsp;config</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Alias of register.</div><div class="long">Alias of <a href="output/Ext.QuickTips.html#Ext.QuickTips-register" ext:member="register" ext:cls="Ext.QuickTips">register</a>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>config</code> : Object<div class="sub-desc">The config object</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.QuickTips-unregister"></a><b><a href="source/QuickTips.html#method-Ext.QuickTips-unregister">unregister</a></b>(&nbsp;<code>String/HTMLElement/Element&nbsp;el</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes any registered quick tip from the target element and destroys it.</div><div class="long">Removes any registered quick tip from the target element and destroys it.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>el</code> : String/HTMLElement/Element<div class="sub-desc">The element from which the quick tip is to be removed.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">QuickTips</td></tr></tbody></table><a id="Ext.QuickTips-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>