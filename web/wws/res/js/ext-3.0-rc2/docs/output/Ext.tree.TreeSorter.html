<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.tree.TreeSorter-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.tree.TreeSorter-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.tree.TreeSorter-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.tree.TreeSorter-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.tree.TreeSorter"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><h1>Class <a href="source/TreeSorter.html#cls-Ext.tree.TreeSorter">Ext.tree.TreeSorter</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.tree</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TreeSorter.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TreeSorter.html#cls-Ext.tree.TreeSorter">TreeSorter</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info">Object</td></tr></table><div class="description">Provides sorting of nodes in a <a href="output/Ext.tree.TreePanel.html" ext:cls="Ext.tree.TreePanel">Ext.tree.TreePanel</a>.  The TreeSorter automatically monitors events on the 
associated TreePanel that might affect the tree's sort order (beforechildrenrendered, append, insert and textchange).
Example usage:<br />
<pre><code><b>new</b> Ext.tree.TreeSorter(myTree, {
    folderSort: true,
    dir: <em>"desc"</em>,
    sortType: <b>function</b>(node) {
        <i>// sort by a custom, typed attribute:
</i>
        <b>return</b> parseInt(node.id, 10);
    }
});</code></pre></div><div class="hr"></div><a id="Ext.tree.TreeSorter-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeSorter-caseSensitive"></a><b><a href="source/TreeSorter.html#cfg-Ext.tree.TreeSorter-caseSensitive">caseSensitive</a></b> : Boolean<div class="mdesc">true for case-sensitive sort (defaults to false)</div></td><td class="msource">TreeSorter</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeSorter-dir"></a><b><a href="source/TreeSorter.html#cfg-Ext.tree.TreeSorter-dir">dir</a></b> : String<div class="mdesc">The direction to sort ("asc" or "desc," case-insensitive, defaults to "asc")</div></td><td class="msource">TreeSorter</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeSorter-folderSort"></a><b><a href="source/TreeSorter.html#cfg-Ext.tree.TreeSorter-folderSort">folderSort</a></b> : Boolean<div class="mdesc">True to sort leaf nodes under non-leaf nodes (defaults to false)</div></td><td class="msource">TreeSorter</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeSorter-leafAttr"></a><b><a href="source/TreeSorter.html#cfg-Ext.tree.TreeSorter-leafAttr">leafAttr</a></b> : String<div class="mdesc">The attribute used to determine leaf nodes when <a href="output/Ext.tree.TreeSorter.html#Ext.tree.TreeSorter-folderSort" ext:member="folderSort" ext:cls="Ext.tree.TreeSorter">folderSort</a> = true (defaults to "leaf")</div></td><td class="msource">TreeSorter</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeSorter-property"></a><b><a href="source/TreeSorter.html#cfg-Ext.tree.TreeSorter-property">property</a></b> : String<div class="mdesc"><div class="short">The named attribute on the node to sort by (defaults to "text").  Note that this &#13;
property is only used if no sortTy...</div><div class="long">The named attribute on the node to sort by (defaults to "text").  Note that this 
property is only used if no <a href="output/Ext.tree.TreeSorter.html#Ext.tree.TreeSorter-sortType" ext:member="sortType" ext:cls="Ext.tree.TreeSorter">sortType</a> function is specified, otherwise it is ignored.</div></div></td><td class="msource">TreeSorter</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeSorter-sortType"></a><b><a href="source/TreeSorter.html#cfg-Ext.tree.TreeSorter-sortType">sortType</a></b> : Function<div class="mdesc"><div class="short">A custom "casting" function used to convert node values before sorting.  The function&#13;
will be called with a single p...</div><div class="long">A custom "casting" function used to convert node values before sorting.  The function
will be called with a single parameter (the <a href="output/Ext.tree.TreeNode.html" ext:cls="Ext.tree.TreeNode">Ext.tree.TreeNode</a> being evaluated) and is expected to return
the node's sort value cast to the specific data type required for sorting.  This could be used, for example, when
a node's text (or other attribute) should be sorted as a date or numeric value.  See the class description for 
example usage.  Note that if a sortType is specified, any <a href="output/Ext.tree.TreeSorter.html#Ext.tree.TreeSorter-property" ext:member="property" ext:cls="Ext.tree.TreeSorter">property</a> config will be ignored.</div></div></td><td class="msource">TreeSorter</td></tr></tbody></table><a id="Ext.tree.TreeSorter-props"></a><h2>Public Properties</h2><div class="no-members">This class has no public properties.</div><a id="Ext.tree.TreeSorter-methods"></a><h2>Public Methods</h2><div class="no-members">This class has no public methods.</div><a id="Ext.tree.TreeSorter-events"></a><h2>Public Events</h2><div class="no-members">This class has no public events.</div></div>