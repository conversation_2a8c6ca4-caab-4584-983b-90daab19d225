<div class="body-wrap" xmlns:ext="http://www.extjs.com"><div class="top-tools"><a class="inner-link" href="#Ext.tree.TreeNode-props"><img src="resources/images/default/s.gif" class="item-icon icon-prop">Properties</a>&#13;<a class="inner-link" href="#Ext.tree.TreeNode-methods"><img src="resources/images/default/s.gif" class="item-icon icon-method">Methods</a>&#13;<a class="inner-link" href="#Ext.tree.TreeNode-events"><img src="resources/images/default/s.gif" class="item-icon icon-event">Events</a>&#13;<a class="inner-link" href="#Ext.tree.TreeNode-configs"><img src="resources/images/default/s.gif" class="item-icon icon-config">Config Options</a>&#13;<a class="bookmark" href="../docs/?class=Ext.tree.TreeNode"><img src="resources/images/default/s.gif" class="item-icon icon-fav">Direct Link</a>&#13;</div><div class="inheritance res-block"><pre class="res-block-inner"><a href="output/Ext.util.Observable.html" ext:member="" ext:cls="Ext.util.Observable">Observable</a>&#13;&nbsp;&nbsp;<img src="resources/elbow-end.gif"><a href="output/Ext.data.Node.html" ext:member="" ext:cls="Ext.data.Node">Node</a>&#13;&nbsp;&nbsp;&nbsp;&nbsp;<img src="resources/elbow-end.gif">TreeNode</pre></div><h1>Class <a href="source/TreeNode.html#cls-Ext.tree.TreeNode">Ext.tree.TreeNode</a></h1><table cellspacing="0"><tr><td class="label">Package:</td><td class="hd-info">Ext.tree</td></tr><tr><td class="label">Defined In:</td><td class="hd-info">TreeNode.js</td></tr><tr><td class="label">Class:</td><td class="hd-info"><a href="source/TreeNode.html#cls-Ext.tree.TreeNode">TreeNode</a></td></tr><tr><td class="label">Subclasses:</td><td class="hd-info"><a href="output/Ext.tree.AsyncTreeNode.html" ext:cls="Ext.tree.AsyncTreeNode">AsyncTreeNode</a></td></tr><tr><td class="label">Extends:</td><td class="hd-info"><a href="output/Ext.data.Node.html" ext:cls="Ext.data.Node" ext:member="">Node</a></td></tr></table><div class="description"></div><div class="hr"></div><a id="Ext.tree.TreeNode-configs"></a><h2>Config Options</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Config Options</th><th class="msource-header">Defined By</th></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-allowChildren"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-allowChildren">allowChildren</a></b> : Boolean<div class="mdesc">False to not allow this node to have child nodes (defaults to true)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-allowDrag"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-allowDrag">allowDrag</a></b> : Boolean<div class="mdesc">False to make this node undraggable if <a href="output/Ext.tree.TreeNode.html#Ext.tree.TreeNode-draggable" ext:member="draggable" ext:cls="Ext.tree.TreeNode">draggable</a> = true (defaults to true)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-allowDrop"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-allowDrop">allowDrop</a></b> : Boolean<div class="mdesc">False if this node cannot have child nodes dropped on it (defaults to true)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-checked"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-checked">checked</a></b> : Boolean<div class="mdesc"><div class="short">True to render a checked checkbox for this node, false to render an unchecked checkbox&#13;
(defaults to undefined with n...</div><div class="long">True to render a checked checkbox for this node, false to render an unchecked checkbox
(defaults to undefined with no checkbox rendered)</div></div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-cls"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-cls">cls</a></b> : String<div class="mdesc">A css class to be added to the node</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-disabled"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-disabled">disabled</a></b> : Boolean<div class="mdesc">true to start the node disabled</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-draggable"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-draggable">draggable</a></b> : Boolean<div class="mdesc">True to make this node draggable (defaults to false)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-editable"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-editable">editable</a></b> : Boolean<div class="mdesc">False to not allow this node to be edited by an (@link Ext.tree.TreeEditor} (defaults to true)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-expandable"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-expandable">expandable</a></b> : Boolean<div class="mdesc">If set to true, the node will always show a plus/minus icon, even when empty</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-expanded"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-expanded">expanded</a></b> : Boolean<div class="mdesc">true to start the node expanded</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-hidden"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-hidden">hidden</a></b> : Boolean<div class="mdesc">True to render hidden. (Defaults to false).</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-href"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-href">href</a></b> : String<div class="mdesc">URL of the link used for the node (defaults to #)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-hrefTarget"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-hrefTarget">hrefTarget</a></b> : String<div class="mdesc">target frame for the link</div></td><td class="msource">TreeNode</td></tr><tr class="config-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-icon"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-icon">icon</a></b> : String<div class="mdesc"><div class="short">The path to an icon for the node. The preferred way to do this&#13;
is to use the cls or iconCls attributes and add the i...</div><div class="long">The path to an icon for the node. The preferred way to do this
is to use the cls or iconCls attributes and add the icon via a CSS background image.</div></div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-iconCls"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-iconCls">iconCls</a></b> : String<div class="mdesc">A css class to be added to the nodes icon element for applying css background images</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-id"></a><b><a href="source/Tree.html#cfg-Ext.data.Node-id">id</a></b> : String<div class="mdesc">The id for this node. If one is not specified, one is generated.</div></td><td class="msource"><a href="output/Ext.data.Node.html#id" ext:member="#id" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-isTarget"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-isTarget">isTarget</a></b> : Boolean<div class="mdesc">False to not allow this node to act as a drop target (defaults to true)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-leaf"></a><b><a href="source/Tree.html#cfg-Ext.data.Node-leaf">leaf</a></b> : Boolean<div class="mdesc">true if this node is a leaf and does not have children</div></td><td class="msource"><a href="output/Ext.data.Node.html#leaf" ext:member="#leaf" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="config-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-listeners"></a><b><a href="source/Observable.html#cfg-Ext.util.Observable-listeners">listeners</a></b> : Object<div class="mdesc"><div class="short">A config object containing one or more event handlers to be added to this
object during initialization.  This should ...</div><div class="long"><p>A config object containing one or more event handlers to be added to this
object during initialization.  This should be a valid listeners config object as specified in the
<a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> example for attaching multiple handlers at once.</p>
<p><b>Note:</b> While <i>some</i> Observable classes export selected DOM events (eg "click", "mouseover" etc), this
is usually only done when extra value can be added. For example the <a href="output/Ext.DataView.html" ext:cls="Ext.DataView">DataView</a>'s
<b><code><a href="output/Ext.DataView.html#Ext.DataView-click" ext:member="click" ext:cls="Ext.DataView">click</a></code></b> event passing the node clicked on. To access DOM
events directly from a Component's HTMLElement listeners must be added to the element after the Component
has been rendered. A plugin can simplify this step:<code><pre>
<i>// Plugin is configured <b>with</b> a listeners config object.</i>
<i>// The Component is appended to the argument list of all handler functions.</i>
Ext.DomObserver = Ext.extend(Object, {
    constructor: <b>function</b>(config) {
        this.listeners = config.listeners ? config.listeners : config;
    },

    init: <b>function</b>(c) {
        <b>var</b> p, l = this.listeners;
        <b>for</b> (p <b>in</b> l) {
            <b>if</b> (Ext.isFunction(l[p])) {
                l[p] = this.createHandler(l[p], c);
            } <b>else</b> {
                l[p].fn = this.createHandler(l[p].fn, c);
            }
        }
        c.render = c.render.createSequence(<b>function</b>() {
            c.el.on(l);
        });
    },

    createHandler: <b>function</b>(fn, c) {
        <b>return</b> <b>function</b>(e) {
            fn.call(this, e, c);
        };
    }
});

<b>var</b> combo = <b>new</b> Ext.form.ComboBox({

<i>// Collapse combo when its element is clicked on</i>
    plugins: [ <b>new</b> Ext.DomObserver({
        click: <b>function</b>(evt, comp) {
            comp.collapse();
        }
    })],
    store: myStore,
    typeAhead: true,
    mode: <em>'local'</em>,
    triggerAction: <em>'all'</em>
});
</pre></code></p></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#listeners" ext:member="#listeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-qtip"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-qtip">qtip</a></b> : String<div class="mdesc">An Ext QuickTip for the node</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-qtipCfg"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-qtipCfg">qtipCfg</a></b> : String<div class="mdesc">An Ext QuickTip config for the node (used instead of qtip)</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-singleClickExpand"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-singleClickExpand">singleClickExpand</a></b> : Boolean<div class="mdesc">True for single click expand on this node</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-text"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-text">text</a></b> : String<div class="mdesc">The text for this node</div></td><td class="msource">TreeNode</td></tr><tr class="config-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-uiProvider"></a><b><a href="source/TreeNode.html#cfg-Ext.tree.TreeNode-uiProvider">uiProvider</a></b> : Function<div class="mdesc">A UI <b>class</b> to use for this node (defaults to Ext.tree.TreeNodeUI)</div></td><td class="msource">TreeNode</td></tr></tbody></table><a id="Ext.tree.TreeNode-props"></a><h2>Public Properties</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Property</th><th class="msource-header">Defined By</th></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-attributes"></a><b><a href="source/Tree.html#prop-Ext.data.Node-attributes">attributes</a></b> : Object<div class="mdesc">The attributes supplied for the node. You can use this property to access any custom attributes you supplied.</div></td><td class="msource"><a href="output/Ext.data.Node.html#attributes" ext:member="#attributes" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-childNodes"></a><b><a href="source/Tree.html#prop-Ext.data.Node-childNodes">childNodes</a></b> : Array<div class="mdesc">All child nodes of this node.</div></td><td class="msource"><a href="output/Ext.data.Node.html#childNodes" ext:member="#childNodes" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-disabled"></a><b><a href="source/TreeNode.html#prop-Ext.tree.TreeNode-disabled">disabled</a></b> : Boolean<div class="mdesc">True if this node is disabled.</div></td><td class="msource">TreeNode</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-firstChild"></a><b><a href="source/Tree.html#prop-Ext.data.Node-firstChild">firstChild</a></b> : Node<div class="mdesc">The first direct child node of this node, or null if this node has no child nodes.</div></td><td class="msource"><a href="output/Ext.data.Node.html#firstChild" ext:member="#firstChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-hidden"></a><b><a href="source/TreeNode.html#prop-Ext.tree.TreeNode-hidden">hidden</a></b> : Boolean<div class="mdesc">True if this node is hidden.</div></td><td class="msource">TreeNode</td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-id"></a><b><a href="source/Tree.html#prop-Ext.data.Node-id">id</a></b> : String<div class="mdesc">The node id.</div></td><td class="msource"><a href="output/Ext.data.Node.html#id" ext:member="#id" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-lastChild"></a><b><a href="source/Tree.html#prop-Ext.data.Node-lastChild">lastChild</a></b> : Node<div class="mdesc">The last direct child node of this node, or null if this node has no child nodes.</div></td><td class="msource"><a href="output/Ext.data.Node.html#lastChild" ext:member="#lastChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-nextSibling"></a><b><a href="source/Tree.html#prop-Ext.data.Node-nextSibling">nextSibling</a></b> : Node<div class="mdesc">The node immediately following this node in the tree, or null if there is no sibling node.</div></td><td class="msource"><a href="output/Ext.data.Node.html#nextSibling" ext:member="#nextSibling" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-parentNode"></a><b><a href="source/Tree.html#prop-Ext.data.Node-parentNode">parentNode</a></b> : Node<div class="mdesc">The parent node for this node.</div></td><td class="msource"><a href="output/Ext.data.Node.html#parentNode" ext:member="#parentNode" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-previousSibling"></a><b><a href="source/Tree.html#prop-Ext.data.Node-previousSibling">previousSibling</a></b> : Node<div class="mdesc">The node immediately preceding this node in the tree, or null if there is no sibling node.</div></td><td class="msource"><a href="output/Ext.data.Node.html#previousSibling" ext:member="#previousSibling" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-text"></a><b><a href="source/TreeNode.html#prop-Ext.tree.TreeNode-text">text</a></b> : String<div class="mdesc">Read-only. The text for this node. To change it use setText().</div></td><td class="msource">TreeNode</td></tr><tr class="property-row  "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-ui"></a><b><a href="source/TreeNode.html#prop-Ext.tree.TreeNode-ui">ui</a></b> : TreeNodeUI<div class="mdesc">Read-only. The UI for this node</div></td><td class="msource">TreeNode</td></tr></tbody></table><a id="Ext.tree.TreeNode-methods"></a><h2>Public Methods</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Method</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-TreeNode"></a><b><a href="source/TreeNode.html#cls-Ext.tree.TreeNode">TreeNode</a></b>(&nbsp;<code>Object/String&nbsp;attributes</code>&nbsp;)
    <div class="mdesc"><div class="short"></div><div class="long"><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>attributes</code> : Object/String<div class="sub-desc">The attributes/config for the node or just a string with the text for the node</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addEvents">addEvents</a></b>(&nbsp;<code>Object&nbsp;object</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to define events on this Observable</div><div class="long">Used to define events on this Observable<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>object</code> : Object<div class="sub-desc">The object with the events defined</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addEvents" ext:member="#addEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-addListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-addListener">addListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object.</div><div class="long">Appends an event handler to this object.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to listen for.</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.
properties. This may contain any of the following properties:<ul>
<li><b>scope</b> : Object<div class="sub-desc">The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li>
<li><b>delay</b> : Number<div class="sub-desc">The number of milliseconds to delay the invocation of the handler after the event fires.</div></li>
<li><b>single</b> : Boolean<div class="sub-desc">True to add a handler to handle just the next firing of the event, and then remove itself.</div></li>
<li><b>buffer</b> : Number<div class="sub-desc">Causes the handler to be scheduled to run in an <a href="output/Ext.util.DelayedTask.html" ext:cls="Ext.util.DelayedTask">Ext.util.DelayedTask</a> delayed
by the specified number of milliseconds. If the event fires again within that time, the original
handler is <em>not</em> invoked, but the new handler is scheduled in its place.</div></li>
<li><b>target</b> : Observable<div class="sub-desc">Only call the handler if the event was fired on the target Observable, <i>not</i>
if the event was bubbled up from a child Observable.</div></li>
</ul><br>
<p>
<b>Combining Options</b><br>
Using the options argument, it is possible to combine different types of listeners:<br>
<br>
A delayed, one-time listener.
<pre><code>myDataView.on(<em>'click'</em>, this.onClick, this, {
        single: true,
        delay: 100
    });</code></pre>
<p>
<b>Attaching multiple handlers in 1 call</b><br>
The method also allows for a single argument to be passed which is a config object containing properties
which specify multiple handlers.
<p>
<pre><code>myGridPanel.on({
        <em>'click'</em> : {
            fn: this.onClick,
            scope: this,
            delay: 100
        },
        <em>'mouseover'</em> : {
            fn: this.onMouseOver,
            scope: this
        },
        <em>'mouseout'</em> : {
            fn: this.onMouseOut,
            scope: this
        }
    });</code></pre>
<p>
Or a shorthand syntax:<br>
<pre><code>myGridPanel.on({
        <em>'click'</em> : this.onClick,
        <em>'mouseover'</em> : this.onMouseOver,
        <em>'mouseout'</em> : this.onMouseOut,
         scope: this
    });</code></pre></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#addListener" ext:member="#addListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-appendChild"></a><b><a href="source/Tree.html#method-Ext.data.Node-appendChild">appendChild</a></b>(&nbsp;<code>Node/Array&nbsp;node</code>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Insert node(s) as the last child node of this node.</div><div class="long">Insert node(s) as the last child node of this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Node/Array<div class="sub-desc">The node or Array of nodes to append</div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">The appended node if single append, or null if an array was passed</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#appendChild" ext:member="#appendChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-bubble"></a><b><a href="source/Tree.html#method-Ext.data.Node-bubble">bubble</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Bubbles up the tree from this node, calling the specified function with each node. The scope (this) of&#13;
function call...</div><div class="long">Bubbles up the tree from this node, calling the specified function with each node. The scope (<i>this</i>) of
function call will be the scope provided or the current node. The arguments to the function
will be the args provided or the current node. If the function returns false at any point,
the bubble is stopped.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to current node)</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The args to call the function with (default to passing the current node)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#bubble" ext:member="#bubble" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-cascade"></a><b><a href="source/Tree.html#method-Ext.data.Node-cascade">cascade</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Cascades down the tree from this node, calling the specified function with each node. The scope (this) of&#13;
function c...</div><div class="long">Cascades down the tree from this node, calling the specified function with each node. The scope (<i>this</i>) of
function call will be the scope provided or the current node. The arguments to the function
will be the args provided or the current node. If the function returns false at any point,
the cascade is stopped on that branch.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to current node)</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The args to call the function with (default to passing the current node)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#cascade" ext:member="#cascade" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-collapse"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-collapse">collapse</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;deep</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anim</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Collapse this node.</div><div class="long">Collapse this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>deep</code> : Boolean<div class="sub-desc">(optional) True to collapse all children as well</div></li><li><code>anim</code> : Boolean<div class="sub-desc">(optional) false to cancel the default animation</div></li><li><code>callback</code> : Function<div class="sub-desc">(optional) A callback to be called when
expanding this node completes (does not wait for deep expand to complete).
Called with 1 parameter, this node.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the callback.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-collapseChildNodes"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-collapseChildNodes">collapseChildNodes</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;deep</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Collapse all child nodes</div><div class="long">Collapse all child nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>deep</code> : Boolean<div class="sub-desc">(optional) true if the child nodes should also collapse their child nodes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-contains"></a><b><a href="source/Tree.html#method-Ext.data.Node-contains">contains</a></b>(&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node is an ancestor (at any point) of the passed node.</div><div class="long">Returns true if this node is an ancestor (at any point) of the passed node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Node<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#contains" ext:member="#contains" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-disable"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-disable">disable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Disables this node</div><div class="long">Disables this node<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-eachChild"></a><b><a href="source/Tree.html#method-Ext.data.Node-eachChild">eachChild</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Array&nbsp;args</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Interates the child nodes of this node, calling the specified function with each node. The scope (this) of&#13;
function ...</div><div class="long">Interates the child nodes of this node, calling the specified function with each node. The scope (<i>this</i>) of
function call will be the scope provided or the current node. The arguments to the function
will be the args provided or the current node. If the function returns false at any point,
the iteration stops.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc">The function to call</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope of the function (defaults to current node)</div></li><li><code>args</code> : Array<div class="sub-desc">(optional) The args to call the function with (default to passing the current node)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#eachChild" ext:member="#eachChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-enable"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-enable">enable</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Enables this node</div><div class="long">Enables this node<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-enableBubble"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-enableBubble">enableBubble</a></b>(&nbsp;<code>Object&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Used to enable bubbling of events</div><div class="long">Used to enable bubbling of events<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>events</code> : Object<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#enableBubble" ext:member="#enableBubble" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-ensureVisible"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-ensureVisible">ensureVisible</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Ensures all parent nodes are expanded, and if necessary, scrolls&#13;
the node into view.</div><div class="long">Ensures all parent nodes are expanded, and if necessary, scrolls
the node into view.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>callback</code> : Function<div class="sub-desc">(optional) A function to call when the node has been made visible.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the callback.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-expand"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-expand">expand</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;deep</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;anim</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Function&nbsp;callback</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Expand this node.</div><div class="long">Expand this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>deep</code> : Boolean<div class="sub-desc">(optional) True to expand all children as well</div></li><li><code>anim</code> : Boolean<div class="sub-desc">(optional) false to cancel the default animation</div></li><li><code>callback</code> : Function<div class="sub-desc">(optional) A callback to be called when
expanding this node completes (does not wait for deep expand to complete).
Called with 1 parameter, this node.</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope in which to execute the callback.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-expandChildNodes"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-expandChildNodes">expandChildNodes</a></b>(&nbsp;<span title="Optional" class="optional">[<code>Boolean&nbsp;deep</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Expand all child nodes</div><div class="long">Expand all child nodes<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>deep</code> : Boolean<div class="sub-desc">(optional) true if the child nodes should also expand their child nodes</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-findChild"></a><b><a href="source/Tree.html#method-Ext.data.Node-findChild">findChild</a></b>(&nbsp;<code>String&nbsp;attribute</code>,&nbsp;<code>Mixed&nbsp;value</code>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Finds the first child that has the attribute with the specified value.</div><div class="long">Finds the first child that has the attribute with the specified value.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>attribute</code> : String<div class="sub-desc">The attribute name</div></li><li><code>value</code> : Mixed<div class="sub-desc">The value to search for</div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">The found child or null if none was found</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#findChild" ext:member="#findChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-findChildBy"></a><b><a href="source/Tree.html#method-Ext.data.Node-findChildBy">findChildBy</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Finds the first child by a custom function. The child matches if the function passed&#13;
returns true.</div><div class="long">Finds the first child by a custom function. The child matches if the function passed
returns true.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">The found child or null if none was found</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#findChildBy" ext:member="#findChildBy" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-fireEvent"></a><b><a href="source/Observable.html#method-Ext.util.Observable-fireEvent">fireEvent</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Object...&nbsp;args</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Fires the specified event with the passed parameters (minus the event name).
An event may be set to bubble up an Obse...</div><div class="long"><p>Fires the specified event with the passed parameters (minus the event name).</p>
<p>An event may be set to bubble up an Observable parent hierarchy (See <a href="output/Ext.Component.html#Ext.Component-getBubbleTarget" ext:member="getBubbleTarget" ext:cls="Ext.Component">Ext.Component.getBubbleTarget</a>)
by calling <a href="output/Ext.util.Observable.html#Ext.util.Observable-enableBubble" ext:member="enableBubble" ext:cls="Ext.util.Observable">enableBubble</a>.</p><div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to fire.</div></li><li><code>args</code> : Object...<div class="sub-desc">Variable number of parameters are passed to handlers.</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">returns false if any of the handlers return false otherwise it returns true.</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#fireEvent" ext:member="#fireEvent" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-getDepth"></a><b><a href="source/Tree.html#method-Ext.data.Node-getDepth">getDepth</a></b>()
    :
                                        Number<div class="mdesc"><div class="short">Returns depth of this node (the root node has a depth of 0)</div><div class="long">Returns depth of this node (the root node has a depth of 0)<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#getDepth" ext:member="#getDepth" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-getOwnerTree"></a><b><a href="source/Tree.html#method-Ext.data.Node-getOwnerTree">getOwnerTree</a></b>()
    :
                                        Tree<div class="mdesc"><div class="short">Returns the tree this node is in.</div><div class="long">Returns the tree this node is in.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Tree</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#getOwnerTree" ext:member="#getOwnerTree" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-getPath"></a><b><a href="source/Tree.html#method-Ext.data.Node-getPath">getPath</a></b>(&nbsp;<span title="Optional" class="optional">[<code>String&nbsp;attr</code>]</span>&nbsp;)
    :
                                        String<div class="mdesc"><div class="short">Returns the path for this node. The path can be used to expand or select this node programmatically.</div><div class="long">Returns the path for this node. The path can be used to expand or select this node programmatically.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>attr</code> : String<div class="sub-desc">(optional) The attr to use for the path (defaults to the node's id)</div></li></ul><strong>Returns:</strong><ul><li><code>String</code><div class="sub-desc">The path</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#getPath" ext:member="#getPath" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-getUI"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-getUI">getUI</a></b>()
    :
                                        TreeNodeUI<div class="mdesc"><div class="short">Returns the UI object for this node.</div><div class="long">Returns the UI object for this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>TreeNodeUI</code><div class="sub-desc">The object which is providing the user interface for this tree&#13;
node. Unless otherwise specified in the {@link #uiProvider}, this will be an instance&#13;
of {@link Ext.tree.TreeNodeUI}</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-hasChildNodes"></a><b><a href="source/Tree.html#method-Ext.data.Node-hasChildNodes">hasChildNodes</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node has one or more child nodes, else false.</div><div class="long">Returns true if this node has one or more child nodes, else false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#hasChildNodes" ext:member="#hasChildNodes" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-hasListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-hasListener">hasListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Checks to see if this object has any listeners for a specified event</div><div class="long">Checks to see if this object has any listeners for a specified event<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The name of the event to check for</div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc">True if the event is being listened for, else false</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#hasListener" ext:member="#hasListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-indexOf"></a><b><a href="source/Tree.html#method-Ext.data.Node-indexOf">indexOf</a></b>(&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    :
                                        Number<div class="mdesc"><div class="short">Returns the index of a child node</div><div class="long">Returns the index of a child node<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Node<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Number</code><div class="sub-desc">The index of the node or -1 if it was not found</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#indexOf" ext:member="#indexOf" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-insertBefore"></a><b><a href="source/Tree.html#method-Ext.data.Node-insertBefore">insertBefore</a></b>(&nbsp;<code>Node&nbsp;node</code>,&nbsp;<code>Node&nbsp;refNode</code>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Inserts the first node before the second node in this nodes childNodes collection.</div><div class="long">Inserts the first node before the second node in this nodes childNodes collection.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Node<div class="sub-desc">The node to insert</div></li><li><code>refNode</code> : Node<div class="sub-desc">The node to insert before (if null the node is appended)</div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">The inserted node</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#insertBefore" ext:member="#insertBefore" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-isAncestor"></a><b><a href="source/Tree.html#method-Ext.data.Node-isAncestor">isAncestor</a></b>(&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if the passed node is an ancestor (at any point) of this node.</div><div class="long">Returns true if the passed node is an ancestor (at any point) of this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Node<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#isAncestor" ext:member="#isAncestor" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-isExpandable"></a><b><a href="source/Tree.html#method-Ext.data.Node-isExpandable">isExpandable</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node has one or more child nodes, or if the expandable&#13;
node attribute is explicitly specified a...</div><div class="long">Returns true if this node has one or more child nodes, or if the <tt>expandable</tt>
node attribute is explicitly specified as true (see <a href="output/Ext.data.Node.html#Ext.data.Node-attributes" ext:member="attributes" ext:cls="Ext.data.Node">attributes</a>), otherwise returns false.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#isExpandable" ext:member="#isExpandable" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-isExpanded"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-isExpanded">isExpanded</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node is expanded</div><div class="long">Returns true if this node is expanded<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-isFirst"></a><b><a href="source/Tree.html#method-Ext.data.Node-isFirst">isFirst</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node is the first child of its parent</div><div class="long">Returns true if this node is the first child of its parent<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#isFirst" ext:member="#isFirst" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-isLast"></a><b><a href="source/Tree.html#method-Ext.data.Node-isLast">isLast</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node is the last child of its parent</div><div class="long">Returns true if this node is the last child of its parent<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#isLast" ext:member="#isLast" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-isLeaf"></a><b><a href="source/Tree.html#method-Ext.data.Node-isLeaf">isLeaf</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node is a leaf</div><div class="long">Returns true if this node is a leaf<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#isLeaf" ext:member="#isLeaf" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-isSelected"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-isSelected">isSelected</a></b>()
    :
                                        Boolean<div class="mdesc"><div class="short">Returns true if this node is selected</div><div class="long">Returns true if this node is selected<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Boolean</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-item"></a><b><a href="source/Tree.html#method-Ext.data.Node-item">item</a></b>(&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Returns the child node at the specified index.</div><div class="long">Returns the child node at the specified index.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>index</code> : Number<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#item" ext:member="#item" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-on"></a><b><a href="source/Observable.html#method-Ext.util.Observable-on">on</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;options</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Appends an event handler to this object (shorthand for addListener.)</div><div class="long">Appends an event handler to this object (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event to listen for</div></li><li><code>handler</code> : Function<div class="sub-desc">The method the event invokes</div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope (<code><b>this</b></code> reference) in which the handler function is executed.
<b>If omitted, defaults to the object which fired the event.</b></div></li><li><code>options</code> : Object<div class="sub-desc">(optional) An object containing handler configuration.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#on" ext:member="#on" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-purgeListeners"></a><b><a href="source/Observable.html#method-Ext.util.Observable-purgeListeners">purgeListeners</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Removes all listeners for this object</div><div class="long">Removes all listeners for this object<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#purgeListeners" ext:member="#purgeListeners" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-relayEvents"></a><b><a href="source/Observable-more.html#method-Ext.util.Observable-relayEvents">relayEvents</a></b>(&nbsp;<code>Object&nbsp;o</code>,&nbsp;<code>Array&nbsp;events</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Relays selected events from the specified Observable as if the events were fired by this.</div><div class="long">Relays selected events from the specified Observable as if the events were fired by <tt><b>this</b></tt>.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>o</code> : Object<div class="sub-desc">The Observable whose events this object is to relay.</div></li><li><code>events</code> : Array<div class="sub-desc">Array of event names to relay.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#relayEvents" ext:member="#relayEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-remove"></a><b><a href="source/Tree.html#method-Ext.data.Node-remove">remove</a></b>()
    :
                                        Node<div class="mdesc"><div class="short">Removes this node from its parent</div><div class="long">Removes this node from its parent<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">this</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#remove" ext:member="#remove" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-removeChild"></a><b><a href="source/Tree.html#method-Ext.data.Node-removeChild">removeChild</a></b>(&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Removes a child node from this node.</div><div class="long">Removes a child node from this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>node</code> : Node<div class="sub-desc">The node to remove</div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">The removed node</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#removeChild" ext:member="#removeChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-removeListener"></a><b><a href="source/Observable.html#method-Ext.util.Observable-removeListener">removeListener</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler.</div><div class="long">Removes an event handler.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#removeListener" ext:member="#removeListener" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-replaceChild"></a><b><a href="source/Tree.html#method-Ext.data.Node-replaceChild">replaceChild</a></b>(&nbsp;<code>Node&nbsp;newChild</code>,&nbsp;<code>Node&nbsp;oldChild</code>&nbsp;)
    :
                                        Node<div class="mdesc"><div class="short">Replaces one child node in this node with another.</div><div class="long">Replaces one child node in this node with another.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>newChild</code> : Node<div class="sub-desc">The replacement node</div></li><li><code>oldChild</code> : Node<div class="sub-desc">The node to replace</div></li></ul><strong>Returns:</strong><ul><li><code>Node</code><div class="sub-desc">The replaced node</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#replaceChild" ext:member="#replaceChild" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-resumeEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-resumeEvents">resumeEvents</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Resume firing events. (see suspendEvents)
If events were suspended using the queueSuspended parameter, then all
event...</div><div class="long">Resume firing events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-suspendEvents" ext:member="suspendEvents" ext:cls="Ext.util.Observable">suspendEvents</a>)
If events were suspended using the <tt><b>queueSuspended</b></tt> parameter, then all
events fired during event suspension will be sent to any listeners now.<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#resumeEvents" ext:member="#resumeEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-select"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-select">select</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Triggers selection of this node</div><div class="long">Triggers selection of this node<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-setId"></a><b><a href="source/Tree.html#method-Ext.data.Node-setId">setId</a></b>(&nbsp;<code>String&nbsp;id</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Changes the id of this node.</div><div class="long">Changes the id of this node.<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>id</code> : String<div class="sub-desc">The new id for the node.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#setId" ext:member="#setId" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-setText"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-setText">setText</a></b>(&nbsp;<code>String&nbsp;text</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sets the text for this node</div><div class="long">Sets the text for this node<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>text</code> : String<div class="sub-desc"></div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-sort"></a><b><a href="source/Tree.html#method-Ext.data.Node-sort">sort</a></b>(&nbsp;<code>Function&nbsp;fn</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Sorts this nodes children using the supplied sort function</div><div class="long">Sorts this nodes children using the supplied sort function<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>fn</code> : Function<div class="sub-desc"></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional)</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#sort" ext:member="#sort" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-suspendEvents"></a><b><a href="source/Observable.html#method-Ext.util.Observable-suspendEvents">suspendEvents</a></b>(&nbsp;<code>Boolean&nbsp;queueSuspended</code>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Suspend the firing of all events. (see resumeEvents)</div><div class="long">Suspend the firing of all events. (see <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a>)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>queueSuspended</code> : Boolean<div class="sub-desc">Pass as true to queue up suspended events to be fired
after the <a href="output/Ext.util.Observable.html#Ext.util.Observable-resumeEvents" ext:member="resumeEvents" ext:cls="Ext.util.Observable">resumeEvents</a> call instead of discarding all suspended events;</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#suspendEvents" ext:member="#suspendEvents" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-toggle"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-toggle">toggle</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Toggles expanded/collapsed state of the node</div><div class="long">Toggles expanded/collapsed state of the node<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.util.Observable-un"></a><b><a href="source/Observable.html#method-Ext.util.Observable-un">un</a></b>(&nbsp;<code>String&nbsp;eventName</code>,&nbsp;<code>Function&nbsp;handler</code>,&nbsp;<span title="Optional" class="optional">[<code>Object&nbsp;scope</code>]</span>&nbsp;)
    :
                                        void<div class="mdesc"><div class="short">Removes an event handler (shorthand for removeListener.)</div><div class="long">Removes an event handler (shorthand for <a href="output/Ext.util.Observable.html#Ext.util.Observable-removeListener" ext:member="removeListener" ext:cls="Ext.util.Observable">removeListener</a>.)<div class="mdetail-params"><strong>Parameters:</strong><ul><li><code>eventName</code> : String<div class="sub-desc">The type of event the handler was associated with.</div></li><li><code>handler</code> : Function<div class="sub-desc">The handler to remove. <b>This must be a reference to the function passed into the <a href="output/Ext.util.Observable.html#Ext.util.Observable-addListener" ext:member="addListener" ext:cls="Ext.util.Observable">addListener</a> call.</b></div></li><li><code>scope</code> : Object<div class="sub-desc">(optional) The scope originally specified for the handler.</div></li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource"><a href="output/Ext.util.Observable.html#un" ext:member="#un" ext:cls="Ext.util.Observable">Observable</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-unselect"></a><b><a href="source/TreeNode.html#method-Ext.tree.TreeNode-unselect">unselect</a></b>()
    :
                                        void<div class="mdesc"><div class="short">Triggers deselection of this node</div><div class="long">Triggers deselection of this node<div class="mdetail-params"><strong>Parameters:</strong><ul><li>None.</li></ul><strong>Returns:</strong><ul><li>void</li></ul></div></div></div></td><td class="msource">TreeNode</td></tr></tbody></table><a id="Ext.tree.TreeNode-events"></a><h2>Public Events</h2><table cellspacing="0" class="member-table"><tbody><tr><th colspan="2" class="sig-header">Event</th><th class="msource-header">Defined By</th></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-append"></a><b><a href="source/Tree.html#event-Ext.data.Node-append">append</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;node</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a new child node is appended</div><div class="long">Fires when a new child node is appended<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>node</code> : Node<div class="sub-desc">The newly appended node</div></li><li><code>index</code> : Number<div class="sub-desc">The index of the newly appended node</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#append" ext:member="#append" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-beforeappend"></a><b><a href="source/Tree.html#event-Ext.data.Node-beforeappend">beforeappend</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a new child is appended, return false to cancel the append.</div><div class="long">Fires before a new child is appended, return false to cancel the append.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>node</code> : Node<div class="sub-desc">The child node to be appended</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#beforeappend" ext:member="#beforeappend" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-beforechildrenrendered"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-beforechildrenrendered">beforechildrenrendered</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires right before the child nodes for this node are rendered</div><div class="long">Fires right before the child nodes for this node are rendered<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-beforeclick"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-beforeclick">beforeclick</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before click processing. Return false to cancel the default action.</div><div class="long">Fires before click processing. Return false to cancel the default action.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc">The event object</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-beforecollapse"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-beforecollapse">beforecollapse</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Boolean&nbsp;deep</code>,&nbsp;<code>Boolean&nbsp;anim</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before this node is collapsed, return false to cancel.</div><div class="long">Fires before this node is collapsed, return false to cancel.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>deep</code> : Boolean<div class="sub-desc"></div></li><li><code>anim</code> : Boolean<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-beforeexpand"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-beforeexpand">beforeexpand</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Boolean&nbsp;deep</code>,&nbsp;<code>Boolean&nbsp;anim</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before this node is expanded, return false to cancel.</div><div class="long">Fires before this node is expanded, return false to cancel.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>deep</code> : Boolean<div class="sub-desc"></div></li><li><code>anim</code> : Boolean<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-beforeinsert"></a><b><a href="source/Tree.html#event-Ext.data.Node-beforeinsert">beforeinsert</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;node</code>,&nbsp;<code>Node&nbsp;refNode</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a new child is inserted, return false to cancel the insert.</div><div class="long">Fires before a new child is inserted, return false to cancel the insert.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>node</code> : Node<div class="sub-desc">The child node to be inserted</div></li><li><code>refNode</code> : Node<div class="sub-desc">The child node the node is being inserted before</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#beforeinsert" ext:member="#beforeinsert" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-beforemove"></a><b><a href="source/Tree.html#event-Ext.data.Node-beforemove">beforemove</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;oldParent</code>,&nbsp;<code>Node&nbsp;newParent</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before this node is moved to a new location in the tree. Return false to cancel the move.</div><div class="long">Fires before this node is moved to a new location in the tree. Return false to cancel the move.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>oldParent</code> : Node<div class="sub-desc">The parent of this node</div></li><li><code>newParent</code> : Node<div class="sub-desc">The new parent this node is moving to</div></li><li><code>index</code> : Number<div class="sub-desc">The index it is being moved to</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#beforemove" ext:member="#beforemove" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-beforeremove"></a><b><a href="source/Tree.html#event-Ext.data.Node-beforeremove">beforeremove</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires before a child is removed, return false to cancel the remove.</div><div class="long">Fires before a child is removed, return false to cancel the remove.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>node</code> : Node<div class="sub-desc">The child node to be removed</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#beforeremove" ext:member="#beforeremove" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-checkchange"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-checkchange">checkchange</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Boolean&nbsp;checked</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a node with a checkbox's checked property changes</div><div class="long">Fires when a node with a checkbox's checked property changes<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>checked</code> : Boolean<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-click"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-click">click</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this node is clicked</div><div class="long">Fires when this node is clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc">The event object</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-collapse"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-collapse">collapse</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this node is collapsed</div><div class="long">Fires when this node is collapsed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-contextmenu"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-contextmenu">contextmenu</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this node is right clicked</div><div class="long">Fires when this node is right clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc">The event object</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-dblclick"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-dblclick">dblclick</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Ext.EventObject&nbsp;e</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this node is double clicked</div><div class="long">Fires when this node is double clicked<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>e</code> : Ext.EventObject<div class="sub-desc">The event object</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-disabledchange"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-disabledchange">disabledchange</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Boolean&nbsp;disabled</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the disabled status of this node changes</div><div class="long">Fires when the disabled status of this node changes<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>disabled</code> : Boolean<div class="sub-desc"></div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-expand"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-expand">expand</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this node is expanded</div><div class="long">Fires when this node is expanded<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-insert"></a><b><a href="source/Tree.html#event-Ext.data.Node-insert">insert</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;node</code>,&nbsp;<code>Node&nbsp;refNode</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a new child node is inserted.</div><div class="long">Fires when a new child node is inserted.<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>node</code> : Node<div class="sub-desc">The child node inserted</div></li><li><code>refNode</code> : Node<div class="sub-desc">The child node the node was inserted before</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#insert" ext:member="#insert" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-move"></a><b><a href="source/Tree.html#event-Ext.data.Node-move">move</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;oldParent</code>,&nbsp;<code>Node&nbsp;newParent</code>,&nbsp;<code>Number&nbsp;index</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when this node is moved to a new location in the tree</div><div class="long">Fires when this node is moved to a new location in the tree<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>oldParent</code> : Node<div class="sub-desc">The old parent of this node</div></li><li><code>newParent</code> : Node<div class="sub-desc">The new parent of this node</div></li><li><code>index</code> : Number<div class="sub-desc">The index it was moved to</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#move" ext:member="#move" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable inherited"><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.data.Node-remove"></a><b><a href="source/Tree.html#event-Ext.data.Node-remove">remove</a></b> :
                                      (&nbsp;<code>Tree&nbsp;tree</code>,&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>Node&nbsp;node</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when a child node is removed</div><div class="long">Fires when a child node is removed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>tree</code> : Tree<div class="sub-desc">The owner tree</div></li><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>node</code> : Node<div class="sub-desc">The removed node</div></li></ul></div></div></div></td><td class="msource"><a href="output/Ext.data.Node.html#remove" ext:member="#remove" ext:cls="Ext.data.Node">Node</a></td></tr><tr class="method-row expandable "><td class="micon"><a href="#expand" class="exi">&nbsp;</a></td><td class="sig"><a id="Ext.tree.TreeNode-textchange"></a><b><a href="source/TreeNode.html#event-Ext.tree.TreeNode-textchange">textchange</a></b> :
                                      (&nbsp;<code>Node&nbsp;this</code>,&nbsp;<code>String&nbsp;text</code>,&nbsp;<code>String&nbsp;oldText</code>&nbsp;)
    <div class="mdesc"><div class="short">Fires when the text for this node is changed</div><div class="long">Fires when the text for this node is changed<div class="mdetail-params"><strong style="font-weight: normal;">Listeners will be called with the following arguments:</strong><ul><li><code>this</code> : Node<div class="sub-desc">This node</div></li><li><code>text</code> : String<div class="sub-desc">The new text</div></li><li><code>oldText</code> : String<div class="sub-desc">The old text</div></li></ul></div></div></div></td><td class="msource">TreeNode</td></tr></tbody></table></div>