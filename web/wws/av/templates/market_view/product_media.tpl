<table class="table_normal">
	<caption>Media</caption>
	<tr>
		<th>Quelle</th>
		<th width="300"></th>
		<th></th>
	</tr>
	{{foreach from=$result item="row"}}
		<tr {{if $row.media_status == 'offline'}}class="del"{{/if}}>
			<td rowspan="2">
				<b>Typ</b>: {{$row.media_type}}<br>
				{{if $row.topic}}<br>Thema: {{$row.topic}}{{/if}}
				{{if $row.pos}}<br>Position: {{$row.pos}}{{/if}}
				{{if $row.description}}<br>Beschreibung: {{$row.description}}{{/if}}
				{{if $row.width}}<br>Breite: {{$row.width}}{{/if}}
				{{if $row.height}}<br>Höhe: {{$row.height}}{{/if}}
				{{if $row.created_at}}<br>Datum: {{$row.created_at}}{{/if}}

				{{if $_user->hasRight('rights.dev')}}
					<hr>
					<b>mv_media_id</b>: {{$row.mv_media_id}}<br>
					<b>Fingerprint</b>: {{$row.fingerprint}}<br>
					<b>MD5</b>: {{$row.md5}}<br>
					<b>media_key</b>: {{$row.media_key}}<br>
				{{/if}}
			</td>
			<td rowspan="2">{{$row.mvsrc_name}}</td>
			<td>
				{{if $row.media_type == \wws\MarketView\Entities\MarketViewMedia::MEDIA_TYPE_IMAGE_URL || $row.media_type == \wws\MarketView\Entities\MarketViewMedia::MEDIA_TYPE_IMAGE}}
					<a href="{{$row.url}}" target="_blank"><img src="{{$row.url}}" alt="" width="300" /></a>
				{{elseif $row.media_type == \wws\MarketView\Entities\MarketViewMedia::MEDIA_TYPE_PDF}}
					<a href="{{$row.url}}"><img src="/res/images/icon_large_pdf.png" alt="PDF" width="200" /></a>
				{{/if}}
			</td>
		</tr>
		<tr>
			<td><pre style="overflow: scroll; width: 800px; margin-top: 20px;">{{$row.url_org}}</pre></td>
		</tr>
	{{/foreach}}
</table>