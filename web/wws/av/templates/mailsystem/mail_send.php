<?php

use bqp\Utils\HtmlBuilder;
use bqp\Utils\StringUtils;
use wws\Mailsystem\MailsystemMail;

/**
 * @var MailsystemMail $mailsystem_mail
 * @var array $text_blocks_mails
 */
?>
<link rel="stylesheet" type="text/css" href="/res/css/mailsystem.css"/>

<script type="text/javascript" src="/res/js/tinymce_4.9.4/js/tinymce/tinymce.min.js"></script>

<script type="text/javascript">

    function load_editor()
    {
        tinyMCE.init({
            selector: ".wysiwyg",
            language: "de",
            theme: "modern",
            statusbar: false,
            menubar: false,
            toggletoolbars_status: "off",
            browser_spellcheck: true,

            plugins: "textcolor,searchreplace,code,table,paste,autolink,lists,media,searchreplace,fullscreen,advlist,contextmenu,colorpicker",

            toolbar: [
                "undo redo | bold italic underline | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist,numlist",
                "styleselect fontsizeselect | searchreplace removeformat fullscreen code"
            ],

            paste_auto_cleanup_on_paste: true
        });
    }

    jQuery.fn.extend({
        getCaretPosition: function () {
            return this.get()[0].selectionStart;
        },
        insertAtCaret: function (myValue) {
            return this.each(function (i) {
                if (document.selection) {
                    this.focus();
                    var sel = document.selection.createRange();
                    sel.text = myValue;
                    this.focus();
                } else if (this.selectionStart || this.selectionStart == '0') {
                    var startPos = this.selectionStart;
                    var endPos = this.selectionEnd;
                    var scrollTop = this.scrollTop;
                    this.value = this.value.substring(0, startPos) + myValue + this.value.substring(endPos, this.value.length);
                    this.focus();
                    this.selectionStart = startPos + myValue.length;
                    this.selectionEnd = startPos + myValue.length;
                    this.scrollTop = scrollTop;
                } else {
                    this.value += myValue;
                    this.focus();
                }
            });
        }
    });

    function vorlage(mail_id)
    {
        var html_active = '<?=$mailsystem_mail->getMailmodus() == "html" ? 1 : 0;?>';

        jQuery.post(
            '/ax/mailsystem/vorlagen/vorlage/',
            {
                html: html_active,
                mail_id: mail_id,
                customer_id: jQuery('#customer_id').val(),
                order_id: jQuery('#order_id').val()
            }
        ).done(function (response) {
            if (response.betreff && jQuery('#betreff').val() == "") {
                jQuery('#betreff').val(response.betreff);
            }

            if (response.customer_memo) {
                jQuery('#customer_memo').insertAtCaret(response.customer_memo);
            }

            if (html_active == 1) {
                tinyMCE.execCommand('mceInsertContent', false, response.text);
            } else {
                jQuery('#body').insertAtCaret(response.text);
            }

            if (response.has_attachments) {
                const form = document.querySelector('form#mail_form');

                const mail_id = document.createElement('input');
                mail_id.type = 'hidden';
                mail_id.name = 'mail_id';
                mail_id.value = response.mail_id;

                const bookmark = document.createElement('input');
                bookmark.type = 'hidden';
                bookmark.name = 'bookmark';
                bookmark.value = jQuery("#body").getCaretPosition();

                const action = document.createElement('input');
                action.type = 'hidden';
                action.name = 'action_apply_template';
                action.value = '';

                form.appendChild(mail_id);
                form.appendChild(bookmark);
                form.appendChild(action);

                document.body.appendChild(form);
                form.submit();
            }
        });

    }
</script>
<script type="text/javascript" src="/res/js/ajax_treemenu/dhtmlxcommon.js"></script>
<script type="text/javascript" src="/res/js/ajax_treemenu/dhtmlxtree.js"></script>
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function (event) {
        tree = new dhtmlXTreeObject(document.getElementById('treeBox'), "100%", "auto", 0);
        tree.setImagePath("/res/js/ajax_treemenu/imgs/csh_vista/");

        tree.enableCheckBoxes(false);
        tree.enableDragAndDrop(false);
        tree.setXMLAutoLoading("/ax/mailsystem/vorlagen/xml/");
        tree.loadXML("/ax/mailsystem/vorlagen/xml/");

        tree.attachEvent("onSelect", function (id) {
            if (id.substr(0, 9) != 'katmarker') {
                vorlage(id);
            }
        });
        tree.attachEvent("onClick", function (id) {
            if (id.substr(0, 9) != 'katmarker') {
                return;
            }

            //öffnen und schließen auch bei click auf den namen möglich machen -> gibt keine Option dafür
            var state = this._getOpenState(this._globalIdStorageFind(id)); //natürlich geht das nicht einfach!!!
            if (state > 0) {
                this.closeItem(id);
            } else {
                this.openItem(id);
            }
        });

        <?php
        if ($mailsystem_mail->getMailmodus() == 'html') {
            echo 'load_editor();';
        }

        if (isset($bookmark)) {
            echo 'jQuery("#body").focus();';
            echo 'jQuery("#body").prop("selectionStart", ' . $bookmark . ');';
        }
        ?>
    });
</script>
<style type="text/css">
    html, body {
        height: 100%;
    }

    body {
        background-color: #45AD6B;
        padding: 0;
        margin: 0;
    }

    .vorlagen_links {
        list-style-type: none;
    }

    .vorlagen_links li {
        padding: 2px;
    }

    .vorlagen_links a {
        font-size: 12px;
        color: #45AD6B;

    }
</style>
<?=$_info;?>

<form method="post" id="mail_form" action="<?=$_controller->createViewUrl();?>" enctype="multipart/form-data">
    <input type="hidden" name="page" value="send_email"/>
    <input type="hidden" name="mailmodus" value="<?=$mailsystem_mail->getMailmodus();?>"/>
    <input type="hidden" name="submit_check" value="true"/>
    <input type="hidden" name="modus" value="<?=$modus;?>"/>
    <input type="hidden" name="act_mail_id" value="<?=$mailsystem_mail->getPrevMailId();?>"/>
    <input type="hidden" name="customer_id" id="customer_id" value="<?=$mailsystem_mail->getCustomerId();?>"/>
    <input type="hidden" name="order_id" id="order_id" value="<?=$mailsystem_mail->getOrderId();?>"/>

    <div style="display: flex;">
        <div style="flex-grow: 1">
            <div style="background-color: #fff; border: 1px solid black; padding: 2px;">
                <table>
                    <tr>
                        <td>Absender:</td>
                        <td>
                            <select name="sender_email" id="sender_email">
                                <?=HtmlBuilder::options($senders, $selected_sender);?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>Absendername:</td>
                        <td><input type="text" name="sender_name" value="<?=$mailsystem_mail->getSender();?>" style="width: 250px;"/></td>
                    </tr>
                    <tr>
                        <td>Empfänger:</td>
                        <td><input type="text" name="reciver_mail" value="<?=$mailsystem_mail->getEmpfaengerMail();?>" style="width: 250px;"/></td>
                    </tr>
                    <tr>
                        <td>Betreff:</td>
                        <td><input type="text" name="betreff" id="betreff" value="<?=$mailsystem_mail->getBetreff();?>" style="width: 500px;"/></td>
                    </tr>
                    <tr>
                        <td valign="top">Anhänge:</td>
                        <td>
                            <?php
                            foreach ($mailsystem_mail->getAnlagen() as $anlage_id => $anlage) {
                                echo '<input type="hidden" name="anlagen[' . $anlage_id . '][file]" value="' . $anlage['file'] . '" />';
                                echo '<input type="hidden" name="anlagen[' . $anlage_id . '][org_filename]" value="' . $anlage['org_filename'] . '" />';
                                echo '<input type="hidden" name="anlagen[' . $anlage_id . '][filesize]" value="' . $anlage['filesize'] . '" />';

                                echo '<a href="/ax/mailsystem/mail/anhang/?file_hash=' . $anlage['file'] . '&as=' . $anlage['org_filename'] . '&mail_id=-1" target="_blank">';
                                echo $anlage['org_filename'] . ' (' . StringUtils::humanFileSize($anlage['filesize']) . ')';
                                echo '</a>';
                                echo '<br />';
                            }
                            ?>
                            <input type="file" name="anlage_file" style="height: 22px; width: 400px;" class="std_btn"/> <input type="submit" name="action_upload" style="height: 22px;" value="Hochladen" class="std_btn"/>
                        </td>
                    </tr>
                </table>
            </div>

            <div style="background-color: #fff; border: 1px solid black; padding: 2px;">
                <textarea name="body" id="body" class="wysiwyg" style="width: 100%; height: 448px; border: 0px;"><?=$mailsystem_mail->getBody();?></textarea>
            </div>

            <input type="submit" name="chg_modus_text"
                   style="display: block; background-color: #fff; border: 1px solid black; border-top: 0px; width: 80px; margin-left: 10px; float:right; text-align: center; color: black; text-decoration: none; margin-right: 5px; <?php if ($mailsystem_mail->getMailmodus() == 'text') {
                       echo ' font-weight: bold;';
                   } ?>" value="Text"/>
            <input type="submit" name="chg_modus_html"
                   style="display: block; background-color: #fff; border: 1px solid black; border-top: 0px; width: 80px; margin-left: 10px; float:right; text-align: center; color: black; text-decoration: none; <?php if ($mailsystem_mail->getMailmodus() == 'html') {
                       echo ' font-weight: bold;';
                   } ?>" value="HTML"/>


            <?php
            if ($mailsystem_mail->getOrderId() !== 0 || $mailsystem_mail->getCustomerId() !== 0) { ?>
                <div style="margin-top: 50px">
                    <label style="background: white; padding: 2px" for="customer_memo">Bemerkung zum Kundenmemofeld hinzufügen:</label>
                    <textarea name="customer_memo" id="customer_memo" style="width: 100%; height: 100px; border: 0;"><?=$mailsystem_mail->getCustomerMemoTemplate()?></textarea>
                </div>
            <?php } ?>
        </div>


        <div style="width: 300px;">
            <div id="treeBox" style="background-color: #fff;border: 1px solid black; padding: 2px; margin-bottom: 10px;"></div>

            <div style="background-color: #fff;border: 1px solid black; padding: 2px; margin-bottom: 10px;">
                Vorlagen:
                <a href="javascript:OpenPopup('/ax/mailsystem/vorlagen/','vorlagen','',500,600);" style="color:#45AD6B; text-decoration: none;">Übersicht</a>
            </div>

            <div style="background-color: #fff;border: 1px solid black; padding: 2px;">
                Textbausteine:

                <!--a href="javascript:vorlage('signatur');" style="color:#45AD6B; text-decoration: none; margin-bottom: 10px;">Signatur</a/-->
                <?php
                echo '<ul class="vorlagen_links">';
                foreach ($text_blocks_mails as $mails) {
                    if (count($mails) == 1) {
                        $mail = $mails[0];
                        $beschreibung = preg_replace('~([0-9]{2}) [A-Z]{2} -~', '', $mail['beschreibung']);

                        echo '<li><a href="javascript:vorlage(\'' . $mail['mail_id'] . '\');">' . $beschreibung . '</a></li>';
                    } else {
                        $mail = $mails[0];
                        $beschreibung = preg_replace('~([0-9]{2}) [A-Z]{2} -~', '', $mail['beschreibung']);

                        echo '<li><a href="javascript:vorlage(\'' . $mail['mail_id'] . '\');">' . $beschreibung . '</a> (<a href="javascript:vorlage(\'' . $mails[1]['mail_id'] . '\');">engl.</a>)</li>';
                    }
                }
                echo '</ul>'; ?>
            </div>
        </div>
    </div>

    <div class="std_form">
        <br><br><br><br>
        <div class="footdark fixed_late">
            <input type="submit" value="Senden" name="action_send:0" class="std_btn"/>
            <?php if ($mailsystem_mail->getPrevMailId()) {
                echo '<input type="submit" value="Senden und archivieren" name="action_send:1" class="std_btn" />';
            } ?>
            <input type="submit" value="Speichern" name="action_save" class="std_btn"/>
            <input type="button" value="Schließen" onclick="window.close();" class="std_btn"/>
        </div>
    </div>
</form>