<?php

namespace wws\WwsController\market_view;

use service_loader;
use wws\core\wws_market_view_controller;
use wws\MarketView\MarketViewMediaCache;

class controller_media extends wws_market_view_controller
{

    public function view_quick_view(int $mv_media_id)
    {
        $result = $this->db_mv->query("
            SELECT
                market_view_source.mvsrc_name,
                market_view_source.typ,
                
                market_view_media.mv_media_id,
                market_view_media.mvsrc_id,
                market_view_media.media_key,
                market_view_media.media_status,
                market_view_media.created_at,
                market_view_media.media_type,
                market_view_media.topic,
                market_view_media.url,
                market_view_media.url AS url_org,
                market_view_media.url_storage,
                market_view_media.fingerprint,
                market_view_media.md5,
                market_view_media.pos,
                market_view_media.width,
                market_view_media.height,
                market_view_media.description
            FROM
                market_view_media INNER JOIN 
                market_view_product ON (
                        market_view_media.mvsrc_id = market_view_product.mvsrc_id AND
                        market_view_media.mvsrc_product_id = market_view_product.mvsrc_product_id
                ) INNER JOIN
                market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id)
            WHERE
                market_view_media.mv_media_id = " . $mv_media_id . "
		");

        $media_cache = service_loader::getDiContainer()->get(MarketViewMediaCache::class);

        $this->tpl->assign('result', []);

        foreach ($result as $daten) {
            $daten['url'] = $media_cache->buildUrl($daten['mvsrc_id'], $daten['url_storage'] ?: $daten['url']);
            $this->tpl->append('result', $daten);
        }

        $this->tpl->windowDisplay('market_view/product_media.tpl');
    }
}
