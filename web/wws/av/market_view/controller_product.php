<?php

use bqp\table\DataSource\TableObjectDataSourceArray;
use bqp\table\DataSource\TableObjectDataSourceWws;
use bqp\table\field\table_object_field;
use bqp\table\field\table_object_field_callback;
use bqp\table\TableObject;
use bqp\table\TableObjectInline;
use bqp\table\TableObjectUrlPersistent;
use wws\core\wws_market_view_controller;
use wws\MarketView\Entities\MarketViewDevice;
use wws\MarketView\Entities\MarketViewMedia;
use wws\MarketView\Entities\MarketViewProduct;
use wws\MarketView\Entities\MarketViewSource;
use wws\MarketView\MarketViewFactory;
use wws\MarketView\MarketViewMediaCache;
use wws\MarketView\MarketViewProductCreator;
use wws\MarketView\MarketViewRepository;
use wws\Product\Utils\ProductDataPreview;

class controller_product extends wws_market_view_controller
{
    private int $mvsrc_id;
    private string $mvsrc_product_id;
    private MarketViewSource $mvsrc;
    private MarketViewRepository $mv_repository;
    private MarketViewMediaCache $media_cache;
    private MarketViewProduct $market_view_product;

    protected function checkNeededRights()
    {
        parent::checkNeededRights();
        $this->checkRight('rights.market_view_product');
    }

    public function inject(MarketViewRepository $mv_repository, MarketViewMediaCache $media_cache): void
    {
        $this->mv_repository = $mv_repository;
        $this->media_cache = $media_cache;
    }

    public function init()
    {
        $this->tpl->setTemplate('market_view/product.tpl');

        $this->mvsrc_id = (int)$_REQUEST['mvsrc_id'];
        $this->mvsrc_product_id = $_REQUEST['mvsrc_product_id'];

        $this->addDefaultParameter('mvsrc_id', $this->mvsrc_id);
        $this->addDefaultParameter('mvsrc_product_id', $this->mvsrc_product_id);

        $this->mvsrc = $this->mv_repository->getMarketViewSource($this->mvsrc_id);

        $this->market_view_product = $this->mv_repository->getMarketViewProduct($this->mvsrc_id, $this->mvsrc_product_id);

        $this->tpl->assign('use_devices', $this->mvsrc->isUseDevices());
        $this->tpl->assign('mv_master_product_id', $this->market_view_product->getMvMasterProductId());

        $this->tpl->setTitle('MarketView - Produkt');
    }

    public function setActive($menu)
    {
        $this->tpl->assign('submenu_active', $menu);
    }

    public function view_default()
    {
        $this->view_product();
    }

    public function view_product(): void
    {
        $this->setActive('product');

        $row = $this->db_mv->singleQuery("
            SELECT
                market_view_media.mvsrc_id,
                market_view_media.url,
                market_view_media.url_storage
            FROM
                market_view_media
            WHERE
                market_view_media.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_media.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "' AND
                market_view_media.media_type IN ('" . MarketViewMedia::MEDIA_TYPE_IMAGE . "', '" . MarketViewMedia::MEDIA_TYPE_IMAGE_URL . "')
            ORDER BY
                market_view_media.pos
            LIMIT
                1
        ");

        if ($row) {
            $this->tpl->assign('picture', $this->media_cache->buildThumbnailUrl($this->mvsrc_id, $row['url_storage'] ?: $row['url']));
        }

        $additional_categories = $this->db_mv->query("
            SELECT
                market_view_cat.mvsrc_cat_name
            FROM
                market_view_product_cat INNER JOIN
                market_view_cat ON (market_view_product_cat.mv_cat_id = market_view_cat.mv_cat_id)
            WHERE
                market_view_product_cat.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_product_cat.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "'
        ")->asSingleArray();

        $this->tpl->assign('additional_categories', $additional_categories);

        $this->tpl->addTable($this->table_matching(), 'matching');

        $this->tpl->assign('product', $this->market_view_product);

        $mv_features = $this->market_view_product->getMvFeatures();
        if ($mv_features->isFeatures()) {
            $this->tpl->assign('mv_features', $mv_features);
        }

        $this->tpl->assign('ean_edit', $this->mvsrc->canEanEdit());

        $market_view_gpsr = null;
        $market_view_gpsr_2 = null;
        if ($this->market_view_product->getMvGpsrId()) {
            $market_view_gpsr = $this->mv_repository->getMarketViewGpsr($this->market_view_product->getMvGpsrId());
        }
        if ($this->market_view_product->getMvGpsrId2()) {
            $market_view_gpsr_2 = $this->mv_repository->getMarketViewGpsr($this->market_view_product->getMvGpsrId2());
        }

        $this->tpl->assign('market_view_gpsr', $market_view_gpsr);
        $this->tpl->assign('market_view_gpsr_2', $market_view_gpsr_2);

        $this->tpl->setTitle('MarketView - Produkt');
        $this->tpl->windowDisplay('market_view/product_details.tpl');
    }

    public function table_matching(): ?TableObject
    {
        $matchings = $this->db_mv->query("
            SELECT
                market_view_matching.matching_id,
                market_view_matching.product_id,
                market_view_matching.matching_status
            FROM
                market_view_matching
            WHERE
                market_view_matching.mvsrc_id = '" . $this->mvsrc_id . "' AND
                market_view_matching.mvsrc_product_id = '" . $this->db->escape($this->mvsrc_product_id) . "'
        ")->asArray('product_id');

        $product_ids = array_column($matchings, 'product_id');

        if (!$product_ids) {
            return null;
        }

        $sql = "
            SELECT
                product.product_id,
                product_brand.brand_name,
                product.product_name
            FROM
                product INNER JOIN
                product_brand ON (product.brand_id = product_brand.brand_id)
            WHERE
                product.product_id IN (" . $this->db->in($product_ids) . ")
        ";

        $ds = new TableObjectDataSourceWws($this->db, $sql);

        $table = new TableObjectInline($ds);
        $table->setCaption('Matching');

        $field = new table_object_field_callback('matching_status', 'Status');
        $field->setCallback(function (array $row) use ($matchings) {
            return $matchings[$row['product_id']]['matching_status'];
        });

        $table->addField($field);

        return $table;
    }

    public function view_product_preview()
    {
        $product_creator = service_loader::getDiContainer()->get(MarketViewProductCreator::class);

        $product = $product_creator->getMvsrcProductAsProduct($this->market_view_product);

        $preview = new ProductDataPreview();
        $preview->display($product);
    }

    public function view_market()
    {
        $this->setActive('market');

        $sql = "
			SELECT

				market_view_source.mvsrc_name,
				market_view_source.typ,

				market_view_product.mvsrc_id,
				market_view_product.mvsrc_product_id,

				MAKRO.market_view.hersteller,
				market_view_product.product_name,

				market_view_product.ean,
				market_view_product.mpn,

				market_view_product.availability_id,
				market_view_product.mv_availability,
				MAKRO.market_view.vk_netto,

				market_view_product.product_id
			FROM
				market_view_product AS src INNER JOIN
				market_view_product ON (
					(
						src.mpn != '' AND
						src.mpn = market_view_product.mpn
					) OR (
						src.ean != '' AND
						src.ean = market_view_product.ean
					)
				) INNER JOIN
				market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id) LEFT JOIN
				market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id)
			WHERE
				src.mvsrc_id = '" . $this->mvsrc_id . "' AND
				src.mvsrc_product_id = '" . $this->db->escape($this->mvsrc_product_id) . "'
		";

        $datasource = new TableObjectDataSourceWws($this->db_mv, $sql);

        $table = new TableObject($datasource);
        $table->setRenderNavigation(false);
        $table->removeFieldByKey('mv_availability');
        $table->removeFieldByKey('mvsrc_id');
        $table->setRowFormater([MarketViewFactory::getInstance()->getMarketViewOutputHelper(), 'tableHelper_sourcetype_colorize']);

        $field = $table->getFieldByKey('product_name');
        $field->setTemplate('<a href="' . $this->createViewUrl('product', 'mvsrc_id={{$mvsrc_id}}&mvsrc_product_id={{$mvsrc_product_id}}') . '">__value__</a>');

        $this->tpl->addTable($table);

        $this->tpl->windowDisplay();
    }

    public function view_texte()
    {
        $this->setActive('texte');

        $sql = "
			SELECT
				market_view_source.mvsrc_name,
				market_view_product.product_name,
				market_view_product.beschreibung,
				market_view_product.lieferumfang
			FROM
				market_view_product AS src INNER JOIN
				market_view_product ON (
					/*(
						src.mpn != '' AND
						src.mpn = market_view_product.mpn
					) OR*/ (
						src.ean != '' AND
						src.ean = market_view_product.ean
					)
				) INNER JOIN
				market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id)
			WHERE
				src.mvsrc_id = '" . $this->mvsrc_id . "' AND
				src.mvsrc_product_id = '" . $this->db->escape($this->mvsrc_product_id) . "'
		";


        $datasource = new TableObjectDataSourceWws($this->db_mv, $sql);

        $table = new TableObject($datasource);
        $table->setRenderNavigation(false);

        $field = $table->getFieldByKey('product_name');
        $field->setTemplate(false);

        $this->tpl->addTable($table);

        $this->tpl->windowDisplay();
    }

    public function view_zubehoer()
    {
        $this->setActive('zubehoer');

        $sql = "
			SELECT

				market_view_source.mvsrc_name,
				market_view_source.typ,
				market_view_cat.mvsrc_cat_name,
				
				market_view_cross_sell_type.cross_sell_type_name,

				market_view_product.mvsrc_id,
				market_view_product.mvsrc_product_id,
				MAKRO.market_view.hersteller,
				market_view_product.product_name,
                market_view_product.product_id,
				market_view_product.ean,
				market_view_product.mpn,

				market_view_product.availability_id,
				market_view_product.mv_availability,
				MAKRO.market_view.other_sources,
				MAKRO.market_view.vk_netto
			FROM
				market_view_product AS src INNER JOIN
				market_view_product AS src2 ON (
                    (
                        src.ean != '' AND
                        src.ean = src2.ean
                    ) OR (
                        src.mvsrc_id = src2.mvsrc_id AND
                        src.mvsrc_product_id = src2.mvsrc_product_id
                    )
				) INNER JOIN
				market_view_cross_sell ON (
					market_view_cross_sell.mvsrc_id = src2.mvsrc_id AND
					market_view_cross_sell.mvsrc_product_id = src2.mvsrc_product_id
				) INNER JOIN
				market_view_product ON (
					market_view_cross_sell.mvsrc_id = market_view_product.mvsrc_id AND
					market_view_cross_sell.mvsrc_zub_product_id = market_view_product.mvsrc_product_id
				) INNER JOIN
				market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id) INNER JOIN
				market_view_hersteller ON (market_view_product.mv_hersteller_id = market_view_hersteller.mv_hersteller_id) INNER JOIN
				market_view_cat ON (market_view_product.mv_cat_id = market_view_cat.mv_cat_id) INNER JOIN
				market_view_cross_sell_type ON (market_view_cross_sell.cross_sell_type_id = market_view_cross_sell_type.cross_sell_type_id)
			WHERE
				src.mvsrc_id = '" . $this->mvsrc_id . "' AND
				src.mvsrc_product_id = '" . $this->db->escape($this->mvsrc_product_id) . "'
		";


        $datasource = new TableObjectDataSourceWws($this->db_mv, $sql);

        $table = new TableObject($datasource);
        $table->setRenderNavigation(false);
        $table->removeFieldByKey('mv_availability');
        $table->removeFieldByKey('mvsrc_id');
        $table->setEntriesPerPageDefault(1000);

        $field = $table->getFieldByKey('product_name');
        $field->setTemplate('<a href="' . $this->createViewUrl('product', 'mvsrc_id={{$mvsrc_id}}&mvsrc_product_id={{$mvsrc_product_id}}') . '">__value__</a>');

        $this->tpl->addTable($table);

        $this->tpl->windowDisplay();
    }

    public function view_media()
    {
        $this->setActive('media');

        $result = $this->db_mv->query("
			SELECT
                market_view_source.mvsrc_name,
                market_view_source.typ,
                
                market_view_media.mv_media_id,
                market_view_media.media_key,
                market_view_media.media_status,
                market_view_media.created_at,
                market_view_media.media_type,
                market_view_media.topic,
                market_view_media.url,
                market_view_media.url AS url_org,
                market_view_media.url_storage,
                market_view_media.fingerprint,
                market_view_media.md5,
                market_view_media.pos,
                market_view_media.width,
                market_view_media.height,
                market_view_media.description
            FROM
                market_view_product AS src INNER JOIN
                market_view_product ON (
                    (
                        src.mvsrc_id = market_view_product.mvsrc_id AND
                        src.mvsrc_product_id = market_view_product.mvsrc_product_id
                    ) OR
                    /*(
                        src.mpn != '' AND
                        src.mpn = market_view_product.mpn
                    ) OR*/ (
                        src.ean != '' AND
                        src.ean = market_view_product.ean
                    )					
                ) INNER JOIN
                market_view_source ON (market_view_product.mvsrc_id = market_view_source.mvsrc_id) INNER JOIN
                market_view_media ON (
                        market_view_product.mvsrc_id = market_view_media.mvsrc_id AND
                        market_view_product.mvsrc_product_id = market_view_media.mvsrc_product_id
                )
            WHERE
                src.mvsrc_id = '" . $this->mvsrc_id . "' AND
                src.mvsrc_product_id = '" . $this->db->escape($this->mvsrc_product_id) . "'
            ORDER BY
                market_view_source.mvsrc_name,
                market_view_media.pos
		");

        $media_cache = service_loader::getDiContainer()->get(MarketViewMediaCache::class);

        $this->tpl->assign('result', []);

        foreach ($result as $daten) {
            $daten['url'] = $media_cache->buildUrl($this->mvsrc_id, $daten['url_storage'] ?: $daten['url']);
            $this->tpl->append('result', $daten);
        }

        $this->tpl->windowDisplay('market_view/product_media.tpl');
    }

    public function action_update_ean()
    {
        $ean = $this->request->xss('ean');
        $hstnr = $this->request->xss('hstnr');

        $this->db_mv->query("
			UPDATE
				market_view_product
			SET
				market_view_product.ean = '" . $this->db_mv->escape($ean) . "',
				market_view_product.mpn = '" . $this->db_mv->escape($hstnr) . "'
			WHERE
				market_view_product.mvsrc_id = '" . $this->mvsrc_id . "' AND
				market_view_product.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "'
		");
    }

    public function view_history()
    {
        $this->mvsrc_id = (int)$_REQUEST['mvsrc_id'];
        $this->mvsrc_product_id = $_REQUEST['mvsrc_product_id'];

        $this->setActive('history');

        $sql = "
			SELECT
				market_view_history.date_added,
				market_view_history.vk_netto,
				market_view_history.availability_id
			FROM
				market_view_history
			WHERE
				market_view_history.mvsrc_id = '" . $this->mvsrc_id . "' AND
				market_view_history.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "'
			ORDER BY
				market_view_history.date_added DESC
		";

        $datasource = new TableObjectDataSourceWws($this->db_mv, $sql);

        $table = new TableObject($datasource);
        $table->setRenderNavigation(false);

        $this->tpl->addTable($table);

        $this->tpl->windowDisplay();
    }

    public function view_devices()
    {
        $this->setActive('devices');

        $this->tpl->addTable($this->table_devices());
        $this->tpl->windowDisplay();
    }

    public function table_devices()
    {
        $sql = "
			SELECT
				market_view_device.mvsrc_id,
				market_view_device.mvsrc_device_id,
				market_view_device.device_status,
				MAKRO.market_view_device.device_id,
				
				market_view_device.mv_hersteller_id,
				market_view_hersteller.hersteller_name,
				market_view_cat.mvsrc_cat_name,
				market_view_device.model_name,
				market_view_device.code1,
				market_view_device.code2,
				market_view_device.code3,
				market_view_device.code4,
				market_view_device.code5,
				market_view_product_device.extra,
				market_view_product_device.is_online
			FROM
				market_view_product_device INNER JOIN
				market_view_device ON (market_view_product_device.mvsrc_id = market_view_device.mvsrc_id AND market_view_product_device.mvsrc_device_id = market_view_device.mvsrc_device_id) INNER JOIN
				market_view_hersteller ON (market_view_device.mv_hersteller_id = market_view_hersteller.mv_hersteller_id) INNER JOIN
				market_view_cat ON (market_view_device.mv_cat_id = market_view_cat.mv_cat_id)
			WHERE
				market_view_product_device.mvsrc_id = " . (int)$this->mvsrc_id . " AND
				market_view_product_device.mvsrc_product_id = '" . $this->db_mv->escape($this->mvsrc_product_id) . "'
		";

        $datasource = new TableObjectDataSourceWws($this->db_mv, $sql);

        $table = new TableObjectUrlPersistent($datasource);
        $table->addPersistentFieldByController($this);
        $table->removeFieldByKey('mvsrc_id');
        $table->removeFieldByKey('mv_hersteller_id');
        $table->removeFieldByKey('device_status');
        $table->removeFieldByKey('is_online');
        $table->setCaption('MarketView - Geräte');

        $table->addRowFormater(function (array $row) {
            if ($row['device_status'] === MarketViewDevice::DEVICE_STATUS_OFFLINE) {
                return ['class' => 'del'];
            }

            if (!$row['is_online']) {
                return ['class' => 'del'];
            }
        });

        return $table;
    }

    public function view_relations()
    {
        $this->setActive('relations');

        $this->tpl->addTable($this->table_relations(), 'table');
        $this->tpl->stdDisplay();
    }

    public function table_relations()
    {
        $struct = $this->mv_repository->getMarketViewProductRelation($this->mvsrc_id, $this->mvsrc_product_id);

        $src = new TableObjectDataSourceArray($struct->getRelations());

        $table = new TableObjectUrlPersistent($src);
        $table->setCaption('Beziehungen');

        $field = new table_object_field('type', 'Art der Beziehung');
        $table->addField($field);

        $field = new table_object_field('mvsrc_product_id', 'ext. Artikelnummer');
        $table->addField($field);

        $field = new table_object_field('text', 'Text');
        $table->addField($field);

        $field = new table_object_field_callback('extra', 'Extra');
        $field->setCallback(function ($daten) {
            if (!isset($daten['extra'])) {
                return '';
            }

            $return = [];

            foreach ($daten['extra'] as $key => $value) {
                $return[] = '<b>' . $key . ':</b> ' . $value;
            }

            return implode("<br>\n", $return);
        });
        $table->addField($field);

        $field = new table_object_field('raw', 'Rohdaten');
        $table->addField($field);

        return $table;
    }
}
