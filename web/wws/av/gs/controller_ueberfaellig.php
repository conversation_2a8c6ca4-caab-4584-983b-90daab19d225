<?php

use bqp\Date\DateObj;
use bqp\table\DataSource\TableObjectDataSourceWws;
use bqp\table\field\FieldActionShow;
use bqp\table\field\table_object_field_checkbox;
use bqp\table\field\TableObjectFieldActions;
use bqp\table\TableObjectPersistent;
use wws\core\wws_controller;
use wws\frontend\OrderMenu;
use wws\Mails\Mail;
use wws\Order\Order;
use wws\Order\OrderItemEstimatedDelivery;

class controller_ueberfaellig extends wws_controller
{
    protected $unsafe_tpl = true;

    protected $std_wfid = 'overdue_order_list';

    public function view_default(): void
    {
        $this->callView('overdue_products');
    }

    public function view_overdue_customer_inform(): void
    {
        $this->tpl->addTable($this->table_overdue_customer_inform());

        $this->tpl->setTitle('Überfällige Kundenbestellungen');
        $this->tpl->smartDisplay();
    }

    /**
     * @return TableObjectPersistent
     */
    public function table_overdue_customer_inform(): TableObjectPersistent
    {
        $sql = "
			SELECT
				order_item.order_id,
				order_item.auftnr,
				
				customers.firma,
				customers.name,
				customers.vorname,
				
				order_item.added,

				order_item.estimated_delivery_date_customer,
				order_item.estimated_delivery_date,
				MAKRO.order_item.estimated_delivery_customer_difference_days,
				
				product.product_type,
				product.product_id,
				product.product_name
			FROM
				orders INNER JOIN
				order_item ON (orders.order_id = order_item.order_id) INNER JOIN
				customers ON (orders.customer_id = customers.customer_id) INNER JOIN
				product ON (order_item.product_id = product.product_id)
			WHERE
				(
					order_item.estimated_delivery_date > DATE_ADD(order_item.estimated_delivery_date_customer, INTERVAL 3 DAY) OR
					NOW() > DATE_ADD(order_item.estimated_delivery_date_customer, INTERVAL 4 DAY)
				) AND
				" . order_repository::getOverdueSqlFilter() . "
			ORDER BY
				order_item.estimated_delivery_date
		";

        $ds = new TableObjectDataSourceWws($this->db, $sql);
        $ds->setDataRowCallback(function (array $row) {
            $row['estimated_delivery_date_obj'] = new DateObj($row['estimated_delivery_date']);
            if (!$row['estimated_delivery_date_obj']->isFuture()) {
                $row['estimated_delivery_days_diff'] = '';
            }
            return $row;
        });

        $table = new TableObjectPersistent($ds, $this->getPersistentContainer());
        $table->setCaption('Aufträge');
        $table->setExportEnabled(true);

        $table->setRowFormater(static function ($daten) {
            if (!$daten['estimated_delivery_date_obj']->isFuture()) {
                return ['class' => 'red'];
            }
        });

        $field = new TableObjectFieldActions();
        $field->add(new FieldActionShow($this->createViewUrl('overdue_order') . '&order_id={{$order_id}}'));

        $table->addField($field);

        $table->removeFieldByKey('order_id');
        $table->removeFieldByKey('product_id');

        return $table;
    }

    public function view_overdue_order(): void
    {
        $order_id = $this->request->int('order_id');

        $order = new Order($order_id);

        $order_header = new OrderMenu($this);
        $order_header->setActiveNote('liefertermin');
        $order_header->setOrder($order);

        $this->tpl->assign('order', $order);

        $this->tpl->loadDatepicker();

        $this->tpl->setTitle('Liefertermin');
        $this->tpl->smartDisplay('gs/overdue_order.tpl');
    }

    public function action_overdue_order_customer_informed(): void
    {
        $order_id = $this->request->int('order_id');

        $order = new Order($order_id);

        $estimated_delivery_date_customer = $order->getEstimatedDeliveryDateCustomer();

        if ($estimated_delivery_date_customer !== null
            && $order->getEstimatedDeliveryDate()->format(DateObj::DE_DATE) !== $estimated_delivery_date_customer->format(DateObj::DE_DATE)) {
            $delivery = new OrderItemEstimatedDelivery();
            $delivery->setEstimatedDeliveryDate($order->getEstimatedDeliveryDate());
            $delivery->setCustomerInformed(true);
            $delivery->setNote('Kunde informiert');

            $order->addEstimatedDeliveryDate($delivery);

            $order->save();
        }


        $engine = $this->getWorkflow();
        $engine->addParameter('order', $order);
        $engine->execute();
    }

    public function view_overdue_order_mail(): void
    {
        $order_id = $this->request->int('order_id');
        $vorlage = $this->request->vorlage;

        $order = new Order($order_id);

        if (!empty($_POST['mail'])) {
            $mail = new Mail($_POST['mail']);
        } else {
            $mail = new Mail();
            $mail->setOrder($order);
            $mail->loadVorlage($vorlage ?: 'beruhigungsmail1');
            $mail->assign('termin', $order->getEstimatedDeliveryDate()->format('W'));
            $mail->parse();
        }


        $this->tpl->assign('order', $order);
        $this->tpl->assign('mail', $mail);

        $this->tpl->setTitle('Liefertermin - Mail');
        $this->tpl->stdDisplay('gs/overdue_order_mail.tpl');
    }

    public function action_overdue_order_mail(): void
    {
        $mail = new Mail($_POST['mail']);

        if ($mail->trySend()) {
            $order_id = $this->request->int('order_id');

            $order = new Order($order_id);

            $delivery = new OrderItemEstimatedDelivery();
            $delivery->setEstimatedDeliveryDate($order->getEstimatedDeliveryDate());
            $delivery->setCustomerInformed(true);
            $delivery->setNote('Beruhigungsmail*');

            $order->addEstimatedDeliveryDate($delivery);
            $order->save();

            $this->info->ok('Mail wurde gesendet.');

            $engine = $this->getWorkflow();
            $engine->execute();
        } else {
            $this->info->fail('Mail konnte nicht gesendet werden.');
            $this->callView();
        }
    }

    public function action_overdue_order_mail_abort(): void
    {
        $this->getWorkflow()->execute();
    }

    public function action_overdue_order_abort(): void
    {#
        $engine = $this->getWorkflow();
        $engine->execute();
    }

    public function action_overdue_order_add_new_delivery_date(): void
    {
        $order_id = $this->request->int('order_id');

        $order = new Order($order_id);

        if (!$_POST['estimated_deliver']) {
            $this->info->fail('Bitte geben Sie ein Liefertermin an.');
        }

        if ($this->info->isError()) {
            $this->callView();
            return;
        }


        $estimated_deliver_date = new DateObj($_POST['estimated_deliver']);

        $estimated_delivery = new OrderItemEstimatedDelivery();
        $estimated_delivery->setCustomerInformed(isset($_POST['estimated_customer_informed']));
        $estimated_delivery->setEstimatedDeliveryDate($estimated_deliver_date);
        $estimated_delivery->setNote($_POST['estimated_note']);

        $order->addEstimatedDeliveryDate($estimated_delivery);

        $order->save();

        $engine = $this->getWorkflow();
        $engine->execute();
    }

    public function view_overdue_products(): void
    {
        $this->tpl->addTable($this->table_overdue_products());

        $this->tpl->setTitle('Überfällige Kundenbestellungen');
        $this->tpl->smartDisplay();
    }

    /**
     * @return TableObjectPersistent
     */
    public function table_overdue_products(): TableObjectPersistent
    {
        $sql = "
			SELECT
				product.product_type,
				product.product_id,
				product.product_name,

				SUM(order_item.quantity) AS quantity_total,

				MAKRO.order_item.estimated_delivery,

				MAKRO.order_item.estimated_delivery_days_min
			FROM
				orders INNER JOIN
				order_item ON (orders.order_id = order_item.order_id) INNER JOIN
				product ON (order_item.product_id = product.product_id)
			WHERE
				TIMESTAMP(order_item.estimated_delivery_date, '16:00:00') < NOW() AND
				" . order_repository::getOverdueSqlFilter() . '
			GROUP BY
				product.product_id
			ORDER BY
				product.product_name
		';

        $table = new TableObjectPersistent($sql);

        $field = new TableObjectFieldActions();
        $field->add(new FieldActionShow($this->createViewUrl('overdue_product') . '&product_id={{$product_id}}'));

        $table->addField($field);

        $field = $table->getFieldByKey('quantity_total');
        $field->setName('Anzahl');

        $table->removeFieldByKey('product_id');

        return $table;
    }

    public function view_overdue_product(): void
    {
        $container = $this->session->getPersistentContainer('overdue_product');
        $container['product_id'] = $this->request->int('product_id');
        $container['order_item_ids'] = [];

        $this->form_overdue_product($container);
    }

    /**
     * @param $daten
     */
    public function form_overdue_product($daten): void
    {
        $this->tpl->assign('daten', $daten);

        $this->tpl->addTable($this->table_overdue_product());

        $this->tpl->loadDatepicker();
        $this->tpl->setTitle('Überfällige Kunden Bestellungen');
        $this->tpl->smartDisplay('gs/overdue_product.tpl');
    }

    /**
     * @return TableObjectPersistent
     */
    public function table_overdue_product(): TableObjectPersistent
    {
        $container = $this->session->getPersistentContainer('overdue_product');

        $sql = "
			SELECT
				order_item.order_item_id,
				order_item.auftnr,

				customers.firma,
				customers.name,
				customers.vorname,

				order_item.product_id,
				order_item.quantity,
				order_item.product,
				order_item.gros_memo,
				
				order_item.added,				

				MAKRO.order_item.estimated_delivery,
				MAKRO.order_item.estimated_delivery_days_min,
				order_item.estimated_delivery_date_customer
			FROM
				orders INNER JOIN
				order_item ON (orders.order_id = order_item.order_id) INNER JOIN
				customers ON (orders.customer_id = customers.customer_id) LEFT JOIN
				order_item_estimated_delivery ON (order_item.order_item_id = order_item_estimated_delivery.order_item_id)
			WHERE
				/*TIMESTAMP(order_item.estimated_delivery_date, '16:00:00') < NOW() AND*/
				" . order_repository::getOverdueSqlFilter() . " AND
				order_item.product_id = '" . $container['product_id'] . "'
			GROUP BY
				order_item.order_item_id
		";


        $table = new TableObjectPersistent($sql, 'overdue_product');

        $field = new table_object_field_checkbox();
        $field->setCheckboxName('order_item_ids[]');
        $field->setCheckboxValue('{{$order_item_id}}');
        $field->setCheckboxAll(true);
        $field->setCheckedCallback(static function ($daten) use ($container) {
            return in_array($daten['order_item_id'], $container['order_item_ids']);
        });

        $table->addField($field);

        $table->removeFieldByKey('order_item_id');
        $table->removeFieldByKey('product_id');
        return $table;
    }

    public function action_save_overdue_product(): void
    {
        $container = $this->session->getPersistentContainer('overdue_product');

        $daten = [];

        $order_item_ids = $_POST['order_item_ids'] ?? [];

        $daten['order_item_ids'] = input::filter($order_item_ids, 'int');
        $daten['order_item_estimated_deliver'] = $_POST['order_item_estimated_deliver'];
        $daten['note'] = $_POST['note'];
        $daten['customer_informed'] = isset($_POST['customer_informed']);

        $container->merge($daten);

        $estimated_deliver_date = new DateObj($daten['order_item_estimated_deliver']);

        if (!$daten['order_item_ids']) {
            $this->info->fail('Bitte wählen Sie mindestens ein Auftrag aus.');
        }
        if (!$estimated_deliver_date->isFuture()) {
            $this->info->fail('Das Lieferdatum muss in der Zukunft liegen.');
        }

        if ($this->info->isError()) {
            $this->form_overdue_product($container);
        } else {
            $estimated_delivery = new OrderItemEstimatedDelivery();
            $estimated_delivery->setCustomerInformed($daten['customer_informed']);
            $estimated_delivery->setEstimatedDeliveryDate($estimated_deliver_date);
            $estimated_delivery->setNote($daten['note']);

            foreach ($daten['order_item_ids'] as $order_item_id) {
                $order = order_repository::getOrderByOrderItemId($order_item_id);
                $order_item = $order->getOrderItemByOrderItemId($order_item_id);
                $order_item->addEstimatedDelivery(clone $estimated_delivery);
                $order->save();
            }

            $container->reset();

            $this->callView('overdue_products');
        }
    }
}