<?php

use bqp\table\DataSource\TableObjectDataSourceWws;
use bqp\table\TableObjectPersistent;
use wws\core\wws_controller;
use wws\frontend\OrderMenu;
use wws\Order\ModelProtocolTranslatorOrder;

class controller_order_protokoll extends wws_controller
{
    private ModelProtocolTranslatorOrder $protocol_translator;

    public function inject(ModelProtocolTranslatorOrder $protocol_translator): void
    {
        $this->protocol_translator = $protocol_translator;
    }

    public function init()
    {
        $this->setOrderId($this->getOrderId());
        $this->addDefaultParameter('order_id', $this->getOrderId());

        $order_header = new OrderMenu($this);
        $order_header->setOrderId($this->getOrderId());
        $order_header->setActiveNote('order_protokoll');
    }

    public function getOrderId(): int
    {
        return $this->request->int('order_id');
    }

    public function setOrderId(int $order_id): void
    {
        $this->tpl->assign('order_id', $order_id);
    }

    public function setActive(string $note): void
    {
        $this->tpl->assign('active_note', $note);
    }


    public function view_default()
    {
        $this->tpl->addTable($this->table_protokoll());

        $this->tpl->setTitle('Auftrag', 'Protokoll');
        $this->tpl->stdDisplay();
    }

    public function table_protokoll()
    {
        $sql = "SELECT
                protokoll_order.order_id,
                DATE_FORMAT(protokoll_order.date_added, '%d.%m.%Y %H:%i:%s') AS date_added_group,
                protokoll_order.date_added,
                MAKRO.protokoll_order.user,
                protokoll_order.type,
                protokoll_order.type_id,
                protokoll_field.field_name,
                protokoll_order.new_value
            FROM
                protokoll_order LEFT JOIN
                protokoll_field ON (protokoll_field.field_id = protokoll_order.field_id)
            WHERE
                protokoll_order.order_id = " . $this->getOrderId() . "
            ORDER BY
                protokoll_order.date_added DESC
		";

        $ds = new TableObjectDataSourceWws($this->db, $sql);
        $ds->setDataCallback(function (array $rows) {
            return $this->protocol_translator->translate($rows);
        });

        $table = new TableObjectPersistent($ds, 'order_protokoll');
        $table->setCaption('Protokoll');
        $table->addGroupHeaderByField('date_added_group');

        $table->removeFieldByKey('order_id');
        $table->removeFieldByKey('date_added_group');

        $table->getFieldByKey('new_value')->convert('html');

        return $table;
    }
}
